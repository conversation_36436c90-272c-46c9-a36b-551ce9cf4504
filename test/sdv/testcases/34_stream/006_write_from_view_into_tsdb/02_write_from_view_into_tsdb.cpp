#include "gtest/gtest.h"
#include "securec.h"
#include "gmc_sql.h"
#include "rd_feature_stream.h"
#include "stream_table_struct.h"


class WriteFromViewIntoTSDB : public testing::Test {
public:
    static GmcConnT *longConn;
    static GmcStmtT *longStmt;

    bool hasFinishTest = false;
    void RdFinishTest();

    static void SetUpTestCase();
    static void TearDownTestCase();
    virtual void SetUp();
    virtual void TearDown();
};

GmcConnT *WriteFromViewIntoTSDB::longConn = NULL;
GmcStmtT *WriteFromViewIntoTSDB::longStmt = NULL;

void WriteFromViewIntoTSDB::SetUpTestCase()
{
    int32_t ret;
    ret = RdStreamEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void WriteFromViewIntoTSDB::TearDownTestCase()
{
    int32_t ret = RdStreamEnvClean();
    EXPECT_EQ(GMERR_OK, ret);
}

void WriteFromViewIntoTSDB::SetUp()
{
    int32_t ret = RdGmcConnect(&longConn, &longStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t modelType = MODEL_TS;
    ret = GmcSetStmtAttr(longStmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *sqlCmd = (char *)"drop table ts1;";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(longStmt, sqlCmd, cmdLen);
    ret = ret == GMERR_UNDEFINED_TABLE ? GMERR_OK : ret;
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    sqlCmd = (char *)"drop table ts2;";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(longStmt, sqlCmd, cmdLen);
    ret = ret == GMERR_UNDEFINED_TABLE ? GMERR_OK : ret;
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void WriteFromViewIntoTSDB::TearDown()
{
    int32_t ret = RdGmcDisconnect(longConn, longStmt);
    EXPECT_EQ(GMERR_OK, ret);
}

void WriteFromViewIntoTSDB::RdFinishTest()
{
    hasFinishTest = true;
}

// 1个流表，1个view，1个sink结点，1个tsdb表，向流表插入数据，设置timeout和batch_window_size，检查tsdb表中数据是否符合预期
TEST_F(WriteFromViewIntoTSDB, STREAM_006_DML_001)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50));"},
        {"create stream view view1 as select * from stream1 with (tuple_buffer_size = 1200);"},
        {"create stream sink sink1 as select * from view1 into tsdb (ts1) with (batch_window_size = 1000, timeout = 5);"},
    };
    ret = RdStreamExecSql(longStmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 1000;
    RdStructWriteStreamTable(longStmt, vertexLabel, rowNum, 1, 0);

    // 查询ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 1000;
    uint32_t expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    rowNum = 999;
    RdStructWriteStreamTable(longStmt, vertexLabel, rowNum, 1, 0);

    // 插完数据立马查询ts表中数据
    selectTsName = (char *)"ts1";
    expectRowsCount = 1000;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待5s后再查询ts表中数据
    sleep(5);
    selectTsName = (char *)"ts1";
    expectRowsCount = 1999;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(longStmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
}

// 1个流表，1个view，多个sink结点，1个ts表，向流表插入数据，设置timeout和batch_window_size，检查tsdb表中数据是否符合预期
TEST_F(WriteFromViewIntoTSDB, STREAM_006_DML_002)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50));"},
        {"create stream view view1 as select * from stream1 with (tuple_buffer_size = 1200);"},
        {"create stream sink sink1 as select * from view1 into tsdb (ts1) with (batch_window_size = 1000, timeout = 5);"},
        {"create stream sink sink2 as select * from view1 into tsdb (ts1) with (batch_window_size = 1000, timeout = 10);"},
    };
    ret = RdStreamExecSql(longStmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入1000条数据
    uint32_t rowNum = 1000;
    RdStructWriteStreamTable(longStmt, vertexLabel, rowNum, 1, 0);

    // 查询ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 2000;
    uint32_t expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 结构化写入999条数据
    rowNum = 999;
    RdStructWriteStreamTable(longStmt, vertexLabel, rowNum, 1, 0);

    // 插完数据立马查询ts表中数据
    selectTsName = (char *)"ts1";
    expectRowsCount = 2000;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待5s后再查询ts表中数据
    sleep(5);
    selectTsName = (char *)"ts1";
    expectRowsCount = 2999;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待10s后再查询ts表中数据
    sleep(10);
    selectTsName = (char *)"ts1";
    expectRowsCount = 3998;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream sink sink1;"},
        {"drop stream sink sink2;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(longStmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
}

// 1个流表，1个view，多个sink结点，多个ts表，向流表插入数据，设置timeout和batch_window_size，检查tsdb表中数据是否符合预期
TEST_F(WriteFromViewIntoTSDB, STREAM_006_DML_003)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create table ts2 (id integer, name char(50)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50));"},
        {"create stream view view1 as select * from stream1 with (tuple_buffer_size = 1200);"},
        {"create stream sink sink1 as select * from view1 into tsdb (ts1) with (batch_window_size = 1000, timeout = 5);"},
        {"create stream sink sink2 as select * from view1 into tsdb (ts2) with (batch_window_size = 1000, timeout = 10);"},
    };
    ret = RdStreamExecSql(longStmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[2].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入1000条数据
    uint32_t rowNum = 1000;
    RdStructWriteStreamTable(longStmt, vertexLabel, rowNum, 1, 0);

    // 查询ts1表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 1000;
    uint32_t expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 查询ts2表中数据
    selectTsName = (char *)"ts2";
    expectRowsCount = 1000;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 结构化写入999条数据
    rowNum = 999;
    RdStructWriteStreamTable(longStmt, vertexLabel, rowNum, 1, 0);

    // 插完数据立马查询ts1表中数据
    selectTsName = (char *)"ts1";
    expectRowsCount = 1000;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 插完数据立马查询ts1表中数据
    selectTsName = (char *)"ts2";
    expectRowsCount = 1000;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待5s后再查询ts1表中数据
    sleep(5);
    selectTsName = (char *)"ts1";
    expectRowsCount = 1999;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待5s后再查询ts2表中数据
    selectTsName = (char *)"ts2";
    expectRowsCount = 1000;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待10s后再查询ts2表中数据
    sleep(10);
    selectTsName = (char *)"ts2";
    expectRowsCount = 1999;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop table ts2;", GMC_MODEL_TS},
        {"drop stream sink sink1;"},
        {"drop stream sink sink2;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(longStmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
}

// 1个流表，多个view，多个sink结点，1个ts表，向流表插入数据，设置timeout和batch_window_size，检查tsdb表中数据是否符合预期
TEST_F(WriteFromViewIntoTSDB, STREAM_006_DML_004)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50));"},
        {"create stream view view1 as select * from stream1 with (tuple_buffer_size = 1200);"},
        {"create stream view view2 as select * from stream1 with (tuple_buffer_size = 1200);"},
        {"create stream sink sink1 as select * from view1 into tsdb (ts1) with (batch_window_size = 1000, timeout = 5);"},
        {"create stream sink sink2 as select * from view2 into tsdb (ts1) with (batch_window_size = 1000, timeout = 10);"},
    };
    ret = RdStreamExecSql(longStmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入1000条数据
    uint32_t rowNum = 1000;
    RdStructWriteStreamTable(longStmt, vertexLabel, rowNum, 1, 0);

    // 查询ts1表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 2000;
    uint32_t expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 结构化写入999条数据
    rowNum = 999;
    RdStructWriteStreamTable(longStmt, vertexLabel, rowNum, 1, 0);

    // 插完数据立马查询ts1表中数据
    selectTsName = (char *)"ts1";
    expectRowsCount = 2000;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待5s后再查询ts1表中数据
    sleep(5);
    selectTsName = (char *)"ts1";
    expectRowsCount = 2999;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待10s后再查询ts1表中数据
    sleep(10);
    selectTsName = (char *)"ts1";
    expectRowsCount = 3998;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream sink sink1;"},
        {"drop stream sink sink2;"},
        {"drop stream view view1;"},
        {"drop stream view view2;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(longStmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
}

// 1个流表，多个view，多个sink结点，多个ts表，向流表插入数据，设置timeout和batch_window_size，检查tsdb表中数据是否符合预期
TEST_F(WriteFromViewIntoTSDB, STREAM_006_DML_005)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create table ts2 (id integer, name char(50)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50));"},
        {"create stream table stream2 (id integer, name char(50));"},
        {"create stream view view1 as select * from stream1 with (tuple_buffer_size = 1200);"},
        {"create stream view view2 as select * from stream2 with (tuple_buffer_size = 1200);"},
        {"create stream sink sink1 as select * from view1 into tsdb (ts1) with (batch_window_size = 1000, timeout = 5);"},
        {"create stream sink sink2 as select * from view2 into tsdb (ts2) with (batch_window_size = 500, timeout = 10);"},
    };
    ret = RdStreamExecSql(longStmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel01 = RdStreamParseTableSchema(creates[2].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel01);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel02 = RdStreamParseTableSchema(creates[3].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel02);

    // 向流表stream1结构化写入1000条数据
    uint32_t rowNum01 = 1000;
    RdStructWriteStreamTable(longStmt, vertexLabel01, rowNum01, 1, 0);

    // 向流表stream2结构化写入1000条数据
    uint32_t rowNum02 = 500;
    RdStructWriteStreamTable(longStmt, vertexLabel02, rowNum02, 1, 0);

    // 查询ts1表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 1000;
    uint32_t expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 查询ts2表中数据
    selectTsName = (char *)"ts2";
    expectRowsCount = 500;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 向流表stream1结构化写入999条数据
    rowNum01 = 999;
    RdStructWriteStreamTable(longStmt, vertexLabel01, rowNum01, 1, 0);

    // 向流表stream1结构化写入999条数据
    rowNum02 = 499;
    RdStructWriteStreamTable(longStmt, vertexLabel02, rowNum02, 1, 0);

    // 插完数据立马查询ts1表中数据
    selectTsName = (char *)"ts1";
    expectRowsCount = 1000;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 插完数据立马查询ts2表中数据
    selectTsName = (char *)"ts2";
    expectRowsCount = 500;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待5s后再查询ts1表中数据
    sleep(5);
    selectTsName = (char *)"ts1";
    expectRowsCount = 1999;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待5s后再查询ts2表中数据
    selectTsName = (char *)"ts2";
    expectRowsCount = 500;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待10s后再查询ts2表中数据
    sleep(10);
    selectTsName = (char *)"ts2";
    expectRowsCount = 999;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop table ts2;", GMC_MODEL_TS},
        {"drop stream sink sink1;"},
        {"drop stream sink sink2;"},
        {"drop stream view view1;"},
        {"drop stream view view2;"},
        {"drop stream table stream1;"},
        {"drop stream table stream2;"},
    };
    ret = RdStreamExecSql(longStmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel01);
    RdStreamFreeTableSchema(vertexLabel02);
}

// 多个流表，多个view，多个sink结点，1个ts表，向流表插入数据，设置timeout和batch_window_size，检查tsdb表中数据是否符合预期
TEST_F(WriteFromViewIntoTSDB, STREAM_006_DML_006)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50));"},
        {"create stream table stream2 (id integer, name char(50));"},
        {"create stream view view1 as select * from stream1 with (tuple_buffer_size = 1200);"},
        {"create stream view view2 as select * from stream2 with (tuple_buffer_size = 1200);"},
        {"create stream view view3 as select * from stream2 with (tuple_buffer_size = 1200);"},
        {"create stream sink sink1 as select * from view1 into tsdb (ts1) with (batch_window_size = 1000, timeout = 5);"},
        {"create stream sink sink2 as select * from view2 into tsdb (ts1) with (batch_window_size = 500, timeout = 10);"},
        {"create stream sink sink3 as select * from view3 into tsdb (ts1) with (batch_window_size = 500, timeout = 10);"},
    };
    ret = RdStreamExecSql(longStmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel01 = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel01);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel02 = RdStreamParseTableSchema(creates[2].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel02);

    // 向流表stream1结构化写入1000条数据
    uint32_t rowNum01 = 1000;
    RdStructWriteStreamTable(longStmt, vertexLabel01, rowNum01, 1, 0);

    // 向流表stream2结构化写入1000条数据
    uint32_t rowNum02 = 500;
    RdStructWriteStreamTable(longStmt, vertexLabel02, rowNum02, 1, 0);

    // 查询ts1表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 2000;
    uint32_t expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 向流表stream1结构化写入999条数据
    rowNum01 = 999;
    RdStructWriteStreamTable(longStmt, vertexLabel01, rowNum01, 1, 0);

    // 向流表stream2结构化写入499条数据
    rowNum02 = 499;
    RdStructWriteStreamTable(longStmt, vertexLabel02, rowNum02, 1, 0);

    // 插完数据立马查询ts1表中数据
    selectTsName = (char *)"ts1";
    expectRowsCount = 2000;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待5s后再查询ts1表中数据
    sleep(5);
    selectTsName = (char *)"ts1";
    expectRowsCount = 2999;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待10s后再查询ts1表中数据
    sleep(10);
    selectTsName = (char *)"ts1";
    expectRowsCount = 3997;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream sink sink1;"},
        {"drop stream sink sink2;"},
        {"drop stream sink sink3;"},
        {"drop stream view view1;"},
        {"drop stream view view2;"},
        {"drop stream view view3;"},
        {"drop stream table stream1;"},
        {"drop stream table stream2;"},
    };
    ret = RdStreamExecSql(longStmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel01);
    RdStreamFreeTableSchema(vertexLabel02);
}

// 多个流表，多个view，多个sink结点，多个ts表，向流表插入数据，设置timeout和batch_window_size，检查tsdb表中数据是否符合预期
TEST_F(WriteFromViewIntoTSDB, STREAM_006_DML_007)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create table ts2 (id integer, name char(50)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50));"},
        {"create stream table stream2 (id integer, name char(50));"},
        {"create stream view view1 as select * from stream1 with (tuple_buffer_size = 1200);"},
        {"create stream view view2 as select * from stream2 with (tuple_buffer_size = 1200);"},
        {"create stream view view3 as select * from stream2 with (tuple_buffer_size = 1200);"},
        {"create stream sink sink1 as select * from view1 into tsdb (ts1) with (batch_window_size = 1000, timeout = 5);"},
        {"create stream sink sink2 as select * from view2 into tsdb (ts2) with (batch_window_size = 500, timeout = 10);"},
        {"create stream sink sink3 as select * from view3 into tsdb (ts2) with (batch_window_size = 500, timeout = 10);"},
    };
    ret = RdStreamExecSql(longStmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel01 = RdStreamParseTableSchema(creates[2].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel01);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel02 = RdStreamParseTableSchema(creates[3].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel02);

    // 向流表stream1结构化写入1000条数据
    uint32_t rowNum01 = 1000;
    RdStructWriteStreamTable(longStmt, vertexLabel01, rowNum01, 1, 0);

    // 向流表stream2结构化写入1000条数据
    uint32_t rowNum02 = 500;
    RdStructWriteStreamTable(longStmt, vertexLabel02, rowNum02, 1, 0);

    // 查询ts1表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 1000;
    uint32_t expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 查询ts2表中数据
    selectTsName = (char *)"ts2";
    expectRowsCount = 1000;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 向流表stream1结构化写入999条数据
    rowNum01 = 999;
    RdStructWriteStreamTable(longStmt, vertexLabel01, rowNum01, 1, 0);

    // 向流表stream2结构化写入999条数据
    rowNum02 = 499;
    RdStructWriteStreamTable(longStmt, vertexLabel02, rowNum02, 1, 0);

    // 插完数据立马查询ts1表中数据
    selectTsName = (char *)"ts1";
    expectRowsCount = 1000;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 插完数据立马查询ts2表中数据
    selectTsName = (char *)"ts2";
    expectRowsCount = 1000;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待5s后再查询ts1表中数据
    sleep(5);
    selectTsName = (char *)"ts1";
    expectRowsCount = 1999;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待5s后再查询ts2表中数据
    selectTsName = (char *)"ts2";
    expectRowsCount = 1000;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待10s后再查询ts2表中数据
    sleep(10);
    selectTsName = (char *)"ts1";
    expectRowsCount = 1999;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    selectTsName = (char *)"ts2";
    expectRowsCount = 1998;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop table ts2;", GMC_MODEL_TS},
        {"drop stream sink sink1;"},
        {"drop stream sink sink2;"},
        {"drop stream sink sink3;"},
        {"drop stream view view1;"},
        {"drop stream view view2;"},
        {"drop stream view view3;"},
        {"drop stream table stream1;"},
        {"drop stream table stream2;"},
    };
    ret = RdStreamExecSql(longStmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel01);
    RdStreamFreeTableSchema(vertexLabel02);
}

// 1个流表，多个view，多个sink结点，1个ts表，流表的出边同时有view和sink，向流表插入数据，设置timeout和batch_window_size，检查tsdb表中数据是否符合预期
TEST_F(WriteFromViewIntoTSDB, STREAM_006_DML_008)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50));"},
        {"create stream view view1 as select * from stream1 with (tuple_buffer_size = 1200);"},
        {"create stream view view2 as select * from stream1 with (tuple_buffer_size = 1200);"},
        {"create stream sink sink1 as select * from stream1 into tsdb (ts1) with (batch_window_size = 1000, timeout = 5);"},
        {"create stream sink sink2 as select * from view1 into tsdb (ts1) with (batch_window_size = 500, timeout = 10);"},
        {"create stream sink sink3 as select * from view2 into tsdb (ts1) with (batch_window_size = 1000, timeout = 10);"},
    };
    ret = RdStreamExecSql(longStmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入1000条数据
    uint32_t rowNum = 1000;
    RdStructWriteStreamTable(longStmt, vertexLabel, rowNum, 1, 0);

    // 查询ts1表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 3000;
    uint32_t expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 结构化写入999条数据
    rowNum = 999;
    RdStructWriteStreamTable(longStmt, vertexLabel, rowNum, 1, 0);

    // 插完数据立马查询ts1表中数据
    selectTsName = (char *)"ts1";
    expectRowsCount = 3500;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待5s后再查询ts1表中数据
    sleep(5);
    selectTsName = (char *)"ts1";
    expectRowsCount = 4499;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待10s后再查询ts1表中数据
    sleep(10);
    selectTsName = (char *)"ts1";
    expectRowsCount = 5997;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream sink sink1;"},
        {"drop stream sink sink2;"},
        {"drop stream sink sink3;"},
        {"drop stream view view1;"},
        {"drop stream view view2;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(longStmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
}

// sink结点不设置timeout，检查tsdb表中数据是否符合预期
TEST_F(WriteFromViewIntoTSDB, STREAM_006_DML_009)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50));"},
        {"create stream view view1 as select * from stream1 with (tuple_buffer_size = 1200);"},
        {"create stream view view2 as select * from stream1 with (tuple_buffer_size = 1200);"},
        {"create stream view view3 as select * from stream1 with (tuple_buffer_size = 1200);"},
        {"create stream view view4 as select * from stream1 with (tuple_buffer_size = 1200);"},
        {"create stream view view5 as select * from stream1 with (tuple_buffer_size = 1200);"},
        {"create stream view view6 as select * from stream1 with (tuple_buffer_size = 1200);"},
        {"create stream sink sink1 as select * from view1 into tsdb (ts1) with (batch_window_size = 1000);"},
        {"create stream sink sink2 as select * from view1 into tsdb (ts1) with (batch_window_size = 1000);"},
        {"create stream sink sink3 as select * from view1 into tsdb (ts1) with (batch_window_size = 1000);"},
        {"create stream sink sink4 as select * from view1 into tsdb (ts1) with (batch_window_size = 1000);"},
        {"create stream sink sink5 as select * from view1 into tsdb (ts1) with (batch_window_size = 1000);"},
        {"create stream sink sink6 as select * from view1 into tsdb (ts1) with (batch_window_size = 500);"},
        {"create stream sink sink7 as select * from view1 into tsdb (ts1) with (batch_window_size = 500);"},
        {"create stream sink sink8 as select * from view1 into tsdb (ts1) with (batch_window_size = 500);"},
        {"create stream sink sink9 as select * from stream1 into tsdb (ts1) with (batch_window_size = 500);"},
        {"create stream sink sink10 as select * from stream1 into tsdb (ts1) with (batch_window_size = 500);"},
        {"create stream sink sink11 as select * from view2 into tsdb (ts1) with (batch_window_size = 2000);"},
        {"create stream sink sink12 as select * from view3 into tsdb (ts1) with (batch_window_size = 2000);"},
        {"create stream sink sink13 as select * from view4 into tsdb (ts1) with (batch_window_size = 2000);"},
        {"create stream sink sink14 as select * from view5 into tsdb (ts1) with (batch_window_size = 2000);"},
        {"create stream sink sink15 as select * from view6 into tsdb (ts1) with (batch_window_size = 2000);"},
    };
    ret = RdStreamExecSql(longStmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入1000条数据
    uint32_t rowNum = 1000;
    RdStructWriteStreamTable(longStmt, vertexLabel, rowNum, 1, 0);

    // 查询ts1表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 10000;
    uint32_t expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 结构化写入999条数据
    rowNum = 999;
    RdStructWriteStreamTable(longStmt, vertexLabel, rowNum, 1, 0);

    // 查询ts1表中数据
    selectTsName = (char *)"ts1";
    expectRowsCount = 12500;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 结构化写入2001条数据
    rowNum = 2001;
    RdStructWriteStreamTable(longStmt, vertexLabel, rowNum, 1, 0);

    // 查询ts1表中数据
    selectTsName = (char *)"ts1";
    expectRowsCount = 60000;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 结构化写入499条数据
    rowNum = 499;
    RdStructWriteStreamTable(longStmt, vertexLabel, rowNum, 1, 0);

    // 查询ts1表中数据
    selectTsName = (char *)"ts1";
    expectRowsCount = 60000;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待10s查询ts1表中数据
    sleep(10);
    selectTsName = (char *)"ts1";
    expectRowsCount = 60000;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream sink sink1;"},
        {"drop stream sink sink2;"},
        {"drop stream sink sink3;"},
        {"drop stream sink sink4;"},
        {"drop stream sink sink5;"},
        {"drop stream sink sink6;"},
        {"drop stream sink sink7;"},
        {"drop stream sink sink8;"},
        {"drop stream sink sink9;"},
        {"drop stream sink sink10;"},
        {"drop stream sink sink11;"},
        {"drop stream sink sink12;"},
        {"drop stream sink sink13;"},
        {"drop stream sink sink14;"},
        {"drop stream sink sink15;"},
        {"drop stream view view1;"},
        {"drop stream view view2;"},
        {"drop stream view view3;"},
        {"drop stream view view4;"},
        {"drop stream view view5;"},
        {"drop stream view view6;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(longStmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
}

// sink结点设置timeout为0，检查tsdb表中数据是否符合预期
TEST_F(WriteFromViewIntoTSDB, STREAM_006_DML_010)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50));"},
        {"create stream view view1 as select * from stream1 with (tuple_buffer_size = 1200);"},
        {"create stream view view2 as select * from stream1 with (tuple_buffer_size = 1200);"},
        {"create stream view view3 as select * from stream1 with (tuple_buffer_size = 1200);"},
        {"create stream view view4 as select * from stream1 with (tuple_buffer_size = 1200);"},
        {"create stream view view5 as select * from stream1 with (tuple_buffer_size = 1200);"},
        {"create stream view view6 as select * from stream1 with (tuple_buffer_size = 1200);"},
        {"create stream sink sink1 as select * from view1 into tsdb (ts1) with (batch_window_size = 1000, timeout = 0);"},
        {"create stream sink sink2 as select * from view1 into tsdb (ts1) with (batch_window_size = 1000, timeout = 0);"},
        {"create stream sink sink3 as select * from view1 into tsdb (ts1) with (batch_window_size = 1000, timeout = 0);"},
        {"create stream sink sink4 as select * from view1 into tsdb (ts1) with (batch_window_size = 1000, timeout = 0);"},
        {"create stream sink sink5 as select * from view1 into tsdb (ts1) with (batch_window_size = 1000, timeout = 0);"},
        {"create stream sink sink6 as select * from view1 into tsdb (ts1) with (batch_window_size = 500, timeout = 0);"},
        {"create stream sink sink7 as select * from view1 into tsdb (ts1) with (batch_window_size = 500, timeout = 0);"},
        {"create stream sink sink8 as select * from view1 into tsdb (ts1) with (batch_window_size = 500, timeout = 0);"},
        {"create stream sink sink9 as select * from stream1 into tsdb (ts1) with (batch_window_size = 500, timeout = 0);"},
        {"create stream sink sink10 as select * from stream1 into tsdb (ts1) with (batch_window_size = 500, timeout = 0);"},
        {"create stream sink sink11 as select * from view2 into tsdb (ts1) with (batch_window_size = 2000, timeout = 0);"},
        {"create stream sink sink12 as select * from view3 into tsdb (ts1) with (batch_window_size = 2000, timeout = 0);"},
        {"create stream sink sink13 as select * from view4 into tsdb (ts1) with (batch_window_size = 2000, timeout = 0);"},
        {"create stream sink sink14 as select * from view5 into tsdb (ts1) with (batch_window_size = 2000, timeout = 0);"},
        {"create stream sink sink15 as select * from view6 into tsdb (ts1) with (batch_window_size = 2000, timeout = 0);"},
    };
    ret = RdStreamExecSql(longStmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入1000条数据
    uint32_t rowNum = 1000;
    RdStructWriteStreamTable(longStmt, vertexLabel, rowNum, 1, 0);

    // 查询ts1表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 10000;
    uint32_t expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 结构化写入999条数据
    rowNum = 999;
    RdStructWriteStreamTable(longStmt, vertexLabel, rowNum, 1, 0);

    // 查询ts1表中数据
    selectTsName = (char *)"ts1";
    expectRowsCount = 12500;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 结构化写入2001条数据
    rowNum = 2001;
    RdStructWriteStreamTable(longStmt, vertexLabel, rowNum, 1, 0);

    // 查询ts1表中数据
    selectTsName = (char *)"ts1";
    expectRowsCount = 60000;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 结构化写入499条数据
    rowNum = 499;
    RdStructWriteStreamTable(longStmt, vertexLabel, rowNum, 1, 0);

    // 查询ts1表中数据
    selectTsName = (char *)"ts1";
    expectRowsCount = 60000;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待10s查询ts1表中数据
    sleep(10);
    selectTsName = (char *)"ts1";
    expectRowsCount = 60000;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream sink sink1;"},
        {"drop stream sink sink2;"},
        {"drop stream sink sink3;"},
        {"drop stream sink sink4;"},
        {"drop stream sink sink5;"},
        {"drop stream sink sink6;"},
        {"drop stream sink sink7;"},
        {"drop stream sink sink8;"},
        {"drop stream sink sink9;"},
        {"drop stream sink sink10;"},
        {"drop stream sink sink11;"},
        {"drop stream sink sink12;"},
        {"drop stream sink sink13;"},
        {"drop stream sink sink14;"},
        {"drop stream sink sink15;"},
        {"drop stream view view1;"},
        {"drop stream view view2;"},
        {"drop stream view view3;"},
        {"drop stream view view4;"},
        {"drop stream view view5;"},
        {"drop stream view view6;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(longStmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
}

// sink结点设置timeout为不同值，检查tsdb表中数据是否符合预期
TEST_F(WriteFromViewIntoTSDB, STREAM_006_DML_011)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create table ts2 (id integer, name char(50)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50));"},
        {"create stream view view1 as select * from stream1 with (tuple_buffer_size = 1200);"},
        {"create stream view view2 as select * from stream1 with (tuple_buffer_size = 1200);"},
        {"create stream view view3 as select * from stream1 with (tuple_buffer_size = 1200);"},
        {"create stream view view4 as select * from stream1 with (tuple_buffer_size = 1200);"},
        {"create stream view view5 as select * from stream1 with (tuple_buffer_size = 1200);"},
        {"create stream view view6 as select * from stream1 with (tuple_buffer_size = 1200);"},
        {"create stream sink sink1 as select * from view1 into tsdb (ts1) with (batch_window_size = 1000, timeout = 0);"},
        {"create stream sink sink2 as select * from view1 into tsdb (ts2) with (batch_window_size = 1000, timeout = 0);"},
        {"create stream sink sink3 as select * from view1 into tsdb (ts1) with (batch_window_size = 1000, timeout = 5);"},
        {"create stream sink sink4 as select * from view1 into tsdb (ts2) with (batch_window_size = 1000, timeout = 5);"},
        {"create stream sink sink5 as select * from view1 into tsdb (ts1) with (batch_window_size = 500, timeout = 5);"},
        {"create stream sink sink6 as select * from view1 into tsdb (ts2) with (batch_window_size = 500, timeout = 5);"},
        {"create stream sink sink7 as select * from view1 into tsdb (ts1) with (batch_window_size = 500, timeout = 10);"},
        {"create stream sink sink8 as select * from view1 into tsdb (ts2) with (batch_window_size = 500, timeout = 10);"},
        {"create stream sink sink9 as select * from stream1 into tsdb (ts1) with (batch_window_size = 500, timeout = 10);"},
        {"create stream sink sink10 as select * from stream1 into tsdb (ts2) with (batch_window_size = 500, timeout = 10);"},
        {"create stream sink sink11 as select * from view2 into tsdb (ts1) with (batch_window_size = 2000, timeout = 10);"},
        {"create stream sink sink12 as select * from view3 into tsdb (ts2) with (batch_window_size = 2000, timeout = 10);"},
        {"create stream sink sink13 as select * from view4 into tsdb (ts1) with (batch_window_size = 2000, timeout = 10);"},
        {"create stream sink sink14 as select * from view5 into tsdb (ts1) with (batch_window_size = 2000, timeout = 10);"},
        {"create stream sink sink15 as select * from view6 into tsdb (ts1) with (batch_window_size = 2000, timeout = 10);"},
    };
    ret = RdStreamExecSql(longStmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[2].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入1000条数据
    uint32_t rowNum = 1000;
    RdStructWriteStreamTable(longStmt, vertexLabel, rowNum, 1, 0);

    // 立马查询ts1表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 5000;
    uint32_t expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 立马查询ts2表中数据
    selectTsName = (char *)"ts2";
    expectRowsCount = 5000;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待5秒查询ts1表中数据
    sleep(5);
    selectTsName = (char *)"ts1";
    expectRowsCount = 5000;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待5秒查询ts2表中数据
    selectTsName = (char *)"ts2";
    expectRowsCount = 5000;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待10秒查询ts1表中数据
    sleep(10);
    selectTsName = (char *)"ts1";
    expectRowsCount = 9000;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待10秒查询ts2表中数据
    selectTsName = (char *)"ts2";
    expectRowsCount = 6000;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 结构化写入999条数据
    rowNum = 999;
    RdStructWriteStreamTable(longStmt, vertexLabel, rowNum, 1, 0);

    // 立马查询ts1表中数据
    selectTsName = (char *)"ts1";
    expectRowsCount = 10500;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 立马查询ts2表中数据
    selectTsName = (char *)"ts2";
    expectRowsCount = 7500;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待5秒查询ts1表中数据
    sleep(5);
    selectTsName = (char *)"ts1";
    expectRowsCount = 11998;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待5秒查询ts2表中数据
    selectTsName = (char *)"ts2";
    expectRowsCount = 8998;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待10秒查询ts1表中数据
    sleep(10);
    selectTsName = (char *)"ts1";
    expectRowsCount = 16992;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待10秒查询ts2表中数据
    selectTsName = (char *)"ts2";
    expectRowsCount = 10995;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop table ts2;", GMC_MODEL_TS},
        {"drop stream sink sink1;"},
        {"drop stream sink sink2;"},
        {"drop stream sink sink3;"},
        {"drop stream sink sink4;"},
        {"drop stream sink sink5;"},
        {"drop stream sink sink6;"},
        {"drop stream sink sink7;"},
        {"drop stream sink sink8;"},
        {"drop stream sink sink9;"},
        {"drop stream sink sink10;"},
        {"drop stream sink sink11;"},
        {"drop stream sink sink12;"},
        {"drop stream sink sink13;"},
        {"drop stream sink sink14;"},
        {"drop stream sink sink15;"},
        {"drop stream view view1;"},
        {"drop stream view view2;"},
        {"drop stream view view3;"},
        {"drop stream view view4;"},
        {"drop stream view view5;"},
        {"drop stream view view6;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(longStmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
}

// view视图投影部分列，tsdb投影部分列检查tsdb表中数据是否符合预期
TEST_F(WriteFromViewIntoTSDB, STREAM_006_DML_012)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), address char(50)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create table ts2 (id integer, name char(50)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), age integer, address char(50));"},
        {"create stream view view1 as select id,name,address from stream1 with (tuple_buffer_size = 1200);"},
        {"create stream view view2 as select id,name,address from stream1 with (tuple_buffer_size = 1200);"},
        {"create stream view view3 as select id,name,address from stream1 with (tuple_buffer_size = 1200);"},
        {"create stream view view4 as select id,name,address from stream1 with (tuple_buffer_size = 1200);"},
        {"create stream view view5 as select id,name,address from stream1 with (tuple_buffer_size = 1200);"},
        {"create stream view view6 as select id,name,address from stream1 with (tuple_buffer_size = 1200);"},
        {"create stream sink sink1 as select id,name,name from view1 into tsdb (ts1) with (batch_window_size = 1000, timeout = 0);"},
        {"create stream sink sink2 as select id,name from view1 into tsdb (ts2) with (batch_window_size = 1000, timeout = 0);"},
        {"create stream sink sink3 as select id,name,name from view1 into tsdb (ts1) with (batch_window_size = 1000, timeout = 5);"},
        {"create stream sink sink4 as select id,name from view1 into tsdb (ts2) with (batch_window_size = 1000, timeout = 5);"},
        {"create stream sink sink5 as select id,name,name from view1 into tsdb (ts1) with (batch_window_size = 500, timeout = 5);"},
        {"create stream sink sink6 as select id,name from view1 into tsdb (ts2) with (batch_window_size = 500, timeout = 5);"},
        {"create stream sink sink7 as select id,name,name from view1 into tsdb (ts1) with (batch_window_size = 500, timeout = 10);"},
        {"create stream sink sink8 as select id,name from view1 into tsdb (ts2) with (batch_window_size = 500, timeout = 10);"},
        {"create stream sink sink9 as select id,name,name from stream1 into tsdb (ts1) with (batch_window_size = 500, timeout = 10);"},
        {"create stream sink sink10 as select id,name from stream1 into tsdb (ts2) with (batch_window_size = 500, timeout = 10);"},
        {"create stream sink sink11 as select id,name,name from view2 into tsdb (ts1) with (batch_window_size = 2000, timeout = 10);"},
        {"create stream sink sink12 as select id,name from view3 into tsdb (ts2) with (batch_window_size = 2000, timeout = 10);"},
        {"create stream sink sink13 as select id,name,name from view4 into tsdb (ts1) with (batch_window_size = 2000, timeout = 10);"},
        {"create stream sink sink14 as select id,name,name from view5 into tsdb (ts1) with (batch_window_size = 2000, timeout = 10);"},
        {"create stream sink sink15 as select id,name,name from view6 into tsdb (ts1) with (batch_window_size = 2000, timeout = 10);"},
    };
    ret = RdStreamExecSql(longStmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[2].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入1000条数据
    uint32_t rowNum = 1000;
    RdStructWriteStreamTable2(longStmt, vertexLabel, rowNum, 1, 0);

    // 立马查询ts1表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 5000;
    uint32_t expectColsCount = 3;
    RdCheckDataInTSTableOfStreamTable3(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 立马查询ts2表中数据
    selectTsName = (char *)"ts2";
    expectRowsCount = 5000;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待5秒查询ts1表中数据
    sleep(5);
    selectTsName = (char *)"ts1";
    expectRowsCount = 5000;
    expectColsCount = 3;
    RdCheckDataInTSTableOfStreamTable3(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待5秒查询ts2表中数据
    selectTsName = (char *)"ts2";
    expectRowsCount = 5000;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待10秒查询ts1表中数据
    sleep(10);
    selectTsName = (char *)"ts1";
    expectRowsCount = 9000;
    expectColsCount = 3;
    RdCheckDataInTSTableOfStreamTable3(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待10秒查询ts2表中数据
    selectTsName = (char *)"ts2";
    expectRowsCount = 6000;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 结构化写入999条数据
    rowNum = 999;
    RdStructWriteStreamTable2(longStmt, vertexLabel, rowNum, 1, 0);

    // 立马查询ts1表中数据
    selectTsName = (char *)"ts1";
    expectRowsCount = 10500;
    expectColsCount = 3;
    RdCheckDataInTSTableOfStreamTable3(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 立马查询ts2表中数据
    selectTsName = (char *)"ts2";
    expectRowsCount = 7500;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待5秒查询ts1表中数据
    sleep(5);
    selectTsName = (char *)"ts1";
    expectRowsCount = 11998;
    expectColsCount = 3;
    RdCheckDataInTSTableOfStreamTable3(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待5秒查询ts2表中数据
    selectTsName = (char *)"ts2";
    expectRowsCount = 8998;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待10秒查询ts1表中数据
    sleep(10);
    selectTsName = (char *)"ts1";
    expectRowsCount = 16992;
    expectColsCount = 3;
    RdCheckDataInTSTableOfStreamTable3(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待10秒查询ts2表中数据
    selectTsName = (char *)"ts2";
    expectRowsCount = 10995;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable1(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop table ts2;", GMC_MODEL_TS},
        {"drop stream sink sink1;"},
        {"drop stream sink sink2;"},
        {"drop stream sink sink3;"},
        {"drop stream sink sink4;"},
        {"drop stream sink sink5;"},
        {"drop stream sink sink6;"},
        {"drop stream sink sink7;"},
        {"drop stream sink sink8;"},
        {"drop stream sink sink9;"},
        {"drop stream sink sink10;"},
        {"drop stream sink sink11;"},
        {"drop stream sink sink12;"},
        {"drop stream sink sink13;"},
        {"drop stream sink sink14;"},
        {"drop stream sink sink15;"},
        {"drop stream view view1;"},
        {"drop stream view view2;"},
        {"drop stream view view3;"},
        {"drop stream view view4;"},
        {"drop stream view view5;"},
        {"drop stream view view6;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(longStmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
}

// view视图投影部分列，tsdb投影全部列，检查tsdb表中数据是否符合预期
TEST_F(WriteFromViewIntoTSDB, STREAM_006_DML_012_1)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), address char(50)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create table ts2 (id integer, name char(50), address char(50)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), age integer, address char(50));"},
        {"create stream view view1 as select id,name,address from stream1 with (tuple_buffer_size = 1200);"},
        {"create stream view view2 as select id,name,address from stream1 with (tuple_buffer_size = 1200);"},
        {"create stream view view3 as select id,name,address from stream1 with (tuple_buffer_size = 1200);"},
        {"create stream view view4 as select id,name,address from stream1 with (tuple_buffer_size = 1200);"},
        {"create stream view view5 as select id,name,address from stream1 with (tuple_buffer_size = 1200);"},
        {"create stream view view6 as select id,name,address from stream1 with (tuple_buffer_size = 1200);"},
        {"create stream sink sink1 as select * from view1 into tsdb (ts1) with (batch_window_size = 1000, timeout = 0);"},
        {"create stream sink sink2 as select * from view1 into tsdb (ts2) with (batch_window_size = 1000, timeout = 0);"},
        {"create stream sink sink3 as select * from view1 into tsdb (ts1) with (batch_window_size = 1000, timeout = 5);"},
        {"create stream sink sink4 as select * from view1 into tsdb (ts2) with (batch_window_size = 1000, timeout = 5);"},
        {"create stream sink sink5 as select * from view1 into tsdb (ts1) with (batch_window_size = 500, timeout = 5);"},
        {"create stream sink sink6 as select * from view1 into tsdb (ts2) with (batch_window_size = 500, timeout = 5);"},
        {"create stream sink sink7 as select * from view1 into tsdb (ts1) with (batch_window_size = 500, timeout = 10);"},
        {"create stream sink sink8 as select * from view1 into tsdb (ts2) with (batch_window_size = 500, timeout = 10);"},
        {"create stream sink sink9 as select id,name,address from stream1 into tsdb (ts1) with (batch_window_size = 500, timeout = 10);"},
        {"create stream sink sink10 as select id,name,address from stream1 into tsdb (ts2) with (batch_window_size = 500, timeout = 10);"},
        {"create stream sink sink11 as select * from view2 into tsdb (ts1) with (batch_window_size = 2000, timeout = 10);"},
        {"create stream sink sink12 as select * from view3 into tsdb (ts2) with (batch_window_size = 2000, timeout = 10);"},
        {"create stream sink sink13 as select * from view4 into tsdb (ts1) with (batch_window_size = 2000, timeout = 10);"},
        {"create stream sink sink14 as select * from view5 into tsdb (ts1) with (batch_window_size = 2000, timeout = 10);"},
        {"create stream sink sink15 as select * from view6 into tsdb (ts1) with (batch_window_size = 2000, timeout = 10);"},
    };
    ret = RdStreamExecSql(longStmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[2].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入1000条数据
    uint32_t rowNum = 1000;
    RdStructWriteStreamTable2(longStmt, vertexLabel, rowNum, 1, 0);

    // 立马查询ts1表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 5000;
    uint32_t expectColsCount = 3;
    RdCheckDataInTSTableOfStreamTable3(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 立马查询ts2表中数据
    selectTsName = (char *)"ts2";
    expectRowsCount = 5000;
    expectColsCount = 3;
    RdCheckDataInTSTableOfStreamTable3(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待5秒查询ts1表中数据
    sleep(5);
    selectTsName = (char *)"ts1";
    expectRowsCount = 5000;
    expectColsCount = 3;
    RdCheckDataInTSTableOfStreamTable3(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待5秒查询ts2表中数据
    selectTsName = (char *)"ts2";
    expectRowsCount = 5000;
    expectColsCount = 3;
    RdCheckDataInTSTableOfStreamTable3(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待10秒查询ts1表中数据
    sleep(10);
    selectTsName = (char *)"ts1";
    expectRowsCount = 9000;
    expectColsCount = 3;
    RdCheckDataInTSTableOfStreamTable3(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待10秒查询ts2表中数据
    selectTsName = (char *)"ts2";
    expectRowsCount = 6000;
    expectColsCount = 3;
    RdCheckDataInTSTableOfStreamTable3(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 结构化写入999条数据
    rowNum = 999;
    RdStructWriteStreamTable2(longStmt, vertexLabel, rowNum, 1, 0);

    // 立马查询ts1表中数据
    selectTsName = (char *)"ts1";
    expectRowsCount = 10500;
    expectColsCount = 3;
    RdCheckDataInTSTableOfStreamTable3(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 立马查询ts2表中数据
    selectTsName = (char *)"ts2";
    expectRowsCount = 7500;
    expectColsCount = 3;
    RdCheckDataInTSTableOfStreamTable3(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待5秒查询ts1表中数据
    sleep(5);
    selectTsName = (char *)"ts1";
    expectRowsCount = 11998;
    expectColsCount = 3;
    RdCheckDataInTSTableOfStreamTable3(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待5秒查询ts2表中数据
    selectTsName = (char *)"ts2";
    expectRowsCount = 8998;
    expectColsCount = 3;
    RdCheckDataInTSTableOfStreamTable3(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待10秒查询ts1表中数据
    sleep(10);
    selectTsName = (char *)"ts1";
    expectRowsCount = 16992;
    expectColsCount = 3;
    RdCheckDataInTSTableOfStreamTable3(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待10秒查询ts2表中数据
    selectTsName = (char *)"ts2";
    expectRowsCount = 10995;
    expectColsCount = 3;
    RdCheckDataInTSTableOfStreamTable3(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop table ts2;", GMC_MODEL_TS},
        {"drop stream sink sink1;"},
        {"drop stream sink sink2;"},
        {"drop stream sink sink3;"},
        {"drop stream sink sink4;"},
        {"drop stream sink sink5;"},
        {"drop stream sink sink6;"},
        {"drop stream sink sink7;"},
        {"drop stream sink sink8;"},
        {"drop stream sink sink9;"},
        {"drop stream sink sink10;"},
        {"drop stream sink sink11;"},
        {"drop stream sink sink12;"},
        {"drop stream sink sink13;"},
        {"drop stream sink sink14;"},
        {"drop stream sink sink15;"},
        {"drop stream view view1;"},
        {"drop stream view view2;"},
        {"drop stream view view3;"},
        {"drop stream view view4;"},
        {"drop stream view view5;"},
        {"drop stream view view6;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(longStmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
}

// 四个线程分别向不同流表插入数据，检查tsdb表中数据是否符合预期
TEST_F(WriteFromViewIntoTSDB, STREAM_006_DML_013)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50));"},
        {"create stream table stream2 (id integer, name char(50));"},
        {"create stream table stream3 (id integer, name char(50));"},
        {"create stream table stream4 (id integer, name char(50));"},
        {"create stream view view1 as select * from stream1 with (tuple_buffer_size = 1200);"},
        {"create stream view view2 as select * from stream2 with (tuple_buffer_size = 1200);"},
        {"create stream view view3 as select * from stream3 with (tuple_buffer_size = 1200);"},
        {"create stream view view4 as select * from stream4 with (tuple_buffer_size = 1200);"},
        {"create stream sink sink1 as select * from view1 into tsdb (ts1) with (batch_window_size = 1000, timeout = 5);"},
        {"create stream sink sink2 as select * from view2 into tsdb (ts1) with (batch_window_size = 1000, timeout = 5);"},
        {"create stream sink sink3 as select * from view3 into tsdb (ts1) with (batch_window_size = 1000, timeout = 5);"},
        {"create stream sink sink4 as select * from view4 into tsdb (ts1) with (batch_window_size = 1000, timeout = 5);"},
    };
    ret = RdStreamExecSql(longStmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    static const uint32_t threadNum = 4;
    pthread_t threads[threadNum] = {0};
    RdThreadWriteT writeInfo[threadNum] = {0};
    RdVertexLabelT *vertexLabels[threadNum] = {0};

    for (uint32_t i = 0; i < threadNum; i++) {
        vertexLabels[i] = RdStreamParseTableSchema(creates[i + 1].sql); // 从下标2开始是创建流表的SQL
        ASSERT_NE((RdVertexLabelT *)NULL, vertexLabels[i]);
    }
    for (uint32_t i = 0; i < threadNum; i++) {
        writeInfo[i] = {
            .vertexLabel = vertexLabels[i],
            .rowNum = 1900,
            .id = 1,
            .seed = 0,
        };
        ret = pthread_create(&threads[i], NULL, RdInsertDataThread, &writeInfo[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    for (uint32_t i = 0; i < threadNum; i++) {
        ret = pthread_join(threads[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 查询ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 4000;
    uint32_t expectColsCount = 2;
    RdCheckDataInTSTableOfConcurrency(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待5秒再查询ts表中数据
    sleep(5);
    selectTsName = (char *)"ts1";
    expectRowsCount = 7600;
    expectColsCount = 2;
    RdCheckDataInTSTableOfConcurrency(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 全部线程退出后再校验和释放资源，否则可能导致进程崩溃
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream sink sink2;"},
        {"drop stream sink sink3;"},
        {"drop stream sink sink4;"},
        {"drop stream view view1;"},
        {"drop stream view view2;"},
        {"drop stream view view3;"},
        {"drop stream view view4;"},
        {"drop stream table stream1;"},
        {"drop stream table stream2;"},
        {"drop stream table stream3;"},
        {"drop stream table stream4;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(longStmt, drops, sizeof(drops));
    ASSERT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < threadNum; i++) {
        RdStreamFreeTableSchema(vertexLabels[i]);
    }
}

// 四个线程同时向同一个流表插入数据，检查tsdb表中数据是否符合预期
TEST_F(WriteFromViewIntoTSDB, STREAM_006_DML_014)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50));"},
        {"create stream view view1 as select * from stream1 with (tuple_buffer_size = 1200);"},
        {"create stream view view2 as select * from stream1 with (tuple_buffer_size = 1200);"},
        {"create stream view view3 as select * from stream1 with (tuple_buffer_size = 1200);"},
        {"create stream view view4 as select * from stream1 with (tuple_buffer_size = 1200);"},
        {"create stream sink sink1 as select * from view1 into tsdb (ts1) with (batch_window_size = 1000, timeout = 5);"},
        {"create stream sink sink2 as select * from view2 into tsdb (ts1) with (batch_window_size = 1000, timeout = 5);"},
        {"create stream sink sink3 as select * from view3 into tsdb (ts1) with (batch_window_size = 1000, timeout = 5);"},
        {"create stream sink sink4 as select * from view4 into tsdb (ts1) with (batch_window_size = 1000, timeout = 5);"},
    };
    ret = RdStreamExecSql(longStmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    static const uint32_t threadNum = 4;

    pthread_t threadIds[threadNum] = {0};

    RdThreadWriteT writeInfo = {
        .vertexLabel = vertexLabel,
        .rowNum = 1900,
        .id = 1,
        .seed = 0,
    };
    
    // 三个线程分别向同一个流表插入数据
    for (int32_t i = 0; i < threadNum; i++) {
        ret = pthread_create(&threadIds[i], NULL, RdInsertDataThread, (void *)&writeInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
 
    for (int32_t i = 0; i < threadNum; i++) {
        ret = pthread_join(threadIds[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 查询ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 4000;
    uint32_t expectColsCount = 2;
    RdCheckDataInTSTableOfConcurrency(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待5秒再查询ts表中数据
    sleep(5);
    selectTsName = (char *)"ts1";
    expectRowsCount = 7600;
    expectColsCount = 2;
    RdCheckDataInTSTableOfConcurrency(longStmt, selectTsName, expectRowsCount, expectColsCount);

    // 全部线程退出后再校验和释放资源，否则可能导致进程崩溃
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream sink sink2;"},
        {"drop stream sink sink3;"},
        {"drop stream sink sink4;"},
        {"drop stream view view1;"},
        {"drop stream view view2;"},
        {"drop stream view view3;"},
        {"drop stream view view4;"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(longStmt, drops, sizeof(drops));
    ASSERT_EQ(GMERR_OK, ret);

    RdStreamFreeTableSchema(vertexLabel);
}






