/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-11-15 10:25:27
 * @FilePath: \GMDBV5\test\sdv\gmdb_td\testcases\001_stream\020_create_drop_subscribtion\01_create_subscribtion_subconfig.cpp
 * @Description: 
 * @LastEditors: tian<PERSON><PERSON> 
 * @LastEditTime: 2024-11-29 17:01:26
 */
#include "gtest/gtest.h"
#include "securec.h"
#include "gmc_sql.h"
#include "t_rd_sn.h"
#include "rd_feature_stream.h"


class CreateSubscribtion : public testing::Test {
public:
    static GmcConnT *conn;
    static GmcStmtT *stmt;

    bool hasFinishTest = false;
    void RdFinishTest();

    static void SetUpTestCase();
    static void TearDownTestCase();
    virtual void SetUp();
    virtual void TearDown();
};

GmcConnT *CreateSubscribtion::conn = NULL;
GmcStmtT *CreateSubscribtion::stmt = NULL;

void CreateSubscribtion::SetUpTestCase()
{
    int32_t ret;
    ret = RdStreamEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void CreateSubscribtion::TearDownTestCase()
{
    int32_t ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = RdStreamEnvClean();
    EXPECT_EQ(GMERR_OK, ret);
}

void CreateSubscribtion::SetUp()
{   
    // 创建客户端连接
    int32_t ret = RdGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void CreateSubscribtion::TearDown()
{
    int32_t ret = RdGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 创建订阅sink，设置schema_version为非0数字 预期：失败
TEST_F(CreateSubscribtion, STREAM_020_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer);"
        },
        {
            "create stream view v1 as select * from t1;"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, age FROM v1 INTO pubsub_channel with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "s1",
                "comment":"this is a sub.",
                "schema_version": 1,
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] }
                ]  
            }
        )",
    };
    
    // 定义回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) { return; };

    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 创建订阅
    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &received);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);

    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建订阅sink，设置schema_version为0 预期：成功
TEST_F(CreateSubscribtion, STREAM_020_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer);"
        },
        {
            "create stream view v1 as select * from t1;"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, age FROM v1 INTO pubsub_channel with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "s1",
                "comment":"this is a sub.",
                "schema_version": 0,
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] }
                ]  
            }
        )",
    };
    
    // 定义回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) { return; };

    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 创建订阅
    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建订阅sink，不指定schema_version，默认为0 预期：成功
TEST_F(CreateSubscribtion, STREAM_020_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer);"
        },
        {
            "create stream view v1 as select * from t1;"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, age FROM v1 INTO pubsub_channel with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "s1",
                "subs_type": "message_queue",
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] }
                ]     
            }
        )",
    };

    // 回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) { return; };

    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 创建订阅
    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建订阅sink，设置subs_type为message_queue 预期：成功
TEST_F(CreateSubscribtion, STREAM_020_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer);"
        },
        {
            "create stream view v1 as select * from t1;"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, age FROM v1 INTO pubsub_channel with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "s1",
                "schema_version": 0,
                "subs_type": "message_queue",
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] }
                ]     
            }
        )",
    };

    // 回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) { return; };

    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 创建订阅
    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建订阅sink，设置subs_type为status_merge 预期：失败
TEST_F(CreateSubscribtion, STREAM_020_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer);"
        },
        {
            "create stream view v1 as select * from t1;"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, age FROM v1 INTO pubsub_channel with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "s1",
                "schema_version": 0,
                "subs_type": "status_merge",
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] }
                ]     
            }
        )",
    };

    // 回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) { return; };

    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 创建订阅
    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &received);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);

    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建订阅sink，不设置subs_type 预期：成功
TEST_F(CreateSubscribtion, STREAM_020_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer);"
        },
        {
            "create stream view v1 as select * from t1;"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, age FROM v1 INTO pubsub_channel with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "s1",
                "schema_version": 0,
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] }
                ]     
            }
        )",
    };

    // 回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) { return; };

    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 创建订阅
    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建订阅sink，设置events，type为insert；msgTypes只有1个值且为new object 预期：成功
TEST_F(CreateSubscribtion, STREAM_020_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer);"
        },
        {
            "create stream view v1 as select * from t1;"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, age FROM v1 INTO pubsub_channel with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "s1",
                "schema_version": 0,
                "subs_type": "message_queue",
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] }
                ]     
            }
        )",
    };

    // 回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) { return; };

    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 创建订阅
    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建订阅sink，设置events，type为insert；msgTypes只有1个值且为除new object以外的其他值 预期：失败
TEST_F(CreateSubscribtion, STREAM_020_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer);"
        },
        {
            "create stream view v1 as select * from t1;"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, age FROM v1 INTO pubsub_channel with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "s1",
                "schema_version": 0,
                "subs_type": "message_queue",
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["old object"] }
                ]     
            }
        )",
    };

    const GmcSubConfigT subConfig2 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "s1",
                "schema_version": 0,
                "subs_type": "message_queue",
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["key"] }
                ]     
            }
        )",
    };

    const GmcSubConfigT subConfig3 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "s1",
                "schema_version": 0,
                "subs_type": "message_queue",
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["delta object"] }
                ]     
            }
        )",
    };

    // 回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) { return; };

    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 创建订阅
    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &received);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    ret = GmcSubscribe(stmt, &subConfig2, conn, callback, &received);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);

    ret = GmcSubscribe(stmt, &subConfig3, conn, callback, &received);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);

    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建订阅sink，设置events，type为insert；msgTypes有多个值且其中1个为new object 预期：失败
TEST_F(CreateSubscribtion, STREAM_020_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer);"
        },
        {
            "create stream view v1 as select * from t1;"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, age FROM v1 INTO pubsub_channel with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "s1",
                "schema_version": 0,
                "subs_type": "message_queue",
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["old object", "new object"] }
                ]     
            }
        )",
    };

    // 回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) { return; };

    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 创建订阅
    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &received);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建订阅sink，设置events，type为除insert以外的其他值；msgTypes只有1个值且为new object 预期：失败
TEST_F(CreateSubscribtion, STREAM_020_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer);"
        },
        {
            "create stream view v1 as select * from t1;"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, age FROM v1 INTO pubsub_channel with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "s1",
                "schema_version": 0,
                "subs_type": "message_queue",
                "is_reliable": false,
                "events": [
                    { "type": "replace", "msgTypes": ["new object"] }
                ]     
            }
        )",
    };

    // 回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) { return; };

    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 创建订阅
    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &received);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建订阅sink，设置多个events事件，其中一个是{type:insert, msgTypes:[new object]} 预期：失败
TEST_F(CreateSubscribtion, STREAM_020_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer);"
        },
        {
            "create stream view v1 as select * from t1;"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, age FROM v1 INTO pubsub_channel with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "s1",
                "schema_version": 0,
                "subs_type": "message_queue",
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] },
                    { "type": "replace", "msgTypes": ["old object", "new object"] },
                    { "type": "delete", "msgTypes": ["old object"] }
                ]     
            }
        )",
    };

    // 回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) { return; };

    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 创建订阅
    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &received);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建订阅sink，设置多个events事件，都为{type:insert, msgTypes:[new object]} 预期：失败
TEST_F(CreateSubscribtion, STREAM_020_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer);"
        },
        {
            "create stream view v1 as select * from t1;"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, age FROM v1 INTO pubsub_channel with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "s1",
                "schema_version": 0,
                "subs_type": "message_queue",
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] },
                    { "type": "insert", "msgTypes": ["new object"] },
                    { "type": "insert", "msgTypes": ["new object"] },
                    { "type": "insert", "msgTypes": ["new object"] }
                ]     
            }
        )",
    };

    // 回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) { return; };

    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 创建订阅
    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &received);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建订阅sink，不设置events 预期：失败
TEST_F(CreateSubscribtion, STREAM_020_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer);"
        },
        {
            "create stream view v1 as select * from t1;"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, age FROM v1 INTO pubsub_channel with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "s1",
                "schema_version": 0,
                "subs_type": "message_queue",
                "is_reliable": false
            }
        )",
    };

    // 回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) { return; };

    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 创建订阅
    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &received);
    EXPECT_EQ(GMERR_UNDEFINE_COLUMN, ret);

    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建订阅sink，设置is_reliable为true 预期：失败
TEST_F(CreateSubscribtion, STREAM_020_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer);"
        },
        {
            "create stream view v1 as select * from t1;"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, age FROM v1 INTO pubsub_channel with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "s1",
                "schema_version": 0,
                "subs_type": "message_queue",
                "is_reliable": true,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] }
                ]     
            }
        )",
    };

    // 回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) { return; };

    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 创建订阅
    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &received);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建订阅sink，不设置is_reliable 预期：成功
TEST_F(CreateSubscribtion, STREAM_020_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer);"
        },
        {
            "create stream view v1 as select * from t1;"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, age FROM v1 INTO pubsub_channel with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "s1",
                "schema_version": 0,
                "subs_type": "message_queue",
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] }
                ]     
            }
        )",
    };

    // 回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) { return; };

    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 创建订阅
    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建订阅sink，设置constraint 预期：失败
TEST_F(CreateSubscribtion, STREAM_020_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer);"
        },
        {
            "create stream view v1 as select * from t1;"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, age FROM v1 INTO pubsub_channel with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "s1",
                "schema_version": 0,
                "subs_type": "message_queue",
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] }
                ],
                "constraint": {
                    "operator_type":"and",
                    "conditions":
                    [ {"property": "age", "cmp_type":0,  "value": -2 } ]
                }   
            }
        )",
    };

    // 回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) { return; };

    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 创建订阅
    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &received);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);

    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    AW_FUN_Log(LOG_STEP, "test end.");
}
