/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 创建和删除流视图tuple_buffer_size规格测试
 * Author: tianyihui
 * Create: 2024-10-18
 */
#include "gtest/gtest.h"
#include "gmc_sql.h"
#include "rd_feature_stream.h"

class t02_stream_view_tuple_buffer_size : public testing::Test {
public:
    static GmcConnT *longConn;
    static GmcStmtT *longStmt;
    GmcConnT *shortConn;
    GmcStmtT *shortStmt;
    GmcStmtT *streamStmt = NULL;
    GmcStmtT *tsStmt = NULL;

    bool hasFinishTest = false;
    void RdFinishTest();

    static void SetUpTestCase();
    static void TearDownTestCase();
    virtual void SetUp();
    virtual void TearDown();
};

GmcConnT *t02_stream_view_tuple_buffer_size::longConn = NULL;
GmcStmtT *t02_stream_view_tuple_buffer_size::longStmt = NULL;

void t02_stream_view_tuple_buffer_size::SetUpTestCase()
{
    int32_t ret;
    ret = RdStreamEnvInit();
    ASSERT_EQ(GMERR_OK, ret);
    ret = RdGmcConnect(&longConn, &longStmt);
    ASSERT_EQ(GMERR_OK, ret);
}

void t02_stream_view_tuple_buffer_size::TearDownTestCase()
{
    int32_t ret;
    ret = RdGmcDisconnect(longConn, longStmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = RdStreamEnvClean();
    ASSERT_EQ(GMERR_OK, ret);
}

void t02_stream_view_tuple_buffer_size::SetUp()
{
    int32_t ret;
    hasFinishTest = false;
    ret = RdGmcConnect(&shortConn, &shortStmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcAllocStmt(shortConn, &streamStmt);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t modelStream = GMC_MODEL_STREAM;
    ret = GmcSetStmtAttr(streamStmt, GMC_STMT_ATTR_MODEL_TYPE, &modelStream, sizeof(modelStream));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcAllocStmt(shortConn, &tsStmt);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t modelTs = GMC_MODEL_TS;
    ret = GmcSetStmtAttr(tsStmt, GMC_STMT_ATTR_MODEL_TYPE, &modelTs, sizeof(modelTs));
    ASSERT_EQ(GMERR_OK, ret);
}

void t02_stream_view_tuple_buffer_size::TearDown()
{
    int32_t ret;
    if (!hasFinishTest) {
        RdStreamExecDescT desc[] = {
            {"drop stream view v1;"},
            {"drop stream table t1;"},
        };
        ret = RdStreamDropMetadata(longStmt, desc, sizeof(desc));
        ASSERT_EQ(GMERR_OK, ret);
    }

    GmcFreeStmt(streamStmt);
    GmcFreeStmt(tsStmt);
    ret = RdGmcDisconnect(shortConn, shortStmt);
    ASSERT_EQ(GMERR_OK, ret);
}

void t02_stream_view_tuple_buffer_size::RdFinishTest()
{
    hasFinishTest = true;
}

// @TestSuite tuple_buffer_size规格
// @TestcaseName STREAM_012_VIEW_005	创建前驱为视图的视图，tuple_buffer_size为1	成功
TEST_F(t02_stream_view_tuple_buffer_size, STREAM_012_VIEW_005)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT testDesc[] = {
        {"create stream table t1 (id integer, name char(50));"},
        {"create stream view viewFrom as select * from t1;"},
        {"create stream view v1 as select * from viewFrom with (tuple_buffer_size = 1);"},
        {"drop stream view v1;"},
        {"drop stream view viewFrom;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(shortStmt, testDesc, sizeof(testDesc));
    ASSERT_EQ(GMERR_OK, ret);
    RdFinishTest();
}

// @TestcaseName STREAM_012_VIEW_006	创建前驱为视图的视图，tuple_buffer_size为50,000	成功
TEST_F(t02_stream_view_tuple_buffer_size, STREAM_012_VIEW_006)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT testDesc[] = {
        {"create stream table t1 (id integer, name char(50));"},
        {"create stream view viewFrom as select * from t1;"},
        {"create stream view v1 as select * from viewFrom with (tuple_buffer_size = 50000);"},
        {"drop stream view v1;"},
        {"drop stream view viewFrom;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(shortStmt, testDesc, sizeof(testDesc));
    ASSERT_EQ(GMERR_OK, ret);
    RdFinishTest();
}

// @TestcaseName STREAM_012_VIEW_007	创建前驱为视图的视图，tuple_buffer_size为0	报错，规格错误
TEST_F(t02_stream_view_tuple_buffer_size, STREAM_012_VIEW_007)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT testDesc[] = {
        {"create stream table t1 (id integer, name char(50));"},
        {"create stream view viewFrom as select * from t1;"},
        {
            "create stream view v1 as select * from viewFrom with (tuple_buffer_size = 0);",
            GMC_MODEL_STREAM, GMERR_INVALID_PARAMETER_VALUE
        },
        {"drop stream view v1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view viewFrom;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(shortStmt, testDesc, sizeof(testDesc));
    ASSERT_EQ(GMERR_OK, ret);
    RdFinishTest();
}

// @TestcaseName STREAM_012_VIEW_008	创建前驱为视图的视图，tuple_buffer_size为-8	报错，规格错误
TEST_F(t02_stream_view_tuple_buffer_size, STREAM_012_VIEW_008)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT testDesc[] = {
        {"create stream table t1 (id integer, name char(50));"},
        {"create stream view viewFrom as select * from t1;"},
        {
            "create stream view v1 as select * from viewFrom with (tuple_buffer_size = -8);",
            GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR
        },
        {"drop stream view v1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view viewFrom;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(shortStmt, testDesc, sizeof(testDesc));
    ASSERT_EQ(GMERR_OK, ret);
    RdFinishTest();
}

// @TestcaseName STREAM_012_VIEW_009	创建前驱为视图的视图，tuple_buffer_size为50,001	报错，规格错误
TEST_F(t02_stream_view_tuple_buffer_size, STREAM_012_VIEW_009)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT testDesc[] = {
        {"create stream table t1 (id integer, name char(50));"},
        {"create stream view viewFrom as select * from t1;"},
        {
            "create stream view v1 as select * from viewFrom with (tuple_buffer_size = 50001);",
            GMC_MODEL_STREAM, GMERR_INVALID_PARAMETER_VALUE
        },
        {"drop stream view v1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view viewFrom;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(shortStmt, testDesc, sizeof(testDesc));
    ASSERT_EQ(GMERR_OK, ret);
    RdFinishTest();
}

