/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 重演DLR和SINK、VIEW交互
 * Author: guopanpan
 * Create: 2024-11-21
 */
#include "gtest/gtest.h"
#include "gmc_sql.h"
#include "rd_feature_stream.h"
#include "rd_feature_ts.h"
#include "rd_stream_nineteen.h"

class t08_replay_dlr_buffer_sink_and_view : public testing::Test {
public:
    static GmcConnT *longConn;
    static GmcStmtT *longStmt;
    GmcConnT *shortConn = NULL;
    GmcStmtT *shortStmt = NULL;
    GmcConnT *asyncConn = NULL;
    GmcStmtT *asyncStmt = NULL;
    RdVertexLabelT *vertexLabel = NULL;

    bool hasFinishTest = false;
    void RdFinishTest();

    static void SetUpTestCase();
    static void TearDownTestCase();
    virtual void SetUp();
    virtual void TearDown();
};

GmcConnT *t08_replay_dlr_buffer_sink_and_view::longConn = NULL;
GmcStmtT *t08_replay_dlr_buffer_sink_and_view::longStmt = NULL;

void t08_replay_dlr_buffer_sink_and_view::SetUpTestCase()
{
    int32_t ret;
    ret = RdStreamEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdGmcConnect(&longConn, &longStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdCreateEpollThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdLogSetLevel4Stdout(RD_LOG_LEVEL_INFO);
}

void t08_replay_dlr_buffer_sink_and_view::TearDownTestCase()
{
    int32_t ret;
    ret = RdGmcDisconnect(longConn, longStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdStreamEnvClean();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdCloseEpollThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void t08_replay_dlr_buffer_sink_and_view::SetUp()
{
    int32_t ret;
    hasFinishTest = false;
    ret = RdGmcConnect(&shortConn, &shortStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdGmcConnect(&asyncConn, &asyncStmt, GMC_CONN_TYPE_ASYNC);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void t08_replay_dlr_buffer_sink_and_view::TearDown()
{
    int32_t ret;
    if (!hasFinishTest) {
        RdStreamExecDescT clean[] = {
            {"drop stream sink s1;"},
            {"drop stream sink s2;"},
            {"drop stream sink s3;"},
            {"drop stream sink s4;"},

            {"drop stream view v9;"},
            {"drop stream view v8;"},
            {"drop stream view v7;"},
            {"drop stream view v6;"},
            {"drop stream view v5;"},
            {"drop stream view v4;"},
            {"drop stream view v3;"},
            {"drop stream view v2;"},
            {"drop stream view v1;"},

            {"drop stream table t1;"},
            {"drop stream table t2;"},
            {"drop stream table t3;"},

            {"drop table ts1;"},
        };
        ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;
    ret = RdGmcDisconnect(shortConn, shortStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdGmcDisconnect(asyncConn, asyncStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void t08_replay_dlr_buffer_sink_and_view::RdFinishTest()
{
    hasFinishTest = true;
}

// @TestSuite sink功能
// @TestcaseName sink的前驱是流表，重演dlr	成功
TEST_F(t08_replay_dlr_buffer_sink_and_view, STREAM_019_044)
{
    int32_t ret;
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content char(120)) "
            "with (time_col = 'time', interval = '1 hour');"},

        {"create stream table t1 (id integer, name char(50), time integer, level integer, content char(120), "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select * from v3 into pubsub_channel with (batch_window_size = 100);"},

        {"create stream table t2 (id integer, name char(50), time integer, level integer, content char(120), "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream sink s2 as select * from t2 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 下发订阅
    AW_FUN_Log(LOG_STEP, "start test.");
    RdNineteenSubUserDataT userData;
    GmcConnT *subConn = NULL;
    RdNineteenSubCfgT subCfg = {
        .subsName = "stream_sub",
        .sinkName = "s1",
        .userCb = RdNineteenGetDlrDataBuf,
        .userData = &userData,
    };
    ret = RdNineteenSubscribe(shortStmt, subCfg, &subConn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    uint32_t rowNum = 200;
    ret = RdNineteenStructWrite(shortStmt, vertexLabel, 0, rowNum, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 等待推送结束后进行重演，并校验数据
    ret = RdNineteenWaitSnRecv(&userData, rowNum, 20);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdNineteenReplayAsync(asyncConn, asyncStmt, "t2", &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdNineteenQueryTsTableAllData(shortStmt, "ts1", 0, rowNum, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 清理测试数据
    ret = RdNineteenUnSubscribe(shortStmt, subCfg.subsName, subConn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdNineteenFreeDlrDataBuf(&userData);

    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},

        {"drop stream sink s2;"},
        {"drop stream table t2;"},

        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdFinishTest();
}

// @TestcaseName sink的前驱是流视图，重演dlr	成功
TEST_F(t08_replay_dlr_buffer_sink_and_view, STREAM_019_045)
{
    int32_t ret;
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content char(120)) "
            "with (time_col = 'time', interval = '1 hour');"},

        {"create stream table t1 (id integer, name char(50), time integer, level integer, content char(120), "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select * from v3 into pubsub_channel with (batch_window_size = 100);"},

        {"create stream table t2 (id integer, name char(50), time integer, level integer, content char(120), "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v4 as select * from t2 with (tuple_buffer_size = 1);"},
        {"create stream sink s2 as select * from v4 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 下发订阅
    AW_FUN_Log(LOG_STEP, "start test.");
    RdNineteenSubUserDataT userData;
    GmcConnT *subConn = NULL;
    RdNineteenSubCfgT subCfg = {
        .subsName = "stream_sub",
        .sinkName = "s1",
        .userCb = RdNineteenGetDlrDataBuf,
        .userData = &userData,
    };
    ret = RdNineteenSubscribe(shortStmt, subCfg, &subConn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    uint32_t rowNum = 200;
    ret = RdNineteenStructWrite(shortStmt, vertexLabel, 0, rowNum, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 等待推送结束后进行重演，并校验数据
    ret = RdNineteenWaitSnRecv(&userData, rowNum, 20);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdNineteenReplayAsync(asyncConn, asyncStmt, "t2", &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdNineteenQueryTsTableAllData(shortStmt, "ts1", 0, rowNum, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 清理测试数据
    ret = RdNineteenUnSubscribe(shortStmt, subCfg.subsName, subConn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdNineteenFreeDlrDataBuf(&userData);

    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},

        {"drop stream sink s2;"},
        {"drop stream view v4;"},
        {"drop stream table t2;"},

        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdFinishTest();
}

// @TestcaseName 创建订阅推送sink和写时序表sink，重演dlr	成功
TEST_F(t08_replay_dlr_buffer_sink_and_view, STREAM_019_046)
{
    int32_t ret;
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content char(120)) "
            "with (time_col = 'time', interval = '1 hour');"},

        {"create stream table t1 (id integer, name char(50), time integer, level integer, content char(120), "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select * from v3 into pubsub_channel with (batch_window_size = 100);"},

        {"create stream table t2 (id integer, name char(50), time integer, level integer, content char(120), "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v4 as select * from t2 with (tuple_buffer_size = 1);"},
        {"create stream view v5 as select * from v4 with (tuple_buffer_size = 1);"},
        {"create stream view v6 as select * from v5 with (tuple_buffer_size = 1);"},
        {"create stream sink s2 as select * from v6 into tsdb (ts1) with (batch_window_size = 1);"},
        {"create stream sink s3 as select * from v6 into pubsub_channel with (batch_window_size = 100);"},

        {"create stream table t3 (id integer, name char(50), time integer, level integer, content char(120), "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v7 as select * from t3 with (tuple_buffer_size = 1);"},
        {"create stream view v8 as select * from v7 with (tuple_buffer_size = 1);"},
        {"create stream sink s4 as select * from v8 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 下发订阅
    AW_FUN_Log(LOG_STEP, "start test.");
    RdNineteenSubUserDataT userData1;
    GmcConnT *subConn1 = NULL;
    RdNineteenSubCfgT subCfg1 = {
        .subsName = "stream_sub",
        .sinkName = "s1",
        .userCb = RdNineteenGetDlrDataBuf,
        .userData = &userData1,
    };
    ret = RdNineteenSubscribe(shortStmt, subCfg1, &subConn1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    RdNineteenSubUserDataT userData2;
    GmcConnT *subConn2 = NULL;
    RdNineteenSubCfgT subCfg2 = {
        .subsName = "stream_sub_02",
        .sinkName = "s3",
        .userCb = RdNineteenGetDlrDataBuf,
        .userData = &userData2,
    };
    ret = RdNineteenSubscribe(shortStmt, subCfg2, &subConn2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    uint32_t rowNum = 200;
    ret = RdNineteenStructWrite(shortStmt, vertexLabel, 0, rowNum, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 等待推送结束后进行重演，并校验数据
    ret = RdNineteenWaitSnRecv(&userData1, rowNum, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdNineteenReplayAsync(asyncConn, asyncStmt, "t2", &userData1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckRecordCount(shortStmt, "ts1", rowNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = RdNineteenWaitSnRecv(&userData2, rowNum, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdNineteenReplayAsync(asyncConn, asyncStmt, "t3", &userData2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckRecordCount(shortStmt, "ts1", rowNum * 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 清理测试数据
    ret = RdNineteenUnSubscribe(shortStmt, subCfg1.subsName, subConn1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdNineteenUnSubscribe(shortStmt, subCfg2.subsName, subConn2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdNineteenFreeDlrDataBuf(&userData1);
    RdNineteenFreeDlrDataBuf(&userData2);

    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},

        {"drop stream sink s2;"},
        {"drop stream sink s3;"},
        {"drop stream view v6;"},
        {"drop stream view v5;"},
        {"drop stream view v4;"},
        {"drop stream table t2;"},

        {"drop stream sink s4;"},
        {"drop stream view v8;"},
        {"drop stream view v7;"},
        {"drop stream table t3;"},

        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdFinishTest();
}

// @TestcaseName 创建订阅推送sink和写时序表sink，订阅推送sink未被订阅，重演dlr	成功
TEST_F(t08_replay_dlr_buffer_sink_and_view, STREAM_019_047)
{
    int32_t ret;
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content char(120)) "
            "with (time_col = 'time', interval = '1 hour');"},

        {"create stream table t1 (id integer, name char(50), time integer, level integer, content char(120), "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select * from v3 into pubsub_channel with (batch_window_size = 100);"},

        {"create stream table t2 (id integer, name char(50), time integer, level integer, content char(120), "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v4 as select * from t2 with (tuple_buffer_size = 1);"},
        {"create stream view v5 as select * from v4 with (tuple_buffer_size = 1);"},
        {"create stream view v6 as select * from v5 with (tuple_buffer_size = 1);"},
        {"create stream sink s2 as select * from v6 into tsdb (ts1) with (batch_window_size = 1);"},
        {"create stream sink s3 as select * from v6 into pubsub_channel with (batch_window_size = 100);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 下发订阅
    AW_FUN_Log(LOG_STEP, "start test.");
    RdNineteenSubUserDataT userData;
    GmcConnT *subConn = NULL;
    RdNineteenSubCfgT subCfg = {
        .subsName = "stream_sub",
        .sinkName = "s1",
        .userCb = RdNineteenGetDlrDataBuf,
        .userData = &userData,
    };
    ret = RdNineteenSubscribe(shortStmt, subCfg, &subConn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    uint32_t rowNum = 200;
    ret = RdNineteenStructWrite(shortStmt, vertexLabel, 0, rowNum, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 等待推送结束后进行重演，并校验数据
    ret = RdNineteenWaitSnRecv(&userData, rowNum, 20);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdNineteenReplayAsync(asyncConn, asyncStmt, "t2", &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdNineteenQueryTsTableAllData(shortStmt, "ts1", 0, rowNum, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 清理测试数据
    ret = RdNineteenUnSubscribe(shortStmt, subCfg.subsName, subConn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdNineteenFreeDlrDataBuf(&userData);

    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},

        {"drop stream sink s2;"},
        {"drop stream sink s3;"},
        {"drop stream view v6;"},
        {"drop stream view v5;"},
        {"drop stream view v4;"},
        {"drop stream table t2;"},

        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdFinishTest();
}

// @TestcaseName 创建包含连续去重函数的sink，重演dlr	成功
TEST_F(t08_replay_dlr_buffer_sink_and_view, STREAM_019_048)
{
    int32_t ret;
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content char(120), count integer) "
            "with (time_col = 'time', interval = '1 hour');"},

        {"create stream table t1 (id integer, name char(50), time integer, level integer, content char(120), "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select * from v3 into pubsub_channel with (batch_window_size = 100);"},

        {"create stream table t2 (id integer, name char(50), time integer, level integer, content char(120), "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v4 as select * from t2 with (tuple_buffer_size = 1);"},
        {"create stream view v5 as select * from v4 with (tuple_buffer_size = 1);"},
        {"create stream view v6 as select * from v5 with (tuple_buffer_size = 1);"},
        {"create stream sink s2 as select *, seq_distinct_count(id, name) from v6 into tsdb (ts1) "
            "with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 下发订阅
    AW_FUN_Log(LOG_STEP, "start test.");
    RdNineteenSubUserDataT userData;
    GmcConnT *subConn = NULL;
    RdNineteenSubCfgT subCfg = {
        .subsName = "stream_sub",
        .sinkName = "s1",
        .userCb = RdNineteenGetDlrDataBuf,
        .userData = &userData,
    };
    ret = RdNineteenSubscribe(shortStmt, subCfg, &subConn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    uint32_t rowNum = 200;
    ret = RdNineteenStructWrite(shortStmt, vertexLabel, 0, rowNum, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 等待推送结束后进行重演，并校验数据
    ret = RdNineteenWaitSnRecv(&userData, rowNum, 20);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdNineteenReplayAsync(asyncConn, asyncStmt, "t2", &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdNineteenQueryTsTableAllData(shortStmt, "ts1", 0, rowNum - 1, 0); // 最后一条数据残留在流引擎内
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 清理测试数据
    ret = RdNineteenUnSubscribe(shortStmt, subCfg.subsName, subConn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdNineteenFreeDlrDataBuf(&userData);

    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},

        {"drop stream sink s2;"},
        {"drop stream view v6;"},
        {"drop stream view v5;"},
        {"drop stream view v4;"},
        {"drop stream table t2;"},

        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdFinishTest();
}

// @TestcaseName 创建包含关系运算符、逻辑运算符和like运算符的sink，重演dlr	成功
TEST_F(t08_replay_dlr_buffer_sink_and_view, STREAM_019_049)
{
    int32_t ret;
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content char(120)) "
            "with (time_col = 'time', interval = '1 hour');"},

        {"create stream table t1 (id integer, name char(50), time integer, level integer, content char(120), "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select * from v3 into pubsub_channel with (batch_window_size = 100);"},

        {"create stream table t2 (id integer, name char(50), time integer, level integer, content char(120), "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v4 as select * from t2 with (tuple_buffer_size = 1);"},
        {"create stream view v5 as select * from v4 with (tuple_buffer_size = 1);"},
        {"create stream view v6 as select * from v5 with (tuple_buffer_size = 1);"},
        {"create stream sink s2 as select * from v6 where id >= 10 and name like 'info%' "
            "into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 下发订阅
    AW_FUN_Log(LOG_STEP, "start test.");
    RdNineteenSubUserDataT userData;
    GmcConnT *subConn = NULL;
    RdNineteenSubCfgT subCfg = {
        .subsName = "stream_sub",
        .sinkName = "s1",
        .userCb = RdNineteenGetDlrDataBuf,
        .userData = &userData,
    };
    ret = RdNineteenSubscribe(shortStmt, subCfg, &subConn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    uint32_t rowNum = 200;
    ret = RdNineteenStructWrite(shortStmt, vertexLabel, 0, rowNum, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 等待推送结束后进行重演，并校验数据
    ret = RdNineteenWaitSnRecv(&userData, rowNum, 20);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdNineteenReplayAsync(asyncConn, asyncStmt, "t2", &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckRecordCount(shortStmt, "ts1", rowNum - 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdNineteenQueryTsTableAllData(shortStmt, "ts1", 10, rowNum - 10, 0); // 过滤id大于等于10的数据
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 清理测试数据
    ret = RdNineteenUnSubscribe(shortStmt, subCfg.subsName, subConn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdNineteenFreeDlrDataBuf(&userData);

    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},

        {"drop stream sink s2;"},
        {"drop stream view v6;"},
        {"drop stream view v5;"},
        {"drop stream view v4;"},
        {"drop stream table t2;"},

        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdFinishTest();
}

// @TestSuite 视图功能
// @TestcaseName 创建包含连续去重函数的流视图，重演dlr	成功
TEST_F(t08_replay_dlr_buffer_sink_and_view, STREAM_019_050)
{
    int32_t ret;
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content char(120), count integer) "
            "with (time_col = 'time', interval = '1 hour');"},

        {"create stream table t1 (id integer, name char(50), time integer, level integer, content char(120), "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select * from v3 into pubsub_channel with (batch_window_size = 100);"},

        {"create stream table t2 (id integer, name char(50), time integer, level integer, content char(120), "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v4 as select *, seq_distinct_count(id, name) from t2 with (tuple_buffer_size = 1);"},
        {"create stream view v5 as select * from v4 with (tuple_buffer_size = 1);"},
        {"create stream view v6 as select * from v5 with (tuple_buffer_size = 1);"},
        {"create stream sink s2 as select * from v6 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 下发订阅
    AW_FUN_Log(LOG_STEP, "start test.");
    RdNineteenSubUserDataT userData;
    GmcConnT *subConn = NULL;
    RdNineteenSubCfgT subCfg = {
        .subsName = "stream_sub",
        .sinkName = "s1",
        .userCb = RdNineteenGetDlrDataBuf,
        .userData = &userData,
    };
    ret = RdNineteenSubscribe(shortStmt, subCfg, &subConn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    uint32_t rowNum = 200;
    ret = RdNineteenStructWrite(shortStmt, vertexLabel, 0, rowNum, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 等待推送结束后进行重演，并校验数据
    ret = RdNineteenWaitSnRecv(&userData, rowNum, 20);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdNineteenReplayAsync(asyncConn, asyncStmt, "t2", &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdNineteenQueryTsTableAllData(shortStmt, "ts1", 0, rowNum - 1, 0); // 最后一条数据残留在流引擎内
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 清理测试数据
    ret = RdNineteenUnSubscribe(shortStmt, subCfg.subsName, subConn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdNineteenFreeDlrDataBuf(&userData);

    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},

        {"drop stream sink s2;"},
        {"drop stream view v6;"},
        {"drop stream view v5;"},
        {"drop stream view v4;"},
        {"drop stream table t2;"},

        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdFinishTest();
}

// @TestcaseName 创建包含关系运算符、逻辑运算符和like运算符的流视图，重演dlr	成功
TEST_F(t08_replay_dlr_buffer_sink_and_view, STREAM_019_051)
{
    int32_t ret;
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content char(120)) "
            "with (time_col = 'time', interval = '1 hour');"},

        {"create stream table t1 (id integer, name char(50), time integer, level integer, content char(120), "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select * from v3 into pubsub_channel with (batch_window_size = 100);"},

        {"create stream table t2 (id integer, name char(50), time integer, level integer, content char(120), "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v4 as select * from t2 with (tuple_buffer_size = 1);"},
        {"create stream view v5 as select * from v4 where id < 20 with (tuple_buffer_size = 1);"},
        {"create stream view v6 as select * from v5 where id >= 10 and name like 'info%' with (tuple_buffer_size = 1);"},
        {"create stream sink s2 as select * from v6 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 下发订阅
    AW_FUN_Log(LOG_STEP, "start test.");
    RdNineteenSubUserDataT userData;
    GmcConnT *subConn = NULL;
    RdNineteenSubCfgT subCfg = {
        .subsName = "stream_sub",
        .sinkName = "s1",
        .userCb = RdNineteenGetDlrDataBuf,
        .userData = &userData,
    };
    ret = RdNineteenSubscribe(shortStmt, subCfg, &subConn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    uint32_t rowNum = 200;
    ret = RdNineteenStructWrite(shortStmt, vertexLabel, 0, rowNum, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 等待推送结束后进行重演，并校验数据
    ret = RdNineteenWaitSnRecv(&userData, rowNum, 20);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdNineteenReplayAsync(asyncConn, asyncStmt, "t2", &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckRecordCount(shortStmt, "ts1", 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdNineteenQueryTsTableAllData(shortStmt, "ts1", 10, 10, 0); // 过滤id大于等于10且小于20的数据
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 清理测试数据
    ret = RdNineteenUnSubscribe(shortStmt, subCfg.subsName, subConn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdNineteenFreeDlrDataBuf(&userData);

    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},

        {"drop stream sink s2;"},
        {"drop stream view v6;"},
        {"drop stream view v5;"},
        {"drop stream view v4;"},
        {"drop stream table t2;"},

        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdFinishTest();
}

// @TestcaseName 流表定义水位，视图定义滑动窗口视图，重演dlr	成功
TEST_F(t08_replay_dlr_buffer_sink_and_view, STREAM_019_052)
{
    int32_t ret;
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content char(120)) "
            "with (time_col = 'time', interval = '1 hour');"},

        {"create stream table t1 (id integer, name char(50), time integer, level integer, content char(120), "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select * from v3 into pubsub_channel with (batch_window_size = 100);"},

        {"create stream table t2 (id integer, name char(50), time integer, level integer, content char(120), "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v4 as select * from "
            "table(hop(table t2, time, interval '10' seconds, interval '30' seconds)) with (tuple_buffer_size = 1);"},
        {"create stream view v5 as select * from v4 with (tuple_buffer_size = 1);"},
        {"create stream view v6 as select id, name, time, level, content from v5 with (tuple_buffer_size = 1);"},
        {"create stream sink s2 as select * from v6 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 下发订阅
    AW_FUN_Log(LOG_STEP, "start test.");
    RdNineteenSubUserDataT userData;
    GmcConnT *subConn = NULL;
    RdNineteenSubCfgT subCfg = {
        .subsName = "stream_sub",
        .sinkName = "s1",
        .userCb = RdNineteenGetDlrDataBuf,
        .userData = &userData,
    };
    ret = RdNineteenSubscribe(shortStmt, subCfg, &subConn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    uint32_t rowNum = 200;
    ret = RdNineteenStructWrite(shortStmt, vertexLabel, 0, rowNum, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 等待推送结束后进行重演，并校验数据
    ret = RdNineteenWaitSnRecv(&userData, rowNum, 20);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdNineteenReplayAsync(asyncConn, asyncStmt, "t2", &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckRecordCount(shortStmt, "ts1", rowNum * 3);   // 当前用例的窗口将一份数据分裂成3份
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 清理测试数据
    ret = RdNineteenUnSubscribe(shortStmt, subCfg.subsName, subConn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdNineteenFreeDlrDataBuf(&userData);

    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},

        {"drop stream sink s2;"},
        {"drop stream view v6;"},
        {"drop stream view v5;"},
        {"drop stream view v4;"},
        {"drop stream table t2;"},

        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdFinishTest();
}

// @TestcaseName 流表定义水位，视图定义滚动窗口视图，重演dlr	成功
TEST_F(t08_replay_dlr_buffer_sink_and_view, STREAM_019_053)
{
    int32_t ret;
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content char(120)) "
            "with (time_col = 'time', interval = '1 hour');"},

        {"create stream table t1 (id integer, name char(50), time integer, level integer, content char(120), "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select * from v3 into pubsub_channel with (batch_window_size = 100);"},

        {"create stream table t2 (id integer, name char(50), time integer, level integer, content char(120), "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v4 as select * from "
            "table(tumble(table t2, time, interval '10' seconds)) with (tuple_buffer_size = 1);"},
        {"create stream view v5 as select * from v4 with (tuple_buffer_size = 1);"},
        {"create stream view v6 as select * from v5 with (tuple_buffer_size = 1);"},
        {"create stream sink s2 as select id, name, time, level, content from v6 into tsdb (ts1) "
            "with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 下发订阅
    AW_FUN_Log(LOG_STEP, "start test.");
    RdNineteenSubUserDataT userData;
    GmcConnT *subConn = NULL;
    RdNineteenSubCfgT subCfg = {
        .subsName = "stream_sub",
        .sinkName = "s1",
        .userCb = RdNineteenGetDlrDataBuf,
        .userData = &userData,
    };
    ret = RdNineteenSubscribe(shortStmt, subCfg, &subConn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    uint32_t rowNum = 200;
    ret = RdNineteenStructWrite(shortStmt, vertexLabel, 0, rowNum, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 等待推送结束后进行重演，并校验数据
    ret = RdNineteenWaitSnRecv(&userData, rowNum, 20);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdNineteenReplayAsync(asyncConn, asyncStmt, "t2", &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdNineteenQueryTsTableAllData(shortStmt, "ts1", 0, rowNum, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 清理测试数据
    ret = RdNineteenUnSubscribe(shortStmt, subCfg.subsName, subConn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdNineteenFreeDlrDataBuf(&userData);

    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},

        {"drop stream sink s2;"},
        {"drop stream view v6;"},
        {"drop stream view v5;"},
        {"drop stream view v4;"},
        {"drop stream table t2;"},

        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdFinishTest();
}

// @TestcaseName 创建包含row_number函数的视图，重演dlr	成功
TEST_F(t08_replay_dlr_buffer_sink_and_view, STREAM_019_054)
{
    int32_t ret;
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content char(120), "
            "row_number integer) with (time_col = 'time', interval = '1 hour');"},

        {"create stream table t1 (id integer, name char(50), time integer, level integer, content char(120), "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select * from v3 into pubsub_channel with (batch_window_size = 100);"},

        {"create stream table t2 (id integer, name char(50), time integer, level integer, content char(120), "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v4 as select *, row_number() over (partition by window_start, window_end, id) from "
            "table(hop(table t2, time, interval '10' seconds, interval '30' seconds)) with (tuple_buffer_size = 1);"},
        {"create stream view v5 as select * from v4 with (tuple_buffer_size = 1);"},
        {"create stream view v6 as select * from v5 with (tuple_buffer_size = 1);"},
        {"create stream sink s2 as select id, name, time, level, content, row_number "
            "from v6 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 下发订阅
    AW_FUN_Log(LOG_STEP, "start test.");
    RdNineteenSubUserDataT userData;
    GmcConnT *subConn = NULL;
    RdNineteenSubCfgT subCfg = {
        .subsName = "stream_sub",
        .sinkName = "s1",
        .userCb = RdNineteenGetDlrDataBuf,
        .userData = &userData,
    };
    ret = RdNineteenSubscribe(shortStmt, subCfg, &subConn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    uint32_t rowNum = 200;
    ret = RdNineteenStructWrite(shortStmt, vertexLabel, 0, rowNum, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 等待推送结束后进行重演，并校验数据
    ret = RdNineteenWaitSnRecv(&userData, rowNum, 20);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdNineteenReplayAsync(asyncConn, asyncStmt, "t2", &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckRecordCount(shortStmt, "ts1", rowNum * 3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 清理测试数据
    ret = RdNineteenUnSubscribe(shortStmt, subCfg.subsName, subConn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdNineteenFreeDlrDataBuf(&userData);

    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},

        {"drop stream sink s2;"},
        {"drop stream view v6;"},
        {"drop stream view v5;"},
        {"drop stream view v4;"},
        {"drop stream table t2;"},

        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdFinishTest();
}

// @TestcaseName 创建包含max函数的视图，重演dlr	成功
TEST_F(t08_replay_dlr_buffer_sink_and_view, STREAM_019_055)
{
    int32_t ret;
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content char(120), "
            "max_level integer, min_level integer, sum_time integer, count integer) "
            "with (time_col = 'time', interval = '1 hour');"},

        {"create stream table t1 (id integer, name char(50), time integer, level integer, content char(120), "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select * from v3 into pubsub_channel with (batch_window_size = 100);"},

        {"create stream table t2 (id integer, name char(50), time integer, level integer, content char(120), "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v4 as select *, "
            "max(level) over (partition by window_start, window_end, id), "
            "min(level) over (partition by window_start, window_end, id), "
            "sum(time) over (partition by window_start, window_end, id), "
            "count(content) over (partition by window_start, window_end, id) "
            "from table(hop(table t2, time, interval '10' seconds, interval '30' seconds)) "
            "with (tuple_buffer_size = 1);"},
        {"create stream view v5 as select * from v4 with (tuple_buffer_size = 1);"},
        {"create stream view v6 as select * from v5 with (tuple_buffer_size = 1);"},
        {"create stream sink s2 as "
            "select id, name, time, level, content, max_level, min_level, sum_time, count_content "
            "from v6 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 下发订阅
    AW_FUN_Log(LOG_STEP, "start test.");
    RdNineteenSubUserDataT userData;
    GmcConnT *subConn = NULL;
    RdNineteenSubCfgT subCfg = {
        .subsName = "stream_sub",
        .sinkName = "s1",
        .userCb = RdNineteenGetDlrDataBuf,
        .userData = &userData,
    };
    ret = RdNineteenSubscribe(shortStmt, subCfg, &subConn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    uint32_t rowNum = 200;
    ret = RdNineteenStructWrite(shortStmt, vertexLabel, 0, rowNum, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 等待推送结束后进行重演，并校验数据
    ret = RdNineteenWaitSnRecv(&userData, rowNum, 20);
    AW_MACRO_ASSERT_EQ_INT(RD_OK, ret);
    ret = RdNineteenReplayAsync(asyncConn, asyncStmt, "t2", &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 部分窗口还没关闭，数据未推送
    ret = RdTsCheckRecordCount(shortStmt, "ts1", 180 * 3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 清理测试数据
    ret = RdNineteenUnSubscribe(shortStmt, subCfg.subsName, subConn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdNineteenFreeDlrDataBuf(&userData);

    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},

        {"drop stream sink s2;"},
        {"drop stream view v6;"},
        {"drop stream view v5;"},
        {"drop stream view v4;"},
        {"drop stream table t2;"},

        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdFinishTest();
}
