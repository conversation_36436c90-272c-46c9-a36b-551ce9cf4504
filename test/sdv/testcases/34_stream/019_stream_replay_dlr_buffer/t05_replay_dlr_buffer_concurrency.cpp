/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 重演dlr data buffer并发场景
 * Author: guopanpan
 * Create: 2024-11-21
 */
#include "gtest/gtest.h"
#include "gmc_sql.h"
#include "rd_feature_stream.h"
#include "rd_feature_ts.h"
#include "rd_stream_nineteen.h"

class t05_replay_dlr_buffer_concurrency : public testing::Test {
public:
    static GmcConnT *longConn;
    static GmcStmtT *longStmt;
    GmcConnT *shortConn = NULL;
    GmcStmtT *shortStmt = NULL;
    GmcConnT *asyncConn = NULL;
    GmcStmtT *asyncStmt = NULL;
    RdVertexLabelT *vertexLabel1 = NULL;    // 1号流表
    RdVertexLabelT *vertexLabel5 = NULL;    // 5号流表

    bool hasFinishTest = false;
    void RdFinishTest();

    static void SetUpTestCase();
    static void TearDownTestCase();
    virtual void SetUp();
    virtual void TearDown();
};

GmcConnT *t05_replay_dlr_buffer_concurrency::longConn = NULL;
GmcStmtT *t05_replay_dlr_buffer_concurrency::longStmt = NULL;

void t05_replay_dlr_buffer_concurrency::SetUpTestCase()
{
    int32_t ret;
    ret = RdStreamEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdGmcConnect(&longConn, &longStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdCreateEpollThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdLogSetLevel4Stdout(RD_LOG_LEVEL_INFO);
}

void t05_replay_dlr_buffer_concurrency::TearDownTestCase()
{
    int32_t ret;
    ret = RdGmcDisconnect(longConn, longStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdStreamEnvClean();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdCloseEpollThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void t05_replay_dlr_buffer_concurrency::SetUp()
{
    int32_t ret;
    hasFinishTest = false;
    ret = RdGmcConnect(&shortConn, &shortStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdGmcConnect(&asyncConn, &asyncStmt, GMC_CONN_TYPE_ASYNC);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 结构化写需要解析SQL，依赖SQL定义顺序，请勿随意修改，否则可能导致用例失败
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content char(120)) "
            "with (time_col = 'time', interval = '1 hour');"},

        {"create stream table t1 (id integer, name char(50), time integer, level integer, content char(120), "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select * from v3 into pubsub_channel with (batch_window_size = 100);"},

        {"create stream table t2 (id integer, name char(50), time integer, level integer, content char(120), "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v4 as select * from t2 with (tuple_buffer_size = 1);"},
        {"create stream view v5 as select * from v4 with (tuple_buffer_size = 1);"},
        {"create stream view v6 as select * from v5 with (tuple_buffer_size = 1);"},
        {"create stream sink s2 as select * from v6 into tsdb (ts1) with (batch_window_size = 1);"},

        {"create stream table t3 (id integer, name char(50), time integer, level integer, content char(120), "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v7 as select * from t3 with (tuple_buffer_size = 1);"},
        {"create stream sink s3 as select * from v7 into tsdb (ts1) with (batch_window_size = 1);"},
        {"create stream sink s4 as select * from v7 into pubsub_channel with (batch_window_size = 100);"},

        {"create stream table t4 (id integer, name char(50), time integer, level integer, content char(120), "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v8 as select * from t4 with (tuple_buffer_size = 1);"},
        {"create stream sink s5 as select * from v8 into tsdb (ts1) with (batch_window_size = 1);"},

        {"create stream table t5 (id integer, name char(50), time integer, level integer, content char(120), "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v9 as select * from t5 with (tuple_buffer_size = 1);"},
        {"create stream sink s6 as select * from v9 into pubsub_channel with (batch_window_size = 100);"},
        {"create stream sink s7 as select * from v9 into pubsub_channel with (batch_window_size = 100);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel1 = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel1);
    vertexLabel5 = RdStreamParseTableSchema(prepare[18].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel5);
}

void t05_replay_dlr_buffer_concurrency::TearDown()
{
    int32_t ret;
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},

        {"drop stream sink s2;"},
        {"drop stream view v6;"},
        {"drop stream view v5;"},
        {"drop stream view v4;"},
        {"drop stream table t2;"},

        {"drop stream sink s4;"},
        {"drop stream sink s3;"},
        {"drop stream view v7;"},
        {"drop stream table t3;"},

        {"drop stream sink s5;"},
        {"drop stream view v8;"},
        {"drop stream table t4;"},

        {"drop stream sink s6;"},
        {"drop stream sink s7;"},
        {"drop stream view v9;"},
        {"drop stream table t5;"},

        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel1);
    RdStreamFreeTableSchema(vertexLabel5);
    ret = RdGmcDisconnect(shortConn, shortStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdGmcDisconnect(asyncConn, asyncStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void t05_replay_dlr_buffer_concurrency::RdFinishTest()
{
    hasFinishTest = true;
}

// @TestSuite 并发场景
// @TestcaseName 预置一份dlr data buffer，多个线程并发使用相同dlr data buffer重演，写入不同流表	成功或返回错误码
TEST_F(t05_replay_dlr_buffer_concurrency, STREAM_019_030)
{
    // 下发订阅
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");
    RdNineteenSubUserDataT userData1;
    GmcConnT *subConn1 = NULL;
    RdNineteenSubCfgT subCfg1 = {
        .subsName = "stream_sub_01",
        .sinkName = "s1",
        .userCb = RdNineteenGetDlrDataBuf,
        .userData = &userData1,
    };
    ret = RdNineteenSubscribe(shortStmt, subCfg1, &subConn1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    uint32_t rowNum = 200;
    ret = RdNineteenStructWrite(shortStmt, vertexLabel1, 0, rowNum, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 等待订阅推送结束
    ret = RdNineteenWaitSnRecv(&userData1, rowNum, 20);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(rowNum, userData1.realFetchNum);

    // 并发重演DLR
    const char *dstTableName[] = {"t2", "t4"};
    const uint32_t threadNum = 2;
    pthread_t threads[threadNum];
    RdNineteenReplayCtxT ctxs[threadNum] = {0};
    for (int32_t i = 0; i < threadNum; i++) {
        ctxs[i] = {
            .index = i,
            .status = 0,
            .tableName = {0},
            .subUserData = &userData1,
            // HISTORY 不允许并发访问dlr buffer，同一个dlr buffer仅允许被消费一次，验证进程不崩溃即可
            .ignoreError = true,
        };
        (void)strcpy_s(ctxs[i].tableName, RD_MAX_NAME_LEN, dstTableName[i % 2]);
        ret = pthread_create(&threads[i], NULL, RdNineteenReplayWorker, &ctxs[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int32_t i = 0; i < threadNum; i++) {
        ret = pthread_join(threads[i], NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int32_t i = 0; i < threadNum; i++) {
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ctxs[i].status);
    }

    // 清理测试数据
    ret = RdNineteenUnSubscribe(shortStmt, subCfg1.subsName, subConn1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdNineteenFreeDlrDataBuf(&userData1);
    RdFinishTest();
}

// @TestcaseName 预置多份dlr data buffer，多个线程并发使用不同dlr data buffer重演，写入不同流表	成功
TEST_F(t05_replay_dlr_buffer_concurrency, STREAM_019_031)
{
    // 下发订阅
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");
    RdNineteenSubUserDataT userData1;
    GmcConnT *subConn1 = NULL;
    RdNineteenSubCfgT subCfg1 = {
        .subsName = "stream_sub_01",
        .sinkName = "s6",
        .userCb = RdNineteenGetDlrDataBuf,
        .userData = &userData1,
    };
    ret = RdNineteenSubscribe(shortStmt, subCfg1, &subConn1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    RdNineteenSubUserDataT userData2;
    GmcConnT *subConn2 = NULL;
    RdNineteenSubCfgT subCfg2 = {
        .subsName = "stream_sub_02",
        .sinkName = "s7",
        .userCb = RdNineteenGetDlrDataBuf,
        .userData = &userData2,
    };
    ret = RdNineteenSubscribe(shortStmt, subCfg2, &subConn2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    uint32_t rowNum = 200;
    ret = RdNineteenStructWrite(shortStmt, vertexLabel5, 0, rowNum, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 等待订阅推送结束
    ret = RdNineteenWaitSnRecv(&userData1, rowNum, 20);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(rowNum, userData1.realFetchNum);
    ret = RdNineteenWaitSnRecv(&userData2, rowNum, 20);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(rowNum, userData2.realFetchNum);

    // 并发重演DLR
    RdNineteenSubUserDataT *userDatas[] = {&userData1, &userData2};
    const char *dstTableName[] = {"t2", "t4"};
    const uint32_t threadNum = 2;
    pthread_t threads[threadNum];
    RdNineteenReplayCtxT ctxs[threadNum] = {0};
    for (int32_t i = 0; i < threadNum; i++) {
        ctxs[i] = {
            .index = i,
            .status = 0,
            .tableName = {0},
            .subUserData = userDatas[i % 2],
            .ignoreError = false,
        };
        (void)strcpy_s(ctxs[i].tableName, RD_MAX_NAME_LEN, dstTableName[i % 2]);
        ret = pthread_create(&threads[i], NULL, RdNineteenReplayWorker, &ctxs[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int32_t i = 0; i < threadNum; i++) {
        ret = pthread_join(threads[i], NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int32_t i = 0; i < threadNum; i++) {
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ctxs[i].status);
    }

    // 查询校验数据
    ret = RdTsCheckRecordCount(shortStmt, "ts1", rowNum * 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 清理测试数据
    ret = RdNineteenUnSubscribe(shortStmt, subCfg1.subsName, subConn1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdNineteenUnSubscribe(shortStmt, subCfg2.subsName, subConn2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdNineteenFreeDlrDataBuf(&userData1);
    RdNineteenFreeDlrDataBuf(&userData2);
    RdFinishTest();
}

// @TestcaseName 预置一份dlr data buffer，多个线程并发使用相同dlr data buffer重演，写入相同流表	成功或返回错误码
TEST_F(t05_replay_dlr_buffer_concurrency, STREAM_019_032)
{
    // 下发订阅
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");
    RdNineteenSubUserDataT userData1;
    GmcConnT *subConn1 = NULL;
    RdNineteenSubCfgT subCfg1 = {
        .subsName = "stream_sub_01",
        .sinkName = "s1",
        .userCb = RdNineteenGetDlrDataBuf,
        .userData = &userData1,
    };
    ret = RdNineteenSubscribe(shortStmt, subCfg1, &subConn1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    uint32_t rowNum = 200;
    ret = RdNineteenStructWrite(shortStmt, vertexLabel1, 0, rowNum, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 等待订阅推送结束
    ret = RdNineteenWaitSnRecv(&userData1, rowNum, 20);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(rowNum, userData1.realFetchNum);

    // 并发重演DLR
    const uint32_t threadNum = 2;
    pthread_t threads[threadNum];
    RdNineteenReplayCtxT ctxs[threadNum] = {0};
    for (int32_t i = 0; i < threadNum; i++) {
        ctxs[i] = {
            .index = i,
            .status = 0,
            .tableName = {0},
            .subUserData = &userData1,
            // 不允许并发访问dlr buffer，同一个dlr buffer仅允许被消费一次，验证进程不崩溃即可
            .ignoreError = true,
        };
        (void)strcpy_s(ctxs[i].tableName, RD_MAX_NAME_LEN, "t2");
        ret = pthread_create(&threads[i], NULL, RdNineteenReplayWorker, &ctxs[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int32_t i = 0; i < threadNum; i++) {
        ret = pthread_join(threads[i], NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int32_t i = 0; i < threadNum; i++) {
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ctxs[i].status);
    }

    // 清理测试数据
    ret = RdNineteenUnSubscribe(shortStmt, subCfg1.subsName, subConn1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdNineteenFreeDlrDataBuf(&userData1);
    RdFinishTest();
}

// @TestcaseName 预置多份dlr data buffer，多个线程并发使用不同dlr data buffer重演，写入相同流表	成功
TEST_F(t05_replay_dlr_buffer_concurrency, STREAM_019_033)
{
    // 下发订阅
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");
    RdNineteenSubUserDataT userData1;
    GmcConnT *subConn1 = NULL;
    RdNineteenSubCfgT subCfg1 = {
        .subsName = "stream_sub_01",
        .sinkName = "s6",
        .userCb = RdNineteenGetDlrDataBuf,
        .userData = &userData1,
    };
    ret = RdNineteenSubscribe(shortStmt, subCfg1, &subConn1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    RdNineteenSubUserDataT userData2;
    GmcConnT *subConn2 = NULL;
    RdNineteenSubCfgT subCfg2 = {
        .subsName = "stream_sub_02",
        .sinkName = "s7",
        .userCb = RdNineteenGetDlrDataBuf,
        .userData = &userData2,
    };
    ret = RdNineteenSubscribe(shortStmt, subCfg2, &subConn2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    uint32_t rowNum = 200;
    ret = RdNineteenStructWrite(shortStmt, vertexLabel5, 0, rowNum, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 等待订阅推送结束
    ret = RdNineteenWaitSnRecv(&userData1, rowNum, 20);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(rowNum, userData1.realFetchNum);
    ret = RdNineteenWaitSnRecv(&userData2, rowNum, 20);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(rowNum, userData2.realFetchNum);

    // 并发重演DLR
    RdNineteenSubUserDataT *userDatas[] = {&userData1, &userData2};
    const uint32_t threadNum = 2;
    pthread_t threads[threadNum];
    RdNineteenReplayCtxT ctxs[threadNum] = {0};
    for (int32_t i = 0; i < threadNum; i++) {
        ctxs[i] = {
            .index = i,
            .status = 0,
            .tableName = {0},
            .subUserData = userDatas[i % 2],
            .ignoreError = false,
        };
        (void)strcpy_s(ctxs[i].tableName, RD_MAX_NAME_LEN, "t2");
        ret = pthread_create(&threads[i], NULL, RdNineteenReplayWorker, &ctxs[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int32_t i = 0; i < threadNum; i++) {
        ret = pthread_join(threads[i], NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int32_t i = 0; i < threadNum; i++) {
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ctxs[i].status);
    }

    // 查询校验数据
    ret = RdTsCheckRecordCount(shortStmt, "ts1", rowNum * 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 清理测试数据
    ret = RdNineteenUnSubscribe(shortStmt, subCfg1.subsName, subConn1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdNineteenUnSubscribe(shortStmt, subCfg2.subsName, subConn2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdNineteenFreeDlrDataBuf(&userData1);
    RdNineteenFreeDlrDataBuf(&userData2);
    RdFinishTest();
}
