/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 支持文件数据注入 功能测试
 * Author: yushijin
 * Create: 2024-12-10
 */
#include "gtest/gtest.h"
#include "securec.h"
#include "gmc_sql.h"
#include "gmc_persist.h"
#include "rd_feature_stream.h"
#include "file_input.h"
#include <sys/time.h>

class t01_file_input : public testing::Test {
public:
    static GmcConnT *conn;
    static GmcStmtT *stmt;

    static void SetUpTestCase();
    static void TearDownTestCase();
    virtual void SetUp();
    virtual void TearDown();
};

GmcConnT *t01_file_input::conn = NULL;
GmcStmtT *t01_file_input::stmt = NULL;

void t01_file_input::SetUpTestCase()
{
    int32_t ret;
    ret = RdStreamEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void t01_file_input::TearDownTestCase()
{
    int32_t ret = RdStreamEnvClean();
    EXPECT_EQ(GMERR_OK, ret);
}

void t01_file_input::SetUp()
{
    int32_t ret = RdGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void t01_file_input::TearDown()
{
    int32_t ret = RdGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 创建流表，文件路径/dev开头，文件数量256个
TEST_F(t01_file_input, STREAM_026_001)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    system("sudo rm -f /dev/test_file*");

    int64_t fileNum = 256;
    int64_t g_fd[fileNum] = {0};
    for (int32_t i = 0; i < fileNum; ++i) {
        char fileName[256] = {0};
        ret = sprintf_s(fileName, 256, "%s/test_file%03d", FILE_PATH, i);

        // 创建命名管道
        if (mkfifo(fileName, 0666) == -1) {
            perror("mkfifo");
            std::cerr << "Error creating FIFO '" << fileName << "': " << strerror(errno) << std::endl;
        }

        system("sudo chmod 0666 /dev/test_file*");

        g_fd[i] = open(fileName, O_RDWR, 0666);
        if (g_fd[i] == -1) {
            perror("Error opening file");
            std::cerr << "errno: " << errno << std::endl;
        }
    }

    // 创建stream table sql
    char sqlCmd[6000] = "create stream table stream1 (file char(256), water_mark integer DEFAULT 0, event_time integer DEFAULT 0, "
        "system_time integer DEFAULT current_time_second(), "
        "hostname char(50) DEFAULT get_hostname()) "
        "from fileGroup ('/dev/test_file000'";
    for (int32_t i = 1; i < fileNum; ++i) {
        char fileName[32] = {0};
        ret = sprintf_s(fileName, 32, ", '/dev/test_file%03d'", i);
        strcat(sqlCmd, fileName);
    }
    strcat(sqlCmd, ")");

    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (file char(256), water_mark integer, event_time integer, system_time integer, hostname char(50)) "
            "WITH (TIME_COL = 'water_mark', INTERVAL= '1 hour',  COMPRESSION = 'no');", GMC_MODEL_TS},
        {sqlCmd},
        {"CREATE STREAM VIEW view1 AS SELECT file, water_mark, event_time, system_time, hostname FROM stream1;"},
        {"CREATE STREAM SINK sink1 AS SELECT file, water_mark, event_time, system_time, hostname "
            "FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sleep(1);
    // 准备写入内容
    for (int32_t i = 0; i < fileNum; ++i) {
        char data[1024] = {0};
        ret = sprintf_s(data, 1024, "Hello_%03d\r", i);
        ssize_t bytes_written = write(g_fd[i], data, strlen(data));
        EXPECT_NE(bytes_written, -1);
    }

    for (int32_t i = 0; i < fileNum; ++i) {
        close(g_fd[i]);
    }

    // 等待数据落盘，最长等待180s
    char *qryCmd = (char *)"select * from ts1;";
    uint32_t expRowNum = 256;
    uint32_t rowsCount = 0;
    uint32_t time = 0;
    while (rowsCount < expRowNum && time < 180) {
        ret = GmcExecDirect(stmt, qryCmd, strlen(qryCmd));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowsCount, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        sleep(1);
        ++time;
    }
    AW_MACRO_ASSERT_EQ_INT(expRowNum, rowsCount);

    // 校验ts表中数据
    qryCmd = (char *)"select * from ts1 order by file;";
    uint32_t expColNum = 5;
    char expFileContent[256][256] = {0};
    for(int32_t i = 0; i < 256; ++i) {
        ret = sprintf_s(expFileContent[i], 256, "Hello_%03d", i);
    }
    char expHostname[32] = {0};
    gethostname(expHostname, 32);
    RdCheckDataInTSTableOfStreamTable(stmt, qryCmd, expRowNum, expColNum, expFileContent, expHostname);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建流表，文件路径/dev开头，文件数量257个
TEST_F(t01_file_input, STREAM_026_002)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    system("sudo rm -f /dev/test_file*");

    int64_t fileNum = 257;
    int64_t g_fd[fileNum] = {0};
    for (int32_t i = 0; i < fileNum; ++i) {
        char fileName[256] = {0};
        ret = sprintf_s(fileName, 256, "%s/test_file%03d", FILE_PATH, i);

        // 创建命名管道
        if (mkfifo(fileName, 0666) == -1) {
            perror("mkfifo");
            std::cerr << "Error creating FIFO '" << fileName << "': " << strerror(errno) << std::endl;
        }

        system("sudo chmod 0666 /dev/test_file*");

        // 准备写入内容
        char data[1024] = {0};
        ret = sprintf_s(data, 1024, "Hello_%03d\r", i);

        g_fd[i] = open(fileName, O_RDWR, 0666);
        if (g_fd[i] == -1) {
            perror("Error opening file");
            std::cerr << "errno: " << errno << std::endl;
        }

        ssize_t bytes_written = write(g_fd[i], data, strlen(data));
        EXPECT_NE(bytes_written, -1);
    }

    char sqlCmd[6000] = "create stream table stream1 (file char(256), water_mark integer DEFAULT 0) from fileGroup ('/dev/test_file000'";
    for (int32_t i = 1; i < fileNum; ++i) {
        char fileName[32] = {0};
        ret = sprintf_s(fileName, 32, ", '/dev/test_file%03d'", i);
        strcat(sqlCmd, fileName);
    }
    strcat(sqlCmd, ")");

    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONFIGURATION_LIMIT_EXCEEDED, ret);

    for (int32_t i = 0; i < fileNum; ++i) {
        close(g_fd[i]);
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建流表，包含默认填充时间和hostname
TEST_F(t01_file_input, STREAM_026_003)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    system("sudo rm -f /dev/test_file*");
    char fileName[256] = {0};
    ret = sprintf_s(fileName, 256, "%s/test_file000", FILE_PATH);

    // 创建命名管道
    if (mkfifo(fileName, 0666) == -1) {
        perror("mkfifo");
        std::cerr << "Error creating FIFO '" << fileName << "': " << strerror(errno) << std::endl;
    }

    system("sudo chmod 0666 /dev/test_file*");

    int64_t fd = open(fileName, O_RDWR, 0666);
    if (fd == -1) {
        perror("Error opening file");
        std::cerr << "errno: " << errno << std::endl;
    }

    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (file char(256), water_mark integer, event_time integer, system_time integer, hostname char(50)) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour',  COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (file char(256), water_mark integer DEFAULT 0, event_time integer DEFAULT 0, "
            "system_time integer DEFAULT current_time_second(), "
            "hostname char(50) DEFAULT get_hostname())"
            "from fileGroup ('/dev/test_file000');"},
        {"CREATE STREAM VIEW view1 AS SELECT file, water_mark, event_time, system_time, hostname FROM stream1;"},
        {"CREATE STREAM SINK sink1 AS SELECT file, water_mark, event_time, system_time, hostname "
            "FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    close(fd);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 文件名使用字母+数字+符号命名
TEST_F(t01_file_input, STREAM_026_004)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    system("sudo rm -f /dev/tfile*");

    char fileName[128] = {0};
    ret = sprintf_s(fileName, 128, "%s/tfile$#&*", FILE_PATH);
    preFile(fileName, 1);

    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (file char(256), water_mark integer, event_time integer, system_time integer, hostname char(50)) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour',  COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (file char(256), water_mark integer DEFAULT 0, event_time integer DEFAULT 0, "
            "system_time integer DEFAULT current_time_second(), "
            "hostname char(50) DEFAULT get_hostname())"
            "from fileGroup ('/dev/tfile$#&*000');"},
        {"CREATE STREAM VIEW view1 AS SELECT file, water_mark, event_time, system_time, hostname FROM stream1;"},
        {"CREATE STREAM SINK sink1 AS SELECT file, water_mark, event_time, system_time, hostname "
            "FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    close(g_fd);

    // 等待数据落盘，最长等待180s
    char *qryCmd = (char *)"select * from ts1;";
    uint32_t expRowNum = 1;
    uint32_t rowsCount = 0;
    uint32_t time = 0;
    while (rowsCount < expRowNum && time < 180) {
        ret = GmcExecDirect(stmt, qryCmd, strlen(qryCmd));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowsCount, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        sleep(1);
        ++time;
    }
    AW_MACRO_ASSERT_EQ_INT(expRowNum, rowsCount);

    // 校验ts表中数据
    qryCmd = (char *)"select * from ts1 order by file;";
    uint32_t expColNum = 5;
    char expFileContent[1][256] = {"Hello_000"};
    char expHostname[32] = {0};
    gethostname(expHostname, 32);
    RdCheckDataInTSTableOfStreamTable(stmt, qryCmd, expRowNum, expColNum, expFileContent, expHostname);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 文件名后跟.aa后缀
TEST_F(t01_file_input, STREAM_026_005)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    system("sudo rm -f /dev/tf*");

    char fileName[128] = {0};
    ret = sprintf_s(fileName, 128, "%s/tf$#&*.aa", FILE_PATH);
    preFile(fileName, 1);

    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (file char(256), water_mark integer, event_time integer, system_time integer, hostname char(50)) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour',  COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (file char(256), water_mark integer DEFAULT 0, event_time integer DEFAULT 0, "
            "system_time integer DEFAULT current_time_second(), "
            "hostname char(50) DEFAULT get_hostname())"
            "from fileGroup ('/dev/tf$#&*.aa000');"},
        {"CREATE STREAM VIEW view1 AS SELECT file, water_mark, event_time, system_time, hostname FROM stream1;"},
        {"CREATE STREAM SINK sink1 AS SELECT file, water_mark, event_time, system_time, hostname "
            "FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    close(g_fd);

    // 等待数据落盘，最长等待180s
    char *qryCmd = (char *)"select * from ts1;";
    uint32_t expRowNum = 1;
    uint32_t rowsCount = 0;
    uint32_t time = 0;
    while (rowsCount < expRowNum && time < 180) {
        ret = GmcExecDirect(stmt, qryCmd, strlen(qryCmd));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowsCount, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        sleep(1);
        ++time;
    }
    AW_MACRO_ASSERT_EQ_INT(expRowNum, rowsCount);

    // 校验ts表中数据
    qryCmd = (char *)"select * from ts1 order by file;";
    uint32_t expColNum = 5;
    char expFileContent[1][256] = {"Hello_000"};
    char expHostname[32] = {0};
    gethostname(expHostname, 32);
    RdCheckDataInTSTableOfStreamTable(stmt, qryCmd, expRowNum, expColNum, expFileContent, expHostname);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 换行使用/r, /n, /v
TEST_F(t01_file_input, STREAM_026_006)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    system("sudo rm -f /dev/test_file*");

    char fileName[128] = {0};
    ret = sprintf_s(fileName, 128, "%s/test_file000", FILE_PATH);

    // 创建命名管道
    if (mkfifo(fileName, 0666) == -1) {
        perror("mkfifo");
        std::cerr << "Error creating FIFO '" << fileName << "': " << strerror(errno) << std::endl;
    }

    system("sudo chmod 0666 /dev/test_file000");

    // 准备写入内容
    char data[1024] = {0};
    ret = sprintf_s(data, 1024, "Hello_000\rHello_001\nHello_002\vHello_003\r");

    g_fd = open(fileName, O_RDWR, 0666);
    if (g_fd == -1) {
        perror("Error opening file");
        std::cerr << "errno: " << errno << std::endl;
    }

    ssize_t bytes_written = write(g_fd, data, strlen(data));
    EXPECT_NE(bytes_written, -1);

    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (file char(256), water_mark integer, event_time integer, system_time integer, hostname char(50)) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour',  COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (file char(256), water_mark integer DEFAULT 0, event_time integer DEFAULT 0, "
            "system_time integer DEFAULT current_time_second(), "
            "hostname char(50) DEFAULT get_hostname())"
            "from fileGroup ('/dev/test_file000');"},
        {"CREATE STREAM VIEW view1 AS SELECT file, water_mark, event_time, system_time, hostname FROM stream1;"},
        {"CREATE STREAM SINK sink1 AS SELECT file, water_mark, event_time, system_time, hostname "
            "FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    close(g_fd);

    // 等待数据落盘，最长等待180s
    char *qryCmd = (char *)"select * from ts1;";
    uint32_t expRowNum = 4;
    uint32_t rowsCount = 0;
    uint32_t time = 0;
    while (rowsCount < expRowNum && time < 180) {
        ret = GmcExecDirect(stmt, qryCmd, strlen(qryCmd));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowsCount, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        sleep(1);
        ++time;
    }
    AW_MACRO_ASSERT_EQ_INT(expRowNum, rowsCount);

    // 校验ts表中数据
    qryCmd = (char *)"select * from ts1 order by file;";
    uint32_t expColNum = 5;
    char expFileContent[4][256] = {"Hello_000", "Hello_001", "Hello_002", "Hello_003"};
    char expHostname[32] = {0};
    gethostname(expHostname, 32);
    RdCheckDataInTSTableOfStreamTable(stmt, qryCmd, expRowNum, expColNum, expFileContent, expHostname);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 字符串长度超过表定义的长度
TEST_F(t01_file_input, STREAM_026_007)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    system("sudo rm -f /dev/test_file*");

    char fileName[128] = {0};
    ret = sprintf_s(fileName, 128, "%s/test_file000", FILE_PATH);

    // 创建命名管道
    if (mkfifo(fileName, 0666) == -1) {
        perror("mkfifo");
        std::cerr << "Error creating FIFO '" << fileName << "': " << strerror(errno) << std::endl;
    }

    system("sudo chmod 0666 /dev/test_file000");

    // 准备写入内容
    char data[1024] = {0};
    for(int32_t i = 0; i < 20; ++i) {
        char temp[16] = {0};
        ret = sprintf_s(temp, 16, "Hello_%04d", i);
        strcat(data, temp);
    }
    data[199] = '\n';
    

    g_fd = open(fileName, O_RDWR, 0666);
    if (g_fd == -1) {
        perror("Error opening file");
        std::cerr << "errno: " << errno << std::endl;
    }

    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (file char(128), water_mark integer, event_time integer, system_time integer, hostname char(50)) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour',  COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (file char(256), water_mark integer DEFAULT 0, event_time integer DEFAULT 0, "
            "system_time integer DEFAULT current_time_second(), "
            "hostname char(50) DEFAULT get_hostname())"
            "from fileGroup ('/dev/test_file000');"},
        {"CREATE STREAM VIEW view1 AS SELECT file, water_mark, event_time, system_time, hostname FROM stream1;"},
        {"CREATE STREAM SINK sink1 AS SELECT file, water_mark, event_time, system_time, hostname "
            "FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ssize_t bytes_written = write(g_fd, data, strlen(data));
    EXPECT_NE(bytes_written, -1);
    close(g_fd);

    // 等待数据落盘，最长等待180s
    char *qryCmd = (char *)"select * from ts1;";
    uint32_t expRowNum = 1;
    uint32_t rowsCount = 0;
    uint32_t time = 0;
    while (rowsCount < expRowNum && time < 180) {
        ret = GmcExecDirect(stmt, qryCmd, strlen(qryCmd));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowsCount, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        sleep(1);
        ++time;
    }
    AW_MACRO_ASSERT_EQ_INT(expRowNum, rowsCount);

    // 校验ts表中数据
    qryCmd = (char *)"select * from ts1 order by file;";
    uint32_t expColNum = 5;
    char expFileContent[1][256] = {0};
    strncpy_s(expFileContent[0], 256, data, 128);
    char expHostname[32] = {0};
    gethostname(expHostname, 32);
    uint32_t fileSize = 128;
    RdCheckDataInTSTableOfStreamTable(stmt, qryCmd, expRowNum, expColNum, expFileContent, expHostname, fileSize);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 空文件
TEST_F(t01_file_input, STREAM_026_008)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    system("sudo rm -f /dev/test_file*");

    char fileName[128] = {0};
    ret = sprintf_s(fileName, 128, "%s/test_file000", FILE_PATH);

    // 创建命名管道
    if (mkfifo(fileName, 0666) == -1) {
        perror("mkfifo");
        std::cerr << "Error creating FIFO '" << fileName << "': " << strerror(errno) << std::endl;
    }

    system("sudo chmod 0666 /dev/test_file000");

    g_fd = open(fileName, O_RDWR, 0666);
    if (g_fd == -1) {
        perror("Error opening file");
        std::cerr << "errno: " << errno << std::endl;
    }

    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (file char(256), water_mark integer, event_time integer, system_time integer, hostname char(50)) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour',  COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (file char(256), water_mark integer DEFAULT 0, event_time integer DEFAULT 0, "
            "system_time integer DEFAULT current_time_second(), "
            "hostname char(50) DEFAULT get_hostname())"
            "from fileGroup ('/dev/test_file000');"},
        {"CREATE STREAM VIEW view1 AS SELECT file, water_mark, event_time, system_time, hostname FROM stream1;"},
        {"CREATE STREAM SINK sink1 AS SELECT file, water_mark, event_time, system_time, hostname "
            "FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    close(g_fd);

    // 等待数据落盘
    sleep(1);

    // 校验ts表中数据
    char *sqlCmd = (char *)"select * from ts1 order by file;";
    uint32_t expRowNum = 0;
    uint32_t expColNum = 0;
    char expFileContent[1][256] = {"Hello_000"};
    char expHostname[32] = {0};
    gethostname(expHostname, 32);
    RdCheckDataInTSTableOfStreamTable(stmt, sqlCmd, expRowNum, expColNum, expFileContent, expHostname);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 文件内容超过2000个字符
TEST_F(t01_file_input, STREAM_026_009)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    system("sudo rm -f /dev/test_file*");

    char fileName[128] = {0};
    ret = sprintf_s(fileName, 128, "%s/test_file000", FILE_PATH);

    // 创建命名管道
    if (mkfifo(fileName, 0666) == -1) {
        perror("mkfifo");
        std::cerr << "Error creating FIFO '" << fileName << "': " << strerror(errno) << std::endl;
    }

    system("sudo chmod 0666 /dev/test_file000");

    // 准备写入内容
    char data[1024] = {0};
    for(int32_t i = 0; i < 20; ++i) {
        char temp[16] = {0};
        ret = sprintf_s(temp, 16, "Hello_%04d", i);
        strcat(data, temp);
    }
    data[199] = '\r';

    g_fd = open(fileName, O_RDWR, 0666);
    if (g_fd == -1) {
        perror("Error opening file");
        std::cerr << "errno: " << errno << std::endl;
    }

    for (int32_t i = 0; i < 15; ++ i) {
        ssize_t bytes_written = write(g_fd, data, strlen(data));
        EXPECT_NE(bytes_written, -1);
    }

    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (file char(256), water_mark integer, event_time integer, system_time integer, hostname char(50)) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour',  COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (file char(256), water_mark integer DEFAULT 0, event_time integer DEFAULT 0, "
            "system_time integer DEFAULT current_time_second(), "
            "hostname char(50) DEFAULT get_hostname())"
            "from fileGroup ('/dev/test_file000');"},
        {"CREATE STREAM VIEW view1 AS SELECT file, water_mark, event_time, system_time, hostname FROM stream1;"},
        {"CREATE STREAM SINK sink1 AS SELECT file, water_mark, event_time, system_time, hostname "
            "FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    close(g_fd);

    // 等待数据落盘，最长等待180s
    char *qryCmd = (char *)"select * from ts1;";
    uint32_t expRowNum = 15;
    uint32_t rowsCount = 0;
    uint32_t time = 0;
    while (rowsCount < expRowNum && time < 180) {
        ret = GmcExecDirect(stmt, qryCmd, strlen(qryCmd));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowsCount, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        sleep(1);
        ++time;
    }
    AW_MACRO_ASSERT_EQ_INT(expRowNum, rowsCount);

    // 校验ts表中数据
    qryCmd = (char *)"select * from ts1 order by file;";
    uint32_t expColNum = 5;
    char expFileContent[15][256] = {0};
    for(int32_t i = 0; i < 15; ++i) {
        strncpy_s(expFileContent[i], 256, data, 199);
    }
    char expHostname[32] = {0};
    gethostname(expHostname, 32);
    RdCheckDataInTSTableOfStreamTable(stmt, qryCmd, expRowNum, expColNum, expFileContent, expHostname);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 单行内容超过2000个字符
TEST_F(t01_file_input, STREAM_026_010)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    system("sudo rm -f /dev/test_file*");

    char fileName[128] = {0};
    ret = sprintf_s(fileName, 128, "%s/test_file000", FILE_PATH);

    // 创建命名管道
    if (mkfifo(fileName, 0666) == -1) {
        perror("mkfifo");
        std::cerr << "Error creating FIFO '" << fileName << "': " << strerror(errno) << std::endl;
    }

    system("sudo chmod 0666 /dev/test_file000");

    // 准备写入内容
    char data[1024] = {0};
    for(int32_t i = 0; i < 20; ++i) {
        char temp[16] = {0};
        ret = sprintf_s(temp, 16, "Hello_%04d", i);
        strcat(data, temp);
    }

    g_fd = open(fileName, O_RDWR, 0666);
    if (g_fd == -1) {
        perror("Error opening file");
        std::cerr << "errno: " << errno << std::endl;
    }

    for (int32_t i = 0; i < 10; ++ i) {
        ssize_t bytes_written = write(g_fd, data, strlen(data));
        EXPECT_NE(bytes_written, -1);
    }

    // expFileContent用于校验
    char expFileContent[1][2560] = {0};
    for(int32_t i = 0; i < 10; ++i) {
        strncpy_s(expFileContent[0] + i * 200, 256, data, 200);
    }
    strncpy_s(expFileContent[0] + 2000, 256, data, 199);

    data[199] = '\r';
    ssize_t bytes_written = write(g_fd, data, strlen(data));
    EXPECT_NE(bytes_written, -1);

    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (file char(2560), water_mark integer, event_time integer, system_time integer, hostname char(50)) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour',  COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (file char(2560), water_mark integer DEFAULT 0, event_time integer DEFAULT 0, "
            "system_time integer DEFAULT current_time_second(), "
            "hostname char(50) DEFAULT get_hostname())"
            "from fileGroup ('/dev/test_file000');"},
        {"CREATE STREAM VIEW view1 AS SELECT file, water_mark, event_time, system_time, hostname FROM stream1;"},
        {"CREATE STREAM SINK sink1 AS SELECT file, water_mark, event_time, system_time, hostname "
            "FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    close(g_fd);

    // 等待数据落盘，最长等待180s
    char *qryCmd = (char *)"select * from ts1;";
    uint32_t expRowNum = 1;
    uint32_t rowsCount = 0;
    uint32_t time = 0;
    while (rowsCount < expRowNum && time < 180) {
        ret = GmcExecDirect(stmt, qryCmd, strlen(qryCmd));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowsCount, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        sleep(1);
        ++time;
    }
    AW_MACRO_ASSERT_EQ_INT(expRowNum, rowsCount);

    // 校验ts表中数据
    char *sqlCmd = (char *)"select * from ts1 order by file;";
    uint32_t expColNum = 5;
    char expHostname[32] = {0};
    gethostname(expHostname, 32);
    uint32_t fileSize = 2560;
    RdCheckDataInTSTableOfStreamTable(stmt, sqlCmd, expRowNum, expColNum, expFileContent, expHostname, fileSize);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建流表，文件路径/dev开头，文件数量1个
TEST_F(t01_file_input, STREAM_026_011)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    char fileName[128] = {0};
    ret = sprintf_s(fileName, 128, "%s/kmsg", FILE_PATH);

    char command[STREAM_REFERENCE_MAX_SQL_LEN];
    ret = sprintf_s(command, STREAM_REFERENCE_MAX_SQL_LEN,
        "create stream table kmsgFileTable(file char(256), water_mark integer  DEFAULT 0) from fileGroup2 ('%s')",
        fileName);

    // 创建流表
    ret = GmcExecDirect(stmt, command, STREAM_REFERENCE_MAX_SQL_LEN);
    EXPECT_EQ(ret, GMERR_OK);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table kmsgFileTable;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建流表，文件路径/dev/开头，无文件
TEST_F(t01_file_input, STREAM_026_012)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    char fileName[128] = {0};
    ret = sprintf_s(fileName, 128, "%s/", FILE_PATH);

    char command[STREAM_REFERENCE_MAX_SQL_LEN];
    ret = sprintf_s(command, STREAM_REFERENCE_MAX_SQL_LEN,
        "create stream table stream1 (file char(256), water_mark integer  DEFAULT 0) from fileGroup2 ('%s')",
        fileName);

    // 创建流表
    ret = GmcExecDirect(stmt, command, STREAM_REFERENCE_MAX_SQL_LEN);
    ASSERT_EQ(GMERR_INTERNAL_ERROR, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建流表，文件路径/dev开头，无文件
TEST_F(t01_file_input, STREAM_026_013)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    char fileName[128] = {0};
    ret = sprintf_s(fileName, 128, "%s", FILE_PATH);

    char command[STREAM_REFERENCE_MAX_SQL_LEN];
    ret = sprintf_s(command, STREAM_REFERENCE_MAX_SQL_LEN,
        "create stream table stream1 (file char(256), water_mark integer  DEFAULT 0) from fileGroup2 ('%s')",
        fileName);

    // 创建流表
    ret = GmcExecDirect(stmt, command, STREAM_REFERENCE_MAX_SQL_LEN);
    ASSERT_EQ(GMERR_INTERNAL_ERROR, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建流表，文件不存在
TEST_F(t01_file_input, STREAM_026_014)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    char fileName[128] = {0};
    ret = sprintf_s(fileName, 128, "%s/kmsgxxx", FILE_PATH);

    char command[STREAM_REFERENCE_MAX_SQL_LEN];
    ret = sprintf_s(command, STREAM_REFERENCE_MAX_SQL_LEN,
        "create stream table stream1 (file char(256), water_mark integer  DEFAULT 0) from fileGroup2 ('%s')",
        fileName);

    // 创建流表
    ret = GmcExecDirect(stmt, command, STREAM_REFERENCE_MAX_SQL_LEN);
    ASSERT_EQ(GMERR_FILE_OPERATE_FAILED, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建流表，文件路径不是/dev
TEST_F(t01_file_input, STREAM_026_015)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    char fileName[128] = {0};
    ret = sprintf_s(fileName, 128, "%s/kmsg", ROOT_PATH);

    char command[STREAM_REFERENCE_MAX_SQL_LEN];
    sprintf_s(command, STREAM_REFERENCE_MAX_SQL_LEN,
        "create stream table stream1 (file char(256), water_mark integer  DEFAULT 0) from fileGroup2 ('%s')", fileName);

    // 创建流表
    ret = GmcExecDirect(stmt, command, STREAM_REFERENCE_MAX_SQL_LEN);
    ASSERT_EQ(GMERR_INTERNAL_ERROR, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建流表，单行长度65535
TEST_F(t01_file_input, STREAM_026_016)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    char fileName[128] = {0};
    ret = sprintf_s(fileName, 128, "%s/kmsg", FILE_PATH);

    char command[STREAM_REFERENCE_MAX_SQL_LEN];
    sprintf_s(command, STREAM_REFERENCE_MAX_SQL_LEN,
        "create stream table stream1(file char(65535), water_mark integer  DEFAULT 0) from fileGroup ('%s')", fileName);

    // 创建流表
    ret = GmcExecDirect(stmt, command, STREAM_REFERENCE_MAX_SQL_LEN);
    ASSERT_EQ(ret, GMERR_OK);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建流表，单行长度65536
TEST_F(t01_file_input, STREAM_026_017)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    char fileName[128] = {0};
    ret = sprintf_s(fileName, 128, "%s/kmsg", FILE_PATH);

    char command[STREAM_REFERENCE_MAX_SQL_LEN];
    sprintf_s(command, STREAM_REFERENCE_MAX_SQL_LEN,
        "create stream table stream1(file char(65536), water_mark integer  DEFAULT 0) from fileGroup ('%s')", fileName);

    // 创建流表
    ret = GmcExecDirect(stmt, command, STREAM_REFERENCE_MAX_SQL_LEN);
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建流表，第一列使用整型
TEST_F(t01_file_input, STREAM_026_018)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    char fileName[128] = {0};
    ret = sprintf_s(fileName, 128, "%s/kmsg", FILE_PATH);

    char command[STREAM_REFERENCE_MAX_SQL_LEN];
    sprintf_s(command, STREAM_REFERENCE_MAX_SQL_LEN,
        "create stream table stream1(file integer  DEFAULT 0, water_mark integer  DEFAULT 0) from fileGroup ('%s')", fileName);

    // 创建流表
    ret = GmcExecDirect(stmt, command, STREAM_REFERENCE_MAX_SQL_LEN);
    ASSERT_EQ(GMERR_DATATYPE_MISMATCH, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建流表，第一列使用text
TEST_F(t01_file_input, STREAM_026_019)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    char fileName[128] = {0};
    ret = sprintf_s(fileName, 128, "%s/kmsg", FILE_PATH);

    char command[STREAM_REFERENCE_MAX_SQL_LEN];
    sprintf_s(command, STREAM_REFERENCE_MAX_SQL_LEN,
        "create stream table stream1(file text  DEFAULT 'aa', water_mark integer  DEFAULT 0) from fileGroup ('%s')", fileName);

    // 创建流表
    ret = GmcExecDirect(stmt, command, STREAM_REFERENCE_MAX_SQL_LEN);
    ASSERT_EQ(GMERR_DATATYPE_MISMATCH, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 文件名长度超过18个字符
TEST_F(t01_file_input, STREAM_026_020)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    system("sudo rm -f /dev/test_file*");

    char fileName[128] = {0};
    ret = sprintf_s(fileName, 128, "%s/test_filexx", FILE_PATH);
    preFile(fileName, 1);

    const char *sqlCmd = "create stream table stream1(file char(256), water_mark integer  DEFAULT 0) "
        "from fileGroup ('/dev/test_filexx000'))";

    // 创建流表
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SYNTAX_ERROR, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
