/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 流视图支持基于滑动窗口的over聚合计算 交互测试
 * Author: yushijin
 * Create: 2024-10-24
 */
#include "gtest/gtest.h"
#include "securec.h"
#include "gmc_sql.h"
#include "gmc_persist.h"
#include "rd_feature_stream.h"
#include "over_aggregate_calculation.h"

#define STREAM_REFERENCE_MAX_NAME_LEN 256
#define STREAM_REFERENCE_MAX_SQL_LEN 512

class t04_over_aggregate_calculation_interaction : public testing::Test {
public:
    static GmcConnT *conn;
    static GmcStmtT *stmt;

    static void SetUpTestCase();
    static void TearDownTestCase();
    virtual void SetUp();
    virtual void TearDown();
};

GmcConnT *t04_over_aggregate_calculation_interaction::conn = NULL;
GmcStmtT *t04_over_aggregate_calculation_interaction::stmt = NULL;

void t04_over_aggregate_calculation_interaction::SetUpTestCase()
{
    int32_t ret;
    ret = RdStreamEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void t04_over_aggregate_calculation_interaction::TearDownTestCase()
{
    int32_t ret = RdStreamEnvClean();
    EXPECT_EQ(GMERR_OK, ret);
}

void t04_over_aggregate_calculation_interaction::SetUp()
{
    int32_t ret = RdGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void t04_over_aggregate_calculation_interaction::TearDown()
{
    int32_t ret = RdGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// view不支持设置batch_window_size
TEST_F(t04_over_aggregate_calculation_interaction, STREAM_013_191)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "count(id) OVER(PARTITION BY window_start, window_end, event_time) "
            "FROM TABLE(TUMBLE(TABLE stream1, event_time, INTERVAL '10' SECONDS)) with (batch_window_size = 1);";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 普通流表进行over聚合
TEST_F(t04_over_aggregate_calculation_interaction, STREAM_013_192)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, event_time, "
            "row_number() OVER (PARTITION BY window_start, window_end, event_time) "
            "FROM stream1;";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_UNDEFINE_COLUMN, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 对字符串列进行sum聚合，滚窗
TEST_F(t04_over_aggregate_calculation_interaction, STREAM_013_193)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "idSum char(50)) "
            "WITH (TIME_COL = 'window_end', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "sum(name) OVER(PARTITION BY window_start, window_end, event_time) "
            "FROM TABLE(TUMBLE(TABLE stream1, event_time, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SEMANTIC_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 对字符串列进行sum聚合，滑窗
TEST_F(t04_over_aggregate_calculation_interaction, STREAM_013_194)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "idSum char(50)) "
            "WITH (TIME_COL = 'window_end', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "sum(name) OVER(PARTITION BY window_start, window_end, event_time) "
            "FROM TABLE(hop(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SEMANTIC_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 数据存在负数进行sum聚合,watermark[strcit]，滚窗
TEST_F(t04_over_aggregate_calculation_interaction, STREAM_013_195)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "idSum integer) "
            "WITH (TIME_COL = 'window_end', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "sum(id) OVER(PARTITION BY window_start, window_end, event_time) "
            "FROM TABLE(TUMBLE(TABLE stream1, event_time, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum_id FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    int64_t id[rowNum] = {-1, -2, -3, 4, -5, 6, -7, 8, 9, -10, 1000};
    int64_t water_mark[rowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1000};
    int64_t event_time[rowNum] = {-1, -2, -2, -1, 2, -1, 2, 2, 3, 3, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 9;
    uint32_t expectColsCount = 6;
    int64_t mapping_id[expectRowsCount] = {-3, -2, -1, 4, -10, -7, -5, 8, 9};
    int64_t mapping_window_start[expectRowsCount] = {-10, -10, -10, -10, 0, 0, 0, 0, 0};
    int64_t mapping_window_end[expectRowsCount] = {0, 0, 0, 0, 10, 10, 10, 10, 10};
    int64_t mapping_event_time[expectRowsCount] = {-2, -2, -1, -1, 3, 2, 2, 2, 3};
    int64_t mapping_sum_id[expectRowsCount] = {-5, -5, 3, 3, -1, -4, -4, -4, -1};
    RdCheckDataInTSTableOfStreamTable(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_sum_id);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 数据存在负数进行sum聚合,watermark[tolerant]，滚窗
TEST_F(t04_over_aggregate_calculation_interaction, STREAM_013_196)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "idSum integer) "
            "WITH (TIME_COL = 'window_end', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS tolerant);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "sum(id) OVER(PARTITION BY window_start, window_end, event_time) "
            "FROM TABLE(TUMBLE(TABLE stream1, event_time, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum_id FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    int64_t id[rowNum] = {-1, -2, -3, 4, -5, 6, -7, 8, 9, -10, 1000};
    int64_t water_mark[rowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1000};
    int64_t event_time[rowNum] = {-1, -2, -2, -1, 2, -1, 2, 2, 3, 3, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 10;
    uint32_t expectColsCount = 6;
    int64_t mapping_id[expectRowsCount] = {-3, -2, -1, 4, 6, -10, -7, -5, 8, 9};
    int64_t mapping_window_start[expectRowsCount] = {-10, -10, -10, -10, -10, 0, 0, 0, 0, 0};
    int64_t mapping_window_end[expectRowsCount] = {0, 0, 0, 0, 0, 10, 10, 10, 10, 10};
    int64_t mapping_event_time[expectRowsCount] = {-2, -2, -1, -1, -1, 3, 2, 2, 2, 3};
    int64_t mapping_sum_id[expectRowsCount] = {-5, -5, 9, 9, 9, -1, -4, -4, -4, -1};
    RdCheckDataInTSTableOfStreamTable(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_sum_id);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 数据存在负数进行sum聚合,watermark[strcit]，滑窗
TEST_F(t04_over_aggregate_calculation_interaction, STREAM_013_197)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "idSum integer) "
            "WITH (TIME_COL = 'window_end', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "sum(id) OVER(PARTITION BY window_start, window_end, event_time) "
            "FROM TABLE(hop(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum_id FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    int64_t id[rowNum] = {-1, -2, -3, 4, -5, 6, -7, 8, 9, -10, 1000};
    int64_t water_mark[rowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1000};
    int64_t event_time[rowNum] = {-1, -2, -2, -1, 2, -1, 2, 2, 3, 3, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 19;
    uint32_t expectColsCount = 6;
    int64_t mapping_id[expectRowsCount] = {-3, -2, -1, 4, -10, -7, -5, -3, -2, -1, 4, 6, 8, 9, -10, -7, -5, 8, 9};
    int64_t mapping_window_start[expectRowsCount] = {-10, -10, -10, -10, -5, -5, -5, -5, -5, -5, -5, -5, -5, -5, 0, 0, 0, 0, 0};
    int64_t mapping_window_end[expectRowsCount] = {0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10};
    int64_t mapping_event_time[expectRowsCount] = {-2, -2, -1, -1, 3, 2, 2, -2, -2, -1, -1, -1, 2, 3, 3, 2, 2, 2, 3};
    int64_t mapping_sum_id[expectRowsCount] = {-5, -5, 3, 3, -1, -4, -4, -5, -5, 9, 9, 9, -4, -1, -1, -4, -4, -4, -1};
    RdCheckDataInTSTableOfStreamTable(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_sum_id);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 数据存在负数进行sum聚合,watermark[tolerant]，滑窗
TEST_F(t04_over_aggregate_calculation_interaction, STREAM_013_198)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "idSum integer) "
            "WITH (TIME_COL = 'window_end', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS tolerant);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "sum(id) OVER(PARTITION BY window_start, window_end, event_time) "
            "FROM TABLE(hop(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum_id FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    int64_t id[rowNum] = {-1, -2, -3, 4, -5, 6, -7, 8, 9, -10, 1000};
    int64_t water_mark[rowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1000};
    int64_t event_time[rowNum] = {-1, -2, -2, -1, 2, -1, 2, 2, 3, 3, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 20;
    uint32_t expectColsCount = 6;
    int64_t mapping_id[expectRowsCount] = {-3, -2, -1, 4, 6, -10, -7, -5, -3, -2, -1, 4, 6, 8, 9, -10, -7, -5, 8, 9};
    int64_t mapping_window_start[expectRowsCount] = {-10, -10, -10, -10, -10, -5, -5, -5, -5, -5, -5, -5, -5, -5, -5, 0, 0, 0, 0, 0};
    int64_t mapping_window_end[expectRowsCount] = {0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10};
    int64_t mapping_event_time[expectRowsCount] = {-2, -2, -1, -1, -1, 3, 2, 2, -2, -2, -1, -1, -1, 2, 3, 3, 2, 2, 2, 3};
    int64_t mapping_sum_id[expectRowsCount] = {-5, -5, 9, 9, 9, -1, -4, -4, -5, -5, 9, 9, 9, -4, -1, -1, -4, -4, -4, -1};
    RdCheckDataInTSTableOfStreamTable(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_sum_id);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 数据为0/-0进行sum聚合,watermark[strcit]，滚窗
TEST_F(t04_over_aggregate_calculation_interaction, STREAM_013_199)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "idSum integer) "
            "WITH (TIME_COL = 'window_end', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "sum(id) OVER(PARTITION BY window_start, window_end, event_time) "
            "FROM TABLE(TUMBLE(TABLE stream1, event_time, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum_id FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 8;
    int64_t id[rowNum] = {-0, -0, 0, 0, 0, 0, 0, 1000};
    int64_t water_mark[rowNum] = {1, 2, 3, 4, 5, 6, 7, 1000};
    int64_t event_time[rowNum] = {-1, -2, -2, -1, 2, -1, 2, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, event_time;";
    uint32_t expectRowsCount = 6;
    uint32_t expectColsCount = 6;
    int64_t mapping_id[expectRowsCount] = {0, 0, 0, 0, 0, 0};
    int64_t mapping_window_start[expectRowsCount] = {-10, -10, -10, -10, 0, 0};
    int64_t mapping_window_end[expectRowsCount] = {0, 0, 0, 0, 10, 10};
    int64_t mapping_event_time[expectRowsCount] = {-2, -2, -1, -1, 2, 2};
    int64_t mapping_sum_id[expectRowsCount] = {0, 0, 0, 0, 0, 0};
    RdCheckDataInTSTableOfStreamTable(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_sum_id);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 数据为0/-0进行sum聚合,watermark[tolerant]，滚窗
TEST_F(t04_over_aggregate_calculation_interaction, STREAM_013_200)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "idSum integer) "
            "WITH (TIME_COL = 'window_end', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS tolerant);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "sum(id) OVER(PARTITION BY window_start, window_end, event_time) "
            "FROM TABLE(TUMBLE(TABLE stream1, event_time, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum_id FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 8;
    int64_t id[rowNum] = {-0, -0, 0, 0, 0, 0, 0, 1000};
    int64_t water_mark[rowNum] = {1, 2, 3, 4, 5, 6, 7, 1000};
    int64_t event_time[rowNum] = {-1, -2, -2, -1, 2, -1, 2, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, event_time;";
    uint32_t expectRowsCount = 7;
    uint32_t expectColsCount = 6;
    int64_t mapping_id[expectRowsCount] = {0, 0, 0, 0, 0, 0, 0};
    int64_t mapping_window_start[expectRowsCount] = {-10, -10, -10, -10, -10, 0, 0};
    int64_t mapping_window_end[expectRowsCount] = {0, 0, 0, 0, 0, 10, 10};
    int64_t mapping_event_time[expectRowsCount] = {-2, -2, -1, -1, -1, 2, 2};
    int64_t mapping_sum_id[expectRowsCount] = {0, 0, 0, 0, 0, 0, 0};
    RdCheckDataInTSTableOfStreamTable(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_sum_id);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 数据为0/-0进行sum聚合,watermark[strcit]，滑窗
TEST_F(t04_over_aggregate_calculation_interaction, STREAM_013_201)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "idSum integer) "
            "WITH (TIME_COL = 'window_end', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS );"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "sum(id) OVER(PARTITION BY window_start, window_end, event_time) "
            "FROM TABLE(hop(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum_id FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 8;
    int64_t id[rowNum] = {-0, -0, 0, 0, 0, 0, 0, 1000};
    int64_t water_mark[rowNum] = {1, 2, 3, 4, 5, 6, 7, 1000};
    int64_t event_time[rowNum] = {-1, -2, -2, -1, 2, -1, 2, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, event_time;";
    uint32_t expectRowsCount = 13;
    uint32_t expectColsCount = 6;
    int64_t mapping_id[expectRowsCount] = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
    int64_t mapping_window_start[expectRowsCount] = {-10, -10, -10, -10, -5, -5, -5, -5, -5, -5, -5, 0, 0};
    int64_t mapping_window_end[expectRowsCount] = {0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 10, 10};
    int64_t mapping_event_time[expectRowsCount] = {-2, -2, -1, -1, -2, -2, -1, -1, -1, 2, 2, 2, 2};
    int64_t mapping_sum_id[expectRowsCount] = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
    RdCheckDataInTSTableOfStreamTable(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_sum_id);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 数据为0/-0进行sum聚合,watermark[tolerant]，滑窗
TEST_F(t04_over_aggregate_calculation_interaction, STREAM_013_202)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "idSum integer) "
            "WITH (TIME_COL = 'window_end', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS tolerant);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "sum(id) OVER(PARTITION BY window_start, window_end, event_time) "
            "FROM TABLE(hop(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum_id FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 8;
    int64_t id[rowNum] = {-0, -0, 0, 0, 0, 0, 0, 1000};
    int64_t water_mark[rowNum] = {1, 2, 3, 4, 5, 6, 7, 1000};
    int64_t event_time[rowNum] = {-1, -2, -2, -1, 2, -1, 2, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, event_time;";
    uint32_t expectRowsCount = 14;
    uint32_t expectColsCount = 6;
    int64_t mapping_id[expectRowsCount] = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
    int64_t mapping_window_start[expectRowsCount] = {-10, -10, -10, -10, -10, -5, -5, -5, -5, -5, -5, -5, 0, 0};
    int64_t mapping_window_end[expectRowsCount] = {0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 10, 10};
    int64_t mapping_event_time[expectRowsCount] = {-2, -2, -1, -1, -1, -2, -2, -1, -1, -1, 2, 2, 2, 2};
    int64_t mapping_sum_id[expectRowsCount] = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
    RdCheckDataInTSTableOfStreamTable(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_sum_id);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// partition by按0/-0分组，滚窗
TEST_F(t04_over_aggregate_calculation_interaction, STREAM_013_203)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "idSum integer) "
            "WITH (TIME_COL = 'window_end', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "sum(id) OVER(PARTITION BY window_start, window_end, event_time) "
            "FROM TABLE(TUMBLE(TABLE stream1, event_time, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum_id FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 3;
    int64_t id[rowNum] = {1, 2, 1000};
    int64_t water_mark[rowNum] = {1, 2, 1000};
    int64_t event_time[rowNum] = {0, -0, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 2;
    uint32_t expectColsCount = 6;
    int64_t mapping_id[expectRowsCount] = {1, 2};
    int64_t mapping_window_start[expectRowsCount] = {0, 0};
    int64_t mapping_window_end[expectRowsCount] = {10, 10};
    int64_t mapping_event_time[expectRowsCount] = {0, -0};
    int64_t mapping_sum_id[expectRowsCount] = {3, 3};
    RdCheckDataInTSTableOfStreamTable(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_sum_id);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// partition by按0/-0分组，滑窗
TEST_F(t04_over_aggregate_calculation_interaction, STREAM_013_204)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "idSum integer) "
            "WITH (TIME_COL = 'window_end', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "sum(id) OVER(PARTITION BY window_start, window_end, event_time) "
            "FROM TABLE(hop(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum_id FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 3;
    int64_t id[rowNum] = {1, 2, 1000};
    int64_t water_mark[rowNum] = {1, 2, 1000};
    int64_t event_time[rowNum] = {0, -0, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 4;
    uint32_t expectColsCount = 6;
    int64_t mapping_id[expectRowsCount] = {1, 2, 1, 2};
    int64_t mapping_window_start[expectRowsCount] = {-5, -5, 0, 0};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 10, 10};
    int64_t mapping_event_time[expectRowsCount] = {0, -0, 0, -0};
    int64_t mapping_sum_id[expectRowsCount] = {3, 3, 3, 3};
    RdCheckDataInTSTableOfStreamTable(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_sum_id);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 数据有正数负数时进行max/min聚合,watermark[有界乱序时间戳，strict],滚窗
TEST_F(t04_over_aggregate_calculation_interaction, STREAM_013_205)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "idMax integer, idMin integer) "
            "WITH (TIME_COL = 'window_end', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "max(id) OVER(PARTITION BY window_start, window_end, event_time), "
            "min(id) OVER(PARTITION BY window_start, window_end, event_time) "
            "FROM TABLE(TUMBLE(TABLE stream1, event_time, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "max_id, min_id FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    int64_t id[rowNum] = {-10, 0, -1, -3, 5, -7, 12, -4, 7, 10, 1000};
    int64_t water_mark[rowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1000};
    int64_t event_time[rowNum] = {-10, 2, -1, -1, 2, -7, 12, -4, 7, 10, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 5;
    uint32_t expectColsCount = 7;
    int64_t mapping_id[expectRowsCount] = {-10, 0, 5, 10, 12};
    int64_t mapping_window_start[expectRowsCount] = {-10, 0, 0, 10, 10};
    int64_t mapping_window_end[expectRowsCount] = {0, 10, 10, 20, 20};
    int64_t mapping_event_time[expectRowsCount] = {-10, 2, 2, 10, 12};
    int64_t mapping_max_id[expectRowsCount] = {-10, 5, 5, 10, 12};
    int64_t mapping_min_id[expectRowsCount] = {-10, 0, 0, 10, 12};
    RdCheckDataInTSTableOfStreamTable5(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_max_id, mapping_min_id);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 数据有正数负数时进行max/min聚合,watermark[有界乱序时间戳，tolerant],滚窗
TEST_F(t04_over_aggregate_calculation_interaction, STREAM_013_206)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "idMax integer, idMin integer) "
            "WITH (TIME_COL = 'window_end', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS tolerant);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "max(id) OVER(PARTITION BY window_start, window_end, event_time), "
            "min(id) OVER(PARTITION BY window_start, window_end, event_time) "
            "FROM TABLE(TUMBLE(TABLE stream1, event_time, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "max_id, min_id FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    int64_t id[rowNum] = {-10, 0, -1, -3, 5, -7, 12, -4, 7, 10, 1000};
    int64_t water_mark[rowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1000};
    int64_t event_time[rowNum] = {-10, 2, -1, -1, 2, -7, 12, -4, 7, 10, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 9;
    uint32_t expectColsCount = 7;
    int64_t mapping_id[expectRowsCount] = {-10, -7, -3, -1, 0, 5, 7, 10, 12};
    int64_t mapping_window_start[expectRowsCount] = {-10, -10, -10, -10, 0, 0, 0, 10, 10};
    int64_t mapping_window_end[expectRowsCount] = {0, 0, 0, 0, 10, 10, 10, 20, 20};
    int64_t mapping_event_time[expectRowsCount] = {-10, -7, -1, -1, 2, 2, 7, 10, 12};
    int64_t mapping_max_id[expectRowsCount] = {-10, -7, -1, -1, 5, 5, 7, 10, 12};
    int64_t mapping_min_id[expectRowsCount] = {-10, -7, -3, -3, 0, 0, 7, 10, 12};
    RdCheckDataInTSTableOfStreamTable5(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_max_id, mapping_min_id);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 数据有正数负数时进行max/min聚合,watermark[有界乱序时间戳，strict],滑窗
TEST_F(t04_over_aggregate_calculation_interaction, STREAM_013_207)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "idMax integer, idMin integer) "
            "WITH (TIME_COL = 'window_end', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "max(id) OVER(PARTITION BY window_start, window_end, event_time), "
            "min(id) OVER(PARTITION BY window_start, window_end, event_time) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "max_id, min_id FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    int64_t id[rowNum] = {-10, 0, -1, -3, 5, -7, 12, -4, 7, 10, 1000};
    int64_t water_mark[rowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1000};
    int64_t event_time[rowNum] = {-10, 2, -1, -1, 2, -7, 12, -4, 7, 10, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 12;
    uint32_t expectColsCount = 7;
    int64_t mapping_id[expectRowsCount] = {-10, -3, -1, 0, 5, 0, 5, 7, 10, 12, 10, 12};
    int64_t mapping_window_start[expectRowsCount] = {-10, -5, -5, -5, -5, 0, 0, 5, 5, 5, 10, 10};
    int64_t mapping_window_end[expectRowsCount] = {0, 5, 5, 5, 5, 10, 10, 15, 15, 15, 20, 20};
    int64_t mapping_event_time[expectRowsCount] = {-10, -1, -1, 2, 2, 2, 2, 7, 10, 12, 10, 12};
    int64_t mapping_max_id[expectRowsCount] = {-10, -1, -1, 5, 5, 5, 5, 7, 10, 12, 10, 12};
    int64_t mapping_min_id[expectRowsCount] = {-10, -3, -3, 0, 0, 0, 0, 7, 10, 12, 10, 12};
    RdCheckDataInTSTableOfStreamTable5(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_max_id, mapping_min_id);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 数据有正数负数时进行max/min聚合,watermark[有界乱序时间戳，tolerant],滑窗
TEST_F(t04_over_aggregate_calculation_interaction, STREAM_013_208)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "idMax integer, idMin integer) "
            "WITH (TIME_COL = 'window_end', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS TOLERANT);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "max(id) OVER(PARTITION BY window_start, window_end, event_time), "
            "min(id) OVER(PARTITION BY window_start, window_end, event_time) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "max_id, min_id FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    int64_t id[rowNum] = {-10, 0, -1, -3, 5, -7, 12, -4, 7, 10, 1000};
    int64_t water_mark[rowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1000};
    int64_t event_time[rowNum] = {-10, 2, -1, -1, 2, -7, 12, -4, 7, 10, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 16;
    uint32_t expectColsCount = 7;
    int64_t mapping_id[expectRowsCount] = {-10, -7, -3, -1, -3, -1, 0, 5, 0, 5, 7, 7, 10, 12, 10, 12};
    int64_t mapping_window_start[expectRowsCount] = {-10, -10, -10, -10, -5, -5, -5, -5, 0, 0, 0, 5, 5, 5, 10, 10};
    int64_t mapping_window_end[expectRowsCount] = {0, 0, 0, 0, 5, 5, 5, 5, 10, 10, 10, 15, 15, 15, 20, 20};
    int64_t mapping_event_time[expectRowsCount] = {-10, -7, -1, -1, -1, -1, 2, 2, 2, 2, 7, 7, 10, 12, 10, 12};
    int64_t mapping_max_id[expectRowsCount] = {-10, -7, -1, -1, -1, -1, 5, 5, 5, 5, 7, 7, 10, 12, 10, 12};
    int64_t mapping_min_id[expectRowsCount] = {-10, -7, -3, -3, -3, -3, 0, 0, 0, 0, 7, 7, 10, 12, 10, 12};
    RdCheckDataInTSTableOfStreamTable5(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_max_id, mapping_min_id);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 全为负数时进行max/min聚合,watermark[有界乱序时间戳，strict],滚窗
TEST_F(t04_over_aggregate_calculation_interaction, STREAM_013_209)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "idMax integer, idMin integer) "
            "WITH (TIME_COL = 'window_end', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "max(id) OVER(PARTITION BY window_start, window_end, event_time), "
            "min(id) OVER(PARTITION BY window_start, window_end, event_time) "
            "FROM TABLE(TUMBLE(TABLE stream1, event_time, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "max_id, min_id FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    int64_t id[rowNum] = {-1, -2, -3, -4, -5, -6, -7, -8, -9, -10, 1000};
    int64_t water_mark[rowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1000};
    int64_t event_time[rowNum] = {-10, -12, -1, -1, -12, -7, -1, -14, -7, -10, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 7;
    uint32_t expectColsCount = 7;
    int64_t mapping_id[expectRowsCount] = {-10, -9, -7, -6, -4, -3, -1};
    int64_t mapping_window_start[expectRowsCount] = {-10, -10, -10, -10, -10, -10, -10};
    int64_t mapping_window_end[expectRowsCount] = {0, 0, 0, 0, 0, 0, 0};
    int64_t mapping_event_time[expectRowsCount] = {-10, -7, -1, -7, -1, -1, -10};
    int64_t mapping_max_id[expectRowsCount] = {-1, -6, -3, -6, -3, -3, -1};
    int64_t mapping_min_id[expectRowsCount] = {-10, -9, -7, -9, -7, -7, -10};
    RdCheckDataInTSTableOfStreamTable5(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_max_id, mapping_min_id);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 全为负数时进行max/min聚合,watermark[有界乱序时间戳，tolerant],滚窗
TEST_F(t04_over_aggregate_calculation_interaction, STREAM_013_210)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "idMax integer, idMin integer) "
            "WITH (TIME_COL = 'window_end', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS TOLERANT);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "max(id) OVER(PARTITION BY window_start, window_end, event_time), "
            "min(id) OVER(PARTITION BY window_start, window_end, event_time) "
            "FROM TABLE(TUMBLE(TABLE stream1, event_time, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "max_id, min_id FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    int64_t id[rowNum] = {-1, -2, -3, -4, -5, -6, -7, -8, -9, -10, 1000};
    int64_t water_mark[rowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1000};
    int64_t event_time[rowNum] = {-10, 2, -1, -1, -12, -7, -1, -14, -7, -10, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 8;
    uint32_t expectColsCount = 7;
    int64_t mapping_id[expectRowsCount] = {-10, -9, -7, -6, -4, -3, -1, -2};
    int64_t mapping_window_start[expectRowsCount] = {-10, -10, -10, -10, -10, -10, -10, 0};
    int64_t mapping_window_end[expectRowsCount] = {0, 0, 0, 0, 0, 0, 0, 10};
    int64_t mapping_event_time[expectRowsCount] = {-10, -7, -1, -7, -1, -1, -10, 2};
    int64_t mapping_max_id[expectRowsCount] = {-1, -6, -3, -6, -3, -3, -1, -2};
    int64_t mapping_min_id[expectRowsCount] = {-10, -9, -7, -9, -7, -7, -10, -2};
    RdCheckDataInTSTableOfStreamTable5(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_max_id, mapping_min_id);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 全为负数时进行max/min聚合,watermark[有界乱序时间戳，strict],滑窗
TEST_F(t04_over_aggregate_calculation_interaction, STREAM_013_211)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "idMax integer, idMin integer) "
            "WITH (TIME_COL = 'window_end', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "max(id) OVER(PARTITION BY window_start, window_end, event_time), "
            "min(id) OVER(PARTITION BY window_start, window_end, event_time) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "max_id, min_id FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    int64_t id[rowNum] = {-1, -2, -3, -4, -5, -6, -7, -8, -9, -10, 1000};
    int64_t water_mark[rowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1000};
    int64_t event_time[rowNum] = {-10, -12, -1, -1, -12, -7, -1, -14, -7, -10, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 10;
    uint32_t expectColsCount = 7;
    int64_t mapping_id[expectRowsCount] = {-10, -9, -7, -6, -4, -3, -1, -7, -4, -3};
    int64_t mapping_window_start[expectRowsCount] = {-10, -10, -10, -10, -10, -10, -10, -5, -5, -5};
    int64_t mapping_window_end[expectRowsCount] = {0, 0, 0, 0, 0, 0, 0, 5, 5, 5};
    int64_t mapping_event_time[expectRowsCount] = {-10, -7, -1, -7, -1, -1, -10, -1, -1, -1};
    int64_t mapping_max_id[expectRowsCount] = {-1, -6, -3, -6, -3, -3, -1, -3, -3, -3};
    int64_t mapping_min_id[expectRowsCount] = {-10, -9, -7, -9, -7, -7, -10, -7, -7, -7};
    RdCheckDataInTSTableOfStreamTable5(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_max_id, mapping_min_id);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 全为负数时进行max/min聚合,watermark[有界乱序时间戳，tolerant],滑窗
TEST_F(t04_over_aggregate_calculation_interaction, STREAM_013_212)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "idMax integer, idMin integer) "
            "WITH (TIME_COL = 'window_end', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS TOLERANT);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "max(id) OVER(PARTITION BY window_start, window_end, event_time), "
            "min(id) OVER(PARTITION BY window_start, window_end, event_time) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "max_id, min_id FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    int64_t id[rowNum] = {-1, -2, -3, -4, -5, -6, -7, -8, -9, -10, 1000};
    int64_t water_mark[rowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1000};
    int64_t event_time[rowNum] = {-10, 2, -1, -1, -12, -7, -1, 3, -7, -10, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 12;
    uint32_t expectColsCount = 7;
    int64_t mapping_id[expectRowsCount] = {-7, -6, -4, -3, -1, -8, -7, -4, -3, -2, -8, -2};
    int64_t mapping_window_start[expectRowsCount] = {-10, -10, -10, -10, -10, -5, -5, -5, -5, -5, 0, 0};
    int64_t mapping_window_end[expectRowsCount] = {0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 10, 10};
    int64_t mapping_event_time[expectRowsCount] = {-1, -7, -1, -1, -10, 3, -1, -1, -1, 2, 3, 2};
    int64_t mapping_max_id[expectRowsCount] = {-3, -6, -3, -3, -1, -8, -3, -3, -3, -2, -8, -2};
    int64_t mapping_min_id[expectRowsCount] = {-7, -6, -7, -7, -1, -8, -7, -7, -7, -2, -8, -2};
    RdCheckDataInTSTableOfStreamTable5(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_max_id, mapping_min_id);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// integer最大/最小值进行max/min聚合, 滚窗
TEST_F(t04_over_aggregate_calculation_interaction, STREAM_013_213)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "idMax integer, idMin integer) "
            "WITH (TIME_COL = 'window_end', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "max(id) OVER(PARTITION BY window_start, window_end, event_time), "
            "min(id) OVER(PARTITION BY window_start, window_end, event_time) "
            "FROM TABLE(TUMBLE(TABLE stream1, event_time, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "max_id, min_id FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 6;
    int64_t id[rowNum] = {9223372036854775807, 9223372036854775806, 0, -9223372036854775807, INT64_MIN, 100};
    int64_t water_mark[rowNum] = {1, 2, 3, 4, 5, 1000};
    int64_t event_time[rowNum] = {0, 0, 0, 0, 0, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据2,562,047,788,015,215
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 5;
    uint32_t expectColsCount = 7;
    int64_t mapping_id[expectRowsCount] = {INT64_MIN, -9223372036854775807, 0,
        9223372036854775806, 9223372036854775807};
    int64_t mapping_window_start[expectRowsCount] = {0, 0, 0, 0, 0};
    int64_t mapping_window_end[expectRowsCount] = {10, 10, 10, 10, 10};
    int64_t mapping_event_time[expectRowsCount] = {0, 0, 0, 0, 0};
    int64_t mapping_max_id[expectRowsCount] = {9223372036854775807, 9223372036854775807, 9223372036854775807,
        9223372036854775807, 9223372036854775807};
    int64_t mapping_min_id[expectRowsCount] = {INT64_MIN, INT64_MIN, INT64_MIN,
        INT64_MIN, INT64_MIN};
    RdCheckDataInTSTableOfStreamTable5(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_max_id, mapping_min_id);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// integer最大/最小值进行max/min聚合, 滑窗
TEST_F(t04_over_aggregate_calculation_interaction, STREAM_013_214)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "idMax integer, idMin integer) "
            "WITH (TIME_COL = 'window_end', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "max(id) OVER(PARTITION BY window_start, window_end, event_time), "
            "min(id) OVER(PARTITION BY window_start, window_end, event_time) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "max_id, min_id FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 6;
    int64_t id[rowNum] = {9223372036854775807, 9223372036854775806, 0, -9223372036854775807, INT64_MIN, 100};
    int64_t water_mark[rowNum] = {1, 2, 3, 4, 5, 1000};
    int64_t event_time[rowNum] = {5, 5, 5, 5, 5, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据2,562,047,788,015,215
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 10;
    uint32_t expectColsCount = 7;
    int64_t mapping_id[expectRowsCount] = {INT64_MIN, -9223372036854775807, 0,
        9223372036854775806, 9223372036854775807, INT64_MIN, -9223372036854775807, 0,
        9223372036854775806, 9223372036854775807};
    int64_t mapping_window_start[expectRowsCount] = {0, 0, 0, 0, 0, 5, 5, 5, 5, 5};
    int64_t mapping_window_end[expectRowsCount] = {10, 10, 10, 10, 10, 15, 15, 15, 15, 15};
    int64_t mapping_event_time[expectRowsCount] = {5, 5, 5, 5, 5, 5, 5, 5, 5, 5};
    int64_t mapping_max_id[expectRowsCount] = {9223372036854775807, 9223372036854775807, 9223372036854775807,
        9223372036854775807, 9223372036854775807, 9223372036854775807, 9223372036854775807, 9223372036854775807,
        9223372036854775807, 9223372036854775807};
    int64_t mapping_min_id[expectRowsCount] = {INT64_MIN, INT64_MIN, INT64_MIN,
        INT64_MIN, INT64_MIN, INT64_MIN, INT64_MIN, INT64_MIN,
        INT64_MIN, INT64_MIN};
    RdCheckDataInTSTableOfStreamTable5(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_max_id, mapping_min_id);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 数据全为0时，进行进行max/min聚合, 滚窗
TEST_F(t04_over_aggregate_calculation_interaction, STREAM_013_215)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "idMax integer, idMin integer) "
            "WITH (TIME_COL = 'window_end', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "max(id) OVER(PARTITION BY window_start, window_end, event_time), "
            "min(id) OVER(PARTITION BY window_start, window_end, event_time) "
            "FROM TABLE(TUMBLE(TABLE stream1, event_time, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "max_id, min_id FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 6;
    int64_t id[rowNum] = {0, 0, 0, 0, 0, 1000};
    int64_t water_mark[rowNum] = {1, 2, 3, 4, 5, 1000};
    int64_t event_time[rowNum] = {0, 0, 0, 0, 0, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 5;
    uint32_t expectColsCount = 7;
    int64_t mapping_id[expectRowsCount] = {0, 0, 0, 0, 0};
    int64_t mapping_window_start[expectRowsCount] = {0, 0, 0, 0, 0};
    int64_t mapping_window_end[expectRowsCount] = {10, 10, 10, 10, 10};
    int64_t mapping_event_time[expectRowsCount] = {0, 0, 0, 0, 0};
    int64_t mapping_max_id[expectRowsCount] = {0, 0, 0, 0, 0};
    int64_t mapping_min_id[expectRowsCount] = {0, 0, 0, 0, 0};
    RdCheckDataInTSTableOfStreamTable5(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_max_id, mapping_min_id);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 数据全为0时，进行进行max/min聚合, 滑窗
TEST_F(t04_over_aggregate_calculation_interaction, STREAM_013_216)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "idMax integer, idMin integer) "
            "WITH (TIME_COL = 'window_end', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "max(id) OVER(PARTITION BY window_start, window_end, event_time), "
            "min(id) OVER(PARTITION BY window_start, window_end, event_time) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "max_id, min_id FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 6;
    int64_t id[rowNum] = {0, 0, 0, 0, 0, 1000};
    int64_t water_mark[rowNum] = {1, 2, 3, 4, 5, 1000};
    int64_t event_time[rowNum] = {0, 0, 0, 0, 0, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 10;
    uint32_t expectColsCount = 7;
    int64_t mapping_id[expectRowsCount] = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
    int64_t mapping_window_start[expectRowsCount] = {-5, -5, -5, -5, -5, 0, 0, 0, 0, 0};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 5, 5, 10, 10, 10, 10, 10};
    int64_t mapping_event_time[expectRowsCount] = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
    int64_t mapping_max_id[expectRowsCount] = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
    int64_t mapping_min_id[expectRowsCount] = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
    RdCheckDataInTSTableOfStreamTable5(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_max_id, mapping_min_id);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 对字符串列进行max/min聚合,watermark[有界乱序时间戳，strict],滚窗
TEST_F(t04_over_aggregate_calculation_interaction, STREAM_013_217)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "max_name char(50), min_name char(50)) "
            "WITH (TIME_COL = 'window_end', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "max(name) OVER(PARTITION BY window_start, window_end, event_time), "
            "min(name) OVER(PARTITION BY window_start, window_end, event_time) "
            "FROM TABLE(TUMBLE(TABLE stream1, event_time, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "max_name, min_name FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 6;
    int64_t id[rowNum] = {1, 2, 3, 4, 5, 1000};
    int64_t water_mark[rowNum] = {1, 2, 3, 4, 5, 1000};
    int64_t event_time[rowNum] = {0, 0, 0, 0, 0, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 5;
    uint32_t expectColsCount = 7;
    int64_t mapping_id[expectRowsCount] = {1, 2, 3, 4, 5};
    int64_t mapping_window_start[expectRowsCount] = {0, 0, 0, 0, 0};
    int64_t mapping_window_end[expectRowsCount] = {10, 10, 10, 10, 10};
    int64_t mapping_event_time[expectRowsCount] = {0, 0, 0, 0, 0};
    const char *mapping_max_name = "name_5";
    const char *mapping_min_name = "name_1";
    RdCheckDataInTSTableOfStreamTable6(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_max_name, mapping_min_name);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 对字符串列进行max/min聚合,watermark[有界乱序时间戳，tolerant],滚窗
TEST_F(t04_over_aggregate_calculation_interaction, STREAM_013_218)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "max_name char(50), min_name char(50)) "
            "WITH (TIME_COL = 'window_end', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS TOLERANT);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "max(name) OVER(PARTITION BY window_start, window_end, event_time), "
            "min(name) OVER(PARTITION BY window_start, window_end, event_time) "
            "FROM TABLE(TUMBLE(TABLE stream1, event_time, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "max_name, min_name FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 6;
    int64_t id[rowNum] = {1, 2, 3, 4, 5, 1000};
    int64_t water_mark[rowNum] = {1, 2, 3, 4, 5, 1000};
    int64_t event_time[rowNum] = {0, 0, 0, 0, 0, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 5;
    uint32_t expectColsCount = 7;
    int64_t mapping_id[expectRowsCount] = {1, 2, 3, 4, 5};
    int64_t mapping_window_start[expectRowsCount] = {0, 0, 0, 0, 0};
    int64_t mapping_window_end[expectRowsCount] = {10, 10, 10, 10, 10};
    int64_t mapping_event_time[expectRowsCount] = {0, 0, 0, 0, 0};
    const char *mapping_max_name = "name_5";
    const char *mapping_min_name = "name_1";
    RdCheckDataInTSTableOfStreamTable6(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_max_name, mapping_min_name);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 对字符串列进行max/min聚合,watermark[有界乱序时间戳，strict],滑窗
TEST_F(t04_over_aggregate_calculation_interaction, STREAM_013_219)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "max_name char(50), min_name char(50)) "
            "WITH (TIME_COL = 'window_end', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "max(name) OVER(PARTITION BY window_start, window_end, event_time), "
            "min(name) OVER(PARTITION BY window_start, window_end, event_time) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "max_name, min_name FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 6;
    int64_t id[rowNum] = {1, 2, 3, 4, 5, 1000};
    int64_t water_mark[rowNum] = {1, 2, 3, 4, 5, 1000};
    int64_t event_time[rowNum] = {0, 0, 0, 0, 0, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 10;
    uint32_t expectColsCount = 7;
    int64_t mapping_id[expectRowsCount] = {1, 2, 3, 4, 5, 1, 2, 3, 4, 5};
    int64_t mapping_window_start[expectRowsCount] = {-5, -5, -5, -5, -5, 0, 0, 0, 0, 0};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 5, 5, 10, 10, 10, 10, 10};
    int64_t mapping_event_time[expectRowsCount] = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
    const char *mapping_max_name = "name_5";
    const char *mapping_min_name = "name_1";
    RdCheckDataInTSTableOfStreamTable6(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_max_name, mapping_min_name);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 对字符串列进行max/min聚合,watermark[有界乱序时间戳，strict],滑窗
TEST_F(t04_over_aggregate_calculation_interaction, STREAM_013_220)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "max_name char(50), min_name char(50)) "
            "WITH (TIME_COL = 'window_end', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS TOlerant);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "max(name) OVER(PARTITION BY window_start, window_end, event_time), "
            "min(name) OVER(PARTITION BY window_start, window_end, event_time) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "max_name, min_name FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 6;
    int64_t id[rowNum] = {1, 2, 3, 4, 5, 1000};
    int64_t water_mark[rowNum] = {1, 2, 3, 4, 5, 1000};
    int64_t event_time[rowNum] = {0, 0, 0, 0, 0, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 10;
    uint32_t expectColsCount = 7;
    int64_t mapping_id[expectRowsCount] = {1, 2, 3, 4, 5, 1, 2, 3, 4, 5};
    int64_t mapping_window_start[expectRowsCount] = {-5, -5, -5, -5, -5, 0, 0, 0, 0, 0};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 5, 5, 10, 10, 10, 10, 10};
    int64_t mapping_event_time[expectRowsCount] = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
    const char *mapping_max_name = "name_5";
    const char *mapping_min_name = "name_1";
    RdCheckDataInTSTableOfStreamTable6(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_max_name, mapping_min_name);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 在创建滚窗的view中使用seq_distinct_count函数
TEST_F(t04_over_aggregate_calculation_interaction, STREAM_013_221)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, seq_distinct_count(id), "
            "sum(id) OVER(PARTITION BY window_start, window_end, water_mark) "
            "FROM TABLE(Tumble(TABLE stream1, event_time, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SEMANTIC_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 在创建滑窗的view中使用seq_distinct_count函数
TEST_F(t04_over_aggregate_calculation_interaction, STREAM_013_222)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, seq_distinct_count(id), "
            "sum(id) OVER(PARTITION BY window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SEMANTIC_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 有重复数据进行count聚合,滚窗
TEST_F(t04_over_aggregate_calculation_interaction, STREAM_013_223)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "count_id integer) "
            "WITH (TIME_COL = 'window_end', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "count(id) OVER(PARTITION BY window_start, window_end, event_time) "
            "FROM TABLE(TUMBLE(TABLE stream1, event_time, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "count_id FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 6;
    int64_t id[rowNum] = {1, 1, 1, 1, 2, 1000};
    int64_t water_mark[rowNum] = {1, 2, 3, 4, 5, 1000};
    int64_t event_time[rowNum] = {0, 0, 0, 0, 0, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 5;
    uint32_t expectColsCount = 6;
    int64_t mapping_id[expectRowsCount] = {1, 1, 1, 1, 2};
    int64_t mapping_window_start[expectRowsCount] = {0, 0, 0, 0, 0};
    int64_t mapping_window_end[expectRowsCount] = {10, 10, 10, 10, 10};
    int64_t mapping_event_time[expectRowsCount] = {0, 0, 0, 0, 0};
    int64_t mapping_count_id[expectRowsCount] = {5, 5, 5, 5, 5};
    RdCheckDataInTSTableOfStreamTable(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_count_id);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 有重复数据进行count聚合,滑窗
TEST_F(t04_over_aggregate_calculation_interaction, STREAM_013_224)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "count_id integer) "
            "WITH (TIME_COL = 'window_end', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "count(id) OVER(PARTITION BY window_start, window_end, event_time) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "count_id FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 6;
    int64_t id[rowNum] = {1, 1, 1, 1, 2, 1000};
    int64_t water_mark[rowNum] = {1, 2, 3, 4, 5, 1000};
    int64_t event_time[rowNum] = {0, 0, 0, 0, 0, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 10;
    uint32_t expectColsCount = 6;
    int64_t mapping_id[expectRowsCount] = {1, 1, 1, 1, 2, 1, 1, 1, 1, 2};
    int64_t mapping_window_start[expectRowsCount] = {-5, -5, -5, -5, -5, 0, 0, 0, 0, 0};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 5, 5, 10, 10, 10, 10, 10};
    int64_t mapping_event_time[expectRowsCount] = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
    int64_t mapping_count_id[expectRowsCount] = {5, 5, 5, 5, 5, 5, 5, 5, 5, 5};
    RdCheckDataInTSTableOfStreamTable(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_count_id);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 对字符串列进行count聚合,watermark[有界乱序时间戳，strict],滚窗
TEST_F(t04_over_aggregate_calculation_interaction, STREAM_013_225)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "count_name integer) "
            "WITH (TIME_COL = 'window_end', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "count(name) OVER(PARTITION BY window_start, window_end, event_time) "
            "FROM TABLE(TUMBLE(TABLE stream1, event_time, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "count_name FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 9;
    int64_t id[rowNum] = {-1, -2, 3, 4, -5, 6, 7, 8, 1000};
    int64_t water_mark[rowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 1000};
    int64_t event_time[rowNum] = {-1, -1, 2, -1, 2, 2, 10, 11, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 7;
    uint32_t expectColsCount = 6;
    int64_t mapping_id[expectRowsCount] = {-2, -1, -5, 3, 6, 7, 8};
    int64_t mapping_window_start[expectRowsCount] = {-10, -10, 0, 0, 0, 10, 10};
    int64_t mapping_window_end[expectRowsCount] = {0, 0, 10, 10, 10, 20, 20};
    int64_t mapping_event_time[expectRowsCount] = {-1, -1, 2, 2, 2, 10, 11};
    int64_t mapping_count_name[expectRowsCount] = {2, 2, 3, 3, 3, 1, 1};
    RdCheckDataInTSTableOfStreamTable(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_count_name);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 对字符串列进行count聚合,watermark[有界乱序时间戳，tolerant],滚窗
TEST_F(t04_over_aggregate_calculation_interaction, STREAM_013_226)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "count_name integer) "
            "WITH (TIME_COL = 'window_end', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS tolerant);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "count(name) OVER(PARTITION BY window_start, window_end, event_time) "
            "FROM TABLE(TUMBLE(TABLE stream1, event_time, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "count_name FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 9;
    int64_t id[rowNum] = {-1, -2, 3, 4, -5, 6, 7, 8, 1000};
    int64_t water_mark[rowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 1000};
    int64_t event_time[rowNum] = {-1, -1, 2, -2, 2, 2, 10, 11, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 8;
    uint32_t expectColsCount = 6;
    int64_t mapping_id[expectRowsCount] = {-2, -1, 4, -5, 3, 6, 7, 8};
    int64_t mapping_window_start[expectRowsCount] = {-10, -10, -10, 0, 0, 0, 10, 10};
    int64_t mapping_window_end[expectRowsCount] = {0, 0, 0, 10, 10, 10, 20, 20};
    int64_t mapping_event_time[expectRowsCount] = {-1, -1, -2, 2, 2, 2, 10, 11};
    int64_t mapping_count_name[expectRowsCount] = {2, 2, 1, 3, 3, 3, 1, 1};
    RdCheckDataInTSTableOfStreamTable(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_count_name);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 对字符串列进行count聚合,watermark[有界乱序时间戳，strict],滑窗
TEST_F(t04_over_aggregate_calculation_interaction, STREAM_013_227)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "count_name integer) "
            "WITH (TIME_COL = 'window_end', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "count(name) OVER(PARTITION BY window_start, window_end, event_time) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "count_name FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 9;
    int64_t id[rowNum] = {-1, -2, 3, 4, -5, 6, 7, 8, 1000};
    int64_t water_mark[rowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 1000};
    int64_t event_time[rowNum] = {-1, -1, 2, -1, 2, 2, 10, 11, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 15;
    uint32_t expectColsCount = 6;
    int64_t mapping_id[expectRowsCount] = {-2, -1, -5, -2, -1, 3, 4, 6, -5, 3, 6, 7, 8, 7, 8};
    int64_t mapping_window_start[expectRowsCount] = {-10, -10, -5, -5, -5, -5, -5, -5, 0, 0, 0, 5, 5, 10, 10};
    int64_t mapping_window_end[expectRowsCount] = {0, 0, 5, 5, 5, 5, 5, 5, 10, 10, 10, 15, 15, 20, 20};
    int64_t mapping_event_time[expectRowsCount] = {-1, -1, 2, -1, -1, 2, -1, 2, 2, 2, 2, 10, 11, 10, 11};
    int64_t mapping_count_name[expectRowsCount] = {2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3, 1, 1, 1, 1};
    RdCheckDataInTSTableOfStreamTable(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_count_name);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 对字符串列进行count聚合,watermark[有界乱序时间戳，tolerant],滑窗
TEST_F(t04_over_aggregate_calculation_interaction, STREAM_013_228)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "count_name integer) "
            "WITH (TIME_COL = 'window_end', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS tolerant);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "count(name) OVER(PARTITION BY window_start, window_end, event_time) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "count_name FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 9;
    int64_t id[rowNum] = {-1, -2, 3, 4, -5, 6, 7, 8, 1000};
    int64_t water_mark[rowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 1000};
    int64_t event_time[rowNum] = {-1, -1, 2, -1, 2, 2, 10, 11, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 16;
    uint32_t expectColsCount = 6;
    int64_t mapping_id[expectRowsCount] = {-2, -1, 4, -5, -2, -1, 3, 4, 6, -5, 3, 6, 7, 8, 7, 8};
    int64_t mapping_window_start[expectRowsCount] = {-10, -10, -10, -5, -5, -5, -5, -5, -5, 0, 0, 0, 5, 5, 10, 10};
    int64_t mapping_window_end[expectRowsCount] = {0, 0, 0, 5, 5, 5, 5, 5, 5, 10, 10, 10, 15, 15, 20, 20};
    int64_t mapping_event_time[expectRowsCount] = {-1, -1, -1, 2, -1, -1, 2, -1, 2, 2, 2, 2, 10, 11, 10, 11};
    int64_t mapping_count_name[expectRowsCount] = {3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 1, 1, 1, 1};
    RdCheckDataInTSTableOfStreamTable(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_count_name);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 滑窗步长3，大小7，watermark[有界乱序时间戳，strict]，执行聚合
TEST_F(t04_over_aggregate_calculation_interaction, STREAM_013_229)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "idMax integer, idMin integer) "
            "WITH (TIME_COL = 'window_end', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS strict);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "max(id) OVER(PARTITION BY window_start, window_end, event_time), "
            "min(id) OVER(PARTITION BY window_start, window_end, event_time) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '3' SECONDS, INTERVAL '7' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "max_id, min_id FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    int64_t id[rowNum] = {-10, 0, -1, -3, 5, -7, 12, -4, 7, 10, 1000};
    int64_t water_mark[rowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1000};
    int64_t event_time[rowNum] = {-10, 2, -1, -1, 2, -7, 12, -4, 7, 10, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 14;
    uint32_t expectColsCount = 7;
    int64_t mapping_id[expectRowsCount] = {-3, -1, -3, -1, 0, 5, 0, 5, 7, 10, 12, 10, 12, 12};
    int64_t mapping_window_start[expectRowsCount] = {-6, -6, -3, -3, -3, -3, 0, 0, 6, 6, 6, 9, 9, 12};
    int64_t mapping_window_end[expectRowsCount] = {1, 1, 4, 4, 4, 4, 7, 7, 13, 13, 13, 16, 16, 19};
    int64_t mapping_event_time[expectRowsCount] = {-1, -1, -1, -1, 2, 2, 2, 2, 7, 10, 12, 10, 12, 12};
    int64_t mapping_max_id[expectRowsCount] = {-1, -1, -1, -1, 5, 5, 5, 5, 7, 10, 12, 10, 12, 12};
    int64_t mapping_min_id[expectRowsCount] = {-3, -3, -3, -3, 0, 0, 0, 0, 7, 10, 12, 10, 12, 12};
    RdCheckDataInTSTableOfStreamTable5(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_max_id, mapping_min_id);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 滑窗步长3，大小7，watermark[有界乱序时间戳，tolerant]，执行聚合
TEST_F(t04_over_aggregate_calculation_interaction, STREAM_013_230)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "idMax integer, idMin integer) "
            "WITH (TIME_COL = 'window_end', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS tolerant);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "max(id) OVER(PARTITION BY window_start, window_end, event_time), "
            "min(id) OVER(PARTITION BY window_start, window_end, event_time) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '3' SECONDS, INTERVAL '7' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "max_id, min_id FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    int64_t id[rowNum] = {-10, 0, -1, -3, 5, -7, 12, -4, 7, 10, 1000};
    int64_t water_mark[rowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1000};
    int64_t event_time[rowNum] = {-10, 2, -1, -1, 2, -7, 12, -4, 7, 10, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 15;
    uint32_t expectColsCount = 7;
    int64_t mapping_id[expectRowsCount] = {-3, -1, -3, -1, 0, 5, 0, 5, 7, 7, 10, 12, 10, 12, 12};
    int64_t mapping_window_start[expectRowsCount] = {-6, -6, -3, -3, -3, -3, 0, 0, 3, 6, 6, 6, 9, 9, 12};
    int64_t mapping_window_end[expectRowsCount] = {1, 1, 4, 4, 4, 4, 7, 7, 10, 13, 13, 13, 16, 16, 19};
    int64_t mapping_event_time[expectRowsCount] = {-1, -1, -1, -1, 2, 2, 2, 2, 7, 7, 10, 12, 10, 12, 12};
    int64_t mapping_max_id[expectRowsCount] = {-1, -1, -1, -1, 5, 5, 5, 5, 7, 7, 10, 12, 10, 12, 12};
    int64_t mapping_min_id[expectRowsCount] = {-3, -3, -3, -3, 0, 0, 0, 0, 7, 7, 10, 12, 10, 12, 12};
    RdCheckDataInTSTableOfStreamTable5(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_max_id, mapping_min_id);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
