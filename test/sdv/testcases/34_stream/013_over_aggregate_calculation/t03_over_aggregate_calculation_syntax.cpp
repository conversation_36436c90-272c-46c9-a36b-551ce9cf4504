/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 流视图支持基于滑动窗口的over聚合计算 语法测试
 * Author: yushijin
 * Create: 2024-10-24
 */
#include "gtest/gtest.h"
#include "securec.h"
#include "gmc_sql.h"
#include "gmc_persist.h"
#include "rd_feature_stream.h"
#include "over_aggregate_calculation.h"

#define STREAM_REFERENCE_MAX_NAME_LEN 256
#define STREAM_REFERENCE_MAX_SQL_LEN 512

class t03_over_aggregate_calculation_syntax : public testing::Test {
public:
    static GmcConnT *conn;
    static GmcStmtT *stmt;

    static void SetUpTestCase();
    static void TearDownTestCase();
    virtual void SetUp();
    virtual void TearDown();
};

GmcConnT *t03_over_aggregate_calculation_syntax::conn = NULL;
GmcStmtT *t03_over_aggregate_calculation_syntax::stmt = NULL;

void t03_over_aggregate_calculation_syntax::SetUpTestCase()
{
    int32_t ret;
    ret = RdStreamEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void t03_over_aggregate_calculation_syntax::TearDownTestCase()
{
    int32_t ret = RdStreamEnvClean();
    EXPECT_EQ(GMERR_OK, ret);
}

void t03_over_aggregate_calculation_syntax::SetUp()
{
    int32_t ret = RdGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void t03_over_aggregate_calculation_syntax::TearDown()
{
    int32_t ret = RdGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// sum使用*做参数
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_068)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time STRICT);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum(*) OVER(PARTITION BY window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SYNTAX_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// count使用*做参数
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_069)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idCount integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time STRICT);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "count(*) OVER(PARTITION BY window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SYNTAX_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// max使用*做参数
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_070)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idMax integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time STRICT);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "max(*) OVER(PARTITION BY window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SYNTAX_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// min使用*做参数
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_071)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idMin integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time STRICT);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "min(*) OVER(PARTITION BY window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SYNTAX_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// sum使用多个列做参数
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_072)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time STRICT);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum(id, water_mark) OVER(PARTITION BY window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SEMANTIC_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// count使用多个列做参数
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_073)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idCount integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time STRICT);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "count(id, water_mark) OVER(PARTITION BY window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SEMANTIC_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// max使用多个列做参数
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_074)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idMax integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time STRICT);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "max(id, water_mark) OVER(PARTITION BY window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SEMANTIC_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// min使用多个列做参数
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_075)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idMin integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time STRICT);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "min(id, water_mark) OVER(PARTITION BY window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SEMANTIC_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// sum使用不存在的列做参数
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_076)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time STRICT);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum(id1) OVER(PARTITION BY window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_UNDEFINE_COLUMN, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// count使用不存在的列做参数
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_077)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idCount integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time STRICT);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "count(id1) OVER(PARTITION BY window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_UNDEFINE_COLUMN, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// max使用不存在的列做参数
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_078)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idMax integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time STRICT);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "max(id1) OVER(PARTITION BY window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_UNDEFINE_COLUMN, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// min使用不存在的列做参数
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_079)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idMin integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time STRICT);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "min(id1) OVER(PARTITION BY window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_UNDEFINE_COLUMN, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// sum使用常数做参数
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_080)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time STRICT);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum(1) OVER(PARTITION BY window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum_1 FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    int64_t id[rowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11};
    int64_t event_time[rowNum] = {10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *selectTsName = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 20;
    uint32_t expectColsCount = 6;
    int64_t mapping_id[expectRowsCount] = {1, 2, 3, 4, 5, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 6, 7, 8, 9, 10};
    int64_t mapping_window_start[expectRowsCount] = {5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 15, 15, 15, 15, 15};
    int64_t mapping_window_end[expectRowsCount] = {15, 15, 15, 15, 15, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 25, 25, 25, 25, 25};
    int64_t mapping_event_time[expectRowsCount] = {10, 11, 12, 13, 14, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 15, 16, 17, 18, 19};
    int64_t mapping_aggregate[expectRowsCount] = {2, 2, 2, 2, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 1, 2, 2, 2, 2};
    RdCheckDataInTSTableOfStreamTable(stmt, selectTsName, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time, mapping_aggregate);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// count使用常数做参数
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_081)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50),  window_start integer, window_end integer, event_time integer,"
            "constCount integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "count(1) OVER(PARTITION BY window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "count_1 FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    int64_t id[rowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11};
    int64_t event_time[rowNum] = {10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *selectTsName = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 20;
    uint32_t expectColsCount = 6;
    int64_t mapping_id[expectRowsCount] = {1, 2, 3, 4, 5, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 6, 7, 8, 9, 10};
    int64_t mapping_window_start[expectRowsCount] = {5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 15, 15, 15, 15, 15};
    int64_t mapping_window_end[expectRowsCount] = {15, 15, 15, 15, 15, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 25, 25, 25, 25, 25};
    int64_t mapping_event_time[expectRowsCount] = {10, 11, 12, 13, 14, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 15, 16, 17, 18, 19};
    int64_t mapping_aggregate[expectRowsCount] = {2, 2, 2, 2, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 1, 2, 2, 2, 2};
    RdCheckDataInTSTableOfStreamTable(stmt, selectTsName, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time, mapping_aggregate);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}


// max使用常数做参数
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_082)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idMax integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time STRICT);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "max(1) OVER(PARTITION BY window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "max_1 FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    int64_t id[rowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11};
    int64_t event_time[rowNum] = {10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *selectTsName = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 20;
    uint32_t expectColsCount = 6;
    int64_t mapping_id[expectRowsCount] = {1, 2, 3, 4, 5, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 6, 7, 8, 9, 10};
    int64_t mapping_window_start[expectRowsCount] = {5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 15, 15, 15, 15, 15};
    int64_t mapping_window_end[expectRowsCount] = {15, 15, 15, 15, 15, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 25, 25, 25, 25, 25};
    int64_t mapping_event_time[expectRowsCount] = {10, 11, 12, 13, 14, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 15, 16, 17, 18, 19};
    int64_t mapping_aggregate[expectRowsCount] = {1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1};
    RdCheckDataInTSTableOfStreamTable(stmt, selectTsName, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time, mapping_aggregate);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// min使用常数做参数
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_083)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idMin integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time STRICT);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "min(1) OVER(PARTITION BY window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "min_1 FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    int64_t id[rowNum] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 11};
    int64_t event_time[rowNum] = {10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *selectTsName = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 20;
    uint32_t expectColsCount = 6;
    int64_t mapping_id[expectRowsCount] = {1, 2, 3, 4, 5, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 6, 7, 8, 9, 10};
    int64_t mapping_window_start[expectRowsCount] = {5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 15, 15, 15, 15, 15};
    int64_t mapping_window_end[expectRowsCount] = {15, 15, 15, 15, 15, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 25, 25, 25, 25, 25};
    int64_t mapping_event_time[expectRowsCount] = {10, 11, 12, 13, 14, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 15, 16, 17, 18, 19};
    int64_t mapping_aggregate[expectRowsCount] = {1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1};
    RdCheckDataInTSTableOfStreamTable(stmt, selectTsName, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time, mapping_aggregate);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// sum使用字符串做参数
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_084)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time STRICT);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum(\"id\") OVER(PARTITION BY window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SYNTAX_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// count使用字符串做参数
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_085)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idCount integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time STRICT);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "count(\"id\") OVER(PARTITION BY window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SYNTAX_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// max使用字符串做参数
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_086)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idMax integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time STRICT);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "max(\"id\") OVER(PARTITION BY window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SYNTAX_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// min使用字符串做参数
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_087)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idMin integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time STRICT);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "min(\"id\") OVER(PARTITION BY window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SYNTAX_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// sum使用中文做参数
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_088)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time STRICT);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum(测试) OVER(PARTITION BY window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_UNDEFINE_COLUMN, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// count使用中文做参数
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_089)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idCount integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time STRICT);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "count(测试) OVER(PARTITION BY window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_UNDEFINE_COLUMN, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// max使用中文做参数
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_090)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idMax integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time STRICT);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "max(测试) OVER(PARTITION BY window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_UNDEFINE_COLUMN, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// min使用中文做参数
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_091)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idMin integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time STRICT);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "min(测试) OVER(PARTITION BY window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_UNDEFINE_COLUMN, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// sum嵌套使用
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_092)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time STRICT);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum(sum(id)) OVER(PARTITION BY window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SEMANTIC_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// count嵌套使用
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_093)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idCount integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time STRICT);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "count(count(id)) OVER(PARTITION BY window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SEMANTIC_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// max嵌套使用
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_094)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idMax integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time STRICT);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "max(max(id)) OVER(PARTITION BY window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SEMANTIC_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// min嵌套使用
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_095)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idMin integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time STRICT);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "min(min(id)) OVER(PARTITION BY window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SEMANTIC_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// sum函数partition by后参数只有window_start和window_end
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_096)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum(id) OVER(PARTITION BY window_start, window_end) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, sum_id "
            "FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    int64_t id[rowNum] = {0, 0, 0, 5, 5, 5, 10, 10, 10, 10, 11};
    int64_t water_mark[rowNum] = {1, 1, 1, 2, 2, 2, 3, 3, 3, 3, 11};
    int64_t event_time[rowNum] = {0, 0, 0, 5, 5, 5, 10, 10, 10, 10, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 20;
    uint32_t expectColsCount = 6;
    int64_t mapping_id[expectRowsCount] = {0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10};
    int64_t mapping_window_start[expectRowsCount] = {-5, -5, -5, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 10, 10, 10, 10, 10, 10, 15, 15, 15, 15, 15, 15, 15, 20, 20, 20, 20};
    int64_t mapping_event_time[expectRowsCount] = {0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10};
    int64_t mapping_sum[expectRowsCount] = {0, 0, 0, 15, 15, 15, 15, 15, 15, 55, 55, 55, 55, 55, 55, 55, 40, 40, 40, 40};
    RdCheckDataInTSTableOfStreamTable(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time, mapping_sum);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// count函数partition by后参数只有window_start和window_end
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_097)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idCount integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "count(id) OVER(PARTITION BY window_start, window_end) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, count_id "
            "FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    int64_t id[rowNum] = {0, 0, 0, 5, 5, 5, 10, 10, 10, 10, 11};
    int64_t water_mark[rowNum] = {1, 1, 1, 2, 2, 2, 3, 3, 3, 3, 11};
    int64_t event_time[rowNum] = {0, 0, 0, 5, 5, 5, 10, 10, 10, 10, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 20;
    uint32_t expectColsCount = 6;
    int64_t mapping_id[expectRowsCount] = {0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10};
    int64_t mapping_window_start[expectRowsCount] = {-5, -5, -5, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 10, 10, 10, 10, 10, 10, 15, 15, 15, 15, 15, 15, 15, 20, 20, 20, 20};
    int64_t mapping_event_time[expectRowsCount] = {0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10};
    int64_t mapping_count[expectRowsCount] = {3, 3, 3, 6, 6, 6, 6, 6, 6, 7, 7, 7, 7, 7, 7, 7, 4, 4, 4, 4};
    RdCheckDataInTSTableOfStreamTable(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time, mapping_count);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// max函数partition by后参数只有window_start和window_end
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_098)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idMax integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "max(id) OVER(PARTITION BY window_start, window_end) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, max_id "
            "FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    int64_t id[rowNum] = {0, 0, 0, 5, 5, 5, 10, 10, 10, 10, 11};
    int64_t water_mark[rowNum] = {1, 1, 1, 2, 2, 2, 3, 3, 3, 3, 11};
    int64_t event_time[rowNum] = {0, 0, 0, 5, 5, 5, 10, 10, 10, 10, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 20;
    uint32_t expectColsCount = 6;
    int64_t mapping_id[expectRowsCount] = {0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10};
    int64_t mapping_window_start[expectRowsCount] = {-5, -5, -5, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 10, 10, 10, 10, 10, 10, 15, 15, 15, 15, 15, 15, 15, 20, 20, 20, 20};
    int64_t mapping_event_time[expectRowsCount] = {0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10};
    int64_t mapping_max[expectRowsCount] = {0, 0, 0, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10};
    RdCheckDataInTSTableOfStreamTable(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time, mapping_max);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// min函数partition by后参数只有window_start和window_end
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_099)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idMin integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "min(id) OVER(PARTITION BY window_start, window_end) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, min_id "
            "FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    int64_t id[rowNum] = {0, 0, 0, 5, 5, 5, 10, 10, 10, 10, 11};
    int64_t water_mark[rowNum] = {1, 1, 1, 2, 2, 2, 3, 3, 3, 3, 11};
    int64_t event_time[rowNum] = {0, 0, 0, 5, 5, 5, 10, 10, 10, 10, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 20;
    uint32_t expectColsCount = 6;
    int64_t mapping_id[expectRowsCount] = {0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10};
    int64_t mapping_window_start[expectRowsCount] = {-5, -5, -5, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 10, 10, 10, 10, 10, 10, 15, 15, 15, 15, 15, 15, 15, 20, 20, 20, 20};
    int64_t mapping_event_time[expectRowsCount] = {0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10};
    int64_t mapping_min[expectRowsCount] = {0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10};
    RdCheckDataInTSTableOfStreamTable(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time, mapping_min);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// sum函数partition by参数缺少window_start
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_100)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum(id) OVER(PARTITION BY window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SEMANTIC_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// count函数partition by参数缺少window_start
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_101)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idCount integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "count(id) OVER(PARTITION BY window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SEMANTIC_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// max函数partition by参数缺少window_start
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_102)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idMax integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "max(id) OVER(PARTITION BY window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SEMANTIC_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// min函数partition by参数缺少window_start
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_103)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idMin integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "min(id) OVER(PARTITION BY window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SEMANTIC_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// sum函数partition by参数缺少window_end
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_104)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum(id) OVER(PARTITION BY window_start, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SEMANTIC_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// count函数partition by参数缺少window_end
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_105)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idCount integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "count(id) OVER(PARTITION BY window_start, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SEMANTIC_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// max函数partition by参数缺少window_end
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_106)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idMax integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "max(id) OVER(PARTITION BY window_start, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SEMANTIC_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// min函数partition by参数缺少window_end
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_107)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idMin integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "min(id) OVER(PARTITION BY window_start, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SEMANTIC_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// sum函数partition by后参数window_start和window_end交换顺序
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_108)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum(id) OVER(PARTITION BY window_end, window_start) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, sum_id "
            "FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    int64_t id[rowNum] = {0, 0, 0, 5, 5, 5, 10, 10, 10, 10, 11};
    int64_t water_mark[rowNum] = {1, 1, 1, 2, 2, 2, 3, 3, 3, 3, 11};
    int64_t event_time[rowNum] = {0, 0, 0, 5, 5, 5, 10, 10, 10, 10, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 20;
    uint32_t expectColsCount = 6;
    int64_t mapping_id[expectRowsCount] = {0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10};
    int64_t mapping_window_start[expectRowsCount] = {-5, -5, -5, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 10, 10, 10, 10, 10, 10, 15, 15, 15, 15, 15, 15, 15, 20, 20, 20, 20};
    int64_t mapping_event_time[expectRowsCount] = {0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10};
    int64_t mapping_sum[expectRowsCount] = {0, 0, 0, 15, 15, 15, 15, 15, 15, 55, 55, 55, 55, 55, 55, 55, 40, 40, 40, 40};
    RdCheckDataInTSTableOfStreamTable(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time, mapping_sum);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// count函数partition by后参数window_start和window_end交换顺序
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_109)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idCount integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "count(id) OVER(PARTITION BY window_end, window_start) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, count_id "
            "FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    int64_t id[rowNum] = {0, 0, 0, 5, 5, 5, 10, 10, 10, 10, 11};
    int64_t water_mark[rowNum] = {1, 1, 1, 2, 2, 2, 3, 3, 3, 3, 11};
    int64_t event_time[rowNum] = {0, 0, 0, 5, 5, 5, 10, 10, 10, 10, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 20;
    uint32_t expectColsCount = 6;
    int64_t mapping_id[expectRowsCount] = {0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10};
    int64_t mapping_window_start[expectRowsCount] = {-5, -5, -5, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 10, 10, 10, 10, 10, 10, 15, 15, 15, 15, 15, 15, 15, 20, 20, 20, 20};
    int64_t mapping_event_time[expectRowsCount] = {0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10};
    int64_t mapping_count[expectRowsCount] = {3, 3, 3, 6, 6, 6, 6, 6, 6, 7, 7, 7, 7, 7, 7, 7, 4, 4, 4, 4};
    RdCheckDataInTSTableOfStreamTable(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time, mapping_count);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// max函数partition by后参数window_start和window_end交换顺序
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_110)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idMax integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "max(id) OVER(PARTITION BY window_end, window_start) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, max_id "
            "FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    int64_t id[rowNum] = {0, 0, 0, 5, 5, 5, 10, 10, 10, 10, 11};
    int64_t water_mark[rowNum] = {1, 1, 1, 2, 2, 2, 3, 3, 3, 3, 11};
    int64_t event_time[rowNum] = {0, 0, 0, 5, 5, 5, 10, 10, 10, 10, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 20;
    uint32_t expectColsCount = 6;
    int64_t mapping_id[expectRowsCount] = {0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10};
    int64_t mapping_window_start[expectRowsCount] = {-5, -5, -5, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 10, 10, 10, 10, 10, 10, 15, 15, 15, 15, 15, 15, 15, 20, 20, 20, 20};
    int64_t mapping_event_time[expectRowsCount] = {0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10};
    int64_t mapping_max[expectRowsCount] = {0, 0, 0, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10};
    RdCheckDataInTSTableOfStreamTable(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time, mapping_max);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// min函数partition by后参数window_start和window_end交换顺序
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_111)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idMin integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "min(id) OVER(PARTITION BY window_end, window_start) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, min_id "
            "FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    int64_t id[rowNum] = {0, 0, 0, 5, 5, 5, 10, 10, 10, 10, 11};
    int64_t water_mark[rowNum] = {1, 1, 1, 2, 2, 2, 3, 3, 3, 3, 11};
    int64_t event_time[rowNum] = {0, 0, 0, 5, 5, 5, 10, 10, 10, 10, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 20;
    uint32_t expectColsCount = 6;
    int64_t mapping_id[expectRowsCount] = {0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10};
    int64_t mapping_window_start[expectRowsCount] = {-5, -5, -5, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 10, 10, 10, 10, 10, 10, 15, 15, 15, 15, 15, 15, 15, 20, 20, 20, 20};
    int64_t mapping_event_time[expectRowsCount] = {0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10};
    int64_t mapping_min[expectRowsCount] = {0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10};
    RdCheckDataInTSTableOfStreamTable(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time, mapping_min);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// sum函数partition by参数列不存在
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_112)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum(id) OVER(PARTITION BY window_start, window_end, water_mark1) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_UNDEFINE_COLUMN, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// count函数partition by参数列不存在
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_113)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idCount integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "count(id) OVER(PARTITION BY window_start, window_end, water_mark1) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_UNDEFINE_COLUMN, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// max函数partition by参数列不存在
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_114)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idMax integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "max(id) OVER(PARTITION BY window_start, window_end, water_mark1) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_UNDEFINE_COLUMN, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// min函数partition by参数列不存在
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_115)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idMin integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "min(id) OVER(PARTITION BY window_start, window_end, water_mark1) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_UNDEFINE_COLUMN, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 多个相同的over，sum参数不同
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_116)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, water_mark integer, event_time integer, "
            "idSum integer, wmSum integer, etSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "sum(id) OVER(PARTITION BY window_start, window_end, water_mark), "
            "sum(water_mark) OVER(PARTITION BY window_start, window_end, water_mark), "
            "sum(event_time) OVER(PARTITION BY window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "sum_id, sum_water_mark, sum_event_time FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    int64_t id[rowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10};
    int64_t event_time[rowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 20;
    uint32_t expectColsCount = 9;
    int64_t mapping_id[expectRowsCount] = {0, 1, 2, 3, 4, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 5, 6, 7, 8, 9};
    int64_t mapping_window_start[expectRowsCount] = {-5, -5, -5, -5, -5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 15, 15, 15, 15, 15};
    int64_t mapping_water_mark[expectRowsCount] = {1, 1, 3, 3, 5, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 5, 7, 7, 9, 9};
    int64_t mapping_event_time[expectRowsCount] = {0, 1, 2, 3, 4, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 5, 6, 7, 8, 9};
    int64_t mapping_sum_id[expectRowsCount] = {1, 1, 5, 5, 4, 1, 1, 5, 5, 9, 9, 13, 13, 17, 17, 5, 13, 13, 17, 17};
    int64_t mapping_sum_water_mark[expectRowsCount] = {2, 2, 6, 6, 5, 2, 2, 6, 6, 10, 10, 14, 14, 18, 18, 5, 14, 14, 18, 18};
    int64_t mapping_sum_event_time[expectRowsCount] = {1, 1, 5, 5, 4, 1, 1, 5, 5, 9, 9, 13, 13, 17, 17, 5, 13, 13, 17, 17};
    RdCheckDataInTSTableOfStreamTable4(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_water_mark, mapping_event_time,
        mapping_sum_id, mapping_sum_water_mark, mapping_sum_event_time);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 多个相同的over，count参数不同
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_117)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, water_mark integer, event_time integer, "
            "idCount integer, wmCount integer, etCount integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "counT(id) OVER(PARTITION BY window_start, window_end, water_mark), "
            "cOunt(water_mark) OVER(PARTITION BY window_start, window_end, water_mark), "
            "Count(event_time) OVER(PARTITION BY window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "counT_id, cOunt_water_mark, Count_event_time FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    int64_t id[rowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10};
    int64_t event_time[rowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 20;
    uint32_t expectColsCount = 9;
    int64_t mapping_id[expectRowsCount] = {0, 1, 2, 3, 4, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 5, 6, 7, 8, 9};
    int64_t mapping_window_start[expectRowsCount] = {-5, -5, -5, -5, -5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 15, 15, 15, 15, 15};
    int64_t mapping_water_mark[expectRowsCount] = {1, 1, 3, 3, 5, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 5, 7, 7, 9, 9};
    int64_t mapping_event_time[expectRowsCount] = {0, 1, 2, 3, 4, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 5, 6, 7, 8, 9};
    int64_t mapping_count_id[expectRowsCount] = {2, 2, 2, 2, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 1, 2, 2, 2, 2};
    int64_t mapping_count_water_mark[expectRowsCount] = {2, 2, 2, 2, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 1, 2, 2, 2, 2};
    int64_t mapping_count_event_time[expectRowsCount] = {2, 2, 2, 2, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 1, 2, 2, 2, 2};
    RdCheckDataInTSTableOfStreamTable4(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_water_mark, mapping_event_time,
        mapping_count_id, mapping_count_water_mark, mapping_count_event_time);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 多个相同的over，max参数不同
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_118)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, water_mark integer, event_time integer, "
            "idMax integer, wmMax integer, etMax integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "Max(id) OVER(PARTITION BY window_start, window_end, water_mark), "
            "mAx(water_mark) OVER(PARTITION BY window_start, window_end, water_mark), "
            "maX(event_time) OVER(PARTITION BY window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "Max_id, mAx_water_mark, maX_event_time FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    int64_t id[rowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10};
    int64_t event_time[rowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 20;
    uint32_t expectColsCount = 9;
    int64_t mapping_id[expectRowsCount] = {0, 1, 2, 3, 4, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 5, 6, 7, 8, 9};
    int64_t mapping_window_start[expectRowsCount] = {-5, -5, -5, -5, -5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 15, 15, 15, 15, 15};
    int64_t mapping_water_mark[expectRowsCount] = {1, 1, 3, 3, 5, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 5, 7, 7, 9, 9};
    int64_t mapping_event_time[expectRowsCount] = {0, 1, 2, 3, 4, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 5, 6, 7, 8, 9};
    int64_t mapping_max_id[expectRowsCount] = {1, 1, 3, 3, 4, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 5, 7, 7, 9, 9};
    int64_t mapping_max_water_mark[expectRowsCount] = {1, 1, 3, 3, 5, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 5, 7, 7, 9, 9};
    int64_t mapping_max_event_time[expectRowsCount] = {1, 1, 3, 3, 4, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 5, 7, 7, 9, 9};
    RdCheckDataInTSTableOfStreamTable4(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_water_mark, mapping_event_time,
        mapping_max_id, mapping_max_water_mark, mapping_max_event_time);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 多个相同的over，min参数不同
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_119)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, water_mark integer, event_time integer, "
            "idMin integer, wmMin integer, etMin integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "Min(id) OVER(PARTITION BY window_start, window_end, water_mark), "
            "mIn(water_mark) OVER(PARTITION BY window_start, window_end, water_mark), "
            "miN(event_time) OVER(PARTITION BY window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "Min_id, mIn_water_mark, miN_event_time FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    int64_t id[rowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 10};
    int64_t event_time[rowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 20;
    uint32_t expectColsCount = 9;
    int64_t mapping_id[expectRowsCount] = {0, 1, 2, 3, 4, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 5, 6, 7, 8, 9};
    int64_t mapping_window_start[expectRowsCount] = {-5, -5, -5, -5, -5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 15, 15, 15, 15, 15};
    int64_t mapping_water_mark[expectRowsCount] = {1, 1, 3, 3, 5, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 5, 7, 7, 9, 9};
    int64_t mapping_event_time[expectRowsCount] = {0, 1, 2, 3, 4, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 5, 6, 7, 8, 9};
    int64_t mapping_min_id[expectRowsCount] = {0, 0, 2, 2, 4, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 5, 6, 6, 8, 8};
    int64_t mapping_min_water_mark[expectRowsCount] = {1, 1, 3, 3, 5, 1, 1, 3, 3, 5, 5, 7, 7, 9, 9, 5, 7, 7, 9, 9};
    int64_t mapping_min_event_time[expectRowsCount] = {0, 0, 2, 2, 4, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 5, 6, 6, 8, 8};
    RdCheckDataInTSTableOfStreamTable4(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_water_mark, mapping_event_time,
        mapping_min_id, mapping_min_water_mark, mapping_min_event_time);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 多个相同的over，sum参数相同
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_120)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, water_mark integer, event_time integer, "
            "idSum1 integer, idSum2 integer, idSum3 integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "sum(id) OVER(PARTITION BY window_start, window_end, water_mark), "
            "sum(id) OVER(PARTITION BY window_start, window_end, water_mark), "
            "sum(id) OVER(PARTITION BY window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SEMANTIC_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 多个相同的over，count参数相同
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_121)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, water_mark integer, event_time integer, "
            "idCount1 integer, idCount2 integer, idCount3 integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "Count(id) OVER(PARTITION BY window_start, window_end, water_mark), "
            "Count(id) OVER(PARTITION BY window_start, window_end, water_mark), "
            "Count(id) OVER(PARTITION BY window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SEMANTIC_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}


// 多个相同的over，max参数相同
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_122)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, water_mark integer, event_time integer, "
            "idMax1 integer, idMax2 integer, idMax3 integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "Max(id) OVER(PARTITION BY window_start, window_end, water_mark), "
            "Max(id) OVER(PARTITION BY window_start, window_end, water_mark), "
            "Max(id) OVER(PARTITION BY window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SEMANTIC_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 多个相同的over，min参数相同
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_123)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, water_mark integer, event_time integer, "
            "idMin1 integer, idMin2 integer, idMin3 integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "Min(id) OVER(PARTITION BY window_start, window_end, water_mark), "
            "Min(id) OVER(PARTITION BY window_start, window_end, water_mark), "
            "Min(id) OVER(PARTITION BY window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SEMANTIC_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 所有聚合函数一起执行[STRICT]，滚窗，partition by 所有参数相同，顺序改变，window_start和window_end在中间
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_124)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "idSum integer, idCount integer, idMax integer, idMin integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "sum(id) OVER(PARTITION BY water_mark, window_start, window_end, event_time), "
            "count(id) OVER(PARTITION BY event_time, window_start, window_end, water_mark), "
            "max(id) OVER(PARTITION BY water_mark, window_start, window_end, event_time), "
            "min(id) OVER(PARTITION BY event_time, window_start, window_end, water_mark) "
            "FROM TABLE(TUMBLE(TABLE stream1, event_time, INTERVAL '5' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum_id, count_id, max_id, min_id FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 9;
    int64_t id[rowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 10};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 10};
    int64_t event_time[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 6;
    uint32_t expectColsCount = 9;
    int64_t mapping_id[expectRowsCount] = {0, 1, 2, 3, 4, 5};
    int64_t mapping_window_start[expectRowsCount] = {0, 0, 0, 0, 5, 5};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 5, 10, 10};
    int64_t mapping_event_time[expectRowsCount] = {1, 1, 3, 3, 7, 7};
    int64_t mapping_sum_id[expectRowsCount] = {1, 1, 5, 5, 9, 9};
    int64_t mapping_count_id[expectRowsCount] = {2, 2, 2, 2, 2, 2};
    int64_t mapping_max_id[expectRowsCount] = {1, 1, 3, 3, 5, 5};
    int64_t mapping_min_id[expectRowsCount] = {0, 0, 2, 2, 4, 4};
    RdCheckDataInTSTableOfStreamTable2(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_sum_id, mapping_count_id, mapping_max_id, mapping_min_id);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 所有聚合函数一起执行[TOLERANT]，滚窗，partition by 所有参数相同，顺序改变，window_start和window_end在中间
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_125)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "idSum integer, idCount integer, idMax integer, idMin integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS TOLERANT);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "sum(id) OVER(PARTITION BY water_mark, window_start, window_end, event_time), "
            "count(id) OVER(PARTITION BY event_time, window_start, window_end, water_mark), "
            "max(id) OVER(PARTITION BY water_mark, window_start, window_end, event_time), "
            "min(id) OVER(PARTITION BY event_time, window_start, window_end, water_mark) "
            "FROM TABLE(TUMBLE(TABLE stream1, event_time, INTERVAL '5' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum_id, count_id, max_id, min_id FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 9;
    int64_t id[rowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 10};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 10};
    int64_t event_time[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 8;
    uint32_t expectColsCount = 9;
    int64_t mapping_id[expectRowsCount] = {0, 1, 2, 3, 6, 7, 4, 5};
    int64_t mapping_window_start[expectRowsCount] = {0, 0, 0, 0, 0, 0, 5, 5};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 5, 5, 5, 10, 10};
    int64_t mapping_event_time[expectRowsCount] = {1, 1, 3, 3, 4, 4, 7, 7};
    int64_t mapping_sum_id[expectRowsCount] = {1, 1, 5, 5, 13, 13, 9, 9};
    int64_t mapping_count_id[expectRowsCount] = {2, 2, 2, 2, 2, 2, 2, 2};
    int64_t mapping_max_id[expectRowsCount] = {1, 1, 3, 3, 7, 7, 5, 5};
    int64_t mapping_min_id[expectRowsCount] = {0, 0, 2, 2, 6, 6, 4, 4};
    RdCheckDataInTSTableOfStreamTable2(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_sum_id, mapping_count_id, mapping_max_id, mapping_min_id);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 所有聚合函数一起执行[STRICT]，滑窗，partition by 所有参数相同，顺序改变，window_start和window_end在中间
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_126)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "idSum integer, idCount integer, idMax integer, idMin integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "sum(id) OVER(PARTITION BY water_mark, window_start, window_end, event_time), "
            "count(id) OVER(PARTITION BY event_time, window_start, window_end, water_mark), "
            "max(id) OVER(PARTITION BY water_mark, window_start, window_end, event_time), "
            "min(id) OVER(PARTITION BY event_time, window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum_id, count_id, max_id, min_id FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    int64_t id[rowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 10};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 10};
    int64_t event_time[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 14;
    uint32_t expectColsCount = 9;
    int64_t mapping_id[expectRowsCount] = {0, 1, 2, 3, 0, 1, 2, 3, 4, 5, 6, 7, 4, 5};
    int64_t mapping_window_start[expectRowsCount] = {-5, -5, -5, -5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 15, 15};
    int64_t mapping_event_time[expectRowsCount] = {1, 1, 3, 3, 1, 1, 3, 3, 7, 7, 4, 4, 7, 7};
    int64_t mapping_sum_id[expectRowsCount] = {1, 1, 5, 5, 1, 1, 5, 5, 9, 9, 13, 13, 9, 9};
    int64_t mapping_count_id[expectRowsCount] = {2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2};
    int64_t mapping_max_id[expectRowsCount] = {1, 1, 3, 3, 1, 1, 3, 3, 5, 5, 7, 7, 5, 5};
    int64_t mapping_min_id[expectRowsCount] = {0, 0, 2, 2, 0, 0, 2, 2, 4, 4, 6, 6, 4, 4};
    RdCheckDataInTSTableOfStreamTable2(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_sum_id, mapping_count_id, mapping_max_id, mapping_min_id);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 所有聚合函数一起执行[TOLERANT]，滑窗，partition by 所有参数相同，顺序改变，window_start和window_end在中间
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_127)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "idSum integer, idCount integer, idMax integer, idMin integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS TOLERANT);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "sum(id) OVER(PARTITION BY water_mark, window_start, window_end, event_time), "
            "count(id) OVER(PARTITION BY event_time, window_start, window_end, water_mark), "
            "max(id) OVER(PARTITION BY water_mark, window_start, window_end, event_time), "
            "min(id) OVER(PARTITION BY event_time, window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum_id, count_id, max_id, min_id FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    int64_t id[rowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 10};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 10};
    int64_t event_time[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 16;
    uint32_t expectColsCount = 9;
    int64_t mapping_id[expectRowsCount] = {0, 1, 2, 3, 6, 7, 0, 1, 2, 3, 4, 5, 6, 7, 4, 5};
    int64_t mapping_window_start[expectRowsCount] = {-5, -5, -5, -5, -5, -5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 15, 15};
    int64_t mapping_event_time[expectRowsCount] = {1, 1, 3, 3, 4, 4, 1, 1, 3, 3, 7, 7, 4, 4, 7, 7};
    int64_t mapping_sum_id[expectRowsCount] = {1, 1, 5, 5, 13, 13, 1, 1, 5, 5, 9, 9, 13, 13, 9, 9};
    int64_t mapping_count_id[expectRowsCount] = {2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2};
    int64_t mapping_max_id[expectRowsCount] = {1, 1, 3, 3, 7, 7, 1, 1, 3, 3, 5, 5, 7, 7, 5, 5};
    int64_t mapping_min_id[expectRowsCount] = {0, 0, 2, 2, 6, 6, 0, 0, 2, 2, 4, 4, 6, 6, 4, 4};
    RdCheckDataInTSTableOfStreamTable2(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_sum_id, mapping_count_id, mapping_max_id, mapping_min_id);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 所有聚合函数一起执行[STRICT]，滚窗，partition by 所有参数相同，顺序改变，window_start和window_end在最后
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_128)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "idSum integer, idCount integer, idMax integer, idMin integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "sum(id) OVER(PARTITION BY water_mark, event_time, window_start, window_end), "
            "count(id) OVER(PARTITION BY event_time, water_mark, window_start, window_end), "
            "max(id) OVER(PARTITION BY water_mark, event_time, window_start, window_end), "
            "min(id) OVER(PARTITION BY event_time, water_mark, window_start, window_end) "
            "FROM TABLE(TUMBLE(TABLE stream1, event_time, INTERVAL '5' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum_id, count_id, max_id, min_id FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 9;
    int64_t id[rowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 10};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 10};
    int64_t event_time[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 6;
    uint32_t expectColsCount = 9;
    int64_t mapping_id[expectRowsCount] = {0, 1, 2, 3, 4, 5};
    int64_t mapping_window_start[expectRowsCount] = {0, 0, 0, 0, 5, 5};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 5, 10, 10};
    int64_t mapping_event_time[expectRowsCount] = {1, 1, 3, 3, 7, 7};
    int64_t mapping_sum_id[expectRowsCount] = {1, 1, 5, 5, 9, 9};
    int64_t mapping_count_id[expectRowsCount] = {2, 2, 2, 2, 2, 2};
    int64_t mapping_max_id[expectRowsCount] = {1, 1, 3, 3, 5, 5};
    int64_t mapping_min_id[expectRowsCount] = {0, 0, 2, 2, 4, 4};
    RdCheckDataInTSTableOfStreamTable2(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_sum_id, mapping_count_id, mapping_max_id, mapping_min_id);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 所有聚合函数一起执行[TOLERANT]，滚窗，partition by 所有参数相同，顺序改变，window_start和window_end在最后
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_129)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "idSum integer, idCount integer, idMax integer, idMin integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS TOLERANT);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "sum(id) OVER(PARTITION BY water_mark, event_time, window_start, window_end), "
            "count(id) OVER(PARTITION BY event_time, water_mark, window_start, window_end), "
            "max(id) OVER(PARTITION BY water_mark, event_time, window_start, window_end), "
            "min(id) OVER(PARTITION BY event_time, water_mark, window_start, window_end) "
            "FROM TABLE(TUMBLE(TABLE stream1, event_time, INTERVAL '5' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum_id, count_id, max_id, min_id FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 9;
    int64_t id[rowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 10};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 10};
    int64_t event_time[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 8;
    uint32_t expectColsCount = 9;
    int64_t mapping_id[expectRowsCount] = {0, 1, 2, 3, 6, 7, 4, 5};
    int64_t mapping_window_start[expectRowsCount] = {0, 0, 0, 0, 0, 0, 5, 5};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 5, 5, 5, 10, 10};
    int64_t mapping_event_time[expectRowsCount] = {1, 1, 3, 3, 4, 4, 7, 7};
    int64_t mapping_sum_id[expectRowsCount] = {1, 1, 5, 5, 13, 13, 9, 9};
    int64_t mapping_count_id[expectRowsCount] = {2, 2, 2, 2, 2, 2, 2, 2};
    int64_t mapping_max_id[expectRowsCount] = {1, 1, 3, 3, 7, 7, 5, 5};
    int64_t mapping_min_id[expectRowsCount] = {0, 0, 2, 2, 6, 6, 4, 4};
    RdCheckDataInTSTableOfStreamTable2(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_sum_id, mapping_count_id, mapping_max_id, mapping_min_id);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 所有聚合函数一起执行[STRICT]，滑窗，partition by 所有参数相同，顺序改变，window_start和window_end在最后
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_130)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "idSum integer, idCount integer, idMax integer, idMin integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "sum(id) OVER(PARTITION BY water_mark, event_time, window_start, window_end), "
            "count(id) OVER(PARTITION BY event_time, water_mark, window_start, window_end), "
            "max(id) OVER(PARTITION BY water_mark, event_time, window_start, window_end), "
            "min(id) OVER(PARTITION BY event_time, water_mark, window_start, window_end) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum_id, count_id, max_id, min_id FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    int64_t id[rowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 10};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 10};
    int64_t event_time[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 14;
    uint32_t expectColsCount = 9;
    int64_t mapping_id[expectRowsCount] = {0, 1, 2, 3, 0, 1, 2, 3, 4, 5, 6, 7, 4, 5};
    int64_t mapping_window_start[expectRowsCount] = {-5, -5, -5, -5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 15, 15};
    int64_t mapping_event_time[expectRowsCount] = {1, 1, 3, 3, 1, 1, 3, 3, 7, 7, 4, 4, 7, 7};
    int64_t mapping_sum_id[expectRowsCount] = {1, 1, 5, 5, 1, 1, 5, 5, 9, 9, 13, 13, 9, 9};
    int64_t mapping_count_id[expectRowsCount] = {2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2};
    int64_t mapping_max_id[expectRowsCount] = {1, 1, 3, 3, 1, 1, 3, 3, 5, 5, 7, 7, 5, 5};
    int64_t mapping_min_id[expectRowsCount] = {0, 0, 2, 2, 0, 0, 2, 2, 4, 4, 6, 6, 4, 4};
    RdCheckDataInTSTableOfStreamTable2(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_sum_id, mapping_count_id, mapping_max_id, mapping_min_id);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 所有聚合函数一起执行[TOLERANT]，滑窗，partition by 所有参数相同，顺序改变，window_start和window_end在最后
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_131)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "idSum integer, idCount integer, idMax integer, idMin integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS TOLERANT);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "sum(id) OVER(PARTITION BY water_mark, event_time, window_start, window_end), "
            "count(id) OVER(PARTITION BY event_time, water_mark, window_start, window_end), "
            "max(id) OVER(PARTITION BY water_mark, event_time, window_start, window_end), "
            "min(id) OVER(PARTITION BY event_time, water_mark, window_start, window_end) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum_id, count_id, max_id, min_id FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    int64_t id[rowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 10};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 10};
    int64_t event_time[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 16;
    uint32_t expectColsCount = 9;
    int64_t mapping_id[expectRowsCount] = {0, 1, 2, 3, 6, 7, 0, 1, 2, 3, 4, 5, 6, 7, 4, 5};
    int64_t mapping_window_start[expectRowsCount] = {-5, -5, -5, -5, -5, -5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 15, 15};
    int64_t mapping_event_time[expectRowsCount] = {1, 1, 3, 3, 4, 4, 1, 1, 3, 3, 7, 7, 4, 4, 7, 7};
    int64_t mapping_sum_id[expectRowsCount] = {1, 1, 5, 5, 13, 13, 1, 1, 5, 5, 9, 9, 13, 13, 9, 9};
    int64_t mapping_count_id[expectRowsCount] = {2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2};
    int64_t mapping_max_id[expectRowsCount] = {1, 1, 3, 3, 7, 7, 1, 1, 3, 3, 5, 5, 7, 7, 5, 5};
    int64_t mapping_min_id[expectRowsCount] = {0, 0, 2, 2, 6, 6, 0, 0, 2, 2, 4, 4, 6, 6, 4, 4};
    RdCheckDataInTSTableOfStreamTable2(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_sum_id, mapping_count_id, mapping_max_id, mapping_min_id);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 所有聚合函数一起执行[STRICT]，滚窗，partition by 所有参数相同，顺序改变，window_start和window_end在开头和最后
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_132)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "idSum integer, idCount integer, idMax integer, idMin integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "sum(id) OVER(PARTITION BY window_start, water_mark, event_time, window_end), "
            "count(id) OVER(PARTITION BY window_start, event_time, water_mark, window_end), "
            "max(id) OVER(PARTITION BY window_start, water_mark, event_time, window_end), "
            "min(id) OVER(PARTITION BY window_start, event_time, water_mark, window_end) "
            "FROM TABLE(TUMBLE(TABLE stream1, event_time, INTERVAL '5' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum_id, count_id, max_id, min_id FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 9;
    int64_t id[rowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 10};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 10};
    int64_t event_time[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 6;
    uint32_t expectColsCount = 9;
    int64_t mapping_id[expectRowsCount] = {0, 1, 2, 3, 4, 5};
    int64_t mapping_window_start[expectRowsCount] = {0, 0, 0, 0, 5, 5};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 5, 10, 10};
    int64_t mapping_event_time[expectRowsCount] = {1, 1, 3, 3, 7, 7};
    int64_t mapping_sum_id[expectRowsCount] = {1, 1, 5, 5, 9, 9};
    int64_t mapping_count_id[expectRowsCount] = {2, 2, 2, 2, 2, 2};
    int64_t mapping_max_id[expectRowsCount] = {1, 1, 3, 3, 5, 5};
    int64_t mapping_min_id[expectRowsCount] = {0, 0, 2, 2, 4, 4};
    RdCheckDataInTSTableOfStreamTable2(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_sum_id, mapping_count_id, mapping_max_id, mapping_min_id);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 所有聚合函数一起执行[TOLERANT]，滚窗，partition by 所有参数相同，顺序改变，window_start和window_end在开头和最后
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_133)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "idSum integer, idCount integer, idMax integer, idMin integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS TOLERANT);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "sum(id) OVER(PARTITION BY window_start, water_mark, event_time, window_end), "
            "count(id) OVER(PARTITION BY window_start, event_time, water_mark, window_end), "
            "max(id) OVER(PARTITION BY window_start, water_mark, event_time, window_end), "
            "min(id) OVER(PARTITION BY window_start, event_time, water_mark, window_end) "
            "FROM TABLE(TUMBLE(TABLE stream1, event_time, INTERVAL '5' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum_id, count_id, max_id, min_id FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 9;
    int64_t id[rowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 10};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 10};
    int64_t event_time[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 8;
    uint32_t expectColsCount = 9;
    int64_t mapping_id[expectRowsCount] = {0, 1, 2, 3, 6, 7, 4, 5};
    int64_t mapping_window_start[expectRowsCount] = {0, 0, 0, 0, 0, 0, 5, 5};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 5, 5, 5, 10, 10};
    int64_t mapping_event_time[expectRowsCount] = {1, 1, 3, 3, 4, 4, 7, 7};
    int64_t mapping_sum_id[expectRowsCount] = {1, 1, 5, 5, 13, 13, 9, 9};
    int64_t mapping_count_id[expectRowsCount] = {2, 2, 2, 2, 2, 2, 2, 2};
    int64_t mapping_max_id[expectRowsCount] = {1, 1, 3, 3, 7, 7, 5, 5};
    int64_t mapping_min_id[expectRowsCount] = {0, 0, 2, 2, 6, 6, 4, 4};
    RdCheckDataInTSTableOfStreamTable2(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_sum_id, mapping_count_id, mapping_max_id, mapping_min_id);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 所有聚合函数一起执行[STRICT]，滑窗，partition by 所有参数相同，顺序改变，window_start和window_end在开头和最后
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_134)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "idSum integer, idCount integer, idMax integer, idMin integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "sum(id) OVER(PARTITION BY window_start, water_mark, event_time, window_end), "
            "count(id) OVER(PARTITION BY window_start, event_time, water_mark, window_end), "
            "max(id) OVER(PARTITION BY window_start, water_mark, event_time, window_end), "
            "min(id) OVER(PARTITION BY window_start, event_time, water_mark, window_end) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum_id, count_id, max_id, min_id FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    int64_t id[rowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 10};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 10};
    int64_t event_time[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 14;
    uint32_t expectColsCount = 9;
    int64_t mapping_id[expectRowsCount] = {0, 1, 2, 3, 0, 1, 2, 3, 4, 5, 6, 7, 4, 5};
    int64_t mapping_window_start[expectRowsCount] = {-5, -5, -5, -5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 15, 15};
    int64_t mapping_event_time[expectRowsCount] = {1, 1, 3, 3, 1, 1, 3, 3, 7, 7, 4, 4, 7, 7};
    int64_t mapping_sum_id[expectRowsCount] = {1, 1, 5, 5, 1, 1, 5, 5, 9, 9, 13, 13, 9, 9};
    int64_t mapping_count_id[expectRowsCount] = {2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2};
    int64_t mapping_max_id[expectRowsCount] = {1, 1, 3, 3, 1, 1, 3, 3, 5, 5, 7, 7, 5, 5};
    int64_t mapping_min_id[expectRowsCount] = {0, 0, 2, 2, 0, 0, 2, 2, 4, 4, 6, 6, 4, 4};
    RdCheckDataInTSTableOfStreamTable2(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_sum_id, mapping_count_id, mapping_max_id, mapping_min_id);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 所有聚合函数一起执行[TOLERANT]，滑窗，partition by 所有参数相同，顺序改变，window_start和window_end在开头和最后
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_135)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "idSum integer, idCount integer, idMax integer, idMin integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS TOLERANT);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "sum(id) OVER(PARTITION BY window_start, water_mark, event_time, window_end), "
            "count(id) OVER(PARTITION BY window_start, event_time, water_mark, window_end), "
            "max(id) OVER(PARTITION BY window_start, water_mark, event_time, window_end), "
            "min(id) OVER(PARTITION BY window_start, event_time, water_mark, window_end) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum_id, count_id, max_id, min_id FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    int64_t id[rowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 10};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 10};
    int64_t event_time[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 16;
    uint32_t expectColsCount = 9;
    int64_t mapping_id[expectRowsCount] = {0, 1, 2, 3, 6, 7, 0, 1, 2, 3, 4, 5, 6, 7, 4, 5};
    int64_t mapping_window_start[expectRowsCount] = {-5, -5, -5, -5, -5, -5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 15, 15};
    int64_t mapping_event_time[expectRowsCount] = {1, 1, 3, 3, 4, 4, 1, 1, 3, 3, 7, 7, 4, 4, 7, 7};
    int64_t mapping_sum_id[expectRowsCount] = {1, 1, 5, 5, 13, 13, 1, 1, 5, 5, 9, 9, 13, 13, 9, 9};
    int64_t mapping_count_id[expectRowsCount] = {2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2};
    int64_t mapping_max_id[expectRowsCount] = {1, 1, 3, 3, 7, 7, 1, 1, 3, 3, 5, 5, 7, 7, 5, 5};
    int64_t mapping_min_id[expectRowsCount] = {0, 0, 2, 2, 6, 6, 0, 0, 2, 2, 4, 4, 6, 6, 4, 4};
    RdCheckDataInTSTableOfStreamTable2(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_sum_id, mapping_count_id, mapping_max_id, mapping_min_id);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 所有函数一起执行，partition by window_start和window_end缺失，其他参数相同
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_136)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "sum(id) OVER(PARTITION BY water_mark, event_time), "
            "count(id) OVER(PARTITION BY water_mark, event_time), "
            "max(id) OVER(PARTITION BY water_mark, event_time), "
            "min(id) OVER(PARTITION BY water_mark, event_time) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SEMANTIC_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 所有函数一起执行，partition by 参数不同
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_137)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "sum(id) OVER(PARTITION BY window_start, window_end,event_time), "
            "count(id) OVER(PARTITION BY window_start, window_end, event_time), "
            "max(id) OVER(PARTITION BY window_start, window_end, water_mark), "
            "min(id) OVER(PARTITION BY window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SEMANTIC_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 聚合函数参数使用window_start,[INTERVAL '2' SECONDS STRICT]，滚窗
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_138)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "window_start_Sum integer, window_start_Count integer, window_start_Max integer, window_start_Min integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "sum(window_start) OVER(PARTITION BY water_mark, window_start, window_end, event_time), "
            "count(window_start) OVER(PARTITION BY event_time, window_start, window_end, water_mark), "
            "max(window_start) OVER(PARTITION BY water_mark, window_start, window_end, event_time), "
            "min(window_start) OVER(PARTITION BY event_time, window_start, window_end, water_mark) "
            "FROM TABLE(TUMBLE(TABLE stream1, event_time, INTERVAL '5' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum_window_start, count_window_start, max_window_start, min_window_start FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 9;
    int64_t id[rowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 10};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 10};
    int64_t event_time[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 6;
    uint32_t expectColsCount = 9;
    int64_t mapping_id[expectRowsCount] = {0, 1, 2, 3, 4, 5};
    int64_t mapping_window_start[expectRowsCount] = {0, 0, 0, 0, 5, 5};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 5, 10, 10};
    int64_t mapping_event_time[expectRowsCount] = {1, 1, 3, 3, 7, 7};
    int64_t mapping_sum_window_start[expectRowsCount] = {0, 0, 0, 0, 10, 10};
    int64_t mapping_count_window_start[expectRowsCount] = {2, 2, 2, 2, 2, 2};
    int64_t mapping_max_window_start[expectRowsCount] = {0, 0, 0, 0, 5, 5};
    int64_t mapping_min_window_start[expectRowsCount] = {0, 0, 0, 0, 5, 5};
    RdCheckDataInTSTableOfStreamTable2(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_sum_window_start, mapping_count_window_start, mapping_max_window_start, mapping_min_window_start);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 聚合函数参数使用window_start,[INTERVAL '2' SECONDS TOLERANT]，滚窗
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_139)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "window_start_Sum integer, window_start_Count integer, window_start_Max integer, window_start_Min integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS Tolerant);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "sum(window_start) OVER(PARTITION BY water_mark, window_start, window_end, event_time), "
            "count(window_start) OVER(PARTITION BY event_time, window_start, window_end, water_mark), "
            "max(window_start) OVER(PARTITION BY water_mark, window_start, window_end, event_time), "
            "min(window_start) OVER(PARTITION BY event_time, window_start, window_end, water_mark) "
            "FROM TABLE(TUMBLE(TABLE stream1, event_time, INTERVAL '5' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum_window_start, count_window_start, max_window_start, min_window_start FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 9;
    int64_t id[rowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 10};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 10};
    int64_t event_time[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 8;
    uint32_t expectColsCount = 9;
    int64_t mapping_id[expectRowsCount] = {0, 1, 2, 3, 6, 7, 4, 5};
    int64_t mapping_window_start[expectRowsCount] = {0, 0, 0, 0, 0, 0, 5, 5};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 5, 5, 5, 10, 10};
    int64_t mapping_event_time[expectRowsCount] = {1, 1, 3, 3, 4, 4, 7, 7};
    int64_t mapping_sum_window_start[expectRowsCount] = {0, 0, 0, 0, 0, 0, 10, 10};
    int64_t mapping_count_window_start[expectRowsCount] = {2, 2, 2, 2, 2, 2, 2, 2};
    int64_t mapping_max_window_start[expectRowsCount] = {0, 0, 0, 0, 0, 0, 5, 5};
    int64_t mapping_min_window_start[expectRowsCount] = {0, 0, 0, 0, 0, 0, 5, 5};
    RdCheckDataInTSTableOfStreamTable2(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_sum_window_start, mapping_count_window_start, mapping_max_window_start, mapping_min_window_start);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 聚合函数参数使用window_end,[INTERVAL '2' SECONDS STRICT]，滚窗
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_140)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "window_end_Sum integer, window_end_Count integer, window_end_Max integer, window_end_Min integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "sum(window_end) OVER(PARTITION BY water_mark, window_start, window_end, event_time), "
            "count(window_end) OVER(PARTITION BY event_time, window_start, window_end, water_mark), "
            "max(window_end) OVER(PARTITION BY water_mark, window_start, window_end, event_time), "
            "min(window_end) OVER(PARTITION BY event_time, window_start, window_end, water_mark) "
            "FROM TABLE(TUMBLE(TABLE stream1, event_time, INTERVAL '5' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum_window_end, count_window_end, max_window_end, min_window_end FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 9;
    int64_t id[rowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 10};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 10};
    int64_t event_time[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 6;
    uint32_t expectColsCount = 9;
    int64_t mapping_id[expectRowsCount] = {0, 1, 2, 3, 4, 5};
    int64_t mapping_window_start[expectRowsCount] = {0, 0, 0, 0, 5, 5};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 5, 10, 10};
    int64_t mapping_event_time[expectRowsCount] = {1, 1, 3, 3, 7, 7};
    int64_t mapping_sum_window_end[expectRowsCount] = {10, 10, 10, 10, 20, 20};
    int64_t mapping_count_window_end[expectRowsCount] = {2, 2, 2, 2, 2, 2};
    int64_t mapping_max_window_end[expectRowsCount] = {5, 5, 5, 5, 10, 10};
    int64_t mapping_min_window_end[expectRowsCount] = {5, 5, 5, 5, 10, 10};
    RdCheckDataInTSTableOfStreamTable2(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_sum_window_end, mapping_count_window_end, mapping_max_window_end, mapping_min_window_end);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 聚合函数参数使用window_end,[INTERVAL '2' SECONDS TOLERANT]，滚窗
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_141)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "window_end_Sum integer, window_end_Count integer, window_end_Max integer, window_end_Min integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS toLERANT);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "sum(window_end) OVER(PARTITION BY water_mark, window_start, window_end, event_time), "
            "count(window_end) OVER(PARTITION BY event_time, window_start, window_end, water_mark), "
            "max(window_end) OVER(PARTITION BY water_mark, window_start, window_end, event_time), "
            "min(window_end) OVER(PARTITION BY event_time, window_start, window_end, water_mark) "
            "FROM TABLE(TUMBLE(TABLE stream1, event_time, INTERVAL '5' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum_window_end, count_window_end, max_window_end, min_window_end FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 9;
    int64_t id[rowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 10};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 10};
    int64_t event_time[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 8;
    uint32_t expectColsCount = 9;
    int64_t mapping_id[expectRowsCount] = {0, 1, 2, 3, 6, 7, 4, 5};
    int64_t mapping_window_start[expectRowsCount] = {0, 0, 0, 0, 0, 0, 5, 5};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 5, 5, 5, 10, 10};
    int64_t mapping_event_time[expectRowsCount] = {1, 1, 3, 3, 4, 4, 7, 7};
    int64_t mapping_sum_window_end[expectRowsCount] = {10, 10, 10, 10, 10, 10, 20, 20};
    int64_t mapping_count_window_end[expectRowsCount] = {2, 2, 2, 2, 2, 2, 2, 2};
    int64_t mapping_max_window_end[expectRowsCount] = {5, 5, 5, 5, 5, 5, 10, 10};
    int64_t mapping_min_window_end[expectRowsCount] = {5, 5, 5, 5, 5, 5, 10, 10};
    RdCheckDataInTSTableOfStreamTable2(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_sum_window_end, mapping_count_window_end, mapping_max_window_end, mapping_min_window_end);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 聚合函数参数使用window_start,[INTERVAL '2' SECONDS STRICT]，滑窗
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_142)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "window_start_Sum integer, window_start_Count integer, window_start_Max integer, window_start_Min integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "sum(window_start) OVER(PARTITION BY water_mark, window_start, window_end, event_time), "
            "count(window_start) OVER(PARTITION BY event_time, window_start, window_end, water_mark), "
            "max(window_start) OVER(PARTITION BY water_mark, window_start, window_end, event_time), "
            "min(window_start) OVER(PARTITION BY event_time, window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum_window_start, count_window_start, max_window_start, min_window_start FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 9;
    int64_t id[rowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 10};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 10};
    int64_t event_time[rowNum] = {1, 1, 3, 3, 12, 12, 5, 5, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 14;
    uint32_t expectColsCount = 9;
    int64_t mapping_id[expectRowsCount] = {0, 1, 2, 3, 0, 1, 2, 3, 4, 5, 6, 7, 4, 5};
    int64_t mapping_window_start[expectRowsCount] = {-5, -5, -5, -5, 0, 0, 0, 0, 5, 5, 5, 5, 10, 10};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 5, 10, 10, 10, 10, 15, 15, 15, 15, 20, 20};
    int64_t mapping_event_time[expectRowsCount] = {1, 1, 3, 3, 1, 1, 3, 3, 12, 12, 5, 5, 12, 12};
    int64_t mapping_sum_window_start[expectRowsCount] = {-10, -10, -10, -10, 0, 0, 0, 0, 10, 10, 10, 10, 20, 20};
    int64_t mapping_count_window_start[expectRowsCount] = {2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2};
    int64_t mapping_max_window_start[expectRowsCount] = {-5, -5, -5, -5, 0, 0, 0, 0, 5, 5, 5, 5, 10, 10};
    int64_t mapping_min_window_start[expectRowsCount] = {-5, -5, -5, -5, 0, 0, 0, 0, 5, 5, 5, 5, 10, 10};
    RdCheckDataInTSTableOfStreamTable2(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_sum_window_start, mapping_count_window_start, mapping_max_window_start, mapping_min_window_start);/**/

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 聚合函数参数使用window_start,[INTERVAL '2' SECONDS TOLERANT]，滑窗
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_143)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "window_start_Sum integer, window_start_Count integer, window_start_Max integer, window_start_Min integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS Tolerant);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "sum(window_start) OVER(PARTITION BY water_mark, window_start, window_end, event_time), "
            "count(window_start) OVER(PARTITION BY event_time, window_start, window_end, water_mark), "
            "max(window_start) OVER(PARTITION BY water_mark, window_start, window_end, event_time), "
            "min(window_start) OVER(PARTITION BY event_time, window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum_window_start, count_window_start, max_window_start, min_window_start FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 9;
    int64_t id[rowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 10};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 10};
    int64_t event_time[rowNum] = {1, 1, 3, 3, 12, 12, 5, 5, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 16;
    uint32_t expectColsCount = 9;
    int64_t mapping_id[expectRowsCount] = {0, 1, 2, 3, 0, 1, 2, 3, 6, 7, 4, 5, 6, 7, 4, 5};
    int64_t mapping_window_start[expectRowsCount] = {-5, -5, -5, -5, 0, 0, 0, 0 , 0, 0, 5, 5, 5, 5, 10, 10};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 15, 15, 15, 15, 20, 20};
    int64_t mapping_event_time[expectRowsCount] = {1, 1, 3, 3, 1, 1, 3, 3, 5, 5, 12, 12, 5, 5, 12, 12};
    int64_t mapping_sum_window_start[expectRowsCount] = {-10, -10, -10, -10, 0, 0, 0, 0, 0, 0, 10, 10, 10, 10, 20, 20};
    int64_t mapping_count_window_start[expectRowsCount] = {2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2};
    int64_t mapping_max_window_start[expectRowsCount] = {-5, -5, -5, -5, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 10, 10};
    int64_t mapping_min_window_start[expectRowsCount] = {-5, -5, -5, -5, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 10, 10};
    RdCheckDataInTSTableOfStreamTable2(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_sum_window_start, mapping_count_window_start, mapping_max_window_start, mapping_min_window_start);/**/

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 聚合函数参数使用window_end,[INTERVAL '2' SECONDS INTERVAL STRICT]，滑窗
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_144)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "window_end_Sum integer, window_end_Count integer, window_end_Max integer, window_end_Min integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS STRICT);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "sum(window_end) OVER(PARTITION BY water_mark, window_start, window_end, event_time), "
            "count(window_end) OVER(PARTITION BY event_time, window_start, window_end, water_mark), "
            "max(window_end) OVER(PARTITION BY water_mark, window_start, window_end, event_time), "
            "min(window_end) OVER(PARTITION BY event_time, window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum_window_end, count_window_end, max_window_end, min_window_end FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 9;
    int64_t id[rowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 10};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 10};
    int64_t event_time[rowNum] = {1, 1, 3, 3, 12, 12, 5, 5, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 14;
    uint32_t expectColsCount = 9;
    int64_t mapping_id[expectRowsCount] = {0, 1, 2, 3, 0, 1, 2, 3, 4, 5, 6, 7, 4, 5};
    int64_t mapping_window_start[expectRowsCount] = {-5, -5, -5, -5, 0, 0, 0, 0, 5, 5, 5, 5, 10, 10};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 5, 10, 10, 10, 10, 15, 15, 15, 15, 20, 20};
    int64_t mapping_event_time[expectRowsCount] = {1, 1, 3, 3, 1, 1, 3, 3, 12, 12, 5, 5, 12, 12};
    int64_t mapping_sum_window_end[expectRowsCount] = {10, 10, 10, 10, 20, 20, 20, 20, 30, 30, 30, 30, 40, 40};
    int64_t mapping_count_window_end[expectRowsCount] = {2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2};
    int64_t mapping_max_window_end[expectRowsCount] = {5, 5, 5, 5, 10, 10, 10, 10, 15, 15, 15, 15, 20, 20};
    int64_t mapping_min_window_end[expectRowsCount] = {5, 5, 5, 5, 10, 10, 10, 10, 15, 15, 15, 15, 20, 20};
    RdCheckDataInTSTableOfStreamTable2(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_sum_window_end, mapping_count_window_end, mapping_max_window_end, mapping_min_window_end);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 聚合函数参数使用window_end,[INTERVAL '2' SECONDS INTERVAL TOLERANT]，滑窗
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_145)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "window_end_Sum integer, window_end_Count integer, window_end_Max integer, window_end_Min integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS Tolerant);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "sum(window_end) OVER(PARTITION BY water_mark, window_start, window_end, event_time), "
            "count(window_end) OVER(PARTITION BY event_time, window_start, window_end, water_mark), "
            "max(window_end) OVER(PARTITION BY water_mark, window_start, window_end, event_time), "
            "min(window_end) OVER(PARTITION BY event_time, window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum_window_end, count_window_end, max_window_end, min_window_end FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 9;
    int64_t id[rowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 10};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 10};
    int64_t event_time[rowNum] = {1, 1, 3, 3, 12, 12, 5, 5, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 16;
    uint32_t expectColsCount = 9;
    int64_t mapping_id[expectRowsCount] = {0, 1, 2, 3, 0, 1, 2, 3, 6, 7, 4, 5, 6, 7, 4, 5};
    int64_t mapping_window_start[expectRowsCount] = {-5, -5, -5, -5, 0, 0, 0, 0 , 0, 0, 5, 5, 5, 5, 10, 10};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 15, 15, 15, 15, 20, 20};
    int64_t mapping_event_time[expectRowsCount] = {1, 1, 3, 3, 1, 1, 3, 3, 5, 5, 12, 12, 5, 5, 12, 12};
    int64_t mapping_sum_window_end[expectRowsCount] = {10, 10, 10, 10, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 40, 40};
    int64_t mapping_count_window_end[expectRowsCount] = {2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2};
    int64_t mapping_max_window_end[expectRowsCount] = {5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 15, 15, 15, 15, 20, 20};
    int64_t mapping_min_window_end[expectRowsCount] = {5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 15, 15, 15, 15, 20, 20};
    RdCheckDataInTSTableOfStreamTable2(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_sum_window_end, mapping_count_window_end, mapping_max_window_end, mapping_min_window_end);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 聚合函数参数使用window_start,[STRICT]，滚窗
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_146)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "window_start_Sum integer, window_start_Count integer, window_start_Max integer, window_start_Min integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time STRict);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "sum(window_start) OVER(PARTITION BY water_mark, window_start, window_end, event_time), "
            "count(window_start) OVER(PARTITION BY event_time, window_start, window_end, water_mark), "
            "max(window_start) OVER(PARTITION BY water_mark, window_start, window_end, event_time), "
            "min(window_start) OVER(PARTITION BY event_time, window_start, window_end, water_mark) "
            "FROM TABLE(TUMBLE(TABLE stream1, event_time, INTERVAL '5' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum_window_start, count_window_start, max_window_start, min_window_start FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 9;
    int64_t id[rowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 10};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 10};
    int64_t event_time[rowNum] = {1, 1, 3, 3, 5, 5, 4, 4, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 6;
    uint32_t expectColsCount = 9;
    int64_t mapping_id[expectRowsCount] = {0, 1, 2, 3, 4, 5};
    int64_t mapping_window_start[expectRowsCount] = {0, 0, 0, 0, 5, 5};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 5, 10, 10};
    int64_t mapping_event_time[expectRowsCount] = {1, 1, 3, 3, 5, 5};
    int64_t mapping_sum_window_start[expectRowsCount] = {0, 0, 0, 0, 10, 10};
    int64_t mapping_count_window_start[expectRowsCount] = {2, 2, 2, 2, 2, 2};
    int64_t mapping_max_window_start[expectRowsCount] = {0, 0, 0, 0, 5, 5};
    int64_t mapping_min_window_start[expectRowsCount] = {0, 0, 0, 0, 5, 5};
    RdCheckDataInTSTableOfStreamTable2(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_sum_window_start, mapping_count_window_start, mapping_max_window_start, mapping_min_window_start);/**/

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 聚合函数参数使用window_start,[TOLERANT]，滚窗
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_147)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "window_start_Sum integer, window_start_Count integer, window_start_Max integer, window_start_Min integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time TOLERANT);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "sum(window_start) OVER(PARTITION BY water_mark, window_start, window_end, event_time), "
            "count(window_start) OVER(PARTITION BY event_time, window_start, window_end, water_mark), "
            "max(window_start) OVER(PARTITION BY water_mark, window_start, window_end, event_time), "
            "min(window_start) OVER(PARTITION BY event_time, window_start, window_end, water_mark) "
            "FROM TABLE(TUMBLE(TABLE stream1, event_time, INTERVAL '5' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum_window_start, count_window_start, max_window_start, min_window_start FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 9;
    int64_t id[rowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 10};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 10};
    int64_t event_time[rowNum] = {1, 1, 3, 3, 5, 5, 4, 4, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 8;
    uint32_t expectColsCount = 9;
    int64_t mapping_id[expectRowsCount] = {0, 1, 2, 3, 6, 7, 4, 5};
    int64_t mapping_window_start[expectRowsCount] = {0, 0, 0, 0, 0, 0, 5, 5};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 5, 5, 5, 10, 10};
    int64_t mapping_event_time[expectRowsCount] = {1, 1, 3, 3, 4, 4, 5, 5};
    int64_t mapping_sum_window_start[expectRowsCount] = {0, 0, 0, 0, 0, 0, 10, 10};
    int64_t mapping_count_window_start[expectRowsCount] = {2, 2, 2, 2, 2, 2, 2, 2};
    int64_t mapping_max_window_start[expectRowsCount] = {0, 0, 0, 0, 0, 0, 5, 5};
    int64_t mapping_min_window_start[expectRowsCount] = {0, 0, 0, 0, 0, 0, 5, 5};
    RdCheckDataInTSTableOfStreamTable2(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_sum_window_start, mapping_count_window_start, mapping_max_window_start, mapping_min_window_start);/**/

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 聚合函数参数使用window_start,[STRICT]，滑窗
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_148)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "window_start_Sum integer, window_start_Count integer, window_start_Max integer, window_start_Min integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time STRICT);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "sum(window_start) OVER(PARTITION BY water_mark, window_start, window_end, event_time), "
            "count(window_start) OVER(PARTITION BY event_time, window_start, window_end, water_mark), "
            "max(window_start) OVER(PARTITION BY water_mark, window_start, window_end, event_time), "
            "min(window_start) OVER(PARTITION BY event_time, window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum_window_start, count_window_start, max_window_start, min_window_start FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 9;
    int64_t id[rowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 10};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 10};
    int64_t event_time[rowNum] = {1, 1, 3, 3, 5, 5, 4, 4, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 14;
    uint32_t expectColsCount = 9;
    int64_t mapping_id[expectRowsCount] = {0, 1, 2, 3, 0, 1, 2, 3, 4, 5, 6, 7, 4, 5};
    int64_t mapping_window_start[expectRowsCount] = {-5, -5, -5, -5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 15, 15};
    int64_t mapping_event_time[expectRowsCount] = {1, 1, 3, 3, 1, 1, 3, 3, 5, 5, 4, 4, 5, 5};
    int64_t mapping_sum_window_start[expectRowsCount] = {-10, -10, -10, -10, 0, 0, 0, 0, 0, 0, 0, 0, 10, 10};
    int64_t mapping_count_window_start[expectRowsCount] = {2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2};
    int64_t mapping_max_window_start[expectRowsCount] = {-5, -5, -5, -5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5};
    int64_t mapping_min_window_start[expectRowsCount] = {-5, -5, -5, -5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5};
    RdCheckDataInTSTableOfStreamTable2(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_sum_window_start, mapping_count_window_start, mapping_max_window_start, mapping_min_window_start);/**/

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 聚合函数参数使用window_start,[TOLERANT]，滑窗
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_149)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "window_start_Sum integer, window_start_Count integer, window_start_Max integer, window_start_Min integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time TOLERANT);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "sum(window_start) OVER(PARTITION BY water_mark, window_start, window_end, event_time), "
            "count(window_start) OVER(PARTITION BY event_time, window_start, window_end, water_mark), "
            "max(window_start) OVER(PARTITION BY water_mark, window_start, window_end, event_time), "
            "min(window_start) OVER(PARTITION BY event_time, window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum_window_start, count_window_start, max_window_start, min_window_start FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 9;
    int64_t id[rowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 10};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 10};
    int64_t event_time[rowNum] = {1, 1, 3, 3, 5, 5, 4, 4, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 16;
    uint32_t expectColsCount = 9;
    int64_t mapping_id[expectRowsCount] = {0, 1, 2, 3, 6, 7, 0, 1, 2, 3, 4, 5, 6, 7, 4, 5};
    int64_t mapping_window_start[expectRowsCount] = {-5, -5, -5, -5, -5, -5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 15, 15};
    int64_t mapping_event_time[expectRowsCount] = {1, 1, 3, 3, 4, 4, 1, 1, 3, 3, 5, 5, 4, 4, 5, 5};
    int64_t mapping_sum_window_start[expectRowsCount] = {-10, -10, -10, -10, -10, -10, 0, 0, 0, 0, 0, 0, 0, 0, 10, 10};
    int64_t mapping_count_window_start[expectRowsCount] = {2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2};
    int64_t mapping_max_window_start[expectRowsCount] = {-5, -5, -5, -5, -5, -5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5};
    int64_t mapping_min_window_start[expectRowsCount] = {-5, -5, -5, -5, -5, -5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5};
    RdCheckDataInTSTableOfStreamTable2(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_sum_window_start, mapping_count_window_start, mapping_max_window_start, mapping_min_window_start);/**/

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 聚合函数参数使用window_end,[STRICT]，滚窗
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_150)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "window_end_Sum integer, window_end_Count integer, window_end_Max integer, window_end_Min integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time STRict);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "sum(window_end) OVER(PARTITION BY water_mark, window_start, window_end, event_time), "
            "count(window_end) OVER(PARTITION BY event_time, window_start, window_end, water_mark), "
            "max(window_end) OVER(PARTITION BY water_mark, window_start, window_end, event_time), "
            "min(window_end) OVER(PARTITION BY event_time, window_start, window_end, water_mark) "
            "FROM TABLE(TUMBLE(TABLE stream1, event_time, INTERVAL '5' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum_window_end, count_window_end, max_window_end, min_window_end FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 9;
    int64_t id[rowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 10};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 10};
    int64_t event_time[rowNum] = {1, 1, 3, 3, 5, 5, 4, 4, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 6;
    uint32_t expectColsCount = 9;
    int64_t mapping_id[expectRowsCount] = {0, 1, 2, 3, 4, 5};
    int64_t mapping_window_start[expectRowsCount] = {0, 0, 0, 0, 5, 5};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 5, 10, 10};
    int64_t mapping_event_time[expectRowsCount] = {1, 1, 3, 3, 5, 5};
    int64_t mapping_sum_window_end[expectRowsCount] = {10, 10, 10, 10, 20, 20};
    int64_t mapping_count_window_end[expectRowsCount] = {2, 2, 2, 2, 2, 2};
    int64_t mapping_max_window_end[expectRowsCount] = {5, 5, 5, 5, 10, 10};
    int64_t mapping_min_window_end[expectRowsCount] = {5, 5, 5, 5, 10, 10};
    RdCheckDataInTSTableOfStreamTable2(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_sum_window_end, mapping_count_window_end, mapping_max_window_end, mapping_min_window_end);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 聚合函数参数使用window_end,[TOLERANT]，滚窗
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_151)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "window_end_Sum integer, window_end_Count integer, window_end_Max integer, window_end_Min integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time TOLERANT);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "sum(window_end) OVER(PARTITION BY water_mark, window_start, window_end, event_time), "
            "count(window_end) OVER(PARTITION BY event_time, window_start, window_end, water_mark), "
            "max(window_end) OVER(PARTITION BY water_mark, window_start, window_end, event_time), "
            "min(window_end) OVER(PARTITION BY event_time, window_start, window_end, water_mark) "
            "FROM TABLE(TUMBLE(TABLE stream1, event_time, INTERVAL '5' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum_window_end, count_window_end, max_window_end, min_window_end FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 9;
    int64_t id[rowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 10};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 10};
    int64_t event_time[rowNum] = {1, 1, 3, 3, 5, 5, 4, 4, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 8;
    uint32_t expectColsCount = 9;
    int64_t mapping_id[expectRowsCount] = {0, 1, 2, 3, 6, 7, 4, 5};
    int64_t mapping_window_start[expectRowsCount] = {0, 0, 0, 0, 0, 0, 5, 5};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 5, 5, 5, 10, 10};
    int64_t mapping_event_time[expectRowsCount] = {1, 1, 3, 3, 4, 4, 5, 5};
    int64_t mapping_sum_window_end[expectRowsCount] = {10, 10, 10, 10, 10, 10, 20, 20};
    int64_t mapping_count_window_end[expectRowsCount] = {2, 2, 2, 2, 2, 2, 2, 2};
    int64_t mapping_max_window_end[expectRowsCount] = {5, 5, 5, 5, 5, 5, 10, 10};
    int64_t mapping_min_window_end[expectRowsCount] = {5, 5, 5, 5, 5, 5, 10, 10};
    RdCheckDataInTSTableOfStreamTable2(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_sum_window_end, mapping_count_window_end, mapping_max_window_end, mapping_min_window_end);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 聚合函数参数使用window_end,[STRICT]，滑窗
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_152)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "window_end_Sum integer, window_end_Count integer, window_end_Max integer, window_end_Min integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time STRICT);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "sum(window_end) OVER(PARTITION BY water_mark, window_start, window_end, event_time), "
            "count(window_end) OVER(PARTITION BY event_time, window_start, window_end, water_mark), "
            "max(window_end) OVER(PARTITION BY water_mark, window_start, window_end, event_time), "
            "min(window_end) OVER(PARTITION BY event_time, window_start, window_end, water_mark) "
            "FROM TABLE(hop(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum_window_end, count_window_end, max_window_end, min_window_end FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 9;
    int64_t id[rowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 10};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 10};
    int64_t event_time[rowNum] = {1, 1, 3, 3, 5, 5, 4, 4, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 14;
    uint32_t expectColsCount = 9;
    int64_t mapping_id[expectRowsCount] = {0, 1, 2, 3, 0, 1, 2, 3, 4, 5, 6, 7, 4, 5};
    int64_t mapping_window_start[expectRowsCount] = {-5, -5, -5, -5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 15, 15};
    int64_t mapping_event_time[expectRowsCount] = {1, 1, 3, 3, 1, 1, 3, 3, 5, 5, 4, 4, 5, 5};
    int64_t mapping_sum_window_end[expectRowsCount] = {10, 10, 10, 10, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30};
    int64_t mapping_count_window_end[expectRowsCount] = {2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2};
    int64_t mapping_max_window_end[expectRowsCount] = {5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 15, 15};
    int64_t mapping_min_window_end[expectRowsCount] = {5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 15, 15};
    RdCheckDataInTSTableOfStreamTable2(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_sum_window_end, mapping_count_window_end, mapping_max_window_end, mapping_min_window_end);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 聚合函数参数使用window_end,[TOLERANT]，滑窗
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_153)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "window_end_Sum integer, window_end_Count integer, window_end_Max integer, window_end_Min integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time TOLERANT);"},
        {"CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, water_mark, event_time, "
            "sum(window_end) OVER(PARTITION BY water_mark, window_start, window_end, event_time), "
            "count(window_end) OVER(PARTITION BY event_time, window_start, window_end, water_mark), "
            "max(window_end) OVER(PARTITION BY water_mark, window_start, window_end, event_time), "
            "min(window_end) OVER(PARTITION BY event_time, window_start, window_end, water_mark) "
            "FROM TABLE(hop(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum_window_end, count_window_end, max_window_end, min_window_end FROM view1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 9;
    int64_t id[rowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 10};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 10};
    int64_t event_time[rowNum] = {1, 1, 3, 3, 5, 5, 4, 4, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 16;
    uint32_t expectColsCount = 9;
    int64_t mapping_id[expectRowsCount] = {0, 1, 2, 3, 6, 7, 0, 1, 2, 3, 4, 5, 6, 7, 4, 5};
    int64_t mapping_window_start[expectRowsCount] = {-5, -5, -5, -5, -5, -5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 15, 15};
    int64_t mapping_event_time[expectRowsCount] = {1, 1, 3, 3, 4, 4, 1, 1, 3, 3, 5, 5, 4, 4, 5, 5};
    int64_t mapping_sum_window_end[expectRowsCount] = {10, 10, 10, 10, 10, 10, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30};
    int64_t mapping_count_window_end[expectRowsCount] = {2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2};
    int64_t mapping_max_window_end[expectRowsCount] = {5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 15, 15};
    int64_t mapping_min_window_end[expectRowsCount] = {5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 15, 15};
    RdCheckDataInTSTableOfStreamTable2(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_sum_window_end, mapping_count_window_end, mapping_max_window_end, mapping_min_window_end);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// over聚合函数使用大写字母，滚窗
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_154)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE TS1 (ID INTEGER, NAME CHAR(50), WINDOW_START INTEGER, WINDOW_END INTEGER, EVENT_TIME INTEGER, "
            "IDSUM INTEGER, IDCOUNT INTEGER, IDMAX INTEGER, IDMIN INTEGER) "
            "WITH (TIME_COL = 'EVENT_TIME', INTERVAL= '1 HOUR', TTL = '1000 HOURS', COMPRESSION = 'NO');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE STREAM1 (ID INTEGER, NAME CHAR(50), WATER_MARK INTEGER, EVENT_TIME INTEGER, "
            "WATERMARK FOR EVENT_TIME AS EVENT_TIME - INTERVAL '2' SECONDS);"},
        {"CREATE STREAM VIEW VIEW1 AS SELECT ID, NAME, window_start, WINDOW_END, WATER_MARK, EVENT_TIME, "
            "sum(ID) OVER(PARTITION BY window_start, water_mark, event_time, WINDOW_END), "
            "count(ID) OVER(PARTITION BY window_start, event_time, water_mark, WINDOW_END), "
            "max(ID) OVER(PARTITION BY window_start, water_mark, event_time, WINDOW_END), "
            "min(ID) OVER(PARTITION BY window_start, event_time, water_mark, WINDOW_END) "
            "FROM TABLE(TUMBLE(TABLE stream1, event_time, INTERVAL '5' SECONDS));"},
        {"CREATE STREAM SINK sink1 AS SELECT id, name, window_start, WINDOW_END, event_time, "
            "sum_ID, count_ID, max_ID, min_ID FROM VIEW1 INTO TSDB(ts1) WITH (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 9;
    int64_t id[rowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 10};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 10};
    int64_t event_time[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 6;
    uint32_t expectColsCount = 9;
    int64_t mapping_id[expectRowsCount] = {0, 1, 2, 3, 4, 5};
    int64_t mapping_window_start[expectRowsCount] = {0, 0, 0, 0, 5, 5};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 5, 10, 10};
    int64_t mapping_event_time[expectRowsCount] = {1, 1, 3, 3, 7, 7};
    int64_t mapping_sum_id[expectRowsCount] = {1, 1, 5, 5, 9, 9};
    int64_t mapping_count_id[expectRowsCount] = {2, 2, 2, 2, 2, 2};
    int64_t mapping_max_id[expectRowsCount] = {1, 1, 3, 3, 5, 5};
    int64_t mapping_min_id[expectRowsCount] = {0, 0, 2, 2, 4, 4};
    RdCheckDataInTSTableOfStreamTable2(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_sum_id, mapping_count_id, mapping_max_id, mapping_min_id);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// over聚合函数使用大写字母，滑窗
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_155)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE TS1 (ID INTEGER, NAME CHAR(50), WINDOW_START INTEGER, WINDOW_END INTEGER, EVENT_TIME INTEGER, "
            "IDSUM INTEGER, IDCOUNT INTEGER, IDMAX INTEGER, IDMIN INTEGER) "
            "WITH (TIME_COL = 'EVENT_TIME', INTERVAL= '1 HOUR', TTL = '1000 HOURS', COMPRESSION = 'NO');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE STREAM1 (ID INTEGER, NAME CHAR(50), WATER_MARK INTEGER, EVENT_TIME INTEGER, "
            "WATERMARK FOR EVENT_TIME AS EVENT_TIME - INTERVAL '2' SECONDS);"},
        {"CREATE STREAM VIEW VIEW1 AS SELECT ID, NAME, WINDOW_START, WINDOW_END, WATER_MARK, EVENT_TIME, "
            "SUM(ID) OVER(PARTITION BY WATER_MARK, EVENT_TIME, WINDOW_START, WINDOW_END), "
            "COUNT(ID) OVER(PARTITION BY EVENT_TIME, WATER_MARK, WINDOW_START, WINDOW_END), "
            "MAX(ID) OVER(PARTITION BY WATER_MARK, EVENT_TIME, WINDOW_START, WINDOW_END), "
            "MIN(ID) OVER(PARTITION BY EVENT_TIME, WATER_MARK, WINDOW_START, WINDOW_END) "
            "FROM TABLE(HOP(TABLE STREAM1, EVENT_TIME, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));"},
        {"CREATE STREAM SINK SINK1 AS SELECT ID, NAME, WINDOW_START, WINDOW_END, EVENT_TIME, "
            "SUM_ID, COUNT_ID, MAX_ID, MIN_ID FROM VIEW1 INTO TSDB(TS1) WITH (BATCH_WINDOW_SIZE = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    int64_t id[rowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 10};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 10};
    int64_t event_time[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 14;
    uint32_t expectColsCount = 9;
    int64_t mapping_id[expectRowsCount] = {0, 1, 2, 3, 0, 1, 2, 3, 4, 5, 6, 7, 4, 5};
    int64_t mapping_window_start[expectRowsCount] = {-5, -5, -5, -5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 15, 15};
    int64_t mapping_event_time[expectRowsCount] = {1, 1, 3, 3, 1, 1, 3, 3, 7, 7, 4, 4, 7, 7};
    int64_t mapping_sum_id[expectRowsCount] = {1, 1, 5, 5, 1, 1, 5, 5, 9, 9, 13, 13, 9, 9};
    int64_t mapping_count_id[expectRowsCount] = {2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2};
    int64_t mapping_max_id[expectRowsCount] = {1, 1, 3, 3, 1, 1, 3, 3, 5, 5, 7, 7, 5, 5};
    int64_t mapping_min_id[expectRowsCount] = {0, 0, 2, 2, 0, 0, 2, 2, 4, 4, 6, 6, 4, 4};
    RdCheckDataInTSTableOfStreamTable2(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_sum_id, mapping_count_id, mapping_max_id, mapping_min_id);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// over聚合函数使用小写字母，滚窗
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_156)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "idsum integer, idcount integer, idmax integer, idmin integer) "
            "with (time_col = 'event_time', interval= '1 hour', ttl = '1000 hours', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "watermark for event_time as event_time - interval '2' seconds);"},
        {"create stream view view1 as select id, name, window_start, window_end, water_mark, event_time, "
            "sum(id) over(partition by window_start, water_mark, event_time, window_end), "
            "count(id) over(partition by window_start, event_time, water_mark, window_end), "
            "max(id) over(partition by window_start, water_mark, event_time, window_end), "
            "min(id) over(partition by window_start, event_time, water_mark, window_end) "
            "from table(tumble(table stream1, event_time, interval '5' seconds));"},
        {"create stream sink sink1 as select id, name, window_start, window_end, event_time, "
            "sum_id, count_id, max_id, min_id from view1 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 9;
    int64_t id[rowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 10};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 10};
    int64_t event_time[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 6;
    uint32_t expectColsCount = 9;
    int64_t mapping_id[expectRowsCount] = {0, 1, 2, 3, 4, 5};
    int64_t mapping_window_start[expectRowsCount] = {0, 0, 0, 0, 5, 5};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 5, 10, 10};
    int64_t mapping_event_time[expectRowsCount] = {1, 1, 3, 3, 7, 7};
    int64_t mapping_sum_id[expectRowsCount] = {1, 1, 5, 5, 9, 9};
    int64_t mapping_count_id[expectRowsCount] = {2, 2, 2, 2, 2, 2};
    int64_t mapping_max_id[expectRowsCount] = {1, 1, 3, 3, 5, 5};
    int64_t mapping_min_id[expectRowsCount] = {0, 0, 2, 2, 4, 4};
    RdCheckDataInTSTableOfStreamTable2(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_sum_id, mapping_count_id, mapping_max_id, mapping_min_id);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// over聚合函数使用小写字母，滑窗
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_157)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "idsum integer, idcount integer, idmax integer, idmin integer) "
            "with (time_col = 'event_time', interval= '1 hour', ttl = '1000 hours', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "watermark for event_time as event_time - interval '2' seconds);"},
        {"create stream view view1 as select id, name, window_start, window_end, water_mark, event_time, "
            "sum(id) over(partition by water_mark, event_time, window_start, window_end), "
            "count(id) over(partition by event_time, water_mark, window_start, window_end), "
            "max(id) over(partition by water_mark, event_time, window_start, window_end), "
            "min(id) over(partition by event_time, water_mark, window_start, window_end) "
            "from table(hop(table stream1, event_time, interval '5' seconds, interval '10' seconds));"},
        {"create stream sink sink1 as select id, name, window_start, window_end, event_time, "
            "sum_id, count_id, max_id, min_id from view1 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    int64_t id[rowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 10};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 10};
    int64_t event_time[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 14;
    uint32_t expectColsCount = 9;
    int64_t mapping_id[expectRowsCount] = {0, 1, 2, 3, 0, 1, 2, 3, 4, 5, 6, 7, 4, 5};
    int64_t mapping_window_start[expectRowsCount] = {-5, -5, -5, -5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 15, 15};
    int64_t mapping_event_time[expectRowsCount] = {1, 1, 3, 3, 1, 1, 3, 3, 7, 7, 4, 4, 7, 7};
    int64_t mapping_sum_id[expectRowsCount] = {1, 1, 5, 5, 1, 1, 5, 5, 9, 9, 13, 13, 9, 9};
    int64_t mapping_count_id[expectRowsCount] = {2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2};
    int64_t mapping_max_id[expectRowsCount] = {1, 1, 3, 3, 1, 1, 3, 3, 5, 5, 7, 7, 5, 5};
    int64_t mapping_min_id[expectRowsCount] = {0, 0, 2, 2, 0, 0, 2, 2, 4, 4, 6, 6, 4, 4};
    RdCheckDataInTSTableOfStreamTable2(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_sum_id, mapping_count_id, mapping_max_id, mapping_min_id);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// over聚合函数使用大小写字母混合，滚窗
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_158)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"Create taBle Ts1 (id inteGer, name Char(50), window_start integer, window_end integer, event_time integer, "
            "idsum integer, idcount integer, idmax integer, idmin integer) "
            "with (time_Col = 'event_time', interVal= '1 hour', Ttl = '1000 hours', comPression = 'no');", GMC_MODEL_TS},
        {"creAte Stream Table stream1 (id integer, name char(50), water_mark integer, event_Time integer, "
            "waterMark for Event_time as evenT_time - interval '2' seconds);"},
        {"creatE strEam View View1 as select Id, name, window_start, window_end, Water_mark, Event_Time, "
            "Sum(Id) Over(Partition By WINDOW_start, water_mark, event_time, window_end), "
            "Count(iD) oVer(partItion bY WINDOW_start, event_time, water_mark, window_end), "
            "mAx(ID) ovEr(partiTion by WINDOW_start, water_mark, event_time, window_end), "
            "Min(id) oveR(partitioN by WINDOW_start, event_time, water_mark, window_end) "
            "from table(tumBle(taBle Stream1, event_time, inTerval '5' secondS));"},
        {"cReate sTream Sink sink1 as Select Id, nAme, window_start, window_end, event_Time, "
            "suM_id, counT_id, maX_id, miN_id From viEW1 inTo tSdB(tS1) With (bAtch_winDow_siZe = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 9;
    int64_t id[rowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 10};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 10};
    int64_t event_time[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 6;
    uint32_t expectColsCount = 9;
    int64_t mapping_id[expectRowsCount] = {0, 1, 2, 3, 4, 5};
    int64_t mapping_window_start[expectRowsCount] = {0, 0, 0, 0, 5, 5};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 5, 10, 10};
    int64_t mapping_event_time[expectRowsCount] = {1, 1, 3, 3, 7, 7};
    int64_t mapping_sum_id[expectRowsCount] = {1, 1, 5, 5, 9, 9};
    int64_t mapping_count_id[expectRowsCount] = {2, 2, 2, 2, 2, 2};
    int64_t mapping_max_id[expectRowsCount] = {1, 1, 3, 3, 5, 5};
    int64_t mapping_min_id[expectRowsCount] = {0, 0, 2, 2, 4, 4};
    RdCheckDataInTSTableOfStreamTable2(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_sum_id, mapping_count_id, mapping_max_id, mapping_min_id);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// over聚合函数使用大小写字母混合，滑窗
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_159)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "idsum integer, idcount integer, idmax integer, idmin integer) "
            "with (time_col = 'event_time', interval= '1 hour', ttl = '1000 hours', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "watermark for event_time as event_time - interval '2' seconds);"},
        {"create stream view view1 as select id, name, window_start, window_end, water_mark, event_time, "
            "sum(id) over(partition by water_mark, event_time, window_start, window_end), "
            "count(id) over(partition by event_time, water_mark, window_start, window_end), "
            "max(id) over(partition by water_mark, event_time, window_start, window_end), "
            "min(id) over(partition by event_time, water_mark, window_start, window_end) "
            "from table(hop(table stream1, event_time, interval '5' seconds, interval '10' seconds));"},
        {"create stream sink sink1 as select id, name, window_start, window_end, event_time, "
            "sum_id, count_id, max_id, min_id from view1 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    int64_t id[rowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 10};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 10};
    int64_t event_time[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 14;
    uint32_t expectColsCount = 9;
    int64_t mapping_id[expectRowsCount] = {0, 1, 2, 3, 0, 1, 2, 3, 4, 5, 6, 7, 4, 5};
    int64_t mapping_window_start[expectRowsCount] = {-5, -5, -5, -5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 15, 15};
    int64_t mapping_event_time[expectRowsCount] = {1, 1, 3, 3, 1, 1, 3, 3, 7, 7, 4, 4, 7, 7};
    int64_t mapping_sum_id[expectRowsCount] = {1, 1, 5, 5, 1, 1, 5, 5, 9, 9, 13, 13, 9, 9};
    int64_t mapping_count_id[expectRowsCount] = {2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2};
    int64_t mapping_max_id[expectRowsCount] = {1, 1, 3, 3, 1, 1, 3, 3, 5, 5, 7, 7, 5, 5};
    int64_t mapping_min_id[expectRowsCount] = {0, 0, 2, 2, 0, 0, 2, 2, 4, 4, 6, 6, 4, 4};
    RdCheckDataInTSTableOfStreamTable2(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_sum_id, mapping_count_id, mapping_max_id, mapping_min_id);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// over聚合中,'over'拼写错误
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_160)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum(id) OVET(PARTITION BY window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SYNTAX_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// over聚合中,'sum'拼写错误
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_161)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "sam(id) over(PARTITION BY window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// over聚合中,'count'拼写错误
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_162)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "counx(id) over(PARTITION BY window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// over聚合中,'max'拼写错误
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_163)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "maxx(id) over(PARTITION BY window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// over聚合中,'min'拼写错误
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_164)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "mmin(id) over(PARTITION BY window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// over聚合中,'partition'拼写错误
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_165)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum(id) over(PARTION BY window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SYNTAX_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// over聚合中,'by'拼写错误
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_166)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum(id) over(PARTITION BE window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SYNTAX_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// over聚合中,'window_start'拼写错误
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_167)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_star, window_end, event_time, "
            "sum(id) over(PARTITION BY window_star, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_UNDEFINE_COLUMN, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// over聚合中,'window_end'拼写错误
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_168)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_enb, event_time, "
            "sum(id) over(PARTITION BY window_start, window_enb, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_UNDEFINE_COLUMN, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// over聚合中,'over'缺失
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_169)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum(id) (PARTITION BY window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SYNTAX_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// over聚合中,'partition'缺失
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_170)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum(id) over(BY window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SYNTAX_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// over聚合中,'by'缺失
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_171)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum(id) over(partition window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SYNTAX_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// over聚合中,sum参数缺失
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_172)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum() over(partition by window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SEMANTIC_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// over聚合中,count参数缺失
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_173)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "count() over(partition by window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SEMANTIC_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// over聚合中,max参数缺失
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_174)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "max() over(partition by window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SEMANTIC_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// over聚合中,min参数缺失
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_175)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "min() over(partition by window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SEMANTIC_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// over前函数缺失
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_176)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "over(partition by window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SYNTAX_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// over前使用select
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_177)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "select over(partition by window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SYNTAX_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// over前使用select+列名
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_178)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "select id over(partition by window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SYNTAX_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// over前使用select+常数
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_179)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "select 1 over(partition by window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SYNTAX_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// over聚合语句后缺少分号
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_180)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "idsum integer, idcount integer, idmax integer, idmin integer) "
            "with (time_col = 'event_time', interval= '1 hour', ttl = '1000 hours', compression = 'no')", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "watermark for event_time as event_time - interval '2' seconds);"},
        {"create stream view view1 as select id, name, window_start, window_end, water_mark, event_time, "
            "sum(id) over(partition by water_mark, event_time, window_start, window_end), "
            "count(id) over(partition by event_time, water_mark, window_start, window_end), "
            "max(id) over(partition by water_mark, event_time, window_start, window_end), "
            "min(id) over(partition by event_time, water_mark, window_start, window_end) "
            "from table(hop(table stream1, event_time, interval '5' seconds, interval '10' seconds))"},
        {"create stream sink sink1 as select id, name, window_start, window_end, event_time, "
            "sum_id, count_id, max_id, min_id from view1 into tsdb(ts1) with (batch_window_size = 1)"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 11;
    int64_t id[rowNum] = {0, 1, 2, 3, 4, 5, 6, 7, 10};
    int64_t water_mark[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 10};
    int64_t event_time[rowNum] = {1, 1, 3, 3, 7, 7, 4, 4, 1000};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, water_mark, event_time);

    // 校验ts表中数据
    char *sqlCmd = (char *)"SELECT * FROM ts1 order by window_end, id;";
    uint32_t expectRowsCount = 14;
    uint32_t expectColsCount = 9;
    int64_t mapping_id[expectRowsCount] = {0, 1, 2, 3, 0, 1, 2, 3, 4, 5, 6, 7, 4, 5};
    int64_t mapping_window_start[expectRowsCount] = {-5, -5, -5, -5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5};
    int64_t mapping_window_end[expectRowsCount] = {5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 15, 15};
    int64_t mapping_event_time[expectRowsCount] = {1, 1, 3, 3, 1, 1, 3, 3, 7, 7, 4, 4, 7, 7};
    int64_t mapping_sum_id[expectRowsCount] = {1, 1, 5, 5, 1, 1, 5, 5, 9, 9, 13, 13, 9, 9};
    int64_t mapping_count_id[expectRowsCount] = {2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2};
    int64_t mapping_max_id[expectRowsCount] = {1, 1, 3, 3, 1, 1, 3, 3, 5, 5, 7, 7, 5, 5};
    int64_t mapping_min_id[expectRowsCount] = {0, 0, 2, 2, 0, 0, 2, 2, 4, 4, 6, 6, 4, 4};
    RdCheckDataInTSTableOfStreamTable2(stmt, sqlCmd, expectRowsCount, expectColsCount,
        mapping_id, mapping_window_start, mapping_window_end, mapping_event_time,
        mapping_sum_id, mapping_count_id, mapping_max_id, mapping_min_id);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1"},
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 多个over中缺少逗号
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_181)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum(id) over(partition by window_start, window_end, water_mark) "
            "count(id) over(partition by window_start, window_end, water_mark) "
            "max(id) over(partition by window_start, window_end, water_mark) "
            "min(id) over(partition by window_start, window_end, water_mark) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SYNTAX_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// sum缺失over子句
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_182)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum(id) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SEMANTIC_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// count缺失over子句
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_183)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "count(id) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SEMANTIC_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// max缺失over子句
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_184)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "max(id) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SEMANTIC_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// min缺失over子句
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_185)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "min(id) "
            "FROM TABLE(HOP(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SEMANTIC_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 聚合结果进行表达式计算
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_186)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, "
            "idsum integer) "
            "with (time_col = 'event_time', interval= '1 hour', ttl = '1000 hours', compression = 'no')", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "watermark for event_time as event_time - interval '2' seconds);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "create stream view view1 as select id, name, window_start, window_end, water_mark, event_time, "
            "sum(id) over(partition by water_mark, event_time, window_start, window_end) > 3 "
            "from table(hop(table stream1, event_time, interval '5' seconds, interval '10' seconds))";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SEMANTIC_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 缺少tumble关键字
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_187)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum(id) over(partition by window_start, window_end, water_mark) "
            "FROM TABLE((TABLE stream1, event_time, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SYNTAX_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// tumble关键字错误
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_188)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum(id) over(partition by window_start, window_end, water_mark) "
            "FROM TABLE(tumdle(TABLE stream1, event_time, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SYNTAX_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 缺少hop关键字
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_189)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum(id) over(partition by window_start, window_end, water_mark) "
            "FROM TABLE((TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SYNTAX_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// hop关键字错误
TEST_F(t03_over_aggregate_calculation_syntax, STREAM_013_190)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    // create
    RdStreamExecDescT creates[] = {
        {"CREATE TABLE ts1 (id integer, name char(50), window_start integer, window_end integer, event_time integer, idSum integer) "
            "WITH (TIME_COL = 'event_time', INTERVAL= '1 hour', TTL = '1000 hours', COMPRESSION = 'no');", GMC_MODEL_TS},
        {"CREATE STREAM TABLE stream1 (id integer, name char(50), water_mark integer, event_time integer, "
            "WATERMARK FOR event_time AS event_time - INTERVAL '2' SECONDS);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *sqlCmd = "CREATE STREAM VIEW view1 AS SELECT id, name, window_start, window_end, event_time, "
            "sum(id) over(partition by window_start, window_end, water_mark) "
            "FROM TABLE(hap(TABLE stream1, event_time, INTERVAL '5' SECONDS, INTERVAL '10' SECONDS));";
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    ASSERT_EQ(GMERR_SYNTAX_ERROR, ret);

    // drop
    RdStreamExecDescT drops[] = {
        {"drop stream table stream1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
