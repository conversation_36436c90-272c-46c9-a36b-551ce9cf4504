/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-11-05 15:56:18
 * @FilePath: \GMDBV5\test\sdv\gmdb_td\testcases\001_stream\017_support_line_num\01_support_line_num.cpp
 * @Description: 
 * @LastEditors: tian<PERSON><PERSON> 
 * @LastEditTime: 2024-11-14 09:57:39
 */
#include "gtest/gtest.h"
#include "securec.h"
#include "gmc_sql.h"
#include "rd_feature_stream.h"
#include "line_num_util.h"


class LineNum : public testing::Test {
public:
    static GmcConnT *conn;
    static GmcStmtT *stmt;

    bool hasFinishTest = false;
    void RdFinishTest();

    static void SetUpTestCase();
    static void TearDownTestCase();
    virtual void SetUp();
    virtual void TearDown();
};

GmcConnT *LineNum::conn = NULL;
GmcStmtT *LineNum::stmt = NULL;

void LineNum::SetUpTestCase()
{
    int32_t ret;
    ret = RdStreamEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void LineNum::TearDownTestCase()
{
    int32_t ret = RdStreamEnvClean();
    EXPECT_EQ(GMERR_OK, ret);
}

void LineNum::SetUp()
{
    int32_t ret = RdGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void LineNum::TearDown()
{
    int32_t ret = RdGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 创建流表，定义strict【有界乱序】watermark，再创建包含一个line_num、基于滑动步长(slide) > 窗口大小(size)的滑动窗口的流视图预期：成功
TEST_F(LineNum, STREAM_017_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name char(50), time integer, age integer, row_num integer) "
            "with (time_col = 'time', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer, "
            "watermark for time as time - interval '10' seconds strict);"
        },
        {
            "create stream view v1 as select id, name, time, age, ROW_NUMBER() over (PARTITION BY window_start, window_end, age) from "
            "table(hop(table t1, time, interval '20' seconds, interval '10' seconds)) with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"
        }
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 18;
    int64_t id[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 23, 25, 30, 8, 35, 22, 23};
    int64_t time[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 23, 25, 30, 8, 35, 22, 23};
    int64_t age[rowNum] = {10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, time, age);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 9;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 3, 5, 20, 21, 23, 25, 22, 23};
    int64_t expectTime[expectRowsCount] = {1, 3, 5, 20, 21, 23, 25, 22, 23};
    int64_t expectAge[expectRowsCount] = {10, 11, 12, 10, 12, 11, 12, 11, 10};
    int64_t expectLineNum[expectRowsCount] = {1, 1, 1, 1, 1, 1, 2, 2, 2};
    RdCheckDataInTSTableOfOneOver(stmt, selectTsName, expectRowsCount, expectColsCount,
                                  expectId, expectTime, expectAge, expectLineNum);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop table ts1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建流表，定义strict【有界乱序】watermark，再创建包含一个line_num、基于滑动步长(slide) < 窗口大小(size)的滑动窗口的流视图预期：成功
TEST_F(LineNum, STREAM_017_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name char(50), time integer, age integer, row_num integer) "
            "with (time_col = 'age', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer, "
            "watermark for time as time - interval '5' seconds strict);"
        },
        {
            "create stream view v1 as select id, name, time, age, ROW_NUMBER() over (PARTITION BY window_start, window_end, age) from "
            "table(hop(table t1, time, interval '10' seconds, interval '20' seconds)) with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"
        }
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 19;
    int64_t id[rowNum] = {-5, -3, 0, 1, 3, 5, 10, 11, 13, 10, 14, 15, 20, 23, 25, 30, 35, 22, 23};
    int64_t time[rowNum] = {-5, -3, 0, 1, 3, 5, 10, 11, 13, 10, 14, 15, 20, 23, 25, 30, 35, 22, 23};
    int64_t age[rowNum] = {14, 13, 10, 11, 12, 13, 13, 12, 16, 10, 10, 12, 15, 11, 12, 11, 14, 13, 11};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, time, age);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 36;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {
        -5, -5, -3, -3, 0,  0,  1,  1,  3,  3,  5,  5,  10, 10, 11, 11, 13, 13, 10, 10, 14,  
        14, 15, 15, 20, 20, 23, 23, 25, 25, 30, 30, 35, 35, 22, 23
    };
    int64_t expectTime[expectRowsCount] = {
        -5, -5, -3, -3, 0,  0,  1,  1,  3,  3,  5,  5,  10, 10, 11, 11, 13, 13, 10, 10, 14,  
        14, 15, 15, 20, 20, 23, 23, 25, 25, 30, 30, 35, 35, 22, 23
    };
    int64_t expectAge[expectRowsCount] = {
        14, 14, 13, 13, 10, 10, 11, 11, 12, 12, 13, 13, 13, 13, 12, 12, 16, 16, 
        10, 10, 10, 10, 12, 12, 15, 15, 11, 11, 12, 12, 11, 11, 14, 14, 13, 11
    };
    int64_t expectLineNum[expectRowsCount] = {
        1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 1, 2, 
        1, 2, 1, 1, 1, 2, 2, 3, 2, 3, 1, 1, 1, 1, 1, 3, 1, 2, 1, 1, 1, 3};
    RdCheckDataInTSTableOfOneOver(stmt, selectTsName, expectRowsCount, expectColsCount,
                                      expectId, expectTime, expectAge, expectLineNum);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop table ts1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建流表，定义strict【有界乱序】watermark，再创建包含一个line_num、基于滑动步长(slide) = 窗口大小(size)的滑动窗口的流视图预期：成功
TEST_F(LineNum, STREAM_017_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name char(50), time integer, age integer, row_num integer) "
            "with (time_col = 'age', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer, "
            "watermark for time as time - interval '5' seconds strict);"
        },
        {
            "create stream view v1 as select id, name, time, age, ROW_NUMBER() over (PARTITION BY window_start, window_end, age) from "
            "table(hop(table t1, time, interval '10' seconds, interval '10' seconds)) with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"
        }
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 17;
    int64_t id[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t time[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t age[rowNum] = {14, 13, 10, 11, 12, 14, 13, 14, 12, 14, 10, 10, 12, 15, 11, 12, 11};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, time, age);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 14;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 16, 20, 23, 25, 30};
    int64_t expectTime[expectRowsCount] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 16, 20, 23, 25, 30};
    int64_t expectAge[expectRowsCount] = {14, 13, 10, 11, 12, 14, 13, 14, 12, 10, 12, 11, 12, 11};
    int64_t expectLineNum[expectRowsCount] = {1, 1, 1, 1, 1, 1, 1, 2, 2, 1, 1, 1, 2, 1};
    RdCheckDataInTSTableOfOneOver(stmt, selectTsName, expectRowsCount, expectColsCount,
                                      expectId, expectTime, expectAge, expectLineNum);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop table ts1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建流表，定义tolerent【有界乱序】watermark，再创建包含一个line_num、基于滑动步长(slide) > 窗口大小(size)的滑动窗口的流视图预期：成功
TEST_F(LineNum, STREAM_017_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name char(50), time integer, age integer, row_num integer) "
            "with (time_col = 'time', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer, "
            "watermark for time as time - interval '10' seconds tolerant);"
        },
        {
            "create stream view v1 as select id, name, time, age, ROW_NUMBER() over (PARTITION BY window_start, window_end, age) from "
            "table(hop(table t1, time, interval '20' seconds, interval '10' seconds)) with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"
        }
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 18;
    int64_t id[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 23, 25, 30, 8, 35, 22, 23};
    int64_t time[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 23, 25, 30, 8, 35, 22, 23};
    int64_t age[rowNum] = {10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, time, age);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 10;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 3, 5, 20, 7, 21, 23, 25, 22, 23};
    int64_t expectTime[expectRowsCount] = {1, 3, 5, 20, 7, 21, 23, 25, 22, 23};
    int64_t expectAge[expectRowsCount] = {10, 11, 12, 10, 10, 12, 11, 12, 11, 10};
    int64_t expectLineNum[expectRowsCount] = {1, 1, 1, 1, 2, 1, 1, 2, 2, 2};
    RdCheckDataInTSTableOfOneOver(stmt, selectTsName, expectRowsCount, expectColsCount,
                                  expectId, expectTime, expectAge, expectLineNum);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop table ts1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建流表，定义tolerent【有界乱序】watermark，再创建包含一个line_num、基于滑动步长(slide) < 窗口大小(size)的滑动窗口的流视图预期：成功
TEST_F(LineNum, STREAM_017_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name char(50), time integer, age integer, row_num integer) "
            "with (time_col = 'age', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer, "
            "watermark for time as time - interval '5' seconds tolerant);"
        },
        {
            "create stream view v1 as select id, name, time, age, ROW_NUMBER() over (PARTITION BY window_start, window_end, age) from "
            "table(hop(table t1, time, interval '10' seconds, interval '20' seconds)) with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"
        }
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 20;
    int64_t id[rowNum] = {0, 1, 3, 5, 10, 15, 16, 20, 23, 25, 7, 26, 8, 30, 35, 4, 36, 5, 22, 23};
    int64_t time[rowNum] = {0, 1, 3, 5, 10, 15, 16, 20, 23, 25, 7, 26, 8, 30, 35, 4, 36, 5, 22, 23};
    int64_t age[rowNum] = {10, 11, 12, 13, 10, 11, 12, 10, 11, 12, 10, 13, 10, 10, 11, 10, 12, 11, 10, 11};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, time, age);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 31;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {
        0, 0, 1, 1, 3, 3, 5, 5, 10, 10, 15, 15, 16, 16, 20, 20, 23, 23, 
        25, 25, 7, 26, 26, 30, 30, 35, 35, 36, 36, 22, 23
    };
    int64_t expectTime[expectRowsCount] = {
        0, 0, 1, 1, 3, 3, 5, 5, 10, 10, 15, 15, 16, 16, 20, 20, 23, 23, 
        25, 25, 7, 26, 26, 30, 30, 35, 35, 36, 36, 22, 23
    };
    int64_t expectAge[expectRowsCount] = {
        10, 10, 11, 11, 12, 12, 13, 13, 10, 10, 11, 11, 12, 12, 10, 
        10, 11, 11, 12, 12, 10, 13, 13, 10, 10, 11, 11, 12, 12, 10, 11
    };
    int64_t expectLineNum[expectRowsCount] = {
        1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 1, 2, 1, 2, 1, 2, 1, 
        2, 1, 2, 3, 1, 1, 1, 2, 1, 2, 1, 2, 3, 3
    };
    RdCheckDataInTSTableOfOneOver(stmt, selectTsName, expectRowsCount, expectColsCount,
                                      expectId, expectTime, expectAge, expectLineNum);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop table ts1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建流表，定义tolerent【有界乱序】watermark，再创建包含一个line_num、基于滑动步长(slide) = 窗口大小(size)的滑动窗口的流视图预期：成功
TEST_F(LineNum, STREAM_017_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name char(50), time integer, age integer, row_num integer) "
            "with (time_col = 'age', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer, "
            "watermark for time as time - interval '5' seconds tolerant);"
        },
        {
            "create stream view v1 as select id, name, time, age, ROW_NUMBER() over (PARTITION BY window_start, window_end, age) from "
            "table(hop(table t1, time, interval '10' seconds, interval '10' seconds)) with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"
        }
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 17;
    int64_t id[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t time[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t age[rowNum] = {14, 13, 10, 11, 12, 14, 13, 14, 12, 14, 10, 10, 12, 15, 11, 12, 11};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, time, age);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 15;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 20, 23, 25, 30};
    int64_t expectTime[expectRowsCount] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 20, 23, 25, 30};
    int64_t expectAge[expectRowsCount] = {14, 13, 10, 11, 12, 14, 13, 14, 12, 14, 10, 12, 11, 12, 11};
    int64_t expectLineNum[expectRowsCount] = {1, 1, 1, 1, 1, 1, 1, 2, 2, 3, 1, 1, 1, 2, 1};
    RdCheckDataInTSTableOfOneOver(stmt, selectTsName, expectRowsCount, expectColsCount,
                                      expectId, expectTime, expectAge, expectLineNum);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop table ts1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建流表，定义strict watermark，再创建包含一个line_num、基于滑动步长(slide) > 窗口大小(size)的滑动窗口的流视图预期：成功
TEST_F(LineNum, STREAM_017_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name char(50), time integer, age integer, row_num integer) "
            "with (time_col = 'time', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer, "
            "watermark for time as time strict);"
        },
        {
            "create stream view v1 as select id, name, time, age, ROW_NUMBER() over (PARTITION BY window_start, window_end, age) from "
            "table(hop(table t1, time, interval '20' seconds, interval '10' seconds)) with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"
        }
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 18;
    int64_t id[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 20, 21, 7, 23, 25, 30, 8, 35, 22, 23};
    int64_t time[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 20, 21, 7, 23, 25, 30, 8, 35, 22, 23};
    int64_t age[rowNum] = {10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, time, age);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 8;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 3, 5, 20, 20, 21, 23, 25};
    int64_t expectTime[expectRowsCount] = {1, 3, 5, 20, 20, 21, 23, 25};
    int64_t expectAge[expectRowsCount] = {10, 11, 12, 10, 10, 12, 11, 12};
    int64_t expectLineNum[expectRowsCount] = {1, 1, 1, 1, 2, 1, 1, 2};
    RdCheckDataInTSTableOfOneOver(stmt, selectTsName, expectRowsCount, expectColsCount,
                                  expectId, expectTime, expectAge, expectLineNum);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop table ts1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建流表，定义strict watermark，再创建包含一个line_num、基于滑动步长(slide) < 窗口大小(size)的滑动窗口的流视图预期：成功
TEST_F(LineNum, STREAM_017_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name char(50), time integer, age integer, row_num integer) "
            "with (time_col = 'age', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer, "
            "watermark for time as time strict);"
        },
        {
            "create stream view v1 as select id, name, time, age, ROW_NUMBER() over (PARTITION BY window_start, window_end, age) from "
            "table(hop(table t1, time, interval '10' seconds, interval '20' seconds)) with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"
        }
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 20;
    int64_t id[rowNum] = {0, 1, 3, 5, 10, 10, 11, 13, 10, 14, 15, 20, 20, 23, 25, 30, 30, 35, 22, 23};
    int64_t time[rowNum] = {0, 1, 3, 5, 10, 10, 11, 13, 10, 14, 15, 20, 20, 23, 25, 30, 30, 35, 22, 23};
    int64_t age[rowNum] = {10, 11, 12, 13, 10, 10, 11, 12, 10, 13, 14, 10, 10, 11, 12, 10, 10, 11, 12, 13};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, time, age);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 38;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {
        0, 0, 1, 1, 3, 3, 5, 5, 10, 10, 10, 10, 11, 11, 13, 13, 10, 10, 14, 14,
         15, 15, 20, 20, 20, 20, 23, 23, 25, 25, 30, 30, 30, 30, 35, 35, 22, 23
    };
    int64_t expectTime[expectRowsCount] = {
        0, 0, 1, 1, 3, 3, 5, 5, 10, 10, 10, 10, 11, 11, 13, 13, 10, 10, 14, 14, 
        15, 15, 20, 20, 20, 20, 23, 23, 25, 25, 30, 30, 30, 30, 35, 35, 22, 23
    };
    int64_t expectAge[expectRowsCount] = {
        10, 10, 11, 11, 12, 12, 13, 13, 10, 10, 10, 10, 11, 11, 12, 12, 10, 10, 
        13, 13, 14, 14, 10, 10, 10, 10, 11, 11, 12, 12, 10, 10, 10, 10, 11, 11, 12, 13
    };
    int64_t expectLineNum[expectRowsCount] = {
        1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 3, 1, 2, 1, 2, 3, 4, 1, 
        2, 1, 1, 1, 4, 2, 5, 1, 2, 1, 2, 1, 3, 2, 4, 1, 2, 2, 1
    };
    RdCheckDataInTSTableOfOneOver(stmt, selectTsName, expectRowsCount, expectColsCount,
                                      expectId, expectTime, expectAge, expectLineNum);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop table ts1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建流表，定义strict watermark，再创建包含一个line_num、基于滑动步长(slide) = 窗口大小(size)的滑动窗口的流视图预期：成功
TEST_F(LineNum, STREAM_017_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name char(50), time integer, age integer, row_num integer) "
            "with (time_col = 'age', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer, "
            "watermark for time as time strict);"
        },
        {
            "create stream view v1 as select id, name, time, age, ROW_NUMBER() over (PARTITION BY window_start, window_end, age) from "
            "table(hop(table t1, time, interval '10' seconds, interval '10' seconds)) with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"
        }
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 20;
    int64_t id[rowNum] = {0, 1, 3, 5, 10, 10, 11, 13, 10, 14, 15, 20, 20, 23, 25, 30, 30, 35, 22, 23};
    int64_t time[rowNum] = {0, 1, 3, 5, 10, 10, 11, 13, 10, 14, 15, 20, 20, 23, 25, 30, 30, 35, 22, 23};
    int64_t age[rowNum] = {10, 11, 12, 13, 10, 10, 11, 12, 10, 13, 14, 10, 10, 11, 12, 10, 10, 11, 12, 13};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, time, age);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 18;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {0, 1, 3, 5, 10, 10, 11, 13, 10, 14, 15, 20, 20, 23, 25, 30, 30, 35};
    int64_t expectTime[expectRowsCount] = {0, 1, 3, 5, 10, 10, 11, 13, 10, 14, 15, 20, 20, 23, 25, 30, 30, 35};
    int64_t expectAge[expectRowsCount] = {10, 11, 12, 13, 10, 10, 11, 12, 10, 13, 14, 10, 10, 11, 12, 10, 10, 11};
    int64_t expectLineNum[expectRowsCount] = {1, 1, 1, 1, 1, 2, 1, 1, 3, 1, 1, 1, 2, 1, 1, 1, 2, 1};
    RdCheckDataInTSTableOfOneOver(stmt, selectTsName, expectRowsCount, expectColsCount,
                                      expectId, expectTime, expectAge, expectLineNum);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop table ts1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建流表，定义tolerent watermark，再创建包含一个line_num、基于滑动步长(slide) > 窗口大小(size)的滑动窗口的流视图预期：成功
TEST_F(LineNum, STREAM_017_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name char(50), time integer, age integer, row_num integer) "
            "with (time_col = 'time', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer, "
            "watermark for time as time tolerant);"
        },
        {
            "create stream view v1 as select id, name, time, age, ROW_NUMBER() over (PARTITION BY window_start, window_end, age) from "
            "table(hop(table t1, time, interval '20' seconds, interval '10' seconds)) with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"
        }
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 18;
    int64_t id[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 20, 21, 7, 23, 25, 30, 8, 35, 22, 23};
    int64_t time[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 20, 21, 7, 23, 25, 30, 8, 35, 22, 23};
    int64_t age[rowNum] = {10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, time, age);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 8;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 3, 5, 20, 20, 21, 23, 25};
    int64_t expectTime[expectRowsCount] = {1, 3, 5, 20, 20, 21, 23, 25};
    int64_t expectAge[expectRowsCount] = {10, 11, 12, 10, 10, 12, 11, 12};
    int64_t expectLineNum[expectRowsCount] = {1, 1, 1, 1, 2, 1, 1, 2};
    RdCheckDataInTSTableOfOneOver(stmt, selectTsName, expectRowsCount, expectColsCount,
                                  expectId, expectTime, expectAge, expectLineNum);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop table ts1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建流表，定义tolerent watermark，再创建包含一个line_num、基于滑动步长(slide) < 窗口大小(size)的滑动窗口的流视图预期：成功
TEST_F(LineNum, STREAM_017_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name char(50), time integer, age integer, row_num integer) "
            "with (time_col = 'age', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer, "
            "watermark for time as time tolerant);"
        },
        {
            "create stream view v1 as select id, name, time, age, ROW_NUMBER() over (PARTITION BY window_start, window_end, age) from "
            "table(hop(table t1, time, interval '10' seconds, interval '20' seconds)) with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"
        }
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 20;
    int64_t id[rowNum] = {0, 1, 3, 5, 10, 10, 11, 13, 10, 14, 15, 20, 20, 23, 25, 30, 30, 35, 22, 23};
    int64_t time[rowNum] = {0, 1, 3, 5, 10, 10, 11, 13, 10, 14, 15, 20, 20, 23, 25, 30, 30, 35, 22, 23};
    int64_t age[rowNum] = {10, 11, 12, 13, 10, 10, 11, 12, 10, 13, 14, 10, 10, 11, 12, 10, 10, 11, 12, 13};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, time, age);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 38;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {
        0, 0, 1, 1, 3, 3, 5, 5, 10, 10, 10, 10, 11, 11, 13, 13, 10, 10, 14, 14,
         15, 15, 20, 20, 20, 20, 23, 23, 25, 25, 30, 30, 30, 30, 35, 35, 22, 23
    };
    int64_t expectTime[expectRowsCount] = {
        0, 0, 1, 1, 3, 3, 5, 5, 10, 10, 10, 10, 11, 11, 13, 13, 10, 10, 14, 14, 
        15, 15, 20, 20, 20, 20, 23, 23, 25, 25, 30, 30, 30, 30, 35, 35, 22, 23
    };
    int64_t expectAge[expectRowsCount] = {
        10, 10, 11, 11, 12, 12, 13, 13, 10, 10, 10, 10, 11, 11, 12, 12, 10, 10, 
        13, 13, 14, 14, 10, 10, 10, 10, 11, 11, 12, 12, 10, 10, 10, 10, 11, 11, 12, 13
    };
    int64_t expectLineNum[expectRowsCount] = {
        1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 3, 1, 2, 1, 2, 3, 4, 1, 
        2, 1, 1, 1, 4, 2, 5, 1, 2, 1, 2, 1, 3, 2, 4, 1, 2, 2, 1
    };
    RdCheckDataInTSTableOfOneOver(stmt, selectTsName, expectRowsCount, expectColsCount,
                                      expectId, expectTime, expectAge, expectLineNum);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop table ts1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建流表，定义tolerent watermark，再创建包含一个line_num、基于滑动步长(slide) = 窗口大小(size)的滑动窗口的流视图预期：成功
TEST_F(LineNum, STREAM_017_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name char(50), time integer, age integer, row_num integer) "
            "with (time_col = 'age', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer, "
            "watermark for time as time tolerant);"
        },
        {
            "create stream view v1 as select id, name, time, age, ROW_NUMBER() over (PARTITION BY window_start, window_end, age) from "
            "table(hop(table t1, time, interval '10' seconds, interval '10' seconds)) with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"
        }
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 20;
    int64_t id[rowNum] = {0, 1, 3, 5, 10, 10, 11, 13, 10, 14, 15, 20, 20, 23, 25, 30, 30, 35, 22, 23};
    int64_t time[rowNum] = {0, 1, 3, 5, 10, 10, 11, 13, 10, 14, 15, 20, 20, 23, 25, 30, 30, 35, 22, 23};
    int64_t age[rowNum] = {10, 11, 12, 13, 10, 10, 11, 12, 10, 13, 14, 10, 10, 11, 12, 10, 10, 11, 12, 13};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, time, age);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 18;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {0, 1, 3, 5, 10, 10, 11, 13, 10, 14, 15, 20, 20, 23, 25, 30, 30, 35};
    int64_t expectTime[expectRowsCount] = {0, 1, 3, 5, 10, 10, 11, 13, 10, 14, 15, 20, 20, 23, 25, 30, 30, 35};
    int64_t expectAge[expectRowsCount] = {10, 11, 12, 13, 10, 10, 11, 12, 10, 13, 14, 10, 10, 11, 12, 10, 10, 11};
    int64_t expectLineNum[expectRowsCount] = {1, 1, 1, 1, 1, 2, 1, 1, 3, 1, 1, 1, 2, 1, 1, 1, 2, 1};
    RdCheckDataInTSTableOfOneOver(stmt, selectTsName, expectRowsCount, expectColsCount,
                                      expectId, expectTime, expectAge, expectLineNum);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop table ts1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建流表，定义tolerent【有界乱序】watermark，再创建包含一个line_num、基于滚动窗口的流视图 预期：成功
TEST_F(LineNum, STREAM_017_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name char(50), time integer, age integer, row_num integer) "
            "with (time_col = 'age', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer, "
            "watermark for time as time - interval '5' seconds tolerant);"
        },
        {
            "create stream view v1 as select id, name, time, age, ROW_NUMBER() over (PARTITION BY window_start, window_end, age) from "
            "table(tumble(table t1, time, interval '10' seconds)) with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"
        }
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 17;
    int64_t id[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t time[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t age[rowNum] = {14, 13, 10, 11, 12, 14, 13, 14, 12, 14, 10, 10, 12, 15, 11, 12, 11};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, time, age);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 15;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 20, 23, 25, 30};
    int64_t expectTime[expectRowsCount] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 20, 23, 25, 30};
    int64_t expectAge[expectRowsCount] = {14, 13, 10, 11, 12, 14, 13, 14, 12, 14, 10, 12, 11, 12, 11};
    int64_t expectLineNum[expectRowsCount] = {1, 1, 1, 1, 1, 1, 1, 2, 2, 3, 1, 1, 1, 2, 1};
    RdCheckDataInTSTableOfOneOver(stmt, selectTsName, expectRowsCount, expectColsCount,
                                      expectId, expectTime, expectAge, expectLineNum);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop table ts1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建流表，定义strict【有界乱序】watermark，再创建包含一个line_num、基于滚动窗口的流视图 预期：成功
TEST_F(LineNum, STREAM_017_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name char(50), time integer, age integer, row_num integer) "
            "with (time_col = 'age', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer, "
            "watermark for time as time - interval '5' seconds strict);"
        },
        {
            "create stream view v1 as select id, name, time, age, ROW_NUMBER() over (PARTITION BY window_start, window_end, age) from "
            "table(tumble(table t1, time, interval '10' seconds)) with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"
        }
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 17;
    int64_t id[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t time[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t age[rowNum] = {14, 13, 10, 11, 12, 14, 13, 14, 12, 14, 10, 10, 12, 15, 11, 12, 11};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, time, age);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 14;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 16, 20, 23, 25, 30};
    int64_t expectTime[expectRowsCount] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 16, 20, 23, 25, 30};
    int64_t expectAge[expectRowsCount] = {14, 13, 10, 11, 12, 14, 13, 14, 12, 10, 12, 11, 12, 11};
    int64_t expectLineNum[expectRowsCount] = {1, 1, 1, 1, 1, 1, 1, 2, 2, 1, 1, 1, 2, 1};
    RdCheckDataInTSTableOfOneOver(stmt, selectTsName, expectRowsCount, expectColsCount,
                                      expectId, expectTime, expectAge, expectLineNum);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop table ts1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建流表，定义strict watermark，再创建包含一个line_num、基于滚动窗口的流视图 预期：成功
TEST_F(LineNum, STREAM_017_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name char(50), time integer, age integer, row_num integer) "
            "with (time_col = 'age', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer, "
            "watermark for time as time strict);"
        },
        {
            "create stream view v1 as select id, name, time, age, ROW_NUMBER() over (PARTITION BY window_start, window_end, age) from "
            "table(tumble(table t1, time, interval '10' seconds)) with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"
        }
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 20;
    int64_t id[rowNum] = {0, 1, 3, 5, 10, 10, 11, 13, 10, 14, 15, 20, 20, 23, 25, 30, 30, 35, 22, 23};
    int64_t time[rowNum] = {0, 1, 3, 5, 10, 10, 11, 13, 10, 14, 15, 20, 20, 23, 25, 30, 30, 35, 22, 23};
    int64_t age[rowNum] = {10, 11, 12, 13, 10, 10, 11, 12, 10, 13, 14, 10, 10, 11, 12, 10, 10, 11, 12, 13};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, time, age);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 18;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {0, 1, 3, 5, 10, 10, 11, 13, 10, 14, 15, 20, 20, 23, 25, 30, 30, 35};
    int64_t expectTime[expectRowsCount] = {0, 1, 3, 5, 10, 10, 11, 13, 10, 14, 15, 20, 20, 23, 25, 30, 30, 35};
    int64_t expectAge[expectRowsCount] = {10, 11, 12, 13, 10, 10, 11, 12, 10, 13, 14, 10, 10, 11, 12, 10, 10, 11};
    int64_t expectLineNum[expectRowsCount] = {1, 1, 1, 1, 1, 2, 1, 1, 3, 1, 1, 1, 2, 1, 1, 1, 2, 1};
    RdCheckDataInTSTableOfOneOver(stmt, selectTsName, expectRowsCount, expectColsCount,
                                      expectId, expectTime, expectAge, expectLineNum);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop table ts1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建流表，定义tolerent watermark，再创建包含一个line_num、基于滚动窗口的流视图 预期：成功
TEST_F(LineNum, STREAM_017_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name char(50), time integer, age integer, row_num integer) "
            "with (time_col = 'age', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer, "
            "watermark for time as time tolerant);"
        },
        {
            "create stream view v1 as select id, name, time, age, ROW_NUMBER() over (PARTITION BY window_start, window_end, age) from "
            "table(tumble(table t1, time, interval '10' seconds)) with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"
        }
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 20;
    int64_t id[rowNum] = {0, 1, 3, 5, 10, 10, 11, 13, 10, 14, 15, 20, 20, 23, 25, 30, 30, 35, 22, 23};
    int64_t time[rowNum] = {0, 1, 3, 5, 10, 10, 11, 13, 10, 14, 15, 20, 20, 23, 25, 30, 30, 35, 22, 23};
    int64_t age[rowNum] = {10, 11, 12, 13, 10, 10, 11, 12, 10, 13, 14, 10, 10, 11, 12, 10, 10, 11, 12, 13};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, time, age);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 18;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {0, 1, 3, 5, 10, 10, 11, 13, 10, 14, 15, 20, 20, 23, 25, 30, 30, 35};
    int64_t expectTime[expectRowsCount] = {0, 1, 3, 5, 10, 10, 11, 13, 10, 14, 15, 20, 20, 23, 25, 30, 30, 35};
    int64_t expectAge[expectRowsCount] = {10, 11, 12, 13, 10, 10, 11, 12, 10, 13, 14, 10, 10, 11, 12, 10, 10, 11};
    int64_t expectLineNum[expectRowsCount] = {1, 1, 1, 1, 1, 2, 1, 1, 3, 1, 1, 1, 2, 1, 1, 1, 2, 1};
    RdCheckDataInTSTableOfOneOver(stmt, selectTsName, expectRowsCount, expectColsCount,
                                      expectId, expectTime, expectAge, expectLineNum);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop table ts1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建流表，定义strict【有界乱序】watermark，再创建包含1个line_num的、partition by 关联多个不同列的、基于滚动窗口的流视图 预期：成功
TEST_F(LineNum, STREAM_017_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name char(50), time integer, age integer, row_num integer) "
            "with (time_col = 'age', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer, "
            "watermark for time as time - interval '5' seconds strict);"
        },
        {
            "create stream view v1 as select id, name, time, age, ROW_NUMBER() over (PARTITION BY window_start, window_end, age, time) from "
            "table(tumble(table t1, time, interval '10' seconds)) with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"
        }
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 17;
    int64_t id[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t time[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t age[rowNum] = {14, 13, 10, 11, 12, 14, 13, 14, 12, 14, 10, 10, 12, 15, 11, 12, 11};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, time, age);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 14;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 16, 20, 23, 25, 30};
    int64_t expectTime[expectRowsCount] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 16, 20, 23, 25, 30};
    int64_t expectAge[expectRowsCount] = {14, 13, 10, 11, 12, 14, 13, 14, 12, 10, 12, 11, 12, 11};
    int64_t expectLineNum[expectRowsCount] = {1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1};
    RdCheckDataInTSTableOfOneOver(stmt, selectTsName, expectRowsCount, expectColsCount,
                                      expectId, expectTime, expectAge, expectLineNum);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop table ts1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建流表，定义strict【有界乱序】watermark，再创建包含1个line_num的、partition by 关联多个不同列的,列顺序交换、基于滚动窗口的流视图 预期：成功
TEST_F(LineNum, STREAM_017_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name char(50), time integer, age integer, row_num integer) "
            "with (time_col = 'age', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer, "
            "watermark for time as time - interval '5' seconds strict);"
        },
        {
            "create stream view v1 as select id, name, time, age, ROW_NUMBER() over (PARTITION BY window_start, window_end, time, age) from "
            "table(tumble(table t1, time, interval '10' seconds)) with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"
        }
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 21;
    int64_t id[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 9, 15, 8, 16, 16, 9, 20, 20, 7, 23, 25, 30, 30};
    int64_t time[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 9, 15, 8, 16, 16, 9, 20, 20, 7, 23, 25, 30, 30};
    int64_t age[rowNum] = {14, 13, 10, 11, 12, 14, 13, 14, 14, 12, 14, 10, 10, 10, 12, 12, 15, 11, 12, 11, 11};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, time, age);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 18;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {0, 1, 3, 5, 10, 11, 13, 9, 9, 15, 16, 16, 20, 20, 23, 25, 30, 30};
    int64_t expectTime[expectRowsCount] = {0, 1, 3, 5, 10, 11, 13, 9, 9, 15, 16, 16, 20, 20, 23, 25, 30, 30};
    int64_t expectAge[expectRowsCount] = {14, 13, 10, 11, 12, 14, 13, 14, 14, 12, 10, 10, 12, 12, 11, 12, 11, 11};
    int64_t expectLineNum[expectRowsCount] = {1, 1, 1, 1, 1, 1, 1, 1, 2, 1, 1, 2, 1, 2, 1, 1, 1, 2};
    RdCheckDataInTSTableOfOneOver(stmt, selectTsName, expectRowsCount, expectColsCount,
                                      expectId, expectTime, expectAge, expectLineNum);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop table ts1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建流表，定义strict【有界乱序】watermark，再创建包含1个line_num的、partition by 关联多个相同列的、基于滚动窗口的流视图 预期：成功
TEST_F(LineNum, STREAM_017_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name char(50), time integer, age integer, row_num integer) "
            "with (time_col = 'age', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer, "
            "watermark for time as time - interval '5' seconds strict);"
        },
        {
            "create stream view v1 as select id, name, time, age, ROW_NUMBER() over (PARTITION BY window_start, window_end, age, age, age) from "
            "table(tumble(table t1, time, interval '10' seconds)) with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"
        }
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 17;
    int64_t id[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t time[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t age[rowNum] = {14, 13, 10, 11, 12, 14, 13, 14, 12, 14, 10, 10, 12, 15, 11, 12, 11};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, time, age);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 14;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 16, 20, 23, 25, 30};
    int64_t expectTime[expectRowsCount] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 16, 20, 23, 25, 30};
    int64_t expectAge[expectRowsCount] = {14, 13, 10, 11, 12, 14, 13, 14, 12, 10, 12, 11, 12, 11};
    int64_t expectLineNum[expectRowsCount] = {1, 1, 1, 1, 1, 1, 1, 2, 2, 1, 1, 1, 2, 1};
    RdCheckDataInTSTableOfOneOver(stmt, selectTsName, expectRowsCount, expectColsCount,
                                      expectId, expectTime, expectAge, expectLineNum);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop table ts1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建流表，定义strict【有界乱序】watermark，再创建包含1个line_num的、partition by 只有window_start、window_end参数、基于滚动窗口的流视图 预期：成功
TEST_F(LineNum, STREAM_017_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name char(50), time integer, age integer, row_num integer) "
            "with (time_col = 'age', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer, "
            "watermark for time as time - interval '5' seconds strict);"
        },
        {
            "create stream view v1 as select id, name, time, age, ROW_NUMBER() over (PARTITION BY window_start, window_end) from "
            "table(tumble(table t1, time, interval '10' seconds)) with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"
        }
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 17;
    int64_t id[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t time[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t age[rowNum] = {14, 13, 10, 11, 12, 14, 13, 14, 12, 14, 10, 10, 12, 15, 11, 12, 11};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, time, age);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 14;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 16, 20, 23, 25, 30};
    int64_t expectTime[expectRowsCount] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 16, 20, 23, 25, 30};
    int64_t expectAge[expectRowsCount] = {14, 13, 10, 11, 12, 14, 13, 14, 12, 10, 12, 11, 12, 11};
    int64_t expectLineNum[expectRowsCount] = {1, 2, 3, 4, 1, 2, 3, 5, 4, 5, 1, 2, 3, 1};
    RdCheckDataInTSTableOfOneOver(stmt, selectTsName, expectRowsCount, expectColsCount,
                                      expectId, expectTime, expectAge, expectLineNum);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop table ts1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建流表，定义strict【有界乱序】watermark，再创建包含1个line_num的、partition by window_start和window_end交换顺序、基于滚动窗口的流视图 预期：成功
TEST_F(LineNum, STREAM_017_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name char(50), time integer, age integer, row_num integer) "
            "with (time_col = 'age', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer, "
            "watermark for time as time - interval '5' seconds strict);"
        },
        {
            "create stream view v1 as select id, name, time, age, ROW_NUMBER() over (PARTITION BY window_end, window_start, age) from "
            "table(tumble(table t1, time, interval '10' seconds)) with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"
        }
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 17;
    int64_t id[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t time[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t age[rowNum] = {14, 13, 10, 11, 12, 14, 13, 14, 12, 14, 10, 10, 12, 15, 11, 12, 11};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, time, age);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 14;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 16, 20, 23, 25, 30};
    int64_t expectTime[expectRowsCount] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 16, 20, 23, 25, 30};
    int64_t expectAge[expectRowsCount] = {14, 13, 10, 11, 12, 14, 13, 14, 12, 10, 12, 11, 12, 11};
    int64_t expectLineNum[expectRowsCount] = {1, 1, 1, 1, 1, 1, 1, 2, 2, 1, 1, 1, 2, 1};
    RdCheckDataInTSTableOfOneOver(stmt, selectTsName, expectRowsCount, expectColsCount,
                                      expectId, expectTime, expectAge, expectLineNum);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop table ts1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建流表，定义strict watermark，再创建包含一个line_num、partition by的列在window_start、window_end前面、基于滚动窗口的流视图 预期：成功
TEST_F(LineNum, STREAM_017_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name char(50), time integer, age integer, row_num integer) "
            "with (time_col = 'age', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer, "
            "watermark for time as time - interval '5' seconds strict);"
        },
        {
            "create stream view v1 as select id, name, time, age, ROW_NUMBER() over (PARTITION BY age, window_start, window_end) from "
            "table(tumble(table t1, time, interval '10' seconds)) with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"
        }
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 17;
    int64_t id[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t time[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t age[rowNum] = {14, 13, 10, 11, 12, 14, 13, 14, 12, 14, 10, 10, 12, 15, 11, 12, 11};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, time, age);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 14;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 16, 20, 23, 25, 30};
    int64_t expectTime[expectRowsCount] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 16, 20, 23, 25, 30};
    int64_t expectAge[expectRowsCount] = {14, 13, 10, 11, 12, 14, 13, 14, 12, 10, 12, 11, 12, 11};
    int64_t expectLineNum[expectRowsCount] = {1, 1, 1, 1, 1, 1, 1, 2, 2, 1, 1, 1, 2, 1};
    RdCheckDataInTSTableOfOneOver(stmt, selectTsName, expectRowsCount, expectColsCount,
                                      expectId, expectTime, expectAge, expectLineNum);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop table ts1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建流表，定义strict watermark，再创建包含一个line_num、partition by的列在window_start、window_end中间、基于滚动窗口的流视图 预期：成功
TEST_F(LineNum, STREAM_017_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name char(50), time integer, age integer, row_num integer) "
            "with (time_col = 'age', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer, "
            "watermark for time as time - interval '5' seconds strict);"
        },
        {
            "create stream view v1 as select id, name, time, age, ROW_NUMBER() over (PARTITION BY window_start, age, window_end) from "
            "table(tumble(table t1, time, interval '10' seconds)) with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"
        }
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 17;
    int64_t id[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t time[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t age[rowNum] = {14, 13, 10, 11, 12, 14, 13, 14, 12, 14, 10, 10, 12, 15, 11, 12, 11};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, time, age);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 14;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 16, 20, 23, 25, 30};
    int64_t expectTime[expectRowsCount] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 16, 20, 23, 25, 30};
    int64_t expectAge[expectRowsCount] = {14, 13, 10, 11, 12, 14, 13, 14, 12, 10, 12, 11, 12, 11};
    int64_t expectLineNum[expectRowsCount] = {1, 1, 1, 1, 1, 1, 1, 2, 2, 1, 1, 1, 2, 1};
    RdCheckDataInTSTableOfOneOver(stmt, selectTsName, expectRowsCount, expectColsCount,
                                      expectId, expectTime, expectAge, expectLineNum);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop table ts1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建流表，定义strict【有界乱序】watermark，再创建包含1个line_num的、watermark和partition by关联同一列、基于滚动窗口的流视图 预期：成功
TEST_F(LineNum, STREAM_017_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name char(50), time integer, age integer, row_num integer) "
            "with (time_col = 'age', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer, "
            "watermark for time as time - interval '5' seconds strict);"
        },
        {
            "create stream view v1 as select id, name, time, age, ROW_NUMBER() over (PARTITION BY window_start, window_end, time) from "
            "table(tumble(table t1, time, interval '10' seconds)) with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"
        }
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 17;
    int64_t id[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t time[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t age[rowNum] = {14, 13, 10, 11, 12, 14, 13, 14, 12, 14, 10, 10, 12, 15, 11, 12, 11};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, time, age);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 14;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 16, 20, 23, 25, 30};
    int64_t expectTime[expectRowsCount] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 16, 20, 23, 25, 30};
    int64_t expectAge[expectRowsCount] = {14, 13, 10, 11, 12, 14, 13, 14, 12, 10, 12, 11, 12, 11};
    int64_t expectLineNum[expectRowsCount] = {1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1};
    RdCheckDataInTSTableOfOneOver(stmt, selectTsName, expectRowsCount, expectColsCount,
                                      expectId, expectTime, expectAge, expectLineNum);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop table ts1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// watermark列映射到TS表的时间列，向watermark列写入负数 预期：成功
TEST_F(LineNum, STREAM_017_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name char(50), time integer, age integer, row_num integer) "
            "with (time_col = 'time', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer, "
            "watermark for time as time - interval '5' seconds strict);"
        },
        {
            "create stream view v1 as select id, name, time, age, ROW_NUMBER() over (PARTITION BY window_start, window_end, age) from "
            "table(hop(table t1, time, interval '10' seconds, interval '20' seconds)) with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"
        }
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 19;
    int64_t id[rowNum] = {-5, -3, 0, 1, 3, 5, 10, 11, 13, 10, 14, 15, 20, 23, 25, 30, 35, 22, 23};
    int64_t time[rowNum] = {-5, -3, 0, 1, 3, 5, 10, 11, 13, 10, 14, 15, 20, 23, 25, 30, 35, 22, 23};
    int64_t age[rowNum] = {14, 13, 10, 11, 12, 13, 13, 12, 16, 10, 10, 12, 15, 11, 12, 11, 14, 13, 11};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, time, age);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 32;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {
        0,  0,  1,  1,  3,  3,  5,  5,  10, 10, 11, 11, 13, 13, 10, 10, 14,  
        14, 15, 15, 20, 20, 23, 23, 25, 25, 30, 30, 35, 35, 22, 23
    };
    int64_t expectTime[expectRowsCount] = {
        0,  0,  1,  1,  3,  3,  5,  5,  10, 10, 11, 11, 13, 13, 10, 10, 14,  
        14, 15, 15, 20, 20, 23, 23, 25, 25, 30, 30, 35, 35, 22, 23
    };
    int64_t expectAge[expectRowsCount] = {
        10, 10, 11, 11, 12, 12, 13, 13, 13, 13, 12, 12, 16, 16, 
        10, 10, 10, 10, 12, 12, 15, 15, 11, 11, 12, 12, 11, 11, 14, 14, 13, 11
    };
    int64_t expectLineNum[expectRowsCount] = {
        1, 1, 1, 1, 1, 1, 1, 2, 1, 2, 
        1, 2, 1, 1, 1, 2, 2, 3, 2, 3, 1, 1, 1, 1, 1, 3, 1, 2, 1, 1, 1, 3};
    RdCheckDataInTSTableOfOneOver(stmt, selectTsName, expectRowsCount, expectColsCount,
                                      expectId, expectTime, expectAge, expectLineNum);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop table ts1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建同时包含line_num函数和sum函数的、partition by 关联1个相同列的、基于滚动窗口的流视图 预期：成功
TEST_F(LineNum, STREAM_017_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name char(50), time integer, age integer, row_num integer) "
            "with (time_col = 'age', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer, "
            "watermark for time as time - interval '5' seconds strict);"
        },
        {
            "create stream view v1 as select id, name, time, sum(id) over (PARTITION BY window_start, window_end, age), "
            "ROW_NUMBER() over (PARTITION BY window_start, window_end, age) from "
            "table(tumble(table t1, time, interval '10' seconds)) with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"
        }
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 17;
    int64_t id[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t time[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t age[rowNum] = {14, 13, 10, 11, 12, 14, 13, 14, 12, 14, 10, 10, 12, 15, 11, 12, 11};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, time, age);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 10;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {0, 1, 3, 5, 9, 10, 11, 13, 15, 16};
    int64_t expectTime[expectRowsCount] = {0, 1, 3, 5, 9, 10, 11, 13, 15, 16};
    int64_t expectSum[expectRowsCount] = {9, 1, 3, 5, 9, 25, 11, 13, 25, 16};
    int64_t expectLineNum[expectRowsCount] = {1, 1, 1, 1, 2, 1, 1, 1, 2, 1};
    RdCheckDataInTSTableOfOneOverWithOrder(stmt, selectTsName, expectRowsCount, expectColsCount,
                                      expectId, expectTime, expectSum, expectLineNum);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop table ts1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建同时包含line_num函数和max函数的、partition by 关联1个相同列的、基于滚动窗口的流视图 预期：成功
TEST_F(LineNum, STREAM_017_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name char(50), time integer, age integer, row_num integer) "
            "with (time_col = 'age', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer, "
            "watermark for time as time - interval '5' seconds strict);"
        },
        {
            "create stream view v1 as select id, name, time, max(id) over (PARTITION BY window_start, window_end, age), "
            "ROW_NUMBER() over (PARTITION BY window_start, window_end, age) from "
            "table(tumble(table t1, time, interval '10' seconds)) with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"
        }
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 17;
    int64_t id[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t time[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t age[rowNum] = {14, 13, 10, 11, 12, 14, 13, 14, 12, 14, 10, 10, 12, 15, 11, 12, 11};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, time, age);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 10;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {0, 1, 3, 5, 9, 10, 11, 13, 15, 16};
    int64_t expectTime[expectRowsCount] = {0, 1, 3, 5, 9, 10, 11, 13, 15, 16};
    int64_t expectMax[expectRowsCount] = {9, 1, 3, 5, 9, 15, 11, 13, 15, 16};
    int64_t expectLineNum[expectRowsCount] = {1, 1, 1, 1, 2, 1, 1, 1, 2, 1};
    RdCheckDataInTSTableOfOneOverWithOrder(stmt, selectTsName, expectRowsCount, expectColsCount,
                                      expectId, expectTime, expectMax, expectLineNum);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop table ts1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建同时包含line_num函数和min函数的、partition by 关联1个相同列的、基于滚动窗口的流视图 预期：成功
TEST_F(LineNum, STREAM_017_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name char(50), time integer, age integer, row_num integer) "
            "with (time_col = 'age', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer, "
            "watermark for time as time - interval '5' seconds strict);"
        },
        {
            "create stream view v1 as select id, name, time, min(id) over (PARTITION BY window_start, window_end, age), "
            "ROW_NUMBER() over (PARTITION BY window_start, window_end, age) from "
            "table(tumble(table t1, time, interval '10' seconds)) with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"
        }
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 17;
    int64_t id[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t time[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t age[rowNum] = {14, 13, 10, 11, 12, 14, 13, 14, 12, 14, 10, 10, 12, 15, 11, 12, 11};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, time, age);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 10;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {0, 1, 3, 5, 9, 10, 11, 13, 15, 16};
    int64_t expectTime[expectRowsCount] = {0, 1, 3, 5, 9, 10, 11, 13, 15, 16};
    int64_t expectMin[expectRowsCount] = {0, 1, 3, 5, 0, 10, 11, 13, 10, 16};
    int64_t expectLineNum[expectRowsCount] = {1, 1, 1, 1, 2, 1, 1, 1, 2, 1};
    RdCheckDataInTSTableOfOneOverWithOrder(stmt, selectTsName, expectRowsCount, expectColsCount,
                                      expectId, expectTime, expectMin, expectLineNum);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop table ts1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建同时包含line_num函数和count函数的、partition by 关联1个相同列的、基于滚动窗口的流视图 预期：成功
TEST_F(LineNum, STREAM_017_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name char(50), time integer, age integer, row_num integer) "
            "with (time_col = 'age', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer, "
            "watermark for time as time - interval '5' seconds strict);"
        },
        {
            "create stream view v1 as select id, name, time, count(id) over (PARTITION BY window_start, window_end, age), "
            "ROW_NUMBER() over (PARTITION BY window_start, window_end, age) from "
            "table(tumble(table t1, time, interval '10' seconds)) with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"
        }
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 17;
    int64_t id[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t time[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t age[rowNum] = {14, 13, 10, 11, 12, 14, 13, 14, 12, 14, 10, 10, 12, 15, 11, 12, 11};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, time, age);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 10;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {0, 1, 3, 5, 9, 10, 11, 13, 15, 16};
    int64_t expectTime[expectRowsCount] = {0, 1, 3, 5, 9, 10, 11, 13, 15, 16};
    int64_t expectCount[expectRowsCount] = {2, 1, 1, 1, 2, 2, 1, 1, 2, 1};
    int64_t expectLineNum[expectRowsCount] = {1, 1, 1, 1, 2, 1, 1, 1, 2, 1};
    RdCheckDataInTSTableOfOneOverWithOrder(stmt, selectTsName, expectRowsCount, expectColsCount,
                                      expectId, expectTime, expectCount, expectLineNum);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop table ts1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建同时包含line_num函数和sum|count|max|min函数的、partition by 关联1个相同列的、基于滚动窗口的流视图 预期：成功
TEST_F(LineNum, STREAM_017_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name char(50), time integer, age integer, row_num integer) "
            "with (time_col = 'age', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer, "
            "watermark for time as time - interval '5' seconds strict);"
        },
        {
            "create stream view v1 as select id, name, "
            "min(id) over (PARTITION BY window_start, window_end, age), "
            "count(id) over (PARTITION BY window_start, window_end, age), "
            "ROW_NUMBER() over (PARTITION BY window_start, window_end, age) from "
            "table(tumble(table t1, time, interval '10' seconds)) with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"
        }
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 17;
    int64_t id[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t time[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t age[rowNum] = {14, 13, 10, 11, 12, 14, 13, 14, 12, 14, 10, 10, 12, 15, 11, 12, 11};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, time, age);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 10;
    uint32_t expectColsCount = 5;
    int64_t expectMax[expectRowsCount] = {0, 1, 3, 5, 9, 10, 11, 13, 15, 16};
    int64_t expectMin[expectRowsCount] = {0, 1, 3, 5, 0, 10, 11, 13, 10, 16};
    int64_t expectCount[expectRowsCount] = {2, 1, 1, 1, 2, 2, 1, 1, 2, 1};
    int64_t expectLineNum[expectRowsCount] = {1, 1, 1, 1, 2, 1, 1, 1, 2, 1};
    RdCheckDataInTSTableOfOneOverWithOrder(stmt, selectTsName, expectRowsCount, expectColsCount,
                                      expectMax, expectMin, expectCount, expectLineNum);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop table ts1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建同时包含line_num函数和sum函数的、partition by 关联多个相同列的、基于滚动窗口的流视图 预期：成功
TEST_F(LineNum, STREAM_017_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name char(50), time integer, age integer, row_num integer) "
            "with (time_col = 'age', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer, "
            "watermark for time as time - interval '5' seconds strict);"
        },
        {
            "create stream view v1 as select id, name, time, sum(id) over (PARTITION BY window_start, window_end, age, time), "
            "ROW_NUMBER() over (PARTITION BY window_start, window_end, age, time) from "
            "table(tumble(table t1, time, interval '10' seconds)) with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"
        }
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 17;
    int64_t id[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t time[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t age[rowNum] = {14, 13, 10, 11, 12, 14, 13, 14, 12, 14, 10, 10, 12, 15, 11, 12, 11};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, time, age);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 10;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {0, 1, 3, 5, 9, 10, 11, 13, 15, 16};
    int64_t expectTime[expectRowsCount] = {0, 1, 3, 5, 9, 10, 11, 13, 15, 16};
    int64_t expectSum[expectRowsCount] = {0, 1, 3, 5, 9, 10, 11, 13, 15, 16};
    int64_t expectLineNum[expectRowsCount] = {1, 1, 1, 1, 1, 1, 1, 1, 1, 1};
    RdCheckDataInTSTableOfOneOverWithOrder(stmt, selectTsName, expectRowsCount, expectColsCount,
                                      expectId, expectTime, expectSum, expectLineNum);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop table ts1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建同时包含line_num函数和max函数的、partition by 关联多个相同列的、基于滚动窗口的流视图 预期：成功
TEST_F(LineNum, STREAM_017_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name char(50), time integer, age integer, row_num integer) "
            "with (time_col = 'age', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer, "
            "watermark for time as time - interval '5' seconds strict);"
        },
        {
            "create stream view v1 as select id, name, time, max(id) over (PARTITION BY window_start, window_end, age, time), "
            "ROW_NUMBER() over (PARTITION BY window_start, window_end, age, time) from "
            "table(tumble(table t1, time, interval '10' seconds)) with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"
        }
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 17;
    int64_t id[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t time[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t age[rowNum] = {14, 13, 10, 11, 12, 14, 13, 14, 12, 14, 10, 10, 12, 15, 11, 12, 11};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, time, age);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 10;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {0, 1, 3, 5, 9, 10, 11, 13, 15, 16};
    int64_t expectTime[expectRowsCount] = {0, 1, 3, 5, 9, 10, 11, 13, 15, 16};
    int64_t expectMax[expectRowsCount] = {0, 1, 3, 5, 9, 10, 11, 13, 15, 16};
    int64_t expectLineNum[expectRowsCount] = {1, 1, 1, 1, 1, 1, 1, 1, 1, 1};
    RdCheckDataInTSTableOfOneOverWithOrder(stmt, selectTsName, expectRowsCount, expectColsCount,
                                      expectId, expectTime, expectMax, expectLineNum);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop table ts1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建同时包含line_num函数和min函数的、partition by 关联多个相同列的、基于滚动窗口的流视图 预期：成功
TEST_F(LineNum, STREAM_017_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name char(50), time integer, age integer, row_num integer) "
            "with (time_col = 'age', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer, "
            "watermark for time as time - interval '5' seconds strict);"
        },
        {
            "create stream view v1 as select id, name, time, min(id) over (PARTITION BY window_start, window_end, age, time), "
            "ROW_NUMBER() over (PARTITION BY window_start, window_end, age, time) from "
            "table(tumble(table t1, time, interval '10' seconds)) with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"
        }
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 17;
    int64_t id[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t time[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t age[rowNum] = {14, 13, 10, 11, 12, 14, 13, 14, 12, 14, 10, 10, 12, 15, 11, 12, 11};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, time, age);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 10;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {0, 1, 3, 5, 9, 10, 11, 13, 15, 16};
    int64_t expectTime[expectRowsCount] = {0, 1, 3, 5, 9, 10, 11, 13, 15, 16};
    int64_t expectMin[expectRowsCount] = {0, 1, 3, 5, 9, 10, 11, 13, 15, 16};
    int64_t expectLineNum[expectRowsCount] = {1, 1, 1, 1, 1, 1, 1, 1, 1, 1};
    RdCheckDataInTSTableOfOneOverWithOrder(stmt, selectTsName, expectRowsCount, expectColsCount,
                                      expectId, expectTime, expectMin, expectLineNum);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop table ts1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建同时包含line_num函数和count函数的、partition by 关联多个相同列的、基于滚动窗口的流视图 预期：成功
TEST_F(LineNum, STREAM_017_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name char(50), time integer, age integer, row_num integer) "
            "with (time_col = 'age', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer, "
            "watermark for time as time - interval '5' seconds strict);"
        },
        {
            "create stream view v1 as select id, name, time, count(id) over (PARTITION BY window_start, window_end, age, time), "
            "ROW_NUMBER() over (PARTITION BY window_start, window_end, age, time) from "
            "table(tumble(table t1, time, interval '10' seconds)) with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"
        }
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 17;
    int64_t id[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t time[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t age[rowNum] = {14, 13, 10, 11, 12, 14, 13, 14, 12, 14, 10, 10, 12, 15, 11, 12, 11};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, time, age);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 10;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {0, 1, 3, 5, 9, 10, 11, 13, 15, 16};
    int64_t expectTime[expectRowsCount] = {0, 1, 3, 5, 9, 10, 11, 13, 15, 16};
    int64_t expectCount[expectRowsCount] = {1, 1, 1, 1, 1, 1, 1, 1, 1, 1};
    int64_t expectLineNum[expectRowsCount] = {1, 1, 1, 1, 1, 1, 1, 1, 1, 1};
    RdCheckDataInTSTableOfOneOverWithOrder(stmt, selectTsName, expectRowsCount, expectColsCount,
                                      expectId, expectTime, expectCount, expectLineNum);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop table ts1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建同时包含line_num函数和sum|count|max|min函数的、partition by 关联多个相同列、基于滚动窗口的流视图 预期：成功
TEST_F(LineNum, STREAM_017_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name char(50), time integer, age integer, row_num integer) "
            "with (time_col = 'age', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer, "
            "watermark for time as time - interval '5' seconds strict);"
        },
        {
            "create stream view v1 as select id, name, "
            "min(id) over (PARTITION BY window_start, window_end, age, time), "
            "count(id) over (PARTITION BY window_start, window_end, age, time), "
            "ROW_NUMBER() over (PARTITION BY window_start, window_end, age, time) from "
            "table(tumble(table t1, time, interval '10' seconds)) with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"
        }
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 17;
    int64_t id[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t time[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t age[rowNum] = {14, 13, 10, 11, 12, 14, 13, 14, 12, 14, 10, 10, 12, 15, 11, 12, 11};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, time, age);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 10;
    uint32_t expectColsCount = 5;
    int64_t expectMax[expectRowsCount] = {0, 1, 3, 5, 9, 10, 11, 13, 15, 16};
    int64_t expectMin[expectRowsCount] = {0, 1, 3, 5, 9, 10, 11, 13, 15, 16};
    int64_t expectCount[expectRowsCount] = {1, 1, 1, 1, 1, 1, 1, 1, 1, 1};
    int64_t expectLineNum[expectRowsCount] = {1, 1, 1, 1, 1, 1, 1, 1, 1, 1};
    RdCheckDataInTSTableOfOneOverWithOrder(stmt, selectTsName, expectRowsCount, expectColsCount,
                                      expectMax, expectMin, expectCount, expectLineNum);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop table ts1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建同时包含line_num函数和sum|count|max|min函数的、partition by 关联多个相同列|乱序的、基于滚动窗口的流视图 预期：成功
TEST_F(LineNum, STREAM_017_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name char(50), time integer, age integer, row_num integer) "
            "with (time_col = 'age', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer, "
            "watermark for time as time - interval '5' seconds strict);"
        },
        {
            "create stream view v1 as select id, name, "
            "min(id) over (PARTITION BY window_start, window_end, time, age), "
            "count(id) over (PARTITION BY window_start, age, window_end, time), "
            "ROW_NUMBER() over (PARTITION BY age, window_start, window_end, time) from "
            "table(tumble(table t1, time, interval '10' seconds)) with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"
        }
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 17;
    int64_t id[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t time[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t age[rowNum] = {14, 13, 10, 11, 12, 14, 13, 14, 12, 14, 10, 10, 12, 15, 11, 12, 11};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, time, age);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 10;
    uint32_t expectColsCount = 5;
    int64_t expectMax[expectRowsCount] = {0, 1, 3, 5, 9, 10, 11, 13, 15, 16};
    int64_t expectMin[expectRowsCount] = {0, 1, 3, 5, 9, 10, 11, 13, 15, 16};
    int64_t expectCount[expectRowsCount] = {1, 1, 1, 1, 1, 1, 1, 1, 1, 1};
    int64_t expectLineNum[expectRowsCount] = {1, 1, 1, 1, 1, 1, 1, 1, 1, 1};
    RdCheckDataInTSTableOfOneOverWithOrder(stmt, selectTsName, expectRowsCount, expectColsCount,
                                      expectMax, expectMin, expectCount, expectLineNum);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop table ts1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建流表，定义strict【有界乱序】watermark，再创建包含一个line_num、基于滚动窗口的流视图，同一个窗口落入超20条数据 预期：成功
TEST_F(LineNum, STREAM_017_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name char(50), time integer, age integer, row_num integer) "
            "with (time_col = 'age', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer, "
            "watermark for time as time - interval '5' seconds strict);"
        },
        {
            "create stream view v1 as select id, name, time, age, ROW_NUMBER() over (PARTITION BY window_start, window_end, age) from "
            "table(tumble(table t1, time, interval '10' seconds)) with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"
        }
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 22;
    int64_t id[rowNum] = {0, 1, 3, 5, 6, 7, 8, 9, 0, 1, 3, 5, 6, 7, 8, 9, 6, 6, 7, 7, 8, 8};
    int64_t time[rowNum] = {0, 1, 3, 5, 6, 7, 8, 9, 0, 1, 3, 5, 6, 7, 8, 9, 6, 6, 7, 7, 8, 8};
    int64_t age[rowNum] = {0, 1, 3, 5, 6, 7, 8, 9, 0, 1, 3, 5, 6, 7, 8, 9, 6, 6, 7, 7, 8, 8};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, time, age);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 22;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {0, 1, 3, 5, 6, 7, 8, 9, 0, 1, 3, 5, 6, 7, 8, 9, 6, 6, 7, 7, 8, 8};
    int64_t expectTime[expectRowsCount] = {0, 1, 3, 5, 6, 7, 8, 9, 0, 1, 3, 5, 6, 7, 8, 9, 6, 6, 7, 7, 8, 8};
    int64_t expectAge[expectRowsCount] = {0, 1, 3, 5, 6, 7, 8, 9, 0, 1, 3, 5, 6, 7, 8, 9, 6, 6, 7, 7, 8, 8};
    int64_t expectLineNum[expectRowsCount] = {1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 2, 3, 4, 3, 4, 3, 4};
    RdCheckDataInTSTableOfOneOver(stmt, selectTsName, expectRowsCount, expectColsCount,
                                      expectId, expectTime, expectAge, expectLineNum);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop table ts1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建流表，定义tolerent【有界乱序】watermark，再创建包含一个关联定长字符串字段的line_num、基于滚动窗口的流视图 预期：成功
TEST_F(LineNum, STREAM_017_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name char(50), time integer, age integer, row_num integer) "
            "with (time_col = 'age', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name char(50), time integer, age integer, "
            "watermark for time as time - interval '5' seconds tolerant);"
        },
        {
            "create stream view v1 as select id, name, time, age, ROW_NUMBER() over (PARTITION BY window_start, window_end, name) from "
            "table(tumble(table t1, time, interval '10' seconds)) with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"
        }
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 17;
    int64_t id[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t time[rowNum] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 9, 20, 7, 23, 25, 30};
    int64_t age[rowNum] = {14, 13, 10, 11, 12, 14, 13, 14, 12, 14, 10, 10, 12, 15, 11, 12, 11};
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, id, time, age);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 15;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 20, 23, 25, 30};
    int64_t expectTime[expectRowsCount] = {0, 1, 3, 5, 10, 11, 13, 9, 15, 8, 16, 20, 23, 25, 30};
    int64_t expectAge[expectRowsCount] = {14, 13, 10, 11, 12, 14, 13, 14, 12, 14, 10, 12, 11, 12, 11};
    int64_t expectLineNum[expectRowsCount] = {1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1};
    RdCheckDataInTSTableOfOneOver(stmt, selectTsName, expectRowsCount, expectColsCount,
                                      expectId, expectTime, expectAge, expectLineNum);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop table ts1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}




