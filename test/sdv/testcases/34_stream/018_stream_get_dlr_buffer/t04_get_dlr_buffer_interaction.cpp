/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 通过接口直接填充DLR Buffer特性交互
 * Author: guopanpan
 * Create: 2024-11-12
 */
#include "gtest/gtest.h"
#include "gmc_sql.h"
#include "rd_feature_stream.h"
#include "rd_feature_ts.h"
#include "rd_stream_eighteen.h"

class t04_get_dlr_buffer_interaction : public testing::Test {
public:
    static GmcConnT *longConn;
    static GmcStmtT *longStmt;
    GmcConnT *shortConn;
    GmcStmtT *shortStmt;
    RdVertexLabelT *vertexLabel = NULL;

    bool hasFinishTest = false;
    void RdFinishTest();

    static void SetUpTestCase();
    static void TearDownTestCase();
    virtual void SetUp();
    virtual void TearDown();
};

GmcConnT *t04_get_dlr_buffer_interaction::longConn = NULL;
GmcStmtT *t04_get_dlr_buffer_interaction::longStmt = NULL;

void t04_get_dlr_buffer_interaction::SetUpTestCase()
{
    int32_t ret;
    ret = RdStreamEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdGmcConnect(&longConn, &longStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdCreateEpollThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void t04_get_dlr_buffer_interaction::TearDownTestCase()
{
    int32_t ret;
    ret = RdGmcDisconnect(longConn, longStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdStreamEnvClean();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdCloseEpollThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void t04_get_dlr_buffer_interaction::SetUp()
{
    int32_t ret;
    hasFinishTest = false;
    ret = RdGmcConnect(&shortConn, &shortStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void t04_get_dlr_buffer_interaction::TearDown()
{
    int32_t ret;
    if (!hasFinishTest) {
        RdStreamExecDescT clean[] = {
            {"drop stream sink s1;"},
            {"drop stream view v3;"},
            {"drop stream view v2;"},
            {"drop stream view v1;"},
            {"drop stream table t1;"},

            {"drop stream sink s2;"},
            {"drop stream view v6;"},
            {"drop stream view v5;"},
            {"drop stream view v4;"},
            {"drop stream table t2;"},

            {"drop table ts1;"},
        };
        ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;
    ret = RdGmcDisconnect(shortConn, shortStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void t04_get_dlr_buffer_interaction::RdFinishTest()
{
    hasFinishTest = true;
}

// @TestSuite 数据类型
// @TestcaseName 流表中仅定义integer，获取dlr data buffer	成功
TEST_F(t04_get_dlr_buffer_interaction, STREAM_018_025)
{
    // 创建测试表
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, time integer, level integer) "
            "with (time_col = 'time', interval = '1 hour');"},

        {"create stream table t1 (id integer, time integer, level integer, "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select * from v3 into pubsub_channel with (batch_window_size = 1);"},

        {"create stream table t2 (id integer, time integer, level integer, "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v4 as select * from t2 with (tuple_buffer_size = 1);"},
        {"create stream view v5 as select * from v4 with (tuple_buffer_size = 1);"},
        {"create stream view v6 as select * from v5 with (tuple_buffer_size = 1);"},
        {"create stream sink s2 as select * from v6 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 下发订阅
    RdEighteenSubUserDataT userData;
    GmcConnT *subConn = NULL;
    RdEighteenSubCfgT subCfg = {
        .subsName = "stream_sub",
        .sinkName = "s1",
        .userCb = RdEighteenGetDlrDataBuf,
        .userData = &userData,
    };
    ret = RdEighteenSubscribe(shortStmt, subCfg, &subConn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    const uint32_t nameLen = 50;
    const uint32_t contentLen = 120;
#pragma pack(1)
    struct RdInteractionData {
        int64_t id;
        int64_t time;
        int64_t level;
    };
#pragma pack()
    uint32_t rowNum = 200;
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < rowNum; i++) {
        struct RdInteractionData data = {
            .id = i,
            .time = labs(i), // 注意labs(INT64_MIN)的结算结果是负数
            .level = i % RD_STREAM_EIGHTEEN_MAX_LEVEL,
        };

        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, &data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 等待推送结束
    ret = RdEighteenWaitSnRecv(&userData, rowNum, 30);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(rowNum, userData.realFetchNum);

    // 取消订阅
    ret = RdEighteenUnSubscribe(shortStmt, subCfg.subsName, subConn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},

        {"drop stream sink s2;"},
        {"drop stream view v6;"},
        {"drop stream view v5;"},
        {"drop stream view v4;"},
        {"drop stream table t2;"},

        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdEighteenFreeDlrDataBuf(&userData);
    RdFinishTest();
}

// @TestcaseName 流表中仅定义integer和char，获取dlr data buffer	成功
TEST_F(t04_get_dlr_buffer_interaction, STREAM_018_026)
{
    // 创建测试表
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content char(120)) "
            "with (time_col = 'time', interval = '1 hour');"},

        {"create stream table t1 (id integer, name char(50), time integer, level integer, content char(120), "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select * from v3 into pubsub_channel with (batch_window_size = 1);"},

        {"create stream table t2 (id integer, name char(50), time integer, level integer, content char(120), "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v4 as select * from t2 with (tuple_buffer_size = 1);"},
        {"create stream view v5 as select * from v4 with (tuple_buffer_size = 1);"},
        {"create stream view v6 as select * from v5 with (tuple_buffer_size = 1);"},
        {"create stream sink s2 as select * from v6 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 下发订阅
    RdEighteenSubUserDataT userData;
    GmcConnT *subConn = NULL;
    RdEighteenSubCfgT subCfg = {
        .subsName = "stream_sub",
        .sinkName = "s1",
        .userCb = RdEighteenGetDlrDataBuf,
        .userData = &userData,
    };
    ret = RdEighteenSubscribe(shortStmt, subCfg, &subConn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    const uint32_t nameLen = 50;
    const uint32_t contentLen = 120;
#pragma pack(1)
    struct RdInteractionData {
        int64_t id;
        char name[nameLen];
        int64_t time;
        int64_t level;
        char content[contentLen];
    };
#pragma pack()
    uint32_t rowNum = 200;
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < rowNum; i++) {
        struct RdInteractionData data = {
            .id = i,
            .name = {0},
            .time = labs(i), // 注意labs(INT64_MIN)的结算结果是负数
            .level = i % RD_STREAM_EIGHTEEN_MAX_LEVEL,
            .content = {0},
        };
        (void)snprintf_s(data.name, nameLen, nameLen, "info_%0ld", i);
        (void)snprintf_s(data.content, contentLen, contentLen, "%0100ld", i);

        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, &data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 等待推送结束
    ret = RdEighteenWaitSnRecv(&userData, rowNum, 30);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(rowNum, userData.realFetchNum);

    // 取消订阅
    ret = RdEighteenUnSubscribe(shortStmt, subCfg.subsName, subConn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},

        {"drop stream sink s2;"},
        {"drop stream view v6;"},
        {"drop stream view v5;"},
        {"drop stream view v4;"},
        {"drop stream table t2;"},

        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdEighteenFreeDlrDataBuf(&userData);
    RdFinishTest();
}

// @TestcaseName 流表中仅定义integer和text，获取dlr data buffer	成功
TEST_F(t04_get_dlr_buffer_interaction, STREAM_018_027)
{
    // 创建测试表
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name text, time integer, level integer, content text) "
            "with (time_col = 'time', interval = '1 hour');"},

        {"create stream table t1 (id integer, name text, time integer, level integer, content text, "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select * from v3 into pubsub_channel with (batch_window_size = 1);"},

        {"create stream table t2 (id integer, name text, time integer, level integer, content text, "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v4 as select * from t2 with (tuple_buffer_size = 1);"},
        {"create stream view v5 as select * from v4 with (tuple_buffer_size = 1);"},
        {"create stream view v6 as select * from v5 with (tuple_buffer_size = 1);"},
        {"create stream sink s2 as select * from v6 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 下发订阅
    RdEighteenSubUserDataT userData;
    GmcConnT *subConn = NULL;
    RdEighteenSubCfgT subCfg = {
        .subsName = "stream_sub",
        .sinkName = "s1",
        .userCb = RdEighteenGetDlrDataBuf,
        .userData = &userData,
    };
    ret = RdEighteenSubscribe(shortStmt, subCfg, &subConn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    const uint32_t nameLen = 50;
    const uint32_t contentLen = 120;
#pragma pack(1)
    struct RdInteractionData {
        int64_t id;
        uint16_t nameLen;
        char *name;
        int64_t time;
        int64_t level;
        uint16_t contentLen;
        char *content;
    };
#pragma pack()
    uint32_t rowNum = 200;
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < rowNum; i++) {
        char *name = (char *)malloc(nameLen);
        AW_MACRO_ASSERT_NOTNULL(name);
        char *content = (char *)malloc(contentLen);
        AW_MACRO_ASSERT_NOTNULL(content);
        struct RdInteractionData data = {
            .id = i,
            .nameLen = nameLen,
            .name = name,
            .time = labs(i), // 注意labs(INT64_MIN)的结算结果是负数
            .level = i % RD_STREAM_EIGHTEEN_MAX_LEVEL,
            .contentLen = contentLen,
            .content = content,
        };
        (void)snprintf_s(data.name, nameLen, nameLen, "info_%0ld", i);
        (void)snprintf_s(data.content, contentLen, contentLen, "%0100ld", i);

        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, &data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        free(content);
    }

    // 等待推送结束
    ret = RdEighteenWaitSnRecv(&userData, rowNum, 30);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(rowNum, userData.realFetchNum);

    // 取消订阅
    ret = RdEighteenUnSubscribe(shortStmt, subCfg.subsName, subConn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},

        {"drop stream sink s2;"},
        {"drop stream view v6;"},
        {"drop stream view v5;"},
        {"drop stream view v4;"},
        {"drop stream table t2;"},

        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdEighteenFreeDlrDataBuf(&userData);
    RdFinishTest();
}

// @TestcaseName 流表中定义integer、char和text，获取dlr data buffer	成功
TEST_F(t04_get_dlr_buffer_interaction, STREAM_018_028)
{
    // 创建测试表
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text) "
            "with (time_col = 'time', interval = '1 hour');"},

        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select * from v3 into pubsub_channel with (batch_window_size = 1);"},

        {"create stream table t2 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v4 as select * from t2 with (tuple_buffer_size = 1);"},
        {"create stream view v5 as select * from v4 with (tuple_buffer_size = 1);"},
        {"create stream view v6 as select * from v5 with (tuple_buffer_size = 1);"},
        {"create stream sink s2 as select * from v6 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 下发订阅
    RdEighteenSubUserDataT userData;
    GmcConnT *subConn = NULL;
    RdEighteenSubCfgT subCfg = {
        .subsName = "stream_sub",
        .sinkName = "s1",
        .userCb = RdEighteenGetDlrDataBuf,
        .userData = &userData,
    };
    ret = RdEighteenSubscribe(shortStmt, subCfg, &subConn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    const uint32_t nameLen = 50;
    const uint32_t contentLen = 120;
#pragma pack(1)
    struct RdInteractionData {
        int64_t id;
        char name[nameLen];
        int64_t time;
        int64_t level;
        uint16_t contentLen;
        char *content;
    };
#pragma pack()
    uint32_t rowNum = 200;
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < rowNum; i++) {
        char *content = (char *)malloc(contentLen);
        AW_MACRO_ASSERT_NOTNULL(content);
        struct RdInteractionData data = {
            .id = i,
            .name = {0},
            .time = labs(i), // 注意labs(INT64_MIN)的结算结果是负数
            .level = i % RD_STREAM_EIGHTEEN_MAX_LEVEL,
            .contentLen = contentLen,
            .content = content,
        };
        (void)snprintf_s(data.name, nameLen, nameLen, "info_%0ld", i);
        (void)snprintf_s(data.content, contentLen, contentLen, "%0100ld", i);

        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, &data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        free(content);
    }

    // 等待推送结束
    ret = RdEighteenWaitSnRecv(&userData, rowNum, 30);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(rowNum, userData.realFetchNum);

    // 取消订阅
    ret = RdEighteenUnSubscribe(shortStmt, subCfg.subsName, subConn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},

        {"drop stream sink s2;"},
        {"drop stream view v6;"},
        {"drop stream view v5;"},
        {"drop stream view v4;"},
        {"drop stream table t2;"},

        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdEighteenFreeDlrDataBuf(&userData);
    RdFinishTest();
}

// @TestSuite sink
// @TestcaseName 设置sink的batch_window_size为1，写入1条数据，获取dlr data buffer	成功
TEST_F(t04_get_dlr_buffer_interaction, STREAM_018_029)
{
    // 创建测试表
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content char(120)) "
            "with (time_col = 'time', interval = '1 hour');"},

        {"create stream table t1 (id integer, name char(50), time integer, level integer, content char(120), "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select * from v3 into pubsub_channel with (batch_window_size = 1);"},

        {"create stream table t2 (id integer, name char(50), time integer, level integer, content char(120), "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v4 as select * from t2 with (tuple_buffer_size = 1);"},
        {"create stream view v5 as select * from v4 with (tuple_buffer_size = 1);"},
        {"create stream view v6 as select * from v5 with (tuple_buffer_size = 1);"},
        {"create stream sink s2 as select * from v6 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 下发订阅
    RdEighteenSubUserDataT userData;
    GmcConnT *subConn = NULL;
    RdEighteenSubCfgT subCfg = {
        .subsName = "stream_sub",
        .sinkName = "s1",
        .userCb = RdEighteenGetDlrDataBuf,
        .userData = &userData,
    };
    ret = RdEighteenSubscribe(shortStmt, subCfg, &subConn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    uint32_t rowNum = 1;
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < rowNum; i++) {
        RdEighteenDataT data = {0};
        RdEighteenSetData(i, 0, &data);
        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, &data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 等待推送结束
    ret = RdEighteenWaitSnRecv(&userData, rowNum, 30);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(rowNum, userData.realFetchNum);

    // 取消订阅
    ret = RdEighteenUnSubscribe(shortStmt, subCfg.subsName, subConn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},

        {"drop stream sink s2;"},
        {"drop stream view v6;"},
        {"drop stream view v5;"},
        {"drop stream view v4;"},
        {"drop stream table t2;"},

        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdEighteenFreeDlrDataBuf(&userData);
    RdFinishTest();
}

// @TestcaseName 设置sink的batch_window_size为1，写入多条数据，获取dlr data buffer	成功
TEST_F(t04_get_dlr_buffer_interaction, STREAM_018_030)
{
    // 创建测试表
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content char(120)) "
            "with (time_col = 'time', interval = '1 hour');"},

        {"create stream table t1 (id integer, name char(50), time integer, level integer, content char(120), "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select * from v3 into pubsub_channel with (batch_window_size = 1);"},

        {"create stream table t2 (id integer, name char(50), time integer, level integer, content char(120), "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v4 as select * from t2 with (tuple_buffer_size = 1);"},
        {"create stream view v5 as select * from v4 with (tuple_buffer_size = 1);"},
        {"create stream view v6 as select * from v5 with (tuple_buffer_size = 1);"},
        {"create stream sink s2 as select * from v6 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 下发订阅
    RdEighteenSubUserDataT userData;
    GmcConnT *subConn = NULL;
    RdEighteenSubCfgT subCfg = {
        .subsName = "stream_sub",
        .sinkName = "s1",
        .userCb = RdEighteenGetDlrDataBuf,
        .userData = &userData,
    };
    ret = RdEighteenSubscribe(shortStmt, subCfg, &subConn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    uint32_t rowNum = 128;
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < rowNum; i++) {
        RdEighteenDataT data = {0};
        RdEighteenSetData(i, 0, &data);
        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, &data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 等待推送结束
    ret = RdEighteenWaitSnRecv(&userData, rowNum, 30);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(rowNum, userData.realFetchNum);

    // 取消订阅
    ret = RdEighteenUnSubscribe(shortStmt, subCfg.subsName, subConn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},

        {"drop stream sink s2;"},
        {"drop stream view v6;"},
        {"drop stream view v5;"},
        {"drop stream view v4;"},
        {"drop stream table t2;"},

        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdEighteenFreeDlrDataBuf(&userData);
    RdFinishTest();
}

// @TestcaseName 设置sink的batch_window_size为128，写入128条数据，获取dlr data buffer	成功，部分数据可能不推送
TEST_F(t04_get_dlr_buffer_interaction, STREAM_018_031)
{
    // 创建测试表
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content char(120)) "
            "with (time_col = 'time', interval = '1 hour');"},

        {"create stream table t1 (id integer, name char(50), time integer, level integer, content char(120), "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select * from v3 into pubsub_channel with (batch_window_size = 128);"},

        {"create stream table t2 (id integer, name char(50), time integer, level integer, content char(120), "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v4 as select * from t2 with (tuple_buffer_size = 1);"},
        {"create stream view v5 as select * from v4 with (tuple_buffer_size = 1);"},
        {"create stream view v6 as select * from v5 with (tuple_buffer_size = 1);"},
        {"create stream sink s2 as select * from v6 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 下发订阅
    RdEighteenSubUserDataT userData;
    GmcConnT *subConn = NULL;
    RdEighteenSubCfgT subCfg = {
        .subsName = "stream_sub",
        .sinkName = "s1",
        .userCb = RdEighteenGetDlrDataBuf,
        .userData = &userData,
    };
    ret = RdEighteenSubscribe(shortStmt, subCfg, &subConn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    uint32_t rowNum = 128;
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < rowNum; i++) {
        RdEighteenDataT data = {0};
        RdEighteenSetData(i, 0, &data);
        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, &data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 等待推送结束
    ret = RdEighteenWaitSnRecv(&userData, rowNum, 30);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(rowNum, userData.realFetchNum);

    // 取消订阅
    ret = RdEighteenUnSubscribe(shortStmt, subCfg.subsName, subConn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},

        {"drop stream sink s2;"},
        {"drop stream view v6;"},
        {"drop stream view v5;"},
        {"drop stream view v4;"},
        {"drop stream table t2;"},

        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdEighteenFreeDlrDataBuf(&userData);
    RdFinishTest();
}

// @TestcaseName 设置sink的batch_window_size为128，写入多条数据，获取dlr data buffer	成功
TEST_F(t04_get_dlr_buffer_interaction, STREAM_018_032)
{
    // 创建测试表
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content char(120)) "
            "with (time_col = 'time', interval = '1 hour');"},

        {"create stream table t1 (id integer, name char(50), time integer, level integer, content char(120), "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select * from v3 into pubsub_channel with (batch_window_size = 128);"},

        {"create stream table t2 (id integer, name char(50), time integer, level integer, content char(120), "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v4 as select * from t2 with (tuple_buffer_size = 1);"},
        {"create stream view v5 as select * from v4 with (tuple_buffer_size = 1);"},
        {"create stream view v6 as select * from v5 with (tuple_buffer_size = 1);"},
        {"create stream sink s2 as select * from v6 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 下发订阅
    RdEighteenSubUserDataT userData;
    GmcConnT *subConn = NULL;
    RdEighteenSubCfgT subCfg = {
        .subsName = "stream_sub",
        .sinkName = "s1",
        .userCb = RdEighteenGetDlrDataBuf,
        .userData = &userData,
    };
    ret = RdEighteenSubscribe(shortStmt, subCfg, &subConn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    uint32_t rowNum = 180;
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < rowNum; i++) {
        RdEighteenDataT data = {0};
        RdEighteenSetData(i, 0, &data);
        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, &data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 等待推送结束，推送128条数据，其他数据未触发推送
    ret = RdEighteenWaitSnRecv(&userData, rowNum, 5);
    AW_MACRO_ASSERT_EQ_INT(RD_FAILED, ret);
    AW_MACRO_ASSERT_EQ_INT(128, userData.realFetchNum);

    // 取消订阅
    ret = RdEighteenUnSubscribe(shortStmt, subCfg.subsName, subConn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},

        {"drop stream sink s2;"},
        {"drop stream view v6;"},
        {"drop stream view v5;"},
        {"drop stream view v4;"},
        {"drop stream table t2;"},

        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdEighteenFreeDlrDataBuf(&userData);
    RdFinishTest();
}

// @TestcaseName 设置sink的batch_window_size为50000，写入50000条数据，获取dlr data buffer	成功
TEST_F(t04_get_dlr_buffer_interaction, STREAM_018_033)
{
    // 创建测试表
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content char(120)) "
            "with (time_col = 'time', interval = '1 hour');"},

        {"create stream table t1 (id integer, name char(50), time integer, level integer, content char(120), "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select * from v3 into pubsub_channel with (batch_window_size = 5000);"},

        {"create stream table t2 (id integer, name char(50), time integer, level integer, content char(120), "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v4 as select * from t2 with (tuple_buffer_size = 1);"},
        {"create stream view v5 as select * from v4 with (tuple_buffer_size = 1);"},
        {"create stream view v6 as select * from v5 with (tuple_buffer_size = 1);"},
        {"create stream sink s2 as select * from v6 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 下发订阅
    RdEighteenSubUserDataT userData;
    GmcConnT *subConn = NULL;
    RdEighteenSubCfgT subCfg = {
        .subsName = "stream_sub",
        .sinkName = "s1",
        .userCb = RdEighteenGetDlrDataBuf,
        .userData = &userData,
    };
    ret = RdEighteenSubscribe(shortStmt, subCfg, &subConn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    uint32_t rowNum = 5000;
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < rowNum; i++) {
        RdEighteenDataT data = {0};
        RdEighteenSetData(i, 0, &data);
        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, &data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 等待推送结束
    ret = RdEighteenWaitSnRecv(&userData, rowNum, 30);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(rowNum, userData.realFetchNum);

    // 取消订阅
    ret = RdEighteenUnSubscribe(shortStmt, subCfg.subsName, subConn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},

        {"drop stream sink s2;"},
        {"drop stream view v6;"},
        {"drop stream view v5;"},
        {"drop stream view v4;"},
        {"drop stream table t2;"},

        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdEighteenFreeDlrDataBuf(&userData);
    RdFinishTest();
}

// @TestcaseName 设置sink的batch_window_size为50000，写入多条数据，获取dlr data buffer	成功，部分数据可能不推送
TEST_F(t04_get_dlr_buffer_interaction, STREAM_018_034)
{
    // 创建测试表
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content char(120)) "
            "with (time_col = 'time', interval = '1 hour');"},

        {"create stream table t1 (id integer, name char(50), time integer, level integer, content char(120), "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select * from v3 into pubsub_channel with (batch_window_size = 5000);"},

        {"create stream table t2 (id integer, name char(50), time integer, level integer, content char(120), "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v4 as select * from t2 with (tuple_buffer_size = 1);"},
        {"create stream view v5 as select * from v4 with (tuple_buffer_size = 1);"},
        {"create stream view v6 as select * from v5 with (tuple_buffer_size = 1);"},
        {"create stream sink s2 as select * from v6 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 下发订阅
    RdEighteenSubUserDataT userData;
    GmcConnT *subConn = NULL;
    RdEighteenSubCfgT subCfg = {
        .subsName = "stream_sub",
        .sinkName = "s1",
        .userCb = RdEighteenGetDlrDataBuf,
        .userData = &userData,
    };
    ret = RdEighteenSubscribe(shortStmt, subCfg, &subConn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    uint32_t rowNum = 5100;
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < rowNum; i++) {
        RdEighteenDataT data = {0};
        RdEighteenSetData(i, 0, &data);
        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, &data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 等待推送结束，推送5000条数据，其他数据未触发推送
    ret = RdEighteenWaitSnRecv(&userData, rowNum, 5);
    AW_MACRO_ASSERT_EQ_INT(RD_FAILED, ret);
    AW_MACRO_ASSERT_EQ_INT(5000, userData.realFetchNum);

    // 取消订阅
    ret = RdEighteenUnSubscribe(shortStmt, subCfg.subsName, subConn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},

        {"drop stream sink s2;"},
        {"drop stream view v6;"},
        {"drop stream view v5;"},
        {"drop stream view v4;"},
        {"drop stream table t2;"},

        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdEighteenFreeDlrDataBuf(&userData);
    RdFinishTest();
}
