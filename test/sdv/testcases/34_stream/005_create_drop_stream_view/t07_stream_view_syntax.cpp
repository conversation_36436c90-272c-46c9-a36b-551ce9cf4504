/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 创建和删除流视图语法测试
 * Author: guopanpan
 * Create: 2024-09-23
 */
#include "gtest/gtest.h"
#include "gmc_sql.h"
#include "rd_feature_stream.h"

class t07_stream_view_syntax : public testing::Test {
public:
    static GmcConnT *longConn;
    static GmcStmtT *longStmt;
    GmcConnT *shortConn;
    GmcStmtT *shortStmt;
    GmcStmtT *streamStmt = NULL;
    GmcStmtT *tsStmt = NULL;

    bool hasFinishTest = false;
    void RdFinishTest();

    static void SetUpTestCase();
    static void TearDownTestCase();
    virtual void SetUp();
    virtual void TearDown();
};

GmcConnT *t07_stream_view_syntax::longConn = NULL;
GmcStmtT *t07_stream_view_syntax::longStmt = NULL;

void t07_stream_view_syntax::SetUpTestCase()
{
    int32_t ret;
    ret = RdStreamEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdGmcConnect(&longConn, &longStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void t07_stream_view_syntax::TearDownTestCase()
{
    int32_t ret;
    ret = RdGmcDisconnect(longConn, longStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdStreamEnvClean();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void t07_stream_view_syntax::SetUp()
{
    int32_t ret;
    hasFinishTest = false;
    ret = RdGmcConnect(&shortConn, &shortStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcAllocStmt(shortConn, &streamStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint32_t modelStream = GMC_MODEL_STREAM;
    ret = GmcSetStmtAttr(streamStmt, GMC_STMT_ATTR_MODEL_TYPE, &modelStream, sizeof(modelStream));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcAllocStmt(shortConn, &tsStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint32_t modelTs = GMC_MODEL_TS;
    ret = GmcSetStmtAttr(tsStmt, GMC_STMT_ATTR_MODEL_TYPE, &modelTs, sizeof(modelTs));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void t07_stream_view_syntax::TearDown()
{
    int32_t ret;
    if (!hasFinishTest) {
        RdStreamExecDescT desc[] = {
            {"drop stream sink sink1;"},
            {"drop stream view view1;"},
            {"drop stream table stream_table1;"},
            {"drop table ts_table1;", GMC_MODEL_TS},
        };
        ret = RdStreamDropMetadata(longStmt, desc, sizeof(desc));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    GmcFreeStmt(streamStmt);
    GmcFreeStmt(tsStmt);
    ret = RdGmcDisconnect(shortConn, shortStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void t07_stream_view_syntax::RdFinishTest()
{
    hasFinishTest = true;
}

// @TestSuite 大小写敏感
// @TestcaseName STREAM_005_050	创建视图，STREAM SQL均为大写字符	成功
TEST_F(t07_stream_view_syntax, STREAM_005_050)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT testDesc[] = {
        {"CREATE STREAM TABLE STREAM_TABLE1 (ID INTEGER, NAME CHAR(50));"},

        {"CREATE STREAM VIEW VIEW1 AS SELECT * FROM STREAM_TABLE1 WITH (TUPLE_BUFFER_SIZE = 15);"},
        {"DROP STREAM VIEW VIEW1;"},
        {"CREATE STREAM VIEW VIEW1 AS SELECT ID, NAME FROM STREAM_TABLE1 WITH (TUPLE_BUFFER_SIZE = 15);"},
        {"DROP STREAM VIEW VIEW1;"},

        {"DROP STREAM TABLE STREAM_TABLE1;"},
    };
    ret = RdStreamExecSql(shortStmt, testDesc, sizeof(testDesc));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdFinishTest();
}

// @TestcaseName STREAM_005_051	创建视图，STREAM SQL均为小写字符	成功
TEST_F(t07_stream_view_syntax, STREAM_005_051)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT testDesc[] = {
        {"create stream table stream_table1 (id integer, name char(50));"},

        {"create stream view view1 as select * from stream_table1 with (tuple_buffer_size = 15);"},
        {"drop stream view view1;"},
        {"create stream view view1 as select id, name from stream_table1 with (tuple_buffer_size = 15);"},
        {"drop stream view view1;"},

        {"drop stream table stream_table1;"},
    };
    ret = RdStreamExecSql(shortStmt, testDesc, sizeof(testDesc));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdFinishTest();
}

// @TestcaseName STREAM_005_052	创建视图，STREAM SQL混合大小写字符	成功
TEST_F(t07_stream_view_syntax, STREAM_005_052)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT testDesc[] = {
        {"CREATE STREAM TABLE STREAM_TABLE1 (ID INTEGER, NAME CHAR(50));"},

        {"CReAtE stREAM VieW view1 As selECT * FrOM STReAm_TabLE1 WitH (tUPLe_BUffER_sIZE = 15);"},
        {"drOP sTReAM viEW VIew1;"},
        {"CReAtE stREAM VieW view1 As selECT Id,naMe FrOM STReAm_TabLE1 WitH (tUPLe_BUffER_sIZE = 15);",},
        {"DroP STreAM ViEW VieW1;"},

        {"DROP STREAM TABLE STREAM_TABLE1;"},
    };
    ret = RdStreamExecSql(shortStmt, testDesc, sizeof(testDesc));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdFinishTest();
}

// @TestcaseName STREAM_005_053	创建名称为大写字符的视图，再创建名称为相同小写字符的视图	报错，对象已存在
TEST_F(t07_stream_view_syntax, STREAM_005_053)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT testDesc[] = {
        {"create stream table stream_table1 (id integer, name char(50));"},

        {"create stream view VIEW1 as select * from stream_table1 with (tuple_buffer_size = 15);"},
        {
            "create stream view view1 as select * from stream_table1 with (tuple_buffer_size = 15);",
            GMC_MODEL_STREAM, GMERR_DUPLICATE_TABLE
        },
        {"drop stream view VIEW1;"},

        {"drop stream table stream_table1;"},
    };
    ret = RdStreamExecSql(shortStmt, testDesc, sizeof(testDesc));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdFinishTest();
}

// @TestcaseName STREAM_005_054	创建名称为小写字符的视图，再创建名称为相同大写字符的视图	报错，对象已存在
TEST_F(t07_stream_view_syntax, STREAM_005_054)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT testDesc[] = {
        {"create stream table stream_table1 (id integer, name char(50));"},

        {"create stream view view1 as select * from stream_table1 with (tuple_buffer_size = 15);"},
        {
            "create stream view VIEW1 as select * from stream_table1 with (tuple_buffer_size = 15);",
            GMC_MODEL_STREAM, GMERR_DUPLICATE_TABLE
        },
        {"drop stream view view1;"},

        {"drop stream table stream_table1;"},
    };
    ret = RdStreamExecSql(shortStmt, testDesc, sizeof(testDesc));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdFinishTest();
}

// @TestcaseName STREAM_005_055	创建名称为小写字符的流表，再创建名称为相同大写字符的视图	报错，对象已存在
TEST_F(t07_stream_view_syntax, STREAM_005_055)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT testDesc[] = {
        {"create stream table stream_table1 (id integer, name char(50));"},

        {
            "create stream view STREAM_TABLE1 as select * from stream_table1 with (tuple_buffer_size = 15);",
            GMC_MODEL_STREAM, GMERR_DUPLICATE_TABLE
        },
        {"drop stream view STREAM_TABLE1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},

        {"drop stream table stream_table1;"},
    };
    ret = RdStreamExecSql(shortStmt, testDesc, sizeof(testDesc));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdFinishTest();
}

// @TestcaseName STREAM_005_056	创建名称为小写字符的SINK，再创建名称为相同大写字符的视图	报错，对象已存在
TEST_F(t07_stream_view_syntax, STREAM_005_056)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT testDesc[] = {
        {"create table ts_table1 (id integer, name char(50)) with (time_col = 'id', interval = '1 hour');", GMC_MODEL_TS},
        {"create stream table stream_table1 (id integer, name char(50));"},
        {"create stream view view1 as select * from stream_table1 with (tuple_buffer_size = 15);"},
        {"create stream sink sink1 as select * from view1 into tsdb (ts_table1);"},

        {
            "create stream view SINK1 as select * from stream_table1 with (tuple_buffer_size = 15);",
            GMC_MODEL_STREAM, GMERR_DUPLICATE_TABLE,
        },

        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream_table1;"},
        {"drop table ts_table1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(shortStmt, testDesc, sizeof(testDesc));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdFinishTest();
}

// @TestcaseName STREAM_005_057	创建名称为小写字符的时序表，再创建名称为相同大写字符的视图	报错，对象已存在
TEST_F(t07_stream_view_syntax, STREAM_005_057)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT testDesc[] = {
        {"create table ts_table1 (id integer, name char(50)) with (time_col = 'id', interval = '1 hour');", GMC_MODEL_TS},
        {"create stream table stream_table1 (id integer, name char(50));"},
        {"create stream view view1 as select * from stream_table1 with (tuple_buffer_size = 15);"},
        {"create stream sink sink1 as select * from view1 into tsdb (ts_table1);"},
        {"drop stream sink sink1;"},

        {
            "create stream view TS_TABLE1 as select * from stream_table1 with (tuple_buffer_size = 15);",
            GMC_MODEL_STREAM, GMERR_DUPLICATE_TABLE,
        },
        {"drop stream view TS_TABLE1;", GMC_MODEL_STREAM, GMERR_FEATURE_NOT_SUPPORTED},

        {"drop stream view view1;"},
        {"drop stream table stream_table1;"},
        {"drop table ts_table1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(shortStmt, testDesc, sizeof(testDesc));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdFinishTest();
}

// @TestSuite 关键字错误
// @TestcaseName STREAM_005_058	创建视图，STREAM SQL中关键字拼写错误	报错，语法错误
TEST_F(t07_stream_view_syntax, STREAM_005_058)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT testDesc[] = {
        {"create stream table t1 (id integer, name char(50));"},

        {
            "creete stream view v1 as select * from t1 with (tuple_buffer_size = 15);",
            GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR
        },
        {
            "create steam view v1 as select * from t1 with (tuple_buffer_size = 15);",
            GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR
        },
        {
            "create stream viaw v1 as select * from t1 with (tuple_buffer_size = 15);",
            GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR
        },
        {
            "create stream view v1 az select * from t1 with (tuple_buffer_size = 15);",
            GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR
        },
        {
            "create stream view v1 as selct * from t1 with (tuple_buffer_size = 15);",
            GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR
        },
        {
            "create stream view v1 as select * frem t1 with (tuple_buffer_size = 15);",
            GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR
        },
        {
            "create stream view v1 as select * from t1 withy (tuple_buffer_size = 15);",
            GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR
        },
        {
            "create stream view v1 as select * from t1 with (tuple_buff_size = 15);",
            GMC_MODEL_STREAM, GMERR_FEATURE_NOT_SUPPORTED
        },
        {
            "create stream view v1 as select * from t1 with (tuple_buffer_size = six);",
            GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR
        },

        {"drep stream view v1;", GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR},
        {"drop stram view v1;", GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR},
        {"drop stream viw v1;", GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR},

        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(shortStmt, testDesc, sizeof(testDesc));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdFinishTest();
}

// @TestSuite 关键字缺失
// @TestcaseName STREAM_005_059	创建视图，STREAM SQL中关键字缺失	报错，语法错误
TEST_F(t07_stream_view_syntax, STREAM_005_059)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT testDesc[] = {
        {"create stream table t1 (id integer, name char(50));"},

        {
            "stream view v1 as select * from t1 with (tuple_buffer_size = 15);",
            GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR
        },
        {
            "create view v1 as select * from t1 with (tuple_buffer_size = 15);",
            GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR
        },
        {
            "create stream v1 as select * from t1 with (tuple_buffer_size = 15);",
            GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR
        },
        {
            "create stream view v1 select * from t1 with (tuple_buffer_size = 15);",
            GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR
        },
        {
            "create stream view v1 as * from t1 with (tuple_buffer_size = 15);",
            GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR
        },
        {
            "create stream view v1 as select * t1 with (tuple_buffer_size = 15);",
            GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR
        },
        {
            "create stream view v1 as select * from t1 (tuple_buffer_size = 15);",
            GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR
        },
        {
            "create stream view v1 as select * from t1 with tuple_buffer_size = 15);",
            GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR
        },
        {
            "create stream view v1 as select * from t1 with (= 15);",
            GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR
        },
        {
            "create stream view v1 as select * from t1 with (tuple_buffer_size 15);",
            GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR
        },
        {
            "create stream view v1 as select * from t1 with (tuple_buffer_size = );",
            GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR
        },
        {
            "create stream view v1 as select * from t1 with (tuple_buffer_size = 15;",
            GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR
        },
        {
            "create stream view v1 as select * from t1 with (tuple_buffer_size = 15)",
            GMC_MODEL_STREAM, GMERR_OK
        },
        {"drop stream view v1", GMC_MODEL_STREAM, GMERR_OK},

        {"stream view v1;", GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR},
        {"drop view v1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream v1;", GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR},

        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(shortStmt, testDesc, sizeof(testDesc));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdFinishTest();
}

// @TestcaseName STREAM_005_060	创建视图，STREAM SQL中视图名缺失	报错，语法错误
TEST_F(t07_stream_view_syntax, STREAM_005_060)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT testDesc[] = {
        {"create stream table t1 (id integer, name char(50));"},

        {
            "create stream view as select * from t1 with (tuple_buffer_size = 15);",
            GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR
        },
        {
            "create stream view v1 as select * from with (tuple_buffer_size = 15);",
            GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR
        },

        {"drop stream view;", GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR},

        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(shortStmt, testDesc, sizeof(testDesc));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdFinishTest();
}

// @TestcaseName STREAM_005_061	创建视图，STREAM SQL中列名缺失	报错，语法错误
TEST_F(t07_stream_view_syntax, STREAM_005_061)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT testDesc[] = {
        {"create stream table t1 (id integer, name char(50));"},

        {
            "create stream view v1 as select from t1 with (tuple_buffer_size = 15);",
            GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR
        },

        {"drop stream table t1;"},
    };
    ret = RdStreamExecSql(shortStmt, testDesc, sizeof(testDesc));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdFinishTest();
}
