/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: 流计算format测试加固
 * Author: moxiaotong
 * Create: 2024-04-17
 */
#include "gtest/gtest.h"
#include "securec.h"
#include "gmc_sql.h"
#include "rd_feature_stream.h"
#include "text_util.h"

class format_feature : public testing::Test {
private:
    char socketAddr1[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
    int32_t ret1 = TestGetResultCommand("cat pathConfig.txt |grep socketAddr1 |awk -F '[:]' '{print $2}' |sed 's/ //g'",
        NULL, socketAddr1, sizeof(socketAddr1));
    char socketAddr2[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
    int32_t ret2 = TestGetResultCommand("cat pathConfig.txt |grep socketAddr2 |awk -F '[:]' '{print $2}' |sed 's/ //g'",
        NULL, socketAddr2, sizeof(socketAddr2));
    const vector<string> sockFileLists = {socketAddr1, socketAddr2};

public:
    static GmcConnT *conn;
    static GmcStmtT *stmt;
    bool hasFinishTest = false;
    void RdFinishTest();
    static void SetUpTestCase();
    static void TearDownTestCase();
    virtual void SetUp();
    virtual void TearDown();
};

GmcConnT *format_feature::conn = NULL;
GmcStmtT *format_feature::stmt = NULL;

void format_feature::SetUpTestCase()
{
    int32_t ret;
    ret = RdStreamEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void format_feature::TearDownTestCase()
{
    int32_t ret;
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RdStreamEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void format_feature::SetUp()
{
    hasFinishTest = false;
    int32_t ret;
    // 移除环境中的sock文件
    sockEnvInit(sockFileLists);
    ret = RdGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void format_feature::TearDown()
{
    int32_t ret;
    // 检查删sink时是否成功删除sock文件
    checkSockFileNotExist(sockFileLists);
    if (!hasFinishTest) {
        RdStreamExecDescT drops[] = {
            {"drop table ts1;"},
            {"drop stream sink sink1;"},
            {"drop stream view view1;"},
            {"drop stream table stream1;"},
        };
        ret = RdStreamExecSql(stmt, drops, sizeof(drops));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = RdGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void format_feature::RdFinishTest()
{
    hasFinishTest = true;
}

// 036对默认值字段进行格式化
// 	成功
TEST_F(format_feature, STREAM_041_036)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, name char(50), address char(50)) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer DEFAULT 412, age integer, name char(50), address "
         "char(50));"},
        {"create stream view view1 AS select * FROM stream1;"},
        {"create stream sink sink1 AS select id, age, name, format('My id is %d', id) "
         "FROM view1 INTO tsdb(ts1) with (batch_window_size = '1')"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    AW_MACRO_EXPECT_NE_INT((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 4;
    int64_t event_time[rowNum] = {0, 1, 2, 3, 4};
    bool nullInfo[colNum] = {true, false, false, false};
    RdStructWriteStreamTableF(stmt, vertexLabel, rowNum, event_time, nullInfo);

    // 校验ts表中数据
    char *sqlCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = 5;
    uint32_t expColNum = 4;
    int64_t ids = 412;
    char *expectAddress = (char *)"My id is 412";
    RdCheckDataInTSTableOfStreamTableF(stmt, sqlCmd, expRowNum, expColNum, ids, event_time, expectAddress);
    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 对默认填充hostname列进行格式化
// 	成功
TEST_F(format_feature, STREAM_041_037)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, name char(50), address char(50)) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer, age integer, "
         "name char(50) DEFAULT get_hostname(), address char(50));"},
        {"create stream view view1 AS select * FROM stream1;"},
        {"create stream sink sink1 AS select id, age, name, format('My hostname is %s', name) "
         "FROM view1 INTO tsdb(ts1) with (batch_window_size = '1')"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    AW_MACRO_EXPECT_NE_INT((RdVertexLabelT *)NULL, vertexLabel);
    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 4;
    int64_t id[rowNum] = {0, 1, 2, 3, 4};
    int64_t event_time[rowNum] = {10, 11, 12, 13, 14};
    bool nullInfo[colNum] = {false, false, true, false};
    RdStructWriteStreamTableF2(stmt, vertexLabel, rowNum, id, event_time, nullInfo);
    // 校验ts表中数据
    char *sqlCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = 5;
    uint32_t expColNum = 4;
    char expHostname[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
    gethostname(expHostname, RD_STREAM_TABLE_T1_NAME_SIZE);
    RdCheckDataInTSTableOfStreamTableF2(stmt, sqlCmd, expRowNum, expColNum, id, event_time, expHostname);
    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 对默认填充时间列进行格式化
// 	成功
TEST_F(format_feature, STREAM_041_038)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, name char(50), address char(50)) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer, time integer DEFAULT current_time_second(), "
         "name char(50), address char(50));"},
        {"create stream view view1 AS select * FROM stream1;"},
        {"create stream sink sink1 AS select id, time, name, format('Now time is %d', time) "
         "FROM view1 INTO tsdb(ts1) with (batch_window_size = '1')"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    AW_MACRO_EXPECT_NE_INT((RdVertexLabelT *)NULL, vertexLabel);
    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 4;
    int64_t id[rowNum] = {0, 1, 2, 3, 4};
    bool nullInfo[colNum] = {false, true, false, false};
    RdStructWriteStreamTableF3(stmt, vertexLabel, rowNum, id, nullInfo);
    // get system time
    struct timeval tv;
    gettimeofday(&tv, NULL);
    // 校验ts表中数据
    char *sqlCmd = (char *)"select * from ts1 order by id;";
    uint32_t expRowNum = rowNum;
    uint32_t expColNum = colNum;
    uint32_t timeDiff = 3;
    RdCheckDataInTSTableOfStreamTableF3(stmt, sqlCmd, expRowNum, expColNum, id, tv.tv_sec, timeDiff);
    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 对format函数直接进行over聚合
// 	报错
TEST_F(format_feature, STREAM_041_039)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, name char(50), address char(50)) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (age integer, time integer, name char(50), address char(50), "
         "WATERMARK FOR time AS time);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *create = (char *)"create stream view view1 AS select *, sum(format('My id is %d', id)) over(partition by "
                           "window_start, window_end) FROM "
                           "TABLE(HOP(TABLE stream1, time, INTERVAL '5' SECONDS, INTERVAL '5' SECONDS));";
    ret = GmcExecDirect(stmt, create, strlen(create));
    AW_MACRO_EXPECT_EQ_INT(GMERR_SEMANTIC_ERROR, ret);
    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 包含format的view，其前驱流表通过文件注入
// 	成功
TEST_F(format_feature, STREAM_041_040)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    system("sudo rm -f /dev/mydevice");
    char fileName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
    ret = TestGetResultCommand(
        "cat pathConfig.txt |grep fileName |awk -F '[:]' '{print $2}' |sed 's/ //g'", NULL, fileName, sizeof(fileName));
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    char command[STREAM_REFERENCE_MAX_SQL_LEN];
    ret = sprintf_s(command, STREAM_REFERENCE_MAX_SQL_LEN,
        "create stream table stream1(file char(50), water_mark integer DEFAULT 223, time integer DEFAULT 143, address "
        "char(50) DEFAULT 'a') from fileGroup "
        "('%s')",
        fileName);
    // 创建命名管道
    if (mkfifo(fileName, 0666) == -1) {
        perror("mkfifo");
        std::cerr << "Error creating FIFO '" << fileName << "': " << strerror(errno) << std::endl;
    }
    system("sudo chmod 0666 /dev/mydevice");
    int g_fd = open(fileName, O_RDWR, 0666);
    if (g_fd == -1) {
        perror("Error opening file");
        std::cerr << "errno: " << errno << std::endl;
    }
    ret = GmcExecDirect(stmt, command, STREAM_REFERENCE_MAX_SQL_LEN);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    RdStreamExecDescT creates[] = {
        {"create table ts1 (name char(50), id integer, time integer, address char(50)) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream view view1 AS select file, water_mark, time, format('My time is %d', time) FROM stream1;"},
        {"create stream sink sink1 AS select * FROM view1 into tsdb(ts1) with "
         "(batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(1);
    // 准备写入内容
    for (int32_t i = 0; i < 5; ++i) {
        char data[1024] = {0};
        ret = sprintf_s(data, 1024, "Cat_%03d\r", i);
        ssize_t bytes_written = write(g_fd, data, strlen(data));
        EXPECT_NE(bytes_written, -1);
    }
    close(g_fd);
    // 等待数据落盘，最长等待180s
    char *qryCmd = (char *)"select * from ts1;";
    uint32_t expRowNum = 5;
    uint32_t rowsCount = 0;
    uint32_t time = 0;
    while (rowsCount < expRowNum && time < 180) {
        ret = GmcExecDirect(stmt, qryCmd, strlen(qryCmd));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowsCount, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        sleep(1);
        ++time;
    }
    AW_MACRO_ASSERT_EQ_INT(expRowNum, rowsCount);
    // 校验ts表中数据
    qryCmd = (char *)"select * from ts1 order by name;";
    uint32_t expColNum = 4;
    char expFileContent[50][50] = {0};
    for (int32_t i = 0; i < 5; ++i) {
        ret = sprintf_s(expFileContent[i], 50, "Cat_%03d", i);
    }
    int64_t ids = 223;
    int64_t times = 143;
    char expectDept[RD_STREAM_TABLE_T1_NAME_SIZEF] = "My time is ";
    RdCheckDataInTSTableOfStreamTableF4(stmt, qryCmd, expRowNum, expColNum, expFileContent, ids, times, expectDept);
    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 创建写入socket的sink，投影字段包含format
// 	成功
TEST_F(format_feature, STREAM_041_041)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    RdStreamExecDescT creates[] = {
        {"create stream table stream1 (age integer , id integer, name char(50), address char(50));"},
        {"create stream view view1 AS select * FROM stream1;"},
        {"create stream sink sink1 AS select id, age, name, format('My name is %d', id) FROM view1 "
         "INTO SERVER_SOCKET('/tmp/stream_unix_sock1.sock') with (batch_window_size = '1')"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 连接server socket
    char socketAddr[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
    ret = TestGetResultCommand("cat pathConfig.txt |grep socketAddr1 |awk -F '[:]' '{print $2}' |sed 's/ //g'", NULL,
        socketAddr, sizeof(socketAddr));
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    int32_t socketFd = ConnectSinkSocket(socketAddr);
    sleep(2);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[0].sql);
    AW_MACRO_EXPECT_NE_INT((RdVertexLabelT *)NULL, vertexLabel);
    int64_t rowNum = 200;
    // 结构化写入数据
    RdStructWriteStreamTableF4(stmt, vertexLabel, rowNum);
    // 检查数据
    char buf[65535] = {0};
    read(socketFd, buf, 65535);
    char expectBuf[7000] = {0};
    for (int i = 0; i < rowNum; i++) {
        char sub[100] = {0};
        (void)sprintf(sub, "%d,%d,name_%d,My name is %d", i, i, i, i);
        strcat(expectBuf, sub);
    }
    AW_MACRO_EXPECT_EQ_INT(0, strcmp(buf, expectBuf));
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    // 关闭client socket连接
    close(socketFd);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 直接使用format函数作为比较运算符左表达式
// 报错
TEST_F(format_feature, STREAM_041_042)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, name char(100), address char(100)) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer, age integer, name char(100), address "
         "char(100));"},
        {"create stream view view1 AS select * FROM stream1;"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *create =
        (char *)"create stream sink sink1 AS select id, age, name, format('My name is %d', id) "
                "FROM view1 where format('My name is %d', id) > 1 INTO tsdb(ts1) with (batch_window_size = '1')";
    ret = GmcExecDirect(stmt, create, strlen(create));
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 对format函数使用like操作符
// 报错
TEST_F(format_feature, STREAM_041_043)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, name char(100), address char(100)) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer, age integer, name char(100), address "
         "char(100));"},
        {"create stream view view1 AS select * FROM stream1;"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *create = (char *)"create stream sink sink1 AS select id, age, name, format('My name is %d', id) "
                           "FROM view1 where format('My name is %d', id) like '%name%' INTO tsdb(ts1) with "
                           "(batch_window_size = '1')";
    ret = GmcExecDirect(stmt, create, strlen(create));
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 使用format函数作为in/not in 的左表达式
// 报错
TEST_F(format_feature, STREAM_041_044)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, name char(100), address char(100)) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer, age integer, name char(100), address "
         "char(100));"},
        {"create stream view view1 AS select * FROM stream1;"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *create = (char *)"create stream sink sink1 AS select id, age, name, format('My name is %d', id) "
                           "FROM view1 where format('My name is %d', id) not in ('aa', 'bb', 'cc') INTO tsdb(ts1) with "
                           "(batch_window_size = '1')";
    ret = GmcExecDirect(stmt, create, strlen(create));
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);

    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 包含dispatch by 的视图同时投影format函数
// 	成功
TEST_F(format_feature, STREAM_041_045)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, name char(50), address char(50)) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (age integer, id integer, name char(50), address char(50));"},
        {"create stream view view1 AS select id, age, name, format('My name is %d', id) FROM stream1"
         " DISPATCH BY id;"},
        {"create stream sink sink1 AS select * FROM view1 INTO tsdb(ts1) with (batch_window_size = '1')"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    AW_MACRO_EXPECT_NE_INT((RdVertexLabelT *)NULL, vertexLabel);
    // 结构化写入数据
    int64_t rowNum = 200;
    RdStructWriteStreamTableF4(stmt, vertexLabel, rowNum);
    // 查询和校验时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = rowNum;
    uint32_t expectColsCount = 4;
    char expectDept[RD_STREAM_TABLE_T1_NAME_SIZEF] = "My name is ";
    RdCheckDataInTSTableOfStreamTableF5(stmt, selectTsName, expectRowsCount, expectColsCount, expectDept);
    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 包含From dispatch的视图/sink同时投影format函数
// 	成功
TEST_F(format_feature, STREAM_041_046)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, name char(50), address char(50)) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (age integer, id integer, name char(50), address char(50)) DISPATCH BY id;"},
        {"create stream sink sink1 AS select age, id, name, format('My name is %d', id) FROM table(dispatch(table "
         "stream1, 10)) "
         "INTO tsdb(ts1) with (batch_window_size = '1')"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    AW_MACRO_EXPECT_NE_INT((RdVertexLabelT *)NULL, vertexLabel);
    uint32_t rowNum = 200;
    uint32_t ids = 10;
    // 结构化写入数据
    RdStructWriteStreamTableF4(stmt, vertexLabel, rowNum);
    // 查询和校验时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 1;
    uint32_t expectColsCount = 4;
    char expectDept[RD_STREAM_TABLE_T1_NAME_SIZEF] = "My name is ";
    RdCheckDataInTSTableOfStreamTableF6(stmt, selectTsName, expectRowsCount, expectColsCount, ids, expectDept);
    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream sink sink1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 对包含format投影的view/sink进行union
// 	成功
TEST_F(format_feature, STREAM_041_047)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, name char(50), address char(50)) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (age integer, id integer, name char(50), address char(50));"},
        {"create stream view view1 AS select age, id, name, format('My name is %d', id) FROM stream1;"},
        {"create stream view view2 AS select age, id, name, format('My name is %d', id) FROM stream1;"},
        {"create stream sink sink1 AS select * FROM TABLE(UNION(TABLE view1, TABLE view2)) into tsdb(ts1) with "
         "(batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    AW_MACRO_EXPECT_NE_INT((RdVertexLabelT *)NULL, vertexLabel);
    uint32_t rowNum = 200;
    // 结构化写入数据
    RdStructWriteStreamTableF4(stmt, vertexLabel, rowNum);
    // 查询和校验时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = rowNum * 2;
    uint32_t expectColsCount = 4;
    char expectDept[RD_STREAM_TABLE_T1_NAME_SIZEF] = "My name is ";
    RdCheckDataInTSTableOfStreamTableF7(stmt, selectTsName, expectRowsCount, expectColsCount, expectDept);
    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream view view2;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// alter union一个包含format函数投影列的view
// 	成功
TEST_F(format_feature, STREAM_041_048)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, name char(50), address char(50)) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (age integer, id integer, name char(50), address char(50));"},
        {"create stream view view1 AS select age, id, name, format('My id is %d', id) FROM stream1;"},
        {"create stream view view2 AS select age, id, name, format('My id is %d', id) FROM stream1;"},
        {"create stream sink sink1 AS select * FROM TABLE(UNION(TABLE view1, TABLE view2)) into tsdb(ts1) with "
         "(batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *alterCmd = "alter stream sink sink1 alter from_union drop view2;";
    ret = GmcExecDirect(stmt, alterCmd, strlen(alterCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    AW_MACRO_EXPECT_NE_INT((RdVertexLabelT *)NULL, vertexLabel);
    // 结构化写入数据
    uint32_t rowNum = 200;
    RdStructWriteStreamTableF4(stmt, vertexLabel, rowNum);
    // 查询和校验时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = rowNum;
    uint32_t expectColsCount = 4;
    char expectDept[RD_STREAM_TABLE_T1_NAME_SIZEF] = "My id is ";
    RdCheckDataInTSTableOfStreamTableF5(stmt, selectTsName, expectRowsCount, expectColsCount, expectDept);

    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream view view2;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// alter where 的条件左表达式为format
// 失败
TEST_F(format_feature, STREAM_041_049)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, name char(100), address char(100)) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer, age integer, name char(100), address "
         "char(100));"},
        {"create stream view view1 AS select * FROM stream1;"},
        {"create stream sink sink1 AS select id, age, name, format('My id is %d', id) "
         "FROM view1 INTO tsdb(ts1) with (batch_window_size = '1')"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *create = (char *)"alter stream sink sink1 alter where as format('My id is %d', id) < 5;";
    ret = GmcExecDirect(stmt, create, strlen(create));
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// format函数值长度超过时序表定义的长度
// 失败
TEST_F(format_feature, STREAM_041_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret;
    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, age integer, name char(300), address char(300)) with (time_col = 'id', "
         "interval= '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer, age integer, name char(300), address char(300));"},
        {"create stream view view1 as select id, age, name, address from stream1;"},
        {"create stream sink sink1 as SELECT id, age, name, format('this is the information:%100d, %50d, %100s, %50s', "
         "id, age, name, address) "
         "from view1 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    AW_MACRO_EXPECT_NE_INT((RdVertexLabelT *)NULL, vertexLabel);
    // 结构化写入数据
    uint32_t rowNum = 200;
    RdStructWriteStreamTableF4(stmt, vertexLabel, rowNum);
    // 查询和校验时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = rowNum;
    uint32_t expectColsCount = 4;
    char expectDept[RD_STREAM_TABLE_T1_NAME_SIZEF] = "this is the information:";
    RdCheckDataInTSTableOfStreamTableF8(stmt, selectTsName, expectRowsCount, expectColsCount, expectDept);
    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "test end.");
    RdFinishTest();
}

// where(and、or)条件包含format
// 	报错
TEST_F(format_feature, STREAM_041_051)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, name char(100), address char(100)) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer, age integer, name char(100), address "
         "char(100));"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *create = (char *)"create stream view view1 AS select id, age, name, "
                           "format('My id is %d', id) FROM stream1 where format('My id is %d', id) "
                           "!= 5 or format('My id is %d', id) > 50;";
    ret = GmcExecDirect(stmt, create, strlen(create));
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 窗口视图/sink 包含format
// 	成功
TEST_F(format_feature, STREAM_041_052)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, name char(50), address char(50)) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer, time integer, name char(50), address char(50), "
         "WATERMARK FOR time AS time - interval '5' seconds strict);"},
        {"create stream view view1 AS select id, time, name, "
         "format('this is my idcard %d', id) FROM TABLE(HOP(TABLE stream1, time, "
         "INTERVAL '5' SECONDS, INTERVAL '5' SECONDS)) with (tuple_buffer_size = 1);"},
        {"create stream sink sink1 AS select * FROM view1 INTO tsdb(ts1) with (batch_window_size = '1')"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    AW_MACRO_EXPECT_NE_INT((RdVertexLabelT *)NULL, vertexLabel);
    // 结构化写入数据
    int64_t rowNum = 200;
    RdStructWriteStreamTableF4(stmt, vertexLabel, rowNum);
    // 查询和校验时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = rowNum;
    uint32_t expectColsCount = 4;
    char expectDept[RD_STREAM_TABLE_T1_NAME_SIZEF] = "this is my idcard ";
    RdCheckDataInTSTableOfStreamTableF5(stmt, selectTsName, expectRowsCount, expectColsCount, expectDept);
    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// format参数包含常量
// 报错
TEST_F(format_feature, STREAM_041_053)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, name char(100), address char(100)) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer, age integer, name char(100), address "
         "char(100));"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *create = (char *)"create stream view view1 AS select id, age, name, "
                           "format(5) FROM stream1;";
    ret = GmcExecDirect(stmt, create, strlen(create));
    AW_MACRO_EXPECT_EQ_INT(GMERR_SEMANTIC_ERROR, ret);
    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// format参数为空值
// 报错
TEST_F(format_feature, STREAM_041_054)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, name char(100), address char(100)) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer, age integer, name char(100), address "
         "char(100));"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *create = (char *)"create stream view view1 AS select id, age, name, "
                           "format() FROM stream1;";
    ret = GmcExecDirect(stmt, create, strlen(create));
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// format参数包含某一列，不带%d等，例如format(id)
// 报错
TEST_F(format_feature, STREAM_041_055)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, name char(100), address char(100)) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer, age integer, name char(100), address "
         "char(100));"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *create = (char *)"create stream view view1 AS select id, age, name, "
                           "format(name) FROM stream1;";
    ret = GmcExecDirect(stmt, create, strlen(create));
    AW_MACRO_EXPECT_EQ_INT(GMERR_SEMANTIC_ERROR, ret);
    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 056format算子参数为字符串，如format('helloworld')
// 	成功
TEST_F(format_feature, STREAM_041_056)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, name char(50), address char(50)) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (age integer, id integer, name char(50), address char(50));"},
        {"create stream view view1 AS select id, age, name, format('helloTom') FROM stream1;"},
        {"create stream sink sink1 AS select * FROM view1 INTO tsdb(ts1) with (batch_window_size = '1')"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    AW_MACRO_EXPECT_NE_INT((RdVertexLabelT *)NULL, vertexLabel);
    // 结构化写入数据
    int64_t rowNum = 200;
    RdStructWriteStreamTableF4(stmt, vertexLabel, rowNum);
    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expRowNum = rowNum;
    uint32_t expColNum = 4;
    char expectDept[RD_STREAM_TABLE_T1_NAME_SIZEF] = "helloTom";
    RdCheckDataInTSTableOfStreamTableF9(stmt, selectTsName, expRowNum, expColNum, expectDept);
    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}
