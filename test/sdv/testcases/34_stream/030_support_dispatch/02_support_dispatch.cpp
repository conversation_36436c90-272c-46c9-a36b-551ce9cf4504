#include "gtest/gtest.h"
#include "securec.h"
#include "gmc_sql.h"
#include "t_rd_sn.h"
#include "rd_feature_stream.h"
#include "rd_feature_ts.h"

#include "dispatch_util.h"


class SupportDispatch : public testing::Test {
public:
    static GmcConnT *conn;
    static GmcStmtT *stmt;

    bool hasFinishTest = false;
    void RdFinishTest();

    static void SetUpTestCase();
    static void TearDownTestCase();
    virtual void SetUp();
    virtual void TearDown();
};

GmcConnT *SupportDispatch::conn = NULL;
GmcStmtT *SupportDispatch::stmt = NULL;

void SupportDispatch::SetUpTestCase()
{
    int32_t ret;
    ret = RdStreamEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void SupportDispatch::TearDownTestCase()
{
    int32_t ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = RdStreamEnvClean();
    EXPECT_EQ(GMERR_OK, ret);
}

void SupportDispatch::SetUp()
{   
    // 创建客户端连接
    int32_t ret = RdGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void SupportDispatch::TearDown()
{
    int32_t ret = RdGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 创建包含Dispatch by的流表，后继为包含Dispatch By、包含From Dispatch的普通视图，后继为包含Dispatch By、包含From Dispatch的普通视图，后继为包含From Dispatch的sink 预期：成功
TEST_F(SupportDispatch, STREAM_030_Test_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id,time;"
        },
        {
            "create stream view v1 as select * from table(dispatch(table t1, 1, 2)) "
            "DISPATCH BY id,time,age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select * from table(dispatch(table v1, 1, 2, 18)) "
            "DISPATCH BY id,time,age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from table(dispatch(table v2, 1, 2, 18)) "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 15;
    int64_t id[rowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 5, 5, 5, 5, 5};
    int64_t time[rowNum] = {2, 2, 2, 2, 2, 6, 6, 6, 6, 6, 8, 8, 8, 8, 8};
    int64_t age[rowNum] = {18, 18, 13, 13, 15, 15, 15, 15, 12, 12, 12, 12, 13, 13, 13};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len);


    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 2;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 1};
    int64_t expectTime[expectRowsCount] = {2, 2};
    int64_t expectAge[expectRowsCount] = {18, 18};
    RdCheckDataInTSTableByArr(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, expectTime, expectAge);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建包含Dispatch by的流表，后继为包含Dispatch By、包含From Dispatch的普通视图，后继为包含Dispatch By、包含From Dispatch的普通视图，后继为不包含From Dispatch的sink 预期：成功
TEST_F(SupportDispatch, STREAM_030_Test_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id,time;"
        },
        {
            "create stream view v1 as select * from table(dispatch(table t1, 1, 2)) "
            "DISPATCH BY id,time,age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select * from table(dispatch(table v1, 1, 2, 18)) "
            "DISPATCH BY id,time,age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v2 "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 15;
    int64_t id[rowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 5, 5, 5, 5, 5};
    int64_t time[rowNum] = {2, 2, 2, 2, 2, 6, 6, 6, 6, 6, 8, 8, 8, 8, 8};
    int64_t age[rowNum] = {18, 18, 13, 13, 15, 15, 15, 15, 12, 12, 12, 12, 13, 13, 13};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len);


    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 2;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 1};
    int64_t expectTime[expectRowsCount] = {2, 2};
    int64_t expectAge[expectRowsCount] = {18, 18};
    RdCheckDataInTSTableByArr(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, expectTime, expectAge);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建包含Dispatch by的流表，后继为包含Dispatch By、包含From Dispatch的普通视图，后继为包含Dispatch By、不包含From Dispatch的普通视图，后继为包含From Dispatch的sink 预期：成功
TEST_F(SupportDispatch, STREAM_030_Test_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id,time;"
        },
        {
            "create stream view v1 as select * from table(dispatch(table t1, 1, 2)) "
            "DISPATCH BY id,time,age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select * from v1 "
            "DISPATCH BY id,time,age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from table(dispatch(table v2, 1, 2, 18)) "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 15;
    int64_t id[rowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 5, 5, 5, 5, 5};
    int64_t time[rowNum] = {2, 2, 2, 2, 2, 6, 6, 6, 6, 6, 8, 8, 8, 8, 8};
    int64_t age[rowNum] = {18, 18, 13, 13, 15, 15, 15, 15, 12, 12, 12, 12, 13, 13, 13};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len);


    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 2;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 1};
    int64_t expectTime[expectRowsCount] = {2, 2};
    int64_t expectAge[expectRowsCount] = {18, 18};
    RdCheckDataInTSTableByArr(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, expectTime, expectAge);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建包含Dispatch by的流表，后继为包含Dispatch By、包含From Dispatch的普通视图，后继为包含Dispatch By、不包含From Dispatch的普通视图，后继为不包含From Dispatch的sink 预期：成功
TEST_F(SupportDispatch, STREAM_030_Test_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id,time;"
        },
        {
            "create stream view v1 as select * from table(dispatch(table t1, 1, 2)) "
            "DISPATCH BY id,time,age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select * from v1 "
            "DISPATCH BY id,time,age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v2 "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 15;
    int64_t id[rowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 5, 5, 5, 5, 5};
    int64_t time[rowNum] = {2, 2, 2, 2, 2, 6, 6, 6, 6, 6, 8, 8, 8, 8, 8};
    int64_t age[rowNum] = {18, 18, 13, 13, 15, 15, 15, 15, 12, 12, 12, 12, 13, 13, 13};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len);


    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 5;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 1, 1, 1, 1};
    int64_t expectTime[expectRowsCount] = {2, 2, 2, 2, 2};
    int64_t expectAge[expectRowsCount] = {18, 18, 13, 13, 15};
    RdCheckDataInTSTableByArr(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, expectTime, expectAge);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建包含Dispatch by的流表，后继为包含Dispatch By、包含From Dispatch的普通视图，后继为不包含Dispatch By、包含From Dispatch的普通视图，后继为包含From Dispatch的sink 预期：失败
TEST_F(SupportDispatch, STREAM_030_Test_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id,time;"
        },
        {
            "create stream view v1 as select * from table(dispatch(table t1, 1, 2)) "
            "DISPATCH BY id,time,age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select * from table(dispatch(table v1, 1, 2, 18)) "
            "with (tuple_buffer_size = 1);"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    const char *create = "create stream sink s1 as select * from table(dispatch(table v2, 1, 2, 18)) "
                         "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SEMANTIC_ERROR, ret);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建包含Dispatch by的流表，后继为包含Dispatch By、包含From Dispatch的普通视图，后继为不包含Dispatch By、包含From Dispatch的普通视图，后继为不包含From Dispatch的sink 预期：成功
TEST_F(SupportDispatch, STREAM_030_Test_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id,time;"
        },
        {
            "create stream view v1 as select * from table(dispatch(table t1, 1, 2)) "
            "DISPATCH BY id,time,age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select * from table(dispatch(table v1, 1, 2, 18)) "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v2 "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 15;
    int64_t id[rowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 5, 5, 5, 5, 5};
    int64_t time[rowNum] = {2, 2, 2, 2, 2, 6, 6, 6, 6, 6, 8, 8, 8, 8, 8};
    int64_t age[rowNum] = {18, 18, 13, 13, 15, 15, 15, 15, 12, 12, 12, 12, 13, 13, 13};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len);


    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 2;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 1};
    int64_t expectTime[expectRowsCount] = {2, 2};
    int64_t expectAge[expectRowsCount] = {18, 18};
    RdCheckDataInTSTableByArr(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, expectTime, expectAge);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建包含Dispatch by的流表，后继为包含Dispatch By、包含From Dispatch的普通视图，后继为不包含Dispatch By、不包含From Dispatch的普通视图，后继为包含From Dispatch的sink 预期：失败
TEST_F(SupportDispatch, STREAM_030_Test_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id,time;"
        },
        {
            "create stream view v1 as select * from table(dispatch(table t1, 1, 2)) "
            "DISPATCH BY id,time,age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select * from v1 "
            "with (tuple_buffer_size = 1);"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    const char *create = "create stream sink s1 as select * from table(dispatch(table v2, 1, 2, 18)) "
                         "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SEMANTIC_ERROR, ret);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建包含Dispatch by的流表，后继为包含Dispatch By、包含From Dispatch的普通视图，后继为不包含Dispatch By、不包含From Dispatch的普通视图，后继为不包含From Dispatch的sink 预期：成功
TEST_F(SupportDispatch, STREAM_030_Test_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id,time;"
        },
        {
            "create stream view v1 as select * from table(dispatch(table t1, 1, 2)) "
            "DISPATCH BY id,time,age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select * from v1 "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v2 "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 15;
    int64_t id[rowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 5, 5, 5, 5, 5};
    int64_t time[rowNum] = {2, 2, 2, 2, 2, 6, 6, 6, 6, 6, 8, 8, 8, 8, 8};
    int64_t age[rowNum] = {18, 18, 13, 13, 15, 15, 15, 15, 12, 12, 12, 12, 13, 13, 13};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len);


    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 5;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 1, 1, 1, 1};
    int64_t expectTime[expectRowsCount] = {2, 2, 2, 2, 2};
    int64_t expectAge[expectRowsCount] = {18, 18, 13, 13, 15};
    RdCheckDataInTSTableByArr(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, expectTime, expectAge);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建包含Dispatch by的流表，后继为包含Dispatch By、不包含From Dispatch的普通视图，后继为包含Dispatch By、包含From Dispatch的普通视图，后继为包含From Dispatch的sink 预期：成功
TEST_F(SupportDispatch, STREAM_030_Test_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id,time;"
        },
        {
            "create stream view v1 as select * from t1 "
            "DISPATCH BY id,time "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select * from table(dispatch(table v1, 1, 2)) "
            "DISPATCH BY id,time,age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from table(dispatch(table v2, 1, 2, 18)) "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 15;
    int64_t id[rowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 5, 5, 5, 5, 5};
    int64_t time[rowNum] = {2, 2, 2, 2, 2, 6, 6, 6, 6, 6, 8, 8, 8, 8, 8};
    int64_t age[rowNum] = {18, 18, 13, 13, 15, 15, 15, 15, 12, 12, 12, 12, 13, 13, 13};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len);


    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 2;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 1};
    int64_t expectTime[expectRowsCount] = {2, 2};
    int64_t expectAge[expectRowsCount] = {18, 18};
    RdCheckDataInTSTableByArr(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, expectTime, expectAge);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建包含Dispatch by的流表，后继为包含Dispatch By、不包含From Dispatch的普通视图，后继为包含Dispatch By、包含From Dispatch的普通视图，后继为不包含From Dispatch的sink 预期：成功
TEST_F(SupportDispatch, STREAM_030_Test_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id,time;"
        },
        {
            "create stream view v1 as select * from t1 "
            "DISPATCH BY id,time "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select * from table(dispatch(table v1, 1, 2)) "
            "DISPATCH BY id,time,age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v2 "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 15;
    int64_t id[rowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 5, 5, 5, 5, 5};
    int64_t time[rowNum] = {2, 2, 2, 2, 2, 6, 6, 6, 6, 6, 8, 8, 8, 8, 8};
    int64_t age[rowNum] = {18, 18, 13, 13, 15, 15, 15, 15, 12, 12, 12, 12, 13, 13, 13};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len);


    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 5;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 1, 1, 1, 1};
    int64_t expectTime[expectRowsCount] = {2, 2, 2, 2, 2};
    int64_t expectAge[expectRowsCount] = {18, 18, 13, 13, 15};
    RdCheckDataInTSTableByArr(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, expectTime, expectAge);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建包含Dispatch by的流表，后继为包含Dispatch By、不包含From Dispatch的普通视图，后继为包含Dispatch By、不包含From Dispatch的普通视图，后继为包含From Dispatch的sink 预期：成功
TEST_F(SupportDispatch, STREAM_030_Test_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id,time;"
        },
        {
            "create stream view v1 as select * from t1 "
            "DISPATCH BY id,time,age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select * from v1 "
            "DISPATCH BY id,time "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from table(dispatch(table v2, 1, 2)) "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 15;
    int64_t id[rowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 5, 5, 5, 5, 5};
    int64_t time[rowNum] = {2, 2, 2, 2, 2, 6, 6, 6, 6, 6, 8, 8, 8, 8, 8};
    int64_t age[rowNum] = {18, 18, 13, 13, 15, 15, 15, 15, 12, 12, 12, 12, 13, 13, 13};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len);


    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 5;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 1, 1, 1, 1};
    int64_t expectTime[expectRowsCount] = {2, 2, 2, 2, 2};
    int64_t expectAge[expectRowsCount] = {18, 18, 13, 13, 15};
    RdCheckDataInTSTableByArr(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, expectTime, expectAge);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建包含Dispatch by的流表，后继为包含Dispatch By、不包含From Dispatch的普通视图，后继为包含Dispatch By、不包含From Dispatch的普通视图，后继为不包含From Dispatch的sink 预期：成功
TEST_F(SupportDispatch, STREAM_030_Test_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id,time;"
        },
        {
            "create stream view v1 as select * from t1 "
            "DISPATCH BY id,time,age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select * from v1 "
            "DISPATCH BY id,time,age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v2 "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 15;
    int64_t id[rowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 5, 5, 5, 5, 5};
    int64_t time[rowNum] = {2, 2, 2, 2, 2, 6, 6, 6, 6, 6, 8, 8, 8, 8, 8};
    int64_t age[rowNum] = {18, 18, 13, 13, 15, 15, 15, 15, 12, 12, 12, 12, 13, 13, 13};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len);


    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 15;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 5, 5, 5, 5, 5};
    int64_t expectTime[expectRowsCount] = {2, 2, 2, 2, 2, 6, 6, 6, 6, 6, 8, 8, 8, 8, 8};
    int64_t expectAge[expectRowsCount] = {18, 18, 13, 13, 15, 15, 15, 15, 12, 12, 12, 12, 13, 13, 13};
    RdCheckDataInTSTableByArr(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, expectTime, expectAge);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建包含Dispatch by的流表，后继为包含Dispatch By、不包含From Dispatch的普通视图，后继为不包含Dispatch By、包含From Dispatch的普通视图，后继为包含From Dispatch的sink 预期：失败
TEST_F(SupportDispatch, STREAM_030_Test_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id,time;"
        },
        {
            "create stream view v1 as select * from t1 "
            "DISPATCH BY id,time,age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select * from table(dispatch(table v1, 1, 2, 18)) "
            "with (tuple_buffer_size = 1);"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    const char *create = "create stream sink s1 as select * from table(dispatch(table v2, 1, 2, 18)) "
                         "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SEMANTIC_ERROR, ret);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建包含Dispatch by的流表，后继为包含Dispatch By、不包含From Dispatch的普通视图，后继为不包含Dispatch By、包含From Dispatch的普通视图，后继为不包含From Dispatch的sink 预期：成功
TEST_F(SupportDispatch, STREAM_030_Test_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id,time;"
        },
        {
            "create stream view v1 as select * from t1 "
            "DISPATCH BY id,time,age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select * from table(dispatch(table v1, 1, 2, 18)) "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v2 "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 15;
    int64_t id[rowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 5, 5, 5, 5, 5};
    int64_t time[rowNum] = {2, 2, 2, 2, 2, 6, 6, 6, 6, 6, 8, 8, 8, 8, 8};
    int64_t age[rowNum] = {18, 18, 13, 13, 15, 15, 15, 15, 12, 12, 12, 12, 13, 13, 13};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len);


    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 2;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 1};
    int64_t expectTime[expectRowsCount] = {2, 2};
    int64_t expectAge[expectRowsCount] = {18, 18};
    RdCheckDataInTSTableByArr(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, expectTime, expectAge);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建包含Dispatch by的流表，后继为包含Dispatch By、不包含From Dispatch的普通视图，后继为不包含Dispatch By、不包含From Dispatch的普通视图，后继为包含From Dispatch的sink 预期：失败
TEST_F(SupportDispatch, STREAM_030_Test_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id,time;"
        },
        {
            "create stream view v1 as select * from t1 "
            "DISPATCH BY id,time,age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select * from v1 "
            "with (tuple_buffer_size = 1);"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    const char *create = "create stream sink s1 as select * from table(dispatch(table v2, 1, 2, 18)) "
                         "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SEMANTIC_ERROR, ret);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建包含Dispatch by的流表，后继为包含Dispatch By、不包含From Dispatch的普通视图，后继为不包含Dispatch By、不包含From Dispatch的普通视图，后继为不包含From Dispatch的sink 预期：成功
TEST_F(SupportDispatch, STREAM_030_Test_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id,time;"
        },
        {
            "create stream view v1 as select * from t1 "
            "DISPATCH BY id,time,age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select * from v1 "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v2 "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 15;
    int64_t id[rowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 5, 5, 5, 5, 5};
    int64_t time[rowNum] = {2, 2, 2, 2, 2, 6, 6, 6, 6, 6, 8, 8, 8, 8, 8};
    int64_t age[rowNum] = {18, 18, 13, 13, 15, 15, 15, 15, 12, 12, 12, 12, 13, 13, 13};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len);


    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 15;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 5, 5, 5, 5, 5};
    int64_t expectTime[expectRowsCount] = {2, 2, 2, 2, 2, 6, 6, 6, 6, 6, 8, 8, 8, 8, 8};
    int64_t expectAge[expectRowsCount] = {18, 18, 13, 13, 15, 15, 15, 15, 12, 12, 12, 12, 13, 13, 13};
    RdCheckDataInTSTableByArr(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, expectTime, expectAge);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建包含Dispatch by的流表，后继为不包含Dispatch By、包含From Dispatch的滑动窗口视图，后继为包含Dispatch By、不包含From Dispatch的普通视图，后继为包含From Dispatch的sink 预期：成功
TEST_F(SupportDispatch, STREAM_030_Test_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for id as id - interval '10' seconds strict) "
            "DISPATCH BY time;"
        },
        {
            "create stream view v1 as select id, name, age, ROW_NUMBER() "
            "over (PARTITION BY window_start, window_end, age), "
            "address, window_start, window_end from "
            "table(hop(table dispatch(table t1, 1), id, interval '20' seconds, interval '10' seconds)) "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select id, name, age, row_number, address from v1 "
            "DISPATCH BY row_number "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from table(dispatch(table v2, 1)) "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 20;
    int64_t id[rowNum] = {1, 1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 23, 25, 30, 8, 35, 22, 23, 23};
    int64_t time[rowNum] = {1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2};
    int64_t age[rowNum] = {10, 10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len);


    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 6;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 3, 5, 20, 21, 23};
    int64_t expectAge[expectRowsCount] = {10, 11, 12, 10, 12, 11};
    int64_t expectLineNum[expectRowsCount] = {1, 1, 1, 1, 1, 1};
    RdCheckDataInTSTableByArr(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, expectAge, expectLineNum);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建包含Dispatch by的流表，后继为不包含Dispatch By、包含From Dispatch的滑动窗口视图，后继为包含Dispatch By、不包含From Dispatch的普通视图，后继为不包含From Dispatch的sink 预期：成功
TEST_F(SupportDispatch, STREAM_030_Test_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for id as id - interval '10' seconds strict) "
            "DISPATCH BY time;"
        },
        {
            "create stream view v1 as select id, name, age, ROW_NUMBER() "
            "over (PARTITION BY window_start, window_end, age), "
            "address, window_start, window_end from "
            "table(hop(table dispatch(table t1, 1), id, interval '20' seconds, interval '10' seconds)) "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select id, name, age, row_number, address from v1 "
            "DISPATCH BY id, row_number "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v2 "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 20;
    int64_t id[rowNum] = {1, 1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 23, 25, 30, 8, 35, 22, 23, 23};
    int64_t time[rowNum] = {1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2};
    int64_t age[rowNum] = {10, 10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len);


    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 9;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 3, 5, 20, 21, 23, 25, 22, 23};
    int64_t expectTime[expectRowsCount] = {10, 11, 12, 10, 12, 11, 12, 11, 10};
    int64_t expectAge[expectRowsCount] = {1, 1, 1, 1, 1, 1, 2, 2, 2};
    RdCheckDataInTSTableByArr(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, expectTime, expectAge);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建包含Dispatch by的流表，后继为不包含Dispatch By、包含From Dispatch的普通视图，后继为包含Dispatch By、不包含From Dispatch的普通视图，后继为包含From Dispatch的sink 预期：成功
TEST_F(SupportDispatch, STREAM_030_Test_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id,time;"
        },
        {
            "create stream view v1 as select * from table(dispatch(table t1, 1, 2)) "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select * from v1 "
            "DISPATCH BY id,time,age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from table(dispatch(table v2, 1, 2, 13)) "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 15;
    int64_t id[rowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 5, 5, 5, 5, 5};
    int64_t time[rowNum] = {2, 2, 2, 2, 2, 6, 6, 6, 6, 6, 8, 8, 8, 8, 8};
    int64_t age[rowNum] = {18, 18, 13, 13, 13, 15, 15, 15, 12, 12, 12, 12, 13, 13, 13};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len);


    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 3;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 1, 1};
    int64_t expectTime[expectRowsCount] = {2, 2, 2};
    int64_t expectAge[expectRowsCount] = {13, 13, 13};
    RdCheckDataInTSTableByArr(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, expectTime, expectAge);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建包含Dispatch by的流表，后继为不包含Dispatch By、包含From Dispatch的普通视图，后继为不包含Dispatch By、不包含From Dispatch的普通视图，后继为包含From Dispatch的sink 预期：成功
TEST_F(SupportDispatch, STREAM_030_Test_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id,time;"
        },
        {
            "create stream view v1 as select * from table(dispatch(table t1, 1, 2)) "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select * from v1 "
            "with (tuple_buffer_size = 1);"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    const char *create = "create stream sink s1 as select * from table(dispatch(table v2, 1, 2, 13)) "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SEMANTIC_ERROR, ret);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建包含Dispatch by的流表，后继为不包含Dispatch By、包含From Dispatch的普通视图，后继为不包含Dispatch By、不包含From Dispatch的普通视图，后继为不包含From Dispatch的sink 预期：成功
TEST_F(SupportDispatch, STREAM_030_Test_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id,time;"
        },
        {
            "create stream view v1 as select * from table(dispatch(table t1, 1, 2)) "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select * from v1 "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v2 "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 15;
    int64_t id[rowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 5, 5, 5, 5, 5};
    int64_t time[rowNum] = {2, 2, 2, 2, 2, 6, 6, 6, 6, 6, 8, 8, 8, 8, 8};
    int64_t age[rowNum] = {18, 18, 13, 13, 13, 15, 15, 15, 12, 12, 12, 12, 13, 13, 13};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len);


    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 5;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 1, 1, 1, 1};
    int64_t expectTime[expectRowsCount] = {2, 2, 2, 2, 2};
    int64_t expectAge[expectRowsCount] = {18, 18, 13, 13, 13};
    RdCheckDataInTSTableByArr(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, expectTime, expectAge);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建包含Dispatch by的流表，后继为不包含Dispatch By、包含From Dispatch的普通视图，后继为不包含Dispatch By、包含From Dispatch的普通视图 预期：失败
TEST_F(SupportDispatch, STREAM_030_Test_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id,time;"
        },
        {
            "create stream view v1 as select * from table(dispatch(table t1, 1, 2)) "
            "with (tuple_buffer_size = 1);"
        },  
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    const char *create = "create stream view v2 as select * from table(dispatch(table v1, 1, 2)) "
                         "with (tuple_buffer_size = 1);";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SEMANTIC_ERROR, ret);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建包含Dispatch by的流表，后继为不包含Dispatch By、包含From Dispatch的普通视图，后继为包含Dispatch By、包含From Dispatch的普通视图 预期：失败
TEST_F(SupportDispatch, STREAM_030_Test_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id,time;"
        },
        {
            "create stream view v1 as select * from table(dispatch(table t1, 1, 2)) "
            "with (tuple_buffer_size = 1);"
        },  
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    const char *create = "create stream view v2 as select * from table(dispatch(table v1, 1, 2)) "
                         "DISPATCH BY id,time "
                         "with (tuple_buffer_size = 1);";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SEMANTIC_ERROR, ret);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建包含Dispatch by的流表，后继为不包含Dispatch By、不包含From Dispatch的普通视图，后继为不包含Dispatch By、包含From Dispatch的普通视图 预期：失败
TEST_F(SupportDispatch, STREAM_030_Test_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id,time;"
        },
        {
            "create stream view v1 as select * from t1 "
            "with (tuple_buffer_size = 1);"
        },  
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    const char *create = "create stream view v2 as select * from table(dispatch(table v1, 1, 2)) "
                         "with (tuple_buffer_size = 1);";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SEMANTIC_ERROR, ret);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建包含Dispatch by的流表，后继为不包含Dispatch By、不包含From Dispatch的普通视图，后继为包含Dispatch By、包含From Dispatch的普通视图 预期：失败
TEST_F(SupportDispatch, STREAM_030_Test_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id,time;"
        },
        {
            "create stream view v1 as select * from t1 "
            "with (tuple_buffer_size = 1);"
        },  
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    const char *create = "create stream view v2 as select * from table(dispatch(table v1, 1, 2)) "
                         "DISPATCH BY id,time "
                         "with (tuple_buffer_size = 1);";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SEMANTIC_ERROR, ret);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建包含Dispatch by的流表，后继为不包含Dispatch By、不包含From Dispatch的普通视图，后继为不包含Dispatch By、不包含From Dispatch的普通视图，后继为不包含From Dispatch的sink 预期：成功
TEST_F(SupportDispatch, STREAM_030_Test_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id,time;"
        },
        {
            "create stream view v1 as select * from t1 "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select * from v1 "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v2 "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 15;
    int64_t id[rowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 5, 5, 5, 5, 5};
    int64_t time[rowNum] = {2, 2, 2, 2, 2, 6, 6, 6, 6, 6, 8, 8, 8, 8, 8};
    int64_t age[rowNum] = {18, 18, 13, 13, 13, 15, 15, 15, 12, 12, 12, 12, 13, 13, 13};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len);


    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 15;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 5, 5, 5, 5, 5};
    int64_t expectTime[expectRowsCount] = {2, 2, 2, 2, 2, 6, 6, 6, 6, 6, 8, 8, 8, 8, 8};
    int64_t expectAge[expectRowsCount] = {18, 18, 13, 13, 13, 15, 15, 15, 12, 12, 12, 12, 13, 13, 13};
    RdCheckDataInTSTableByArr(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, expectTime, expectAge);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建包含Dispatch by的流表，后继为不包含Dispatch By、不包含From Dispatch的普通视图，后继为不包含Dispatch By、不包含From Dispatch的普通视图，后继为包含From Dispatch的sink 预期：失败
TEST_F(SupportDispatch, STREAM_030_Test_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id,time;"
        },
        {
            "create stream view v1 as select * from t1 "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select * from v1 "
            "with (tuple_buffer_size = 1);"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    const char *create = "create stream sink s1 as select * from table(dispatch(table v2, 1, 2)) "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SEMANTIC_ERROR, ret);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建包含Dispatch by的流表，后继为不包含Dispatch By、不包含From Dispatch的普通视图，后继为包含Dispatch By、不包含From Dispatch的普通视图，后继为不包含From Dispatch的sink 预期：成功
TEST_F(SupportDispatch, STREAM_030_Test_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id,time;"
        },
        {
            "create stream view v1 as select * from t1 "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select * from v1 "
            "DISPATCH BY id,time "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v2 "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 15;
    int64_t id[rowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 5, 5, 5, 5, 5};
    int64_t time[rowNum] = {2, 2, 2, 2, 2, 6, 6, 6, 6, 6, 8, 8, 8, 8, 8};
    int64_t age[rowNum] = {18, 18, 13, 13, 13, 15, 15, 15, 12, 12, 12, 12, 13, 13, 13};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len);


    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 15;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 5, 5, 5, 5, 5};
    int64_t expectTime[expectRowsCount] = {2, 2, 2, 2, 2, 6, 6, 6, 6, 6, 8, 8, 8, 8, 8};
    int64_t expectAge[expectRowsCount] = {18, 18, 13, 13, 13, 15, 15, 15, 12, 12, 12, 12, 13, 13, 13};
    RdCheckDataInTSTableByArr(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, expectTime, expectAge);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建包含Dispatch by的流表，后继为不包含Dispatch By、不包含From Dispatch的普通视图，后继为包含Dispatch By、不包含From Dispatch的普通视图，后继为包含From Dispatch的sink 预期：成功
TEST_F(SupportDispatch, STREAM_030_Test_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id,time;"
        },
        {
            "create stream view v1 as select * from t1 "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select * from v1 "
            "DISPATCH BY id,time "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from table(dispatch(table v2, 1, 2)) "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 15;
    int64_t id[rowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 5, 5, 5, 5, 5};
    int64_t time[rowNum] = {2, 2, 2, 2, 2, 6, 6, 6, 6, 6, 8, 8, 8, 8, 8};
    int64_t age[rowNum] = {18, 18, 13, 13, 13, 15, 15, 15, 12, 12, 12, 12, 13, 13, 13};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len);


    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 5;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 1, 1, 1, 1};
    int64_t expectTime[expectRowsCount] = {2, 2, 2, 2, 2};
    int64_t expectAge[expectRowsCount] = {18, 18, 13, 13, 13};
    RdCheckDataInTSTableByArr(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, expectTime, expectAge);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建不包含Dispatch by的流表，后继为不包含Dispatch By、不包含From Dispatch的普通视图，后继为包含Dispatch By、不包含From Dispatch的普通视图，后继为包含From Dispatch的sink 预期：成功
TEST_F(SupportDispatch, STREAM_030_Test_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict);"
        },
        {
            "create stream view v1 as select * from t1 "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select * from v1 "
            "DISPATCH BY id,time "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from table(dispatch(table v2, 1, 2)) "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 15;
    int64_t id[rowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 5, 5, 5, 5, 5};
    int64_t time[rowNum] = {2, 2, 2, 2, 2, 6, 6, 6, 6, 6, 8, 8, 8, 8, 8};
    int64_t age[rowNum] = {18, 18, 13, 13, 15, 15, 15, 15, 12, 12, 12, 12, 13, 13, 13};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len);


    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 5;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 1, 1, 1, 1};
    int64_t expectTime[expectRowsCount] = {2, 2, 2, 2, 2};
    int64_t expectAge[expectRowsCount] = {18, 18, 13, 13, 15};
    RdCheckDataInTSTableByArr(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, expectTime, expectAge);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建不包含Dispatch by的流表，后继为不包含Dispatch By、不包含From Dispatch的普通视图，后继为包含Dispatch By、不包含From Dispatch的普通视图，后继为不包含From Dispatch的sink 预期：成功
TEST_F(SupportDispatch, STREAM_030_Test_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict);"
        },
        {
            "create stream view v1 as select * from t1 "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select * from v1 "
            "DISPATCH BY id,time "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v2 "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 15;
    int64_t id[rowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 5, 5, 5, 5, 5};
    int64_t time[rowNum] = {2, 2, 2, 2, 2, 6, 6, 6, 6, 6, 8, 8, 8, 8, 8};
    int64_t age[rowNum] = {18, 18, 13, 13, 15, 15, 15, 15, 12, 12, 12, 12, 13, 13, 13};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len);


    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 15;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 5, 5, 5, 5, 5};
    int64_t expectTime[expectRowsCount] = {2, 2, 2, 2, 2, 6, 6, 6, 6, 6, 8, 8, 8, 8, 8};
    int64_t expectAge[expectRowsCount] = {18, 18, 13, 13, 15, 15, 15, 15, 12, 12, 12, 12, 13, 13, 13};
    RdCheckDataInTSTableByArr(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, expectTime, expectAge);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建不包含Dispatch by的流表，后继为不包含Dispatch By、不包含From Dispatch的滑动窗口视图，后继为包含Dispatch By、不包含From Dispatch的普通视图，后继为包含From Dispatch的sink 预期：成功
TEST_F(SupportDispatch, STREAM_030_Test_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for id as id - interval '10' seconds strict);"
        },
        {
            "create stream view v1 as select id, name, age, ROW_NUMBER() "
            "over (PARTITION BY window_start, window_end, age), "
            "address, window_start, window_end from "
            "table(hop(table t1, id, interval '20' seconds, interval '10' seconds)) "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select id, name, age, row_number, address from v1 "
            "DISPATCH BY row_number "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from table(dispatch(table v2, 1)) "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 20;
    int64_t id[rowNum] = {1, 1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 23, 25, 30, 8, 35, 22, 23, 23};
    int64_t time[rowNum] = {1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2};
    int64_t age[rowNum] = {10, 10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len);


    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 6;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 3, 5, 20, 21, 23};
    int64_t expectAge[expectRowsCount] = {10, 11, 12, 10, 12, 11};
    int64_t expectLineNum[expectRowsCount] = {1, 1, 1, 1, 1, 1};
    RdCheckDataInTSTableByArr(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, expectAge, expectLineNum);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建不包含Dispatch by的流表，后继为不包含Dispatch By、不包含From Dispatch的滑动窗口视图，后继为不包含Dispatch By、包含From Dispatch的普通视图 预期：失败
TEST_F(SupportDispatch, STREAM_030_Test_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for id as id - interval '10' seconds strict);"
        },
        {
            "create stream view v1 as select id, name, age, ROW_NUMBER() "
            "over (PARTITION BY window_start, window_end, age), "
            "address, window_start, window_end from "
            "table(hop(table t1, id, interval '20' seconds, interval '10' seconds)) "
            "with (tuple_buffer_size = 1);"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    const char *create = "create stream view v2 as select id, name, age, row_number, address from table(dispatch(table v1, 1)) "
                         "with (tuple_buffer_size = 1);";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SEMANTIC_ERROR, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建不包含Dispatch by的流表，后继为不包含Dispatch By、不包含From Dispatch的滑动窗口视图，后继为包含Dispatch By、包含From Dispatch的普通视图 预期：失败
TEST_F(SupportDispatch, STREAM_030_Test_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for id as id - interval '10' seconds strict);"
        },
        {
            "create stream view v1 as select id, name, age, ROW_NUMBER() "
            "over (PARTITION BY window_start, window_end, age), "
            "address, window_start, window_end from "
            "table(hop(table t1, id, interval '20' seconds, interval '10' seconds)) "
            "with (tuple_buffer_size = 1);"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    const char *create = "create stream view v2 as select id, name, age, row_number, address from table(dispatch(table v1, 1)) "
                         "DISPATCH BY id,time "
                         "with (tuple_buffer_size = 1);";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SEMANTIC_ERROR, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建不包含Dispatch by的流表，后继为包含Dispatch By、不包含From Dispatch的滑动窗口视图，后继为包含Dispatch By、包含From Dispatch的普通视图，后继为包含From Dispatch的sink 预期：成功
TEST_F(SupportDispatch, STREAM_030_Test_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for id as id - interval '10' seconds strict);"
        },
        {
            "create stream view v1 as select id, name, age, ROW_NUMBER() "
            "over (PARTITION BY window_start, window_end, age), "
            "address, window_start, window_end from "
            "table(hop(table t1, id, interval '20' seconds, interval '10' seconds)) "
            "dispatch by age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select id, name, age, row_number, address from table(dispatch(table v1, 10)) "
            "DISPATCH BY row_number "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from table(dispatch(table v2, 1)) "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 20;
    int64_t id[rowNum] = {1, 1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 23, 25, 30, 8, 35, 22, 23, 23};
    int64_t time[rowNum] = {1, 1, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2};
    int64_t age[rowNum] = {10, 10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len);


    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 2;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 20};
    int64_t expectAge[expectRowsCount] = {10, 10};
    int64_t expectLineNum[expectRowsCount] = {1, 1};
    RdCheckDataInTSTableByArr(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, expectAge, expectLineNum);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建不包含Dispatch by的流表，后继为包含Dispatch By、不包含From Dispatch的滑动窗口视图，后继为包含Dispatch By、包含From Dispatch的普通视图，后继为不包含From Dispatch的sink 预期：成功
TEST_F(SupportDispatch, STREAM_030_Test_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for id as id - interval '10' seconds tolerant);"
        },
        {
            "create stream view v1 as select id, name, age, ROW_NUMBER() "
            "over (PARTITION BY window_start, window_end, age), "
            "address, window_start, window_end from "
            "table(hop(table t1, id, interval '20' seconds, interval '10' seconds)) "
            "dispatch by age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select id, name, age, row_number, address from table(dispatch(table v1, 10)) "
            "DISPATCH BY row_number "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v2 "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 20;
    int64_t id[rowNum] = {1, 1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 23, 25, 30, 8, 35, 22, 23, 23};
    int64_t time[rowNum] = {1, 1, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2};
    int64_t age[rowNum] = {10, 10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len);


    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 6;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 1, 20, 7, 23, 23};
    int64_t expectAge[expectRowsCount] = {10, 10, 10, 10, 10, 10};
    int64_t expectLineNum[expectRowsCount] = {1, 2, 1, 3, 2, 3};
    RdCheckDataInTSTableByArr(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, expectAge, expectLineNum);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建不包含Dispatch by的流表，后继为包含Dispatch By、不包含From Dispatch的滑动窗口视图，后继为包含Dispatch By、不包含From Dispatch的普通视图，后继为包含From Dispatch的sink 预期：成功
TEST_F(SupportDispatch, STREAM_030_Test_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for id as id - interval '10' seconds strict);"
        },
        {
            "create stream view v1 as select id, name, age, ROW_NUMBER() "
            "over (PARTITION BY window_start, window_end, age), "
            "address, window_start, window_end from "
            "table(hop(table t1, id, interval '20' seconds, interval '10' seconds)) "
            "dispatch by age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select id, name, age, row_number, address from v1 "
            "DISPATCH BY row_number "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from table(dispatch(table v2, 1)) "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 20;
    int64_t id[rowNum] = {1, 1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 23, 25, 30, 8, 35, 22, 23, 23};
    int64_t time[rowNum] = {1, 1, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2};
    int64_t age[rowNum] = {10, 10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len);


    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 6;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 3, 5, 20, 21, 23};
    int64_t expectAge[expectRowsCount] = {10, 11, 12, 10, 12, 11};
    int64_t expectLineNum[expectRowsCount] = {1, 1, 1, 1, 1, 1};
    RdCheckDataInTSTableByArr(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, expectAge, expectLineNum);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建不包含Dispatch by的流表，后继为包含Dispatch By、不包含From Dispatch的滑动窗口视图，后继为包含Dispatch By、不包含From Dispatch的普通视图，后继为不包含From Dispatch的sink 预期：成功
TEST_F(SupportDispatch, STREAM_030_Test_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for id as id - interval '10' seconds tolerant);"
        },
        {
            "create stream view v1 as select id, name, age, ROW_NUMBER() "
            "over (PARTITION BY window_start, window_end, age), "
            "address, window_start, window_end from "
            "table(hop(table t1, id, interval '20' seconds, interval '10' seconds)) "
            "dispatch by age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select id, name, age, row_number, address from v1 "
            "DISPATCH BY row_number "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v2 "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 20;
    int64_t id[rowNum] = {1, 1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 23, 25, 30, 8, 35, 22, 23, 23};
    int64_t time[rowNum] = {1, 1, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2};
    int64_t age[rowNum] = {10, 10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len);


    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 12;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 1, 3, 5, 20, 7, 21, 23, 25, 22, 23, 23};
    int64_t expectAge[expectRowsCount] = {10, 10, 11, 12, 10, 10, 12, 11, 12, 11, 10, 10};
    int64_t expectLineNum[expectRowsCount] = {1, 2, 1, 1, 1, 3, 1, 1, 2, 2, 2, 3};
    RdCheckDataInTSTableByArr(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, expectAge, expectLineNum);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建不包含Dispatch by的流表，后继为包含Dispatch By、不包含From Dispatch的滑动窗口视图，后继为不包含Dispatch By、包含From Dispatch的普通视图，后继为包含From Dispatch的sink 预期：失败
TEST_F(SupportDispatch, STREAM_030_Test_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for id as id - interval '10' seconds strict);"
        },
        {
            "create stream view v1 as select id, name, age, ROW_NUMBER() "
            "over (PARTITION BY window_start, window_end, age), "
            "address, window_start, window_end from "
            "table(hop(table t1, id, interval '20' seconds, interval '10' seconds)) "
            "dispatch by age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select id, name, age, row_number, address from table(dispatch(table v1, 1)) "
            "with (tuple_buffer_size = 1);"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    const char *create = "create stream sink s1 as select * from table(dispatch(table v2, 1)) "
                         "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SEMANTIC_ERROR, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建不包含Dispatch by的流表，后继为包含Dispatch By、不包含From Dispatch的滑动窗口视图，后继为不包含Dispatch By、包含From Dispatch的普通视图，后继为不包含From Dispatch的sink 预期：成功
TEST_F(SupportDispatch, STREAM_030_Test_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for id as id - interval '10' seconds tolerant);"
        },
        {
            "create stream view v1 as select id, name, age, ROW_NUMBER() "
            "over (PARTITION BY window_start, window_end, age), "
            "address, window_start, window_end from "
            "table(hop(table t1, id, interval '20' seconds, interval '10' seconds)) "
            "dispatch by age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select id, name, age, row_number, address from table(dispatch(table v1, 10)) "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v2 "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 20;
    int64_t id[rowNum] = {1, 1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 23, 25, 30, 8, 35, 22, 23, 23};
    int64_t time[rowNum] = {1, 1, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2};
    int64_t age[rowNum] = {10, 10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len);


    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 6;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 1, 20, 7, 23, 23};
    int64_t expectAge[expectRowsCount] = {10, 10, 10, 10, 10, 10};
    int64_t expectLineNum[expectRowsCount] = {1, 2, 1, 3, 2, 3};
    RdCheckDataInTSTableByArr(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, expectAge, expectLineNum);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建不包含Dispatch by的流表，后继为包含Dispatch By、不包含From Dispatch的滑动窗口视图，后继为不包含Dispatch By、不包含From Dispatch的普通视图，后继为包含From Dispatch的sink 预期：失败
TEST_F(SupportDispatch, STREAM_030_Test_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for id as id - interval '10' seconds strict);"
        },
        {
            "create stream view v1 as select id, name, age, ROW_NUMBER() "
            "over (PARTITION BY window_start, window_end, age), "
            "address, window_start, window_end from "
            "table(hop(table t1, id, interval '20' seconds, interval '10' seconds)) "
            "dispatch by age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select id, name, age, row_number, address from v1 "
            "with (tuple_buffer_size = 1);"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    const char *create = "create stream sink s1 as select * from table(dispatch(table v2, 1)) "
                         "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SEMANTIC_ERROR, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建不包含Dispatch by的流表，后继为包含Dispatch By、不包含From Dispatch的滑动窗口视图，后继为不包含Dispatch By、不包含From Dispatch的普通视图，后继为不包含From Dispatch的sink 预期：成功
TEST_F(SupportDispatch, STREAM_030_Test_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for id as id - interval '10' seconds tolerant);"
        },
        {
            "create stream view v1 as select id, name, age, ROW_NUMBER() "
            "over (PARTITION BY window_start, window_end, age), "
            "address, window_start, window_end from "
            "table(hop(table t1, id, interval '20' seconds, interval '10' seconds)) "
            "dispatch by age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select id, name, age, row_number, address from v1 "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v2 "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 20;
    int64_t id[rowNum] = {1, 1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 23, 25, 30, 8, 35, 22, 23, 23};
    int64_t time[rowNum] = {1, 1, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2};
    int64_t age[rowNum] = {10, 10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len);


    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 12;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 1, 3, 5, 20, 7, 21, 23, 25, 22, 23, 23};
    int64_t expectAge[expectRowsCount] = {10, 10, 11, 12, 10, 10, 12, 11, 12, 11, 10, 10};
    int64_t expectLineNum[expectRowsCount] = {1, 2, 1, 1, 1, 3, 1, 1, 2, 2, 2, 3};
    RdCheckDataInTSTableByArr(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, expectAge, expectLineNum);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建不包含Dispatch by的流表，后继为包含From Dispatch的滑动窗口视图 预期：失败
TEST_F(SupportDispatch, STREAM_030_Test_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for id as id - interval '10' seconds strict);"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    const char *create = "create stream view v1 as select id, name, age, ROW_NUMBER() "
                        "over (PARTITION BY window_start, window_end, age), "
                        "address, window_start, window_end from "
                        "table(hop(table dispatch(table t1, 1), id, interval '20' seconds, interval '10' seconds)) "
                        "dispatch by age "
                        "with (tuple_buffer_size = 1);";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SEMANTIC_ERROR, ret);

    create = "create stream view v1 as select id, name, age, ROW_NUMBER() "
                        "over (PARTITION BY window_start, window_end, age), "
                        "address, window_start, window_end from "
                        "table(hop(table dispatch(table t1, 1), id, interval '20' seconds, interval '10' seconds)) "
                        "with (tuple_buffer_size = 1);";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SEMANTIC_ERROR, ret);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建1个包含Dispatch By的流表，有多个后继结点，所有都包含From Dispatch 预期：成功
TEST_F(SupportDispatch, STREAM_030_Test_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id,time;"
        },
        {
            "create stream view v1 as select * from table(dispatch(table t1, 1, 2)) "
            "DISPATCH BY id,time,age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select * from table(dispatch(table v1, 1, 2, 18)) "
            "DISPATCH BY id,time,age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v3 as select * from table(dispatch(table t1, 1, 2)) "
            "DISPATCH BY id,time,age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from table(dispatch(table v2, 1, 2, 18)) "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },

        {
            "create stream sink s2 as select * from table(dispatch(table v3, 1, 2, 18)) "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 15;
    int64_t id[rowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 5, 5, 5, 5, 5};
    int64_t time[rowNum] = {2, 2, 2, 2, 2, 6, 6, 6, 6, 6, 8, 8, 8, 8, 8};
    int64_t age[rowNum] = {18, 18, 13, 13, 15, 15, 15, 15, 12, 12, 12, 12, 13, 13, 13};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len);


    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 4;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 1, 1, 1};
    int64_t expectTime[expectRowsCount] = {2, 2, 2, 2};
    int64_t expectAge[expectRowsCount] = {18, 18, 18, 18};
    RdCheckDataInTSTableByArr(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, expectTime, expectAge);

    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream sink s2;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream view v3;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建1个包含Dispatch By的流表，有多个后继结点，分别为包含From Dispatch的和普通From的 预期：成功
TEST_F(SupportDispatch, STREAM_030_Test_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id,time;"
        },
        {
            "create stream view v1 as select * from table(dispatch(table t1, 1, 2)) "
            "DISPATCH BY id,time "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select * from table(dispatch(table v1, 1, 2)) "
            "DISPATCH BY id,time,age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from table(dispatch(table v2, 1, 2, 18)) "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },

        {
            "create stream sink s2 as select * from v2 "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 15;
    int64_t id[rowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 5, 5, 5, 5, 5};
    int64_t time[rowNum] = {2, 2, 2, 2, 2, 6, 6, 6, 6, 6, 8, 8, 8, 8, 8};
    int64_t age[rowNum] = {18, 18, 13, 13, 15, 15, 15, 15, 12, 12, 12, 12, 13, 13, 13};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len);


    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 7;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 1, 1, 1, 1, 1, 1};
    int64_t expectTime[expectRowsCount] = {2, 2, 2, 2, 2, 2, 2};
    int64_t expectAge[expectRowsCount] = {18, 18, 18, 18, 13, 13, 15};
    RdCheckDataInTSTableByArr(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, expectTime, expectAge);

    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream sink s2;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 视图union多个带From Dispatch 的流表，并且包含Dispatch BY，后继sink不包含From Dispatch 预期：成功
TEST_F(SupportDispatch, STREAM_030_Test_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id,time;"
        },

        {
            "create stream table t2 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id,time;"
        },
        {
            "create stream table t3 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id,time;"
        },
        {
            "create stream view v1 as select * from TABLE(UNION(TABLE(dispatch(table t1, 1, 2)), TABLE(dispatch(table t2, 1, 2)), TABLE(dispatch(table t3, 1, 2)))) "
            "DISPATCH BY id,time "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    int streamNum = 3; 
    RdVertexLabelT *vertexLabels[streamNum] = {0};

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 15;
    int64_t id[rowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 5, 5, 5, 5, 5};
    int64_t time[rowNum] = {2, 2, 2, 2, 2, 6, 6, 6, 6, 6, 8, 8, 8, 8, 8};
    int64_t age[rowNum] = {18, 18, 13, 13, 15, 15, 15, 15, 12, 12, 12, 12, 13, 13, 13};

    // 解析建表SQL，生成结构化schema
    for (int i = 0; i < streamNum; i++){
        vertexLabels[i] = RdStreamParseTableSchema(creates[i + 1].sql);
        ASSERT_NE((RdVertexLabelT *)NULL, vertexLabels[i]);
        RdStructWriteStreamTableByArr(stmt, vertexLabels[i], rowNum, id, time, age, len);
    }

    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 15;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1};
    int64_t expectTime[expectRowsCount] = {2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2};
    int64_t expectAge[expectRowsCount] = {18, 18, 13, 13, 15, 18, 18, 13, 13, 15, 18, 18, 13, 13, 15};
    RdCheckDataInTSTableByArr(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, expectTime, expectAge);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop stream table t2;"},
        {"drop stream table t3;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < streamNum; i++){
        RdStreamFreeTableSchema(vertexLabels[i]);
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 视图union多个带From Dispatch 的流表，并且包含Dispatch BY，后继sink包含From Dispatch 预期：成功
TEST_F(SupportDispatch, STREAM_030_Test_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id, time;"
        },

        {
            "create stream table t2 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id, time;"
        },
        {
            "create stream table t3 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id, time;"
        },
        {
            "create stream view v1 as select * from TABLE(UNION(TABLE(dispatch(table t1, 1, 2)), TABLE(dispatch(table t2, 1, 2)), TABLE(dispatch(table t3, 1, 2)))) "
            "DISPATCH BY id, time, age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from TABLE(dispatch(table v1, 1, 2, 13)) "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    int streamNum = 3; 
    RdVertexLabelT *vertexLabels[streamNum] = {0};

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 15;
    int64_t id[rowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 5, 5, 5, 5, 5};
    int64_t time[rowNum] = {2, 2, 2, 2, 2, 6, 6, 6, 6, 6, 8, 8, 8, 8, 8};
    int64_t age[rowNum] = {18, 18, 13, 13, 15, 15, 15, 15, 12, 12, 12, 12, 13, 13, 13};

    // 解析建表SQL，生成结构化schema
    for (int i = 0; i < streamNum; i++){
        vertexLabels[i] = RdStreamParseTableSchema(creates[i + 1].sql);
        ASSERT_NE((RdVertexLabelT *)NULL, vertexLabels[i]);
        RdStructWriteStreamTableByArr(stmt, vertexLabels[i], rowNum, id, time, age, len);
    }

    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 6;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 1, 1, 1, 1, 1};
    int64_t expectTime[expectRowsCount] = {2, 2, 2, 2, 2, 2};
    int64_t expectAge[expectRowsCount] = {13, 13, 13, 13, 13, 13};
    RdCheckDataInTSTableByArr(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, expectTime, expectAge);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop stream table t2;"},
        {"drop stream table t3;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < streamNum; i++){
        RdStreamFreeTableSchema(vertexLabels[i]);
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 视图union多个带From Dispatch 的流表，并且不包含Dispatch BY，后继sink不包含From Dispatch，batch_window_size为10，timeout为5 预期：成功
TEST_F(SupportDispatch, STREAM_030_Test_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id;"
        },

        {
            "create stream table t2 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id;"
        },
        {
            "create stream table t3 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id;"
        },
        {
            "create stream view v1 as select * from TABLE(UNION(TABLE(dispatch(table t1, 2)), TABLE(dispatch(table t2, 5)), TABLE(dispatch(table t3, 1)))) "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v1 "
            "into tsdb(ts1) with (batch_window_size = '10', tuple_buffer_size = '1', timeout = '5');"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    int streamNum = 3; 
    RdVertexLabelT *vertexLabels[streamNum] = {0};

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 15;
    int64_t id[rowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 5, 5, 5, 5, 5};
    int64_t time[rowNum] = {2, 2, 2, 2, 2, 6, 6, 6, 6, 6, 8, 8, 8, 8, 8};
    int64_t age[rowNum] = {18, 18, 13, 13, 15, 15, 15, 15, 12, 12, 12, 12, 13, 13, 13};

    // 解析建表SQL，生成结构化schema
    for (int i = 0; i < streamNum; i++){
        vertexLabels[i] = RdStreamParseTableSchema(creates[i + 1].sql);
        ASSERT_NE((RdVertexLabelT *)NULL, vertexLabels[i]);
        RdStructWriteStreamTableByArr(stmt, vertexLabels[i], rowNum, id, time, age, len);
    }

    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 10;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {2, 2, 2, 2, 2, 5, 5, 5, 5, 5};
    int64_t expectTime[expectRowsCount] = {6, 6, 6, 6, 6, 8, 8, 8, 8, 8};
    int64_t expectAge[expectRowsCount] = {15, 15, 15, 12, 12, 12, 12, 13, 13, 13};
    RdCheckDataInTSTableByArr(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, expectTime, expectAge);

    // 过5秒再检查数据
    sleep(5);
    expectRowsCount = 15;
    int64_t expectId2[expectRowsCount] = {2, 2, 2, 2, 2, 5, 5, 5, 5, 5, 1, 1, 1, 1, 1};
    int64_t expectTime2[expectRowsCount] = {6, 6, 6, 6, 6, 8, 8, 8, 8, 8, 2, 2, 2, 2, 2};
    int64_t expectAge2[expectRowsCount] = {15, 15, 15, 12, 12, 12, 12, 13, 13, 13, 18, 18, 13, 13, 15};
    RdCheckDataInTSTableByArr(stmt, selectTsName, expectRowsCount, expectColsCount, expectId2, expectTime2, expectAge2);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop stream table t2;"},
        {"drop stream table t3;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < streamNum; i++){
        RdStreamFreeTableSchema(vertexLabels[i]);
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 视图union多个带From Dispatch 的流表，并且包含Dispatch BY，后继view包含From Dispatch，不包含Dispatch BY，后继sink不包含From Dispatch 预期：成功
TEST_F(SupportDispatch, STREAM_030_Test_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id, time;"
        },

        {
            "create stream table t2 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id, time;"
        },
        {
            "create stream table t3 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id, time;"
        },
        {
            "create stream view v1 as select * from TABLE(UNION(TABLE(dispatch(table t1, 1, 2)), TABLE(dispatch(table t2, 1, 2)), TABLE(dispatch(table t3, 1, 2)))) "
            "DISPATCH BY id, time, age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select id, name, time, age, address from TABLE(dispatch(table v1, 1, 2, 13)) "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v2 "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    int streamNum = 3; 
    RdVertexLabelT *vertexLabels[streamNum] = {0};

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 15;
    int64_t id[rowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 5, 5, 5, 5, 5};
    int64_t time[rowNum] = {2, 2, 2, 2, 2, 6, 6, 6, 6, 6, 8, 8, 8, 8, 8};
    int64_t age[rowNum] = {18, 18, 13, 13, 15, 15, 15, 15, 12, 12, 12, 12, 13, 13, 13};

    // 解析建表SQL，生成结构化schema
    for (int i = 0; i < streamNum; i++){
        vertexLabels[i] = RdStreamParseTableSchema(creates[i + 1].sql);
        ASSERT_NE((RdVertexLabelT *)NULL, vertexLabels[i]);
        RdStructWriteStreamTableByArr(stmt, vertexLabels[i], rowNum, id, time, age, len);
    }

    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 6;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 1, 1, 1, 1, 1};
    int64_t expectTime[expectRowsCount] = {2, 2, 2, 2, 2, 2};
    int64_t expectAge[expectRowsCount] = {13, 13, 13, 13, 13, 13};
    RdCheckDataInTSTableByArr(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, expectTime, expectAge);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop stream table t2;"},
        {"drop stream table t3;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < streamNum; i++){
        RdStreamFreeTableSchema(vertexLabels[i]);
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 视图union多个带From Dispatch 的流表，并且包含Dispatch BY，后继view包含From Dispatch，包含Dispatch BY，后继sink包含From Dispatch 预期：成功
TEST_F(SupportDispatch, STREAM_030_Test_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id, time;"
        },

        {
            "create stream table t2 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id, time;"
        },
        {
            "create stream table t3 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id, time;"
        },
        {
            "create stream view v1 as select * from TABLE(UNION(TABLE(dispatch(table t1, 1, 2)), TABLE(dispatch(table t2, 1, 2)), TABLE(dispatch(table t3, 1, 2)))) "
            "DISPATCH BY id, time "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select id, name, time, age, address from TABLE(dispatch(table v1, 1, 2)) "
            "DISPATCH BY id, time, age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from TABLE(dispatch(table v2, 1, 2, 15)) "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    int streamNum = 3; 
    RdVertexLabelT *vertexLabels[streamNum] = {0};

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 15;
    int64_t id[rowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 5, 5, 5, 5, 5};
    int64_t time[rowNum] = {2, 2, 2, 2, 2, 6, 6, 6, 6, 6, 8, 8, 8, 8, 8};
    int64_t age[rowNum] = {18, 18, 13, 13, 15, 15, 15, 15, 12, 12, 12, 12, 13, 13, 13};

    // 解析建表SQL，生成结构化schema
    for (int i = 0; i < streamNum; i++){
        vertexLabels[i] = RdStreamParseTableSchema(creates[i + 1].sql);
        ASSERT_NE((RdVertexLabelT *)NULL, vertexLabels[i]);
        RdStructWriteStreamTableByArr(stmt, vertexLabels[i], rowNum, id, time, age, len);
    }

    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 3;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 1, 1};
    int64_t expectTime[expectRowsCount] = {2, 2, 2};
    int64_t expectAge[expectRowsCount] = {15, 15, 15};
    RdCheckDataInTSTableByArr(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, expectTime, expectAge);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop stream table t2;"},
        {"drop stream table t3;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < streamNum; i++){
        RdStreamFreeTableSchema(vertexLabels[i]);
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 视图union同1个流表，部分带From Dispatch，部分不带From Dispatch 预期：失败
TEST_F(SupportDispatch, STREAM_030_Test_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id, time;"
        },

        {
            "create stream table t2 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id, time;"
        },
        {
            "create stream table t3 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id, time;"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    const char *create = "create stream view v1 as select * from TABLE(UNION(TABLE t1, "
                         "TABLE(dispatch(table t1, 1, 2)), TABLE(dispatch(table t3, 2, 6)))) "
                         "DISPATCH BY id, time "
                         "with (tuple_buffer_size = 1);";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_CONSTRAINT_CHECK_VIOLATION, ret);

    

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream table t1;"},
        {"drop stream table t2;"},
        {"drop stream table t3;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 视图union同1个流表，都带From Dispatch，值相同 预期：失败
TEST_F(SupportDispatch, STREAM_030_Test_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id, time;"
        },

        {
            "create stream table t2 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id, time;"
        },
        {
            "create stream table t3 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id, time;"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    const char *create = "create stream view v1 as select * from TABLE(UNION(TABLE(dispatch(table t1, 1, 2)), "
                         "TABLE(dispatch(table t1, 1, 2)), TABLE(dispatch(table t1, 3, 6)))) "
                         "DISPATCH BY id, time "
                         "with (tuple_buffer_size = 1);";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_CONSTRAINT_CHECK_VIOLATION, ret);

    

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream table t1;"},
        {"drop stream table t2;"},
        {"drop stream table t3;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 视图union同1个流表，都带From Dispatch，部分值流表无数据对应，并且包含Dispatch BY，后继view不包含From Dispatch，包含Dispatch BY，后继sink不包含From Dispatch 预期：成功
TEST_F(SupportDispatch, STREAM_030_Test_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id, time;"
        },
        {
            "create stream view v1 as select * from TABLE(UNION(TABLE(dispatch(table t1, 1, 2)), TABLE(dispatch(table t1, 2, 6)), TABLE(dispatch(table t1, 3, 6)))) "
            "DISPATCH BY id, time "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select id, name, time, age, address from v1 "
            "DISPATCH BY id, time, age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v2 "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 15;
    int64_t id[rowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 5, 5, 5, 5, 5};
    int64_t time[rowNum] = {2, 2, 2, 2, 2, 6, 6, 6, 6, 6, 8, 8, 8, 8, 8};
    int64_t age[rowNum] = {18, 18, 13, 13, 15, 15, 15, 15, 12, 12, 12, 12, 13, 13, 13};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len);
    

    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 10;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2};
    int64_t expectTime[expectRowsCount] = {2, 2, 2, 2, 2, 6, 6, 6, 6, 6};
    int64_t expectAge[expectRowsCount] = {18, 18, 13, 13, 15, 15, 15, 15, 12, 12};
    RdCheckDataInTSTableByArr(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, expectTime, expectAge);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 视图同时union带From Dispatch 的流表和视图，并且包含Dispatch BY，后继sink不包含From Dispatch 预期：成功
TEST_F(SupportDispatch, STREAM_030_Test_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id, time;"
        },
        {
            "create stream view v1 as select * from TABLE(dispatch(table t1, 2, 6)) "
            "DISPATCH BY id, time, age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select id, name, time, age, address from TABLE(UNION(TABLE(dispatch(table t1, 1, 2)), "
            "TABLE(dispatch(table t1, 5, 8)), TABLE(dispatch(table v1, 2, 6, 15)))) "
            "DISPATCH BY id, time, age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v2 "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 15;
    int64_t id[rowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 5, 5, 5, 5, 5};
    int64_t time[rowNum] = {2, 2, 2, 2, 2, 6, 6, 6, 6, 6, 8, 8, 8, 8, 8};
    int64_t age[rowNum] = {18, 18, 13, 13, 15, 15, 15, 15, 12, 12, 12, 12, 13, 13, 13};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len);
    
    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 13;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 1, 1, 1, 1, 2, 2, 2, 5, 5, 5, 5, 5};
    int64_t expectTime[expectRowsCount] = {2, 2, 2, 2, 2, 6, 6, 6, 8, 8, 8, 8, 8};
    int64_t expectAge[expectRowsCount] = {18, 18, 13, 13, 15, 15, 15, 15, 12, 12, 13, 13, 13};
    RdCheckDataInTSTableByArr(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, expectTime, expectAge);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 视图同时union带From Dispatch 的流表和视图，并且包含Dispatch BY，后继sink包含From Dispatch 预期：成功
TEST_F(SupportDispatch, STREAM_030_Test_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id, time;"
        },
        {
            "create stream view v1 as select * from TABLE(dispatch(table t1, 2, 6)) "
            "DISPATCH BY id, time, age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select id, name, time, age, address from TABLE(UNION(TABLE(dispatch(table t1, 1, 2)), "
            "TABLE(dispatch(table t1, 5, 8)), TABLE(dispatch(table v1, 2, 6, 15)))) "
            "DISPATCH BY age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from TABLE(dispatch(table v2, 13)) "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 15;
    int64_t id[rowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 5, 5, 5, 5, 5};
    int64_t time[rowNum] = {2, 2, 2, 2, 2, 6, 6, 6, 6, 6, 8, 8, 8, 8, 8};
    int64_t age[rowNum] = {18, 18, 13, 13, 15, 15, 15, 15, 12, 12, 12, 12, 13, 13, 13};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len);
    

    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 5;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 1, 5, 5, 5};
    int64_t expectTime[expectRowsCount] = {2, 2, 8, 8, 8};
    int64_t expectAge[expectRowsCount] = {13, 13, 13, 13, 13};
    RdCheckDataInTSTableByArr(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, expectTime, expectAge);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

//  视图union同1个流表，都带From Dispatch，值不同，并且包含Dispatch BY，后继sink不包含From Dispatch 预期：成功
TEST_F(SupportDispatch, STREAM_030_Test_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id, time;"
        },
        {
            "create stream view v2 as select id, name, time, age, address from TABLE(UNION(TABLE(dispatch(table t1, 1, 2)), "
            "TABLE(dispatch(table t1, 5, 8)), TABLE(dispatch(table t1, 2, 6)))) "
            "DISPATCH BY id, time, age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v2 "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 15;
    int64_t id[rowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 5, 5, 5, 5, 5};
    int64_t time[rowNum] = {2, 2, 2, 2, 2, 6, 6, 6, 6, 6, 8, 8, 8, 8, 8};
    int64_t age[rowNum] = {18, 18, 13, 13, 15, 15, 15, 15, 12, 12, 12, 12, 13, 13, 13};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len);
    
    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 15;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 5, 5, 5, 5, 5};
    int64_t expectTime[expectRowsCount] = {2, 2, 2, 2, 2, 6, 6, 6, 6, 6, 8, 8, 8, 8, 8};
    int64_t expectAge[expectRowsCount] = {18, 18, 13, 13, 15, 15, 15, 15, 12, 12, 12, 12, 13, 13, 13};
    RdCheckDataInTSTableByArr(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, expectTime, expectAge);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v2;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 视图union同一个带From Dispatch 的视图，并且包含Dispatch BY，后继sink不包含From Dispatch 预期：成功
TEST_F(SupportDispatch, STREAM_030_Test_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id, time;"
        },
        {
            "create stream view v1 as select * from TABLE(dispatch(table t1, 1, 2)) "
            "DISPATCH BY age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select id, name, time, age, address from TABLE(UNION(TABLE(dispatch(table v1, 18)), "
            "TABLE(dispatch(table v1, 15)), TABLE(dispatch(table v1, 13)))) "
            "DISPATCH BY id, time, age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v2 "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 15;
    int64_t id[rowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 5, 5, 5, 5, 5};
    int64_t time[rowNum] = {2, 2, 2, 2, 2, 6, 6, 6, 6, 6, 8, 8, 8, 8, 8};
    int64_t age[rowNum] = {18, 18, 13, 13, 15, 15, 15, 15, 12, 12, 12, 12, 13, 13, 13};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len);
    
    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 5;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 1, 1, 1, 1};
    int64_t expectTime[expectRowsCount] = {2, 2, 2, 2, 2};
    int64_t expectAge[expectRowsCount] = {18, 18, 13, 13, 15};
    RdCheckDataInTSTableByArr(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, expectTime, expectAge);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 视图同时union多个带From Dispatch 的不同的视图，并且包含Dispatch BY，后继sink不包含From Dispatch 预期：成功
TEST_F(SupportDispatch, STREAM_030_Test_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id, time;"
        },
        {
            "create stream view v1 as select * from TABLE(dispatch(table t1, 1, 2)) "
            "DISPATCH BY age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v2 as select * from TABLE(dispatch(table t1, 1, 2)) "
            "DISPATCH BY age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v3 as select * from TABLE(dispatch(table t1, 1, 2)) "
            "DISPATCH BY age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream view v4 as select id, name, time, age, address from TABLE(UNION(TABLE(dispatch(table v1, 18)), "
            "TABLE(dispatch(table v3, 15)), TABLE(dispatch(table v2, 13)))) "
            "DISPATCH BY id, time, age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select * from v4 "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 15;
    int64_t id[rowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 5, 5, 5, 5, 5};
    int64_t time[rowNum] = {2, 2, 2, 2, 2, 6, 6, 6, 6, 6, 8, 8, 8, 8, 8};
    int64_t age[rowNum] = {18, 18, 13, 13, 15, 15, 15, 15, 12, 12, 12, 12, 13, 13, 13};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len);
    
    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 5;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 1, 1, 1, 1};
    int64_t expectTime[expectRowsCount] = {2, 2, 2, 2, 2};
    int64_t expectAge[expectRowsCount] = {18, 18, 13, 13, 15};
    RdCheckDataInTSTableByArr(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, expectTime, expectAge);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v4;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 视图union同1个视图，部分带From Dispatch，部分不带From Dispatch 预期：失败
TEST_F(SupportDispatch, STREAM_030_Test_059)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id, time;"
        },
        {
            "create stream view v1 as select * from TABLE(dispatch(table t1, 1, 2)) "
            "DISPATCH BY age "
            "with (tuple_buffer_size = 1);"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    const char *create = "create stream view v2 as select * from TABLE(UNION(TABLE v1, "
                         "TABLE(dispatch(table v1, 18)), TABLE(dispatch(table v1, 15)))) "
                         "DISPATCH BY id, time "
                         "with (tuple_buffer_size = 1);";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_CONSTRAINT_CHECK_VIOLATION, ret);

    

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 视图union同1个视图，都带From Dispatch，值相同 预期：失败
TEST_F(SupportDispatch, STREAM_030_Test_060)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id, time;"
        },
        {
            "create stream view v1 as select * from TABLE(dispatch(table t1, 1, 2)) "
            "DISPATCH BY age "
            "with (tuple_buffer_size = 1);"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    const char *create = "create stream view v2 as select * from TABLE(UNION(TABLE(dispatch(table v1, 18)), "
                         "TABLE(dispatch(table v1, 18)), TABLE(dispatch(table v1, 18)))) "
                         "DISPATCH BY id, time "
                         "with (tuple_buffer_size = 1);";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_CONSTRAINT_CHECK_VIOLATION, ret);

    

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// sink结点同时union带From Dispatch 的流表和视图，后继sink不包含From Dispatch 预期：成功
TEST_F(SupportDispatch, STREAM_030_Test_061)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time strict) "
            "DISPATCH BY id, time;"
        },
        {
            "create stream view v1 as select * from TABLE(dispatch(table t1, 2, 6)) "
            "DISPATCH BY id, time, age "
            "with (tuple_buffer_size = 1);"
        },
        {
            "create stream sink s1 as select id, name, time, age, address from TABLE(UNION(TABLE(dispatch(table t1, 1, 2)), "
            "TABLE(dispatch(table t1, 5, 8)), TABLE(dispatch(table v1, 2, 6, 15)))) "
            "into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');"
        },
        
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    ASSERT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 120;
    uint32_t rowNum = 15;
    int64_t id[rowNum] = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 5, 5, 5, 5, 5};
    int64_t time[rowNum] = {2, 2, 2, 2, 2, 6, 6, 6, 6, 6, 8, 8, 8, 8, 8};
    int64_t age[rowNum] = {18, 18, 13, 13, 15, 15, 15, 15, 12, 12, 12, 12, 13, 13, 13};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len);
    
    // 检查时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 13;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 1, 1, 1, 1, 2, 2, 2, 5, 5, 5, 5, 5};
    int64_t expectTime[expectRowsCount] = {2, 2, 2, 2, 2, 6, 6, 6, 8, 8, 8, 8, 8};
    int64_t expectAge[expectRowsCount] = {18, 18, 13, 13, 15, 15, 15, 15, 12, 12, 13, 13, 13};
    RdCheckDataInTSTableByArr(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, expectTime, expectAge);

    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}







