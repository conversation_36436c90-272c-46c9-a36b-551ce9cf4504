/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-02-25 16:04:15
 * @FilePath: \GMDBV5\test\sdv\testcases\34_stream\036_support_avg\04_support_avg_interaction_over.cpp
 * @Description: 
 * @LastEditors: t<PERSON><PERSON><PERSON> 
 * @LastEditTime: 2025-03-06 15:23:52
 */

#include "gtest/gtest.h"
#include "securec.h"
#include "gmc_sql.h"
#include "t_rd_sn.h"
#include "rd_feature_stream.h"
#include "rd_feature_ts.h"

#include "avg_util.h"


class SupportAvg : public testing::Test {
public:
    static GmcConnT *conn;
    static GmcStmtT *stmt;

    bool hasFinishTest = false;
    void RdFinishTest();

    static void SetUpTestCase();
    static void TearDownTestCase();
    virtual void SetUp();
    virtual void TearDown();
};

GmcConnT *SupportAvg::conn = NULL;
GmcStmtT *SupportAvg::stmt = NULL;

void SupportAvg::SetUpTestCase()
{
    int32_t ret;
    ret = RdStreamEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void SupportAvg::TearDownTestCase()
{
    int32_t ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = RdStreamEnvClean();
    EXPECT_EQ(GMERR_OK, ret);
}

void SupportAvg::SetUp()
{   
    // 创建客户端连接
    int32_t ret = RdGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void SupportAvg::TearDown()
{
    int32_t ret = RdGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 通过over进行聚合计算，同时包含count和avg函数，参数为相同列 预期：成功
TEST_F(SupportAvg, STREAM_036_INTERACTION_OVER_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, avgCol integer, countCol integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
       {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time - interval '10' seconds tolerant);"
        },
        {
            "create stream view v1 as select id, name, time, avg(id) over (PARTITION BY window_start, window_end, age), "
            "count(id) over (PARTITION BY window_start, window_end, age), address from "
            "table(hop(table t1, time, interval '20' seconds, interval '10' seconds)) "
            "with (tuple_buffer_size = 1);"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, avg_id, count_id, address FROM v1 "
            "INTO tsdb(ts1) with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 7;
    uint16_t fixedLen = 10;
    uint32_t rowNum = 19;
    int64_t id[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41}; 
    int64_t time[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t age[rowNum] = {10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len, fixedLen);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 10;
    uint32_t expectColsCount = 6;
    int64_t expectId[expectRowsCount] = {1, 7, 3, 5, 20, 24, 24, 22, 21, 25};
    int64_t expectTime[expectRowsCount] = {1, 7, 3, 5, 20, 24, 24, 22, 21, 25};
    int64_t expectAvg[expectRowsCount] = {4, 4, 3, 5, 22, 22, 23, 23, 23, 23};
    int64_t expectCount[expectRowsCount] = {2, 2, 1, 1, 2, 2, 2, 2, 2, 2};
    RdCheckDataInTSTableByArrWithTwoOver(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, 
                                         expectTime, expectCount, expectAvg, len, fixedLen);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 通过over进行聚合计算，同时包含count和avg函数，参数为不同列 预期：成功
TEST_F(SupportAvg, STREAM_036_INTERACTION_OVER_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, avgCol integer, countCol integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
       {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time - interval '10' seconds tolerant);"
        },
        {
            "create stream view v1 as select id, name, time, avg(id) over (PARTITION BY window_start, window_end, age), "
            "count(age) over (PARTITION BY window_start, window_end, age), address from "
            "table(hop(table t1, time, interval '20' seconds, interval '10' seconds)) "
            "with (tuple_buffer_size = 1);"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, avg_id, count_age, address FROM v1 "
            "INTO tsdb(ts1) with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 7;
    uint16_t fixedLen = 10;
    uint32_t rowNum = 19;
    int64_t id[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41}; 
    int64_t time[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t age[rowNum] = {10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len, fixedLen);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 10;
    uint32_t expectColsCount = 6;
    int64_t expectId[expectRowsCount] = {1, 7, 3, 5, 20, 24, 24, 22, 21, 25};
    int64_t expectTime[expectRowsCount] = {1, 7, 3, 5, 20, 24, 24, 22, 21, 25};
    int64_t expectAvg[expectRowsCount] = {4, 4, 3, 5, 22, 22, 23, 23, 23, 23};
    int64_t expectCount[expectRowsCount] = {2, 2, 1, 1, 2, 2, 2, 2, 2, 2};
    RdCheckDataInTSTableByArrWithTwoOver(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, 
                                         expectTime, expectCount, expectAvg, len, fixedLen);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 通过over进行聚合计算，同时包含sum和avg函数，参数为相同列 预期：成功
TEST_F(SupportAvg, STREAM_036_INTERACTION_OVER_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, avgCol integer, sumCol integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
       {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time - interval '10' seconds tolerant);"
        },
        {
            "create stream view v1 as select id, name, time, avg(id) over (PARTITION BY window_start, window_end, age), "
            "sum(id) over (PARTITION BY window_start, window_end, age), address from "
            "table(hop(table t1, time, interval '20' seconds, interval '10' seconds)) "
            "with (tuple_buffer_size = 1);"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, avg_id, sum_id, address FROM v1 "
            "INTO tsdb(ts1) with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 7;
    uint16_t fixedLen = 10;
    uint32_t rowNum = 19;
    int64_t id[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41}; 
    int64_t time[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t age[rowNum] = {10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len, fixedLen);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 10;
    uint32_t expectColsCount = 6;
    int64_t expectId[expectRowsCount] = {1, 7, 3, 5, 20, 24, 24, 22, 21, 25};
    int64_t expectTime[expectRowsCount] = {1, 7, 3, 5, 20, 24, 24, 22, 21, 25};
    int64_t expectAvg[expectRowsCount] = {4, 4, 3, 5, 22, 22, 23, 23, 23, 23};
    int64_t expectSum[expectRowsCount] = {8, 8, 3, 5, 44, 44, 46, 46, 46, 46};
    RdCheckDataInTSTableByArrWithTwoOver(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, 
                                         expectTime, expectSum, expectAvg, len, fixedLen);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 通过over进行聚合计算，同时包含sum和avg函数，参数为不同列 预期：成功
TEST_F(SupportAvg, STREAM_036_INTERACTION_OVER_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, avgCol integer, sumCol integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
       {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time - interval '10' seconds tolerant);"
        },
        {
            "create stream view v1 as select id, name, time, avg(id) over (PARTITION BY window_start, window_end, age), "
            "sum(age) over (PARTITION BY window_start, window_end, age), address from "
            "table(hop(table t1, time, interval '20' seconds, interval '10' seconds)) "
            "with (tuple_buffer_size = 1);"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, avg_id, sum_age, address FROM v1 "
            "INTO tsdb(ts1) with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 7;
    uint16_t fixedLen = 10;
    uint32_t rowNum = 19;
    int64_t id[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41}; 
    int64_t time[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t age[rowNum] = {10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len, fixedLen);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 10;
    uint32_t expectColsCount = 6;
    int64_t expectId[expectRowsCount] = {1, 7, 3, 5, 20, 24, 24, 22, 21, 25};
    int64_t expectTime[expectRowsCount] = {1, 7, 3, 5, 20, 24, 24, 22, 21, 25};
    int64_t expectAvg[expectRowsCount] = {4, 4, 3, 5, 22, 22, 23, 23, 23, 23};
    int64_t expectSum[expectRowsCount] = {20, 20, 11, 12, 20, 20, 22, 22, 24, 24};
    RdCheckDataInTSTableByArrWithTwoOver(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, 
                                         expectTime, expectSum, expectAvg, len, fixedLen);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 通过over进行聚合计算，同时包含row_number和avg函数 预期：成功
TEST_F(SupportAvg, STREAM_036_INTERACTION_OVER_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, avgCol integer, rownumCol integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
       {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time - interval '10' seconds tolerant);"
        },
        {
            "create stream view v1 as select id, name, time, avg(id) over (PARTITION BY window_start, window_end, age), "
            "row_number() over (PARTITION BY window_start, window_end, age), address from "
            "table(hop(table t1, time, interval '20' seconds, interval '10' seconds)) "
            "with (tuple_buffer_size = 1);"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, avg_id, row_number, address FROM v1 "
            "INTO tsdb(ts1) with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 7;
    uint16_t fixedLen = 10;
    uint32_t rowNum = 19;
    int64_t id[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41}; 
    int64_t time[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t age[rowNum] = {10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len, fixedLen);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 10;
    uint32_t expectColsCount = 6;
    int64_t expectId[expectRowsCount] = {1, 7, 3, 5, 20, 24, 24, 22, 21, 25};
    int64_t expectTime[expectRowsCount] = {1, 7, 3, 5, 20, 24, 24, 22, 21, 25};
    int64_t expectAvg[expectRowsCount] = {4, 4, 3, 5, 22, 22, 23, 23, 23, 23};
    int64_t expectLineNum[expectRowsCount] = {1, 2, 1, 1, 1, 2, 1, 2, 1, 2};
    RdCheckDataInTSTableByArrWithTwoOver(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, 
                                         expectTime, expectLineNum, expectAvg, len, fixedLen);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 通过over进行聚合计算，同时包含format函数和avg函数 预期：成功
TEST_F(SupportAvg, STREAM_036_INTERACTION_OVER_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, avgCol integer, formatCol char(50), address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
       {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time - interval '10' seconds tolerant);"
        },
        {
            "create stream view v1 as select id, name, time, age, avg(id) over (PARTITION BY window_start, window_end, age), "
            "format('My age is %d', age), address from "
            "table(hop(table t1, time, interval '20' seconds, interval '10' seconds)) "
            "with (tuple_buffer_size = 1);"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, age, avg_id, format_age, address FROM v1 "
            "INTO tsdb(ts1) with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 7;
    uint16_t fixedLen = 10;
    uint32_t rowNum = 19;
    int64_t id[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41}; 
    int64_t time[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t age[rowNum] = {10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len, fixedLen);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 10;
    uint32_t expectColsCount = 7;
    int64_t expectId[expectRowsCount] = {1, 7, 3, 5, 20, 24, 24, 22, 21, 25};
    int64_t expectTime[expectRowsCount] = {1, 7, 3, 5, 20, 24, 24, 22, 21, 25};
    int64_t expectAge[expectRowsCount] = {10, 10, 11, 12, 10, 10, 11, 11, 12, 12};
    int64_t expectAvg[expectRowsCount] = {4, 4, 3, 5, 22, 22, 23, 23, 23, 23};
    RdCheckDataInTSTableByArrWithFormat(stmt, selectTsName, expectRowsCount, expectColsCount, 
                                        expectId, expectTime, expectAge, expectAvg, len, fixedLen);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 通过over进行聚合计算，同时包含max、min、sum、count和avg函数 预期：成功
TEST_F(SupportAvg, STREAM_036_INTERACTION_OVER_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
 
    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
            "create table ts1 (id integer, name text, time integer, "
            "avgCol integer, sumCol integer, countCol integer, "
            "maxCol integer, minCol integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
       {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time - interval '10' seconds tolerant);"
        },
        {
            "create stream view v1 as select id, name, time, avg(id) over (PARTITION BY window_start, window_end, age), "
            "sum(age) over (PARTITION BY window_start, window_end, age), "
            "count(age) over (PARTITION BY window_start, window_end, age), "
            "max(id) over (PARTITION BY window_start, window_end, age), "
            "min(id) over (PARTITION BY window_start, window_end, age), "
            "address from "
            "table(hop(table t1, time, interval '20' seconds, interval '10' seconds)) "
            "with (tuple_buffer_size = 1);"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, avg_id, sum_age, "
            "count_age, max_id, min_id, address FROM v1 "
            "INTO tsdb(ts1) with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 7;
    uint16_t fixedLen = 10;
    uint32_t rowNum = 19;
    int64_t id[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41}; 
    int64_t time[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t age[rowNum] = {10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len, fixedLen);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 10;
    uint32_t expectColsCount = 9;
    int64_t expectId[expectRowsCount] = {1, 7, 3, 5, 20, 24, 24, 22, 21, 25};
    int64_t expectTime[expectRowsCount] = {1, 7, 3, 5, 20, 24, 24, 22, 21, 25};
    int64_t expectAvg[expectRowsCount] = {4, 4, 3, 5, 22, 22, 23, 23, 23, 23};
    int64_t expectSum[expectRowsCount] = {20, 20, 11, 12, 20, 20, 22, 22, 24, 24};
    int64_t expectCount[expectRowsCount] = {2, 2, 1, 1, 2, 2, 2, 2, 2, 2};
    int64_t expectMax[expectRowsCount] = {7, 7, 3, 5, 24, 24, 24, 24, 25, 25};
    int64_t expectMin[expectRowsCount] = {1, 1, 3, 5, 20, 20, 22, 22, 21, 21};
    RdCheckDataInTSTableByArrWithFiveOver(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, expectTime,
                                         expectAvg, expectSum, expectCount, expectMax, expectMin, len, fixedLen);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 通过over进行聚合计算，同时包含distinct函数和avg函数 预期：报错
TEST_F(SupportAvg, STREAM_036_INTERACTION_OVER_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, distinCol integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
        {
            "create stream reference aref(integer, integer) with (mini = 'true');"
        },
       {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time - interval '10' seconds tolerant);"
        },
        {
            "create stream view v1 as select * from t1 "
            "with (tuple_buffer_size = 1);"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    char *create = (char *) "create stream view v2 as select id, name, time, avg(id) over "
                            "(PARTITION BY window_start, window_end, age), seq_distinct_count(id, name), address from "
                            "table(hop(table t1, time, interval '20' seconds, interval '10' seconds)) "
                            "group by window_start, window_end, age "
                            "with (tuple_buffer_size = 1);";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SEMANTIC_ERROR, ret);

    create = (char *) "create stream sink s1 as select id, name, time, avg(id) over "
                            "(PARTITION BY window_start, window_end, age), seq_distinct_count(id, name), address from "
                            "table(hop(table v1, time, interval '20' seconds, interval '10' seconds)) "
                            "into tsdb(ts1) "
                            "with (batch_window_size = '1', tuple_buffer_size = '1');";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SEMANTIC_ERROR, ret);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop stream reference aref;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 通过over进行聚合计算，同时包含ref和avg函数 预期：成功
TEST_F(SupportAvg, STREAM_036_INTERACTION_OVER_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, avgCol integer, countCol integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
       {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time - interval '10' seconds tolerant);"
        },
        {
            "create stream reference aref(integer, integer) with (mini = 'true');"
        },
        {
            "upsert into streamref aref values (10, 0);"
        },
        {
            "upsert into streamref aref values (11, 1);"
        },
        {
            "upsert into streamref aref values (12, 2);"
        },
        {
            "create stream view v1 as select id, name, time, avg(id) over (PARTITION BY window_start, window_end, age), "
            "ref['aref'][age], address from "
            "table(hop(table t1, time, interval '20' seconds, interval '10' seconds)) "
            "with (tuple_buffer_size = 1);"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, avg_id, ref_aref_age, address FROM v1 "
            "INTO tsdb(ts1) with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 7;
    uint16_t fixedLen = 10;
    uint32_t rowNum = 19;
    int64_t id[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41}; 
    int64_t time[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t age[rowNum] = {10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len, fixedLen);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 10;
    uint32_t expectColsCount = 6;
    int64_t expectId[expectRowsCount] = {1, 7, 3, 5, 20, 24, 24, 22, 21, 25};
    int64_t expectTime[expectRowsCount] = {1, 7, 3, 5, 20, 24, 24, 22, 21, 25};
    int64_t expectAvg[expectRowsCount] = {4, 4, 3, 5, 22, 22, 23, 23, 23, 23};
    int64_t expectRef[expectRowsCount] = {0, 0, 1, 2, 0, 0, 1, 1, 2, 2};
    RdCheckDataInTSTableByArrWithTwoOver(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, 
                                         expectTime, expectRef, expectAvg, len, fixedLen);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop stream reference aref;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 通过over进行聚合计算，投影列包含avg函数，和where混合使用 预期：成功
TEST_F(SupportAvg, STREAM_036_INTERACTION_OVER_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
       {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time - interval '10' seconds tolerant);"
        },
        {
            "create stream view v1 as select id, name, time, avg(id) over (PARTITION BY window_start, window_end, age), address from "
            "table(hop(table t1, time, interval '20' seconds, interval '10' seconds)) "
            "where id < 20 "
            "with (tuple_buffer_size = 1);"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, avg_id, address FROM v1 "
            "INTO tsdb(ts1) with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 7;
    uint16_t fixedLen = 10;
    uint32_t rowNum = 19;
    int64_t id[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41}; 
    int64_t time[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t age[rowNum] = {10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len, fixedLen);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 4;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 7, 3, 5};
    int64_t expectTime[expectRowsCount] = {1, 7, 3, 5};
    int64_t expectAvg[expectRowsCount] = {4, 4, 3, 5};
    RdCheckDataInTSTableByArr(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, expectTime, expectAvg, len, fixedLen);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 通过over进行聚合计算，投影列包含avg函数，和dispatch混合使用 预期：成功
TEST_F(SupportAvg, STREAM_036_INTERACTION_OVER_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
       {
            "create stream table t1 (id integer, name text, time integer, age integer, address char(50), "
            "watermark for time as time - interval '10' seconds tolerant) "
            "DISPATCH BY age;"
        },
        {
            "create stream view v1 as select id, name, time, avg(id) over (PARTITION BY window_start, window_end, age), address from "
            "table(hop(table dispatch(table t1, 10), time, interval '20' seconds, interval '10' seconds)) "
            "with (tuple_buffer_size = 1);"
        },
        {
            "CREATE STREAM SINK s1 AS select id, name, time, avg_id, address FROM v1 "
            "INTO tsdb(ts1) with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 7;
    uint16_t fixedLen = 10;
    uint32_t rowNum = 19;
    int64_t id[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41}; 
    int64_t time[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t age[rowNum] = {10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len, fixedLen);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 4;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 7, 20, 24};
    int64_t expectTime[expectRowsCount] = {1, 7, 20, 24};
    int64_t expectAvg[expectRowsCount] = {4, 4, 22, 22};
    RdCheckDataInTSTableByArr(stmt, selectTsName, expectRowsCount, expectColsCount, expectId, expectTime, expectAvg, len, fixedLen);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}



