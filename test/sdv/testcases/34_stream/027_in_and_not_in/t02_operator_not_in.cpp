/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: not in操作符功能测试
 * Author: guopanpan
 * Create: 2024-12-17
 */
#include "gtest/gtest.h"
#include "gmc_sql.h"
#include "rd_feature_stream.h"
#include "rd_feature_ts.h"
#include "rd_stream_twenty_seven.h"

class t02_operator_not_in : public testing::Test {
public:
    static GmcConnT *longConn;
    static GmcStmtT *longStmt;
    GmcConnT *shortConn = NULL;
    GmcStmtT *shortStmt = NULL;
    GmcConnT *asyncConn = NULL;
    GmcStmtT *asyncStmt = NULL;
    RdVertexLabelT *vertexLabel = NULL;

    bool hasFinishTest = false;
    void RdFinishTest();

    static void SetUpTestCase();
    static void TearDownTestCase();
    virtual void SetUp();
    virtual void TearDown();
};

GmcConnT *t02_operator_not_in::longConn = NULL;
GmcStmtT *t02_operator_not_in::longStmt = NULL;

void t02_operator_not_in::SetUpTestCase()
{
    int32_t ret;
    ret = RdStreamEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdGmcConnect(&longConn, &longStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdCreateEpollThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdLogSetLevel4Stdout(RD_LOG_LEVEL_INFO);
}

void t02_operator_not_in::TearDownTestCase()
{
    int32_t ret;
    ret = RdGmcDisconnect(longConn, longStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdStreamEnvClean();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdCloseEpollThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void t02_operator_not_in::SetUp()
{
    int32_t ret;
    hasFinishTest = false;
    ret = RdGmcConnect(&shortConn, &shortStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdGmcConnect(&asyncConn, &asyncStmt, GMC_CONN_TYPE_ASYNC);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void t02_operator_not_in::TearDown()
{
    int32_t ret;
    if (!hasFinishTest) {
        RdStreamExecDescT clean[] = {
            {"drop stream sink s1;"},
            {"drop stream view v3;"},
            {"drop stream view v2;"},
            {"drop stream view v1;"},
            {"drop stream table t1;"},
            {"drop table ts1;"},
        };
        ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        RdStreamFreeTableSchema(vertexLabel);
        vertexLabel = NULL;
    }

    ret = RdGmcDisconnect(shortConn, shortStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdGmcDisconnect(asyncConn, asyncStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void t02_operator_not_in::RdFinishTest()
{
    hasFinishTest = true;
}

// @TestSuite 02_NOT IN功能测试
// @TestcaseName 创建视图时通过NOT IN过滤整形数据，写入满足过滤条件的数据 成功
TEST_F(t02_operator_not_in, STREAM_027_031)
{
    // DEMO NOT IN和NOT NOT IN基础功能测试
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v1 as select * from t1 where id not in (3, 6, 9, 12, 15) with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select * from v3 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 2. 创建视图时通过NOT IN过滤整形数据，写入满足过滤条件的数据
    int64_t insertIds[] = {4, 7, 4, 10, 13, 4, 13};
    for (size_t i = 0; i < RD_ELEMENT_COUNT(insertIds); i++) {
        ret = RdTwentySevenStructWrite(shortStmt, vertexLabel, insertIds[i], 1, 0);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 3. 查询数据, 有预期结果
    int64_t qryIds[] = {4, 7, 10, 13};
    int64_t qryCnt[] = {3, 1, 1,  2};
    for (size_t i = 0; i < RD_ELEMENT_COUNT(qryIds); i++) {
        ret = RdTwentySevenQueryTsTableById(shortStmt, "ts1", qryIds[i], qryCnt[i], 0);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = RdTsCheckRecordCount(shortStmt, "ts1", 7);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 4. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;
    RdFinishTest();
}

// @TestcaseName 创建视图时通过NOT IN过滤整形数据，写入不满足过滤条件的数据 数据未写入表中
TEST_F(t02_operator_not_in, STREAM_027_032)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 where id not in (1, 2, 5, 4, 8, 7) with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select * from v3 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 2. 创建视图时通过NOT IN过滤整形数据，写入不满足过滤条件的数据
    int64_t insertIds[] = {2, 7, 2, 8, 4, 1, 1};
    for (size_t i = 0; i < RD_ELEMENT_COUNT(insertIds); i++) {
        ret = RdTwentySevenStructWrite(shortStmt, vertexLabel, insertIds[i], 1, 0);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 3. 查询数据, 有预期结果
    ret = RdTsCheckRecordCount(shortStmt, "ts1", 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 4. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;
    RdFinishTest();
}

// @TestcaseName 创建视图时通过NOT IN过滤整形数据，写入满足和不满足过滤条件的数据 仅满足条件的数据写入表中
TEST_F(t02_operator_not_in, STREAM_027_033)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 where id not in (3, 6, 9, 12, 90) with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select * from v3 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 2. 创建视图时通过NOT IN过滤整形数据，写入满足和不满足过滤条件的数据
    uint32_t rowNum = 100;
    ret = RdTwentySevenStructWrite(shortStmt, vertexLabel, 0, rowNum, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 3. 查询数据, 有预期结果
    uint32_t count = 0;
    for (uint32_t i = 0; i < rowNum; i++) {
        if (i == 3 || i == 6 || i ==9 || i == 12 || i == 90) {
            ret = RdTwentySevenCheckIdExist(shortStmt, "ts1", i, false);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = RdTwentySevenCheckIdExist(shortStmt, "ts1", i, true);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            count++;
        }
    }
    ret = RdTsCheckRecordCount(shortStmt, "ts1", count);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 4. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;
    RdFinishTest();
}

// @TestcaseName 创建视图时通过NOT IN过滤整形数据，写入随机数据 仅满足条件的数据写入表中
TEST_F(t02_operator_not_in, STREAM_027_034)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v1 as select * from t1 where id not in (2, 3, 5, 6, 7, 8) with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 where id not in (3, 5, 7, 8, 9, 0) with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 where id not in (5, 7, 10, 20, 30) with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select * from v3 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 2. 创建视图时通过NOT IN过滤整形数据，写入随机数据
    const uint32_t rowNum = 1000;
    int64_t ids[rowNum] = {0};
    for (size_t i = 0; i < rowNum; i++) {
        ids[i] = random() % 10;
        ret = RdTwentySevenStructWrite(shortStmt, vertexLabel, ids[i], 1, 0);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 3. 查询数据, 有预期结果
    uint32_t count = 0;
    for (size_t i = 0; i < rowNum; i++) {
        if (ids[i] == 1 || ids[i] == 4) {
            ret = RdTwentySevenCheckIdExist(shortStmt, "ts1", ids[i], true);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            count++;
        } else {
            ret = RdTwentySevenCheckIdExist(shortStmt, "ts1", ids[i], false);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }
    RD_INFO("count = %ld", count);
    ret = RdTsCheckRecordCount(shortStmt, "ts1", count);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 4. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;
    RdFinishTest();
}

// @TestcaseName 创建视图时通过NOT IN过滤整形数据，NOT IN前是时间列 成功
TEST_F(t02_operator_not_in, STREAM_027_035)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v1 as select * from t1 where time not in (1, 3, 5, 6) with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select * from v3 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 2. 创建视图时通过NOT IN过滤整形数据，NOT IN前是时间列，再写入数据
    int64_t insertIds[] = {3, 6, 3, 9, 12, 3, 12};
    for (size_t i = 0; i < RD_ELEMENT_COUNT(insertIds); i++) {
        ret = RdTwentySevenStructWrite(shortStmt, vertexLabel, insertIds[i], 1, 0);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 3. 查询数据, 有预期结果
    int64_t qryIds[] = {9, 12};
    int64_t qryCnt[] = {1, 2};
    for (size_t i = 0; i < RD_ELEMENT_COUNT(qryIds); i++) {
        ret = RdTwentySevenQueryTsTableById(shortStmt, "ts1", qryIds[i], qryCnt[i], 0);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = RdTsCheckRecordCount(shortStmt, "ts1", 3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 4. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;
    RdFinishTest();
}

// @TestcaseName 创建视图的语句中包含NOT IN，NOT IN前存在多个列 报错，语法错误
TEST_F(t02_operator_not_in, STREAM_027_036)
{
    // DEMO NOT IN和NOT NOT IN异常场景测试
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT createTests[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '5' seconds strict);"},

        // 2. 创建视图的语句中包含NOT IN，NOT IN前存在多个列
        {
            "create stream view v1 as select * from t1 where id, time not in (1, 3, 5) with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"create stream sink s1 as select * from v3 into tsdb (ts1) with (batch_window_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
    };
    ret = RdStreamExecSql(shortStmt, createTests, sizeof(createTests));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(createTests[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 3. 清理测试数据
    RdStreamExecDescT dropTests[] = {
        {"drop stream sink s1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v3;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v2;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, dropTests, sizeof(dropTests));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;
    RdFinishTest();
}

// @TestcaseName 创建视图的语句中包含NOT IN，NOT IN后没有参数 报错，语法错误
TEST_F(t02_operator_not_in, STREAM_027_037)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT createTests[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '5' seconds strict);"},

        // 2. 创建视图的语句中包含NOT IN，NOT IN后没有参数
        {
            "create stream view v1 as select * from t1 where id time not in with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"create stream sink s1 as select * from v3 into tsdb (ts1) with (batch_window_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
    };
    ret = RdStreamExecSql(shortStmt, createTests, sizeof(createTests));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(createTests[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 3. 清理测试数据
    RdStreamExecDescT dropTests[] = {
        {"drop stream sink s1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v3;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v2;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, dropTests, sizeof(dropTests));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;
    RdFinishTest();
}

// @TestcaseName 创建视图的语句中包含NOT IN，NOT IN后的括号里没有参数 报错，语法错误
TEST_F(t02_operator_not_in, STREAM_027_038)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT createTests[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '5' seconds strict);"},

        // 2. 创建视图的语句中包含NOT IN，NOT IN后的括号里没有参数
        {
            "create stream view v1 as select * from t1 where id not in () with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"create stream sink s1 as select * from v3 into tsdb (ts1) with (batch_window_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
    };
    ret = RdStreamExecSql(shortStmt, createTests, sizeof(createTests));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(createTests[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 3. 清理测试数据
    RdStreamExecDescT dropTests[] = {
        {"drop stream sink s1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v3;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v2;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, dropTests, sizeof(dropTests));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;
    RdFinishTest();
}

// @TestcaseName 创建视图的语句中包含NOT IN，NOT IN后的参数没有括号修饰 报错，语法错误
TEST_F(t02_operator_not_in, STREAM_027_039)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT createTests[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '5' seconds strict);"},

        // 2. 创建视图的语句中包含NOT IN，NOT IN后的参数没有括号修饰
        {
            "create stream view v1 as select * from t1 where id not in 1, 3, 5 with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"create stream sink s1 as select * from v3 into tsdb (ts1) with (batch_window_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
    };
    ret = RdStreamExecSql(shortStmt, createTests, sizeof(createTests));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(createTests[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 3. 清理测试数据
    RdStreamExecDescT dropTests[] = {
        {"drop stream sink s1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v3;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v2;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, dropTests, sizeof(dropTests));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;
    RdFinishTest();
}

// @TestcaseName 创建视图时通过NOT IN过滤整形数据，NOT IN后的列表存在重复的数据 成功
TEST_F(t02_operator_not_in, STREAM_027_040)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v1 as select * from t1 where id not in (8, 3, 3, 3, 4, 3, 7, 3, 9, 3) "
            "with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select * from v3 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 2. 创建视图时通过NOT IN过滤整形数据，NOT IN后的列表存在重复的数据，再写入数据
    int64_t insertIds[] = {3, 6, 3, 9, 12, 3, 12, 7, 7};
    for (size_t i = 0; i < RD_ELEMENT_COUNT(insertIds); i++) {
        ret = RdTwentySevenStructWrite(shortStmt, vertexLabel, insertIds[i], 1, 0);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 3. 查询数据, 有预期结果
    int64_t qryIds[] = {6, 12};
    int64_t qryCnt[] = {1, 2};
    for (size_t i = 0; i < RD_ELEMENT_COUNT(qryIds); i++) {
        ret = RdTwentySevenQueryTsTableById(shortStmt, "ts1", qryIds[i], qryCnt[i], 0);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = RdTsCheckRecordCount(shortStmt, "ts1", 3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 4. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;
    RdFinishTest();
}

// @TestcaseName 创建视图时通过NOT IN过滤整形数据，NOT IN后是使用括号修饰的单个正数 成功
TEST_F(t02_operator_not_in, STREAM_027_041)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v1 as select * from t1 where id not in (3) with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select * from v3 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 2. 创建视图时通过NOT IN过滤整形数据，NOT IN后是使用括号修饰的单个正数，再写入数据
    int64_t insertIds[] = {3, 6, 3, 9, 12, 3, 12};
    for (size_t i = 0; i < RD_ELEMENT_COUNT(insertIds); i++) {
        ret = RdTwentySevenStructWrite(shortStmt, vertexLabel, insertIds[i], 1, 0);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 3. 查询数据, 有预期结果
    int64_t qryIds[] = {6, 9, 12};
    int64_t qryCnt[] = {1, 1, 2};
    for (size_t i = 0; i < RD_ELEMENT_COUNT(qryIds); i++) {
        ret = RdTwentySevenQueryTsTableById(shortStmt, "ts1", qryIds[i], qryCnt[i], 0);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = RdTsCheckRecordCount(shortStmt, "ts1", 4);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 4. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;
    RdFinishTest();
}

// @TestcaseName 创建视图时通过NOT IN过滤整形数据，NOT IN后是使用括号修饰的一组正数 成功
TEST_F(t02_operator_not_in, STREAM_027_042)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v1 as select * from t1 where id not in (1, 2, 3, 4, 5, 6, 7, 8, 9) "
            "with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select * from v3 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 2. 创建视图时通过NOT IN过滤整形数据，NOT IN后是使用括号修饰的一组正数，再写入数据
    int64_t insertIds[] = {3, 6, 3, 9, 12, 3, 12, 13, 14, 15};
    for (size_t i = 0; i < RD_ELEMENT_COUNT(insertIds); i++) {
        ret = RdTwentySevenStructWrite(shortStmt, vertexLabel, insertIds[i], 1, 0);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 3. 查询数据, 有预期结果
    int64_t qryIds[] = {12, 13, 14, 15};
    int64_t qryCnt[] = {2,  1,  1,  1};
    for (size_t i = 0; i < RD_ELEMENT_COUNT(qryIds); i++) {
        ret = RdTwentySevenQueryTsTableById(shortStmt, "ts1", qryIds[i], qryCnt[i], 0);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = RdTsCheckRecordCount(shortStmt, "ts1", 5);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 4. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;
    RdFinishTest();
}

// @TestcaseName 创建视图时通过NOT IN过滤整形数据，NOT IN后是使用括号修饰的一组正数和负数 成功
TEST_F(t02_operator_not_in, STREAM_027_043)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '5' seconds strict);"},

        // HISTORY 2024-12-19 暂不支持负数
        // 2. 创建视图时通过NOT IN过滤整形数据，NOT IN后是使用括号修饰的一组正数和负数，再写入数据
        {"create stream view v1 as select * from t1 where id not in (-10, 0, 8, 9, -3, 0, -2, 10, 02, -01, -99, 99) "
            "with (tuple_buffer_size = 1);", GMC_MODEL_STREAM, GMERR_FEATURE_NOT_SUPPORTED},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"create stream sink s1 as select * from v3 into tsdb (ts1) with (batch_window_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 4. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v3;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v2;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;
    RdFinishTest();
}

// @TestcaseName 创建视图时通过NOT IN过滤整形数据，NOT IN后是使用括号修饰的一组浮点数 报错，数据类型不匹配
TEST_F(t02_operator_not_in, STREAM_027_044)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT createTests[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '5' seconds strict);"},

        // 2. 创建视图时通过NOT IN过滤整形数据，NOT IN后是使用括号修饰的一组浮点数
        {
            "create stream view v1 as select * from t1 where id not in (1.2, 3.238, 5.0) with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"create stream sink s1 as select * from v3 into tsdb (ts1) with (batch_window_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
    };
    ret = RdStreamExecSql(shortStmt, createTests, sizeof(createTests));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(createTests[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 3. 清理测试数据
    RdStreamExecDescT dropTests[] = {
        {"drop stream sink s1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v3;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v2;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, dropTests, sizeof(dropTests));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;
    RdFinishTest();
}

// @TestcaseName 创建视图时通过NOT IN过滤整形数据，NOT IN后是使用括号修饰的一组字符串 报错，数据类型不匹配
TEST_F(t02_operator_not_in, STREAM_027_045)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT createTests[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '5' seconds strict);"},

        // 2. 创建视图时通过NOT IN过滤整形数据，NOT IN后是使用括号修饰的一组字符串
        {
            "create stream view v1 as select * from t1 where time not in ('1', 'two', 'three', '4') "
            "with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"create stream sink s1 as select * from v3 into tsdb (ts1) with (batch_window_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
    };
    ret = RdStreamExecSql(shortStmt, createTests, sizeof(createTests));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(createTests[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 3. 清理测试数据
    RdStreamExecDescT dropTests[] = {
        {"drop stream sink s1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v3;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v2;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, dropTests, sizeof(dropTests));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;
    RdFinishTest();
}

// @TestcaseName 创建视图时通过NOT IN过滤整形数据，NOT IN后是使用括号修饰的一组字符串和数值 报错，数据类型不匹配
TEST_F(t02_operator_not_in, STREAM_027_046)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT createTests[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '5' seconds strict);"},

        // 2. 创建视图时通过NOT IN过滤整形数据，NOT IN后是使用括号修饰的一组字符串和数值
        {
            "create stream view v1 as select * from t1 where time not in (1, 3, 5, 'six', '666', 'ten') "
            "with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"create stream sink s1 as select * from v3 into tsdb (ts1) with (batch_window_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
    };
    ret = RdStreamExecSql(shortStmt, createTests, sizeof(createTests));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(createTests[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 3. 清理测试数据
    RdStreamExecDescT dropTests[] = {
        {"drop stream sink s1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v3;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v2;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, dropTests, sizeof(dropTests));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;
    RdFinishTest();
}

// @TestcaseName 创建视图，NOT IN前是char类型字段，NOT IN后的列表存在重复的数据 成功
TEST_F(t02_operator_not_in, STREAM_027_047)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v1 as select * from t1 where name not in ('aa', 'aa',  'bb', 'cc', 'aa') "
            "with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select * from v3 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 2. 创建视图，NOT IN前是char类型字段，NOT IN后的列表存在重复的数据，再写入数据
    const char *insertNames[] = {"bcd", "aa",  "cc",  "aa", "cc",  "aacc", "bbdd", "dd"};
    bool expectInsertSuccess[] = {true,  false, false, false, false, true,  true,   true};
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdTwentySevenDataT *data = RdTwentySevenAllocData();
    AW_MACRO_ASSERT_NOTNULL(data);
    for (size_t i = 0; i < RD_ELEMENT_COUNT(insertNames); i++) {
        RdTwentySevenSetData(data, i, 0);
        (void)strcpy(data->name, insertNames[i]);
        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    RdTwentySevenFreeData(data);

    // 3. 查询数据, 有预期结果
    for (size_t i = 0; i < RD_ELEMENT_COUNT(expectInsertSuccess); i++) {
        ret = RdTwentySevenCheckIdExist(shortStmt, "ts1", i, expectInsertSuccess[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = RdTsCheckRecordCount(shortStmt, "ts1", 4);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 4. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;
    RdFinishTest();
}

// @TestcaseName 创建视图，NOT IN前是char类型字段，NOT IN后是使用括号修饰的单个字符串 成功
TEST_F(t02_operator_not_in, STREAM_027_048)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v1 as select * from t1 where name not in ('zhangsan') with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select * from v3 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 2. 创建视图，NOT IN前是char类型字段，NOT IN后是使用括号修饰的单个字符串，再写入数据
    const char *insertNames[] = {"bcd", "aa", "zhangsan", "aa", "zhangsan", "zhanger", "zhang", "cc", "aacc", "bbdd"};
    bool expectInsertSuccess[] = {true,  true, false,      true, false,       true,     true,    true, true,   true};
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdTwentySevenDataT *data = RdTwentySevenAllocData();
    AW_MACRO_ASSERT_NOTNULL(data);
    for (size_t i = 0; i < RD_ELEMENT_COUNT(insertNames); i++) {
        RdTwentySevenSetData(data, i, 0);
        (void)strcpy(data->name, insertNames[i]);
        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    RdTwentySevenFreeData(data);

    // 3. 查询数据, 有预期结果
    for (size_t i = 0; i < RD_ELEMENT_COUNT(expectInsertSuccess); i++) {
        ret = RdTwentySevenCheckIdExist(shortStmt, "ts1", i, expectInsertSuccess[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = RdTsCheckRecordCount(shortStmt, "ts1", 8);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 4. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;
    RdFinishTest();
}

// @TestcaseName 创建视图，NOT IN前是char类型字段，NOT IN后是使用括号修饰的一组字符串 成功
TEST_F(t02_operator_not_in, STREAM_027_049)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 where name not in ('aa', 'cc', 'aac', 'cca') "
            "with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select * from v3 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 2. 创建视图，NOT IN前是char类型字段，NOT IN后是使用括号修饰的一组字符串，再写入数据
    const char *insertNames[] = {"bcd", "aa", "cc",   "aa",   "cc", "aacc", "bbdd", "aac"};
    bool expectInsertSuccess[] = {true,  false, false, false, false, true,   true,   false};
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdTwentySevenDataT *data = RdTwentySevenAllocData();
    AW_MACRO_ASSERT_NOTNULL(data);
    for (size_t i = 0; i < RD_ELEMENT_COUNT(insertNames); i++) {
        RdTwentySevenSetData(data, i, 0);
        (void)strcpy(data->name, insertNames[i]);
        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    RdTwentySevenFreeData(data);

    // 3. 查询数据, 有预期结果
    for (size_t i = 0; i < RD_ELEMENT_COUNT(expectInsertSuccess); i++) {
        ret = RdTwentySevenCheckIdExist(shortStmt, "ts1", i, expectInsertSuccess[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = RdTsCheckRecordCount(shortStmt, "ts1", 3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 4. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;
    RdFinishTest();
}

// @TestcaseName 创建视图，NOT IN前是char类型字段，NOT IN后是使用括号修饰的一组整数值 报错，数据类型不匹配
TEST_F(t02_operator_not_in, STREAM_027_050)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT createTests[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '5' seconds strict);"},

        // 2. 创建视图，NOT IN前是char类型字段，NOT IN后是使用括号修饰的一组整数值
        {
            "create stream view v1 as select * from t1 where name not in (1, 3, 5) with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"create stream sink s1 as select * from v3 into tsdb (ts1) with (batch_window_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
    };
    ret = RdStreamExecSql(shortStmt, createTests, sizeof(createTests));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(createTests[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 3. 清理测试数据
    RdStreamExecDescT dropTests[] = {
        {"drop stream sink s1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v3;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v2;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, dropTests, sizeof(dropTests));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;
    RdFinishTest();
}

// @TestcaseName 创建视图，NOT IN前是char类型字段，NOT IN后是使用括号修饰的一组浮点数 报错，数据类型不匹配
TEST_F(t02_operator_not_in, STREAM_027_051)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT createTests[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '5' seconds strict);"},

        // 2. 创建视图，NOT IN前是char类型字段，NOT IN后是使用括号修饰的一组浮点数
        {
            "create stream view v1 as select * from t1 where id not in (1.3, 3.0, 5.13834) "
            "with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"create stream sink s1 as select * from v3 into tsdb (ts1) with (batch_window_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
    };
    ret = RdStreamExecSql(shortStmt, createTests, sizeof(createTests));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(createTests[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 3. 清理测试数据
    RdStreamExecDescT dropTests[] = {
        {"drop stream sink s1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v3;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v2;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, dropTests, sizeof(dropTests));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;
    RdFinishTest();
}

// @TestcaseName 创建视图，NOT IN前是char类型字段，NOT IN后是使用括号修饰的一组字符串和数值 报错，数据类型不匹配
TEST_F(t02_operator_not_in, STREAM_027_052)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT createTests[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '5' seconds strict);"},

        // 2. 创建视图，NOT IN前是char类型字段，NOT IN后是使用括号修饰的一组字符串和数值
        {
            "create stream view v1 as select * from t1 where id not in ('aaa', 333, 5.0, 0) "
            "with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"create stream sink s1 as select * from v3 into tsdb (ts1) with (batch_window_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
    };
    ret = RdStreamExecSql(shortStmt, createTests, sizeof(createTests));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(createTests[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 3. 清理测试数据
    RdStreamExecDescT dropTests[] = {
        {"drop stream sink s1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v3;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v2;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, dropTests, sizeof(dropTests));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;
    RdFinishTest();
}

// @TestcaseName 创建视图，NOT IN前是text类型字段，NOT IN后的列表存在重复的数据 成功
TEST_F(t02_operator_not_in, STREAM_027_053)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v1 as select * from t1 where content not in ('aa', 'cc', 'aa', 'aa', 'aac', 'aaa', 'aa') "
            "with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select * from v3 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 2. 创建视图，NOT IN前是text类型字段，NOT IN后的列表存在重复的数据，再写入数据
    const char *insertContent[] = {"bcd", "aa",  "cc",  "aa",  "cc",  "aacc", "bbdd"};
    bool expectInsertSuccess[] =   {true,  false, false, false, false, true,   true};
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdTwentySevenDataT *data = RdTwentySevenAllocData();
    AW_MACRO_ASSERT_NOTNULL(data);
    for (size_t i = 0; i < RD_ELEMENT_COUNT(insertContent); i++) {
        RdTwentySevenSetData(data, i, 0);
        (void)strcpy(data->content, insertContent[i]);
        data->contentSize = strlen(insertContent[i]) + 1;
        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    RdTwentySevenFreeData(data);

    // 3. 查询数据, 有预期结果
    for (size_t i = 0; i < RD_ELEMENT_COUNT(expectInsertSuccess); i++) {
        ret = RdTwentySevenCheckIdExist(shortStmt, "ts1", i, expectInsertSuccess[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = RdTsCheckRecordCount(shortStmt, "ts1", 3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 4. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;
    RdFinishTest();
}

// @TestcaseName 创建视图，NOT IN前是text类型字段，NOT IN后是使用括号修饰的单个字符串 成功
TEST_F(t02_operator_not_in, STREAM_027_054)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v1 as select * from t1 where content not in ('aa') with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select * from v3 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 2. 创建视图，NOT IN前是text类型字段，NOT IN后是使用括号修饰的单个字符串，再写入数据
    const char *insertContent[] = {"bcd", "aa",  "cc", "aa", "cc",  "aacc", "bbdd"};
    bool expectInsertSuccess[] =   {true,  false, true, false, true,  true,  true};
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdTwentySevenDataT *data = RdTwentySevenAllocData();
    AW_MACRO_ASSERT_NOTNULL(data);
    for (size_t i = 0; i < RD_ELEMENT_COUNT(insertContent); i++) {
        RdTwentySevenSetData(data, i, 0);
        (void)strcpy(data->content, insertContent[i]);
        data->contentSize = strlen(insertContent[i]) + 1;
        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    RdTwentySevenFreeData(data);

    // 3. 查询数据, 有预期结果
    for (size_t i = 0; i < RD_ELEMENT_COUNT(expectInsertSuccess); i++) {
        ret = RdTwentySevenCheckIdExist(shortStmt, "ts1", i, expectInsertSuccess[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = RdTsCheckRecordCount(shortStmt, "ts1", 5);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 4. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;
    RdFinishTest();
}

// @TestcaseName 创建视图，NOT IN前是text类型字段，NOT IN后是使用括号修饰的一组字符串 成功
TEST_F(t02_operator_not_in, STREAM_027_055)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '5' seconds strict);"},
        {"create stream view v1 as select * from t1 where content not in ('ee', 'aa', 'aac', 'bb', 'bbb', 'dd') "
            "with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select * from v3 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 2. 创建视图，NOT IN前是text类型字段，NOT IN后是使用括号修饰的一组字符串，再写入数据
    const char *insertContent[] = {"bcd", "aa", "cc",  "aa",  "cc", "aacc", "bbdd", "aac", "ee"};
    bool expectInsertSuccess[] =   {true,  false, true, false, true, true,   true,   false,  false};
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdTwentySevenDataT *data = RdTwentySevenAllocData();
    AW_MACRO_ASSERT_NOTNULL(data);
    for (size_t i = 0; i < RD_ELEMENT_COUNT(insertContent); i++) {
        RdTwentySevenSetData(data, i, 0);
        (void)strcpy(data->content, insertContent[i]);
        data->contentSize = strlen(insertContent[i]) + 1;
        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    RdTwentySevenFreeData(data);

    // 3. 查询数据, 有预期结果
    for (size_t i = 0; i < RD_ELEMENT_COUNT(expectInsertSuccess); i++) {
        ret = RdTwentySevenCheckIdExist(shortStmt, "ts1", i, expectInsertSuccess[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = RdTsCheckRecordCount(shortStmt, "ts1", 5);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 4. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;
    RdFinishTest();
}

// @TestcaseName 创建视图，NOT IN前是text类型字段，NOT IN后是使用括号修饰的一组整数值 报错，数据类型不匹配
TEST_F(t02_operator_not_in, STREAM_027_056)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT createTests[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '5' seconds strict);"},

        // 2. 创建视图，NOT IN前是text类型字段，NOT IN后是使用括号修饰的一组整数值
        {
            "create stream view v1 as select * from t1 where content not in (1, 3, 5) with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"create stream sink s1 as select * from v3 into tsdb (ts1) with (batch_window_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
    };
    ret = RdStreamExecSql(shortStmt, createTests, sizeof(createTests));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(createTests[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 3. 清理测试数据
    RdStreamExecDescT dropTests[] = {
        {"drop stream sink s1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v3;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v2;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, dropTests, sizeof(dropTests));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;
    RdFinishTest();
}

// @TestcaseName 创建视图，NOT IN前是text类型字段，NOT IN后是使用括号修饰的一组浮点数 报错，数据类型不匹配
TEST_F(t02_operator_not_in, STREAM_027_057)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT createTests[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '5' seconds strict);"},

        // 2. 创建视图，NOT IN前是text类型字段，NOT IN后是使用括号修饰的一组浮点数
        {
            "create stream view v1 as select * from t1 where content not in (1.3, 0.3, 5.283433) "
            "with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"create stream sink s1 as select * from v3 into tsdb (ts1) with (batch_window_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
    };
    ret = RdStreamExecSql(shortStmt, createTests, sizeof(createTests));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(createTests[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 3. 清理测试数据
    RdStreamExecDescT dropTests[] = {
        {"drop stream sink s1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v3;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v2;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, dropTests, sizeof(dropTests));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;
    RdFinishTest();
}

// @TestcaseName 创建视图，NOT IN前是text类型字段，NOT IN后是使用括号修饰的一组字符串和数值 报错，数据类型不匹配
TEST_F(t02_operator_not_in, STREAM_027_058)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT createTests[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '5' seconds strict);"},

        // 2. 创建视图，NOT IN前是text类型字段，NOT IN后是使用括号修饰的一组字符串和数值
        {
            "create stream view v1 as select * from t1 where content not in ('a', 1.3, 3, '5') "
            "with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"create stream sink s1 as select * from v3 into tsdb (ts1) with (batch_window_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
    };
    ret = RdStreamExecSql(shortStmt, createTests, sizeof(createTests));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(createTests[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 3. 清理测试数据
    RdStreamExecDescT dropTests[] = {
        {"drop stream sink s1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v3;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v2;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, dropTests, sizeof(dropTests));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;
    RdFinishTest();
}

// @TestcaseName 创建视图时通过NOT IN过滤整形数据，NOT IN后的列表元素数量较多 成功
TEST_F(t02_operator_not_in, STREAM_027_059)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare1[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '5' seconds strict);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare1, sizeof(prepare1));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare1[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    static const uint32_t sqlSize = 1024;
    static const uint32_t elementNum = 64;
    char viewWithIn[sqlSize] = {0};
    ret = strcat_s(viewWithIn, sqlSize, "create stream view v1 as select * from t1 where id not in (");
    AW_MACRO_ASSERT_EQ_INT(EOK, ret);
    for (int32_t i = 0; i < elementNum; i++) {
        uint32_t offset = strlen(viewWithIn);
        uint32_t maxLen = sqlSize - strlen(viewWithIn);
        if (i < elementNum - 1) {
            ret = snprintf_s(viewWithIn + offset, maxLen, maxLen, "%d, ", i);
            ASSERT_GT(ret, 0);
        } else {
            ret = snprintf_s(viewWithIn + offset, maxLen, maxLen, "%d", i);
            ASSERT_GT(ret, 0);
        }
    }
    ret = strcat_s(viewWithIn, sqlSize, ") with (tuple_buffer_size = 1);");
    AW_MACRO_ASSERT_EQ_INT(EOK, ret);
    RD_INFO("%s [len = %u]\n", viewWithIn, strlen(viewWithIn));
    ret = GmcExecDirect(shortStmt, viewWithIn, strlen(viewWithIn));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    RdStreamExecDescT prepare2[] = {
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select * from v3 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare2, sizeof(prepare2));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 2. 创建视图时通过NOT IN过滤整形数据，NOT IN后的列表元素数量较多，再写入数据
    int64_t insertIds[] = {3, 6, 3, 9, 12, 3, 12, elementNum + 1, elementNum + 2, 12, elementNum + 999, elementNum + 2};
    for (size_t i = 0; i < RD_ELEMENT_COUNT(insertIds); i++) {
        ret = RdTwentySevenStructWrite(shortStmt, vertexLabel, insertIds[i], 1, 0);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 3. 查询数据, 有预期结果
    int64_t qryIds[] = {3, 6, 9, 12, elementNum + 1, elementNum + 2, elementNum + 999};
    int64_t qryCnt[] = {0, 0, 0, 0,  1,              2,              1};
    for (size_t i = 0; i < RD_ELEMENT_COUNT(qryIds); i++) {
        if (qryIds[i] > 0) {
            ret = RdTwentySevenQueryTsTableById(shortStmt, "ts1", qryIds[i], qryCnt[i], 0);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = RdTwentySevenCheckIdExist(shortStmt, "ts1", i, false);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }
    ret = RdTsCheckRecordCount(shortStmt, "ts1", 4);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 4. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;
    RdFinishTest();
}

// @TestcaseName 创建视图时通过NOT IN过滤字符串数据，NOT IN后的列表元素数量较多 成功
TEST_F(t02_operator_not_in, STREAM_027_060)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare1[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '5' seconds strict);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare1, sizeof(prepare1));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare1[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    static const uint32_t sqlSize = 2048;
    static const uint32_t elementNum = 64;
    char viewWithIn[sqlSize] = {0};
    ret = strcat_s(viewWithIn, sqlSize, "create stream view v1 as select * from t1 where name not in (");
    AW_MACRO_ASSERT_EQ_INT(EOK, ret);
    for (int32_t i = 0; i < elementNum; i++) {
        uint32_t offset = strlen(viewWithIn);
        uint32_t maxLen = sqlSize - strlen(viewWithIn);
        if (i < elementNum - 1) {
            ret = snprintf_s(viewWithIn + offset, maxLen, maxLen, "'abc%d', ", i);
            ASSERT_GT(ret, 0);
        } else {
            ret = snprintf_s(viewWithIn + offset, maxLen, maxLen, "'abc%d'", i);
            ASSERT_GT(ret, 0);
        }
    }
    ret = strcat_s(viewWithIn, sqlSize, ") with (tuple_buffer_size = 1);");
    AW_MACRO_ASSERT_EQ_INT(EOK, ret);
    RD_INFO("%s [len = %u]\n", viewWithIn, strlen(viewWithIn));
    ret = GmcExecDirect(shortStmt, viewWithIn, strlen(viewWithIn));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    RdStreamExecDescT prepare2[] = {
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select * from v3 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare2, sizeof(prepare2));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 2. 创建视图时通过NOT IN过滤字符串数据，NOT IN后的列表元素数量较多，再写入数据
    const char *insertNames[] = {"bcd", "aa", "cc", "aa", "cc", "aacc", "bbdd", "abc1", "abc2", "abc63", "ccc"};
    bool expectInsertSuccess[] = {true,  true, true, true, true, true,   true,   false,   false, false,    true};
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdTwentySevenDataT *data = RdTwentySevenAllocData();
    AW_MACRO_ASSERT_NOTNULL(data);
    for (size_t i = 0; i < RD_ELEMENT_COUNT(insertNames); i++) {
        RdTwentySevenSetData(data, i, 0);
        (void)strcpy(data->name, insertNames[i]);
        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    RdTwentySevenFreeData(data);

    // 3. 查询数据, 有预期结果
    for (size_t i = 0; i < RD_ELEMENT_COUNT(insertNames); i++) {
        ret = RdTwentySevenCheckIdExist(shortStmt, "ts1", i, expectInsertSuccess[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = RdTsCheckRecordCount(shortStmt, "ts1", 8);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 4. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;
    RdFinishTest();
}
