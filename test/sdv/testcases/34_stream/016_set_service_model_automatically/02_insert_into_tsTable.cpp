/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: GmcPrepareStmtByLabelName接口支持时序表插入数据设置GMC_OPERATION_SQL_INSERT 测试
 * Author: tianyihui
 * Create: 2024-10-31
 */
#include "gtest/gtest.h"
#include "t_rd_ts.h"
#include "securec.h"
#include "gmc_sql.h"
#include "rd_feature_stream.h"
#include "stream_table_struct.h"


static GmcConnOptionsT *connOptions = NULL;
static GmcConnT *conn = NULL;
static GmcStmtT *stmt = NULL;
Status ret = 0;
char g_commonTable[] = "testdb";
char g_commonTable_1[] = "testdb_1";
char g_tempFileDir[500] = {0};

class NotSetServiceMode : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        InitTsCiCfg();
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcConnect(&conn, &stmt, 0);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        GmcConnOptionsDestroy(connOptions);
        GmcFreeStmt(stmt);
        GmcDisconnect(conn);
        testEnvClean();
        RecoverTsCiCfg();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void NotSetServiceMode::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        printf("get env PWD fail.\n");
    }
    AW_MACRO_ASSERT_NOTNULL(pwdDir);
    (void)sprintf(g_tempFileDir, "%s/tempfile", pwdDir);
}

void NotSetServiceMode::TearDown()
{
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
}

int DropCmTable(char *tableName)
{
    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    RETURN_IFERR(ret);
}

int CreateCmTable(char *tableName)
{
    (void)DropCmTable(tableName);
    // 执行DropCmTable时若果没有该表的时候会触发一次1004000错误
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_DATA_EXCEPTION);
    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, num1 integer, num2 integer, num3 integer) with (time_col = 'time', "
        "interval= '1 hour');",
        tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    RETURN_IFERR(ret);
}


// 建时序表类型为定长字符串，插入绑定类型为定长字符串，实际数据为定长字符串  预期：成功
TEST_F(NotSetServiceMode, STREAM_016_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "create table %s(id integer, time integer, str1 char(64)) with (time_col = 'time', "
        "interval= '1 hour', compression='fast');",
        g_commonTable);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50)) with (time_col = 'id', interval= '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer, name char(50));"},
        {"create stream view view1 as select * from stream1 with (tuple_buffer_size = 1200);"},
        {"create stream sink sink1 as select * from view1 into tsdb (ts1) with (batch_window_size = 1000, timeout = 5);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 准备数据
    int64_t count = 100;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    char str2[6400] = {0};
    char* str1 = str2;

    for (int i = 0; i < count; i++) {
        char str[64] = "";
        id[i] = i;
        time[i] = 1704038400 + i;
        sprintf(str, "abc%c", i);
        memcpy(str2 + i * 64, str, 64);
    }

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, str1, 64, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, g_commonTable, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 结构化写入数据
    uint32_t rowNum = 1000;
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, 1, 0);

    // 查询ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 1000;
    uint32_t expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable(stmt, selectTsName, expectRowsCount, expectColsCount);

    rowNum = 999;
    RdStructWriteStreamTable(stmt, vertexLabel, rowNum, 1, 0);

    // 插完数据立马查询ts表中数据
    selectTsName = (char *)"ts1";
    expectRowsCount = 1000;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable(stmt, selectTsName, expectRowsCount, expectColsCount);

    // 等待5s后再查询ts表中数据
    sleep(5);
    selectTsName = (char *)"ts1";
    expectRowsCount = 1999;
    expectColsCount = 2;
    RdCheckDataInTSTableOfStreamTable(stmt, selectTsName, expectRowsCount, expectColsCount);

    AW_MACRO_ASSERT_EQ_INT(DropCmTable(g_commonTable), GMERR_OK);

     // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
}

// 建时序表类型为定长字符串，插入绑定类型为定长字符串，实际数据为整型  预期：成功
TEST_F(NotSetServiceMode, STREAM_016_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "create table %s(id integer, time integer, str1 char(64)) with (time_col = 'time', "
        "interval= '1 hour', compression='fast');",
        g_commonTable);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int64_t count = 100;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t str1[count] = {0};

    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704038400 + i;
        str1[i] = 100000 + i;
    }

    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, str1, 64, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, g_commonTable, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_ASSERT_EQ_INT(DropCmTable(g_commonTable), GMERR_OK);
}

// 建时序表类型为定长字符串，插入绑定类型为定长字符串，实际数据为double  预期：成功
TEST_F(NotSetServiceMode, STREAM_016_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "create table %s(id integer, time integer, str1 char(64)) with (time_col = 'time', "
        "interval= '1 hour', compression='fast');",
        g_commonTable);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int64_t count = 100;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    double str1[count] = {0};

    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704038400 + i;
        str1[i] = 100000.25 + i * 1.25;
    }

    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, str1, 64, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, g_commonTable, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_ASSERT_EQ_INT(DropCmTable(g_commonTable), GMERR_OK);
}

// 建时序表类型为定长字符串，插入绑定类型为整型，实际数据为定长字符串  预期：失败
TEST_F(NotSetServiceMode, STREAM_016_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "create table %s(id integer, time integer, str1 char(64)) with (time_col = 'time', "
        "interval= '1 hour', compression='fast');",
        g_commonTable);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int64_t count = 100;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    char str2[6400] = {0};
    char* str1 = str2;

    for (int i = 0; i < count; i++) {
        char str[64] = "";
        id[i] = i;
        time[i] = 1704038400 + i;
        sprintf(str, "abc%c", i);
        memcpy(str2 + i * 64, str, 64);
    }

    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, str1, 64, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, g_commonTable, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    AW_MACRO_ASSERT_EQ_INT(DropCmTable(g_commonTable), GMERR_OK);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_INVALID_PARAMETER_VALUE);
}

// 建时序表类型为定长字符串，插入绑定类型为整型，实际数据为整型  预期：失败 
TEST_F(NotSetServiceMode, STREAM_016_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "create table %s(id integer, time integer, str1 char(64)) with (time_col = 'time', "
        "interval= '1 hour', compression='fast');",
        g_commonTable);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int64_t count = 100;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t str1[count] = {0};

    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704038400 + i;
        str1[i] = 100000 + i;
    }

    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, str1, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, g_commonTable, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    AW_MACRO_ASSERT_EQ_INT(DropCmTable(g_commonTable), GMERR_OK);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_INVALID_PARAMETER_VALUE);
}

// 建时序表类型为整型，插入绑定类型为整型，实际数据为整型  预期：成功
TEST_F(NotSetServiceMode, STREAM_016_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "create table %s(id integer, time integer, num integer) with (time_col = 'time', "
        "interval= '1 hour', compression='fast');",
        g_commonTable);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int64_t count = 100;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t num[count] = {0};

    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704038400 + i;
        num[i] = 100000 + i;
    }

    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, num, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, g_commonTable, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_ASSERT_EQ_INT(DropCmTable(g_commonTable), GMERR_OK);
}

// 建时序表类型为整型，插入绑定类型为整型，实际数据为定长字符串  预期：成功
TEST_F(NotSetServiceMode, STREAM_016_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "create table %s(id integer, time integer, num integer) with (time_col = 'time', "
        "interval= '1 hour', compression='fast');",
        g_commonTable);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int64_t count = 100;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    char str2[6400] = {0};
    char* str1 = str2;

    for (int i = 0; i < count; i++) {
        char str[64] = "";
        id[i] = i;
        time[i] = 1704038400 + i;
        sprintf(str, "abc%c", i);
        memcpy(str2 + i * 64, str, 64);
    }

    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, str1, 64, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, g_commonTable, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_ASSERT_EQ_INT(DropCmTable(g_commonTable), GMERR_OK);
}

// 建时序表类型为整型，插入绑定类型为定长字符串，实际数据为定整型  预期：失败
TEST_F(NotSetServiceMode, STREAM_016_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "create table %s(id integer, time integer, num integer) with (time_col = 'time', "
        "interval= '1 hour', compression='fast');",
        g_commonTable);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int64_t count = 100;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t num[count] = {0};

    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704038400 + i;
        num[i] = 100000 + i;
    }

    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, num, 64, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, g_commonTable, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_VALUE, ret);

    AW_MACRO_ASSERT_EQ_INT(DropCmTable(g_commonTable), GMERR_OK);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_INVALID_VALUE);
}

// 建时序表类型为整型，插入绑定类型为定长字符串，实际数据为定长字符串  预期：失败
TEST_F(NotSetServiceMode, STREAM_016_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "create table %s(id integer, time integer, num integer) with (time_col = 'time', "
        "interval= '1 hour', compression='fast');",
        g_commonTable);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int64_t count = 100;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    char str2[6400] = {0};
    char* str1 = str2;

    for (int i = 0; i < count; i++) {
        char str[64] = "";
        id[i] = i;
        time[i] = 1704038400 + i;
        sprintf(str, "abc%c", i);
        memcpy(str2 + i * 64, str, 64);
    }

    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, str1, 64, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, g_commonTable, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_VALUE, ret);

    AW_MACRO_ASSERT_EQ_INT(DropCmTable(g_commonTable), GMERR_OK);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_INVALID_VALUE);
}

// 插入int8范围内数据  预期：成功
TEST_F(NotSetServiceMode, STREAM_016_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "create table %s(id integer, time integer, num integer) with (time_col = 'time', "
        "interval= '1 hour', compression='fast');",
        g_commonTable);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int64_t count = 100;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int8_t num[count] = {0};

    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704038400 + i;
        num[i] = i;
    }

    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT8, num, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, g_commonTable, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_ASSERT_EQ_INT(DropCmTable(g_commonTable), GMERR_OK);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DATA_EXCEPTION, GMERR_INVALID_VALUE);
}

// 插入int16范围内数据  预期：成功
TEST_F(NotSetServiceMode, STREAM_016_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "create table %s(id integer, time integer, num integer) with (time_col = 'time', "
        "interval= '1 hour', compression='fast');",
        g_commonTable);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int64_t count = 100;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int16_t num[count] = {0};

    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704038400 + i;
        num[i] = 65035;
    }

    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT16, num, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, g_commonTable, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_ASSERT_EQ_INT(DropCmTable(g_commonTable), GMERR_OK);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DATA_EXCEPTION, GMERR_INVALID_VALUE);
}

// 插入int32范围内数据  预期：成功
TEST_F(NotSetServiceMode, STREAM_016_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "create table %s(id integer, time integer, num integer) with (time_col = 'time', "
        "interval= '1 hour', compression='fast');",
        g_commonTable);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int64_t count = 100;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int32_t num[count] = {0};

    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704038400 + i;
        num[i] = 1000000 + i;
    }

    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT32, num, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, g_commonTable, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_ASSERT_EQ_INT(DropCmTable(g_commonTable), GMERR_OK);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DATA_EXCEPTION, GMERR_INVALID_VALUE);
}

// 插入int8范围内数据，实际值超出范围  预期：成功
TEST_F(NotSetServiceMode, STREAM_016_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "create table %s(id integer, time integer, num integer) with (time_col = 'time', "
        "interval= '1 hour', compression='fast');",
        g_commonTable);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int64_t count = 100;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int8_t num[count] = {0};

    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704038400 + i;
        num[i] = 250 + i;
    }

    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT8, num, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, g_commonTable, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_ASSERT_EQ_INT(DropCmTable(g_commonTable), GMERR_OK);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DATA_EXCEPTION, GMERR_INVALID_VALUE);
}

// 插入int16范围内数据，实际值超出范围  预期：成功
TEST_F(NotSetServiceMode, STREAM_016_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "create table %s(id integer, time integer, num integer) with (time_col = 'time', "
        "interval= '1 hour', compression='fast');",
        g_commonTable);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int64_t count = 100;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int16_t num[count] = {0};

    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704038400 + i;
        num[i] = 65500 + i;
    }

    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT16, num, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, g_commonTable, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_ASSERT_EQ_INT(DropCmTable(g_commonTable), GMERR_OK);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DATA_EXCEPTION, GMERR_INVALID_VALUE);
}

// 插入int32范围内数据，实际值超出范围  预期：成功
TEST_F(NotSetServiceMode, STREAM_016_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "create table %s(id integer, time integer, num integer) with (time_col = 'time', "
        "interval= '1 hour', compression='fast');",
        g_commonTable);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int64_t count = 100;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int32_t num[count] = {0};

    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704038400 + i;
        // int32_t范围是42949672954
        num[i] = 42949672930 + i;
    }

    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT32, num, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, g_commonTable, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_ASSERT_EQ_INT(DropCmTable(g_commonTable), GMERR_OK);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_INVALID_VALUE);
}

// 单列数据插入  预期：成功
TEST_F(NotSetServiceMode, STREAM_016_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table testdb_1(time integer) with (time_col = 'time', interval = '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int64_t count = 100;
    int64_t time[count] = {0};
    for (int i = 0; i < count; i++) {
        time[i] = 1704038400 + i;
    }

    ret = BlukInsert(stmt, g_commonTable_1, count, 1, time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 建时序表时多列数据，插入时指定单时间列数据  预期：失败
TEST_F(NotSetServiceMode, STREAM_016_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_MACRO_ASSERT_EQ_INT(CreateCmTable(g_commonTable), GMERR_OK);

    int64_t count = 100;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t num1[count] = {0};
    int64_t num2[count] = {0};
    int64_t num3[count] = {0};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704038400 + i;
        num1[i] = 1000000 + i;
        num2[i] = 1000 + i;
        num3[i] = i;
    }

    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, 0, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, g_commonTable, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_ASSERT_EQ_INT(DropCmTable(g_commonTable), GMERR_OK);
}

// 建时序表时多列数据，插入时指定单非时间列数据  预期：失败
TEST_F(NotSetServiceMode, STREAM_016_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_MACRO_ASSERT_EQ_INT(CreateCmTable(g_commonTable), GMERR_OK);

    int64_t count = 100;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t num1[count] = {0};
    int64_t num2[count] = {0};
    int64_t num3[count] = {0};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704038400 + i;
        num1[i] = 1000000 + i;
        num2[i] = 1000 + i;
        num3[i] = i;
    }

    ret = BlukInsert(stmt, g_commonTable, count, 1, id);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    AW_MACRO_ASSERT_EQ_INT(DropCmTable(g_commonTable), GMERR_OK);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_INVALID_PARAMETER_VALUE);
}

// 绑定列顺序与插入表的列顺序不一致，定长字符串和整型  预期：失败
TEST_F(NotSetServiceMode, STREAM_016_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "create table %s(id integer, time integer, str1 char(64)) with (time_col = 'time', "
        "interval= '1 hour', compression='fast');",
        g_commonTable);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int64_t count = 100;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    char str2[6400] = {0};
    char* str1 = str2;

    for (int i = 0; i < count; i++) {
        char str[64] = "";
        id[i] = i;
        time[i] = 1704038400 + i;
        sprintf(str, "abc%c", i);
        memcpy(str2 + i * 64, str, 64);
    }

    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // str与time绑定顺序交换
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_FIXED, str1, 64, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, time, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, g_commonTable, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, str1, 64, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, g_commonTable, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_ASSERT_EQ_INT(DropCmTable(g_commonTable), GMERR_OK);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_INVALID_PARAMETER_VALUE);
}
// 绑定列顺序与插入表的列顺序不一致，定长字符串和定长字符串，长度相同  预期：成功
TEST_F(NotSetServiceMode, STREAM_016_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "create table %s(id integer, time integer, str1 char(64), str char(64)) with (time_col = 'time', "
        "interval= '1 hour', compression='fast');",
        g_commonTable);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int64_t count = 100;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    char str2[6400] = {0};
    char str3[6400] = {0};
    char* str1 = str2;
    char* str = str3;

    for (int i = 0; i < count; i++) {
        char str[64] = "";
        id[i] = i;
        time[i] = 1704038400 + i;
        sprintf(str, "abc%c", i);
        memcpy(str2 + i * 64, str, 64);
        sprintf(str, "bcad%c", i);
        memcpy(str3 + i * 64, str, 64);
    }

    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // str与str1绑定顺序交换
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, str, 64, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_FIXED, str1, 64, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, g_commonTable, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_ASSERT_EQ_INT(DropCmTable(g_commonTable), GMERR_OK);
}


// 绑定列顺序与插入表的列顺序不一致，定长字符串和定长字符串，长度不同  预期：成功
TEST_F(NotSetServiceMode, STREAM_016_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "create table %s(id integer, time integer, str1 char(64), str char(32)) with (time_col = 'time', "
        "interval= '1 hour', compression='fast');",
        g_commonTable);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int64_t count = 100;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    char str2[6400] = {0};
    char str3[3200] = {0};
    char* str1 = str2;
    char* str = str3;

    for (int i = 0; i < count; i++) {
        char str[64] = "";
        id[i] = i;
        time[i] = 1704038400 + i;
        sprintf(str, "abc%c", i);
        memcpy(str2 + i * 64, str, 64);
        sprintf(str, "bcad%c", i);
        memcpy(str3 + i * 32, str, 32);
    }

    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // str与str1绑定顺序交换
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, str, 32, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_FIXED, str1, 64, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, g_commonTable, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    AW_MACRO_ASSERT_EQ_INT(DropCmTable(g_commonTable), GMERR_OK);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_INVALID_PARAMETER_VALUE);
}

// 绑定列数目大于表的列数  预期：成功
TEST_F(NotSetServiceMode, STREAM_016_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "create table %s(id integer, time integer, num1 integer, num2 integer) with (time_col = 'time', "
        "interval= '1 hour', compression='fast');",
        g_commonTable);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int64_t count = 100;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int32_t num1[count] = {0};
    int16_t num2[count] = {0};
    int16_t num3[count] = {0};

    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704038400 + i;
        num1[i] = 2000000 + i;
        num2[i] = 30000 + i;
        num3[i] = i;
    }

    // 插入列数与表列数不一致
    ret = BlukInsert(stmt, g_commonTable, count, 5, id, time, num1, num2, num3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_ASSERT_EQ_INT(DropCmTable(g_commonTable), GMERR_OK);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
}

// 绑定列数目小于表的列数  预期：失败
TEST_F(NotSetServiceMode, STREAM_016_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "create table %s(id integer, time integer, num1 integer, num2 integer) with (time_col = 'time', "
        "interval= '1 hour', compression='fast');",
        g_commonTable);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int64_t count = 100;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int32_t num1[count] = {0};
    int16_t num2[count] = {0};

    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704038400 + i;
        num1[i] = 2000000 + i;
        num2[i] = 3000 + i;
    }

    // 插入列数与表列数不一致
    ret = BlukInsert(stmt, g_commonTable, count, 3, id, time, num1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = BlukInsert(stmt, g_commonTable, count, 4, id, time, num1, num2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_ASSERT_EQ_INT(DropCmTable(g_commonTable), GMERR_OK);
}
