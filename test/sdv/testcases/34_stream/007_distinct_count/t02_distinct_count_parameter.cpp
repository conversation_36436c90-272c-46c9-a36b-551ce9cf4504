/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: DistinctCount复杂场景测试
 * Author: guopanpan
 * Create: 2024-09-29
 */
#include "gtest/gtest.h"
#include "gmc_sql.h"
#include "rd_feature_stream.h"
#include "rd_feature_ts.h"
#include "rd_stream_distinct_count.h"
#include "rd_stream_custom_column.h"

class t02_distinct_count_parameter : public testing::Test {
public:
    static GmcConnT *longConn;
    static GmcStmtT *longStmt;
    GmcConnT *shortConn;
    GmcStmtT *shortStmt;
    GmcStmtT *streamStmt = NULL;
    GmcStmtT *tsStmt = NULL;

    bool hasFinishTest = false;
    void RdFinishTest();

    static void SetUpTestCase();
    static void TearDownTestCase();
    virtual void SetUp();
    virtual void TearDown();
};

GmcConnT *t02_distinct_count_parameter::longConn = NULL;
GmcStmtT *t02_distinct_count_parameter::longStmt = NULL;

void t02_distinct_count_parameter::SetUpTestCase()
{
    int32_t ret;
    ret = RdStreamEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdGmcConnect(&longConn, &longStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void t02_distinct_count_parameter::TearDownTestCase()
{
    int32_t ret;
    ret = RdGmcDisconnect(longConn, longStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdStreamEnvClean();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void t02_distinct_count_parameter::SetUp()
{
    int32_t ret;
    hasFinishTest = false;
    ret = RdGmcConnect(&shortConn, &shortStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcAllocStmt(shortConn, &streamStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint32_t modelStream = GMC_MODEL_STREAM;
    ret = GmcSetStmtAttr(streamStmt, GMC_STMT_ATTR_MODEL_TYPE, &modelStream, sizeof(modelStream));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcAllocStmt(shortConn, &tsStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint32_t modelTs = GMC_MODEL_TS;
    ret = GmcSetStmtAttr(tsStmt, GMC_STMT_ATTR_MODEL_TYPE, &modelTs, sizeof(modelTs));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void t02_distinct_count_parameter::TearDown()
{
    int32_t ret;
    if (!hasFinishTest) {
        RdStreamExecDescT desc[] = {
            {"drop stream sink s1;"},
            {"drop stream view v1;"},
            {"drop stream table t1;"},
            {"drop table ts1;", GMC_MODEL_TS},
        };
        ret = RdStreamDropMetadata(longStmt, desc, sizeof(desc));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    GmcFreeStmt(streamStmt);
    GmcFreeStmt(tsStmt);
    ret = RdGmcDisconnect(shortConn, shortStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void t02_distinct_count_parameter::RdFinishTest()
{
    hasFinishTest = true;
}

// @TestSuite 函数入参
// @TestcaseName 创建包含去重函数的视图，投影列和去重列是相同的单个列名	成功
TEST_F(t02_distinct_count_parameter, STREAM_007_014)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");
    RdStreamExecDescT prepare[] = {
        {
            "create table ts1 (time integer, count integer) "
            "with (time_col = 'time', interval = '1 hour');", GMC_MODEL_TS
        },
        {"create stream table t1 (id integer, name char(50), time integer);"},
        {
            "create stream view v1 as select time, seq_distinct_count(time) "
            "from t1 with (tuple_buffer_size = 1);"
        },
        {"create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 写入重复数据
    int64_t repeatId = 8;
    RdStreamDcDataT data = {0};
    RdStreamDcSetData(repeatId, 0, &data);
    uint32_t repeatCnt = 2000;

    ret = GmcPrepareStmtByLabelName(streamStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < repeatCnt; i++) {
        ret = RdStreamSetVertexWithBuf(streamStmt, vertexLabel, &data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(streamStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 写入不同数据
    int64_t uniqueId = 10;
    RdStreamDcSetData(uniqueId, 0, &data);
    ret = RdStreamSetVertexWithBuf(streamStmt, vertexLabel, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(streamStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // DEMO 查询时序表数据（依赖AW）
    bool eof;
    const char *qrySql = "select * from ts1;";
    ret = GmcExecDirect(tsStmt, qrySql, strlen(qrySql));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckResultRows(tsStmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcFetch(tsStmt, &eof);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_BOOL(false, eof);
    RdStreamDcSetData(repeatId, 0, &data);
    ret = RdTsCheckPropertyInt64(tsStmt, 0, data.time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckPropertyInt64(tsStmt, 1, repeatCnt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    RdFinishTest();
}

// @TestcaseName 创建包含去重函数的视图，投影列和去重列是相同的多个列名	成功
TEST_F(t02_distinct_count_parameter, STREAM_007_015)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");
    RdStreamExecDescT prepare[] = {
        {
            "create table ts1 (name char(50), time integer, count integer) "
            "with (time_col = 'time', interval = '1 hour');", GMC_MODEL_TS
        },
        {"create stream table t1 (id integer, name char(50), time integer);"},
        {
            "create stream view v1 as select name, time, seq_distinct_count(name, time) "
            "from t1 with (tuple_buffer_size = 1);"
        },
        {"create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 写入重复数据
    int64_t repeatId = 8;
    RdStreamDcDataT data = {0};
    RdStreamDcSetData(repeatId, 0, &data);
    uint32_t repeatCnt = 2000;

    ret = GmcPrepareStmtByLabelName(streamStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < repeatCnt; i++) {
        ret = RdStreamSetVertexWithBuf(streamStmt, vertexLabel, &data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(streamStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 写入不同数据
    int64_t uniqueId = 10;
    RdStreamDcSetData(uniqueId, 0, &data);
    ret = RdStreamSetVertexWithBuf(streamStmt, vertexLabel, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(streamStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 查询时序表数据
    bool eof;
    const char *qrySql = "select * from ts1;";
    ret = GmcExecDirect(tsStmt, qrySql, strlen(qrySql));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckResultRows(tsStmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(tsStmt, &eof);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_BOOL(false, eof);

    RdStreamDcSetData(repeatId, 0, &data);
    ret = RdTsCheckPropertyText(tsStmt, 0, data.name, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckPropertyInt64(tsStmt, 1, data.time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckPropertyInt64(tsStmt, 2, repeatCnt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    RdFinishTest();
}

// @TestcaseName 创建包含去重函数的视图，投影列和去重列名称相同顺序不同	成功
TEST_F(t02_distinct_count_parameter, STREAM_007_016)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");
    RdStreamExecDescT prepare[] = {
        {
            "create table ts1 (id integer, name char(50), time integer, count integer) "
            "with (time_col = 'time', interval = '1 hour');", GMC_MODEL_TS
        },
        {"create stream table t1 (id integer, name char(50), time integer);"},
        {
            "create stream view v1 as select id, name, time, seq_distinct_count(name, id, time) "
            "from t1 with (tuple_buffer_size = 1);"
        },
        {"create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 写入重复数据
    int64_t repeatId = 8;
    RdStreamDcDataT data = {0};
    RdStreamDcSetData(repeatId, 0, &data);
    uint32_t repeatCnt = 2000;

    ret = GmcPrepareStmtByLabelName(streamStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < repeatCnt; i++) {
        ret = RdStreamSetVertexWithBuf(streamStmt, vertexLabel, &data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(streamStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 写入不同数据
    int64_t uniqueId = 10;
    RdStreamDcSetData(uniqueId, 0, &data);
    ret = RdStreamSetVertexWithBuf(streamStmt, vertexLabel, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(streamStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 查询时序表数据
    bool eof;
    const char *qrySql = "select * from ts1;";
    ret = GmcExecDirect(tsStmt, qrySql, strlen(qrySql));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckResultRows(tsStmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(tsStmt, &eof);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_BOOL(false, eof);

    RdStreamDcSetData(repeatId, 0, &data);
    ret = RdTsCheckPropertyInt64(tsStmt, 0, data.id);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckPropertyText(tsStmt, 1, data.name, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckPropertyInt64(tsStmt, 2, data.time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckPropertyInt64(tsStmt, 3, repeatCnt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    RdFinishTest();
}

// @TestcaseName 创建包含去重函数的视图，投影列是去重列的子集，写入全部列均相同的数据	成功，重复数据仅保留一条
TEST_F(t02_distinct_count_parameter, STREAM_007_017)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");
    RdStreamExecDescT prepare[] = {
        {
            "create table ts1 (name char(50), time integer, count integer) "
            "with (time_col = 'time', interval = '1 hour');", GMC_MODEL_TS
        },
        {"create stream table t1 (id integer, name char(50), time integer);"},
        {
            "create stream view v1 as select name, time, seq_distinct_count(name, id, time) "
            "from t1 with (tuple_buffer_size = 1);"
        },
        {"create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 写入重复数据
    int64_t repeatId = 8;
    RdStreamDcDataT data = {0};
    RdStreamDcSetData(repeatId, 0, &data);
    uint32_t repeatCnt = 2000;

    ret = GmcPrepareStmtByLabelName(streamStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < repeatCnt; i++) {
        ret = RdStreamSetVertexWithBuf(streamStmt, vertexLabel, &data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(streamStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 写入不同数据
    int64_t uniqueId = 10;
    RdStreamDcSetData(uniqueId, 0, &data);
    ret = RdStreamSetVertexWithBuf(streamStmt, vertexLabel, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(streamStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 查询时序表数据
    bool eof;
    const char *qrySql = "select * from ts1;";
    ret = GmcExecDirect(tsStmt, qrySql, strlen(qrySql));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckResultRows(tsStmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(tsStmt, &eof);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_BOOL(false, eof);

    RdStreamDcSetData(repeatId, 0, &data);
    ret = RdTsCheckPropertyText(tsStmt, 0, data.name, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckPropertyInt64(tsStmt, 1, data.time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckPropertyInt64(tsStmt, 2, repeatCnt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    RdFinishTest();
}

// @TestcaseName 创建包含去重函数的视图，投影列是去重列的子集，写入仅投影列相同的数据	成功，数据均写入DB
TEST_F(t02_distinct_count_parameter, STREAM_007_018)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");
    RdStreamExecDescT prepare[] = {
        {
            "create table ts1 (id integer, time integer, count integer) "
            "with (time_col = 'time', interval = '1 hour');", GMC_MODEL_TS
        },
        {"create stream table t1 (id integer, name char(50), time integer);"},
        {
            "create stream view v1 as select id, time, seq_distinct_count(id, name, time) "
            "from t1 with (tuple_buffer_size = 1);"
        },
        {"create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 写入重复数据
    int64_t repeatId = 8;
    RdStreamDcDataT data = {0};
    RdStreamDcSetData(repeatId, 0, &data);
    uint32_t repeatCnt = 2000;

    ret = GmcPrepareStmtByLabelName(streamStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < repeatCnt; i++) {
        (void)snprintf_s(data.name, RD_STREAM_DC_NAME_SIZE, RD_STREAM_DC_NAME_SIZE, "info_%0ld", i);
        ret = RdStreamSetVertexWithBuf(streamStmt, vertexLabel, &data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(streamStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 写入不同数据
    int64_t uniqueId = 10;
    RdStreamDcSetData(uniqueId, 0, &data);
    ret = RdStreamSetVertexWithBuf(streamStmt, vertexLabel, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(streamStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 查询时序表数据
    bool eof;
    const char *qrySql = "select * from ts1;";
    ret = GmcExecDirect(tsStmt, qrySql, strlen(qrySql));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckResultRows(tsStmt, repeatCnt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamDcSetData(repeatId, 0, &data);
    for (uint32_t i = 0; i < repeatCnt; i++) {
        ret = GmcFetch(tsStmt, &eof);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_BOOL(false, eof);
        ret = RdTsCheckPropertyInt64(tsStmt, 0, data.id);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdTsCheckPropertyInt64(tsStmt, 1, data.time);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdTsCheckPropertyInt64(tsStmt, 2, 1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    RdFinishTest();
}

// @TestcaseName 创建包含去重函数的视图，投影列是去重列的超集	报错，语义错误
TEST_F(t02_distinct_count_parameter, STREAM_007_019)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");
    RdStreamExecDescT prepare[] = {
        {
            "create table ts1 (id integer, name char(50), time integer, count integer) "
            "with (time_col = 'time', interval = '1 hour');", GMC_MODEL_TS
        },
        {"create stream table t1 (id integer, name char(50), time integer);"},
        // HISTORY 2024-10-30 允许投影列是去重列的超集，该用例将执行成功
        {
            "create stream view v1 as select id, name, time, seq_distinct_count(id, time) "
            "from t1 with (tuple_buffer_size = 1);"
        },
        {"create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 写入重复数据
    int64_t repeatId = 8;
    RdStreamDcDataT data = {0};
    RdStreamDcSetData(repeatId, 0, &data);
    uint32_t repeatCnt = 2000;

    ret = GmcPrepareStmtByLabelName(streamStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < repeatCnt; i++) {
        ret = RdStreamSetVertexWithBuf(streamStmt, vertexLabel, &data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(streamStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 写入不同数据
    int64_t uniqueId = 10;
    RdStreamDcSetData(uniqueId, 0, &data);
    ret = RdStreamSetVertexWithBuf(streamStmt, vertexLabel, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(streamStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 查询时序表数据
    ret = RdTsCheckRecordCount(tsStmt, "ts1", 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    RdFinishTest();
}

// @TestcaseName 创建包含去重函数的视图，投影列和去重列的部分列不同	报错，语义错误
TEST_F(t02_distinct_count_parameter, STREAM_007_020)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");
    RdStreamExecDescT prepare[] = {
        {
            "create table ts1 (id integer, time integer, count integer) "
            "with (time_col = 'time', interval = '1 hour');", GMC_MODEL_TS
        },
        {"create stream table t1 (id integer, name char(50), time integer);"},
        // HISTORY 2024-10-30 允许投影列是去重列的超集，该用例将执行成功
        {
            "create stream view v1 as select id, time, seq_distinct_count(id, name) "
            "from t1 with (tuple_buffer_size = 1);"
        },
        {"create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 写入重复数据
    int64_t repeatId = 8;
    RdStreamDcDataT data = {0};
    RdStreamDcSetData(repeatId, 0, &data);
    uint32_t repeatCnt = 2000;

    ret = GmcPrepareStmtByLabelName(streamStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < repeatCnt; i++) {
        ret = RdStreamSetVertexWithBuf(streamStmt, vertexLabel, &data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(streamStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 写入不同数据
    int64_t uniqueId = 10;
    RdStreamDcSetData(uniqueId, 0, &data);
    ret = RdStreamSetVertexWithBuf(streamStmt, vertexLabel, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(streamStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 查询时序表数据
    ret = RdTsCheckRecordCount(tsStmt, "ts1", 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    RdFinishTest();
}

// @TestcaseName 创建包含去重函数的视图，投影部分和去重函数入参是通配符	报错，特性不支持
TEST_F(t02_distinct_count_parameter, STREAM_007_021)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");
    RdStreamExecDescT prepare[] = {
        {
            "create table ts1 (id integer, name char(50), time integer, count integer) "
            "with (time_col = 'time', interval = '1 hour');", GMC_MODEL_TS
        },
        {"create stream table t1 (id integer, name char(50), time integer);"},
        {
            "create stream view v1 as select *, seq_distinct_count(*) "
            "from t1 with (tuple_buffer_size = 1);", GMC_MODEL_STREAM, GMERR_FEATURE_NOT_SUPPORTED
        },
        {"create stream sink s1 as select * from v1 into tsdb (ts1);", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 写入重复数据
    int64_t repeatId = 8;
    RdStreamDcDataT data = {0};
    RdStreamDcSetData(repeatId, 0, &data);
    uint32_t repeatCnt = 2000;

    ret = GmcPrepareStmtByLabelName(streamStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < repeatCnt; i++) {
        ret = RdStreamSetVertexWithBuf(streamStmt, vertexLabel, &data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(streamStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, ret);
    }

    // 写入不同数据
    int64_t uniqueId = 10;
    RdStreamDcSetData(uniqueId, 0, &data);
    ret = RdStreamSetVertexWithBuf(streamStmt, vertexLabel, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(streamStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, ret);

    // 查询时序表数据
    ret = RdTsCheckRecordCount(tsStmt, "ts1", 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    RdStreamExecDescT clean[] = {
        {"drop stream table t1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    RdFinishTest();
}

// @TestcaseName 创建包含去重函数的视图，投影部分是通配符，去重函数入参是完整分组列	成功
TEST_F(t02_distinct_count_parameter, STREAM_007_022)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");
    RdStreamExecDescT prepare[] = {
        {
            "create table ts1 (id integer, name char(50), time integer, count integer) "
            "with (time_col = 'time', interval = '1 hour');", GMC_MODEL_TS
        },
        {"create stream table t1 (id integer, name char(50), time integer);"},
        {
            "create stream view v1 as select *, seq_distinct_count(id, name, time) "
            "from t1 with (tuple_buffer_size = 1);"
        },
        {"create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 写入重复数据
    int64_t repeatId = 8;
    RdStreamDcDataT data = {0};
    RdStreamDcSetData(repeatId, 0, &data);
    uint32_t repeatCnt = 2000;

    ret = GmcPrepareStmtByLabelName(streamStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < repeatCnt; i++) {
        ret = RdStreamSetVertexWithBuf(streamStmt, vertexLabel, &data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(streamStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 写入不同数据
    int64_t uniqueId = 10;
    RdStreamDcSetData(uniqueId, 0, &data);
    ret = RdStreamSetVertexWithBuf(streamStmt, vertexLabel, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(streamStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 查询时序表数据
    bool eof;
    const char *qrySql = "select * from ts1;";
    ret = GmcExecDirect(tsStmt, qrySql, strlen(qrySql));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckResultRows(tsStmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(tsStmt, &eof);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_BOOL(false, eof);

    RdStreamDcSetData(repeatId, 0, &data);
    ret = RdTsCheckPropertyInt64(tsStmt, 0, data.id);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckPropertyText(tsStmt, 1, data.name, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckPropertyInt64(tsStmt, 2, data.time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckPropertyInt64(tsStmt, 3, repeatCnt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    RdFinishTest();
}

// @TestcaseName 创建包含去重函数的视图，投影部分是完整分组列，去重函数入参是通配符	报错，特性不支持
TEST_F(t02_distinct_count_parameter, STREAM_007_023)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");
    RdStreamExecDescT prepare[] = {
        {
            "create table ts1 (id integer, name char(50), time integer, count integer) "
            "with (time_col = 'time', interval = '1 hour');", GMC_MODEL_TS
        },
        {"create stream table t1 (id integer, name char(50), time integer);"},
        {
            "create stream view v1 as select id, name, time, seq_distinct_count(*) "
            "from t1 with (tuple_buffer_size = 1);", GMC_MODEL_STREAM, GMERR_FEATURE_NOT_SUPPORTED
        },
        {"create stream sink s1 as select * from v1 into tsdb (ts1);", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 写入重复数据
    int64_t repeatId = 8;
    RdStreamDcDataT data = {0};
    RdStreamDcSetData(repeatId, 0, &data);
    uint32_t repeatCnt = 2000;

    ret = GmcPrepareStmtByLabelName(streamStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < repeatCnt; i++) {
        ret = RdStreamSetVertexWithBuf(streamStmt, vertexLabel, &data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(streamStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, ret);
    }

    // 写入不同数据
    int64_t uniqueId = 10;
    RdStreamDcSetData(uniqueId, 0, &data);
    ret = RdStreamSetVertexWithBuf(streamStmt, vertexLabel, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(streamStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, ret);

    // 查询时序表数据
    ret = RdTsCheckRecordCount(tsStmt, "ts1", 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    RdStreamExecDescT clean[] = {
        {"drop stream table t1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    RdFinishTest();
}

// @TestcaseName 创建包含去重函数的视图，投影部分是通配符，去重函数入参是部分列	报错，语义错误
TEST_F(t02_distinct_count_parameter, STREAM_007_024)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");
    RdStreamExecDescT prepare[] = {
        {
            "create table ts1 (id integer, name char(50), time integer, count integer) "
            "with (time_col = 'time', interval = '1 hour');", GMC_MODEL_TS
        },
        {"create stream table t1 (id integer, name char(50), time integer);"},
        // HISTORY 2024-10-30 允许投影列是去重列的超集，该用例将执行成功
        {
            "create stream view v1 as select *, seq_distinct_count(id, time) "
            "from t1 with (tuple_buffer_size = 1);"
        },
        {"create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 写入重复数据
    int64_t repeatId = 8;
    RdStreamDcDataT data = {0};
    RdStreamDcSetData(repeatId, 0, &data);
    uint32_t repeatCnt = 2000;

    ret = GmcPrepareStmtByLabelName(streamStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < repeatCnt; i++) {
        ret = RdStreamSetVertexWithBuf(streamStmt, vertexLabel, &data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(streamStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 写入不同数据
    int64_t uniqueId = 10;
    RdStreamDcSetData(uniqueId, 0, &data);
    ret = RdStreamSetVertexWithBuf(streamStmt, vertexLabel, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(streamStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 查询时序表数据
    ret = RdTsCheckRecordCount(tsStmt, "ts1", 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    RdFinishTest();
}

// @TestcaseName 创建包含去重函数的视图，投影部分是部分列，去重函数入参是通配符	报错，特性不支持
TEST_F(t02_distinct_count_parameter, STREAM_007_025)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");
    RdStreamExecDescT prepare[] = {
        {
            "create table ts1 (id integer, time integer, count integer) "
            "with (time_col = 'time', interval = '1 hour');", GMC_MODEL_TS
        },
        {"create stream table t1 (id integer, name char(50), time integer);"},
        {
            "create stream view v1 as select id, time, seq_distinct_count(*) "
            "from t1 with (tuple_buffer_size = 1);", GMC_MODEL_STREAM, GMERR_FEATURE_NOT_SUPPORTED
        },
        {"create stream sink s1 as select * from v1 into tsdb (ts1);", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 写入重复数据
    int64_t repeatId = 8;
    RdStreamDcDataT data = {0};
    RdStreamDcSetData(repeatId, 0, &data);
    uint32_t repeatCnt = 2000;

    ret = GmcPrepareStmtByLabelName(streamStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < repeatCnt; i++) {
        ret = RdStreamSetVertexWithBuf(streamStmt, vertexLabel, &data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(streamStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, ret);
    }

    // 写入不同数据
    int64_t uniqueId = 10;
    RdStreamDcSetData(uniqueId, 0, &data);
    ret = RdStreamSetVertexWithBuf(streamStmt, vertexLabel, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(streamStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, ret);

    // 查询时序表数据
    ret = RdTsCheckRecordCount(tsStmt, "ts1", 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    RdStreamExecDescT clean[] = {
        {"drop stream table t1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    RdFinishTest();
}

// @TestcaseName 创建包含去重函数的视图，函数入参包含列名和通配符 报错，语义错误
TEST_F(t02_distinct_count_parameter, STREAM_007_026)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");
    RdStreamExecDescT prepare[] = {
        {
            "create table ts1 (id integer, name char(50), time integer, count integer) "
            "with (time_col = 'time', interval = '1 hour');", GMC_MODEL_TS
        },
        {"create stream table t1 (id integer, name char(50), time integer);"},
        {
            "create stream view v1 as select id, name, time, seq_distinct_count(id, *) "
            "from t1 with (tuple_buffer_size = 1);", GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR
        },
        {"create stream sink s1 as select * from v1 into tsdb (ts1);", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 写入重复数据
    int64_t repeatId = 8;
    RdStreamDcDataT data = {0};
    RdStreamDcSetData(repeatId, 0, &data);
    uint32_t repeatCnt = 2000;

    ret = GmcPrepareStmtByLabelName(streamStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < repeatCnt; i++) {
        ret = RdStreamSetVertexWithBuf(streamStmt, vertexLabel, &data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(streamStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, ret);
    }

    // 写入不同数据
    int64_t uniqueId = 10;
    RdStreamDcSetData(uniqueId, 0, &data);
    ret = RdStreamSetVertexWithBuf(streamStmt, vertexLabel, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(streamStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, ret);

    // 查询时序表数据
    ret = RdTsCheckRecordCount(tsStmt, "ts1", 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    RdStreamExecDescT clean[] = {
        {"drop stream table t1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    RdFinishTest();
}

// @TestcaseName 创建包含去重函数的视图，投影部分包含列名和通配符 成功
TEST_F(t02_distinct_count_parameter, STREAM_007_027)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");
    RdStreamExecDescT prepare[] = {
        {
            "create table ts1 (id integer, id2 integer, name char(50), time integer, count integer) "
            "with (time_col = 'time', interval = '1 hour');", GMC_MODEL_TS
        },
        {"create stream table t1 (id integer, name char(50), time integer);"},
        {
            "create stream view v1 as select id, *, seq_distinct_count(id, name, time) "
            "from t1 with (tuple_buffer_size = 1);"
        },
        {"create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 写入重复数据
    int64_t repeatId = 8;
    RdStreamDcDataT data = {0};
    RdStreamDcSetData(repeatId, 0, &data);
    uint32_t repeatCnt = 2000;

    ret = GmcPrepareStmtByLabelName(streamStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < repeatCnt; i++) {
        ret = RdStreamSetVertexWithBuf(streamStmt, vertexLabel, &data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(streamStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 写入不同数据
    int64_t uniqueId = 10;
    RdStreamDcSetData(uniqueId, 0, &data);
    ret = RdStreamSetVertexWithBuf(streamStmt, vertexLabel, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(streamStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 查询时序表数据
    bool eof;
    const char *qrySql = "select * from ts1;";
    ret = GmcExecDirect(tsStmt, qrySql, strlen(qrySql));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckResultRows(tsStmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(tsStmt, &eof);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_BOOL(false, eof);

    RdStreamDcSetData(repeatId, 0, &data);
    ret = RdTsCheckPropertyInt64(tsStmt, 0, data.id);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckPropertyInt64(tsStmt, 1, data.id);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckPropertyText(tsStmt, 2, data.name, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckPropertyInt64(tsStmt, 3, data.time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckPropertyInt64(tsStmt, 4, repeatCnt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    RdFinishTest();
}

// @TestcaseName 创建包含去重函数的视图，投影部分和函数参数包含列名和通配符 报错，语义错误
TEST_F(t02_distinct_count_parameter, STREAM_007_028)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");
    RdStreamExecDescT prepare[] = {
        {
            "create table ts1 (id integer, id2 integer, name char(50), time integer, count integer) "
            "with (time_col = 'time', interval = '1 hour');", GMC_MODEL_TS
        },
        {"create stream table t1 (id integer, name char(50), time integer);"},
        {
            "create stream view v1 as select id, * seq_distinct_count(id, *) "
            "from t1 with (tuple_buffer_size = 1);", GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR
        },
        {"create stream sink s1 as select * from v1 into tsdb (ts1);", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 写入重复数据
    int64_t repeatId = 8;
    RdStreamDcDataT data = {0};
    RdStreamDcSetData(repeatId, 0, &data);
    uint32_t repeatCnt = 2000;

    ret = GmcPrepareStmtByLabelName(streamStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < repeatCnt; i++) {
        ret = RdStreamSetVertexWithBuf(streamStmt, vertexLabel, &data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(streamStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, ret);
    }

    // 写入不同数据
    int64_t uniqueId = 10;
    RdStreamDcSetData(uniqueId, 0, &data);
    ret = RdStreamSetVertexWithBuf(streamStmt, vertexLabel, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(streamStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, ret);

    // 查询时序表数据
    ret = RdTsCheckRecordCount(tsStmt, "ts1", 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    RdStreamExecDescT clean[] = {
        {"drop stream table t1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    RdFinishTest();
}

// @TestcaseName 创建包含去重函数的视图，函数入参包含重复的列名 成功
TEST_F(t02_distinct_count_parameter, STREAM_007_029)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");
    RdStreamExecDescT prepare[] = {
        {
            "create table ts1 (id integer, name char(50), time integer, count integer) "
            "with (time_col = 'time', interval = '1 hour');", GMC_MODEL_TS
        },
        {"create stream table t1 (id integer, name char(50), time integer);"},
        {
            "create stream view v1 as select id, name, time, seq_distinct_count(id, id, name, time) "
            "from t1 with (tuple_buffer_size = 1);"
        },
        {"create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 写入重复数据
    int64_t repeatId = 8;
    RdStreamDcDataT data = {0};
    RdStreamDcSetData(repeatId, 0, &data);
    uint32_t repeatCnt = 2000;

    ret = GmcPrepareStmtByLabelName(streamStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < repeatCnt; i++) {
        ret = RdStreamSetVertexWithBuf(streamStmt, vertexLabel, &data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(streamStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 写入不同数据
    int64_t uniqueId = 10;
    RdStreamDcSetData(uniqueId, 0, &data);
    ret = RdStreamSetVertexWithBuf(streamStmt, vertexLabel, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(streamStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 查询时序表数据
    bool eof;
    const char *qrySql = "select * from ts1;";
    ret = GmcExecDirect(tsStmt, qrySql, strlen(qrySql));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckResultRows(tsStmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(tsStmt, &eof);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_BOOL(false, eof);

    RdStreamDcSetData(repeatId, 0, &data);
    ret = RdTsCheckPropertyInt64(tsStmt, 0, data.id);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckPropertyText(tsStmt, 1, data.name, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckPropertyInt64(tsStmt, 2, data.time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckPropertyInt64(tsStmt, 3, repeatCnt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    RdFinishTest();
}

// @TestcaseName 创建包含去重函数的视图，投影部分包含重复的列名 成功
TEST_F(t02_distinct_count_parameter, STREAM_007_030)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");
    RdStreamExecDescT prepare[] = {
        {
            "create table ts1 (id integer, id2 integer, name char(50), time integer, count integer) "
            "with (time_col = 'time', interval = '1 hour');", GMC_MODEL_TS
        },
        {"create stream table t1 (id integer, name char(50), time integer);"},
        {
            "create stream view v1 as select id, id, name, time, seq_distinct_count(id, name, time) "
            "from t1 with (tuple_buffer_size = 1);"
        },
        {"create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 写入重复数据
    int64_t repeatId = 8;
    RdStreamDcDataT data = {0};
    RdStreamDcSetData(repeatId, 0, &data);
    uint32_t repeatCnt = 2000;

    ret = GmcPrepareStmtByLabelName(streamStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < repeatCnt; i++) {
        ret = RdStreamSetVertexWithBuf(streamStmt, vertexLabel, &data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(streamStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 写入不同数据
    int64_t uniqueId = 10;
    RdStreamDcSetData(uniqueId, 0, &data);
    ret = RdStreamSetVertexWithBuf(streamStmt, vertexLabel, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(streamStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 查询时序表数据
    bool eof;
    const char *qrySql = "select * from ts1;";
    ret = GmcExecDirect(tsStmt, qrySql, strlen(qrySql));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckResultRows(tsStmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(tsStmt, &eof);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_BOOL(false, eof);

    RdStreamDcSetData(repeatId, 0, &data);
    ret = RdTsCheckPropertyInt64(tsStmt, 0, data.id);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckPropertyInt64(tsStmt, 1, data.id);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckPropertyText(tsStmt, 2, data.name, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckPropertyInt64(tsStmt, 3, data.time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckPropertyInt64(tsStmt, 4, repeatCnt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    RdFinishTest();
}

// @TestcaseName 创建包含去重函数的视图，投影部分和函数参数包含重复的列名 成功
TEST_F(t02_distinct_count_parameter, STREAM_007_031)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");
    RdStreamExecDescT prepare[] = {
        {
            "create table ts1 (id integer, id2 integer, name char(50), time integer, count integer) "
            "with (time_col = 'time', interval = '1 hour');", GMC_MODEL_TS
        },
        {"create stream table t1 (id integer, name char(50), time integer);"},
        {
            "create stream view v1 as select id, id, name, time, seq_distinct_count(id, id, name, time) "
            "from t1 with (tuple_buffer_size = 1);"
        },
        {"create stream sink s1 as select * from v1 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 写入重复数据
    int64_t repeatId = 8;
    RdStreamDcDataT data = {0};
    RdStreamDcSetData(repeatId, 0, &data);
    uint32_t repeatCnt = 2000;

    ret = GmcPrepareStmtByLabelName(streamStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < repeatCnt; i++) {
        ret = RdStreamSetVertexWithBuf(streamStmt, vertexLabel, &data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(streamStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 写入不同数据
    int64_t uniqueId = 10;
    RdStreamDcSetData(uniqueId, 0, &data);
    ret = RdStreamSetVertexWithBuf(streamStmt, vertexLabel, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(streamStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 查询时序表数据
    bool eof;
    const char *qrySql = "select * from ts1;";
    ret = GmcExecDirect(tsStmt, qrySql, strlen(qrySql));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckResultRows(tsStmt, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(tsStmt, &eof);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_BOOL(false, eof);

    RdStreamDcSetData(repeatId, 0, &data);
    ret = RdTsCheckPropertyInt64(tsStmt, 0, data.id);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckPropertyInt64(tsStmt, 1, data.id);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckPropertyText(tsStmt, 2, data.name, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckPropertyInt64(tsStmt, 3, data.time);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckPropertyInt64(tsStmt, 4, repeatCnt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;", GMC_MODEL_TS},
    };
    ret = RdStreamExecSql(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    RdFinishTest();
}
