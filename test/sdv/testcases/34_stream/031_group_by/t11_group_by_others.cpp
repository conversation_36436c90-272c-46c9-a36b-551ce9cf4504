/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: group by其他场景用例
 * Author: guopanpan
 * Create: 2024-12-26
 */
#include <iostream>
#include <map>
#include <vector>
#include <algorithm>
#include "gtest/gtest.h"
#include "gmc_sql.h"
#include "rd_feature_stream.h"
#include "rd_feature_ts.h"
#include "rd_stream_thirty_one.h"

class t11_group_by_others : public testing::Test {
public:
    static GmcConnT *longConn;
    static GmcStmtT *longStmt;
    GmcConnT *shortConn = NULL;
    GmcStmtT *shortStmt = NULL;
    GmcConnT *asyncConn = NULL;
    GmcStmtT *asyncStmt = NULL;

    RdVertexLabelT *vertexLabel = NULL;
    RdThirtyOneDataT *dataObj = NULL;
    RdThirtyOneDataT *dataQry = NULL;

    bool hasFinishTest = false;
    void RdFinishTest();

    static void SetUpTestCase();
    static void TearDownTestCase();
    virtual void SetUp();
    virtual void TearDown();
};

GmcConnT *t11_group_by_others::longConn = NULL;
GmcStmtT *t11_group_by_others::longStmt = NULL;

void t11_group_by_others::SetUpTestCase()
{
    int32_t ret;
    ret = RdStreamEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdGmcConnect(&longConn, &longStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdCreateEpollThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdLogSetLevel4Stdout(RD_LOG_LEVEL_INFO);
}

void t11_group_by_others::TearDownTestCase()
{
    int32_t ret;
    ret = RdGmcDisconnect(longConn, longStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdStreamEnvClean();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdCloseEpollThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void t11_group_by_others::SetUp()
{
    int32_t ret;
    hasFinishTest = false;
    ret = RdGmcConnect(&shortConn, &shortStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdGmcConnect(&asyncConn, &asyncStmt, GMC_CONN_TYPE_ASYNC);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    dataObj = RdThirtyOneAllocData();
    AW_MACRO_ASSERT_NOTNULL(dataObj);
    dataQry = RdThirtyOneAllocData();
    AW_MACRO_ASSERT_NOTNULL(dataQry);
}

void t11_group_by_others::TearDown()
{
    int32_t ret;
    if (!hasFinishTest) {
        RdStreamExecDescT clean[] = {
            {"drop stream sink s1;"},
            {"drop stream view v3;"},
            {"drop stream view v2;"},
            {"drop stream view v1;"},
            {"drop stream reference r1;"},
            {"drop stream table t2;"},
            {"drop stream table t1;"},
            {"drop table ts1;"},
        };
        ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
        EXPECT_EQ(GMERR_OK, ret);
        RdStreamFreeTableSchema(vertexLabel);
        vertexLabel = NULL;
    }

    ret = RdGmcDisconnect(shortConn, shortStmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = RdGmcDisconnect(asyncConn, asyncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    RdThirtyOneFreeData(dataObj);
    dataObj = NULL;
    RdThirtyOneFreeData(dataQry);
    dataQry = NULL;
}

void t11_group_by_others::RdFinishTest()
{
    hasFinishTest = true;
}

// @TestSuite 其他场景
// @TestcaseName 创建无窗口视图，无group by，进行聚合计算    报错，语义错误
TEST_F(t11_group_by_others, STREAM_031_150)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text, max integer) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '1' seconds strict);"},

        // 2. 创建无窗口视图，无group by，进行聚合计算
        {"create stream view v1 as select *, count(id) from t1 with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_SEMANTIC_ERROR},
        {"create stream view v1 as select *, max(id) from t1 with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_SEMANTIC_ERROR},
        {"create stream view v1 as select *, min(id) from t1 with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_SEMANTIC_ERROR},
        {"create stream view v1 as select *, sum(id) from t1 with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_SEMANTIC_ERROR},
        {"create stream view v1 as select *, avg(id) from t1 with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_SEMANTIC_ERROR},

        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"create stream sink s1 as select first_id, first_name, window_end, level, first_content, count_id from v3 "
            "into tsdb (ts1) with (batch_window_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 3. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v3;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v2;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdFinishTest();
}

// @TestcaseName 创建窗口视图，无group by，进行聚合计算    报错，语义错误
TEST_F(t11_group_by_others, STREAM_031_151)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text, max integer) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '1' seconds strict);"},

        // 2. 创建窗口视图，无group by，进行聚合计算
        {"create stream view v1 as select *, count(id) "
            "from table(hop(table t1, time, interval '3' seconds, interval '9' seconds)) "
            "with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_SEMANTIC_ERROR},
        {"create stream view v1 as select *, max(id) "
            "from table(hop(table t1, time, interval '3' seconds, interval '9' seconds)) "
            "with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_SEMANTIC_ERROR},
        {"create stream view v1 as select *, min(id) "
            "from table(hop(table t1, time, interval '3' seconds, interval '9' seconds)) "
            "with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_SEMANTIC_ERROR},
        {"create stream view v1 as select *, sum(id) "
            "from table(hop(table t1, time, interval '3' seconds, interval '9' seconds)) "
            "with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_SEMANTIC_ERROR},
        {"create stream view v1 as select *, avg(id) "
            "from table(hop(table t1, time, interval '3' seconds, interval '9' seconds)) "
            "with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_SEMANTIC_ERROR},

        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"create stream sink s1 as select first_id, first_name, window_end, level, first_content, count_id from v3 "
            "into tsdb (ts1) with (batch_window_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 3. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v3;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v2;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdFinishTest();
}

// @TestcaseName 在group by后进行where过滤  报错，语法错误
TEST_F(t11_group_by_others, STREAM_031_152)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text, max integer) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '1' seconds strict);"},

        // 2. 在group by后进行where过滤
        {"create stream view v1 as select *, count(level) "
            "from table(hop(table t1, time, interval '3' seconds, interval '9' seconds)) "
            "group by window_start, window_end, level "
            "where window_start > 0 and window_end <= 9 "
            "with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR},

        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"create stream sink s1 as select first_id, first_name, window_end, level, first_content, count_id from v3 "
            "into tsdb (ts1) with (batch_window_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 3. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v3;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v2;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdFinishTest();
}
