/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 流表水印测试
 * Author: guopanpan
 * Create: 2024-12-26
 */
#include <iostream>
#include <map>
#include <vector>
#include <algorithm>
#include "gtest/gtest.h"
#include "gmc_sql.h"
#include "rd_feature_stream.h"
#include "rd_feature_ts.h"
#include "rd_stream_thirty_one.h"

class t10_group_by_watermark : public testing::Test {
public:
    static GmcConnT *longConn;
    static GmcStmtT *longStmt;
    GmcConnT *shortConn = NULL;
    GmcStmtT *shortStmt = NULL;
    GmcConnT *asyncConn = NULL;
    GmcStmtT *asyncStmt = NULL;

    RdVertexLabelT *vertexLabel = NULL;
    RdThirtyOneDataT *dataObj = NULL;
    RdThirtyOneDataT *dataQry = NULL;

    bool hasFinishTest = false;
    void RdFinishTest();

    static void SetUpTestCase();
    static void TearDownTestCase();
    virtual void SetUp();
    virtual void TearDown();
};

GmcConnT *t10_group_by_watermark::longConn = NULL;
GmcStmtT *t10_group_by_watermark::longStmt = NULL;

void t10_group_by_watermark::SetUpTestCase()
{
    int32_t ret;
    ret = RdStreamEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdGmcConnect(&longConn, &longStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdCreateEpollThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdLogSetLevel4Stdout(RD_LOG_LEVEL_INFO);
}

void t10_group_by_watermark::TearDownTestCase()
{
    int32_t ret;
    ret = RdGmcDisconnect(longConn, longStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdStreamEnvClean();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdCloseEpollThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void t10_group_by_watermark::SetUp()
{
    int32_t ret;
    hasFinishTest = false;
    ret = RdGmcConnect(&shortConn, &shortStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdGmcConnect(&asyncConn, &asyncStmt, GMC_CONN_TYPE_ASYNC);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    dataObj = RdThirtyOneAllocData();
    AW_MACRO_ASSERT_NOTNULL(dataObj);
    dataQry = RdThirtyOneAllocData();
    AW_MACRO_ASSERT_NOTNULL(dataQry);
}

void t10_group_by_watermark::TearDown()
{
    int32_t ret;
    if (!hasFinishTest) {
        RdStreamExecDescT clean[] = {
            {"drop stream sink s1;"},
            {"drop stream view v5;"},
            {"drop stream view v4;"},
            {"drop stream view v3;"},
            {"drop stream view v2;"},
            {"drop stream view v1;"},
            {"drop stream reference r1;"},
            {"drop stream table t2;"},
            {"drop stream table t1;"},
            {"drop table ts1;"},
        };
        ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
        EXPECT_EQ(GMERR_OK, ret);
        RdStreamFreeTableSchema(vertexLabel);
        vertexLabel = NULL;
    }

    ret = RdGmcDisconnect(shortConn, shortStmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = RdGmcDisconnect(asyncConn, asyncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    RdThirtyOneFreeData(dataObj);
    dataObj = NULL;
    RdThirtyOneFreeData(dataQry);
    dataQry = NULL;
}

void t10_group_by_watermark::RdFinishTest()
{
    hasFinishTest = true;
}

// @TestSuite 时间水印
// @TestcaseName 通过group by进行count聚合计算，group by后指定的列是时间列 成功
TEST_F(t10_group_by_watermark, STREAM_031_133)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text, count integer) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '1' seconds strict);"},

        // 2. 通过group by进行count聚合计算，group by后指定的列是时间列
        {"create stream view v1 as select *, count(level) "
            "from table(hop(table t1, time, interval '3' seconds, interval '9' seconds)) "
            "group by window_start, window_end, time with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select first_id, first_name, time, first_level, first_content, count_level from v3 "
            "into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 3. 写入数据
    // 对应的时间列:           0        3        6        9        12
    int64_t insertLevels[] = {1, 2, 2, 3, 3, 3, 4, 4, 0, 4, 4, 1, 99, 100};
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (size_t i = 0; i < RD_ELEMENT_COUNT(insertLevels); i++) {
        RdThirtyOneSetData(dataObj, i, 0);
        RdThirtyOneSetDataLevel(dataObj, insertLevels[i]);
        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, dataObj);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 4. 读取校验数据, 有预期结果
    struct StreamAggColumns {
        int64_t windowStart;
        int64_t windowEnd;
        int64_t time;

        bool operator<(const StreamAggColumns &b) const {
            if (windowStart != b.windowStart) {
                return windowStart < b.windowStart;
            }
            if (windowEnd != b.windowEnd) {
                return windowEnd < b.windowEnd;
            }
            return time < b.time;
        }
    };
    struct StreamOutputCount {
        RdThirtyOneDataT *base;
        int64_t count;
    };

    auto genStreamOutput = [insertLevels]() {
        int64_t start = 0, width = 9, slide = 3, watermark = 1;
        std::map<StreamAggColumns, StreamOutputCount> resMap;
        for (int64_t s = start - width; s + width < start + RD_ELEMENT_COUNT(insertLevels) - watermark; s += slide) {
            for (int64_t j = s < 0 ? 0 : s; j < s + width && j < RD_ELEMENT_COUNT(insertLevels); j++) {
                StreamAggColumns key = {s, s + width, j};
                if (resMap.find(key) == resMap.end()) {
                    RdThirtyOneDataT *base = RdThirtyOneAllocData();
                    if (base == NULL) {
                        RD_ERROR("Unable to alloc data.");
                        break;
                    }
                    RdThirtyOneSetData(base, j, 0);
                    RdThirtyOneSetDataLevel(base, insertLevels[j]);
                    StreamOutputCount val = {base, 1};
                    resMap[key] = val;
                } else {
                    resMap[key].count++;
                }
            }
        }
        std::vector<StreamOutputCount> resVec;
        for (const auto &pair: resMap) {
            resVec.push_back(pair.second);
        }
        return resVec;
    };
    auto output = genStreamOutput();
    std::sort(output.begin(), output.end(), [](StreamOutputCount a, StreamOutputCount b) {
        if (a.base->time != b.base->time) {
            return a.base->time < b.base->time; // 升序排序
        }
        if (a.base->id != b.base->id) {
            return a.base->id < b.base->id;
        }
        if (a.base->level != b.base->level) {
            return a.base->level < b.base->level;
        }
        return a.count < b.count;
    });

    const char *qrySql = "select * from ts1 order by time, id, level;";
    ret = GmcExecDirect(shortStmt, qrySql, strlen(qrySql));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckResultRows(shortStmt, output.size());
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (const auto &entry: output) {
        ret = RdThirtyOneFetchTsData(shortStmt, dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdTsCheckPropertyInt64(shortStmt, 5, entry.count);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdThirtyOneCheckData(*entry.base, *dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 5. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;

    for (const auto &entry: output) {
        RdThirtyOneFreeData(entry.base);
    }
    RdFinishTest();
}

// @TestcaseName 通过group by进行count聚合计算，count函数的参数是时间列 成功
TEST_F(t10_group_by_watermark, STREAM_031_134)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text, count integer) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '1' seconds strict);"},

        // 2. 通过group by进行count聚合计算，count函数的参数是时间列
        {"create stream view v1 as select *, count(time) "
            "from table(hop(table t1, time, interval '3' seconds, interval '9' seconds)) "
            "group by window_start, window_end, level with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select first_id, first_name, window_end, level, first_content, count_time from v3 "
            "into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 3. 写入数据
    // 对应的时间列:           0        3        6        9        12
    int64_t insertLevels[] = {1, 2, 2, 3, 3, 3, 4, 4, 0, 4, 4, 1, 99, 100};
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (size_t i = 0; i < RD_ELEMENT_COUNT(insertLevels); i++) {
        RdThirtyOneSetData(dataObj, i, 0);
        RdThirtyOneSetDataLevel(dataObj, insertLevels[i]);
        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, dataObj);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 4. 读取校验数据, 有预期结果
    struct StreamAggColumns {
        int64_t windowStart;
        int64_t windowEnd;
        int64_t level;

        bool operator<(const StreamAggColumns &b) const {
            if (windowStart != b.windowStart) {
                return windowStart < b.windowStart;
            }
            if (windowEnd != b.windowEnd) {
                return windowEnd < b.windowEnd;
            }
            return level < b.level;
        }
    };
    struct StreamOutputCount {
        RdThirtyOneDataT *base;
        int64_t count;
    };

    auto genStreamOutput = [insertLevels]() {
        int64_t start = 0, width = 9, slide = 3, watermark = 1;
        std::map<StreamAggColumns, StreamOutputCount> resMap;
        for (int64_t s = start - width; s + width < start + RD_ELEMENT_COUNT(insertLevels) - watermark; s += slide) {
            for (int64_t j = s < 0 ? 0 : s; j < s + width && j < RD_ELEMENT_COUNT(insertLevels); j++) {
                StreamAggColumns key = {s, s + width, insertLevels[j]};
                if (resMap.find(key) == resMap.end()) {
                    RdThirtyOneDataT *base = RdThirtyOneAllocData();
                    if (base == NULL) {
                        RD_ERROR("Unable to alloc data.");
                        break;
                    }
                    RdThirtyOneSetData(base, j, 0);
                    RdThirtyOneSetDataLevel(base, insertLevels[j]);
                    RdThirtyOneSetDataTime(base, s + width);
                    StreamOutputCount val = {base, 1};
                    resMap[key] = val;
                } else {
                    resMap[key].count++;
                }
            }
        }
        std::vector<StreamOutputCount> resVec;
        for (const auto &pair: resMap) {
            resVec.push_back(pair.second);
        }
        return resVec;
    };
    auto output = genStreamOutput();
    std::sort(output.begin(), output.end(), [](StreamOutputCount a, StreamOutputCount b) {
        if (a.base->time != b.base->time) {
            return a.base->time < b.base->time; // 升序排序
        }
        if (a.base->id != b.base->id) {
            return a.base->id < b.base->id;
        }
        if (a.base->level != b.base->level) {
            return a.base->level < b.base->level;
        }
        return a.count < b.count;
    });

    const char *qrySql = "select * from ts1 order by time, id, level;";
    ret = GmcExecDirect(shortStmt, qrySql, strlen(qrySql));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckResultRows(shortStmt, output.size());
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (const auto &entry: output) {
        ret = RdThirtyOneFetchTsData(shortStmt, dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdTsCheckPropertyInt64(shortStmt, 5, entry.count);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdThirtyOneCheckData(*entry.base, *dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 5. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;

    for (const auto &entry: output) {
        RdThirtyOneFreeData(entry.base);
    }
    RdFinishTest();
}

// @TestSuite 水印传递
// @TestcaseName 创建多个视图，在第二个视图上进行group by聚合 成功
TEST_F(t10_group_by_watermark, STREAM_031_135)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text, count integer) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '1' seconds strict);"},

        // 2. 创建多个视图，在第二个视图上进行group by聚合
        {"create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select *, count(level) "
            "from table(hop(table v1, time, interval '3' seconds, interval '9' seconds)) "
            "group by window_start, window_end, level with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select first_id, first_name, window_end, level, first_content, count_level from v3 "
            "into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 3. 写入数据
    // 对应的时间列:           0        3        6        9        12
    int64_t insertLevels[] = {1, 2, 2, 3, 3, 3, 4, 4, 0, 4, 4, 1, 99, 100};
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (size_t i = 0; i < RD_ELEMENT_COUNT(insertLevels); i++) {
        RdThirtyOneSetData(dataObj, i, 0);
        RdThirtyOneSetDataLevel(dataObj, insertLevels[i]);
        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, dataObj);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 4. 读取校验数据, 有预期结果
    struct StreamAggColumns {
        int64_t windowStart;
        int64_t windowEnd;
        int64_t level;

        bool operator<(const StreamAggColumns &b) const {
            if (windowStart != b.windowStart) {
                return windowStart < b.windowStart;
            }
            if (windowEnd != b.windowEnd) {
                return windowEnd < b.windowEnd;
            }
            return level < b.level;
        }
    };
    struct StreamOutputCount {
        RdThirtyOneDataT *base;
        int64_t count;
    };

    auto genStreamOutput = [insertLevels]() {
        int64_t start = 0, width = 9, slide = 3, watermark = 1;
        std::map<StreamAggColumns, StreamOutputCount> resMap;
        for (int64_t s = start - width; s + width < start + RD_ELEMENT_COUNT(insertLevels) - watermark; s += slide) {
            for (int64_t j = s < 0 ? 0 : s; j < s + width && j < RD_ELEMENT_COUNT(insertLevels); j++) {
                StreamAggColumns key = {s, s + width, insertLevels[j]};
                if (resMap.find(key) == resMap.end()) {
                    RdThirtyOneDataT *base = RdThirtyOneAllocData();
                    if (base == NULL) {
                        RD_ERROR("Unable to alloc data.");
                        break;
                    }
                    RdThirtyOneSetData(base, j, 0);
                    RdThirtyOneSetDataLevel(base, insertLevels[j]);
                    RdThirtyOneSetDataTime(base, s + width);
                    StreamOutputCount val = {base, 1};
                    resMap[key] = val;
                } else {
                    resMap[key].count++;
                }
            }
        }
        std::vector<StreamOutputCount> resVec;
        for (const auto &pair: resMap) {
            resVec.push_back(pair.second);
        }
        return resVec;
    };
    auto output = genStreamOutput();
    std::sort(output.begin(), output.end(), [](StreamOutputCount a, StreamOutputCount b) {
        if (a.base->time != b.base->time) {
            return a.base->time < b.base->time; // 升序排序
        }
        if (a.base->id != b.base->id) {
            return a.base->id < b.base->id;
        }
        if (a.base->level != b.base->level) {
            return a.base->level < b.base->level;
        }
        return a.count < b.count;
    });

    const char *qrySql = "select * from ts1 order by time, id, level;";
    ret = GmcExecDirect(shortStmt, qrySql, strlen(qrySql));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckResultRows(shortStmt, output.size());
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (const auto &entry: output) {
        ret = RdThirtyOneFetchTsData(shortStmt, dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdTsCheckPropertyInt64(shortStmt, 5, entry.count);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdThirtyOneCheckData(*entry.base, *dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 5. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;

    for (const auto &entry: output) {
        RdThirtyOneFreeData(entry.base);
    }
    RdFinishTest();
}

// @TestcaseName 创建多个视图，在第五个视图上进行group by聚合 成功
TEST_F(t10_group_by_watermark, STREAM_031_136)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text, count integer) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '1' seconds strict);"},

        // 2. 创建多个视图，在第五个视图上进行group by聚合
        {"create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream view v4 as select * from v3 with (tuple_buffer_size = 1);"},
        {"create stream view v5 as select *, count(level) "
            "from table(hop(table v4, time, interval '3' seconds, interval '9' seconds)) "
            "group by window_start, window_end, level with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select first_id, first_name, window_end, level, first_content, count_level from v5 "
            "into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 3. 写入数据
    // 对应的时间列:           0        3        6        9        12
    int64_t insertLevels[] = {1, 2, 2, 3, 3, 3, 4, 4, 0, 4, 4, 1, 99, 100};
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (size_t i = 0; i < RD_ELEMENT_COUNT(insertLevels); i++) {
        RdThirtyOneSetData(dataObj, i, 0);
        RdThirtyOneSetDataLevel(dataObj, insertLevels[i]);
        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, dataObj);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 4. 读取校验数据, 有预期结果
    struct StreamAggColumns {
        int64_t windowStart;
        int64_t windowEnd;
        int64_t level;

        bool operator<(const StreamAggColumns &b) const {
            if (windowStart != b.windowStart) {
                return windowStart < b.windowStart;
            }
            if (windowEnd != b.windowEnd) {
                return windowEnd < b.windowEnd;
            }
            return level < b.level;
        }
    };
    struct StreamOutputCount {
        RdThirtyOneDataT *base;
        int64_t count;
    };

    auto genStreamOutput = [insertLevels]() {
        int64_t start = 0, width = 9, slide = 3, watermark = 1;
        std::map<StreamAggColumns, StreamOutputCount> resMap;
        for (int64_t s = start - width; s + width < start + RD_ELEMENT_COUNT(insertLevels) - watermark; s += slide) {
            for (int64_t j = s < 0 ? 0 : s; j < s + width && j < RD_ELEMENT_COUNT(insertLevels); j++) {
                StreamAggColumns key = {s, s + width, insertLevels[j]};
                if (resMap.find(key) == resMap.end()) {
                    RdThirtyOneDataT *base = RdThirtyOneAllocData();
                    if (base == NULL) {
                        RD_ERROR("Unable to alloc data.");
                        break;
                    }
                    RdThirtyOneSetData(base, j, 0);
                    RdThirtyOneSetDataLevel(base, insertLevels[j]);
                    RdThirtyOneSetDataTime(base, s + width);
                    StreamOutputCount val = {base, 1};
                    resMap[key] = val;
                } else {
                    resMap[key].count++;
                }
            }
        }
        std::vector<StreamOutputCount> resVec;
        for (const auto &pair: resMap) {
            resVec.push_back(pair.second);
        }
        return resVec;
    };
    auto output = genStreamOutput();
    std::sort(output.begin(), output.end(), [](StreamOutputCount a, StreamOutputCount b) {
        if (a.base->time != b.base->time) {
            return a.base->time < b.base->time; // 升序排序
        }
        if (a.base->id != b.base->id) {
            return a.base->id < b.base->id;
        }
        if (a.base->level != b.base->level) {
            return a.base->level < b.base->level;
        }
        return a.count < b.count;
    });

    const char *qrySql = "select * from ts1 order by time, id, level;";
    ret = GmcExecDirect(shortStmt, qrySql, strlen(qrySql));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckResultRows(shortStmt, output.size());
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (const auto &entry: output) {
        ret = RdThirtyOneFetchTsData(shortStmt, dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdTsCheckPropertyInt64(shortStmt, 5, entry.count);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdThirtyOneCheckData(*entry.base, *dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 5. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v5;"},
        {"drop stream view v4;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;

    for (const auto &entry: output) {
        RdThirtyOneFreeData(entry.base);
    }
    RdFinishTest();
}

// @TestcaseName 创建多视图，在sink上进行group by聚合 成功
TEST_F(t10_group_by_watermark, STREAM_031_137)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text, count integer) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '1' seconds strict);"},

        {"create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},

        // 2. 创建多视图，在sink上进行group by聚合
        {"create stream sink s1 as select id, name, window_end, level, content, count(level) "
            "from table(hop(table v3, time, interval '3' seconds, interval '9' seconds)) "
            "group by window_start, window_end, level "
            "into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 3. 写入数据
    // 对应的时间列:           0        3        6        9        12
    int64_t insertLevels[] = {1, 2, 2, 3, 3, 3, 4, 4, 0, 4, 4, 1, 99, 100};
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (size_t i = 0; i < RD_ELEMENT_COUNT(insertLevels); i++) {
        RdThirtyOneSetData(dataObj, i, 0);
        RdThirtyOneSetDataLevel(dataObj, insertLevels[i]);
        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, dataObj);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 4. 读取校验数据, 有预期结果
    struct StreamAggColumns {
        int64_t windowStart;
        int64_t windowEnd;
        int64_t level;

        bool operator<(const StreamAggColumns &b) const {
            if (windowStart != b.windowStart) {
                return windowStart < b.windowStart;
            }
            if (windowEnd != b.windowEnd) {
                return windowEnd < b.windowEnd;
            }
            return level < b.level;
        }
    };
    struct StreamOutputCount {
        RdThirtyOneDataT *base;
        int64_t count;
    };

    auto genStreamOutput = [insertLevels]() {
        int64_t start = 0, width = 9, slide = 3, watermark = 1;
        std::map<StreamAggColumns, StreamOutputCount> resMap;
        for (int64_t s = start - width; s + width < start + RD_ELEMENT_COUNT(insertLevels) - watermark; s += slide) {
            for (int64_t j = s < 0 ? 0 : s; j < s + width && j < RD_ELEMENT_COUNT(insertLevels); j++) {
                StreamAggColumns key = {s, s + width, insertLevels[j]};
                if (resMap.find(key) == resMap.end()) {
                    RdThirtyOneDataT *base = RdThirtyOneAllocData();
                    if (base == NULL) {
                        RD_ERROR("Unable to alloc data.");
                        break;
                    }
                    RdThirtyOneSetData(base, j, 0);
                    RdThirtyOneSetDataLevel(base, insertLevels[j]);
                    RdThirtyOneSetDataTime(base, s + width);
                    StreamOutputCount val = {base, 1};
                    resMap[key] = val;
                } else {
                    resMap[key].count++;
                }
            }
        }
        std::vector<StreamOutputCount> resVec;
        for (const auto &pair: resMap) {
            resVec.push_back(pair.second);
        }
        return resVec;
    };
    auto output = genStreamOutput();
    std::sort(output.begin(), output.end(), [](StreamOutputCount a, StreamOutputCount b) {
        if (a.base->time != b.base->time) {
            return a.base->time < b.base->time; // 升序排序
        }
        if (a.base->id != b.base->id) {
            return a.base->id < b.base->id;
        }
        if (a.base->level != b.base->level) {
            return a.base->level < b.base->level;
        }
        return a.count < b.count;
    });

    const char *qrySql = "select * from ts1 order by time, id, level;";
    ret = GmcExecDirect(shortStmt, qrySql, strlen(qrySql));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckResultRows(shortStmt, output.size());
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (const auto &entry: output) {
        ret = RdThirtyOneFetchTsData(shortStmt, dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdTsCheckPropertyInt64(shortStmt, 5, entry.count);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdThirtyOneCheckData(*entry.base, *dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 5. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;

    for (const auto &entry: output) {
        RdThirtyOneFreeData(entry.base);
    }
    RdFinishTest();
}

// @TestcaseName 不创建视图，在sink进行group by聚合 成功
TEST_F(t10_group_by_watermark, STREAM_031_138)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text, count integer) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '1' seconds strict);"},

        // 2. 不创建视图，在sink进行group by聚合
        {"create stream sink s1 as select id, name, window_end, level, content, count(level) "
            "from table(hop(table t1, time, interval '3' seconds, interval '9' seconds)) "
            "group by window_start, window_end, level "
            "into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 3. 写入数据
    // 对应的时间列:           0        3        6        9        12
    int64_t insertLevels[] = {1, 2, 2, 3, 3, 3, 4, 4, 0, 4, 4, 1, 99, 100};
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (size_t i = 0; i < RD_ELEMENT_COUNT(insertLevels); i++) {
        RdThirtyOneSetData(dataObj, i, 0);
        RdThirtyOneSetDataLevel(dataObj, insertLevels[i]);
        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, dataObj);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 4. 读取校验数据, 有预期结果
    struct StreamAggColumns {
        int64_t windowStart;
        int64_t windowEnd;
        int64_t level;

        bool operator<(const StreamAggColumns &b) const {
            if (windowStart != b.windowStart) {
                return windowStart < b.windowStart;
            }
            if (windowEnd != b.windowEnd) {
                return windowEnd < b.windowEnd;
            }
            return level < b.level;
        }
    };
    struct StreamOutputCount {
        RdThirtyOneDataT *base;
        int64_t count;
    };

    auto genStreamOutput = [insertLevels]() {
        int64_t start = 0, width = 9, slide = 3, watermark = 1;
        std::map<StreamAggColumns, StreamOutputCount> resMap;
        for (int64_t s = start - width; s + width < start + RD_ELEMENT_COUNT(insertLevels) - watermark; s += slide) {
            for (int64_t j = s < 0 ? 0 : s; j < s + width && j < RD_ELEMENT_COUNT(insertLevels); j++) {
                StreamAggColumns key = {s, s + width, insertLevels[j]};
                if (resMap.find(key) == resMap.end()) {
                    RdThirtyOneDataT *base = RdThirtyOneAllocData();
                    if (base == NULL) {
                        RD_ERROR("Unable to alloc data.");
                        break;
                    }
                    RdThirtyOneSetData(base, j, 0);
                    RdThirtyOneSetDataLevel(base, insertLevels[j]);
                    RdThirtyOneSetDataTime(base, s + width);
                    StreamOutputCount val = {base, 1};
                    resMap[key] = val;
                } else {
                    resMap[key].count++;
                }
            }
        }
        std::vector<StreamOutputCount> resVec;
        for (const auto &pair: resMap) {
            resVec.push_back(pair.second);
        }
        return resVec;
    };
    auto output = genStreamOutput();
    std::sort(output.begin(), output.end(), [](StreamOutputCount a, StreamOutputCount b) {
        if (a.base->time != b.base->time) {
            return a.base->time < b.base->time; // 升序排序
        }
        if (a.base->id != b.base->id) {
            return a.base->id < b.base->id;
        }
        if (a.base->level != b.base->level) {
            return a.base->level < b.base->level;
        }
        return a.count < b.count;
    });

    const char *qrySql = "select * from ts1 order by time, id, level;";
    ret = GmcExecDirect(shortStmt, qrySql, strlen(qrySql));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckResultRows(shortStmt, output.size());
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (const auto &entry: output) {
        ret = RdThirtyOneFetchTsData(shortStmt, dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdTsCheckPropertyInt64(shortStmt, 5, entry.count);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdThirtyOneCheckData(*entry.base, *dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 5. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;

    for (const auto &entry: output) {
        RdThirtyOneFreeData(entry.base);
    }
    RdFinishTest();
}

// @TestcaseName 创建多个视图，第二个视图投影列的顺序和表定义列的不同，进行group by聚合 成功
TEST_F(t10_group_by_watermark, STREAM_031_139)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text, count integer) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '1' seconds strict);"},

        // 2. 创建多个视图，第二个视图投影列的顺序和表定义列的不同，进行group by聚合
        {"create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select name, time, id, level, content, window_end, count(level) "
            "from table(hop(table v1, time, interval '3' seconds, interval '9' seconds)) "
            "group by window_start, window_end, level with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select first_id, first_name, window_end, level, first_content, count_level from v3 "
            "into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 3. 写入数据
    // 对应的时间列:           0        3        6        9        12
    int64_t insertLevels[] = {1, 2, 2, 3, 3, 3, 4, 4, 0, 4, 4, 1, 99, 100};
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (size_t i = 0; i < RD_ELEMENT_COUNT(insertLevels); i++) {
        RdThirtyOneSetData(dataObj, i, 0);
        RdThirtyOneSetDataLevel(dataObj, insertLevels[i]);
        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, dataObj);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 4. 读取校验数据, 有预期结果
    struct StreamAggColumns {
        int64_t windowStart;
        int64_t windowEnd;
        int64_t level;

        bool operator<(const StreamAggColumns &b) const {
            if (windowStart != b.windowStart) {
                return windowStart < b.windowStart;
            }
            if (windowEnd != b.windowEnd) {
                return windowEnd < b.windowEnd;
            }
            return level < b.level;
        }
    };
    struct StreamOutputCount {
        RdThirtyOneDataT *base;
        int64_t count;
    };

    auto genStreamOutput = [insertLevels]() {
        int64_t start = 0, width = 9, slide = 3, watermark = 1;
        std::map<StreamAggColumns, StreamOutputCount> resMap;
        for (int64_t s = start - width; s + width < start + RD_ELEMENT_COUNT(insertLevels) - watermark; s += slide) {
            for (int64_t j = s < 0 ? 0 : s; j < s + width && j < RD_ELEMENT_COUNT(insertLevels); j++) {
                StreamAggColumns key = {s, s + width, insertLevels[j]};
                if (resMap.find(key) == resMap.end()) {
                    RdThirtyOneDataT *base = RdThirtyOneAllocData();
                    if (base == NULL) {
                        RD_ERROR("Unable to alloc data.");
                        break;
                    }
                    RdThirtyOneSetData(base, j, 0);
                    RdThirtyOneSetDataLevel(base, insertLevels[j]);
                    RdThirtyOneSetDataTime(base, s + width);
                    StreamOutputCount val = {base, 1};
                    resMap[key] = val;
                } else {
                    resMap[key].count++;
                }
            }
        }
        std::vector<StreamOutputCount> resVec;
        for (const auto &pair: resMap) {
            resVec.push_back(pair.second);
        }
        return resVec;
    };
    auto output = genStreamOutput();
    std::sort(output.begin(), output.end(), [](StreamOutputCount a, StreamOutputCount b) {
        if (a.base->time != b.base->time) {
            return a.base->time < b.base->time; // 升序排序
        }
        if (a.base->id != b.base->id) {
            return a.base->id < b.base->id;
        }
        if (a.base->level != b.base->level) {
            return a.base->level < b.base->level;
        }
        return a.count < b.count;
    });

    const char *qrySql = "select * from ts1 order by time, id, level;";
    ret = GmcExecDirect(shortStmt, qrySql, strlen(qrySql));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckResultRows(shortStmt, output.size());
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (const auto &entry: output) {
        ret = RdThirtyOneFetchTsData(shortStmt, dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdTsCheckPropertyInt64(shortStmt, 5, entry.count);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdThirtyOneCheckData(*entry.base, *dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 5. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;

    for (const auto &entry: output) {
        RdThirtyOneFreeData(entry.base);
    }
    RdFinishTest();
}

// @TestcaseName 创建多个视图，第二个视图投影包含时间列和其他部分列，在sink上group by聚合 成功
TEST_F(t10_group_by_watermark, STREAM_031_140)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text, count integer) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '1' seconds strict);"},

        {"create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select content, level, time, name from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},

        // 2. 创建多个视图，第二个视图投影包含时间列和其他部分列，在sink上group by聚合
        {"create stream sink s1 as select window_start, name, window_end, level, content, count(level) "
            "from table(hop(table v3, time, interval '3' seconds, interval '9' seconds)) "
            "group by window_start, window_end, level "
            "into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 3. 写入数据
    // 对应的时间列:           0        3        6        9        12
    int64_t insertLevels[] = {1, 2, 2, 3, 3, 3, 4, 4, 0, 4, 4, 1, 99, 100};
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (size_t i = 0; i < RD_ELEMENT_COUNT(insertLevels); i++) {
        RdThirtyOneSetData(dataObj, i, 0);
        RdThirtyOneSetDataLevel(dataObj, insertLevels[i]);
        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, dataObj);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 4. 读取校验数据, 有预期结果
    struct StreamAggColumns {
        int64_t windowStart;
        int64_t windowEnd;
        int64_t level;

        bool operator<(const StreamAggColumns &b) const {
            if (windowStart != b.windowStart) {
                return windowStart < b.windowStart;
            }
            if (windowEnd != b.windowEnd) {
                return windowEnd < b.windowEnd;
            }
            return level < b.level;
        }
    };
    struct StreamOutputCount {
        RdThirtyOneDataT *base;
        int64_t count;
    };

    auto genStreamOutput = [insertLevels]() {
        int64_t start = 0, width = 9, slide = 3, watermark = 1;
        std::map<StreamAggColumns, StreamOutputCount> resMap;
        for (int64_t s = start - width; s + width < start + RD_ELEMENT_COUNT(insertLevels) - watermark; s += slide) {
            for (int64_t j = s < 0 ? 0 : s; j < s + width && j < RD_ELEMENT_COUNT(insertLevels); j++) {
                StreamAggColumns key = {s, s + width, insertLevels[j]};
                if (resMap.find(key) == resMap.end()) {
                    RdThirtyOneDataT *base = RdThirtyOneAllocData();
                    if (base == NULL) {
                        RD_ERROR("Unable to alloc data.");
                        break;
                    }
                    RdThirtyOneSetData(base, j, 0);
                    RdThirtyOneSetDataId(base, s);
                    RdThirtyOneSetDataLevel(base, insertLevels[j]);
                    RdThirtyOneSetDataTime(base, s + width);
                    StreamOutputCount val = {base, 1};
                    resMap[key] = val;
                } else {
                    resMap[key].count++;
                }
            }
        }
        std::vector<StreamOutputCount> resVec;
        for (const auto &pair: resMap) {
            resVec.push_back(pair.second);
        }
        return resVec;
    };
    auto output = genStreamOutput();
    std::sort(output.begin(), output.end(), [](StreamOutputCount a, StreamOutputCount b) {
        if (a.base->time != b.base->time) {
            return a.base->time < b.base->time; // 升序排序
        }
        if (a.base->id != b.base->id) {
            return a.base->id < b.base->id;
        }
        if (a.base->level != b.base->level) {
            return a.base->level < b.base->level;
        }
        return a.count < b.count;
    });

    const char *qrySql = "select * from ts1 order by time, id, level;";
    ret = GmcExecDirect(shortStmt, qrySql, strlen(qrySql));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckResultRows(shortStmt, output.size());
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (const auto &entry: output) {
        ret = RdThirtyOneFetchTsData(shortStmt, dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdTsCheckPropertyInt64(shortStmt, 5, entry.count);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdThirtyOneCheckData(*entry.base, *dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 5. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;

    for (const auto &entry: output) {
        RdThirtyOneFreeData(entry.base);
    }
    RdFinishTest();
}

// @TestcaseName 创建多个视图，第二个视图投影包含时间列和其他部分列，第二个视图group by聚合 成功
TEST_F(t10_group_by_watermark, STREAM_031_141)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text, count integer) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '1' seconds strict);"},

        // 2. 创建多个视图，第二个视图投影包含时间列和其他部分列，第二个视图group by聚合
        {"create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select name, time, level, content, count(level), window_start, window_end "
            "from table(hop(table v1, time, interval '3' seconds, interval '9' seconds)) "
            "group by window_start, window_end, level with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select window_start, first_name, window_end, level, first_content, count_level from v3 "
            "into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 3. 写入数据
    // 对应的时间列:           0        3        6        9        12
    int64_t insertLevels[] = {1, 2, 2, 3, 3, 3, 4, 4, 0, 4, 4, 1, 99, 100};
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (size_t i = 0; i < RD_ELEMENT_COUNT(insertLevels); i++) {
        RdThirtyOneSetData(dataObj, i, 0);
        RdThirtyOneSetDataLevel(dataObj, insertLevels[i]);
        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, dataObj);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 4. 读取校验数据, 有预期结果
    struct StreamAggColumns {
        int64_t windowStart;
        int64_t windowEnd;
        int64_t level;

        bool operator<(const StreamAggColumns &b) const {
            if (windowStart != b.windowStart) {
                return windowStart < b.windowStart;
            }
            if (windowEnd != b.windowEnd) {
                return windowEnd < b.windowEnd;
            }
            return level < b.level;
        }
    };
    struct StreamOutputCount {
        RdThirtyOneDataT *base;
        int64_t count;
    };

    auto genStreamOutput = [insertLevels]() {
        int64_t start = 0, width = 9, slide = 3, watermark = 1;
        std::map<StreamAggColumns, StreamOutputCount> resMap;
        for (int64_t s = start - width; s + width < start + RD_ELEMENT_COUNT(insertLevels) - watermark; s += slide) {
            for (int64_t j = s < 0 ? 0 : s; j < s + width && j < RD_ELEMENT_COUNT(insertLevels); j++) {
                StreamAggColumns key = {s, s + width, insertLevels[j]};
                if (resMap.find(key) == resMap.end()) {
                    RdThirtyOneDataT *base = RdThirtyOneAllocData();
                    if (base == NULL) {
                        RD_ERROR("Unable to alloc data.");
                        break;
                    }
                    RdThirtyOneSetData(base, j, 0);
                    RdThirtyOneSetDataId(base, s);
                    RdThirtyOneSetDataLevel(base, insertLevels[j]);
                    RdThirtyOneSetDataTime(base, s + width);
                    StreamOutputCount val = {base, 1};
                    resMap[key] = val;
                } else {
                    resMap[key].count++;
                }
            }
        }
        std::vector<StreamOutputCount> resVec;
        for (const auto &pair: resMap) {
            resVec.push_back(pair.second);
        }
        return resVec;
    };
    auto output = genStreamOutput();
    std::sort(output.begin(), output.end(), [](StreamOutputCount a, StreamOutputCount b) {
        if (a.base->time != b.base->time) {
            return a.base->time < b.base->time; // 升序排序
        }
        if (a.base->id != b.base->id) {
            return a.base->id < b.base->id;
        }
        if (a.base->level != b.base->level) {
            return a.base->level < b.base->level;
        }
        return a.count < b.count;
    });

    const char *qrySql = "select * from ts1 order by time, id, level;";
    ret = GmcExecDirect(shortStmt, qrySql, strlen(qrySql));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckResultRows(shortStmt, output.size());
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (const auto &entry: output) {
        ret = RdThirtyOneFetchTsData(shortStmt, dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdTsCheckPropertyInt64(shortStmt, 5, entry.count);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdThirtyOneCheckData(*entry.base, *dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 5. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;

    for (const auto &entry: output) {
        RdThirtyOneFreeData(entry.base);
    }
    RdFinishTest();
}

// @TestcaseName 创建多个视图，第二个视图投影包含时间列和其他部分列，第三个视图group by聚合 成功
TEST_F(t10_group_by_watermark, STREAM_031_142)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text, count integer) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '1' seconds strict);"},

        // 2. 创建多个视图，第二个视图投影包含时间列和其他部分列，第三个视图group by聚合
        {"create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select name, time, level, content from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select *, count(level) "
            "from table(hop(table v2, time, interval '3' seconds, interval '9' seconds)) "
            "group by window_start, window_end, level with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select window_start, first_name, window_end, level, first_content, count_level from v3 "
            "into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 3. 写入数据
    // 对应的时间列:           0        3        6        9        12
    int64_t insertLevels[] = {1, 2, 2, 3, 3, 3, 4, 4, 0, 4, 4, 1, 99, 100};
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (size_t i = 0; i < RD_ELEMENT_COUNT(insertLevels); i++) {
        RdThirtyOneSetData(dataObj, i, 0);
        RdThirtyOneSetDataLevel(dataObj, insertLevels[i]);
        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, dataObj);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 4. 读取校验数据, 有预期结果
    struct StreamAggColumns {
        int64_t windowStart;
        int64_t windowEnd;
        int64_t level;

        bool operator<(const StreamAggColumns &b) const {
            if (windowStart != b.windowStart) {
                return windowStart < b.windowStart;
            }
            if (windowEnd != b.windowEnd) {
                return windowEnd < b.windowEnd;
            }
            return level < b.level;
        }
    };
    struct StreamOutputCount {
        RdThirtyOneDataT *base;
        int64_t count;
    };

    auto genStreamOutput = [insertLevels]() {
        int64_t start = 0, width = 9, slide = 3, watermark = 1;
        std::map<StreamAggColumns, StreamOutputCount> resMap;
        for (int64_t s = start - width; s + width < start + RD_ELEMENT_COUNT(insertLevels) - watermark; s += slide) {
            for (int64_t j = s < 0 ? 0 : s; j < s + width && j < RD_ELEMENT_COUNT(insertLevels); j++) {
                StreamAggColumns key = {s, s + width, insertLevels[j]};
                if (resMap.find(key) == resMap.end()) {
                    RdThirtyOneDataT *base = RdThirtyOneAllocData();
                    if (base == NULL) {
                        RD_ERROR("Unable to alloc data.");
                        break;
                    }
                    RdThirtyOneSetData(base, j, 0);
                    RdThirtyOneSetDataId(base, s);
                    RdThirtyOneSetDataLevel(base, insertLevels[j]);
                    RdThirtyOneSetDataTime(base, s + width);
                    StreamOutputCount val = {base, 1};
                    resMap[key] = val;
                } else {
                    resMap[key].count++;
                }
            }
        }
        std::vector<StreamOutputCount> resVec;
        for (const auto &pair: resMap) {
            resVec.push_back(pair.second);
        }
        return resVec;
    };
    auto output = genStreamOutput();
    std::sort(output.begin(), output.end(), [](StreamOutputCount a, StreamOutputCount b) {
        if (a.base->time != b.base->time) {
            return a.base->time < b.base->time; // 升序排序
        }
        if (a.base->id != b.base->id) {
            return a.base->id < b.base->id;
        }
        if (a.base->level != b.base->level) {
            return a.base->level < b.base->level;
        }
        return a.count < b.count;
    });

    const char *qrySql = "select * from ts1 order by time, id, level, count;";
    ret = GmcExecDirect(shortStmt, qrySql, strlen(qrySql));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckResultRows(shortStmt, output.size());
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (const auto &entry: output) {
        ret = RdThirtyOneFetchTsData(shortStmt, dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdTsCheckPropertyInt64(shortStmt, 5, entry.count);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdThirtyOneCheckData(*entry.base, *dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 5. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;

    for (const auto &entry: output) {
        RdThirtyOneFreeData(entry.base);
    }
    RdFinishTest();
}

// @TestcaseName 创建多个视图，第二个视图投影仅包含时间列，第三个视图group by聚合 成功
TEST_F(t10_group_by_watermark, STREAM_031_143)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (window_start integer, window_end integer, time integer, count integer) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '1' seconds strict);"},

        // 2. 创建多个视图，第二个视图投影仅包含时间列，第三个视图group by聚合
        {"create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select time from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select *, count(time) "
            "from table(hop(table v2, time, interval '3' seconds, interval '9' seconds)) "
            "group by window_start, window_end, time with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select window_start, window_end, time, count_time from v3 "
            "into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 3. 写入数据
    // 对应的时间列:           0        3        6        9        12
    int64_t insertLevels[] = {1, 2, 2, 3, 3, 3, 4, 4, 0, 4, 4, 1, 99, 100};
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (size_t i = 0; i < RD_ELEMENT_COUNT(insertLevels); i++) {
        RdThirtyOneSetData(dataObj, i, 0);
        RdThirtyOneSetDataLevel(dataObj, insertLevels[i]);
        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, dataObj);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 4. 读取校验数据, 有预期结果
    struct StreamAggColumns {
        int64_t windowStart;
        int64_t windowEnd;
        int64_t time;

        bool operator<(const StreamAggColumns &b) const {
            if (windowStart != b.windowStart) {
                return windowStart < b.windowStart;
            }
            if (windowEnd != b.windowEnd) {
                return windowEnd < b.windowEnd;
            }
            return time < b.time;
        }
    };
    struct StreamOutputCount {
        RdThirtyOneDataT *base;
        int64_t window_start;
        int64_t window_end;
        int64_t count;
    };

    auto genStreamOutput = [insertLevels]() {
        int64_t start = 0, width = 9, slide = 3, watermark = 1;
        std::map<StreamAggColumns, StreamOutputCount> resMap;
        for (int64_t s = start - width; s + width < start + RD_ELEMENT_COUNT(insertLevels) - watermark; s += slide) {
            for (int64_t j = s < 0 ? 0 : s; j < s + width && j < RD_ELEMENT_COUNT(insertLevels); j++) {
                StreamAggColumns key = {s, s + width, j};
                if (resMap.find(key) == resMap.end()) {
                    RdThirtyOneDataT *base = RdThirtyOneAllocData();
                    if (base == NULL) {
                        RD_ERROR("Unable to alloc data.");
                        break;
                    }
                    RdThirtyOneSetData(base, j, 0);
                    StreamOutputCount val = {base, s, s + width, 1};
                    resMap[key] = val;
                } else {
                    resMap[key].count++;
                }
            }
        }
        std::vector<StreamOutputCount> resVec;
        for (const auto &pair: resMap) {
            resVec.push_back(pair.second);
        }
        return resVec;
    };
    auto output = genStreamOutput();
    std::sort(output.begin(), output.end(), [](StreamOutputCount a, StreamOutputCount b) {
        if (a.window_start != b.window_start) {
            return a.window_start < b.window_start;
        }
        if (a.window_end != b.window_end) {
            return a.window_end < b.window_end;
        }
        if (a.base->time != b.base->time) {
            return a.base->time < b.base->time; // 升序排序
        }
        return a.count < b.count;
    });

    const char *qrySql = "select * from ts1 order by window_start, window_end, time, count;";
    ret = GmcExecDirect(shortStmt, qrySql, strlen(qrySql));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckResultRows(shortStmt, output.size());
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (const auto &entry: output) {
        bool eof;
        ret = GmcFetch(shortStmt, &eof);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_BOOL(false, eof);

        ret = RdTsCheckPropertyInt64(shortStmt, 0, entry.window_start);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdTsCheckPropertyInt64(shortStmt, 1, entry.window_end);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdTsCheckPropertyInt64(shortStmt, 2, entry.base->time);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdTsCheckPropertyInt64(shortStmt, 3, entry.count);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 5. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;

    for (const auto &entry: output) {
        RdThirtyOneFreeData(entry.base);
    }
    RdFinishTest();
}

// @TestcaseName 创建多个视图，第二个视图投影不包含时间列，第三个视图group by聚合 报错，语义错误
TEST_F(t10_group_by_watermark, STREAM_031_144)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text, max integer) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '1' seconds strict);"},

        // 2. 创建多个视图，第二个视图投影不包含时间列，第三个视图group by聚合
        {"create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select id, name, level, content from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select *, count(id) "
            "from table(hop(table v2, time, interval '3' seconds, interval '9' seconds)) "
            "group by window_start, window_end, level with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_RESTRICT_VIOLATION},
        {"create stream sink s1 as select first_id, first_name, window_end, level, first_content, count_id from v3 "
            "into tsdb (ts1) with (batch_window_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 3. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v3;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v2;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdFinishTest();
}

// @TestcaseName 创建多个视图，第二个视图投影包含去重函数和时间列，第二个视图group by聚合 报错，语义错误
TEST_F(t10_group_by_watermark, STREAM_031_145)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text, max integer) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '1' seconds strict);"},

        // 2. 创建多个视图，第二个视图投影包含去重函数和时间列，第二个视图group by聚合
        {"create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select id, name, time, level, seq_distinct_count(time), count(id) "
            "from table(hop(table v1, time, interval '3' seconds, interval '9' seconds)) "
            "group by window_start, window_end, level with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_SEMANTIC_ERROR},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"create stream sink s1 as select first_id, first_name, window_end, level, first_content, count_id from v3 "
            "into tsdb (ts1) with (batch_window_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 3. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v3;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v2;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdFinishTest();
}

// @TestcaseName 创建多个视图，第二个视图投影包含去重函数和时间列，第三个视图group by聚合 成功
TEST_F(t10_group_by_watermark, STREAM_031_146)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text, count integer) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '1' seconds strict);"},

        // 2. 创建多个视图，第二个视图投影包含去重函数和时间列，第三个视图group by聚合
        {"create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select id, name, time, level, content, seq_distinct_count(time) from v1 "
            "with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select *, count(level) "
            "from table(hop(table v2, time, interval '3' seconds, interval '9' seconds)) "
            "group by window_start, window_end, level with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select first_id, first_name, window_end, level, first_content, count_level from v3 "
            "into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 3. 写入数据
    // 对应的时间列:           0        3        6        9        12
    int64_t insertLevels[] = {1, 2, 2, 3, 3, 3, 4, 4, 0, 4, 4, 1, 99, 100};
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (size_t i = 0; i < RD_ELEMENT_COUNT(insertLevels); i++) {
        RdThirtyOneSetData(dataObj, i, 0);
        RdThirtyOneSetDataLevel(dataObj, insertLevels[i]);
        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, dataObj);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 4. 读取校验数据, 有预期结果
    struct StreamAggColumns {
        int64_t windowStart;
        int64_t windowEnd;
        int64_t level;

        bool operator<(const StreamAggColumns &b) const {
            if (windowStart != b.windowStart) {
                return windowStart < b.windowStart;
            }
            if (windowEnd != b.windowEnd) {
                return windowEnd < b.windowEnd;
            }
            return level < b.level;
        }
    };
    struct StreamOutputCount {
        RdThirtyOneDataT *base;
        int64_t count;
    };

    auto genStreamOutput = [insertLevels]() {
        int64_t start = 0, width = 9, slide = 3, watermark = 1;
        std::map<StreamAggColumns, StreamOutputCount> resMap;
        // 最后一条数据缓存在流引擎中，未写入时序表
        for (int64_t s = start - width; s + width < start + RD_ELEMENT_COUNT(insertLevels) - 1 - watermark; s += slide) {
            for (int64_t j = s < 0 ? 0 : s; j < s + width && j < RD_ELEMENT_COUNT(insertLevels) - 1; j++) {
                StreamAggColumns key = {s, s + width, insertLevels[j]};
                if (resMap.find(key) == resMap.end()) {
                    RdThirtyOneDataT *base = RdThirtyOneAllocData();
                    if (base == NULL) {
                        RD_ERROR("Unable to alloc data.");
                        break;
                    }
                    RdThirtyOneSetData(base, j, 0);
                    RdThirtyOneSetDataLevel(base, insertLevels[j]);
                    RdThirtyOneSetDataTime(base, s + width);
                    StreamOutputCount val = {base, 1};
                    resMap[key] = val;
                } else {
                    resMap[key].count++;
                }
            }
        }
        std::vector<StreamOutputCount> resVec;
        for (const auto &pair: resMap) {
            resVec.push_back(pair.second);
        }
        return resVec;
    };
    auto output = genStreamOutput();
    std::sort(output.begin(), output.end(), [](StreamOutputCount a, StreamOutputCount b) {
        if (a.base->time != b.base->time) {
            return a.base->time < b.base->time; // 升序排序
        }
        if (a.base->id != b.base->id) {
            return a.base->id < b.base->id;
        }
        if (a.base->level != b.base->level) {
            return a.base->level < b.base->level;
        }
        return a.count < b.count;
    });

    const char *qrySql = "select * from ts1 order by time, id, level;";
    ret = GmcExecDirect(shortStmt, qrySql, strlen(qrySql));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckResultRows(shortStmt, output.size());
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 0; i < output.size(); i++) {
        auto entry = output.at(i);
        ret = RdThirtyOneFetchTsData(shortStmt, dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdTsCheckPropertyInt64(shortStmt, 5, entry.count);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdThirtyOneCheckData(*entry.base, *dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 5. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;

    for (const auto &entry: output) {
        RdThirtyOneFreeData(entry.base);
    }
    RdFinishTest();
}

// @TestcaseName 创建多个视图，第二个视图投影普通列和ref，ref下标是时间列，进行group by聚合 报错，语义错误
TEST_F(t10_group_by_watermark, STREAM_031_147)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text, max integer) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '1' seconds strict);"},

        {"create stream reference r1 (integer, integer);"},
        {"upsert into streamref r1 values (1, 10);"},
        {"upsert into streamref r1 values (2, 20);"},
        {"upsert into streamref r1 values (3, 30);"},
        {"upsert into streamref r1 values (4, 4);"},

        // 2. 创建多个视图，第二个视图投影普通列和ref，ref下标是时间列，进行group by聚合
        {"create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select id, name, ref['r1'][time], level, content "
            "from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select *, count(id) "
            "from table(hop(table v2, ref_r1_time, interval '3' seconds, interval '9' seconds)) "
            "group by window_start, window_end, level with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_RESTRICT_VIOLATION},
        {"create stream view v3 as select *, count(id) "
            "from table(hop(table v2, time, interval '3' seconds, interval '9' seconds)) "
            "group by window_start, window_end, level with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_RESTRICT_VIOLATION},
        {"create stream sink s1 as select first_id, first_name, window_end, level, first_content, count_id from v3 "
            "into tsdb (ts1) with (batch_window_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 3. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v3;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v2;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream reference r1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdFinishTest();
}

// @TestcaseName 创建多个视图，第二个视图投影时间列和ref，ref下标是时间列，进行group by聚合 成功
TEST_F(t10_group_by_watermark, STREAM_031_148)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text, count integer, "
            "ref integer) with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '1' seconds strict);"},

        {"create stream reference r1 (integer, integer);"},
        {"upsert into streamref r1 values (1, 10);"},
        {"upsert into streamref r1 values (2, 20);"},
        {"upsert into streamref r1 values (3, 20);"},
        {"upsert into streamref r1 values (4, 4);"},
        {"upsert into streamref r1 values (5, 5);"},
        {"upsert into streamref r1 values (6, 60);"},

        // 2. 创建多个视图，第二个视图投影时间列和ref，ref下标是时间列，进行group by聚合
        {"create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select id, name, time, level, content, ref['r1'][time] "
            "from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select *, count(level) "
            "from table(hop(table v2, time, interval '2' seconds, interval '3' seconds)) "
            "group by window_start, window_end, level with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select first_id, first_name, window_end, level, first_content, count_level, "
            "first_ref_r1_time from v3 "
            "into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 3. 写入数据
    // 对应的时间列:           0        3        6        9        12
    int64_t insertLevels[] = {1, 2, 2, 3, 3, 3, 4, 4, 0, 4, 4, 1, 99, 100};
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (size_t i = 0; i < RD_ELEMENT_COUNT(insertLevels); i++) {
        RdThirtyOneSetData(dataObj, i, 0);
        RdThirtyOneSetDataLevel(dataObj, insertLevels[i]);
        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, dataObj);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 4. 读取校验数据, 有预期结果
    struct StreamAggColumns {
        int64_t windowStart;
        int64_t windowEnd;
        int64_t level;

        bool operator<(const StreamAggColumns &b) const {
            if (windowStart != b.windowStart) {
                return windowStart < b.windowStart;
            }
            if (windowEnd != b.windowEnd) {
                return windowEnd < b.windowEnd;
            }
            return level < b.level;
        }
    };
    struct StreamOutputCount {
        RdThirtyOneDataT *base;
        int64_t count;
        int64_t ref;
    };

    auto genStreamOutput = [insertLevels]() {
        int64_t start = 0, width = 3, slide = 2, watermark = 1;
        std::map<StreamAggColumns, StreamOutputCount> resMap;
        std::map<int64_t, int64_t> ref = {{1, 10}, {2, 20}, {3, 20}, {4, 4}, {5, 5}, {6, 60}};
        // 注意第一层循环的条件是小于等于，有序输入的情况下，右边界小于等于最高水位的窗口中的数据均能关闭
        for (int64_t s = start - slide * 2; s + width <= start + (int64_t)ref.size() - watermark; s += slide) {
            for (int64_t j = s < 1 ? 1 : s; j < s + width && j < ref.size(); j++) {
                StreamAggColumns key = {s, s + width, insertLevels[j]};
                if (resMap.find(key) == resMap.end()) {
                    RdThirtyOneDataT *base = RdThirtyOneAllocData();
                    if (base == NULL) {
                        RD_ERROR("Unable to alloc data.");
                        break;
                    }
                    RdThirtyOneSetData(base, j, 0);
                    RdThirtyOneSetDataLevel(base, insertLevels[j]);
                    RdThirtyOneSetDataTime(base, s + width);
                    StreamOutputCount val = {base, 1, ref.at(j)};
                    resMap[key] = val;
                } else {
                    resMap[key].count++;
                }
            }
        }
        std::vector<StreamOutputCount> resVec;
        for (const auto &pair: resMap) {
            resVec.push_back(pair.second);
        }
        return resVec;
    };
    auto output = genStreamOutput();
    std::sort(output.begin(), output.end(), [](StreamOutputCount a, StreamOutputCount b) {
        if (a.base->time != b.base->time) {
            return a.base->time < b.base->time; // 升序排序
        }
        if (a.base->id != b.base->id) {
            return a.base->id < b.base->id;
        }
        if (a.base->level != b.base->level) {
            return a.base->level < b.base->level;
        }
        return a.count < b.count;
    });

    const char *qrySql = "select * from ts1 order by time, id, level;";
    ret = GmcExecDirect(shortStmt, qrySql, strlen(qrySql));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckResultRows(shortStmt, output.size());
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (const auto &entry: output) {
        ret = RdThirtyOneFetchTsData(shortStmt, dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdTsCheckPropertyInt64(shortStmt, 5, entry.count);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdThirtyOneCheckData(*entry.base, *dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 5. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;

    for (const auto &entry: output) {
        RdThirtyOneFreeData(entry.base);
    }
    RdFinishTest();
}

// @TestcaseName 创建多个视图，第二个视图投影时间列和ref，ref下标是普通列，进行group by聚合 成功
TEST_F(t10_group_by_watermark, STREAM_031_149)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text, count integer, "
            "ref integer) with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '1' seconds strict);"},

        {"create stream reference r1 (integer, integer);"},
        {"upsert into streamref r1 values (1, 10);"},
        {"upsert into streamref r1 values (2, 20);"},
        {"upsert into streamref r1 values (3, 20);"},
        {"upsert into streamref r1 values (4, 4);"},
        {"upsert into streamref r1 values (5, 5);"},
        {"upsert into streamref r1 values (6, 60);"},

        // 2. 创建多个视图，第二个视图投影时间列和ref，ref下标是普通列，进行group by聚合
        {"create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select id, name, time, level, content, ref['r1'][id] "
            "from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select *, count(level) "
            "from table(hop(table v2, time, interval '2' seconds, interval '3' seconds)) "
            "group by window_start, window_end, level with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select first_id, first_name, window_end, level, first_content, count_level, "
            "first_ref_r1_id from v3 "
            "into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 3. 写入数据
    // 对应的时间列:           0        3        6        9        12
    int64_t insertLevels[] = {1, 2, 2, 3, 3, 3, 4, 4, 0, 4, 4, 1, 99, 100};
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (size_t i = 0; i < RD_ELEMENT_COUNT(insertLevels); i++) {
        RdThirtyOneSetData(dataObj, i, 0);
        RdThirtyOneSetDataLevel(dataObj, insertLevels[i]);
        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, dataObj);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 4. 读取校验数据, 有预期结果
    struct StreamAggColumns {
        int64_t windowStart;
        int64_t windowEnd;
        int64_t level;

        bool operator<(const StreamAggColumns &b) const {
            if (windowStart != b.windowStart) {
                return windowStart < b.windowStart;
            }
            if (windowEnd != b.windowEnd) {
                return windowEnd < b.windowEnd;
            }
            return level < b.level;
        }
    };
    struct StreamOutputCount {
        RdThirtyOneDataT *base;
        int64_t count;
        int64_t ref;
    };

    auto genStreamOutput = [insertLevels]() {
        int64_t start = 0, width = 3, slide = 2, watermark = 1;
        std::map<StreamAggColumns, StreamOutputCount> resMap;
        std::map<int64_t, int64_t> ref = {{1, 10}, {2, 20}, {3, 20}, {4, 4}, {5, 5}, {6, 60}};
        // 注意第一层循环的条件是小于等于，有序输入的情况下，右边界小于等于最高水位的窗口中的数据均能关闭
        for (int64_t s = start - slide * 2; s + width <= start + (int64_t)ref.size() - watermark; s += slide) {
            for (int64_t j = s < 1 ? 1 : s; j < s + width && j < ref.size(); j++) {
                StreamAggColumns key = {s, s + width, insertLevels[j]};
                if (resMap.find(key) == resMap.end()) {
                    RdThirtyOneDataT *base = RdThirtyOneAllocData();
                    if (base == NULL) {
                        RD_ERROR("Unable to alloc data.");
                        break;
                    }
                    RdThirtyOneSetData(base, j, 0);
                    RdThirtyOneSetDataLevel(base, insertLevels[j]);
                    RdThirtyOneSetDataTime(base, s + width);
                    StreamOutputCount val = {base, 1, ref.at(j)};
                    resMap[key] = val;
                } else {
                    resMap[key].count++;
                }
            }
        }
        std::vector<StreamOutputCount> resVec;
        for (const auto &pair: resMap) {
            resVec.push_back(pair.second);
        }
        return resVec;
    };
    auto output = genStreamOutput();
    std::sort(output.begin(), output.end(), [](StreamOutputCount a, StreamOutputCount b) {
        if (a.base->time != b.base->time) {
            return a.base->time < b.base->time; // 升序排序
        }
        if (a.base->id != b.base->id) {
            return a.base->id < b.base->id;
        }
        if (a.base->level != b.base->level) {
            return a.base->level < b.base->level;
        }
        return a.count < b.count;
    });

    const char *qrySql = "select * from ts1 order by time, id, level;";
    ret = GmcExecDirect(shortStmt, qrySql, strlen(qrySql));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckResultRows(shortStmt, output.size());
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (const auto &entry: output) {
        ret = RdThirtyOneFetchTsData(shortStmt, dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdTsCheckPropertyInt64(shortStmt, 5, entry.count);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdThirtyOneCheckData(*entry.base, *dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 5. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;

    for (const auto &entry: output) {
        RdThirtyOneFreeData(entry.base);
    }
    RdFinishTest();
}
