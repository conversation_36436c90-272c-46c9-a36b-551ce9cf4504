/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 基于滑动窗口的Group By复杂场景测试
 * Author: guopanpan
 * Create: 2024-12-24
 */
#include <iostream>
#include <map>
#include <vector>
#include <algorithm>
#include "gtest/gtest.h"
#include "gmc_sql.h"
#include "rd_feature_stream.h"
#include "rd_feature_ts.h"
#include "rd_stream_thirty_one.h"

class t08_group_by_complex : public testing::Test {
public:
    static GmcConnT *longConn;
    static GmcStmtT *longStmt;
    GmcConnT *shortConn = NULL;
    GmcStmtT *shortStmt = NULL;
    GmcConnT *asyncConn = NULL;
    GmcStmtT *asyncStmt = NULL;

    RdVertexLabelT *vertexLabel = NULL;
    RdThirtyOneDataT *dataObj = NULL;
    RdThirtyOneDataT *dataQry = NULL;

    bool hasFinishTest = false;
    void RdFinishTest();

    static void SetUpTestCase();
    static void TearDownTestCase();
    virtual void SetUp();
    virtual void TearDown();
};

GmcConnT *t08_group_by_complex::longConn = NULL;
GmcStmtT *t08_group_by_complex::longStmt = NULL;

void t08_group_by_complex::SetUpTestCase()
{
    int32_t ret;
    ret = RdStreamEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdGmcConnect(&longConn, &longStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdCreateEpollThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdLogSetLevel4Stdout(RD_LOG_LEVEL_INFO);
}

void t08_group_by_complex::TearDownTestCase()
{
    int32_t ret;
    ret = RdGmcDisconnect(longConn, longStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdStreamEnvClean();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdCloseEpollThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void t08_group_by_complex::SetUp()
{
    int32_t ret;
    hasFinishTest = false;
    ret = RdGmcConnect(&shortConn, &shortStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdGmcConnect(&asyncConn, &asyncStmt, GMC_CONN_TYPE_ASYNC);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    dataObj = RdThirtyOneAllocData();
    AW_MACRO_ASSERT_NOTNULL(dataObj);
    dataQry = RdThirtyOneAllocData();
    AW_MACRO_ASSERT_NOTNULL(dataQry);
}

void t08_group_by_complex::TearDown()
{
    int32_t ret;
    if (!hasFinishTest) {
        RdStreamExecDescT clean[] = {
            {"drop stream sink s1;"},
            {"drop stream view v3;"},
            {"drop stream view v2;"},
            {"drop stream view v1;"},
            {"drop stream reference r1;"},
            {"drop stream table t2;"},
            {"drop stream table t1;"},
            {"drop table ts1;"},
        };
        ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
        EXPECT_EQ(GMERR_OK, ret);
        RdStreamFreeTableSchema(vertexLabel);
        vertexLabel = NULL;
    }

    ret = RdGmcDisconnect(shortConn, shortStmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = RdGmcDisconnect(asyncConn, asyncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    RdThirtyOneFreeData(dataObj);
    dataObj = NULL;
    RdThirtyOneFreeData(dataQry);
    dataQry = NULL;
}

void t08_group_by_complex::RdFinishTest()
{
    hasFinishTest = true;
}

// @TestSuite 交互场景1 - where
// @TestcaseName 在group by前进行where过滤，在where子句中引用window_start和window_end 成功
TEST_F(t08_group_by_complex, STREAM_031_107)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text, count integer) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '1' seconds strict);"},

        // 2. 在group by前进行where过滤，在where子句中引用window_start和window_end
        {"create stream view v1 as select *, count(level) "
            "from table(hop(table t1, time, interval '3' seconds, interval '9' seconds)) "
            "where window_start > 0 and window_end <= 9 "
            "group by window_start, window_end, level "
            "with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select first_id, first_name, window_end, level, first_content, count_level from v3 "
            "into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 3. 写入数据
    // 对应的时间列:           0        3        6        9        12
    int64_t insertLevels[] = {1, 2, 2, 3, 3, 3, 4, 4, 0, 4, 4, 1, 99, 100};
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (size_t i = 0; i < RD_ELEMENT_COUNT(insertLevels); i++) {
        RdThirtyOneSetData(dataObj, i, 0);
        RdThirtyOneSetDataLevel(dataObj, insertLevels[i]);
        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, dataObj);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 4. 读取校验数据, 有预期结果
    struct StreamAggColumns {
        int64_t windowStart;
        int64_t windowEnd;
        int64_t level;

        bool operator<(const StreamAggColumns &b) const {
            if (windowStart != b.windowStart) {
                return windowStart < b.windowStart;
            }
            if (windowEnd != b.windowEnd) {
                return windowEnd < b.windowEnd;
            }
            return level < b.level;
        }
    };
    struct StreamOutputCount {
        RdThirtyOneDataT *base;
        int64_t count;
    };

    auto genStreamOutput = [insertLevels]() {
        int64_t start = 0, width = 9, slide = 3, watermark = 1;
        std::map<StreamAggColumns, StreamOutputCount> resMap;
        for (int64_t s = start - width; s + width < start + RD_ELEMENT_COUNT(insertLevels) - watermark; s += slide) {
            if (!(s > 0 && s + width <= 9)) {
                continue;
            }
            for (int64_t j = s < 0 ? 0 : s; j < s + width && j < RD_ELEMENT_COUNT(insertLevels); j++) {
                StreamAggColumns key = {s, s + width, insertLevels[j]};
                if (resMap.find(key) == resMap.end()) {
                    RdThirtyOneDataT *base = RdThirtyOneAllocData();
                    if (base == NULL) {
                        RD_ERROR("Unable to alloc data.");
                        break;
                    }
                    RdThirtyOneSetData(base, j, 0);
                    RdThirtyOneSetDataLevel(base, insertLevels[j]);
                    RdThirtyOneSetDataTime(base, s + width);
                    StreamOutputCount val = {base, 1};
                    resMap[key] = val;
                } else {
                    resMap[key].count++;
                }
            }
        }
        std::vector<StreamOutputCount> resVec;
        for (const auto &pair: resMap) {
            resVec.push_back(pair.second);
        }
        return resVec;
    };
    auto output = genStreamOutput();
    std::sort(output.begin(), output.end(), [](StreamOutputCount a, StreamOutputCount b) {
        if (a.base->time != b.base->time) {
            return a.base->time < b.base->time; // 升序排序
        }
        if (a.base->id != b.base->id) {
            return a.base->id < b.base->id;
        }
        if (a.base->level != b.base->level) {
            return a.base->level < b.base->level;
        }
        return a.count < b.count;
    });

    const char *qrySql = "select * from ts1 order by time, id, level;";
    ret = GmcExecDirect(shortStmt, qrySql, strlen(qrySql));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckResultRows(shortStmt, output.size());
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (const auto &entry: output) {
        ret = RdThirtyOneFetchTsData(shortStmt, dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdTsCheckPropertyInt64(shortStmt, 5, entry.count);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdThirtyOneCheckData(*entry.base, *dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 5. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;

    for (const auto &entry: output) {
        RdThirtyOneFreeData(entry.base);
    }
    RdFinishTest();
}

// @TestcaseName 在group by前进行where过滤，在where子句中引用被聚合的列 成功
TEST_F(t08_group_by_complex, STREAM_031_108)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text, count integer) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '1' seconds strict);"},

        // 2. 在group by前进行where过滤，在where子句中引用被聚合的列
        {"create stream view v1 as select *, count(level) "
            "from table(hop(table t1, time, interval '3' seconds, interval '9' seconds)) "
            "where level >= 1 and level < 4 "
            "group by window_start, window_end, level "
            "with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select first_id, first_name, window_end, level, first_content, count_level from v3 "
            "into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 3. 写入数据
    // 对应的时间列:           0        3        6        9        12
    int64_t insertLevels[] = {1, 2, 2, 3, 3, 3, 4, 4, 0, 4, 4, 1, 99, 100};
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (size_t i = 0; i < RD_ELEMENT_COUNT(insertLevels); i++) {
        RdThirtyOneSetData(dataObj, i, 0);
        RdThirtyOneSetDataLevel(dataObj, insertLevels[i]);
        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, dataObj);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 4. 读取校验数据, 有预期结果
    struct StreamAggColumns {
        int64_t windowStart;
        int64_t windowEnd;
        int64_t level;

        bool operator<(const StreamAggColumns &b) const {
            if (windowStart != b.windowStart) {
                return windowStart < b.windowStart;
            }
            if (windowEnd != b.windowEnd) {
                return windowEnd < b.windowEnd;
            }
            return level < b.level;
        }
    };
    struct StreamOutputCount {
        RdThirtyOneDataT *base;
        int64_t count;
    };

    auto genStreamOutput = [insertLevels]() {
        int64_t start = 0, width = 9, slide = 3, watermark = 1;
        std::map<StreamAggColumns, StreamOutputCount> resMap;
        for (int64_t s = start - width; s + width < start + RD_ELEMENT_COUNT(insertLevels) - watermark; s += slide) {
            for (int64_t j = s < 0 ? 0 : s; j < s + width && j < RD_ELEMENT_COUNT(insertLevels); j++) {
                if (!(insertLevels[j] >= 1 && insertLevels[j] < 4)) {
                    continue;
                }
                StreamAggColumns key = {s, s + width, insertLevels[j]};
                if (resMap.find(key) == resMap.end()) {
                    RdThirtyOneDataT *base = RdThirtyOneAllocData();
                    if (base == NULL) {
                        RD_ERROR("Unable to alloc data.");
                        break;
                    }
                    RdThirtyOneSetData(base, j, 0);
                    RdThirtyOneSetDataLevel(base, insertLevels[j]);
                    RdThirtyOneSetDataTime(base, s + width);
                    StreamOutputCount val = {base, 1};
                    resMap[key] = val;
                } else {
                    resMap[key].count++;
                }
            }
        }
        std::vector<StreamOutputCount> resVec;
        for (const auto &pair: resMap) {
            resVec.push_back(pair.second);
        }
        return resVec;
    };
    auto output = genStreamOutput();
    std::sort(output.begin(), output.end(), [](StreamOutputCount a, StreamOutputCount b) {
        if (a.base->time != b.base->time) {
            return a.base->time < b.base->time; // 升序排序
        }
        if (a.base->id != b.base->id) {
            return a.base->id < b.base->id;
        }
        if (a.base->level != b.base->level) {
            return a.base->level < b.base->level;
        }
        return a.count < b.count;
    });

    const char *qrySql = "select * from ts1 order by time, id, level;";
    ret = GmcExecDirect(shortStmt, qrySql, strlen(qrySql));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckResultRows(shortStmt, output.size());
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (const auto &entry: output) {
        ret = RdThirtyOneFetchTsData(shortStmt, dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdTsCheckPropertyInt64(shortStmt, 5, entry.count);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdThirtyOneCheckData(*entry.base, *dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 5. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;

    for (const auto &entry: output) {
        RdThirtyOneFreeData(entry.base);
    }
    RdFinishTest();
}

// @TestcaseName 在group by前进行where过滤，在where子句中使用聚合函数 报错，语法错误
TEST_F(t08_group_by_complex, STREAM_031_109)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text, max integer) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '1' seconds strict);"},

        // 2. 在group by前进行where过滤，在where子句中使用聚合函数
        {"create stream view v1 as select *, count(id) "
            "from table(hop(table t1, time, interval '3' seconds, interval '9' seconds)) "
            "group by window_start, window_end, level where count(id) < 10 "
            "with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"create stream sink s1 as select first_id, first_name, window_end, level, first_content, count_id from v3 "
            "into tsdb (ts1) with (batch_window_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 3. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v3;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v2;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdFinishTest();
}

// @TestcaseName 在group by前进行where过滤，在where子句中使用复杂过滤表达式 成功
TEST_F(t08_group_by_complex, STREAM_031_110)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text, count integer) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '1' seconds strict);"},

        {"create stream reference r1 (integer, integer);"},
        {"upsert into streamref r1 values (1, 10);"},
        {"upsert into streamref r1 values (2, 20);"},
        {"upsert into streamref r1 values (3, 20);"},
        {"upsert into streamref r1 values (4, 4);"},

        // 2. 在group by前进行where过滤，在where子句中使用复杂过滤表达式
        {"create stream view v1 as select *, count(level) "
            "from table(hop(table t1, time, interval '3' seconds, interval '9' seconds)) "
            "where id > 0 and name like 'info%' and time in (0, 1, 2, 4, 5, 7, 8, 10) "
            "or ref['r1'][level] >= 10 and window_start >= 0 "
            "group by window_start, window_end, level "
            "with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select first_id, first_name, window_end, level, first_content, count_level from v3 "
            "into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 3. 写入数据
    // 对应的时间列:           0        3        6        9        12
    int64_t insertLevels[] = {1, 2, 2, 3, 3, 3, 4, 4, 0, 4, 4, 1, 99, 100};
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (size_t i = 0; i < RD_ELEMENT_COUNT(insertLevels); i++) {
        RdThirtyOneSetData(dataObj, i, 0);
        RdThirtyOneSetDataLevel(dataObj, insertLevels[i]);
        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, dataObj);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 4. 读取校验数据, 有预期结果
    struct StreamAggColumns {
        int64_t windowStart;
        int64_t windowEnd;
        int64_t level;

        bool operator<(const StreamAggColumns &b) const {
            if (windowStart != b.windowStart) {
                return windowStart < b.windowStart;
            }
            if (windowEnd != b.windowEnd) {
                return windowEnd < b.windowEnd;
            }
            return level < b.level;
        }
    };
    struct StreamOutputCount {
        RdThirtyOneDataT *base;
        int64_t count;
    };

    auto genStreamOutput = [insertLevels]() {
        int64_t start = 0, width = 9, slide = 3, watermark = 1;
        std::map<StreamAggColumns, StreamOutputCount> resMap;
        std::map<int64_t, int64_t> ref = {{1, 10}, {2, 20}, {3, 20}, {4, 4}};
        for (int64_t s = start - width; s + width < start + RD_ELEMENT_COUNT(insertLevels) - watermark; s += slide) {
            for (int64_t j = s < 0 ? 0 : s; j < s + width && j < RD_ELEMENT_COUNT(insertLevels); j++) {
                bool refExist = ref.find(insertLevels[j]) != ref.end();
                bool cond1 = j > 0 && (j == 0 || j == 1 || j == 2 || j == 4 || j == 5 || j == 7 || j == 8 || j == 10);
                if (!cond1 && !refExist) {
                    continue;
                }
                bool cond2 = false;
                if (refExist) {
                    cond2 = ref.at(insertLevels[j]) >= 10 && s >= 0;
                }
                if (!(cond1 or cond2)) {
                    continue;
                }

                StreamAggColumns key = {s, s + width, insertLevels[j]};
                if (resMap.find(key) == resMap.end()) {
                    RdThirtyOneDataT *base = RdThirtyOneAllocData();
                    if (base == NULL) {
                        RD_ERROR("Unable to alloc data.");
                        break;
                    }
                    RdThirtyOneSetData(base, j, 0);
                    RdThirtyOneSetDataLevel(base, insertLevels[j]);
                    RdThirtyOneSetDataTime(base, s + width);
                    StreamOutputCount val = {base, 1};
                    resMap[key] = val;
                } else {
                    resMap[key].count++;
                }
            }
        }
        std::vector<StreamOutputCount> resVec;
        for (const auto &pair: resMap) {
            resVec.push_back(pair.second);
        }
        return resVec;
    };
    auto output = genStreamOutput();
    std::sort(output.begin(), output.end(), [](StreamOutputCount a, StreamOutputCount b) {
        if (a.base->time != b.base->time) {
            return a.base->time < b.base->time; // 升序排序
        }
        if (a.base->id != b.base->id) {
            return a.base->id < b.base->id;
        }
        if (a.base->level != b.base->level) {
            return a.base->level < b.base->level;
        }
        return a.count < b.count;
    });

    const char *qrySql = "select * from ts1 order by time, id, level;";
    ret = GmcExecDirect(shortStmt, qrySql, strlen(qrySql));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckResultRows(shortStmt, output.size());
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (const auto &entry: output) {
        ret = RdThirtyOneFetchTsData(shortStmt, dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdTsCheckPropertyInt64(shortStmt, 5, entry.count);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdThirtyOneCheckData(*entry.base, *dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 5. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream reference r1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;

    for (const auto &entry: output) {
        RdThirtyOneFreeData(entry.base);
    }
    RdFinishTest();
}

// @TestSuite 交互场景2 - 格式化
// @TestcaseName 创建窗口视图，同时使用group by和format函数 成功
TEST_F(t08_group_by_complex, STREAM_031_111)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content char(120), count integer) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '1' seconds strict);"},
        {"create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},

        // HISTORY 2025-01-13 不支持在投影列引用未被聚合的列
        {"create stream view v3 as select id, name, time, level, "
            "format('format content %d %s', id, content), count(level) "
            "window_start, window_end "
            "from table(hop(table v2, time, interval '3' seconds, interval '9' seconds)) "
            "group by window_start, window_end, level "
            "with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_FEATURE_NOT_SUPPORTED},

        {"create stream view v3 as select id, name, time, level, "
            "format('format content %ld', level), count(level), "
            "window_start, window_end "
            "from table(hop(table v2, time, interval '3' seconds, interval '9' seconds)) "
            "group by window_start, window_end, level "
            "with (tuple_buffer_size = 1);"},

        {"create stream sink s1 as select first_id, first_name, window_end, level, format_level, count_level "
            "from v3 into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 3. 写入数据
    // 对应的时间列:           0        3        6        9        12
    int64_t insertLevels[] = {1, 2, 2, 3, 3, 3, 4, 4, 0, 4, 4, 1, 99, 100};
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (size_t i = 0; i < RD_ELEMENT_COUNT(insertLevels); i++) {
        RdThirtyOneSetData(dataObj, i, 0);
        RdThirtyOneSetDataLevel(dataObj, insertLevels[i]);
        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, dataObj);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 4. 读取校验数据, 有预期结果
    struct StreamAggColumns {
        int64_t windowStart;
        int64_t windowEnd;
        int64_t level;

        bool operator<(const StreamAggColumns &b) const {
            if (windowStart != b.windowStart) {
                return windowStart < b.windowStart;
            }
            if (windowEnd != b.windowEnd) {
                return windowEnd < b.windowEnd;
            }
            return level < b.level;
        }
    };
    struct StreamOutputCount {
        RdThirtyOneDataT *base;
        int64_t count;
    };

    auto genStreamOutput = [insertLevels]() {
        int64_t start = 0, width = 9, slide = 3, watermark = 1;
        std::map<StreamAggColumns, StreamOutputCount> resMap;
        for (int64_t s = start - width; s + width < start + RD_ELEMENT_COUNT(insertLevels) - watermark; s += slide) {
            for (int64_t j = s < 0 ? 0 : s; j < s + width && j < RD_ELEMENT_COUNT(insertLevels); j++) {
                StreamAggColumns key = {s, s + width, insertLevels[j]};
                if (resMap.find(key) == resMap.end()) {
                    RdThirtyOneDataT *base = RdThirtyOneAllocData();
                    if (base == NULL) {
                        RD_ERROR("Unable to alloc data.");
                        break;
                    }
                    RdThirtyOneSetData(base, j, 0);
                    RdThirtyOneSetDataLevel(base, insertLevels[j]);
                    RdThirtyOneSetDataTime(base, s + width);
                    RdThirtyOneSetDataContent(base, "format content %d", insertLevels[j]);
                    StreamOutputCount val = {base, 1};
                    resMap[key] = val;
                } else {
                    resMap[key].count++;
                }
            }
        }
        std::vector<StreamOutputCount> resVec;
        for (const auto &pair: resMap) {
            resVec.push_back(pair.second);
        }
        return resVec;
    };
    auto output = genStreamOutput();
    std::sort(output.begin(), output.end(), [](StreamOutputCount a, StreamOutputCount b) {
        if (a.base->time != b.base->time) {
            return a.base->time < b.base->time; // 升序排序
        }
        if (a.base->id != b.base->id) {
            return a.base->id < b.base->id;
        }
        if (a.base->level != b.base->level) {
            return a.base->level < b.base->level;
        }
        return a.count < b.count;
    });

    const char *qrySql = "select * from ts1 order by time, id, level;";
    ret = GmcExecDirect(shortStmt, qrySql, strlen(qrySql));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckResultRows(shortStmt, output.size());
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (const auto &entry: output) {
        ret = RdThirtyOneFetchTsData(shortStmt, dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdTsCheckPropertyInt64(shortStmt, 5, entry.count);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdThirtyOneCheckData(*entry.base, *dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 5. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;

    for (const auto &entry: output) {
        RdThirtyOneFreeData(entry.base);
    }
    RdFinishTest();
}

// @TestcaseName 创建窗口sink，同时使用group by和format函数 成功
TEST_F(t08_group_by_complex, STREAM_031_112)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content char(120), count integer) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '1' seconds strict);"},
        {"create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},

        // HISTORY 2025-01-13 不支持在投影列引用未被聚合的列
        // 2. 创建窗口sink，同时使用group by和format函数
        {"create stream sink s1 as select id, name, window_end, level, "
            "format('format content %d %s', id, content), count(level) "
            "from table(hop(table t1, time, interval '3' seconds, interval '9' seconds)) "
            "group by window_start, window_end, level "
            "into tsdb (ts1) with (batch_window_size = 1);",
            GMC_MODEL_STREAM, GMERR_FEATURE_NOT_SUPPORTED},
        {"create stream sink s1 as select id, name, window_end, level, "
            "format('format content %d', level), count(level) "
            "from table(hop(table t1, time, interval '3' seconds, interval '9' seconds)) "
            "group by window_start, window_end, level "
            "into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 3. 写入数据
    // 对应的时间列:           0        3        6        9        12
    int64_t insertLevels[] = {1, 2, 2, 3, 3, 3, 4, 4, 0, 4, 4, 1, 99, 100};
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (size_t i = 0; i < RD_ELEMENT_COUNT(insertLevels); i++) {
        RdThirtyOneSetData(dataObj, i, 0);
        RdThirtyOneSetDataLevel(dataObj, insertLevels[i]);
        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, dataObj);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 4. 读取校验数据, 有预期结果
    struct StreamAggColumns {
        int64_t windowStart;
        int64_t windowEnd;
        int64_t level;

        bool operator<(const StreamAggColumns &b) const {
            if (windowStart != b.windowStart) {
                return windowStart < b.windowStart;
            }
            if (windowEnd != b.windowEnd) {
                return windowEnd < b.windowEnd;
            }
            return level < b.level;
        }
    };
    struct StreamOutputCount {
        RdThirtyOneDataT *base;
        int64_t count;
    };

    auto genStreamOutput = [insertLevels]() {
        int64_t start = 0, width = 9, slide = 3, watermark = 1;
        std::map<StreamAggColumns, StreamOutputCount> resMap;
        for (int64_t s = start - width; s + width < start + RD_ELEMENT_COUNT(insertLevels) - watermark; s += slide) {
            for (int64_t j = s < 0 ? 0 : s; j < s + width && j < RD_ELEMENT_COUNT(insertLevels); j++) {
                StreamAggColumns key = {s, s + width, insertLevels[j]};
                if (resMap.find(key) == resMap.end()) {
                    RdThirtyOneDataT *base = RdThirtyOneAllocData();
                    if (base == NULL) {
                        RD_ERROR("Unable to alloc data.");
                        break;
                    }
                    RdThirtyOneSetData(base, j, 0);
                    RdThirtyOneSetDataLevel(base, insertLevels[j]);
                    RdThirtyOneSetDataTime(base, s + width);
                    RdThirtyOneSetDataContent(base, "format content %d", insertLevels[j]);
                    StreamOutputCount val = {base, 1};
                    resMap[key] = val;
                } else {
                    resMap[key].count++;
                }
            }
        }
        std::vector<StreamOutputCount> resVec;
        for (const auto &pair: resMap) {
            resVec.push_back(pair.second);
        }
        return resVec;
    };
    auto output = genStreamOutput();
    std::sort(output.begin(), output.end(), [](StreamOutputCount a, StreamOutputCount b) {
        if (a.base->time != b.base->time) {
            return a.base->time < b.base->time; // 升序排序
        }
        if (a.base->id != b.base->id) {
            return a.base->id < b.base->id;
        }
        if (a.base->level != b.base->level) {
            return a.base->level < b.base->level;
        }
        return a.count < b.count;
    });

    const char *qrySql = "select * from ts1 order by time, id, level;";
    ret = GmcExecDirect(shortStmt, qrySql, strlen(qrySql));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckResultRows(shortStmt, output.size());
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (const auto &entry: output) {
        ret = RdThirtyOneFetchTsData(shortStmt, dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdTsCheckPropertyInt64(shortStmt, 5, entry.count);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdThirtyOneCheckData(*entry.base, *dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 5. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;

    for (const auto &entry: output) {
        RdThirtyOneFreeData(entry.base);
    }
    RdFinishTest();
}

// @TestcaseName group by后指定字符串列，写入多条空值 成功，多条空值聚合成一组
TEST_F(t08_group_by_complex, STREAM_031_113)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text, count integer) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '1' seconds strict);"},

        // 2. group by后指定字符串列，写入多条空值
        {"create stream view v1 as select *, count(level) "
            "from table(hop(table t1, time, interval '3' seconds, interval '9' seconds)) "
            "group by window_start, window_end, name, content with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select first_id, name, window_end, first_level, content, count_level from v3 "
            "into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 3. 写入数据
    // 对应的时间列:           0        3        6        9        12
    int64_t insertLevels[] = {1, 2, 2, 3, 3, 3, 4, 4, 0, 4, 4, 1, 99, 100};
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    bool nullInfo[] = {false, true, false, false, true, true};
    for (size_t i = 0; i < RD_ELEMENT_COUNT(insertLevels); i++) {
        RdThirtyOneSetData(dataObj, i, 0);
        RdThirtyOneSetDataLevel(dataObj, insertLevels[i]);
        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, dataObj, nullInfo);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // HISTORY 2025-01-08 不支持写入空值
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    }

    // 4. 读取校验数据, 有预期结果
    ret = RdTsCheckRecordCount(shortStmt, "ts1", 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 5. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;
    RdFinishTest();
}

// @TestcaseName group by后指定整形列，写入多条空值 成功，多条空值聚合成一组
TEST_F(t08_group_by_complex, STREAM_031_114)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text, count integer) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '1' seconds strict);"},

        // 2. group by后指定整形列，写入多条空值
        {"create stream view v1 as select *, count(level) "
            "from table(hop(table t1, time, interval '3' seconds, interval '9' seconds)) "
            "group by window_start, window_end, level with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select first_id, first_name, window_end, level, first_content, count_level from v3 "
            "into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 3. 写入数据
    // 对应的时间列:           0        3        6        9        12
    int64_t insertLevels[] = {1, 2, 2, 3, 3, 3, 4, 4, 0, 4, 4, 1, 99, 100};
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    bool nullInfo[] = {false, false, false, false, true, true};
    for (size_t i = 0; i < RD_ELEMENT_COUNT(insertLevels); i++) {
        RdThirtyOneSetData(dataObj, i, 0);
        RdThirtyOneSetDataLevel(dataObj, insertLevels[i]);
        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, dataObj, nullInfo);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // HISTORY 2025-01-08 不支持写入空值
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    }

    // 4. 读取校验数据, 有预期结果
    ret = RdTsCheckRecordCount(shortStmt, "ts1", 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 5. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;
    RdFinishTest();
}

// @TestSuite 交互场景3 - 窗口
// @TestcaseName 创建步长大于宽度的滑动窗口，通过group by聚合计算，写入数据 成功，部分数据可能丢失
TEST_F(t08_group_by_complex, STREAM_031_115)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text, count integer) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '1' seconds strict);"},

        // 2. 创建步长大于宽度的滑动窗口，通过group by聚合计算，写入数据
        {"create stream view v1 as select *, count(level) "
            "from table(hop(table t1, time, interval '4' seconds, interval '3' seconds)) "
            "group by window_start, window_end, level with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select first_id, first_name, window_end, level, first_content, count_level from v3 "
            "into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 3. 写入数据
    // 对应的时间列:           0        3        6        9        12
    int64_t insertLevels[] = {1, 2, 2, 3, 3, 3, 4, 4, 0, 4, 4, 1, 99, 100};
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (size_t i = 0; i < RD_ELEMENT_COUNT(insertLevels); i++) {
        RdThirtyOneSetData(dataObj, i, 0);
        RdThirtyOneSetDataLevel(dataObj, insertLevels[i]);
        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, dataObj);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 4. 读取校验数据, 有预期结果
    struct StreamAggColumns {
        int64_t windowStart;
        int64_t windowEnd;
        int64_t level;

        bool operator<(const StreamAggColumns &b) const {
            if (windowStart != b.windowStart) {
                return windowStart < b.windowStart;
            }
            if (windowEnd != b.windowEnd) {
                return windowEnd < b.windowEnd;
            }
            return level < b.level;
        }
    };
    struct StreamOutputCount {
        RdThirtyOneDataT *base;
        int64_t count;
    };

    auto genStreamOutput = [insertLevels]() {
        int64_t start = 0, width = 3, slide = 4, watermark = 1;
        std::map<StreamAggColumns, StreamOutputCount> resMap;
        for (int64_t s = start - slide; s + width < start + (int64_t)RD_ELEMENT_COUNT(insertLevels) - watermark;
            s += slide) {
            for (int64_t j = s < 0 ? 0 : s; j < s + width && j < RD_ELEMENT_COUNT(insertLevels); j++) {
                StreamAggColumns key = {s, s + width, insertLevels[j]};
                if (resMap.find(key) == resMap.end()) {
                    RdThirtyOneDataT *base = RdThirtyOneAllocData();
                    if (base == NULL) {
                        RD_ERROR("Unable to alloc data.");
                        break;
                    }
                    RdThirtyOneSetData(base, j, 0);
                    RdThirtyOneSetDataLevel(base, insertLevels[j]);
                    RdThirtyOneSetDataTime(base, s + width);
                    StreamOutputCount val = {base, 1};
                    resMap[key] = val;
                } else {
                    resMap[key].count++;
                }
            }
        }
        std::vector<StreamOutputCount> resVec;
        for (const auto &pair: resMap) {
            resVec.push_back(pair.second);
        }
        return resVec;
    };
    auto output = genStreamOutput();
    std::sort(output.begin(), output.end(), [](StreamOutputCount a, StreamOutputCount b) {
        if (a.base->time != b.base->time) {
            return a.base->time < b.base->time; // 升序排序
        }
        if (a.base->id != b.base->id) {
            return a.base->id < b.base->id;
        }
        if (a.base->level != b.base->level) {
            return a.base->level < b.base->level;
        }
        return a.count < b.count;
    });

    const char *qrySql = "select * from ts1 order by time, id, level;";
    ret = GmcExecDirect(shortStmt, qrySql, strlen(qrySql));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckResultRows(shortStmt, output.size());
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (const auto &entry: output) {
        ret = RdThirtyOneFetchTsData(shortStmt, dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdTsCheckPropertyInt64(shortStmt, 5, entry.count);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdThirtyOneCheckData(*entry.base, *dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 5. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;

    for (const auto &entry: output) {
        RdThirtyOneFreeData(entry.base);
    }
    RdFinishTest();
}

// @TestcaseName 创建步长等于宽度的滑动窗口，通过group by聚合计算，写入数据 成功，等价与滚动窗口
TEST_F(t08_group_by_complex, STREAM_031_116)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text, count integer) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '1' seconds strict);"},

        // 2. 创建步长等于宽度的滑动窗口，通过group by聚合计算，写入数据
        {"create stream view v1 as select *, count(level) "
            "from table(hop(table t1, time, interval '3' seconds, interval '3' seconds)) "
            "group by window_start, window_end, level with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select first_id, first_name, window_end, level, first_content, count_level from v3 "
            "into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 3. 写入数据
    // 对应的时间列:           0        3        6        9        12
    int64_t insertLevels[] = {1, 2, 2, 3, 3, 3, 4, 4, 0, 4, 4, 1, 99, 100};
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (size_t i = 0; i < RD_ELEMENT_COUNT(insertLevels); i++) {
        RdThirtyOneSetData(dataObj, i, 0);
        RdThirtyOneSetDataLevel(dataObj, insertLevels[i]);
        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, dataObj);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 4. 读取校验数据, 有预期结果
    struct StreamAggColumns {
        int64_t windowStart;
        int64_t windowEnd;
        int64_t level;

        bool operator<(const StreamAggColumns &b) const {
            if (windowStart != b.windowStart) {
                return windowStart < b.windowStart;
            }
            if (windowEnd != b.windowEnd) {
                return windowEnd < b.windowEnd;
            }
            return level < b.level;
        }
    };
    struct StreamOutputCount {
        RdThirtyOneDataT *base;
        int64_t count;
    };

    auto genStreamOutput = [insertLevels]() {
        int64_t start = 0, width = 3, slide = 3, watermark = 1;
        std::map<StreamAggColumns, StreamOutputCount> resMap;
        for (int64_t s = start - width; s + width < start + RD_ELEMENT_COUNT(insertLevels) - watermark; s += slide) {
            for (int64_t j = s < 0 ? 0 : s; j < s + width && j < RD_ELEMENT_COUNT(insertLevels); j++) {
                StreamAggColumns key = {s, s + width, insertLevels[j]};
                if (resMap.find(key) == resMap.end()) {
                    RdThirtyOneDataT *base = RdThirtyOneAllocData();
                    if (base == NULL) {
                        RD_ERROR("Unable to alloc data.");
                        break;
                    }
                    RdThirtyOneSetData(base, j, 0);
                    RdThirtyOneSetDataLevel(base, insertLevels[j]);
                    RdThirtyOneSetDataTime(base, s + width);
                    StreamOutputCount val = {base, 1};
                    resMap[key] = val;
                } else {
                    resMap[key].count++;
                }
            }
        }
        std::vector<StreamOutputCount> resVec;
        for (const auto &pair: resMap) {
            resVec.push_back(pair.second);
        }
        return resVec;
    };
    auto output = genStreamOutput();
    std::sort(output.begin(), output.end(), [](StreamOutputCount a, StreamOutputCount b) {
        if (a.base->time != b.base->time) {
            return a.base->time < b.base->time; // 升序排序
        }
        if (a.base->id != b.base->id) {
            return a.base->id < b.base->id;
        }
        if (a.base->level != b.base->level) {
            return a.base->level < b.base->level;
        }
        return a.count < b.count;
    });

    const char *qrySql = "select * from ts1 order by time, id, level;";
    ret = GmcExecDirect(shortStmt, qrySql, strlen(qrySql));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckResultRows(shortStmt, output.size());
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (const auto &entry: output) {
        ret = RdThirtyOneFetchTsData(shortStmt, dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdTsCheckPropertyInt64(shortStmt, 5, entry.count);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdThirtyOneCheckData(*entry.base, *dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 5. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;

    for (const auto &entry: output) {
        RdThirtyOneFreeData(entry.base);
    }
    RdFinishTest();
}

// @TestcaseName 创建步长小于宽度的滑动窗口，通过group by聚合计算，写入数据 成功，一条数据可能流入多个窗口
TEST_F(t08_group_by_complex, STREAM_031_117)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text, count integer) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '1' seconds strict);"},

        // 2. 创建步长小于宽度的滑动窗口，通过group by聚合计算，写入数据
        {"create stream view v1 as select *, count(level) "
            "from table(hop(table t1, time, interval '1' seconds, interval '9' seconds)) "
            "group by window_start, window_end, level with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select first_id, first_name, window_end, level, first_content, count_level from v3 "
            "into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 3. 写入数据
    // 对应的时间列:           0        3        6        9        12
    int64_t insertLevels[] = {1, 2, 2, 3, 3, 3, 4, 4, 0, 4, 4, 1, 99, 100};
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (size_t i = 0; i < RD_ELEMENT_COUNT(insertLevels); i++) {
        RdThirtyOneSetData(dataObj, i, 0);
        RdThirtyOneSetDataLevel(dataObj, insertLevels[i]);
        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, dataObj);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 4. 读取校验数据, 有预期结果
    struct StreamAggColumns {
        int64_t windowStart;
        int64_t windowEnd;
        int64_t level;

        bool operator<(const StreamAggColumns &b) const {
            if (windowStart != b.windowStart) {
                return windowStart < b.windowStart;
            }
            if (windowEnd != b.windowEnd) {
                return windowEnd < b.windowEnd;
            }
            return level < b.level;
        }
    };
    struct StreamOutputCount {
        RdThirtyOneDataT *base;
        int64_t count;
    };

    auto genStreamOutput = [insertLevels]() {
        int64_t start = 0, width = 9, slide = 1, watermark = 1;
        std::map<StreamAggColumns, StreamOutputCount> resMap;
        for (int64_t s = start - width; s + width < start + RD_ELEMENT_COUNT(insertLevels) - watermark; s += slide) {
            for (int64_t j = s < 0 ? 0 : s; j < s + width && j < RD_ELEMENT_COUNT(insertLevels); j++) {
                StreamAggColumns key = {s, s + width, insertLevels[j]};
                if (resMap.find(key) == resMap.end()) {
                    RdThirtyOneDataT *base = RdThirtyOneAllocData();
                    if (base == NULL) {
                        RD_ERROR("Unable to alloc data.");
                        break;
                    }
                    RdThirtyOneSetData(base, j, 0);
                    RdThirtyOneSetDataLevel(base, insertLevels[j]);
                    RdThirtyOneSetDataTime(base, s + width);
                    StreamOutputCount val = {base, 1};
                    resMap[key] = val;
                } else {
                    resMap[key].count++;
                }
            }
        }
        std::vector<StreamOutputCount> resVec;
        for (const auto &pair: resMap) {
            resVec.push_back(pair.second);
        }
        return resVec;
    };
    auto output = genStreamOutput();
    std::sort(output.begin(), output.end(), [](StreamOutputCount a, StreamOutputCount b) {
        if (a.base->time != b.base->time) {
            return a.base->time < b.base->time; // 升序排序
        }
        if (a.base->id != b.base->id) {
            return a.base->id < b.base->id;
        }
        if (a.base->level != b.base->level) {
            return a.base->level < b.base->level;
        }
        return a.count < b.count;
    });

    const char *qrySql = "select * from ts1 order by time, id, level;";
    ret = GmcExecDirect(shortStmt, qrySql, strlen(qrySql));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckResultRows(shortStmt, output.size());
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (const auto &entry: output) {
        ret = RdThirtyOneFetchTsData(shortStmt, dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdTsCheckPropertyInt64(shortStmt, 5, entry.count);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdThirtyOneCheckData(*entry.base, *dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 5. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;

    for (const auto &entry: output) {
        RdThirtyOneFreeData(entry.base);
    }
    RdFinishTest();
}

// @TestcaseName 定义严格递增时间水印，在滑动窗口视图中通过group by聚合计算，写入乱序数据 成功，过期数据丢失
TEST_F(t08_group_by_complex, STREAM_031_118)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // DEMO 乱序窗口
    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text, count integer) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '1' seconds strict);"},

        // 2. 定义严格递增时间水印，在滑动窗口视图中通过group by聚合计算，写入乱序数据
        {"create stream view v1 as select *, count(level) "
            "from table(hop(table t1, time, interval '3' seconds, interval '9' seconds)) "
            "group by window_start, window_end, level with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select first_id, first_name, window_end, level, first_content, count_level from v3 "
            "into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 3. 写入数据
    // 对应的id列:             0         3          6          9         12
    int64_t insertTimes[] =  {1, 9, 10, 22, 2, 23, 0, 24, 23, 9, 28, 30, 27, 28};
    int64_t insertLevels[] = {1, 2, 2,  3,  3, 3,  4, 4,  0,  4, 4,  1,  99, 100};
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (size_t i = 0; i < RD_ELEMENT_COUNT(insertLevels); i++) {
        RdThirtyOneSetData(dataObj, i, 0);
        RdThirtyOneSetDataTime(dataObj, insertTimes[i]);
        RdThirtyOneSetDataLevel(dataObj, insertLevels[i]);
        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, dataObj);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 4. 读取校验数据, 有预期结果
    struct StreamAggColumns {
        int64_t windowStart;
        int64_t windowEnd;
        int64_t level;

        bool operator<(const StreamAggColumns &b) const {
            if (windowStart != b.windowStart) {
                return windowStart < b.windowStart;
            }
            if (windowEnd != b.windowEnd) {
                return windowEnd < b.windowEnd;
            }
            return level < b.level;
        }
    };
    struct StreamOutputCount {
        RdThirtyOneDataT *base;
        int64_t count;
    };

    // DEMO 乱序输入的严格递增滑动窗口，生成流计算结果
    auto genStreamResult = [insertLevels, insertTimes]() {
        int64_t width = 9, slide = 3, watermark = 1, hwm = INT64_MIN;
        std::vector<StreamOutputCount> resVec;
        std::map<StreamAggColumns, StreamOutputCount> tmpMap;
        // 流式处理数据
        for (int32_t i = 0; i < RD_ELEMENT_COUNT(insertTimes); i++) {
            // 严格递增窗口，时间戳"小于等于"最高水位，数据直接丢弃
            int64_t timestamp = insertTimes[i];
            if ((timestamp / slide * slide + width) <= hwm) {
                continue;
            }
            int64_t start = (timestamp - width + 1) / slide * slide;
            for (int64_t s = start; timestamp >= s; s += slide) {
                // 如果窗口已经关闭，继续向前滑动窗口
                if (timestamp >= s + width || s + width <= hwm) {
                    continue;
                }
                StreamAggColumns key = {s, s + width, insertLevels[i]};
                if (tmpMap.find(key) == tmpMap.end()) {
                    RdThirtyOneDataT *base = RdThirtyOneAllocData();
                    if (base == NULL) {
                        RD_ERROR("Unable to alloc data.");
                        break;
                    }
                    RdThirtyOneSetData(base, i, 0);
                    RdThirtyOneSetDataLevel(base, insertLevels[i]);
                    RdThirtyOneSetDataTime(base, s + width);
                    StreamOutputCount val = {base, 1};
                    tmpMap[key] = val;
                } else {
                    tmpMap[key].count++;
                }
            }
            // 更新最高水位，并保存即将关闭的窗口数据
            hwm = std::max(hwm, timestamp - watermark);
            for (auto it = tmpMap.begin(); it != tmpMap.end();) {
                // 严格递增窗口，时间戳"小于等于"最高水位时关闭窗口
                if (it->first.windowEnd <= hwm) {
                    resVec.push_back(it->second);
                    it = tmpMap.erase(it);
                } else {
                    it++;
                }
            }
        }
        return resVec;
    };
    auto output = genStreamResult();
    std::sort(output.begin(), output.end(), [](StreamOutputCount a, StreamOutputCount b) {
        if (a.base->time != b.base->time) {
            return a.base->time < b.base->time; // 升序排序
        }
        if (a.base->id != b.base->id) {
            return a.base->id < b.base->id;
        }
        if (a.base->level != b.base->level) {
            return a.base->level < b.base->level;
        }
        return a.count < b.count;
    });

    const char *qrySql = "select * from ts1 order by time, id, level;";
    ret = GmcExecDirect(shortStmt, qrySql, strlen(qrySql));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckResultRows(shortStmt, output.size());
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (const auto &entry: output) {
        ret = RdThirtyOneFetchTsData(shortStmt, dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdTsCheckPropertyInt64(shortStmt, 5, entry.count);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdThirtyOneCheckData(*entry.base, *dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 5. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;

    for (const auto &entry: output) {
        RdThirtyOneFreeData(entry.base);
    }
    RdFinishTest();
}

// @TestcaseName 定义宽容递增时间水印，在滑动窗口视图中通过group by聚合计算，写入乱序数据 成功，过期数据丢失
TEST_F(t08_group_by_complex, STREAM_031_119)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text, count integer) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '1' seconds tolerant);"},

        // 2. 定义宽容递增时间水印，在滑动窗口视图中通过group by聚合计算，写入乱序数据
        {"create stream view v1 as select *, count(level) "
            "from table(hop(table t1, time, interval '3' seconds, interval '9' seconds)) "
            "group by window_start, window_end, level with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select first_id, first_name, window_end, level, first_content, count_level from v3 "
            "into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 3. 写入数据
    // 对应的id列:             0         3          6          9         12
    int64_t insertTimes[] =  {1, 9, 10, 22, 2, 23, 0, 24, 23, 9, 28, 30, 27, 28};
    int64_t insertLevels[] = {1, 2, 2,  3,  3, 3,  4, 4,  0,  4, 4,  1,  99, 100};
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (size_t i = 0; i < RD_ELEMENT_COUNT(insertLevels); i++) {
        RdThirtyOneSetData(dataObj, i, 0);
        RdThirtyOneSetDataTime(dataObj, insertTimes[i]);
        RdThirtyOneSetDataLevel(dataObj, insertLevels[i]);
        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, dataObj);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 4. 读取校验数据, 有预期结果
    struct StreamAggColumns {
        int64_t windowStart;
        int64_t windowEnd;
        int64_t level;

        bool operator<(const StreamAggColumns &b) const {
            if (windowStart != b.windowStart) {
                return windowStart < b.windowStart;
            }
            if (windowEnd != b.windowEnd) {
                return windowEnd < b.windowEnd;
            }
            return level < b.level;
        }
    };
    struct StreamOutputCount {
        RdThirtyOneDataT *base;
        int64_t count;
    };

    // DEMO 乱序输入的严格递增滑动窗口，生成流计算结果
    auto genStreamResult = [insertLevels, insertTimes]() {
        int64_t width = 9, slide = 3, watermark = 1, hwm = INT64_MIN;
        std::vector<StreamOutputCount> resVec;
        std::map<StreamAggColumns, StreamOutputCount> tmpMap;
        // 流式处理数据
        for (int32_t i = 0; i < RD_ELEMENT_COUNT(insertTimes); i++) {
            // 宽容递增窗口，时间戳"小于"最高水位，数据直接丢弃
            int64_t timestamp = insertTimes[i];
            if ((timestamp / slide * slide + width) < hwm) {
                continue;
            }
            int64_t start = (timestamp - width + 1) / slide * slide;
            for (int64_t s = start; timestamp >= s; s += slide) {
                // 如果窗口已经关闭，继续向前滑动窗口
                if (timestamp >= s + width || s + width <= hwm) {
                    continue;
                }
                StreamAggColumns key = {s, s + width, insertLevels[i]};
                if (tmpMap.find(key) == tmpMap.end()) {
                    RdThirtyOneDataT *base = RdThirtyOneAllocData();
                    if (base == NULL) {
                        RD_ERROR("Unable to alloc data.");
                        break;
                    }
                    RdThirtyOneSetData(base, i, 0);
                    RdThirtyOneSetDataLevel(base, insertLevels[i]);
                    RdThirtyOneSetDataTime(base, s + width);
                    StreamOutputCount val = {base, 1};
                    tmpMap[key] = val;
                } else {
                    tmpMap[key].count++;
                }
            }
            // 更新最高水位，并保存即将关闭的窗口数据
            hwm = std::max(hwm, timestamp - watermark);
            for (auto it = tmpMap.begin(); it != tmpMap.end();) {
                // 宽容递增窗口，时间戳"小于"最高水位时关闭窗口
                if (it->first.windowEnd < hwm) {
                    resVec.push_back(it->second);
                    it = tmpMap.erase(it);
                } else {
                    it++;
                }
            }
        }
        return resVec;
    };
    auto output = genStreamResult();
    std::sort(output.begin(), output.end(), [](StreamOutputCount a, StreamOutputCount b) {
        if (a.base->time != b.base->time) {
            return a.base->time < b.base->time; // 升序排序
        }
        if (a.base->id != b.base->id) {
            return a.base->id < b.base->id;
        }
        if (a.base->level != b.base->level) {
            return a.base->level < b.base->level;
        }
        return a.count < b.count;
    });

    const char *qrySql = "select * from ts1 order by time, id, level;";
    ret = GmcExecDirect(shortStmt, qrySql, strlen(qrySql));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckResultRows(shortStmt, output.size());
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (const auto &entry: output) {
        ret = RdThirtyOneFetchTsData(shortStmt, dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdTsCheckPropertyInt64(shortStmt, 5, entry.count);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdThirtyOneCheckData(*entry.base, *dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 5. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;

    for (const auto &entry: output) {
        RdThirtyOneFreeData(entry.base);
    }
    RdFinishTest();
}

// @TestcaseName 定义宽容递增时间水印，包含group by，不包含聚合函数，写入乱序数据 成功，过期数据丢失
TEST_F(t08_group_by_complex, STREAM_031_120)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '1' seconds strict);"},

        // 2. 定义宽容递增时间水印，包含group by，不包含聚合函数，写入乱序数据
        {"create stream view v1 as select * "
            "from table(hop(table t1, time, interval '3' seconds, interval '9' seconds)) "
            "group by window_start, window_end, level with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select first_id, first_name, window_end, level, first_content from v3 "
            "into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 3. 写入数据
    // 对应的id列:             0         3          6          9         12
    int64_t insertTimes[] =  {1, 9, 10, 22, 2, 23, 0, 24, 23, 9, 28, 30, 27, 28};
    int64_t insertLevels[] = {1, 2, 2,  3,  3, 3,  4, 4,  0,  4, 4,  1,  99, 100};
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (size_t i = 0; i < RD_ELEMENT_COUNT(insertLevels); i++) {
        RdThirtyOneSetData(dataObj, i, 0);
        RdThirtyOneSetDataTime(dataObj, insertTimes[i]);
        RdThirtyOneSetDataLevel(dataObj, insertLevels[i]);
        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, dataObj);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 4. 读取校验数据, 有预期结果
    struct StreamAggColumns {
        int64_t windowStart;
        int64_t windowEnd;
        int64_t level;

        bool operator<(const StreamAggColumns &b) const {
            if (windowStart != b.windowStart) {
                return windowStart < b.windowStart;
            }
            if (windowEnd != b.windowEnd) {
                return windowEnd < b.windowEnd;
            }
            return level < b.level;
        }
    };
    struct StreamOutputCount {
        RdThirtyOneDataT *base;
        int64_t count;
    };

    // DEMO 乱序输入的严格递增滑动窗口，生成流计算结果
    auto genStreamResult = [insertLevels, insertTimes]() {
        int64_t width = 9, slide = 3, watermark = 1, hwm = INT64_MIN;
        std::vector<StreamOutputCount> resVec;
        std::map<StreamAggColumns, StreamOutputCount> tmpMap;
        // 流式处理数据
        for (int32_t i = 0; i < RD_ELEMENT_COUNT(insertTimes); i++) {
            // 严格递增窗口，时间戳小于等于最高水位，数据直接丢弃
            int64_t timestamp = insertTimes[i];
            if ((timestamp / slide * slide + width) <= hwm) {
                continue;
            }
            int64_t start = (timestamp - width + 1) / slide * slide;
            for (int64_t s = start; timestamp >= s; s += slide) {
                // 如果窗口已经关闭，继续向前滑动窗口
                if (timestamp >= s + width || s + width <= hwm) {
                    continue;
                }
                StreamAggColumns key = {s, s + width, insertLevels[i]};
                if (tmpMap.find(key) == tmpMap.end()) {
                    RdThirtyOneDataT *base = RdThirtyOneAllocData();
                    if (base == NULL) {
                        RD_ERROR("Unable to alloc data.");
                        break;
                    }
                    RdThirtyOneSetData(base, i, 0);
                    RdThirtyOneSetDataLevel(base, insertLevels[i]);
                    RdThirtyOneSetDataTime(base, s + width);
                    StreamOutputCount val = {base, 1};
                    tmpMap[key] = val;
                } else {
                    tmpMap[key].count++;
                }
            }
            // 更新最高水位，并保存即将关闭的窗口数据
            hwm = std::max(hwm, timestamp - watermark);
            for (auto it = tmpMap.begin(); it != tmpMap.end();) {
                if (it->first.windowEnd <= hwm) {
                    resVec.push_back(it->second);
                    it = tmpMap.erase(it);
                } else {
                    it++;
                }
            }
        }
        return resVec;
    };
    auto output = genStreamResult();
    std::sort(output.begin(), output.end(), [](StreamOutputCount a, StreamOutputCount b) {
        if (a.base->time != b.base->time) {
            return a.base->time < b.base->time; // 升序排序
        }
        if (a.base->id != b.base->id) {
            return a.base->id < b.base->id;
        }
        if (a.base->level != b.base->level) {
            return a.base->level < b.base->level;
        }
        return a.count < b.count;
    });

    const char *qrySql = "select * from ts1 order by time, id, level;";
    ret = GmcExecDirect(shortStmt, qrySql, strlen(qrySql));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckResultRows(shortStmt, output.size());
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (const auto &entry: output) {
        ret = RdThirtyOneFetchTsData(shortStmt, dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdThirtyOneCheckData(*entry.base, *dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 5. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;

    for (const auto &entry: output) {
        RdThirtyOneFreeData(entry.base);
    }
    RdFinishTest();
}

// @TestSuite 交互场景4  - sink、view
// @TestcaseName 创建滚动窗口sink，在sink上进行group by聚合 成功
TEST_F(t08_group_by_complex, STREAM_031_121)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text, count integer) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '1' seconds strict);"},

        {"create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},

        // 2. 创建滚动窗口sink，在sink上进行group by聚合
        {"create stream sink s1 as select id, name, window_end, level, content, count(level) "
            "from table(tumble(table t1, time, interval '3' seconds)) "
            "group by window_start, window_end, level "
            "into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 3. 写入数据
    // 对应的时间列:           0        3        6        9        12
    int64_t insertLevels[] = {1, 2, 2, 3, 3, 3, 4, 4, 0, 4, 4, 1, 99, 100};
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (size_t i = 0; i < RD_ELEMENT_COUNT(insertLevels); i++) {
        RdThirtyOneSetData(dataObj, i, 0);
        RdThirtyOneSetDataLevel(dataObj, insertLevels[i]);
        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, dataObj);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 4. 读取校验数据, 有预期结果
    struct StreamAggColumns {
        int64_t windowStart;
        int64_t windowEnd;
        int64_t level;

        bool operator<(const StreamAggColumns &b) const {
            if (windowStart != b.windowStart) {
                return windowStart < b.windowStart;
            }
            if (windowEnd != b.windowEnd) {
                return windowEnd < b.windowEnd;
            }
            return level < b.level;
        }
    };
    struct StreamOutputCount {
        RdThirtyOneDataT *base;
        int64_t count;
    };

    auto genStreamOutput = [insertLevels]() {
        int64_t start = 0, width = 3, slide = 3, watermark = 1;
        std::map<StreamAggColumns, StreamOutputCount> resMap;
        for (int64_t s = start - width; s + width < start + RD_ELEMENT_COUNT(insertLevels) - watermark; s += slide) {
            for (int64_t j = s < 0 ? 0 : s; j < s + width && j < RD_ELEMENT_COUNT(insertLevels); j++) {
                StreamAggColumns key = {s, s + width, insertLevels[j]};
                if (resMap.find(key) == resMap.end()) {
                    RdThirtyOneDataT *base = RdThirtyOneAllocData();
                    if (base == NULL) {
                        RD_ERROR("Unable to alloc data.");
                        break;
                    }
                    RdThirtyOneSetData(base, j, 0);
                    RdThirtyOneSetDataLevel(base, insertLevels[j]);
                    RdThirtyOneSetDataTime(base, s + width);
                    StreamOutputCount val = {base, 1};
                    resMap[key] = val;
                } else {
                    resMap[key].count++;
                }
            }
        }
        std::vector<StreamOutputCount> resVec;
        for (const auto &pair: resMap) {
            resVec.push_back(pair.second);
        }
        return resVec;
    };
    auto output = genStreamOutput();
    std::sort(output.begin(), output.end(), [](StreamOutputCount a, StreamOutputCount b) {
        if (a.base->time != b.base->time) {
            return a.base->time < b.base->time; // 升序排序
        }
        if (a.base->id != b.base->id) {
            return a.base->id < b.base->id;
        }
        if (a.base->level != b.base->level) {
            return a.base->level < b.base->level;
        }
        return a.count < b.count;
    });

    const char *qrySql = "select * from ts1 order by time, id, level;";
    ret = GmcExecDirect(shortStmt, qrySql, strlen(qrySql));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckResultRows(shortStmt, output.size());
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (const auto &entry: output) {
        ret = RdThirtyOneFetchTsData(shortStmt, dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdTsCheckPropertyInt64(shortStmt, 5, entry.count);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdThirtyOneCheckData(*entry.base, *dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 5. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;

    for (const auto &entry: output) {
        RdThirtyOneFreeData(entry.base);
    }
    RdFinishTest();
}

// @TestcaseName 创建滑动窗口sink，在sink上进行group by聚合 成功
TEST_F(t08_group_by_complex, STREAM_031_122)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text, count integer) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '1' seconds strict);"},

        {"create stream view v1 as select * from t1 with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},

        // 2. 创建滑动窗口sink，在sink上进行group by聚合
        {"create stream sink s1 as select id, name, window_end, level, content, count(level) "
            "from table(hop(table v3, time, interval '3' seconds, interval '9' seconds)) "
            "group by window_start, window_end, level "
            "into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 3. 写入数据
    // 对应的时间列:           0        3        6        9        12
    int64_t insertLevels[] = {1, 2, 2, 3, 3, 3, 4, 4, 0, 4, 4, 1, 99, 100};
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (size_t i = 0; i < RD_ELEMENT_COUNT(insertLevels); i++) {
        RdThirtyOneSetData(dataObj, i, 0);
        RdThirtyOneSetDataLevel(dataObj, insertLevels[i]);
        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, dataObj);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 4. 读取校验数据, 有预期结果
    struct StreamAggColumns {
        int64_t windowStart;
        int64_t windowEnd;
        int64_t level;

        bool operator<(const StreamAggColumns &b) const {
            if (windowStart != b.windowStart) {
                return windowStart < b.windowStart;
            }
            if (windowEnd != b.windowEnd) {
                return windowEnd < b.windowEnd;
            }
            return level < b.level;
        }
    };
    struct StreamOutputCount {
        RdThirtyOneDataT *base;
        int64_t count;
    };

    auto genStreamOutput = [insertLevels]() {
        int64_t start = 0, width = 9, slide = 3, watermark = 1;
        std::map<StreamAggColumns, StreamOutputCount> resMap;
        for (int64_t s = start - width; s + width < start + RD_ELEMENT_COUNT(insertLevels) - watermark; s += slide) {
            for (int64_t j = s < 0 ? 0 : s; j < s + width && j < RD_ELEMENT_COUNT(insertLevels); j++) {
                StreamAggColumns key = {s, s + width, insertLevels[j]};
                if (resMap.find(key) == resMap.end()) {
                    RdThirtyOneDataT *base = RdThirtyOneAllocData();
                    if (base == NULL) {
                        RD_ERROR("Unable to alloc data.");
                        break;
                    }
                    RdThirtyOneSetData(base, j, 0);
                    RdThirtyOneSetDataLevel(base, insertLevels[j]);
                    RdThirtyOneSetDataTime(base, s + width);
                    StreamOutputCount val = {base, 1};
                    resMap[key] = val;
                } else {
                    resMap[key].count++;
                }
            }
        }
        std::vector<StreamOutputCount> resVec;
        for (const auto &pair: resMap) {
            resVec.push_back(pair.second);
        }
        return resVec;
    };
    auto output = genStreamOutput();
    std::sort(output.begin(), output.end(), [](StreamOutputCount a, StreamOutputCount b) {
        if (a.base->time != b.base->time) {
            return a.base->time < b.base->time; // 升序排序
        }
        if (a.base->id != b.base->id) {
            return a.base->id < b.base->id;
        }
        if (a.base->level != b.base->level) {
            return a.base->level < b.base->level;
        }
        return a.count < b.count;
    });

    const char *qrySql = "select * from ts1 order by time, id, level;";
    ret = GmcExecDirect(shortStmt, qrySql, strlen(qrySql));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckResultRows(shortStmt, output.size());
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (const auto &entry: output) {
        ret = RdThirtyOneFetchTsData(shortStmt, dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdTsCheckPropertyInt64(shortStmt, 5, entry.count);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdThirtyOneCheckData(*entry.base, *dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 5. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;

    for (const auto &entry: output) {
        RdThirtyOneFreeData(entry.base);
    }
    RdFinishTest();
}

// @TestcaseName 创建滑动窗口视图，包含union和group by聚合 报错，语法错误
TEST_F(t08_group_by_complex, STREAM_031_123)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text, max integer) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '1' seconds strict);"},
        {"create stream table t2 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '1' seconds strict);"},

        // 2. 创建滑动窗口视图，包含union和group by聚合
        {"create stream view v1 as select *, count(id) "
            "from table(hop(table table(union(table t1, table t2)), time, interval '3' seconds, interval '9' seconds)) "
            "group by window_start, window_end, level with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR},
        {"create stream view v1 as select *, count(id) "
            "from table(union(table t1, table t2)) "
            "group by level with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_SEMANTIC_ERROR},
        {"create stream view v1 as select *, count(id) "
            "from table(union(table t1, table t2)) "
            "group by window_start, window_end, level with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINE_COLUMN},
        {"create stream view v1 as select *, count(id) "
            "from table(union(table table(hop(table t1, time, interval '3' seconds, interval '9' seconds), table t2))) "
            "group by window_start, window_end, level with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR},

        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"create stream sink s1 as select first_id, first_name, window_end, level, first_content, count_id from v3 "
            "into tsdb (ts1) with (batch_window_size = 1);",
            GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 3. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v3;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v2;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream view v1;", GMC_MODEL_STREAM, GMERR_UNDEFINED_TABLE},
        {"drop stream table t2;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdFinishTest();
}

// @TestcaseName 创建滑动窗口视图，包含dispatch by和group by聚合 成功
TEST_F(t08_group_by_complex, STREAM_031_124)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // DEMO dispatch和group by交互
    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text, count integer) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '1' seconds strict);"},

        // 2. 创建滑动窗口视图，包含dispatch by和group by聚合
        {"create stream view v1 as select *, count(level) "
            "from table(hop(table t1, time, interval '3' seconds, interval '9' seconds)) "
            "group by window_start, window_end, level "
            "dispatch by first_name, first_id "
            "with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from table(dispatch(table v1, 'operation', 0)) with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select first_id, first_name, window_end, level, first_content, count_level from v3 "
            "into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 3. 写入数据
    // 对应的时间列:           0        3        6        9        12
    int64_t insertLevels[] = {1, 2, 2, 3, 3, 3, 4, 4, 0, 4, 4, 1, 99, 100};
    const char *insertNames[] = {"operation", "debug", "run"};
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (size_t i = 0; i < RD_ELEMENT_COUNT(insertLevels); i++) {
        RdThirtyOneSetData(dataObj, i, 0);
        RdThirtyOneSetDataLevel(dataObj, insertLevels[i]);
        RdThirtyOneSetDataId(dataObj, i % 2);
        RdThirtyOneSetDataName(dataObj, insertNames[i % 3]);
        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, dataObj);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 4. 读取校验数据, 有预期结果
    struct StreamAggColumns {
        int64_t windowStart;
        int64_t windowEnd;
        int64_t level;

        bool operator<(const StreamAggColumns &b) const {
            if (windowStart != b.windowStart) {
                return windowStart < b.windowStart;
            }
            if (windowEnd != b.windowEnd) {
                return windowEnd < b.windowEnd;
            }
            return level < b.level;
        }
    };
    struct StreamOutputCount {
        RdThirtyOneDataT *base;
        int64_t count;
    };

    auto genStreamOutput = [insertLevels, insertNames]() {
        int64_t start = 0, width = 9, slide = 3, watermark = 1;
        std::map<StreamAggColumns, StreamOutputCount> resMap;
        for (int64_t s = start - width; s + width < start + RD_ELEMENT_COUNT(insertLevels) - watermark; s += slide) {
            for (int64_t j = s < 0 ? 0 : s; j < s + width && j < RD_ELEMENT_COUNT(insertLevels); j++) {
                StreamAggColumns key = {s, s + width, insertLevels[j]};
                if (resMap.find(key) == resMap.end()) {
                    RdThirtyOneDataT *base = RdThirtyOneAllocData();
                    if (base == NULL) {
                        RD_ERROR("Unable to alloc data.");
                        break;
                    }
                    RdThirtyOneSetData(base, j, 0);
                    RdThirtyOneSetDataLevel(base, insertLevels[j]);
                    RdThirtyOneSetDataTime(base, s + width);
                    RdThirtyOneSetDataId(base, j % 2);
                    RdThirtyOneSetDataName(base, insertNames[j % 3]);
                    StreamOutputCount val = {base, 1};
                    resMap[key] = val;
                } else {
                    resMap[key].count++;
                }
            }
        }
        std::vector<StreamOutputCount> resVec;
        for (const auto &pair: resMap) {
            // 此处要注意，窗口先聚合输出数据，再通过dispatch进行过滤，过滤后写入下一跳节点
            if (!(pair.second.base->id == 0 && strcmp(pair.second.base->name, "operation") == 0)) {
                continue;
            }
            resVec.push_back(pair.second);
        }
        return resVec;
    };
    auto output = genStreamOutput();
    std::sort(output.begin(), output.end(), [](StreamOutputCount a, StreamOutputCount b) {
        if (a.base->time != b.base->time) {
            return a.base->time < b.base->time; // 升序排序
        }
        if (a.base->id != b.base->id) {
            return a.base->id < b.base->id;
        }
        if (a.base->level != b.base->level) {
            return a.base->level < b.base->level;
        }
        return a.count < b.count;
    });

    const char *qrySql = "select * from ts1 order by time, id, level;";
    ret = GmcExecDirect(shortStmt, qrySql, strlen(qrySql));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckResultRows(shortStmt, output.size());
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (const auto &entry: output) {
        ret = RdThirtyOneFetchTsData(shortStmt, dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdTsCheckPropertyInt64(shortStmt, 5, entry.count);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdThirtyOneCheckData(*entry.base, *dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 5. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;

    for (const auto &entry: output) {
        RdThirtyOneFreeData(entry.base);
    }
    RdFinishTest();
}

// @TestcaseName 创建滑动窗口视图，包含dispatch from和group by聚合 成功
TEST_F(t08_group_by_complex, STREAM_031_125)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text, count integer) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '1' seconds strict);"},

        // 2. 创建滑动窗口视图，包含dispatch from和group by聚合
        {"create stream view v1 as select * from t1 dispatch by name, id with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select *, count(level) "
            "from table(hop(table dispatch(table v1, 'operation', 0), time, interval '3' seconds, interval '9' seconds)) "
            "group by window_start, window_end, level "
            "with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select first_id, first_name, window_end, level, first_content, count_level from v3 "
            "into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 3. 写入数据
    // 对应的时间列:           0        3        6        9        12
    // 对应的id列:             0  1  0  1  0  1  0  1  0  1  0  1  0   1
    // 对应的name列:           o  d r   o  d  r  o  d  r  o  d  r  o   d
    // 是否写入成功:           1                 1                 1
    int64_t insertLevels[] = {1, 2, 2, 3, 3, 3, 4, 4, 0, 4, 4, 1, 99, 100};
    const char *insertNames[] = {"operation", "debug", "run"};
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (size_t i = 0; i < RD_ELEMENT_COUNT(insertLevels); i++) {
        RdThirtyOneSetData(dataObj, i, 0);
        RdThirtyOneSetDataLevel(dataObj, insertLevels[i]);
        RdThirtyOneSetDataId(dataObj, i % 2);
        RdThirtyOneSetDataName(dataObj, insertNames[i % 3]);
        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, dataObj);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 4. 读取校验数据, 有预期结果
    struct StreamAggColumns {
        int64_t windowStart;
        int64_t windowEnd;
        int64_t level;

        bool operator<(const StreamAggColumns &b) const {
            if (windowStart != b.windowStart) {
                return windowStart < b.windowStart;
            }
            if (windowEnd != b.windowEnd) {
                return windowEnd < b.windowEnd;
            }
            return level < b.level;
        }
    };
    struct StreamOutputCount {
        RdThirtyOneDataT *base;
        int64_t count;
    };

    auto genStreamOutput = [insertLevels, insertNames]() {
        int64_t start = 0, width = 9, slide = 3, watermark = 1;
        std::map<StreamAggColumns, StreamOutputCount> resMap;
        // 由于where前置，流入窗口的最大时间列是12（详见写入数据处的分析）
        int64_t maxTime = 12;
        for (int64_t s = start - width; s + width < start + maxTime - watermark; s += slide) {
            for (int64_t j = s < 0 ? 0 : s; j < s + width && j < maxTime; j++) {
                if (!(j % 2 == 0 && strcmp(insertNames[j % 3], "operation") == 0)) {
                    continue;
                }
                StreamAggColumns key = {s, s + width, insertLevels[j]};
                if (resMap.find(key) == resMap.end()) {
                    RdThirtyOneDataT *base = RdThirtyOneAllocData();
                    if (base == NULL) {
                        RD_ERROR("Unable to alloc data.");
                        break;
                    }
                    RdThirtyOneSetData(base, j, 0);
                    RdThirtyOneSetDataLevel(base, insertLevels[j]);
                    RdThirtyOneSetDataTime(base, s + width);
                    RdThirtyOneSetDataId(base, j % 2);
                    RdThirtyOneSetDataName(base, insertNames[j % 3]);
                    StreamOutputCount val = {base, 1};
                    resMap[key] = val;
                } else {
                    resMap[key].count++;
                }
            }
        }
        std::vector<StreamOutputCount> resVec;
        for (const auto &pair: resMap) {
            resVec.push_back(pair.second);
        }
        return resVec;
    };
    auto output = genStreamOutput();
    std::sort(output.begin(), output.end(), [](StreamOutputCount a, StreamOutputCount b) {
        if (a.base->time != b.base->time) {
            return a.base->time < b.base->time; // 升序排序
        }
        if (a.base->id != b.base->id) {
            return a.base->id < b.base->id;
        }
        if (a.base->level != b.base->level) {
            return a.base->level < b.base->level;
        }
        return a.count < b.count;
    });

    const char *qrySql = "select * from ts1 order by time, id, level;";
    ret = GmcExecDirect(shortStmt, qrySql, strlen(qrySql));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdTsCheckResultRows(shortStmt, output.size());
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (const auto &entry: output) {
        ret = RdThirtyOneFetchTsData(shortStmt, dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdTsCheckPropertyInt64(shortStmt, 5, entry.count);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = RdThirtyOneCheckData(*entry.base, *dataQry);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 5. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;

    for (const auto &entry: output) {
        RdThirtyOneFreeData(entry.base);
    }
    RdFinishTest();
}

// @TestSuite 并发场景
// @TestcaseName 通过group by进行聚合计算，并发向相同表写入数据 成功
TEST_F(t08_group_by_complex, STREAM_031_126)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text, count integer) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '1' seconds strict);"},

        // 2. 通过group by进行聚合计算，并发向相同表写入数据
        {"create stream view v1 as select *, count(level) "
            "from table(hop(table t1, time, interval '3' seconds, interval '9' seconds)) "
            "group by window_start, window_end, level with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select first_id, first_name, window_end, level, first_content, count_level from v3 "
            "into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // 3. 写入数据
    static const uint32_t threadNum = 4;
    pthread_t threads[threadNum] = {0};
    RdThirtyOneWriteCtxT writeCtx[threadNum] = {0};
    int64_t rowNumPerThread = 1000;
    for (uint32_t i = 0; i < threadNum; i++) {
        writeCtx[i] = {
            .index = (int32_t)i,
            .status = GMERR_OK,
            .rowStart = -100,
            .rowNum = rowNumPerThread,
            .vertexLabel = vertexLabel,
            .ignoreError = false,
        };
        ret = pthread_create(&threads[i], NULL, RdThirtyOneWriteWorker, &writeCtx[i]);
        AW_MACRO_ASSERT_EQ_INT(0, ret);
    }
    for (uint32_t i = 0; i < threadNum; i++) {
        ret = pthread_join(threads[i], NULL);
        AW_MACRO_ASSERT_EQ_INT(0, ret);
    }
    for (uint32_t i = 0; i < threadNum; i++) {
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, writeCtx[i].status);
    }

    // 4. 读取校验数据, 有预期结果
    uint32_t qryCount = 0;
    ret = RdTsGetRecordCount(shortStmt, "ts1", &qryCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_LT(qryCount, rowNumPerThread);

    // 5. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;
    RdFinishTest();
}

// @TestcaseName 通过group by进行聚合计算，并发向不同表写入数据 成功
TEST_F(t08_group_by_complex, STREAM_031_127)
{
    int32_t ret;
    AW_FUN_Log(LOG_STEP, "start test.");

    // 1. 创建流表和时序表
    RdStreamExecDescT prepare[] = {
        {"create table ts1 (id integer, name char(50), time integer, level integer, content text, count integer) "
            "with (time_col = 'time', interval = '1 hour');"},
        {"create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '1' seconds strict);"},
        {"create stream table t2 (id integer, name char(50), time integer, level integer, content text, "
            "watermark for time as time - interval '1' seconds strict);"},

        // 2. 通过group by进行聚合计算，并发向不同表写入数据
        {"create stream view v1 as select *, count(level) "
            "from table(hop(table t1, time, interval '3' seconds, interval '9' seconds)) "
            "group by window_start, window_end, level with (tuple_buffer_size = 1);"},
        {"create stream view v2 as select * from v1 with (tuple_buffer_size = 1);"},
        {"create stream view v3 as select * from v2 with (tuple_buffer_size = 1);"},
        {"create stream sink s1 as select first_id, first_name, window_end, level, first_content, count_level from v3 "
            "into tsdb (ts1) with (batch_window_size = 1);"},

        {"create stream view v4 as select *, count(level) "
            "from table(hop(table t2, time, interval '3' seconds, interval '9' seconds)) "
            "group by window_start, window_end, level with (tuple_buffer_size = 1);"},
        {"create stream view v5 as select * from v4 with (tuple_buffer_size = 1);"},
        {"create stream view v6 as select * from v5 with (tuple_buffer_size = 1);"},
        {"create stream sink s2 as select first_id, first_name, window_end, level, first_content, count_level from v6 "
            "into tsdb (ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(shortStmt, prepare, sizeof(prepare));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexLabel = RdStreamParseTableSchema(prepare[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);
    RdVertexLabelT *vertexLabel2 = RdStreamParseTableSchema(prepare[2].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel2);
    RdVertexLabelT *vertexLabels[] = {vertexLabel, vertexLabel2};

    // 3. 写入数据
    static const uint32_t threadNum = 4;
    pthread_t threads[threadNum] = {0};
    RdThirtyOneWriteCtxT writeCtx[threadNum] = {0};
    int64_t rowNumPerThread = 1000;
    for (uint32_t i = 0; i < threadNum; i++) {
        writeCtx[i] = {
            .index = (int32_t)i,
            .status = GMERR_OK,
            .rowStart = -100,
            .rowNum = rowNumPerThread,
            .vertexLabel = vertexLabels[i % 2],
            .ignoreError = false,
        };
        ret = pthread_create(&threads[i], NULL, RdThirtyOneWriteWorker, &writeCtx[i]);
        AW_MACRO_ASSERT_EQ_INT(0, ret);
    }
    for (uint32_t i = 0; i < threadNum; i++) {
        ret = pthread_join(threads[i], NULL);
        AW_MACRO_ASSERT_EQ_INT(0, ret);
    }
    for (uint32_t i = 0; i < threadNum; i++) {
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, writeCtx[i].status);
    }

    // 4. 读取校验数据, 有预期结果
    uint32_t qryCount = 0;
    ret = RdTsGetRecordCount(shortStmt, "ts1", &qryCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_LT(qryCount, rowNumPerThread);

    // 5. 清理测试数据
    RdStreamExecDescT clean[] = {
        {"drop stream sink s2;"},
        {"drop stream view v6;"},
        {"drop stream view v5;"},
        {"drop stream view v4;"},

        {"drop stream sink s1;"},
        {"drop stream view v3;"},
        {"drop stream view v2;"},
        {"drop stream view v1;"},

        {"drop stream table t2;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamDropMetadata(shortStmt, clean, sizeof(clean));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    vertexLabel = NULL;
    RdStreamFreeTableSchema(vertexLabel2);
    vertexLabel2 = NULL;
    RdFinishTest();
}
