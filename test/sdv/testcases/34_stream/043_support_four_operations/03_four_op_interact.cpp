/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description:流计算支持四则运算交互用例
 * Author: moxiaotong
 * Create: 2025-05-23
 */
#include "gtest/gtest.h"
#include "securec.h"
#include "gmc_sql.h"
#include "t_rd_sn.h"
#include "gmc_persist.h"
#include "rd_feature_stream.h"
#include "text_util.h"

class four_op_interact : public testing::Test {
private:
    char socketAddr1[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
    int32_t ret1 = TestGetResultCommand("cat pathConfig.txt |grep socketAddr1 |awk -F '[:]' '{print $2}' |sed 's/ //g'",
        NULL, socketAddr1, sizeof(socketAddr1));
    char socketAddr2[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
    int32_t ret2 = TestGetResultCommand("cat pathConfig.txt |grep socketAddr2 |awk -F '[:]' '{print $2}' |sed 's/ //g'",
        NULL, socketAddr2, sizeof(socketAddr2));
    const vector<string> sockFileLists = {socketAddr1, socketAddr2};

public:
    static GmcConnT *conn;
    static GmcStmtT *stmt;
    bool hasFinishTest = false;
    void RdFinishTest();
    static void SetUpTestCase();
    static void TearDownTestCase();
    virtual void SetUp();
    virtual void TearDown();
};

GmcConnT *four_op_interact::conn = NULL;
GmcStmtT *four_op_interact::stmt = NULL;

void four_op_interact::SetUpTestCase()
{
    int32_t ret;
    ret = RdStreamEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void four_op_interact::TearDownTestCase()
{
    int32_t ret;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdStreamEnvClean();
    EXPECT_EQ(GMERR_OK, ret);
}

void four_op_interact::SetUp()
{
    hasFinishTest = false;
    // 移除环境中的sock文件
    sockEnvInit(sockFileLists);
    int32_t ret = RdGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void four_op_interact::TearDown()
{
    int32_t ret;
    // 检查删sink时是否成功删除sock文件
    checkSockFileNotExist(sockFileLists);
    if (!hasFinishTest) {
        RdStreamExecDescT drops[] = {
            {"drop table ts1;"},
            {"drop stream sink sink1;"},
            {"drop stream view view1;"},
            {"drop stream table stream1;"},
        };
        ret = RdStreamExecSql(stmt, drops, sizeof(drops));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = RdGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

void four_op_interact::RdFinishTest()
{
    hasFinishTest = true;
}

// 051创建流表，后继结点包含投影去重函数
// 	失败
TEST_F(four_op_interact, STREAM_043_051)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, name char(50), address text) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer, time integer, name char(50), address text);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *create = (char *)"create stream view view1 AS select seq_distinct_count(id + 2) * 2, "
                           "time, name, address FROM stream1;";
    ret = GmcExecDirect(stmt, create, strlen(create));
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);

    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 052创建流表，后继结点使用运算表达式进行format格式化
// 	失败
TEST_F(four_op_interact, STREAM_043_052)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, name char(50), address text) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer, time integer, name char(50), address text);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *create = (char *)"create stream view view1 AS select id, time, name, "
                           "format('My id is %d', id * 2 + 4) FROM stream1;";
    ret = GmcExecDirect(stmt, create, strlen(create));
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);

    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 053创建流表，后继结点使用运算表达式，整形设置默认值
// 	成功
TEST_F(four_op_interact, STREAM_043_053)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, name char(50), address text) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer DEFAULT 48, time integer, name char(50), address text);"},
        {"create stream view view1 AS select id + 2, time * 6 + id, name, address FROM stream1;"},
        {"create stream sink sink1 AS select * FROM view1 INTO tsdb(ts1) with (batch_window_size = '1')"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);
    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 4;
    uint16_t len = 120;
    int64_t event_time[rowNum] = {10, 14, 21, 33, 41};
    // 看结构体设置
    bool nullInfo[colNum] = {true, false, false, false, false};
    RdStructWriteStreamTableO4(stmt, vertexLabel, rowNum, event_time, nullInfo, len);

    // 校验ts表中数据
    char *sqlCmd = (char *)"select * from ts1 order by time;";
    uint32_t expRowNum = 5;
    uint32_t expColNum = 4;
    int64_t ids = 48;
    RdCheckDataInTSTableOfStreamTableO10(stmt, sqlCmd, expRowNum, expColNum, ids, event_time);

    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}
// 054创建流表，后继结点使用运算表达式，浮点数设置默认值
// 成功
TEST_F(four_op_interact, STREAM_043_054)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create stream table stream1 (id integer, time real DEFAULT 11.2, height real DEFAULT 4.5, name char(50), "
         "address text);"},
        {"create stream view view1 AS select id * 2, time + 3.6, height + 10.7 * time, name, address FROM stream1;"},
        {"create stream sink sink1 AS select * FROM view1 INTO pubsub_channel with (batch_window_size = '1')"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "sink1",
                "comment":"this is a sub.",
                "schema_version": 0,
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] }
                ]  
            }
        )",
    };
    // 定义回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
        bool eof = false;
        bool isNull = false;
        uint32_t size = 0;
        while (true) {
            uint32_t ret = GmcFetch(stmt, &eof);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            if (eof || ret != GMERR_OK) {
                break;
            }
            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
            AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            // check id
            int64_t id = 0;
            int64_t val1 = received * 2;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &id, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(val1, id);
            // check time
            size = 8;
            double time = 0.0;
            double val2 = 3.6 + 11.2;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &time, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_DOUBLE(val2, time);
            // check height
            double height = 0.0;
            val2 = 4.5 + 10.7 * 11.2;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &height, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_DOUBLE(val2, height);
            // check name
            size = RD_STREAM_TABLE_T1_NAME_SIZE;
            char name[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            char expectName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            (void)snprintf_s(
                expectName, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", received);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &name, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(0, strcmp(expectName, name));
            // check address
            ret = GmcGetVertexPropertySizeById(stmt, 4, &size);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char address[size] = {0};
            char expectAddress[size] = {0};
            (void)snprintf_s(expectAddress, size, size, "address_%0ld", received);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &address, &size, &isNull);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectAddress, address));
            received++;
        }
    };
    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 创建订阅
    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[0].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);
    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 4;
    int64_t id[rowNum] = {0, 1, 2, 3, 4};
    bool nullInfo[colNum] = {false, true, true, false, false, false};
    uint16_t len = 120;
    RdStructWriteStreamTableO5(stmt, vertexLabel, rowNum, id, nullInfo, len);
    uint32_t expectRowsCount = rowNum;
    RdTwentyOneWaitSnRecv(&received, expectRowsCount, 10);
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, received);
    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);
    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 055创建流表，后继结点使用运算表达式设置默认填充时间列
// 	失败
TEST_F(four_op_interact, STREAM_043_055)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, name char(50), address text) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer DEFAULT current_time_second(), time integer, name char(50), address "
         "text);"},
        {"create stream view view1 AS select id + 2, time * 6, name, address FROM stream1;"},
        {"create stream sink sink1 AS select * FROM view1 INTO tsdb(ts1) with (batch_window_size = '1')"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);
    // 结构化写入数据
    uint32_t rowNum = 5;
    uint32_t colNum = 4;
    uint16_t len = 120;
    int64_t event_time[rowNum] = {10, 14, 21, 33, 41};
    // 看结构体设置
    bool nullInfo[colNum] = {true, false, false, false, false};
    RdStructWriteStreamTableO4(stmt, vertexLabel, rowNum, event_time, nullInfo, len);
    // get system time
    struct timeval tv;
    gettimeofday(&tv, NULL);
    // 校验ts表中数据
    char *sqlCmd = (char *)"select * from ts1 order by time;";
    uint32_t expRowNum = rowNum;
    uint32_t expColNum = colNum;
    uint32_t timeDiff = 3;
    RdCheckDataInTSTableOfStreamTableO8(stmt, sqlCmd, expRowNum, expColNum, event_time, tv.tv_sec, timeDiff);
    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 056创建流表，前驱流表通过文件注入
// 	成功
TEST_F(four_op_interact, STREAM_043_056)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    system("sudo rm -f /dev/mydevice");
    char fileName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
    ret = TestGetResultCommand(
        "cat pathConfig.txt |grep fileName |awk -F '[:]' '{print $2}' |sed 's/ //g'", NULL, fileName, sizeof(fileName));
    EXPECT_EQ(0, ret);
    char command[STREAM_REFERENCE_MAX_SQL_LEN];
    ret = sprintf_s(command, STREAM_REFERENCE_MAX_SQL_LEN,
        "create stream table stream1(file char(50), id integer DEFAULT 23, time integer DEFAULT 14, address "
        "char(50) DEFAULT 'a') from fileGroup "
        "('%s')",
        fileName);
    // 创建命名管道
    if (mkfifo(fileName, 0666) == -1) {
        perror("mkfifo");
        std::cerr << "Error creating FIFO '" << fileName << "': " << strerror(errno) << std::endl;
    }
    system("sudo chmod 0666 /dev/mydevice");
    int g_fd = open(fileName, O_RDWR, 0666);
    if (g_fd == -1) {
        perror("Error opening file");
        std::cerr << "errno: " << errno << std::endl;
    }
    ret = GmcExecDirect(stmt, command, STREAM_REFERENCE_MAX_SQL_LEN);
    EXPECT_EQ(ret, GMERR_OK);

    RdStreamExecDescT creates[] = {
        {"create table ts1 (name char(50), id integer, time integer, address char(50)) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream view view1 AS select file, id + 3, time * 3 + id, address FROM stream1;"},
        {"create stream sink sink1 AS select * FROM view1 into tsdb(ts1) with "
         "(batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(1);
    // 准备写入内容
    for (int32_t i = 0; i < 5; ++i) {
        char data[1024] = {0};
        ret = sprintf_s(data, 1024, "Cat_%03d\r", i);
        ssize_t bytes_written = write(g_fd, data, strlen(data));
        EXPECT_NE(bytes_written, -1);
    }
    close(g_fd);
    // 等待数据落盘，最长等待180s
    char *qryCmd = (char *)"select * from ts1;";
    uint32_t expRowNum = 5;
    uint32_t rowsCount = 0;
    uint32_t time = 0;
    while (rowsCount < expRowNum && time < 180) {
        ret = GmcExecDirect(stmt, qryCmd, strlen(qryCmd));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowsCount, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        sleep(1);
        ++time;
    }
    AW_MACRO_ASSERT_EQ_INT(expRowNum, rowsCount);
    // 校验ts表中数据
    qryCmd = (char *)"select * from ts1 order by name;";
    uint32_t expColNum = 4;
    char expFileContent[50][50] = {0};
    for (int32_t i = 0; i < 5; ++i) {
        ret = sprintf_s(expFileContent[i], 50, "Cat_%03d", i);
    }
    int64_t ids = 23 + 3;
    int64_t times = 14 * 3 + 23;
    RdCheckDataInTSTableOfStreamTableO11(stmt, qryCmd, expRowNum, expColNum, expFileContent, ids, times);
    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 057创建流表，后继为写入socket的sink
// 	成功
TEST_F(four_op_interact, STREAM_043_057)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, name char(50), address text) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer, time integer, name char(50), address text);"},
        {"create stream view view1 AS select (id + 3) * (6 % id) / 2, time % 5 * 6 + time / 4, name, address FROM "
         "stream1;"},
        {"create stream sink sink1 AS select * FROM view1 INTO "
         "SERVER_SOCKET('/tmp/stream_unix_sock1.sock') with (batch_window_size = '1')"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 连接server socket
    char socketAddr[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
    ret = TestGetResultCommand("cat pathConfig.txt |grep socketAddr1 |awk -F '[:]' '{print $2}' |sed 's/ //g'", NULL,
        socketAddr, sizeof(socketAddr));
    EXPECT_EQ(0, ret);
    int32_t socketFd = ConnectSinkSocket(socketAddr);
    sleep(2);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);
    uint32_t rowNum = 10;
    uint16_t len = 120;
    uint32_t g1 = 10;
    // 结构化写入数据
    RdStructWriteStreamTableO(stmt, vertexLabel, rowNum, len, g1, g1);
    // 检查数据
    char buf[65535] = {0};
    read(socketFd, buf, 65535);
    char expectBuf[7000] = {0};
    for (int i = 0; i < rowNum; i++) {
        char sub[100] = {0};
        (void)sprintf(
            sub, "%d,%d,name_%d,address_%d", (i + g1 + 3) * (6 % (i + g1)) / 2, (i + g1) % 5 * 6 + (i + g1) / 4, i, i);
        strcat(expectBuf, sub);
    }
    EXPECT_EQ(0, strcmp(buf, expectBuf));
    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}
// 058创建流表，后继为写入订阅的sink
// 	成功
TEST_F(four_op_interact, STREAM_043_058)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create stream table stream1 (id integer, time real, height real, name char(50), address text);"},
        {"create stream view view1 AS select id * 5 % 15, 7.9 + time * 1.5 - time / 8.8, (height + 2.8) * 3.6 + time, "
         "name, address FROM stream1;"},
        {"create stream sink sink1 AS select * FROM view1 INTO pubsub_channel with (batch_window_size = '1')"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "sink1",
                "comment":"this is a sub.",
                "schema_version": 0,
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] }
                ]  
            }
        )",
    };
    // 定义回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
        bool eof = false;
        bool isNull = false;
        uint32_t size = 0;
        while (true) {
            uint32_t ret = GmcFetch(stmt, &eof);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            if (eof || ret != GMERR_OK) {
                break;
            }
            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
            AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            // check id
            int64_t id = 0;
            int64_t val1 = received * 5 % 15;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &id, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(val1, id);
            // check time
            size = 8;
            double time = 0.0;
            double val2 = 7.9 + (received + 0.1) * 1.5 - (received + 0.1) / 8.8;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &time, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_DOUBLE(val2, time);
            // check height
            double height = 0.0;
            val2 = (received + 1.0 + 2.8) * 3.6 + (received + 0.1);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &height, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_DOUBLE(val2, height);
            // check name
            size = RD_STREAM_TABLE_T1_NAME_SIZE;
            char name[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            char expectName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            (void)snprintf_s(
                expectName, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", received);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &name, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(0, strcmp(expectName, name));
            // check address
            ret = GmcGetVertexPropertySizeById(stmt, 4, &size);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char address[size] = {0};
            char expectAddress[size] = {0};
            (void)snprintf_s(expectAddress, size, size, "address_%0ld", received);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &address, &size, &isNull);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectAddress, address));
            received++;
        }
    };
    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 创建订阅
    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[0].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);
    uint32_t rowNum = 200;
    uint16_t len = 120;
    double val1 = 0.1;
    double val2 = 1.0;
    // 结构化写入数据
    RdStructWriteStreamTableO2(stmt, vertexLabel, rowNum, val1, val2, len);
    uint32_t expectRowsCount = rowNum;
    RdTwentyOneWaitSnRecv(&received, expectRowsCount, 10);
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, received);
    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);
    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}
// 059创建流表，后继结点使用运算表达式，使用in/not in 的左表达式
// 	成功
TEST_F(four_op_interact, STREAM_043_059)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, name char(50), address text) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer, time integer, name char(50), address text);"},
        {"create stream view view1 AS select (id + time) * (id % 11), time % 6 + id, name, address FROM stream1 where "
         "time in (0,1,2,3,4,5,6,7);"},
        {"create stream sink sink1 AS select * FROM view1 INTO tsdb(ts1) with (batch_window_size = '1')"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);
    uint32_t rowNum = 200;
    uint16_t len = 120;
    uint32_t g1 = 5;
    // 结构化写入数据
    RdStructWriteStreamTableO(stmt, vertexLabel, rowNum, len, g1, g1);
    // 查询和校验时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 3;
    uint32_t expectColsCount = 4;
    RdCheckDataInTSTableOfStreamTableO7(stmt, selectTsName, expectRowsCount, expectColsCount, g1, g1);
    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}
// 060创建流表，后继结点使用运算表达式，使用in/not in 的左表达式，in {1,2.0}
// 	失败
TEST_F(four_op_interact, STREAM_043_060)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, name char(50), address text) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer, time integer, name char(50), address text);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *create = (char *)"create stream view view1 AS select id + 2, time * 6 + id, "
                           "name, address FROM stream1 where time in (0,1,2.0);";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 061创建流表，后继结点使用运算表达式作为in/not in 的左表达式，in {1,2.0}
// 	失败
TEST_F(four_op_interact, STREAM_043_061)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, name char(50), address text) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer, time integer, name char(50), address text);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *create = (char *)"create stream view view1 AS select id + 2, time * 6 + id, "
                           "name, address FROM stream1 where time * 6 + id in (0,1,2.0);";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 062创建流表，后继结点包含union
// 	成功
TEST_F(four_op_interact, STREAM_043_062)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, name char(50), address text) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer, time integer, name char(50), address text);"},
        {"create stream view view1 AS select (id + 3) * (6 % id) / 2, (time % 5) * 6 + time / 4, name, address FROM "
         "stream1;"},
        {"create stream view view2 AS select (id + 3) * (6 % id) / 2, (time % 5) * 6 + time / 4, name, address FROM "
         "stream1;"},
        {"create stream sink sink1 AS select * FROM TABLE(UNION(TABLE view1, TABLE view2)) INTO tsdb(ts1) with "
         "(batch_window_size = '1')"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);
    uint32_t rowNum = 200;
    uint16_t len = 120;
    uint32_t g1 = 10;
    // 结构化写入数据
    RdStructWriteStreamTableO(stmt, vertexLabel, rowNum, len, g1, g1);

    // 查询和校验时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = rowNum * 2;
    uint32_t expectColsCount = 4;
    RdCheckDataInTSTableOfStreamTableO12(stmt, selectTsName, expectRowsCount, expectColsCount, g1, g1);
    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream view view2;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}
// 063创建流表，alter union一个包含union的view
// 	成功
TEST_F(four_op_interact, STREAM_043_063)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, name char(50), address text) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer, time integer, name char(50), address text);"},
        {"create stream view view1 AS select (id + 3) * (6 % id) / 2, (time % 5) * 6 + time / 4, name, address FROM "
         "stream1;"},
        {"create stream view view2 AS select (id + 3) * (6 % id) / 2, (time % 5) * 6 + time / 4, name, address FROM "
         "stream1;"},
        {"create stream sink sink1 AS select * FROM TABLE(UNION(TABLE view1, TABLE view2)) INTO tsdb(ts1) with "
         "(batch_window_size = '1')"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *alterCmd = "alter stream sink sink1 alter from_union drop view2;";
    ret = GmcExecDirect(stmt, alterCmd, strlen(alterCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);
    uint32_t rowNum = 200;
    uint16_t len = 120;
    uint32_t g1 = 10;
    // 结构化写入数据
    RdStructWriteStreamTableO(stmt, vertexLabel, rowNum, len, g1, g1);

    // 查询和校验时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = rowNum;
    uint32_t expectColsCount = 4;
    RdCheckDataInTSTableOfStreamTableO5(stmt, selectTsName, expectRowsCount, expectColsCount, g1, g1);
    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream view view2;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}
// 064创建流表，后继结点包含运算表达式，dispatch by a
// 	成功
TEST_F(four_op_interact, STREAM_043_064)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, name char(50), address text) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer, time integer, name char(50), address text);"},
        {"create stream view view1 AS select (id + 3) * (6 % id) / 2, (time % 5) * 6 + time / 4, name, address FROM "
         "stream1 dispatch by _column_1;"},
        {"create stream sink sink1 AS select * FROM table(dispatch(table view1, 2)) INTO tsdb(ts1) with "
         "(batch_window_size = '1')"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);
    uint32_t rowNum = 200;
    uint16_t len = 120;
    uint32_t g1 = 10;
    // 结构化写入数据
    RdStructWriteStreamTableO(stmt, vertexLabel, rowNum, len, g1, g1);
    // 查询和校验时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 1;
    uint32_t expectColsCount = 4;
    RdCheckDataInTSTableOfStreamTableO5(stmt, selectTsName, expectRowsCount, expectColsCount, g1, g1);
    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 065创建流表，后继结点包含运算表达式，流表带dispatch by a
// 	成功
TEST_F(four_op_interact, STREAM_043_065)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, name char(50), address text) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer, time integer, name char(50), address text) dispatch by id;"},
        {"create stream view view1 AS select (id + 3) * (6 % id) / 2, (time % 5) * 6 + time / 4, "
         "name, address FROM table(dispatch(table stream1, "
         "10));"},
        {"create stream sink sink1 AS select * FROM view1 INTO tsdb(ts1) with (batch_window_size = '1')"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);
    uint32_t rowNum = 200;
    uint16_t len = 120;
    uint32_t g1 = 10;
    // 结构化写入数据
    RdStructWriteStreamTableO(stmt, vertexLabel, rowNum, len, g1, g1);
    // 查询和校验时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 1;
    uint32_t expectColsCount = 4;
    RdCheckDataInTSTableOfStreamTableO5(stmt, selectTsName, expectRowsCount, expectColsCount, g1, g1);
    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}
// 066创建流表，后继为alter where条件包含简单运算表达式
// 	成功
TEST_F(four_op_interact, STREAM_043_066)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, name char(50), address text) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer, time integer, name char(50), address text);"},
        {"create stream view view1 AS select id * 3, time / 2, name, address FROM stream1;"},
        {"create stream sink sink1 AS select * FROM view1 INTO tsdb(ts1) with (batch_window_size = '1')"},
        {"alter stream view view1 alter where as (id * 3) - 10 < 100;"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);
    uint32_t rowNum = 200;
    uint16_t len = 120;
    uint32_t g1 = 5;
    // 结构化写入数据
    RdStructWriteStreamTableO(stmt, vertexLabel, rowNum, len, g1, g1);
    // 查询和校验时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 32;
    uint32_t expectColsCount = 4;
    uint32_t formula1 = 3;
    uint32_t formula2 = 2;
    char op1[] = "*";
    char op2[] = "\\";
    RdCheckDataInTSTableOfStreamTableO(
        stmt, selectTsName, expectRowsCount, expectColsCount, formula1, formula2, op1, op2, g1, g1);
    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 067创建流表，后继为alter where条件包含复杂运算表达式
// 	成功
TEST_F(four_op_interact, STREAM_043_067)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, name char(50), address text) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer, time integer, name char(50), address text);"},
        {"create stream view view1 AS select (id + 3) * (6 % id) + id % 5 - 8, (time % 2 + 4) * 6 + time / 3, name, "
         "address FROM stream1;"},
        {"create stream sink sink1 AS select * FROM view1 INTO tsdb(ts1) with (batch_window_size = '1')"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *create = (char *)"alter stream view view1 alter where as ((time % 2 + 4) * 6 + time / 3) < 200;";
    ret = GmcExecDirect(stmt, create, strlen(create));
    AW_MACRO_EXPECT_EQ_INT(GMERR_SEMANTIC_ERROR, ret);

    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 068创建流表，后继结点使用运算表达式作为比较运算符左表达式（< > != ≥ ≤）,where a + b > 1
// 	成功
TEST_F(four_op_interact, STREAM_043_068)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, name char(50), address text) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer, time integer, name char(50), address text);"},
        {"create stream view view1 AS select id * 3, time / 2, name, address FROM stream1 where id + time < 100;"},
        {"create stream sink sink1 AS select * FROM view1 INTO tsdb(ts1) with (batch_window_size = '1')"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);
    uint32_t rowNum = 200;
    uint16_t len = 120;
    uint32_t g1 = 5;
    // 结构化写入数据
    RdStructWriteStreamTableO(stmt, vertexLabel, rowNum, len, g1, g1);

    // 查询和校验时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 45;
    uint32_t expectColsCount = 4;
    uint32_t formula1 = 3;
    uint32_t formula2 = 2;
    char op1[] = "*";
    char op2[] = "\\";
    RdCheckDataInTSTableOfStreamTableO(
        stmt, selectTsName, expectRowsCount, expectColsCount, formula1, formula2, op1, op2, g1, g1);
    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}
// 069创建流表，后继结点使用运算表达式作为比较运算符左表达式（< > != ≥ ≤）,where a + b > c< > != ≥ ≤）,where a + b > c
// 	失败
TEST_F(four_op_interact, STREAM_043_069)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, height integer, name char(50), address text) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer, time integer, height integer, name char(50), address text);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *create = (char *)"create stream view view1 AS select id * 3, time / 2, height + 8, "
                           "name, address FROM stream1 where id + time > height;";
    ret = GmcExecDirect(stmt, create, strlen(create));
    AW_MACRO_EXPECT_EQ_INT(GMERR_SEMANTIC_ERROR, ret);
    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}
// 070创建流表，后继结点使用运算表达式作为比较运算符左表达式（< > != ≥ ≤）,where嵌套，where (a + 3)*2 > 10
// 	成功
TEST_F(four_op_interact, STREAM_043_070)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, name char(50), address text) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer, time integer, name char(50), address text);"},
        {"create stream view view1 AS select 5 + id * 3 - id / 2, (time + 4) * 6 % 3, name, address FROM stream1 where "
         "(id + 3) * 2  > 10;"},
        {"create stream sink sink1 AS select * FROM view1 INTO tsdb(ts1) with (batch_window_size = '1')"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);
    uint32_t rowNum = 200;
    uint16_t len = 120;
    uint32_t g1 = 5;
    // 结构化写入数据
    RdStructWriteStreamTableO(stmt, vertexLabel, rowNum, len, g1, g1);

    // 查询和校验时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = rowNum;
    uint32_t expectColsCount = 4;
    RdCheckDataInTSTableOfStreamTableO3(stmt, selectTsName, expectRowsCount, expectColsCount, g1, g1);
    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}
// 071创建流表，后继结点使用运算表达式作为比较运算符左表达式（< > != ≥ ≤）,where嵌套，where (a + 3)*2 > b
// 	失败
TEST_F(four_op_interact, STREAM_043_071)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, name char(50), address text) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer, time integer, name char(50), address text);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *create = (char *)"create stream view view1 AS select 5 + id * 3 - id / 2, (time + 4) * 6 % 3, "
                           "name, address FROM stream1 where (id + 3) * 2  > time;";
    ret = GmcExecDirect(stmt, create, strlen(create));
    AW_MACRO_EXPECT_EQ_INT(GMERR_SEMANTIC_ERROR, ret);
    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}
// 071创建流表，后继结点使用运算表达式作为逻辑运算符左表达式(and、or),where a + b > 1 and c < 10
// 	成功
TEST_F(four_op_interact, STREAM_043_072)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create stream table stream1 (id integer, time real, height real, name char(50), address text);"},
        {"create stream view view1 AS select id * 5 % 15, 7.9 + time * 1.5 - time / 8.8, (height + 2.8) * 3.6 + time, "
         "name, address FROM stream1 where (id * 5 % 15) > -1 and time < 10.1;"},
        {"create stream sink sink1 AS select * FROM view1 INTO pubsub_channel with (batch_window_size = '1')"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "sink1",
                "comment":"this is a sub.",
                "schema_version": 0,
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] }
                ]  
            }
        )",
    };
    // 定义回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
        bool eof = false;
        bool isNull = false;
        uint32_t size = 0;
        while (true) {
            uint32_t ret = GmcFetch(stmt, &eof);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            if (eof || ret != GMERR_OK) {
                break;
            }
            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
            AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            // check id
            int64_t id = 0;
            int64_t val1 = received * 5 % 15;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &id, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(val1, id);
            // check time
            size = 8;
            double time = 0.0;
            double val2 = 7.9 + (received + 0.1) * 1.5 - (received + 0.1) / 8.8;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &time, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_DOUBLE(val2, time);
            // check height
            double height = 0.0;
            val2 = (received + 1.0 + 2.8) * 3.6 + (received + 0.1);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &height, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_DOUBLE(val2, height);
            // check name
            size = RD_STREAM_TABLE_T1_NAME_SIZE;
            char name[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            char expectName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            (void)snprintf_s(
                expectName, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", received);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &name, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(0, strcmp(expectName, name));
            // check address
            ret = GmcGetVertexPropertySizeById(stmt, 4, &size);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char address[size] = {0};
            char expectAddress[size] = {0};
            (void)snprintf_s(expectAddress, size, size, "address_%0ld", received);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &address, &size, &isNull);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectAddress, address));
            received++;
        }
    };
    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 创建订阅
    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[0].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);
    uint32_t rowNum = 200;
    uint16_t len = 120;
    double val1 = 0.1;
    double val2 = 1.0;
    // 结构化写入数据
    RdStructWriteStreamTableO2(stmt, vertexLabel, rowNum, val1, val2, len);
    uint32_t expectRowsCount = 10;
    RdTwentyOneWaitSnRecv(&received, expectRowsCount, 10);
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, received);
    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);
    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}
// 073创建流表，后继结点使用运算表达式作为模糊查询左表达式(like)
// 	失败
TEST_F(four_op_interact, STREAM_043_073)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, name char(50), address text) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer, time integer, name char(50), address text);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *create = (char *)"create stream view view1 AS select (id + 2) * 8, time + 6, name, address FROM stream1 "
                           "where (id + 2) * 8 like '%3%';";
    ret = GmcExecDirect(stmt, create, strlen(create));
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}
