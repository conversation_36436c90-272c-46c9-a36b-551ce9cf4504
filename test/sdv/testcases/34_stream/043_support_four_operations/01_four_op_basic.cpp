/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description:流计算支持四则运算门槛、基本、复杂运算用例
 * Author: moxiaotong
 * Create: 2025-05-19
 */
#include "gtest/gtest.h"
#include "securec.h"
#include "gmc_sql.h"
#include "t_rd_sn.h"
#include "gmc_persist.h"
#include "rd_feature_stream.h"
#include "text_util.h"

class four_op_basic : public testing::Test {
public:
    static GmcConnT *conn;
    static GmcStmtT *stmt;
    bool hasFinishTest = false;
    void RdFinishTest();
    static void SetUpTestCase();
    static void TearDownTestCase();
    virtual void SetUp();
    virtual void TearDown();
};

GmcConnT *four_op_basic::conn = NULL;
GmcStmtT *four_op_basic::stmt = NULL;

void four_op_basic::SetUpTestCase()
{
    int32_t ret;
    ret = RdStreamEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void four_op_basic::TearDownTestCase()
{
    int32_t ret;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdStreamEnvClean();
    EXPECT_EQ(GMERR_OK, ret);
}

void four_op_basic::SetUp()
{
    hasFinishTest = false;
    int32_t ret = RdGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void four_op_basic::TearDown()
{
    int32_t ret;
    if (!hasFinishTest) {
        RdStreamExecDescT drops[] = {
            {"drop table ts1;"},
            {"drop stream sink sink1;"},
            {"drop stream view view1;"},
            {"drop stream table stream1;"},
        };
        ret = RdStreamExecSql(stmt, drops, sizeof(drops));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = RdGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

void four_op_basic::RdFinishTest()
{
    hasFinishTest = true;
}

// 创建流表，后继结点包含加法，结构化写入数据
// 	成功
TEST_F(four_op_basic, STREAM_043_001)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, name char(50), address text) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer, time integer, name char(50), address text);"},
        {"create stream view view1 AS select id + 2, time + 6, name, address FROM stream1;"},
        {"create stream sink sink1 AS select * FROM view1 INTO tsdb(ts1) with (batch_window_size = '1')"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);
    uint32_t rowNum = 200;
    uint16_t len = 120;
    // 结构化写入数据
    RdStructWriteStreamTableO(stmt, vertexLabel, rowNum, len);

    // 查询和校验时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = rowNum;
    uint32_t expectColsCount = 4;
    uint32_t formula1 = 2;
    uint32_t formula2 = 6;
    char op1[] = "+";
    char op2[] = "+";
    RdCheckDataInTSTableOfStreamTableO(
        stmt, selectTsName, expectRowsCount, expectColsCount, formula1, formula2, op1, op2);

    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 创建流表，后继结点包含减法，结构化写入数据
// 	成功
TEST_F(four_op_basic, STREAM_043_002)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, age integer, name char(50), address text) "
         "with (time_col = 'age', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer, time integer, name char(50), address text);"},
        {"create stream view view1 AS select id - 2, time - 8, name, address FROM stream1;"},
        {"create stream sink sink1 AS select * FROM view1 INTO tsdb(ts1) with (batch_window_size = '1')"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);
    uint32_t rowNum = 200;
    uint16_t len = 120;
    uint32_t g1 = 10;
    // 结构化写入数据
    RdStructWriteStreamTableO(stmt, vertexLabel, rowNum, len, g1, g1);

    // 查询和校验时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = rowNum;
    uint32_t expectColsCount = 4;
    uint32_t formula1 = 2;
    uint32_t formula2 = 8;
    char op1[] = "-";
    char op2[] = "-";
    RdCheckDataInTSTableOfStreamTableO(
        stmt, selectTsName, expectRowsCount, expectColsCount, formula1, formula2, op1, op2, g1, g1);

    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 创建流表，后继结点包含乘法，结构化写入数据
// 	成功
TEST_F(four_op_basic, STREAM_043_003)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, name char(50), address text) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer, time integer, name char(50), address text);"},
        {"create stream view view1 AS select id * 4, time * 5, name, address FROM stream1;"},
        {"create stream sink sink1 AS select * FROM view1 INTO tsdb(ts1) with (batch_window_size = '1')"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);
    uint32_t rowNum = 200;
    uint16_t len = 120;
    // 结构化写入数据
    RdStructWriteStreamTableO(stmt, vertexLabel, rowNum, len);
    // 查询和校验时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = rowNum;
    uint32_t expectColsCount = 4;
    uint32_t formula1 = 4;
    uint32_t formula2 = 5;
    char op1[] = "*";
    char op2[] = "*";
    RdCheckDataInTSTableOfStreamTableO(
        stmt, selectTsName, expectRowsCount, expectColsCount, formula1, formula2, op1, op2);
    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 创建流表，后继结点包含除法，结构化写入数据
// 	成功
TEST_F(four_op_basic, STREAM_043_004)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, name char(50), address text) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer, time integer, name char(50), address text);"},
        {"create stream view view1 AS select id / 2, time / 3, name, address FROM stream1;"},
        {"create stream sink sink1 AS select * FROM view1 INTO tsdb(ts1) with (batch_window_size = '1')"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);
    uint32_t rowNum = 200;
    uint16_t len = 120;
    // // 结构化写入数据
    RdStructWriteStreamTableO(stmt, vertexLabel, rowNum, len);

    // 查询和校验时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = rowNum;
    uint32_t expectColsCount = 4;
    uint32_t formula1 = 2;
    uint32_t formula2 = 3;
    char op1[] = "\\";
    char op2[] = "\\";
    RdCheckDataInTSTableOfStreamTableO(
        stmt, selectTsName, expectRowsCount, expectColsCount, formula1, formula2, op1, op2);

    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 创建流表，后继结点包含mod，结构化写入数据
// 	成功
TEST_F(four_op_basic, STREAM_043_005)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, name char(50), address text) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer, time integer, name char(50), address text);"},
        {"create stream view view1 AS select id % 2, time % 3, name, address FROM stream1;"},
        {"create stream sink sink1 AS select * FROM view1 INTO tsdb(ts1) with (batch_window_size = '1')"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);
    uint32_t rowNum = 200;
    uint16_t len = 120;
    // // 结构化写入数据
    RdStructWriteStreamTableO(stmt, vertexLabel, rowNum, len);

    // 查询和校验时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = rowNum;
    uint32_t expectColsCount = 4;
    uint32_t formula1 = 2;
    uint32_t formula2 = 3;
    char op1[] = "%";
    char op2[] = "%";
    RdCheckDataInTSTableOfStreamTableO(
        stmt, selectTsName, expectRowsCount, expectColsCount, formula1, formula2, op1, op2);

    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 创建流表，后继结点包含加法，异步写入数据
// 	成功
TEST_F(four_op_basic, STREAM_043_006)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer);"},
        {"create stream view view1 AS select id + 4, name, water_mark + 6, event_time + 10 FROM stream1;"},
        {"create stream sink sink1 AS select * from view1 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);
    const char *strTblName = "stream1";
    int64_t rowNum = 5;
    int64_t id[rowNum] = {1, 2, 3, 4, 5};
    int64_t water_mark[rowNum] = {1, 2, 3, 4, 5};
    int64_t event_time[rowNum] = {1, 2, 3, 4, 5};
    RdAsyncWriteStreamTableA(strTblName, rowNum, id, water_mark, event_time);

    // 查询和校验时序表数据
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expectRowsCount = rowNum;
    uint32_t expectColsCount = 4;
    int64_t expectId[rowNum] = {5, 6, 7, 8, 9};
    int64_t expectWater_mark[rowNum] = {7, 8, 9, 10, 11};
    int64_t expectEvent_time[rowNum] = {11, 12, 13, 14, 15};
    RdCheckDataInTSTableOfStreamTableO2(
        stmt, qryCmd, expectRowsCount, expectColsCount, expectId, id, expectWater_mark, expectEvent_time);
    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 创建流表，后继结点包含减法，异步写入数据
// 	成功
TEST_F(four_op_basic, STREAM_043_007)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer);"},
        {"create stream view view1 AS select id - 4, name, water_mark - 3, event_time - 10 FROM stream1;"},
        {"create stream sink sink1 AS select * from view1 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);
    const char *strTblName = "stream1";
    int64_t rowNum = 5;
    int64_t id[rowNum] = {11, 12, 13, 14, 15};
    int64_t water_mark[rowNum] = {11, 12, 13, 14, 15};
    int64_t event_time[rowNum] = {11, 12, 13, 14, 15};
    RdAsyncWriteStreamTableA(strTblName, rowNum, id, water_mark, event_time);

    // 查询和校验时序表数据
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expectRowsCount = rowNum;
    uint32_t expectColsCount = 4;
    int64_t expectId[rowNum] = {7, 8, 9, 10, 11};
    int64_t expectWater_mark[rowNum] = {8, 9, 10, 11, 12};
    int64_t expectEvent_time[rowNum] = {1, 2, 3, 4, 5};
    RdCheckDataInTSTableOfStreamTableO2(
        stmt, qryCmd, expectRowsCount, expectColsCount, expectId, id, expectWater_mark, expectEvent_time);
    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 创建流表，后继结点包含乘法，异步写入数据
// 	成功
TEST_F(four_op_basic, STREAM_043_008)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer);"},
        {"create stream view view1 AS select id * 2, name, water_mark * 6, event_time * 10 FROM stream1;"},
        {"create stream sink sink1 AS select * from view1 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);
    const char *strTblName = "stream1";
    int64_t rowNum = 5;
    int64_t id[rowNum] = {1, 2, 3, 4, 5};
    int64_t water_mark[rowNum] = {1, 2, 3, 4, 5};
    int64_t event_time[rowNum] = {1, 2, 3, 4, 5};
    RdAsyncWriteStreamTableA(strTblName, rowNum, id, water_mark, event_time);

    // 查询和校验时序表数据
    // char *selectTsName = (char *)"ts1";
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expectRowsCount = rowNum;
    uint32_t expectColsCount = 4;
    int64_t expectId[rowNum] = {2, 4, 6, 8, 10};
    int64_t expectWater_mark[rowNum] = {6, 12, 18, 24, 30};
    int64_t expectEvent_time[rowNum] = {10, 20, 30, 40, 50};
    RdCheckDataInTSTableOfStreamTableO2(
        stmt, qryCmd, expectRowsCount, expectColsCount, expectId, id, expectWater_mark, expectEvent_time);

    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 创建流表，后继结点包含除法，异步写入数据
// 	成功
TEST_F(four_op_basic, STREAM_043_009)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer);"},
        {"create stream view view1 AS select id / 2, name, water_mark / 3, event_time / 10 FROM stream1;"},
        {"create stream sink sink1 AS select * from view1 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);
    const char *strTblName = "stream1";
    int64_t rowNum = 5;
    int64_t id[rowNum] = {1, 2, 3, 4, 5};
    int64_t water_mark[rowNum] = {1, 2, 3, 4, 5};
    int64_t event_time[rowNum] = {10, 20, 30, 40, 50};
    RdAsyncWriteStreamTableA(strTblName, rowNum, id, water_mark, event_time);

    // 查询和校验时序表数据
    // char *selectTsName = (char *)"ts1";
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expectRowsCount = rowNum;
    uint32_t expectColsCount = 4;
    int64_t expectId[rowNum] = {0, 1, 1, 2, 2};
    int64_t expectWater_mark[rowNum] = {0, 0, 1, 1, 1};
    int64_t expectEvent_time[rowNum] = {1, 2, 3, 4, 5};
    RdCheckDataInTSTableOfStreamTableO2(
        stmt, qryCmd, expectRowsCount, expectColsCount, expectId, id, expectWater_mark, expectEvent_time);

    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 创建流表，后继结点为写入订阅的sink，包含除法，异步写入数据，1/3
// 	成功
TEST_F(four_op_basic, STREAM_043_010)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    RdStreamExecDescT creates[] = {
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer);"},
        {"create stream view view1 AS select id / 2, name, water_mark / 3, event_time / 10 FROM stream1;"},
        {"create stream sink sink1 AS select * from view1 into pubsub_channel with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "sink1",
                "comment":"this is a sub.",
                "schema_version": 0,
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] }
                ]
            }
        )",
    };
    // 定义回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
        bool eof = false;
        bool isNull = false;
        uint32_t size = 0;
        while (true) {
            uint32_t ret = GmcFetch(stmt, &eof);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            if (eof || ret != GMERR_OK) {
                break;
            }

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
            AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            // check id
            int64_t id = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &id, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT((received + 1) / 2, id);
            // check name
            size = RD_STREAM_TABLE_T1_NAME_SIZE;
            char name[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            char expectName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            (void)snprintf_s(
                expectName, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", received + 1);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(0, strcmp(expectName, name));
            // check time
            size = 8;
            int64_t time = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &time, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT((received + 1) / 3, time);
            // check age
            size = 8;
            int64_t age = 0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &age, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(received + 1, age);

            received++;
        }
    };
    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 创建订阅
    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[0].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);
    const char *strTblName = "stream1";
    int64_t rowNum = 5;
    int64_t id[rowNum] = {1, 2, 3, 4, 5};
    int64_t water_mark[rowNum] = {1, 2, 3, 4, 5};
    int64_t event_time[rowNum] = {10, 20, 30, 40, 50};
    RdAsyncWriteStreamTableA(strTblName, rowNum, id, water_mark, event_time);
    uint32_t expectRowsCount = rowNum;
    RdTwentyOneWaitSnRecv(&received, expectRowsCount, 10);
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, received);
    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);
    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 创建流表，后继结点包含mod，异步写入数据
// 	成功
TEST_F(four_op_basic, STREAM_043_011)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50), water_mark integer, event_time integer) "
         "with (time_col = 'id', interval= '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer, name char(50), water_mark integer, event_time integer);"},
        {"create stream view view1 AS select id % 6, name, water_mark % 3, event_time % 10 FROM stream1;"},
        {"create stream sink sink1 AS select * from view1 into tsdb(ts1) with (batch_window_size = 1);"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);
    const char *strTblName = "stream1";
    int64_t rowNum = 5;
    int64_t id[rowNum] = {1, 2, 3, 4, 5};
    int64_t water_mark[rowNum] = {1, 2, 3, 4, 5};
    int64_t event_time[rowNum] = {11, 22, 33, 44, 55};
    RdAsyncWriteStreamTableA(strTblName, rowNum, id, water_mark, event_time);

    // 查询和校验时序表数据
    // char *selectTsName = (char *)"ts1";
    char *qryCmd = (char *)"select * from ts1 order by id;";
    uint32_t expectRowsCount = rowNum;
    uint32_t expectColsCount = 4;
    int64_t expectId[rowNum] = {1, 2, 3, 4, 5};
    int64_t expectWater_mark[rowNum] = {1, 2, 0, 1, 2};
    int64_t expectEvent_time[rowNum] = {1, 2, 3, 4, 5};
    RdCheckDataInTSTableOfStreamTableO2(
        stmt, qryCmd, expectRowsCount, expectColsCount, expectId, id, expectWater_mark, expectEvent_time);

    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 012创建流表，后继结点为写入订阅的sink，包含浮点数加法，结构化写入数据
// 	成功
TEST_F(four_op_basic, STREAM_043_012)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    RdStreamExecDescT creates[] = {
        {"create stream table stream1 (id integer, time real, height real, name char(50), address text);"},
        {"create stream view view1 AS select id * 2, time + 3.6, height + 10.7, name, address FROM stream1;"},
        {"create stream sink sink1 AS select * FROM view1 INTO pubsub_channel with (batch_window_size = '1')"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "sink1",
                "comment":"this is a sub.",
                "schema_version": 0,
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] }
                ]  
            }
        )",
    };
    // 定义回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
        bool eof = false;
        bool isNull = false;
        uint32_t size = 0;
        while (true) {
            uint32_t ret = GmcFetch(stmt, &eof);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            if (eof || ret != GMERR_OK) {
                break;
            }
            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
            AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            // check id
            int64_t id = 0;
            int64_t val1 = received * 2;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &id, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(val1, id);
            // check time
            size = 8;
            double time = 0.0;
            double val2 = received + 3.6 + 0.1;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &time, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            // check height
            double height = 0.0;
            val2 = received + 10.7 + 0.1;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &height, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            // check name
            size = RD_STREAM_TABLE_T1_NAME_SIZE;
            char name[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            char expectName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            (void)snprintf_s(
                expectName, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", received);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &name, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(0, strcmp(expectName, name));
            // check address
            ret = GmcGetVertexPropertySizeById(stmt, 4, &size);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char address[size] = {0};
            char expectAddress[size] = {0};
            (void)snprintf_s(expectAddress, size, size, "address_%0ld", received);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &address, &size, &isNull);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectAddress, address));
            received++;
        }
    };
    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 创建订阅
    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[0].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);
    uint32_t rowNum = 200;
    uint16_t len = 120;
    double val = 0.1;
    // 结构化写入数据
    RdStructWriteStreamTableO2(stmt, vertexLabel, rowNum, val, val, len);
    uint32_t expectRowsCount = rowNum;
    RdTwentyOneWaitSnRecv(&received, expectRowsCount, 10);
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, received);
    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);
    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 013创建流表，后继结点为写入订阅的sink，包含浮点数减法，结构化写入数据
// 	成功
TEST_F(four_op_basic, STREAM_043_013)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    RdStreamExecDescT creates[] = {
        {"create stream table stream1 (id integer, time real, height real, name char(50), address text);"},
        {"create stream view view1 AS select id / 3, time - 3.6, height - 8.2, name, address FROM stream1;"},
        {"create stream sink sink1 AS select * FROM view1 INTO pubsub_channel with (batch_window_size = '1')"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "sink1",
                "comment":"this is a sub.",
                "schema_version": 0,
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] }
                ]  
            }
        )",
    };
    // 定义回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
        bool eof = false;
        bool isNull = false;
        uint32_t size = 0;
        while (true) {
            uint32_t ret = GmcFetch(stmt, &eof);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            if (eof || ret != GMERR_OK) {
                break;
            }
            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
            AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            // check id
            int64_t id = 0;
            int64_t val1 = received / 3;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &id, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(val1, id);
            // check time
            size = 8;
            double time = 0.0;
            double val2 = received - 3.6 + 10.1;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &time, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            // check height
            double height = 0.0;
            val2 = received - 8.2 + 10.1;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &height, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            // check name
            size = RD_STREAM_TABLE_T1_NAME_SIZE;
            char name[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            char expectName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            (void)snprintf_s(
                expectName, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", received);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &name, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(0, strcmp(expectName, name));
            // check address
            ret = GmcGetVertexPropertySizeById(stmt, 4, &size);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char address[size] = {0};
            char expectAddress[size] = {0};
            (void)snprintf_s(expectAddress, size, size, "address_%0ld", received);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &address, &size, &isNull);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectAddress, address));
            received++;
        }
    };
    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 创建订阅
    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[0].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);
    uint32_t rowNum = 200;
    uint16_t len = 120;
    double val = 10.1;
    // 结构化写入数据
    RdStructWriteStreamTableO2(stmt, vertexLabel, rowNum, val, val, len);
    uint32_t expectRowsCount = rowNum;
    RdTwentyOneWaitSnRecv(&received, expectRowsCount, 10);
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, received);
    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);
    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 014创建流表，后继结点为写入订阅的sink，包含浮点数乘法，结构化写入数据
// 	成功
TEST_F(four_op_basic, STREAM_043_014)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create stream table stream1 (id integer, time real, height real, name char(50), address text);"},
        {"create stream view view1 AS select id % 4, time * 3.1, height * 7.2, name, address FROM stream1;"},
        {"create stream sink sink1 AS select * FROM view1 INTO pubsub_channel with (batch_window_size = '1')"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "sink1",
                "comment":"this is a sub.",
                "schema_version": 0,
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] }
                ]  
            }
        )",
    };
    // 定义回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
        bool eof = false;
        bool isNull = false;
        uint32_t size = 0;
        while (true) {
            uint32_t ret = GmcFetch(stmt, &eof);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            if (eof || ret != GMERR_OK) {
                break;
            }
            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
            AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            // check id
            int64_t id = 0;
            int64_t val1 = received % 4;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &id, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(val1, id);
            // check time
            size = 8;
            double time = 0.0;
            double val2 = (received + 0.1) * 3.1;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &time, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_DOUBLE(val2, time);
            // check height
            double height = 0.0;
            val2 = (received + 0.1) * 7.2;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &height, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_DOUBLE(val2, height);
            // check name
            size = RD_STREAM_TABLE_T1_NAME_SIZE;
            char name[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            char expectName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            (void)snprintf_s(
                expectName, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", received);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &name, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(0, strcmp(expectName, name));
            // check address
            ret = GmcGetVertexPropertySizeById(stmt, 4, &size);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char address[size] = {0};
            char expectAddress[size] = {0};
            (void)snprintf_s(expectAddress, size, size, "address_%0ld", received);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &address, &size, &isNull);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectAddress, address));
            received++;
        }
    };
    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 创建订阅
    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[0].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);
    uint32_t rowNum = 200;
    uint16_t len = 120;
    double val = 0.1;
    // 结构化写入数据
    RdStructWriteStreamTableO2(stmt, vertexLabel, rowNum, val, val, len);
    uint32_t expectRowsCount = rowNum;
    RdTwentyOneWaitSnRecv(&received, expectRowsCount, 10);
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, received);
    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);
    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 015创建流表，后继结点为写入订阅的sink，包含浮点数除法，结构化写入数据，1.0/3.0
// 	成功
TEST_F(four_op_basic, STREAM_043_015)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create stream table stream1 (id integer, time real, height real, name char(50), address text);"},
        {"create stream view view1 AS select (id + 6) % 9, time / 1.5, height / 3.0, name, address FROM stream1;"},
        {"create stream sink sink1 AS select * FROM view1 INTO pubsub_channel with (batch_window_size = '1')"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "sink1",
                "comment":"this is a sub.",
                "schema_version": 0,
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] }
                ]  
            }
        )",
    };
    // 定义回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
        bool eof = false;
        bool isNull = false;
        uint32_t size = 0;
        while (true) {
            uint32_t ret = GmcFetch(stmt, &eof);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            if (eof || ret != GMERR_OK) {
                break;
            }
            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
            AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            // check id
            int64_t id = 0;
            int64_t val1 = (received + 6) % 9;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &id, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(val1, id);
            // check time
            size = 8;
            double time = 0.0;
            double val2 = (received + 0.1) / 1.5;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &time, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_DOUBLE(val2, time);
            // check height
            double height = 0.0;
            val2 = (received + 1.0) / 3.0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &height, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_DOUBLE(val2, height);
            // check name
            size = RD_STREAM_TABLE_T1_NAME_SIZE;
            char name[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            char expectName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            (void)snprintf_s(
                expectName, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", received);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &name, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(0, strcmp(expectName, name));
            // check address
            ret = GmcGetVertexPropertySizeById(stmt, 4, &size);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char address[size] = {0};
            char expectAddress[size] = {0};
            (void)snprintf_s(expectAddress, size, size, "address_%0ld", received);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &address, &size, &isNull);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectAddress, address));
            received++;
        }
    };
    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 创建订阅
    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[0].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);
    uint32_t rowNum = 200;
    uint16_t len = 120;
    double val1 = 0.1;
    double val2 = 1.0;
    // 结构化写入数据
    RdStructWriteStreamTableO2(stmt, vertexLabel, rowNum, val1, val2, len);
    uint32_t expectRowsCount = rowNum;
    RdTwentyOneWaitSnRecv(&received, expectRowsCount, 10);
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, received);
    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);
    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 016创建流表，后继结点为写入订阅的sink，包含浮点数数字常量运算，select常量3.1415926+3.0，结构化写入数据
// 	成功
TEST_F(four_op_basic, STREAM_043_016)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create stream table stream1 (id integer, time real, height real, name char(50), address text);"},
        {"create stream view view1 AS select (5 + 6) % 9, 3.1415926 + 3.0, 3.1415926 * 2.0 + 8.3, name, address FROM "
         "stream1;"},
        {"create stream sink sink1 AS select * FROM view1 INTO pubsub_channel with (batch_window_size = '1')"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "sink1",
                "comment":"this is a sub.",
                "schema_version": 0,
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] }
                ]  
            }
        )",
    };
    // 定义回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
        bool eof = false;
        bool isNull = false;
        uint32_t size = 0;
        while (true) {
            uint32_t ret = GmcFetch(stmt, &eof);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            if (eof || ret != GMERR_OK) {
                break;
            }
            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
            AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            // check id
            int64_t id = 0;
            int64_t val1 = (5 + 6) % 9;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &id, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(val1, id);
            // check time
            size = 8;
            double time = 0.0;
            double val2 = 3.1415926 + 3.0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &time, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_DOUBLE(val2, time);
            // check height
            double height = 0.0;
            val2 = 3.1415926 * 2.0 + 8.3;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &height, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_DOUBLE(val2, height);
            // check name
            size = RD_STREAM_TABLE_T1_NAME_SIZE;
            char name[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            char expectName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            (void)snprintf_s(
                expectName, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", received);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &name, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(0, strcmp(expectName, name));
            // check address
            ret = GmcGetVertexPropertySizeById(stmt, 4, &size);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char address[size] = {0};
            char expectAddress[size] = {0};
            (void)snprintf_s(expectAddress, size, size, "address_%0ld", received);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &address, &size, &isNull);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectAddress, address));
            received++;
        }
    };
    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 创建订阅
    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[0].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);
    uint32_t rowNum = 200;
    uint16_t len = 120;
    double val1 = 0.1;
    double val2 = 1.0;
    // 结构化写入数据
    RdStructWriteStreamTableO2(stmt, vertexLabel, rowNum, val1, val2, len);
    uint32_t expectRowsCount = rowNum;
    RdTwentyOneWaitSnRecv(&received, expectRowsCount, 10);
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, received);
    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);
    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 017创建流表，后继结点包含整形数字常量运算，select常量3*4，结构化写入数据
// 	成功
TEST_F(four_op_basic, STREAM_043_017)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, name char(50), address text) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer, time integer, name char(50), address text);"},
        {"create stream view view1 AS select (2 + 9) * 8 % 10, time + 6, name, address FROM stream1;"},
        {"create stream sink sink1 AS select * FROM view1 INTO tsdb(ts1) with (batch_window_size = '1')"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);
    uint32_t rowNum = 200;
    uint16_t len = 120;
    // 结构化写入数据
    RdStructWriteStreamTableO(stmt, vertexLabel, rowNum, len);
    // 查询和校验时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = rowNum;
    uint32_t expectColsCount = 4;
    uint32_t formula1 = (2 + 9) * 8 % 10;
    uint32_t formula2 = 6;
    char op1[] = "";
    char op2[] = "+";
    RdCheckDataInTSTableOfStreamTableO(
        stmt, selectTsName, expectRowsCount, expectColsCount, formula1, formula2, op1, op2);

    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 018十列数据，随机几列是运算表达式，剩下是名称，不使用AS重命名定义运算表达式，看后继流计算节点中是否还是默认使用_column_<属性id>作为属性名称，插入数据校验表达式与默认名称是否正确
// 	成功
TEST_F(four_op_basic, STREAM_043_018)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create stream table stream1 (id integer, age integer, score integer, quantity integer, "
         "price real, weight real, time real, height real, name char(50), address text);"},
        {"create stream view view1 AS select (id + 9) % 11, age, score * 8 + 11, quantity, "
         "5.9 + price * 1.5 - price / 6.0, weight, time, (height + 2.8) * 3.0 + 11.1, name, address FROM stream1;"},
        {"create stream sink sink1 AS select _column_0, age, _column_2, quantity, _column_4, weight, time, _column_7, "
         "name, address"
         " FROM view1 INTO pubsub_channel with (batch_window_size = '1')"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "sink1",
                "comment":"this is a sub.",
                "schema_version": 0,
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] }
                ]  
            }
        )",
    };
    // 定义回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
        bool eof = false;
        bool isNull = false;
        uint32_t size = 0;
        while (true) {
            uint32_t ret = GmcFetch(stmt, &eof);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            if (eof || ret != GMERR_OK) {
                break;
            }
            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
            AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            // check id
            int64_t intVal = 0;
            int64_t val1 = (received + 9) % 11;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &intVal, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(val1, intVal);
            // check age
            val1 = received;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &intVal, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(val1, intVal);
            // check score
            val1 = received * 8 + 11;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &intVal, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(val1, intVal);
            // check quantity
            val1 = received;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &intVal, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(val1, intVal);
            // check price
            double realVal = 0.0;
            double val2 = 5.9 + (received + 1.1) * 1.5 - (received + 1.1) / 6.0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &realVal, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_DOUBLE(val2, realVal);
            // check weight
            val2 = received + 1.1;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 5, &realVal, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_DOUBLE(val2, realVal);
            // check time
            val2 = received + 1.1;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 6, &realVal, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_DOUBLE(val2, realVal);
            // check height
            realVal = 0.0;
            val2 = (received + 1.1 + 2.8) * 3.0 + 11.1;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 7, &realVal, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_DOUBLE(val2, realVal);
            // check name
            size = RD_STREAM_TABLE_T1_NAME_SIZE;
            char name[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            char expectName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            (void)snprintf_s(
                expectName, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", received);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 8, &name, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(0, strcmp(expectName, name));
            // check address
            ret = GmcGetVertexPropertySizeById(stmt, 9, &size);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char address[size] = {0};
            char expectAddress[size] = {0};
            (void)snprintf_s(expectAddress, size, size, "address_%0ld", received);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 9, &address, &size, &isNull);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectAddress, address));
            received++;
        }
    };
    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 创建订阅
    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[0].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);
    uint32_t rowNum = 200;
    uint16_t len = 120;
    double val1 = 1.1;
    // 结构化写入数据
    RdStructWriteStreamTableO3(stmt, vertexLabel, rowNum, val1, len);
    uint32_t expectRowsCount = rowNum;
    RdTwentyOneWaitSnRecv(&received, expectRowsCount, 10);
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, received);
    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);
    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 5 + a * 3 - a / 2
// 019加、减、乘、除结合，例如 5 + a * 3 - a / 2 , (b + 4) * 6
// 	成功
TEST_F(four_op_basic, STREAM_043_019)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, name char(50), address text) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer, time integer, name char(50), address text);"},
        {"create stream view view1 AS select 5 + id * 3 - id / 2, (time + 4) * 6 % 3, name, address FROM stream1;"},
        {"create stream sink sink1 AS select * FROM view1 INTO tsdb(ts1) with (batch_window_size = '1')"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);
    uint32_t rowNum = 200;
    uint16_t len = 120;
    uint32_t g1 = 10;
    // 结构化写入数据
    RdStructWriteStreamTableO(stmt, vertexLabel, rowNum, len, g1, g1);
    // 查询和校验时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = rowNum;
    uint32_t expectColsCount = 4;
    RdCheckDataInTSTableOfStreamTableO3(stmt, selectTsName, expectRowsCount, expectColsCount, g1, g1);

    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 包含取余运算，例如a % 3 + 2 * (a / 3)
// 	成功
TEST_F(four_op_basic, STREAM_043_020)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, name char(50), address text) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer, time integer, name char(50), address text);"},
        {"create stream view view1 AS select id % 3 + 2 * (id / 3), (time + 10) * 2 - time / 4, name, address FROM "
         "stream1;"},
        {"create stream sink sink1 AS select * FROM view1 INTO tsdb(ts1) with (batch_window_size = '1')"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);
    uint32_t rowNum = 200;
    uint16_t len = 120;
    uint32_t g1 = 10;
    // // 结构化写入数据
    RdStructWriteStreamTableO(stmt, vertexLabel, rowNum, len, g1, g1);

    // 查询和校验时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = rowNum;
    uint32_t expectColsCount = 4;
    RdCheckDataInTSTableOfStreamTableO4(stmt, selectTsName, expectRowsCount, expectColsCount, g1, g1);

    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 混合运算，例如(id + 3) * (2 % id) / 2
// 	成功
TEST_F(four_op_basic, STREAM_043_021)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, name char(50), address text) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer, time integer, name char(50), address text);"},
        {"create stream view view1 AS select (id + 3) * (6 % id) / 2, (time % 5) * 6 + time / 4, name, address FROM "
         "stream1;"},
        {"create stream sink sink1 AS select * FROM view1 INTO tsdb(ts1) with (batch_window_size = '1')"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);
    uint32_t rowNum = 200;
    uint16_t len = 120;
    uint32_t g1 = 10;
    // // 结构化写入数据
    RdStructWriteStreamTableO(stmt, vertexLabel, rowNum, len, g1, g1);

    // 查询和校验时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = rowNum;
    uint32_t expectColsCount = 4;
    RdCheckDataInTSTableOfStreamTableO5(stmt, selectTsName, expectRowsCount, expectColsCount, g1, g1);

    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 022复杂表达式，例如(a + 3) * (2 % a) / (a - 4) + a % 2
// 	成功
TEST_F(four_op_basic, STREAM_043_022)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, name char(50), address text) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer, time integer, name char(50), address text);"},
        {"create stream view view1 AS select (id + 3) * (6 % id) + (id % 5) - 8, ((time % 2) + 4) * 6 + time / 3, "
         "name, address FROM stream1;"},
        {"create stream sink sink1 AS select * FROM view1 INTO tsdb(ts1) with (batch_window_size = '1')"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);
    uint32_t rowNum = 200;
    uint16_t len = 120;
    uint32_t g1 = 10;
    // // 结构化写入数据
    RdStructWriteStreamTableO(stmt, vertexLabel, rowNum, len, g1, g1);

    // 查询和校验时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = rowNum;
    uint32_t expectColsCount = 4;
    RdCheckDataInTSTableOfStreamTableO6(stmt, selectTsName, expectRowsCount, expectColsCount, g1, g1);

    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 023浮点数加、减、乘、除结合，例如5.0 + a * 3.0 - a / 2.0
// 	成功
TEST_F(four_op_basic, STREAM_043_023)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create stream table stream1 (id integer, time real, height real, name char(50), address text);"},
        {"create stream view view1 AS select (id + 9) % 11, 5.9 + time * 1.5 - time / 6.0, (height + 2.8) * 3.0 + "
         "11.1, name, address FROM stream1;"},
        {"create stream sink sink1 AS select * FROM view1 INTO pubsub_channel with (batch_window_size = '1')"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "sink1",
                "comment":"this is a sub.",
                "schema_version": 0,
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] }
                ]  
            }
        )",
    };
    // 定义回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
        bool eof = false;
        bool isNull = false;
        uint32_t size = 0;
        while (true) {
            uint32_t ret = GmcFetch(stmt, &eof);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            if (eof || ret != GMERR_OK) {
                break;
            }
            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
            AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            // check id
            int64_t id = 0;
            int64_t val1 = (received + 9) % 11;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &id, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(val1, id);
            // check time
            size = 8;
            double time = 0.0;
            double val2 = 5.9 + (received + 0.1) * 1.5 - (received + 0.1) / 6.0;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &time, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_DOUBLE(val2, time);
            // check height
            double height = 0.0;
            val2 = (received + 1.0 + 2.8) * 3.0 + 11.1;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &height, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_DOUBLE(val2, height);
            // check name
            size = RD_STREAM_TABLE_T1_NAME_SIZE;
            char name[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            char expectName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            (void)snprintf_s(
                expectName, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", received);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &name, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(0, strcmp(expectName, name));
            // check address
            ret = GmcGetVertexPropertySizeById(stmt, 4, &size);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char address[size] = {0};
            char expectAddress[size] = {0};
            (void)snprintf_s(expectAddress, size, size, "address_%0ld", received);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &address, &size, &isNull);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectAddress, address));
            received++;
        }
    };
    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 创建订阅
    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[0].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);
    uint32_t rowNum = 200;
    uint16_t len = 120;
    double val1 = 0.1;
    double val2 = 1.0;
    // 结构化写入数据
    RdStructWriteStreamTableO2(stmt, vertexLabel, rowNum, val1, val2, len);
    uint32_t expectRowsCount = rowNum;
    RdTwentyOneWaitSnRecv(&received, expectRowsCount, 10);
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, received);
    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);
    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 024浮点数混合运算，例如(a + 3.0) * (2.0 - a) / 2
// 	失败，类型错误
TEST_F(four_op_basic, STREAM_043_024)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create stream table stream1 (id integer, time real, height real, name char(50), address text);"},
        {"create stream view view1 AS select (id + 9) % 11, 5.9 + time * 1.5 - time / 6, (height + 2.8) * 3.0 + 11.1, "
         "name, address FROM stream1;"},
        {"create stream sink sink1 AS select * FROM view1 INTO pubsub_channel with (batch_window_size = '1')"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "sink1",
                "comment":"this is a sub.",
                "schema_version": 0,
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] }
                ]  
            }
        )",
    };
    // 定义回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
        bool eof = false;
        bool isNull = false;
        uint32_t size = 0;
        while (true) {
            uint32_t ret = GmcFetch(stmt, &eof);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            if (eof || ret != GMERR_OK) {
                break;
            }
            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
            AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            // check id
            int64_t id = 0;
            int64_t val1 = (received + 9) % 11;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &id, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(val1, id);
            // check time
            size = 8;
            double time = 0.0;
            double val2 = 5.9 + (received + 0.1) * 1.5 - (received + 0.1) / 6;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &time, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_DOUBLE(val2, time);
            // check height
            double height = 0.0;
            val2 = (received + 1.0 + 2.8) * 3.0 + 11.1;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &height, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_DOUBLE(val2, height);
            // check name
            size = RD_STREAM_TABLE_T1_NAME_SIZE;
            char name[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            char expectName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            (void)snprintf_s(
                expectName, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", received);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &name, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(0, strcmp(expectName, name));
            // check address
            ret = GmcGetVertexPropertySizeById(stmt, 4, &size);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char address[size] = {0};
            char expectAddress[size] = {0};
            (void)snprintf_s(expectAddress, size, size, "address_%0ld", received);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &address, &size, &isNull);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectAddress, address));
            received++;
        }
    };
    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 创建订阅
    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[0].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);
    uint32_t rowNum = 200;
    uint16_t len = 120;
    double val1 = 0.1;
    double val2 = 1.0;
    // 结构化写入数据
    RdStructWriteStreamTableO2(stmt, vertexLabel, rowNum, val1, val2, len);
    // 建表的时候类型错误查不到数据
    uint32_t expectRowsCount = 0;
    RdTwentyOneWaitSnRecv(&received, expectRowsCount, 10);
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, received);
    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);
    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 025两个整形参数交互运算，例如a%(b/2)
// 	成功
TEST_F(four_op_basic, STREAM_043_025)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;

    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, time integer, name char(50), address text) "
         "with (time_col = 'time', interval = '1 hour', compression = 'no');"},
        {"create stream table stream1 (id integer, time integer, name char(50), address text);"},
        {"create stream view view1 AS select (id + time) * (id % 11), time % 6 + id, name, address FROM stream1;"},
        {"create stream sink sink1 AS select * FROM view1 INTO tsdb(ts1) with (batch_window_size = '1')"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);
    uint32_t rowNum = 200;
    uint16_t len = 120;
    uint32_t g1 = 10;
    // 结构化写入数据
    RdStructWriteStreamTableO(stmt, vertexLabel, rowNum, len, g1, g1);

    // 查询和校验时序表数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = rowNum;
    uint32_t expectColsCount = 4;
    RdCheckDataInTSTableOfStreamTableO7(stmt, selectTsName, expectRowsCount, expectColsCount, g1, g1);

    RdStreamExecDescT drops[] = {
        {"drop table ts1;"},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}

// 026两个浮点数参数交互运算，例如a+3.0*b
// 	成功
TEST_F(four_op_basic, STREAM_043_026)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create stream table stream1 (id integer, time real, height real, name char(50), address text);"},
        {"create stream view view1 AS select (id + 8) % 3 + id, 5.8 + time * 3.4 - height / 7.1, (height + 2.8) * 3.0 "
         "+ time, name, address FROM stream1;"},
        {"create stream sink sink1 AS select * FROM view1 INTO pubsub_channel with (batch_window_size = '1')"},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 定义订阅配置
    const GmcSubConfigT subConfig1 = {
        .subsName = "sub01",
        .configJson = R"(
            {
                "label_name": "sink1",
                "comment":"this is a sub.",
                "schema_version": 0,
                "is_reliable": false,
                "events": [
                    { "type": "insert", "msgTypes": ["new object"] }
                ]  
            }
        )",
    };
    // 定义回调函数
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
        bool eof = false;
        bool isNull = false;
        uint32_t size = 0;
        while (true) {
            uint32_t ret = GmcFetch(stmt, &eof);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            if (eof || ret != GMERR_OK) {
                break;
            }
            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
            AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            // check id
            int64_t id = 0;
            int64_t val1 = (received + 8) % 3 + received;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &id, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(val1, id);
            // check time
            size = 8;
            double time = 0.0;
            double val2 = 5.8 + (received + 0.1) * 3.4 - (received + 1.0) / 7.1;
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &time, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_DOUBLE(val2, time);
            // check height
            double height = 0.0;
            val2 = (received + 1.0 + 2.8) * 3.0 + (received + 0.1);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &height, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_DOUBLE(val2, height);
            // check name
            size = RD_STREAM_TABLE_T1_NAME_SIZE;
            char name[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            char expectName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
            (void)snprintf_s(
                expectName, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "name_%0ld", received);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &name, &size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(0, strcmp(expectName, name));
            // check address
            ret = GmcGetVertexPropertySizeById(stmt, 4, &size);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char address[size] = {0};
            char expectAddress[size] = {0};
            (void)snprintf_s(expectAddress, size, size, "address_%0ld", received);
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &address, &size, &isNull);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectAddress, address));
            received++;
        }
    };
    // 创建订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 创建订阅
    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &subConfig1, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[0].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);
    uint32_t rowNum = 200;
    uint16_t len = 120;
    double val1 = 0.1;
    double val2 = 1.0;
    // 结构化写入数据
    RdStructWriteStreamTableO2(stmt, vertexLabel, rowNum, val1, val2, len);
    uint32_t expectRowsCount = rowNum;
    RdTwentyOneWaitSnRecv(&received, expectRowsCount, 10);
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, received);
    // 取消订阅
    ret = GmcUnSubscribe(stmt, subConfig1.subsName);
    EXPECT_EQ(GMERR_OK, ret);
    // 释放订阅连接
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamExecDescT drops[] = {
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    AW_FUN_Log(LOG_STEP, "start end.");
    RdFinishTest();
}
