/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 结构化写流数据特性交互测试
 * Author: guopanpan
 * Create: 2024-09-14
 */
#include "gtest/gtest.h"
#include "securec.h"
#include "gmc_sql.h"
#include "rd_feature_stream.h"
#include "rd_stream_table_t1.h"

class t03_write_stream_data_interaction : public testing::Test {
public:
    static GmcConnT *longConn;
    static GmcStmtT *longStmt;
    GmcConnT *shortConn = nullptr;
    GmcStmtT *shortStmt = nullptr;
    GmcStmtT *streamStmt = nullptr;
    GmcStmtT *tsStmt = nullptr;

    bool hasFinishTest = false;
    void RdFinishTest();

    static void SetUpTestCase();
    static void TearDownTestCase();
    virtual void SetUp();
    virtual void TearDown();
};

GmcConnT *t03_write_stream_data_interaction::longConn = NULL;
GmcStmtT *t03_write_stream_data_interaction::longStmt = NULL;

void t03_write_stream_data_interaction::SetUpTestCase()
{
    int32_t ret;
    ret = RdStreamEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdGmcConnect(&longConn, &longStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdLogSetLevel4Stdout(RD_LOG_LEVEL_INFO);
}

void t03_write_stream_data_interaction::TearDownTestCase()
{
    int32_t ret;
    ret = RdGmcDisconnect(longConn, longStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = RdStreamEnvClean();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void t03_write_stream_data_interaction::SetUp()
{
    hasFinishTest = false;
    int32_t ret;
    ret = RdGmcConnect(&shortConn, &shortStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcAllocStmt(shortConn, &streamStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint32_t modelStream = GMC_MODEL_STREAM;
    ret = GmcSetStmtAttr(streamStmt, GMC_STMT_ATTR_MODEL_TYPE, &modelStream, sizeof(modelStream));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcAllocStmt(shortConn, &tsStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint32_t modelTs = GMC_MODEL_TS;
    ret = GmcSetStmtAttr(tsStmt, GMC_STMT_ATTR_MODEL_TYPE, &modelTs, sizeof(modelTs));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void t03_write_stream_data_interaction::TearDown()
{
    int32_t ret;
    if (!hasFinishTest) {
        RdStreamExecDescT desc[] = {
            {"drop stream sink sink1;"},
            {"drop stream sink sink2;"},
            {"drop stream table sink1;"},
            {"drop stream table stream1;"},
            {"drop table ts1;", GMC_MODEL_TS},
            {"drop table ts2;", GMC_MODEL_TS},
        };
        ret = RdStreamDropMetadata(longStmt, desc, sizeof(desc));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    GmcFreeStmt(streamStmt);
    GmcFreeStmt(tsStmt);
    ret = RdGmcDisconnect(shortConn, shortStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void t03_write_stream_data_interaction::RdFinishTest()
{
    hasFinishTest = true;
}

// @TestSuite 时序交互
// @TestcaseName STREAM_002_024	创建流表和时序表，直接向时序表写入数据	成功
TEST_F(t03_write_stream_data_interaction, STREAM_002_024)
{
    // DEMO 写入和查询时序表
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50)) with (time_col = 'id', interval= '1 hour');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50));"},
        {"create stream sink sink1 as select * from stream1 into tsdb (ts1) with (batch_window_size = 15);"},
    };
    ret = RdStreamExecSql(streamStmt, creates, sizeof(creates));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 写入时序数据
    static const uint32_t rowNum = 200;
    static const uint32_t colNum = 3;
    static const uint32_t nameLen = 50;
    int64_t id[rowNum] = {0};
    char name[rowNum][nameLen] = {0};
    ret = GmcPrepareStmtByLabelName(tsStmt, "ts1", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStmtAttr(tsStmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(rowNum));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < rowNum; i++) {
        id[i] = i;
        (void)snprintf_s(name[i], nameLen, nameLen, "info%04d", i);
    }
    ret = GmcBindCol(tsStmt, 0, GMC_DATATYPE_INT64, id, 0, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(tsStmt, 1, GMC_DATATYPE_FIXED, name, nameLen, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(tsStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 查询时序数据
    const char *sqlText = "select * from ts1;";
    ret = GmcExecDirect(tsStmt, sqlText, strlen(sqlText));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint32_t selectRowNum = 0;
    ret = GmcGetStmtAttr(tsStmt, GMC_STMT_ATTR_RESULT_ROWS, &selectRowNum, sizeof(selectRowNum));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(rowNum, selectRowNum);
    bool eof, isNull;
    for (uint32_t i = 0; i < selectRowNum; i++) {
        ret = GmcFetch(tsStmt, &eof);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_BOOL(false, eof);
        int64_t qryId;
        uint32_t qryIdLen = sizeof(int64_t);
        ret = GmcGetPropertyById(tsStmt, 0, &qryId, &qryIdLen, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(id[i], qryId);
        char qryName[nameLen] = {0};
        uint32_t qryNameLen = nameLen;
        ret = GmcGetPropertyById(tsStmt, 1, qryName, &qryNameLen, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ASSERT_STREQ(name[i], qryName);
    }
    ret = GmcFetch(tsStmt, &eof);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_BOOL(true, eof);

    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream sink sink1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(streamStmt, drops, sizeof(drops));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdFinishTest();
}

// @TestcaseName STREAM_002_025	以时序的方式向流表写入数据	报错
TEST_F(t03_write_stream_data_interaction, STREAM_002_025)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50)) with (time_col = 'id', interval= '1 hour');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50));"},
        {"create stream sink sink1 as select * from stream1 into tsdb (ts1) with (batch_window_size = 15);"},
    };
    ret = RdStreamExecSql(streamStmt, creates, sizeof(creates));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 写入时序数据
    static const uint32_t rowNum = 200;
    static const uint32_t colNum = 3;
    static const uint32_t nameLen = 50;
    int64_t id[rowNum] = {0};
    char name[rowNum][nameLen] = {0};
    ret = GmcPrepareStmtByLabelName(streamStmt, "stream1", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStmtAttr(streamStmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(rowNum));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < rowNum; i++) {
        id[i] = i;
        (void)snprintf_s(name[i], nameLen, nameLen, "info%04d", i);
    }
    ret = GmcBindCol(streamStmt, 0, GMC_DATATYPE_INT64, id, 0, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(streamStmt, 1, GMC_DATATYPE_FIXED, name, nameLen, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(streamStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, ret);

    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream sink sink1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(streamStmt, drops, sizeof(drops));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdFinishTest();
}

// @TestcaseName STREAM_002_026	设置stmt模型为stream，写入时序数据	进程不崩溃
TEST_F(t03_write_stream_data_interaction, STREAM_002_026)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50)) with (time_col = 'id', interval= '1 hour');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50));"},
        {"create stream sink sink1 as select * from stream1 into tsdb (ts1) with (batch_window_size = 15);"},
    };
    ret = RdStreamExecSql(streamStmt, creates, sizeof(creates));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 写入时序数据
    static const uint32_t rowNum = 200;
    static const uint32_t colNum = 3;
    static const uint32_t nameLen = 50;
    int64_t id[rowNum] = {0};
    char name[rowNum][nameLen] = {0};

    // HISTORY 2024-10-30 功能变更，不再支持设置stmt模型，该用例将执行成功
    ret = GmcPrepareStmtByLabelName(streamStmt, "ts1", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStmtAttr(streamStmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(rowNum));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < rowNum; i++) {
        id[i] = i;
        (void)snprintf_s(name[i], nameLen, nameLen, "info%04d", i);
    }
    ret = GmcBindCol(streamStmt, 0, GMC_DATATYPE_INT64, id, 0, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(streamStmt, 1, GMC_DATATYPE_FIXED, name, nameLen, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(streamStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream sink sink1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(streamStmt, drops, sizeof(drops));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdFinishTest();
}

// @TestcaseName STREAM_002_027	不设置stmt模型，写入时序数据	进程不崩溃
TEST_F(t03_write_stream_data_interaction, STREAM_002_027)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50)) with (time_col = 'id', interval= '1 hour');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50));"},
        {"create stream sink sink1 as select * from stream1 into tsdb (ts1) with (batch_window_size = 15);"},
    };
    ret = RdStreamExecSql(streamStmt, creates, sizeof(creates));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 写入时序数据
    static const uint32_t rowNum = 200;
    static const uint32_t colNum = 3;
    static const uint32_t nameLen = 50;
    int64_t id[rowNum] = {0};
    char name[rowNum][nameLen] = {0};
    ret = GmcPrepareStmtByLabelName(shortStmt, "ts1", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStmtAttr(shortStmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(rowNum));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < rowNum; i++) {
        id[i] = i;
        (void)snprintf_s(name[i], nameLen, nameLen, "info%04d", i);
    }
    ret = GmcBindCol(shortStmt, 0, GMC_DATATYPE_INT64, id, 0, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(shortStmt, 1, GMC_DATATYPE_FIXED, name, nameLen, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(shortStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream sink sink1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(streamStmt, drops, sizeof(drops));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdFinishTest();
}

// @TestcaseName STREAM_002_028	设置stmt模型为ts，写入流数据	报错
TEST_F(t03_write_stream_data_interaction, STREAM_002_028)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50)) with (time_col = 'id', interval= '1 hour');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50));"},
        {"create stream sink sink1 as select * from stream1 into tsdb (ts1) with (batch_window_size = 15);"},
    };
    ret = RdStreamExecSql(streamStmt, creates, sizeof(creates));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // HISTORY 2024-10-30 功能变更，不再支持设置stmt模型，该用例将执行成功
    ret = GmcPrepareStmtByLabelName(tsStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint32_t rowNum = 200;
    for (uint32_t i = 0; i < rowNum; i++) {
        RdStreamT1DataT data = {0};
        RdStreamT1SetData(i, 0, &data);
        ret = RdStreamSetVertexWithBuf(tsStmt, vertexLabel, &data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(tsStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream sink sink1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(streamStmt, drops, sizeof(drops));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    RdFinishTest();
}

// @TestcaseName STREAM_002_029	不设置stmt模型，写入流数据	报错
TEST_F(t03_write_stream_data_interaction, STREAM_002_029)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50)) with (time_col = 'id', interval= '1 hour');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50));"},
        {"create stream sink sink1 as select * from stream1 into tsdb (ts1) with (batch_window_size = 15);"},
    };
    ret = RdStreamExecSql(streamStmt, creates, sizeof(creates));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // HISTORY 2024-10-30 功能变更，不再支持设置stmt模型，该用例将执行成功
    ret = GmcPrepareStmtByLabelName(shortStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint32_t rowNum = 200;
    for (uint32_t i = 0; i < rowNum; i++) {
        RdStreamT1DataT data = {0};
        RdStreamT1SetData(i, 0, &data);
        ret = RdStreamSetVertexWithBuf(shortStmt, vertexLabel, &data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(shortStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream sink sink1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(streamStmt, drops, sizeof(drops));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    RdFinishTest();
}

// @TestcaseName STREAM_002_030	Prepare操作类型是GMC_OPERATION_INSERT，写入流数据	报错
TEST_F(t03_write_stream_data_interaction, STREAM_002_030)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50)) with (time_col = 'id', interval= '1 hour');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50));"},
        {"create stream sink sink1 as select * from stream1 into tsdb (ts1) with (batch_window_size = 15);"},
    };
    ret = RdStreamExecSql(streamStmt, creates, sizeof(creates));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    ret = GmcPrepareStmtByLabelName(streamStmt, vertexLabel->topRecordName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint32_t rowNum = 200;
    for (uint32_t i = 0; i < rowNum; i++) {
        RdStreamT1DataT data = {0};
        RdStreamT1SetData(i, 0, &data);
        ret = RdStreamSetVertexWithBuf(streamStmt, vertexLabel, &data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(streamStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    }

    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream sink sink1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(streamStmt, drops, sizeof(drops));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    RdFinishTest();
}

// @TestcaseName STREAM_002_031	Prepare操作类型是GMC_OPERATION_SQL_INSERT，写入流数据	报错
TEST_F(t03_write_stream_data_interaction, STREAM_002_031)
{
    AW_FUN_Log(LOG_STEP, "start test.");
    int32_t ret;
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(50)) with (time_col = 'id', interval= '1 hour');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(50));"},
        {"create stream sink sink1 as select * from stream1 into tsdb (ts1) with (batch_window_size = 15);"},
    };
    ret = RdStreamExecSql(streamStmt, creates, sizeof(creates));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    AW_MACRO_ASSERT_NOTNULL(vertexLabel);

    // HISTORY 2024-10-30 在客户端判断表名对应的表类型是否和操作类型一致，如果不一致，将返回错误码
    ret = GmcPrepareStmtByLabelName(streamStmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_OBJECT, ret);
    uint32_t rowNum = 200;
    for (uint32_t i = 0; i < rowNum; i++) {
        RdStreamT1DataT data = {0};
        RdStreamT1SetData(i, 0, &data);
        ret = RdStreamSetVertexWithBuf(streamStmt, vertexLabel, &data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_VALUE, ret);
        ret = GmcExecute(streamStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_WRONG_STMT_OBJECT, ret);
    }

    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream sink sink1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(streamStmt, drops, sizeof(drops));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);
    RdFinishTest();
}
