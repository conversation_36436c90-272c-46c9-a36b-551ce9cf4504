/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2024. All rights reserved.
 Description  : GMDB 505.1.0 迭代四TSDB支持gmimport建静态表--通过接口gmimport创建表，时序逻辑表含table_path
 Notes        :
 History      :
 Author       : luyang/l00618033
 Create       : [2024.10.14]
*****************************************************************************/
#include "gtest/gtest.h"
#include "t_rd_ts.h"
#include "tsdbSupGmimport.h"

// 默认是push模式，修改配置项
class tsdbSupGmimport_003_test : public testing::Test {
protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}

public:
    virtual void SetUp();
    virtual void TearDown();
};

void tsdbSupGmimport_003_test::SetUp()
{
    InitTsCiCfg();
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建epoll监听线程
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
    ret = TestTsGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void tsdbSupGmimport_003_test::TearDown()
{
    int ret = 0;
    // 断开连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    RecoverTsCiCfg();
    system("sh $TEST_HOME/tools/stop.sh -ts");
}

// 将spaceMaxNum配置项设置为64
class tsdbSupGmimport_003_test2 : public testing::Test {
protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}

public:
    virtual void SetUp();
    virtual void TearDown();
};

void tsdbSupGmimport_003_test2::SetUp()
{
    RecoverTsCiCfg();
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("sh ${TEST_HOME}/tools/modifyCfg.sh -ts \"spaceMaxNum=64\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建epoll监听线程
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
    ret = TestTsGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void tsdbSupGmimport_003_test2::TearDown()
{
    int ret = 0;
    // 断开连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    RecoverTsCiCfg();
    system("sh $TEST_HOME/tools/stop.sh -ts");
}

// 改成pull模式
class tsdbSupGmimport_003_test3 : public testing::Test {
protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}

public:
    virtual void SetUp();
    virtual void TearDown();
};

void tsdbSupGmimport_003_test3::SetUp()
{
    RecoverTsCiCfg();
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建epoll监听线程
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
    ret = TestTsGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void tsdbSupGmimport_003_test3::TearDown()
{
    int ret = 0;
    // 断开连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    RecoverTsCiCfg();
    system("sh $TEST_HOME/tools/stop.sh -ts");
}

/* ****************************************************************************
 Description  : 001.文件名字的后缀为.gmsql,里面创建的时序逻辑表含table_path,导入成功，插入数据，查询数据符合预期
**************************************************************************** */
TEST_F(tsdbSupGmimport_003_test, Timing_054_003_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;

    char filePath[FILE_PATH] = {0};
    char fileName[FILE_PATH] = "test001";
    char logicName[128] = {0};
    char streamName[128] = {0};
    constexpr int64_t count = 20;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    char ip[count][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc", "lucy",
        "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    char messagetemp[count][160] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537", "0071 3521",
        "0010 0001", "", "3102 0022", "0000 0001", "9021 6538", "0071 3522", "0010 0002", "0010 0000", "3102 0023",
        "0000 0002", "9021 6539", "0071 3523", "0010 0003"};
    char *message[count] = {0};
    char *ns[count] = {0};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704067200 + i;
        ns[i] = name[i];
        message[i] = messagetemp[i];
    }
    system("rm -rf /home/<USER>/");
    (void)sprintf(filePath, "./gmsql3/%s.gmsql", fileName);
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c sql -f %s -s %s", filePath, g_connServerTsdb);
    ret = executeCommand(g_command, "gmimport create sql total success num: 1.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对逻辑表插入数据并查询
    C2Int8C4StrT obj1 = {.id = id, .time = time, .name = name, .ip = ip};
    for (int i = 0; i < count; i++) {
        obj1.ns[i] = ns[i];
        obj1.message[i] = message[i];
    }
    // 插入数据
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, &obj1, count, C2Int8C4StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select count(*) from tsdb1;\" -s %s", g_connServerTsdb);
    system(g_command);

    // 读数据
    ret = readRecordTs(g_conn, g_stmt, g_tableName, &obj1, count, C2Int8C4StrGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 002.文件名字的后缀为.gmsql,里面创建的时序逻辑表含table_path,流表和视图存在table_path,预期时序逻辑表成功
**************************************************************************** */
TEST_F(tsdbSupGmimport_003_test, Timing_054_003_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_JSON_CONTENT);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
    int ret = 0;
    char filePath[FILE_PATH] = {0};
    char fileName[FILE_PATH] = "test002";
    system("rm -rf /home/<USER>/");
    (void)sprintf(filePath, "./gmsql3/%s.gmsql", fileName);
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c sql -f %s -s %s", filePath, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_FEATURE_NOT_SUPPORTED);
    (void)snprintf(g_errorMsg2, MAX_CMD_SIZE, "ret = %d", GMERR_INVALID_JSON_CONTENT);
    ret = executeCommand(
        g_command, "Can not exec sql", g_errorMsg, g_errorMsg2, "gmimport create sql total success num: 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select count(*) from tsdb1;\" -s %s", g_connServerTsdb);
    system(g_command);

    // 删表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropCmTable(g_stmt, (char *)"stream1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
003.文件名字的后缀为.gmsql,里面逻辑表含table_path,达到spaceMaxNum上限，预期建满时序逻辑表，达到上限后创建失败；插入数据，查询数据符合预期
**************************************************************************** */
TEST_F(tsdbSupGmimport_003_test2, Timing_054_003_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INSUFFICIENT_RESOURCES);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    char filePath[FILE_PATH] = {0};
    char fileName[FILE_PATH] = "test003";
    char logicName[128] = {0};
    char msgVal[MAX_CMD_SIZE] = {0};
    int spaceInfoCnt = 0;
    int spaceMaxNumVal = 0;
    int remainTableCnt = 0;
    constexpr int64_t count = 20;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    char ip[count][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc", "lucy",
        "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    char messagetemp[count][160] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537", "0071 3521",
        "0010 0001", "", "3102 0022", "0000 0001", "9021 6538", "0071 3522", "0010 0002", "0010 0000", "3102 0023",
        "0000 0002", "9021 6539", "0071 3523", "0010 0003"};
    char *message[count] = {0};
    char *ns[count] = {0};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704067200 + i;
        ns[i] = name[i];
        message[i] = messagetemp[i];
    }

    // 获取当前表空间大小
    ret = TestGetSpaceInfoCnt(&spaceInfoCnt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 获取spaceMaxNum配置项值
    ret = TestGetConfigVal(&spaceMaxNumVal, (char *)"spaceMaxNum");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    remainTableCnt = spaceMaxNumVal - spaceInfoCnt;
    AW_FUN_Log(LOG_STEP, "spaceval is %d, spaceMaxNumVal is %d, remainTableCnt is %d", spaceInfoCnt, spaceMaxNumVal,
        remainTableCnt);

    system("rm -rf /home/<USER>/");
    (void)sprintf(filePath, "./gmsql3/%s.gmsql", fileName);
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c sql -f %s -s %s", filePath, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_INSUFFICIENT_RESOURCES);
    (void)snprintf(msgVal, MAX_CMD_SIZE, "gmimport create sql total success num: %d.", remainTableCnt);
    // 能够创建满空间表
    ret = executeCommand(g_command, "[ERROR] Can not exec sql", g_errorMsg, msgVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 对逻辑表插入数据并查询
    C2Int8C4StrT obj1 = {.id = id, .time = time, .name = name, .ip = ip};
    for (int i = 0; i < count; i++) {
        obj1.ns[i] = ns[i];
        obj1.message[i] = message[i];
    }

    // 对所有的表写数据，串行
    for (int i = 1; i < remainTableCnt + 1; i++) {
        (void)snprintf(logicName, 128, "tsdb%d", i);
        // 插入数据
        ret = writeRecordTs(g_conn, g_stmt, logicName, &obj1, count, C2Int8C4StrSet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 读数据
        ret = readRecordTs(g_conn, g_stmt, logicName, &obj1, count, C2Int8C4StrGet, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 删除创建的表
    for (int i = 1; i < remainTableCnt + 1; i++) {
        (void)snprintf(logicName, 128, "tsdb%d", i);
        ret = DropCmTable(g_stmt, logicName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 004.文件名字的后缀为.gmsql,里面创建的时序逻辑表含table_path同时含drop语法,预期导入失败
**************************************************************************** */
TEST_F(tsdbSupGmimport_003_test, Timing_054_003_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char filePath[FILE_PATH] = {0};
    char fileName[FILE_PATH] = "test007";
    system("rm -rf /home/<USER>/");
    (void)sprintf(filePath, "./gmsql3/%s.gmsql", fileName);
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c sql -f %s -s %s", filePath, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_FEATURE_NOT_SUPPORTED);
    ret = executeCommand(g_command, "[ERROR] ts sql import only supports create stmt", g_errorMsg,
        "gmimport create sql total success num: 1.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select count(*) from tsdb1;\" -s %s", g_connServerTsdb);
    system(g_command);

    // 删表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 005.push模式，文件名字的后缀为.gmsql,里面只含正常create语法（逻辑表含table_path），
 导入成功，插入数据，查询数据，alter逻辑表，删表符合预期
**************************************************************************** */
TEST_F(tsdbSupGmimport_003_test, Timing_054_003_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;

    char filePath[FILE_PATH] = {0};
    char fileName[FILE_PATH] = "test004";
    char logicName[128] = {0};
    char streamName[128] = {0};
    constexpr int64_t count = 20;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    char ip[count][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc", "lucy",
        "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    char messagetemp[count][160] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537", "0071 3521",
        "0010 0001", "", "3102 0022", "0000 0001", "9021 6538", "0071 3522", "0010 0002", "0010 0000", "3102 0023",
        "0000 0002", "9021 6539", "0071 3523", "0010 0003"};
    char *message[count] = {0};
    char *ns[count] = {0};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704067200 + i;
        ns[i] = name[i];
        message[i] = messagetemp[i];
    }

    system("rm -rf /home/<USER>/");
    (void)sprintf(filePath, "./gmsql3/%s.gmsql", fileName);
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c sql -f %s -s %s", filePath, g_connServerTsdb);
    ret = executeCommand(g_command, "gmimport create sql total success num: 1.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对逻辑表插入数据并查询
    C2Int8C4StrT obj1 = {.id = id, .time = time, .name = name, .ip = ip};
    for (int i = 0; i < count; i++) {
        obj1.ns[i] = ns[i];
        obj1.message[i] = message[i];
    }
    // 插入数据
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, &obj1, count, C2Int8C4StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select count(*) from tsdb1;\" -s %s", g_connServerTsdb);
    system(g_command);

    // 读数据
    ret = readRecordTs(g_conn, g_stmt, g_tableName, &obj1, count, C2Int8C4StrGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // alter逻辑表
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol text;", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -sql \"select * from 'V\\$CATA_VERTEX_LABEL_INFO' "
        "where VERTEX_LABEL_NAME = '%s';\" -s %s",
        g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "\"name\": \"newcol\"", "\"type\": \"string\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
006.pull模式，文件名字的后缀为.gmsql,里面只含正常create语法（逻辑表含table_path），导入成功，插入数据，查询数据，删表符合预期
**************************************************************************** */
TEST_F(tsdbSupGmimport_003_test3, Timing_054_003_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    char filePath[FILE_PATH] = {0};
    char fileName[FILE_PATH] = "test004";
    char logicName[128] = {0};
    char streamName[128] = {0};
    constexpr int64_t count = 20;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    char ip[count][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc", "lucy",
        "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    char messagetemp[count][160] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537", "0071 3521",
        "0010 0001", "", "3102 0022", "0000 0001", "9021 6538", "0071 3522", "0010 0002", "0010 0000", "3102 0023",
        "0000 0002", "9021 6539", "0071 3523", "0010 0003"};
    char *message[count] = {0};
    char *ns[count] = {0};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704067200 + i;
        ns[i] = name[i];
        message[i] = messagetemp[i];
    }
    system("rm -rf /home/<USER>/");
    (void)sprintf(filePath, "./gmsql3/%s.gmsql", fileName);
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c sql -f %s -s %s", filePath, g_connServerTsdb);
    ret = executeCommand(g_command, "gmimport create sql total success num: 1.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对逻辑表插入数据并查询
    C2Int8C4StrT obj1 = {.id = id, .time = time, .name = name, .ip = ip};
    for (int i = 0; i < count; i++) {
        obj1.ns[i] = ns[i];
        obj1.message[i] = message[i];
    }
    // 插入数据
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, &obj1, count, C2Int8C4StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select count(*) from tsdb1;\" -s %s", g_connServerTsdb);
    system(g_command);

    // 读数据
    ret = readRecordTs(g_conn, g_stmt, g_tableName, &obj1, count, C2Int8C4StrGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // alter逻辑表
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol text;", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -sql \"select * from 'V\\$CATA_VERTEX_LABEL_INFO' "
        "where VERTEX_LABEL_NAME = '%s';\" -s %s",
        g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "\"name\": \"newcol\"", "\"type\": \"string\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
007.push模式，文件名字的后缀为.gmsql,里面只含正常create语法（逻辑表含table_path），导入成功，
 插入数据，独立重启，查询数据，插入数据，查询数据，删表符合预期
**************************************************************************** */
TEST_F(tsdbSupGmimport_003_test, Timing_054_003_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_CONNECTION_RESET_BY_PEER);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_CONNECTION_FAILURE);
    (void)snprintf(g_errorCode03, MAX_CMD_SIZE, "GMERR-%d", GMERR_INTERNAL_ERROR);
    AW_ADD_ERR_WHITE_LIST(3, g_errorCode01, g_errorCode02, g_errorCode03);
    int ret = 0;
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    // 获取当前系统时间，避免独立重启之后，数据过期删除
    int64_t startTime = 0;
    GetSysTimeVal(&startTime);
    char filePath[FILE_PATH] = {0};
    char fileName[FILE_PATH] = "test004";
    char logicName[128] = {0};
    char streamName[128] = {0};
    constexpr int64_t count = 20;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    char ip[count][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc", "lucy",
        "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    char messagetemp[count][160] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537", "0071 3521",
        "0010 0001", "", "3102 0022", "0000 0001", "9021 6538", "0071 3522", "0010 0002", "0010 0000", "3102 0023",
        "0000 0002", "9021 6539", "0071 3523", "0010 0003"};
    char *message[count] = {0};
    char *ns[count] = {0};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704067200 + i;
        ns[i] = name[i];
        message[i] = messagetemp[i];
    }

    system("rm -rf /home/<USER>/");
    (void)sprintf(filePath, "./gmsql3/%s.gmsql", fileName);
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c sql -f %s -s %s", filePath, g_connServerTsdb);
    ret = executeCommand(g_command, "gmimport create sql total success num: 1.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对逻辑表插入数据并查询
    C2Int8C4StrT obj1 = {.id = id, .time = time, .name = name, .ip = ip};
    for (int i = 0; i < count; i++) {
        obj1.ns[i] = ns[i];
        obj1.message[i] = message[i];
    }
    // 插入数据
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, &obj1, count, C2Int8C4StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select count(*) from tsdb1;\" -s %s", g_connServerTsdb);
    system(g_command);

    // 读数据
    ret = readRecordTs(g_conn, g_stmt, g_tableName, &obj1, count, C2Int8C4StrGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 独立重启
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");

    // 等待服务端自动建立订阅连接
    sleep(3);

    // 重新建连
    ret = TestTsGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询数据
    // 读数据
    ret = readRecordTs(g_conn, g_stmt, g_tableName, &obj1, count, C2Int8C4StrGet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据并查询
    // 逻辑表
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, &obj1, count, C2Int8C4StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select count(*) from tsdb1;\" -s %s", g_connServerTsdb);
    system(g_command);

    // 读数据
    ret = readRecordTs(g_conn, g_stmt, g_tableName, &obj1, count * 2, C2Int8C4StrGet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // alter逻辑表
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol text;", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -sql \"select * from 'V\\$CATA_VERTEX_LABEL_INFO' "
        "where VERTEX_LABEL_NAME = '%s';\" -s %s",
        g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "\"name\": \"newcol\"", "\"type\": \"string\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
008.pull模式，文件名字的后缀为.gmsql,里面只含正常create语法（逻辑表含table_path），导入成功，
 插入数据，独立重启，查询数据，插入数据，查询数据，alter逻辑表，删表符合预期
**************************************************************************** */
TEST_F(tsdbSupGmimport_003_test3, Timing_054_003_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 获取当前系统时间，避免独立重启之后，数据过期删除
    int64_t startTime = 0;
    GetSysTimeVal(&startTime);

    char filePath[FILE_PATH] = {0};
    char fileName[FILE_PATH] = "test004";
    char logicName[128] = {0};
    char streamName[128] = {0};
    constexpr int64_t count = 20;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    char ip[count][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc", "lucy",
        "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    char messagetemp[count][160] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537", "0071 3521",
        "0010 0001", "", "3102 0022", "0000 0001", "9021 6538", "0071 3522", "0010 0002", "0010 0000", "3102 0023",
        "0000 0002", "9021 6539", "0071 3523", "0010 0003"};
    char *message[count] = {0};
    char *ns[count] = {0};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = startTime + i;
        ns[i] = name[i];
        message[i] = messagetemp[i];
    }
    system("rm -rf /home/<USER>/");
    (void)sprintf(filePath, "./gmsql3/%s.gmsql", fileName);
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c sql -f %s -s %s", filePath, g_connServerTsdb);
    ret = executeCommand(g_command, "gmimport create sql total success num: 1.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对逻辑表插入数据并查询
    C2Int8C4StrT obj1 = {.id = id, .time = time, .name = name, .ip = ip};
    for (int i = 0; i < count; i++) {
        obj1.ns[i] = ns[i];
        obj1.message[i] = message[i];
    }
    // 插入数据
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, &obj1, count, C2Int8C4StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select count(*) from tsdb1;\" -s %s", g_connServerTsdb);
    system(g_command);

    // 读数据
    ret = readRecordTs(g_conn, g_stmt, g_tableName, &obj1, count, C2Int8C4StrGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 独立重启
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");

    // 重新建连
    ret = TestTsGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询数据
    // 读数据
    ret = readRecordTs(g_conn, g_stmt, g_tableName, &obj1, count, C2Int8C4StrGet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据并查询
    // 逻辑表
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, &obj1, count, C2Int8C4StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select count(*) from tsdb1;\" -s %s", g_connServerTsdb);
    system(g_command);

    // 读数据
    ret = readRecordTs(g_conn, g_stmt, g_tableName, &obj1, count * 2, C2Int8C4StrGet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // alter逻辑表
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol text;", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -sql \"select * from 'V\\$CATA_VERTEX_LABEL_INFO' "
        "where VERTEX_LABEL_NAME = '%s';\" -s %s",
        g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "\"name\": \"newcol\"", "\"type\": \"string\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :
009.通过gmimport导入的表（逻辑表含table_path和不含table_path），插入数据，查询数据，alter表，删除表符合预期
**************************************************************************** */
TEST_F(tsdbSupGmimport_003_test3, Timing_054_003_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char filePath[FILE_PATH] = {0};
    char fileName[FILE_PATH] = "test005";
    char logicName[128] = {0};
    char streamName[128] = {0};
    constexpr int64_t count = 20;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    char ip[count][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc", "lucy",
        "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    char messagetemp[count][160] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537", "0071 3521",
        "0010 0001", "", "3102 0022", "0000 0001", "9021 6538", "0071 3522", "0010 0002", "0010 0000", "3102 0023",
        "0000 0002", "9021 6539", "0071 3523", "0010 0003"};
    char *message[count] = {0};
    char *ns[count] = {0};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704067200 + i;
        ns[i] = name[i];
        message[i] = messagetemp[i];
    }
    system("rm -rf /home/<USER>/");
    (void)sprintf(filePath, "./gmsql3/%s.gmsql", fileName);
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c sql -f %s -s %s", filePath, g_connServerTsdb);
    ret = executeCommand(g_command, "gmimport create sql total success num: 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对逻辑表插入数据并查询
    C2Int8C4StrT obj1 = {.id = id, .time = time, .name = name, .ip = ip};
    for (int i = 0; i < count; i++) {
        obj1.ns[i] = ns[i];
        obj1.message[i] = message[i];
    }
    // 插入数据
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, &obj1, count, C2Int8C4StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecordTs(g_conn, g_stmt, g_tableName2, &obj1, count, C2Int8C4StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select count(*) from tsdb1;\" -s %s", g_connServerTsdb);
    system(g_command);
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select count(*) from tsdb2;\" -s %s", g_connServerTsdb);
    system(g_command);

    // 读数据
    ret = readRecordTs(g_conn, g_stmt, g_tableName, &obj1, count, C2Int8C4StrGet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecordTs(g_conn, g_stmt, g_tableName2, &obj1, count, C2Int8C4StrGet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // alter逻辑表
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol text;", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -sql \"select * from 'V\\$CATA_VERTEX_LABEL_INFO' "
        "where VERTEX_LABEL_NAME = '%s';\" -s %s",
        g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "\"name\": \"newcol\"", "\"type\": \"string\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol text;", g_tableName2);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -sql \"select * from 'V\\$CATA_VERTEX_LABEL_INFO' "
        "where VERTEX_LABEL_NAME = '%s';\" -s %s",
        g_tableName2, g_connServerTsdb);
    ret = executeCommand(g_command, "\"name\": \"newcol\"", "\"type\": \"string\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 010.通过gmimport导入的表（逻辑表含table_path和不含table_path），插入数据，查询数据，
 独立重启，插入数据，查询数据，alter表，删除表符合预期
**************************************************************************** */
TEST_F(tsdbSupGmimport_003_test3, Timing_054_003_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 获取当前系统时间，避免独立重启之后，数据过期删除
    int64_t startTime = 0;
    GetSysTimeVal(&startTime);

    char filePath[FILE_PATH] = {0};
    char fileName[FILE_PATH] = "test005";
    char logicName[128] = {0};
    char streamName[128] = {0};
    constexpr int64_t count = 20;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    char ip[count][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc", "lucy",
        "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    char messagetemp[count][160] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537", "0071 3521",
        "0010 0001", "", "3102 0022", "0000 0001", "9021 6538", "0071 3522", "0010 0002", "0010 0000", "3102 0023",
        "0000 0002", "9021 6539", "0071 3523", "0010 0003"};
    char *message[count] = {0};
    char *ns[count] = {0};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = startTime + i;
        ns[i] = name[i];
        message[i] = messagetemp[i];
    }
    system("rm -rf /home/<USER>/");
    (void)sprintf(filePath, "./gmsql3/%s.gmsql", fileName);
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c sql -f %s -s %s", filePath, g_connServerTsdb);
    ret = executeCommand(g_command, "gmimport create sql total success num: 2.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对逻辑表插入数据并查询
    C2Int8C4StrT obj1 = {.id = id, .time = time, .name = name, .ip = ip};
    for (int i = 0; i < count; i++) {
        obj1.ns[i] = ns[i];
        obj1.message[i] = message[i];
    }
    // 插入数据
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, &obj1, count, C2Int8C4StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecordTs(g_conn, g_stmt, g_tableName2, &obj1, count, C2Int8C4StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select count(*) from tsdb1;\" -s %s", g_connServerTsdb);
    system(g_command);
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select count(*) from tsdb2;\" -s %s", g_connServerTsdb);
    system(g_command);

    // 读数据
    ret = readRecordTs(g_conn, g_stmt, g_tableName, &obj1, count, C2Int8C4StrGet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecordTs(g_conn, g_stmt, g_tableName2, &obj1, count, C2Int8C4StrGet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 独立重启
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");

    // 重新建连
    ret = TestTsGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询数据
    // 读数据
    ret = readRecordTs(g_conn, g_stmt, g_tableName, &obj1, count, C2Int8C4StrGet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecordTs(g_conn, g_stmt, g_tableName2, &obj1, count, C2Int8C4StrGet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据并查询
    // 逻辑表
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, &obj1, count, C2Int8C4StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecordTs(g_conn, g_stmt, g_tableName2, &obj1, count, C2Int8C4StrSet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select count(*) from tsdb1;\" -s %s", g_connServerTsdb);
    system(g_command);
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select count(*) from tsdb2;\" -s %s", g_connServerTsdb);
    system(g_command);

    // 读数据
    ret = readRecordTs(g_conn, g_stmt, g_tableName, &obj1, count * 2, C2Int8C4StrGet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecordTs(g_conn, g_stmt, g_tableName2, &obj1, count * 2, C2Int8C4StrGet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // alter逻辑表
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol text;", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -sql \"select * from 'V\\$CATA_VERTEX_LABEL_INFO' "
        "where VERTEX_LABEL_NAME = '%s';\" -s %s",
        g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "\"name\": \"newcol\"", "\"type\": \"string\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "alter table %s add Column newcol text;", g_tableName2);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -sql \"select * from 'V\\$CATA_VERTEX_LABEL_INFO' "
        "where VERTEX_LABEL_NAME = '%s';\" -s %s",
        g_tableName2, g_connServerTsdb);
    ret = executeCommand(g_command, "\"name\": \"newcol\"", "\"type\": \"string\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropCmTable(g_stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
