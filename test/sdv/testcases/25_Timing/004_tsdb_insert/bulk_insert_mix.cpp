/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 【技术转交付】TSDB 基础压缩 测试
 * Author: jiangjincheng
 * Create: 2024-0313
 */
#include "gtest/gtest.h"
#include "t_rd_ts.h"

static GmcConnOptionsT *connOptions = NULL;
static GmcConnT *conn = NULL;
static GmcStmtT *stmt = NULL;
Status ret = 0;
char g_commonTable[] = "testdb";
char g_commonTable_1[] = "testdb_1";
char g_tempFileDir[500] = {0};
bool eof = false;
// 当该值为false时，说明并发执行的插入动作或者ddl失败了
bool isBulkInsertConcurrentExcuteSuccessed = true;

class BulkInsertMix : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        InitTsCiCfg();
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        // 创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        ret = TestTsGmcConnect(&conn, &stmt, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        GmcConnOptionsDestroy(connOptions);
        GmcFreeStmt(stmt);
        GmcDisconnect(conn);
        close_epoll_thread();
        testEnvClean();
        RecoverTsCiCfg();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void BulkInsertMix::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        printf("get env PWD fail.\n");
    }
    AW_MACRO_ASSERT_NOTNULL(pwdDir);
    (void)sprintf(g_tempFileDir, "%s/tempfile", pwdDir);
    AW_FUN_Log(LOG_STEP, "test start.");
}

void BulkInsertMix::TearDown()
{
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
}

int DropCmTable(char *tableName)
{
    int ret = 0;
    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    return ret;
}

int CreateCmTable(char *tableName)
{
    int ret = 0;
    (void)DropCmTable(tableName);
    // 执行DropCmTable时若果没有该表的时候会触发一次1004000错误
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_DATA_EXCEPTION);
    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, num integer) with (time_col = 'time', "
        "interval= '1 hour', compression='fast');",
        tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    return ret;
}

typedef struct {
    GmcStmtT *stmt;
    char *tableName;
    int64_t count;
    int columnSize;
    int64_t id[50001];
    int64_t time[50001];
    int64_t num[50001];
} InsertStmt;

void *BulkInsertConcurrent(void *arg)
{
    InsertStmt insertStmt = *(InsertStmt *)arg;
    ret = BlukInsert(insertStmt.stmt, insertStmt.tableName, insertStmt.count, insertStmt.columnSize, insertStmt.id,
        insertStmt.time, insertStmt.num);
    if (ret != GMERR_OK) {
        isBulkInsertConcurrentExcuteSuccessed = false;
    }
    return nullptr;
}

// 同一张物理表注入操作并发 预期：成功
TEST_F(BulkInsertMix, Timing_004_BulkInsertMix_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int threadCount = 2;
    pthread_t tid[threadCount];
    isBulkInsertConcurrentExcuteSuccessed = true;
    ret = CreateCmTable(g_commonTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 一次性插入100条数据，时间跨度小于1h，数据会存储在同一张物理表内
    constexpr int64_t count = 100;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t num[count] = {0};
    int64_t id_other[count] = {0};
    int64_t time_other[count] = {0};
    int64_t num_other[count] = {0};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1695042000 + i;
        num[i] = 2000000000 + i;
        id_other[i] = 1000 + i;
        time_other[i] = 1695043000 + i;
        num_other[i] = 2000001000 + i;
    }

    static GmcConnT *conn_1 = NULL;
    static GmcStmtT *stmt_1 = NULL;
    ret = TestTsGmcConnect(&conn_1, &stmt_1, 0);

    InsertStmt insertStmt = {0};
    insertStmt.stmt = stmt_1;
    insertStmt.tableName = g_commonTable;
    insertStmt.count = count;
    insertStmt.columnSize = 3;
    memcpy(insertStmt.id, id, sizeof(id));
    memcpy(insertStmt.time, time, sizeof(time));
    memcpy(insertStmt.num, num, sizeof(num));
    pthread_create(&tid[0], NULL, BulkInsertConcurrent, &insertStmt);

    InsertStmt insertStmt_other = {0};
    insertStmt_other.stmt = stmt;
    insertStmt_other.tableName = g_commonTable;
    insertStmt_other.count = count;
    insertStmt_other.columnSize = 3;
    memcpy(insertStmt_other.id, id_other, sizeof(id_other));
    memcpy(insertStmt_other.time, time_other, sizeof(time_other));
    memcpy(insertStmt_other.num, num_other, sizeof(num_other));
    pthread_create(&tid[1], NULL, BulkInsertConcurrent, &insertStmt_other);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    // 判断并发注入都成功了，isBulkInsertConcurrentExcuteSuccessed为true
    AW_MACRO_ASSERT_EQ_BOOL(true, isBulkInsertConcurrentExcuteSuccessed);

    GmcFreeStmt(stmt_1);
    GmcDisconnect(conn_1);
    AW_MACRO_EXPECT_EQ_INT(DropCmTable(g_commonTable), GMERR_OK);
}

// 同一张逻辑表注入操作并发 预期：成功
TEST_F(BulkInsertMix, Timing_004_BulkInsertMix_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int threadCount = 2;
    pthread_t tid[threadCount];
    isBulkInsertConcurrentExcuteSuccessed = true;
    ret = CreateCmTable(g_commonTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 一次性插入50001条数据，时间跨度远大于1h，数据不会存储在同一张物理表内，但会存储在同一张逻辑表内
    constexpr int64_t count = 50001;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t num[count] = {0};
    int64_t id_other[count] = {0};
    int64_t time_other[count] = {0};
    int64_t num_other[count] = {0};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1695042000 + i;
        num[i] = 2000000000 + i;
        id_other[i] = 1000 + i;
        time_other[i] = 1695043000 + i;
        num_other[i] = 2000001000 + i;
    }

    static GmcConnT *conn_1 = NULL;
    static GmcStmtT *stmt_1 = NULL;
    ret = TestTsGmcConnect(&conn_1, &stmt_1, 0);

    InsertStmt insertStmt = {0};
    insertStmt.stmt = stmt_1;
    insertStmt.tableName = g_commonTable;
    insertStmt.count = count;
    insertStmt.columnSize = 3;
    memcpy(insertStmt.id, id, sizeof(id));
    memcpy(insertStmt.time, time, sizeof(time));
    memcpy(insertStmt.num, num, sizeof(num));
    pthread_create(&tid[0], NULL, BulkInsertConcurrent, &insertStmt);

    InsertStmt insertStmt_other = {0};
    insertStmt_other.stmt = stmt;
    insertStmt_other.tableName = g_commonTable;
    insertStmt_other.count = count;
    insertStmt_other.columnSize = 3;
    memcpy(insertStmt_other.id, id_other, sizeof(id_other));
    memcpy(insertStmt_other.time, time_other, sizeof(time_other));
    memcpy(insertStmt_other.num, num_other, sizeof(num_other));
    pthread_create(&tid[1], NULL, BulkInsertConcurrent, &insertStmt_other);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    // 判断并发注入都成功了，isBulkInsertConcurrentExcuteSuccessed为true
    AW_MACRO_ASSERT_EQ_BOOL(true, isBulkInsertConcurrentExcuteSuccessed);

    GmcFreeStmt(stmt_1);
    GmcDisconnect(conn_1);
    AW_MACRO_EXPECT_EQ_INT(DropCmTable(g_commonTable), GMERR_OK);
}

// 不同表注入操作并发 预期：成功
TEST_F(BulkInsertMix, Timing_004_BulkInsertMix_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int threadCount = 2;
    pthread_t tid[threadCount];
    isBulkInsertConcurrentExcuteSuccessed = true;
    ret = CreateCmTable(g_commonTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateCmTable(g_commonTable_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    constexpr int64_t count = 50001;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t num[count] = {0};
    int64_t id_other[count] = {0};
    int64_t time_other[count] = {0};
    int64_t num_other[count] = {0};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1695042000 + i;
        num[i] = 2000000000 + i;
        id_other[i] = 1000 + i;
        time_other[i] = 1695043000 + i;
        num_other[i] = 2000001000 + i;
    }

    static GmcConnT *conn_1 = NULL;
    static GmcStmtT *stmt_1 = NULL;
    ret = TestTsGmcConnect(&conn_1, &stmt_1, 0);

    InsertStmt insertStmt = {0};
    insertStmt.stmt = stmt_1;
    insertStmt.tableName = g_commonTable;
    insertStmt.count = count;
    insertStmt.columnSize = 3;
    memcpy(insertStmt.id, id, sizeof(id));
    memcpy(insertStmt.time, time, sizeof(time));
    memcpy(insertStmt.num, num, sizeof(num));
    pthread_create(&tid[0], NULL, BulkInsertConcurrent, &insertStmt);

    InsertStmt insertStmt_other = {0};
    insertStmt_other.stmt = stmt;
    insertStmt_other.tableName = g_commonTable_1;
    insertStmt_other.count = count;
    insertStmt_other.columnSize = 3;
    memcpy(insertStmt_other.id, id_other, sizeof(id_other));
    memcpy(insertStmt_other.time, time_other, sizeof(time_other));
    memcpy(insertStmt_other.num, num_other, sizeof(num_other));
    pthread_create(&tid[1], NULL, BulkInsertConcurrent, &insertStmt_other);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    // 判断并发注入都成功了，isBulkInsertConcurrentExcuteSuccessed为true
    AW_MACRO_ASSERT_EQ_BOOL(true, isBulkInsertConcurrentExcuteSuccessed);

    GmcFreeStmt(stmt_1);
    GmcDisconnect(conn_1);
    AW_MACRO_EXPECT_EQ_INT(DropCmTable(g_commonTable), GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(DropCmTable(g_commonTable_1), GMERR_OK);
}

void *DroptableConcurrent(void *arg)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    InsertStmt insertStmt = *(InsertStmt *)arg;
    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", insertStmt.tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(insertStmt.stmt, sqlCmd, cmdLen);
    if (ret != GMERR_OK) {
        isBulkInsertConcurrentExcuteSuccessed = false;
    }
    return nullptr;
}

// 注入操作与逻辑表的DDL操作并发执行 预期：成功
TEST_F(BulkInsertMix, Timing_004_BulkInsertMix_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int threadCount = 2;
    pthread_t tid[threadCount];
    isBulkInsertConcurrentExcuteSuccessed = true;
    ret = CreateCmTable(g_commonTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    constexpr int64_t count = 50001;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t num[count] = {0};
    int64_t id_other[count] = {0};
    int64_t time_other[count] = {0};
    int64_t num_other[count] = {0};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1695042000 + i;
        num[i] = 2000000000 + i;
        id_other[i] = 1000 + i;
        time_other[i] = 1695043000 + i;
        num_other[i] = 2000001000 + i;
    }

    static GmcConnT *conn_1 = NULL;
    static GmcStmtT *stmt_1 = NULL;
    ret = TestTsGmcConnect(&conn_1, &stmt_1, 0);

    InsertStmt insertStmt = {0};
    insertStmt.stmt = stmt_1;
    insertStmt.tableName = g_commonTable;
    insertStmt.count = count;
    insertStmt.columnSize = 3;
    memcpy(insertStmt.id, id, sizeof(id));
    memcpy(insertStmt.time, time, sizeof(time));
    memcpy(insertStmt.num, num, sizeof(num));
    pthread_create(&tid[0], NULL, BulkInsertConcurrent, &insertStmt);

    InsertStmt insertStmt_other = {0};
    insertStmt_other.stmt = stmt;
    insertStmt_other.tableName = g_commonTable;
    pthread_create(&tid[1], NULL, DroptableConcurrent, &insertStmt_other);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    // 判断注入或者ddl执行失败了，isBulkInsertConcurrentExcuteSuccessed为false
    AW_MACRO_ASSERT_EQ_BOOL(false, isBulkInsertConcurrentExcuteSuccessed);

    GmcFreeStmt(stmt_1);
    GmcDisconnect(conn_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, DropCmTable(g_commonTable));
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_UNDEFINED_TABLE, GMERR_UNEXPECTED_NULL_VALUE);
}
