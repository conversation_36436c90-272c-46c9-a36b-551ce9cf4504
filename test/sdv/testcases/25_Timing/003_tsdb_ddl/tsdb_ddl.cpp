/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 【技术转交付】TSDB 基础DDL 测试
 * Author: jiangjjincheng
 * Create: 2024-03-12
 */

#include "gtest/gtest.h"
#include "t_rd_ts.h"

static GmcConnOptionsT *connOptions = NULL;
static GmcConnT *conn = NULL;
static GmcStmtT *stmt = NULL;
Status ret = 0;
char g_cStoreDir[64] = {0};
char g_commonTable[20] = "testdb";
char g_fdClient[512] = "ls -l /proc/`pidof tsdb_ddl`/fd |wc -l";
char g_fdServer[512] = "ls -l /proc/`pidof gmserver_ts`/fd |wc -l";
int32_t fdClientBefore = 0;
int32_t fdClientAfter = 0;
int32_t fdServerBefore = 0;
int32_t fdServerAfter = 0;

int32_t GetViewFieldResultValue(const char *viewName)
{
    int maxCmdSize = 256;
    char cmdOutput[maxCmdSize];
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));

    char command[maxCmdSize];
    (void)snprintf(command, maxCmdSize, "%s", viewName);

    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        printf("popen(%s) error./n", command);
    }
    EXPECT_NE((void *)NULL, pf);
    while (NULL != fgets(cmdOutput, maxCmdSize, pf)) {
    };
    pclose(pf);

    return atoi(cmdOutput);
}

class TsdbDDL : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/stop.sh -ts");
        TsDefulatDbFileClean();
        system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"dbFilesMaxCnt=200\" \"spaceMaxNum=200\" ");
        system("sh $TEST_HOME/tools/start.sh -ts");
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        // 创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        fdClientBefore = GetViewFieldResultValue(g_fdClient);
        fdServerBefore = GetViewFieldResultValue(g_fdServer);
        ret = TestTsGmcConnect(&conn, &stmt, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        GmcConnOptionsDestroy(connOptions);
        GmcFreeStmt(stmt);
        GmcDisconnect(conn);
        fdClientAfter = GetViewFieldResultValue(g_fdClient);
        // 开始写日志的时机在增量编译和非增量编译下不一样，增量编译下会多一个日志fd，冕泓建议按照两张差值小于1校验
        EXPECT_LE(fdClientAfter - fdClientBefore, 1);
        fdServerAfter = GetViewFieldResultValue(g_fdServer);
        AW_MACRO_EXPECT_EQ_INT(fdServerBefore, fdServerAfter);
        close_epoll_thread();
        testEnvClean();
        RecoverTsCiCfg();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void TsdbDDL::SetUp()
{
    // 不同设备的g_cStoreDir路径不同
#if defined RUN_INDEPENDENT
    ret =
        TestGetResultCommand("cat ../common/logicTablePath.txt | tr -d '$\r'", NULL, g_cStoreDir, sizeof(g_cStoreDir));
#else
    ret =
        TestGetResultCommand("cat ../common/usglogicTablePath.txt | tr -d '$\r'", NULL, g_cStoreDir, sizeof(g_cStoreDir));
#endif
   
    EXPECT_EQ(0, ret);
    AW_CHECK_LOG_BEGIN();
}

void TsdbDDL::TearDown()
{
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
}

// 参数合法，创建时序分区表  预期：创建成功
TEST_F(TsdbDDL, Timing_003_TsdbDDL_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATA_EXCEPTION);
    char *sqlCmd = (char *)"create table testdb(id integer, T1 integer, age integer) with (time_col = "
                           "'T1', interval= '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 时序表类型校验
    GmcVertexLabelTypeE vertexLabelType;
    ret = GmcGetVertexLabelTypeByName(stmt, "testdb", &vertexLabelType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(vertexLabelType, GMC_VERTEX_TYPE_TS);

    GmcDtlLabelTypeE vertexLabelType1;
    ret = GmcGetDatalogLabelTypeByName(stmt, "testdb", &vertexLabelType1);
    // 报错
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    sqlCmd = (char *)"drop table testdb;";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 参数合法，interval为1hour，创建时序分区表  预期：创建成功
TEST_F(TsdbDDL, Timing_003_TsdbDDL_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table testdb(id integer, T1 integer) with (time_col = 'T1', interval "
                           "= '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sqlCmd = (char *)"drop table testdb;";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 参数合法，interval为1day，创建时序分区表  预期：创建成功
TEST_F(TsdbDDL, Timing_003_TsdbDDL_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table testdb(id integer, T1 integer) with (time_col = 'T1', interval "
                           "= '1 day');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sqlCmd = (char *)"drop table testdb;";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 参数合法，interval为1 month，创建时序分区表  预期：创建成功
TEST_F(TsdbDDL, Timing_003_TsdbDDL_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table testdb(id integer, T1 integer) with (time_col = 'T1', interval "
                           "= '1 month');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sqlCmd = (char *)"drop table testdb;";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 参数合法，interval为1year，创建时序分区表  预期：创建成功
TEST_F(TsdbDDL, Timing_003_TsdbDDL_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table testdb(id integer, T1 integer) with (time_col = 'T1', interval "
                           "= '1 year');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sqlCmd = (char *)"drop table testdb;";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 创建时序分区表，table_name长度超过128  预期：创建失败，返回错误
TEST_F(TsdbDDL, Timing_003_TsdbDDL_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    constexpr int len = 128;
    char name[len + 1] = {0};
    (void)memset(name, 'a', len);
    name[len] = '\0';

    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "create table %s(id integer, T1 integer) with (time_col = 'T1', interval= '1 year');", name);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NAME_TOO_LONG, ret);

    char *sqlCmd2 = (char *)"create table testdb(id integer, T1 integer) with (time_col = 'T1', interval "
                            "= '1 year');";
    cmdLen = strlen(sqlCmd2);
    ret = GmcExecDirect(stmt, sqlCmd2, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sqlCmd2 = (char *)"drop table testdb;";
    cmdLen = strlen(sqlCmd2);
    ret = GmcExecDirect(stmt, sqlCmd2, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_NAME_TOO_LONG, GMERR_DATA_EXCEPTION);
}

// 创建时序分区表，column_name长度超过128  预期：创建失败，返回错误
TEST_F(TsdbDDL, Timing_003_TsdbDDL_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    constexpr int len = 129;
    char name[len + 1] = {0};
    (void)memset(name, 'a', len);
    name[len] = '\0';

    char sqlCmd[512] = {0};
    (void)sprintf(
        sqlCmd, "create table testdb(id integer, %s integer) with (time_col = '%s', interval= '1 year');", name, name);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NAME_TOO_LONG, ret);

    char *sqlCmd2 = (char *)"create table testdb(id integer, T1 integer) with (time_col = 'T1', interval "
                            "= '1 year');";
    cmdLen = strlen(sqlCmd2);
    ret = GmcExecDirect(stmt, sqlCmd2, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sqlCmd2 = (char *)"drop table testdb;";
    cmdLen = strlen(sqlCmd2);
    ret = GmcExecDirect(stmt, sqlCmd2, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_NAME_TOO_LONG, GMERR_DATA_EXCEPTION);
}

// 创建时序分区表，column数量超过限制256  预期：创建失败，返回错误
TEST_F(TsdbDDL, Timing_003_TsdbDDL_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int colCount = 257;
    char sql[4096] = "CREATE TABLE students (id integer";
    for (int i = 0; i < colCount - 1; i++) {
        char col[15] = {0};
        sprintf(col, ", id%d integer", i);
        strcat(sql, col);
    }
    strcat(sql, ") with (time_col = 'id', interval= '1 year');");

    uint32_t cmdLen = strlen(sql);
    ret = GmcExecDirect(stmt, sql, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONFIGURATION_LIMIT_EXCEEDED, ret);

    char *sqlCmd2 = (char *)"create table testdb(id integer, T1 integer) with (time_col = 'T1', interval "
                            "= '1 year');";
    cmdLen = strlen(sqlCmd2);
    ret = GmcExecDirect(stmt, sqlCmd2, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sqlCmd2 = (char *)"drop table testdb;";
    cmdLen = strlen(sqlCmd2);
    ret = GmcExecDirect(stmt, sqlCmd2, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_CONFIGURATION_LIMIT_EXCEEDED, GMERR_DATA_EXCEPTION);
}

// 创建时序分区表，time_col指定不存在字段，预期：创建失败，返回错误
TEST_F(TsdbDDL, Timing_003_TsdbDDL_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table testdb(id integer, T1 integer) with (time_col = 'T2', interval "
                           "= '1 year');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    sqlCmd = (char *)"create table testdb(id integer, T1 integer) with (time_col = 'T1', interval = '1 year');";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sqlCmd = (char *)"drop table testdb;";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_INVALID_PROPERTY);
}

// 创建时序分区表，time_col指定非INTEGER类型字段，预期：创建失败，返回错误
TEST_F(TsdbDDL, Timing_003_TsdbDDL_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table testdb(id integer, T1 char(65535)) with (time_col = 'T1', interval "
                           "= '1 year');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    sqlCmd = (char *)"create table testdb(id integer, T1 integer) with (time_col = 'T1', interval = '1 year');";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sqlCmd = (char *)"drop table testdb;";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_INVALID_PARAMETER_VALUE, GMERR_DATA_EXCEPTION);
}

// 创建时序分区表，interval非法取值，不在（1 hour, 1 day, 1 month, 1 year）内，预期：创建失败，返回错误
TEST_F(TsdbDDL, Timing_003_TsdbDDL_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table testdb(id integer, T1 integer) with (time_col = 'T1', interval "
                           "= '2 year');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_JSON_CONTENT, ret);

    sqlCmd = (char *)"create table testdb(id integer, T1 integer) with (time_col = 'T1', interval = '1 year');";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sqlCmd = (char *)"drop table testdb;";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_INVALID_JSON_CONTENT, GMERR_DATA_EXCEPTION);
}

// 创建时序分区表，with只配置time_col，预期：创建失败，返回错误
TEST_F(TsdbDDL, Timing_003_TsdbDDL_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table testdb(id integer, T1 integer) with (time_col = 'T1');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_JSON_CONTENT, ret);

    sqlCmd = (char *)"create table testdb(id integer, T1 integer) with (time_col = 'T1', interval = '1 year');";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sqlCmd = (char *)"drop table testdb;";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_INVALID_JSON_CONTENT, GMERR_DATA_EXCEPTION);
}

// 创建时序分区表，with只配置interval，预期：创建失败，返回错误
TEST_F(TsdbDDL, Timing_003_TsdbDDL_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table testdb(id integer, T1 integer) with (interval = '1 year');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SEMANTIC_ERROR, ret);

    sqlCmd = (char *)"create table testdb(id integer, T1 integer) with (time_col = 'T1', interval = '1 year');";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sqlCmd = (char *)"drop table testdb;";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_SEMANTIC_ERROR);
}

// 创建时序分区表，字段名重复，类型不同，预期：创建失败，返回错误
TEST_F(TsdbDDL, Timing_003_TsdbDDL_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table testdb(id integer, T1 integer, T1 char(65535)) with (time_col = 'T1', "
                           "interval = '1 year');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DUPLICATE_COLUMN, ret);

    sqlCmd = (char *)"create table testdb(id integer, T1 integer) with (time_col = 'T1', interval = '1 year');";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sqlCmd = (char *)"drop table testdb;";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_DUPLICATE_COLUMN);
}

// 创建时序分区表，配置多个time_col，预期：创建失败，返回错误
TEST_F(TsdbDDL, Timing_003_TsdbDDL_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table testdb(id integer, T1 integer) with (time_col = 'T1', time_col = 'id', "
                           "interval = '1 year');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    sqlCmd = (char *)"create table testdb(id integer, T1 integer) with (time_col = 'T1', interval = '1 year');";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sqlCmd = (char *)"drop table testdb;";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_INVALID_PARAMETER_VALUE);
}

// 删除存在的时序分区表，预期：删除成功
TEST_F(TsdbDDL, Timing_003_TsdbDDL_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table testdb(id integer, T1 integer) with (time_col = 'T1', interval = '1 year');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sqlCmd = (char *)"drop table testdb;";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 删除不存在的时序分区表，预期：删除失败
TEST_F(TsdbDDL, Timing_003_TsdbDDL_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table testdb(id integer, T1 integer) with (time_col = 'T1', interval = '1 year');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sqlCmd = (char *)"drop table testdb2;";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);

    sqlCmd = (char *)"drop table testdb;";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNDEFINED_TABLE);
}

// 创建表内容完全相同的已存在的时序表 预期失败
TEST_F(TsdbDDL, Timing_003_TsdbDDL_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table testdb(id integer, T1 integer) with (time_col = 'T1', interval = '1 year');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sqlCmd = (char *)"create table testdb(id integer, T1 integer) with (time_col = 'T1', interval = '1 year');";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DUPLICATE_TABLE, ret);

    sqlCmd = (char *)"drop table testdb;";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DUPLICATE_TABLE, GMERR_DATA_EXCEPTION);
}

// 创建表内容不同的表名存在的时序表 预期失败
TEST_F(TsdbDDL, Timing_003_TsdbDDL_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table testdb(id integer, T1 integer) with (time_col = 'T1', interval = '1 year');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sqlCmd = (char
            *)"create table testdb(id integer, T1 integer, name char(64)) with (time_col = 'T1', interval = '1 hour');";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DUPLICATE_TABLE, ret);

    sqlCmd = (char *)"drop table testdb;";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DUPLICATE_TABLE, GMERR_DATA_EXCEPTION);
}

// 建表时，column数据类型未指定 预期失败
TEST_F(TsdbDDL, Timing_003_TsdbDDL_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table testdb(id, T1 integer) with (time_col = 'T1', interval = '1 year');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATATYPE_MISMATCH, ret);

    sqlCmd = (char *)"create table testdb(id integer, T1 integer) with (time_col = 'T1', interval = '1 year');";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sqlCmd = (char *)"drop table testdb;";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DATATYPE_MISMATCH, GMERR_DATA_EXCEPTION);
}

// 建表时，没有with option项 预期失败
TEST_F(TsdbDDL, Timing_003_TsdbDDL_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table testdb(id integer, T1 integer) with (time_col = 'T1', interval = '1 year');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sqlCmd = (char *)"create table testdb1(id integer, T1 integer) with";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    sqlCmd = (char *)"drop table testdb;";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_SYNTAX_ERROR, GMERR_DATA_EXCEPTION);
}

// 建表时，没有with 关键字 预期失败
TEST_F(TsdbDDL, Timing_003_TsdbDDL_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table testdb(id integer, T1 integer) with (time_col = 'T1', interval = '1 year');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sqlCmd = (char *)"create table testdb1(id integer, T1 integer) (time_col = 'T1', interval = '1 year')";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    sqlCmd = (char *)"drop table testdb;";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_SYNTAX_ERROR, GMERR_DATA_EXCEPTION);
}

// 建表时，缺少表名 预期失败
TEST_F(TsdbDDL, Timing_003_TsdbDDL_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table(id integer, T1 integer) with (time_col = 'T1', interval = '1 year');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    sqlCmd = (char *)"create table testdb(id integer, T1 integer) with (time_col = 'T1', interval = '1 year');";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sqlCmd = (char *)"drop table testdb;";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_SYNTAX_ERROR, GMERR_DATA_EXCEPTION);
}

// 建表时只有一个数据列 预期成功
TEST_F(TsdbDDL, Timing_003_TsdbDDL_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table testdb(T1 integer) with (time_col = 'T1', interval = '1 year');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sqlCmd = (char *)"drop table testdb;";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 建表时表名带特殊字符 预期失败
TEST_F(TsdbDDL, Timing_003_TsdbDDL_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd =
        (char *)"create table testdb@%*(id integer, T1 integer) with (time_col = 'T1', interval = '1 year');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    sqlCmd = (char *)"create table testdb(id integer, T1 integer) with (time_col = 'T1', interval = '1 year');";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sqlCmd = (char *)"drop table testdb;";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_SYNTAX_ERROR, GMERR_DATA_EXCEPTION);
}

// 建表时出现两个表名 预期失败
TEST_F(TsdbDDL, Timing_003_TsdbDDL_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd =
        (char *)"create table testdb testdb1(id integer, T1 integer) with (time_col = 'T1', interval = '1 year');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    sqlCmd = (char *)"create table testdb(id integer, T1 integer) with (time_col = 'T1', interval = '1 year');";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sqlCmd = (char *)"drop table testdb;";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_SYNTAX_ERROR, GMERR_DATA_EXCEPTION);
}

// 建表时with后出现其他字符 预期失败
TEST_F(TsdbDDL, Timing_003_TsdbDDL_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd =
        (char *)"create table testdb (id integer, T1 integer) with testdb (time_col = 'T1', interval = '1 year');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    sqlCmd = (char *)"create table testdb(id integer, T1 integer) with (time_col = 'T1', interval = '1 year');";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sqlCmd = (char *)"drop table testdb;";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_SYNTAX_ERROR, GMERR_DATA_EXCEPTION);
}

// 连续建表后，连续删表 预期成功
TEST_F(TsdbDDL, Timing_003_TsdbDDL_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    for (int i = 0; i < 100; i++) {
        (void)sprintf(
            sqlCmd, "create table testdb%d(id integer, T1 integer) with (time_col = 'T1', interval = '1 year');", i);
        cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (int i = 0; i < 100; i++) {
        (void)sprintf(sqlCmd, "drop table testdb%d;", i);
        cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

void *CreateNewTSDBTableWhenDeleteExistedTable(void *arg)
{
    GmcStmtT *stmt1 = *(GmcStmtT **)arg;
    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    for (int i = 100; i < 200; i++) {
        (void)sprintf(
            sqlCmd, "create table testdb%d(id integer, T1 integer) with (time_col = 'T1', interval = '1 year');", i);
        cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(stmt1, sqlCmd, cmdLen);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    }
    return NULL;
}

void *DeleteExistedTableWhenCreateNewTSDBTable(void *arg)
{
    GmcStmtT *stmt2 = *(GmcStmtT **)arg;
    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    for (int i = 0; i < 100; i++) {
        (void)sprintf(sqlCmd, "drop table testdb%d;", i);
        cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(stmt2, sqlCmd, cmdLen);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    }
    return nullptr;
}

// 连续建表后，建新表与删旧表动作并发 预期成功
TEST_F(TsdbDDL, Timing_003_TsdbDDL_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int threadCount = 2;
    pthread_t tid[threadCount];
    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    for (int i = 0; i < 100; i++) {
        (void)sprintf(
            sqlCmd, "create table testdb%d(id integer, T1 integer) with (time_col = 'T1', interval = '1 year');", i);
        cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    static GmcConnT *conn_1 = NULL;
    static GmcStmtT *stmt_1 = NULL;
    ret = TestTsGmcConnect(&conn_1, &stmt_1, 0);

    pthread_create(&tid[0], NULL, CreateNewTSDBTableWhenDeleteExistedTable, &stmt);
    pthread_create(&tid[1], NULL, DeleteExistedTableWhenCreateNewTSDBTable, &stmt_1);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    for (int i = 100; i < 200; i++) {
        (void)sprintf(sqlCmd, "drop table testdb%d;", i);
        cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    GmcFreeStmt(stmt_1);
    GmcDisconnect(conn_1);
}

// 删除空表  预期：正常删除
TEST_F(TsdbDDL, Timing_003_TsdbDDL_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "create table testdb(id integer, T1 integer, age integer) with (time_col = 'T1', interval= '1 hour')");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   (void)sprintf(sqlCmd, "drop table testdb;");
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)sprintf(sqlCmd, "select * from testdb;");
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNDEFINED_TABLE);
}

// 删除存在数据的时序表，观测持久化目录空间、内存空间是否释放 预期：正常删除，空间释放
TEST_F(TsdbDDL, Timing_003_TsdbDDL_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table testdb(id integer, T1 integer, age integer) with (time_col = "
                           "'T1', interval= '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 注入数据
    constexpr int64_t count = 100;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int32_t age[count] = {0};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1695042000 + i;
        age[i] = i;
    }

    ret = BlukInsert(stmt, g_commonTable, count, 3, id, time, age);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sqlCmd = (char *)"drop table testdb;";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(1);
    AW_MACRO_ASSERT_EQ_BOOL(true, IsDirEmpty(g_cStoreDir));
}

// 删除后重建同名时序表，验证注入查询 预期：正常注入查询
TEST_F(TsdbDDL, Timing_003_TsdbDDL_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *createSqlCmd = (char *)"create table testdb(id integer, T1 integer, age integer) with (time_col = "
                                 "'T1', interval= '1 hour');";
    uint32_t createCmdLen = strlen(createSqlCmd);
    ret = GmcExecDirect(stmt, createSqlCmd, createCmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 注入数据
    constexpr int64_t count = 100;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int32_t age[count] = {0};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1695042000 + i;
        age[i] = i;
    }
    ret = BlukInsert(stmt, g_commonTable, count, 3, id, time, age);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *dropSqlCmd = (char *)"drop table testdb;";
    uint32_t dropCmdLen = strlen(dropSqlCmd);
    ret = GmcExecDirect(stmt, dropSqlCmd, dropCmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重建表
    ret = GmcExecDirect(stmt, createSqlCmd, createCmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 注入数据
    ret = BlukInsert(stmt, g_commonTable, count, 3, id, time, age);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecDirect(stmt, dropSqlCmd, dropCmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(1);

    AW_MACRO_ASSERT_EQ_BOOL(true, IsDirEmpty(g_cStoreDir));
}

// 开启fast压缩，注入数据，删除表 预期：正常删除
TEST_F(TsdbDDL, Timing_003_TsdbDDL_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table testdb(id integer, T1 integer, age integer) with (time_col = "
                           "'T1', interval= '1 hour', compression='fast');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 注入数据
    constexpr int64_t count = 100;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int32_t age[count] = {0};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1695042000 + i;
        age[i] = i;
    }
    ret = BlukInsert(stmt, g_commonTable, count, 3, id, time, age);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sqlCmd = (char *)"drop table testdb;";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(1);
    AW_MACRO_ASSERT_EQ_BOOL(true, IsDirEmpty(g_cStoreDir));
}

// 开启fast(rapidlz)压缩，注入数据，删除表 预期：正常删除
TEST_F(TsdbDDL, Timing_003_TsdbDDL_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table testdb(id integer, T1 integer, age integer) with (time_col = "
                           "'T1', interval= '1 hour', compression='fast(rapidlz)');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 注入数据
    constexpr int64_t count = 100;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int32_t age[count] = {0};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1695042000 + i;
        age[i] = i;
    }
    ret = BlukInsert(stmt, g_commonTable, count, 3, id, time, age);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sqlCmd = (char *)"drop table testdb;";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(1);
    AW_MACRO_ASSERT_EQ_BOOL(true, IsDirEmpty(g_cStoreDir));
}

// 开启fast(zstar)压缩，注入数据，删除表 预期：正常删除
TEST_F(TsdbDDL, Timing_003_TsdbDDL_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table testdb(id integer, T1 integer, age integer) with (time_col = "
                           "'T1', interval= '1 hour', compression='fast(zstar)');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 注入数据
    constexpr int64_t count = 100;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int32_t age[count] = {0};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1695042000 + i;
        age[i] = i;
    }
    ret = BlukInsert(stmt, g_commonTable, count, 3, id, time, age);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sqlCmd = (char *)"drop table testdb;";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(1);
    AW_MACRO_ASSERT_EQ_BOOL(true, IsDirEmpty(g_cStoreDir));
}

// 相同表重复删除 预期：重复删除报错
TEST_F(TsdbDDL, Timing_003_TsdbDDL_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int operateCount = 100;
    char *createSqlCmd = (char *)"create table testdb(id integer, T1 integer, age integer) with (time_col = "
                                 "'T1', interval= '1 hour');";
    uint32_t createCmdLen = strlen(createSqlCmd);
    ret = GmcExecDirect(stmt, createSqlCmd, createCmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 注入数据
    constexpr int64_t count = 100;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int32_t age[count] = {0};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1695042000 + i;
        age[i] = i;
    }
    ret = BlukInsert(stmt, g_commonTable, count, 3, id, time, age);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *dropSqlCmd = (char *)"drop table testdb;";
    uint32_t dropCmdLen = strlen(dropSqlCmd);
    ret = GmcExecDirect(stmt, dropSqlCmd, dropCmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(1);
    ret = GmcExecDirect(stmt, dropSqlCmd, dropCmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    sleep(1);
    AW_MACRO_ASSERT_EQ_BOOL(true, IsDirEmpty(g_cStoreDir));

    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_UNDEFINED_TABLE, GMERR_DATA_EXCEPTION);
}

// 反复创建删除同名表，查看内存，持久化空间是否异常 预期：无异常
TEST_F(TsdbDDL, Timing_003_TsdbDDL_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int operateCount = 100;
    char *createSqlCmd = (char *)"create table testdb(id integer, T1 integer, age integer) with (time_col = "
                                 "'T1', interval= '1 hour');";
    uint32_t createCmdLen = strlen(createSqlCmd);
    ret = GmcExecDirect(stmt, createSqlCmd, createCmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 注入数据
    constexpr int64_t count = 100;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int32_t age[count] = {0};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1695042000 + i;
        age[i] = i;
    }
    ret = BlukInsert(stmt, g_commonTable, count, 3, id, time, age);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *dropSqlCmd = (char *)"drop table testdb;";
    uint32_t dropCmdLen = strlen(dropSqlCmd);
    ret = GmcExecDirect(stmt, dropSqlCmd, dropCmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(1);
    int beforeSize = GetDirSize(g_cStoreDir);
    for (int i = 0; i < operateCount; i++) {
        // 重建表
        ret = GmcExecDirect(stmt, createSqlCmd, createCmdLen);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 注入数据
        ret = BlukInsert(stmt, g_commonTable, count, 3, id, time, age);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecDirect(stmt, dropSqlCmd, dropCmdLen);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    sleep(1);
    int afterSize = GetDirSize(g_cStoreDir);
    AW_MACRO_EXPECT_EQ_INT(afterSize, beforeSize);
    AW_MACRO_ASSERT_EQ_BOOL(true, IsDirEmpty(g_cStoreDir));
}

typedef struct {
    GmcStmtT *stmt;
    int i;
} DropStmt;

void *DropTable(void *arg)
{
    int eachThreadTables = 20;
    DropStmt dropStmt = *(DropStmt *)arg;
    char tempCmd[256] = {0};
    for (int i = 0; i < eachThreadTables; i++) {
        (void)sprintf(tempCmd, "drop table testdb_%d_%d;", dropStmt.i, i);
        uint32_t cmdLen = strlen(tempCmd);
        ret = GmcExecDirect(dropStmt.stmt, tempCmd, cmdLen);
        EXPECT_EQ(GMERR_OK, ret);
        sleep(1);
    }
    return NULL;
}

// 不同表并发删除 预期：正常删除
TEST_F(TsdbDDL, Timing_003_TsdbDDL_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int eachThreadTables = 20;
    int threadCount = 2;
    pthread_t tid[threadCount];
    char tempCmd[1024] = {0};

    constexpr int64_t count = 100;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int32_t age[count] = {0};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1695042000 + i;
        age[i] = i;
    }
    for (int i = 0; i < threadCount; i++) {
        for (int n = 0; n < eachThreadTables; n++) {
            (void)sprintf(g_commonTable, "testdb_%d_%d", i, n);
            (void)sprintf(tempCmd,
                "create table testdb_%d_%d(id integer, T1 integer, age integer) with (time_col = 'T1', interval= '1 "
                "hour');",
                i, n);
            uint32_t cmdLen = strlen(tempCmd);
            ret = GmcExecDirect(stmt, tempCmd, cmdLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 注入数据
            ret = BlukInsert(stmt, g_commonTable, count, 3, id, time, age);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    static GmcConnT *conn_1 = NULL;
    static GmcStmtT *stmt_1 = NULL;
    ret = TestTsGmcConnect(&conn_1, &stmt_1, 0);

    DropStmt dropStmt = {stmt, 0};
    DropStmt dropStmt1 = {stmt_1, 1};
    pthread_create(&tid[0], NULL, DropTable, &dropStmt);
    pthread_create(&tid[1], NULL, DropTable, &dropStmt1);

    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    sleep(5);
    GmcFreeStmt(stmt_1);
    GmcDisconnect(conn_1);
    AW_MACRO_ASSERT_EQ_BOOL(true, IsDirEmpty(g_cStoreDir));
}

// 039.建时序表达到数目上限，再全部删除  预期：建表成功，删表成功
TEST_F(TsdbDDL, Timing_003_TsdbDDL_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char fdCmd[256] = {0};
    (void)sprintf(fdCmd, "ls -l /proc/`pidof gmserver_ts`/fd |wc -l");
    int32_t fdBefore = 0;
    int32_t fdAfter = 0;
    fdBefore = GetViewFieldResultValue(fdCmd);
    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    // 建表达上限1021(系统会占3个，目前只能建到1017张表，因为bufferPoolSize设置的不够大，6月后会调整一下)
    for (int i = 0; i < 197; i++) {
        (void)sprintf(
            sqlCmd, "create table testdb%d(id integer, T1 integer) with (time_col = 'T1', interval = '1 hour');", i);
        cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
        if (i == 198) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_RESOURCES, ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 197, fdAfter);
    for (int i = 0; i < 197; i++) {
        (void)sprintf(sqlCmd, "drop table testdb%d", i);
        cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore, fdAfter);
}
