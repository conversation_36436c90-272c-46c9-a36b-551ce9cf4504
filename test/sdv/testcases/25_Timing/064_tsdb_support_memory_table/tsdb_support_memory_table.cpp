/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 时序支持内存表
 * Author: chenbangjun
 * Create: 2025-01-10
 */
#include "gtest/gtest.h"
#include "t_rd_ts.h"
#include "tsdb_memory_table.h"

static GmcConnOptionsT *connOptions = NULL;
static GmcConnT *conn = NULL;
static GmcStmtT *stmt = NULL;
char g_cStoreDir[64] = {0};
char intervalHour[] = "1 hour";
char intervalDay[] = "1 Day";
char intervalMonth[] = "1 Month";
char intervalYear[] = "1 year";
char *dir = getenv("GMDB_HOME");
char g_fdClient[512] = "ls -l /proc/`pidof tsdb_support_memory_table`/fd |wc -l";
int32_t fdServerBeforeOffset1 = 0;
int32_t fdServerBeforeOffset2 = 0;

int32_t GetViewFieldResultValue(const char *viewName)
{
    sleep(3);
    int maxCmdSize = 256;
    char cmdOutput[maxCmdSize];
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));

    char command[maxCmdSize];
    (void)snprintf(command, maxCmdSize, "%s", viewName);

    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        printf("popen(%s) error./n", command);
    }
    EXPECT_NE((void *)NULL, pf);
    while (NULL != fgets(cmdOutput, maxCmdSize, pf)) {
    };
    pclose(pf);

    return atoi(cmdOutput);
}

class TsdbSupportMemoryTable : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        
    }
    static void TearDownTestCase()
    {
        
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void TsdbSupportMemoryTable::SetUp()
{
// euler和仿真环境清共享内存，清理原有DB文件
#if defined RUN_INDEPENDENT
    system("sh $TEST_HOME/tools/stop.sh -f");
#endif
    // 清理内容放到setup里面避免构建上连跑时受影响
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts recover");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableVectorizedPushDown=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll监听线程
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
    ret =
        TestGetResultCommand("cat ../common/logicTablePath.txt | tr -d '$\r'", NULL, g_cStoreDir, sizeof(g_cStoreDir));
    EXPECT_EQ(0, ret);
}

void TsdbSupportMemoryTable::TearDown()
{
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
    GmcConnOptionsDestroy(connOptions);
    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    close_epoll_thread();
    testEnvClean();
    RecoverTsCiCfg();
    // 清理持久化文件避免影响其他用例
    TsDefulatDbFileClean();
}

// 001.建内存表时，max_size设置为1
// 预期：查询成功
TEST_F(TsdbSupportMemoryTable, Timing_064_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "create table %s(id integer, time integer, name char(64), ip inet, message blob(160),"
        " description text, id1 integer, time1 integer)with (engine = 'memory', max_size =1, "
        " time_col = 'time', interval = '1 hour');",
        g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 删表后增加重启操作，查看重启后是否存在该表
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 等待几秒，可能存在的建表时间
    sleep(3);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002.建内存表时，max_size设置为9223372036854775807
// 预期：查询成功
TEST_F(TsdbSupportMemoryTable, Timing_064_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "create table %s(id integer, time integer, name char(64), ip inet, message blob(160),"
        " description text, id1 integer, time1 integer)with (engine = 'memory', max_size =9223372036854775807,"
        " time_col = 'time', interval = '1 hour');",
        g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003.建内存表时engine、max_size关键字大小写混合
// 预期：查询成功
TEST_F(TsdbSupportMemoryTable, Timing_064_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char fdCmd[256] = {0};
    (void)sprintf(fdCmd, "ls -l /proc/`pidof gmserver_ts`/fd |wc -l");
    int32_t fdBefore = 0;
    int32_t fdAfter = 0;
    fdBefore = GetViewFieldResultValue(fdCmd);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "create table %s(id integer, time integer, name char(64), ip inet, message blob(160),"
        " description text, id1 integer, time1 integer)with (enGine = 'memory', max_Size =1,"
        " time_col = 'time', interval = '1 hour');",
        g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore, fdAfter);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore, fdAfter);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004.建内存表时engine和memory大小写混合
// 预期：查询成功
TEST_F(TsdbSupportMemoryTable, Timing_064_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "create table %s(id integer, time integer, name char(64), ip inet, message blob(160),"
        " description text, id1 integer, time1 integer)with (enGine = 'mEmOry', max_size =1,"
        " time_col = 'time', interval = '1 hour');",
        g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005.按语法创建内存表并注入顺序数据
// 预期：查询成功
TEST_F(TsdbSupportMemoryTable, Timing_064_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "create table %s(id integer, time integer, name char(64), ip inet,"
        " description text, message blob(160),  id1 integer, time1 integer, " 
        "INDEX idx1(time1))with (enGine = 'mEmOry', max_size =1000000, time_col = 'time', interval = '1 hour');",
        g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t count = 10;
    ret = insertOrderData(stmt, g_tableName, count);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)sprintf(sqlCmd, "select iD, time, ip, name, description, id1, time1, message  from %s"
        " order by id", g_tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int64_t ids[count] = {0};
    int64_t checkTimes[count] = {0};
    int64_t ids2[count] = {0};
    int64_t checkTimes2[count] = {0};
    for (int i = 0; i < count; i++) {
        ids[i] = i;
        checkTimes[i] = 10000 + i;
        ids2[i] = i;
        checkTimes2[i] = 10000 + i;
    }
    
    QueryData data1 = {.ids = ids, .checkTimes = checkTimes, .names = g_name, .ips = g_ip, .blobs = g_message, 
        .texts = g_desText, .ids2 = ids2, .times2 = checkTimes2, .coefficients = 1, .dataCount = count};
    checkQueryDataBlob(stmt, data1);

    // 注入数据后增加重启操作，查看重启后是否存在该表以及是否存在数据
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 等待几秒，可能存在的建表时间
    sleep(3);
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 0);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006.按语法创建内存表并注入乱序数据
// 预期：查询成功
TEST_F(TsdbSupportMemoryTable, Timing_064_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "create table %s(id integer, time integer,  name char(64), ip inet, message blob(160),  "
        " description text, id1 integer, time1 integer, INDEX idx1(time1))"
        " with (enGine = 'mEmOry', max_size =10000, time_col = 'time', interval = '1 hour');",
        g_tableName); 
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t count = 1000;
    ret = insertOutOfOrderData(stmt, g_tableName, count);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)sprintf(sqlCmd, "select iD, time, ip, name, description, id1, time1, message from %s"
        " order by id", g_tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, 1000);

    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts recover");
    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 预留可能的建表时间
    sleep(5);
    // 创建同名表
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(sqlCmd, "create table %s(id integer, time integer, name char(64), ip inet,"
        " description text, message blob(160),  id1 integer, time1 integer, " 
        "INDEX idx1(time1))with (enGine = 'mEmOry', max_size =10000, time_col = 'time', interval = '1 hour');",
        g_tableName); 
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DUPLICATE_TABLE, ret);
    ret = returnDataCount(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DUPLICATE_TABLE, GMERR_CONNECTION_RESET_BY_PEER);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007.max_size设置为10000，分批注入10001条数据
// 预期：第三次注入报错，三次查询数据为9999,10000,10000
TEST_F(TsdbSupportMemoryTable, Timing_064_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char fdCmd[256] = {0};
    (void)sprintf(fdCmd, "ls -l /proc/`pidof gmserver_ts`/fd |wc -l");
    int32_t fdBefore = 0;
    int32_t fdAfter = 0;
    fdBefore = GetViewFieldResultValue(fdCmd);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "create table %s(id integer, time integer, name char(64), ip inet,"
        " description text, message blob(160),  id1 integer, time1 integer, " 
        "INDEX idx1(time1))with (enGine = 'mEmOry', max_size =10000, time_col = 'time', interval = '1 hour');",
        g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore, fdAfter);
    int64_t count = 9999;
    ret = insertOrderData(stmt, g_tableName, count);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore, fdAfter);
    ret = returnDataCount(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(ret, 9999);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore, fdAfter);
    ret = insertOrderData(stmt, g_tableName, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore, fdAfter);
    ret = returnDataCount(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(ret, 10000);
    // 最新约束，本次注入成功，阻塞下次注入
    ret = insertOrderData(stmt, g_tableName, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 增加等待时间，避免机器性能不够导致清理未完成
    sleep(5);
    ret = returnDataCount(stmt, g_tableName);
    // max_size删除数据逻辑和disk_limit一致
    AW_MACRO_ASSERT_EQ_INT(ret, 9199);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore, fdAfter);
    ret = insertOrderData(stmt, g_tableName, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(stmt, g_tableName);
    // 此处不跟踪，由新特性用例跟踪
    AW_MACRO_ASSERT_EQ_INT(ret, 9200);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008.max_size设置为99999，一次性注入100000条数据
// 预期：查询成功
TEST_F(TsdbSupportMemoryTable, Timing_064_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "create table %s(id integer, time integer, name char(64), ip inet,"
        " description text, message blob(160),  id1 integer, time1 integer, " 
        "INDEX idx1(time1))with (enGine = 'mEmOry', max_size =19999, time_col = 'time', interval = '1 hour');",
        g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t count = 20000;
    ret = insertOrderData(stmt, g_tableName, count);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 增加等待时间，避免机器性能不够导致清理未完成
    sleep(5);
    ret = returnDataCount(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(ret, 19200);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

class TsdbSupportMemoryTableNULL : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        
    }
    static void TearDownTestCase()
    {
        
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void TsdbSupportMemoryTableNULL::SetUp()
{
// euler和仿真环境清共享内存，清理原有DB文件
#if defined RUN_INDEPENDENT
    system("sh $TEST_HOME/tools/stop.sh -f");
#endif
}

void TsdbSupportMemoryTableNULL::TearDown()
{
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
    GmcConnOptionsDestroy(connOptions);
    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    testEnvClean();
    RecoverTsCiCfg();
    TsDefulatDbFileClean();
}


// 009.max_size设置为10000，注入9999条数据后，此时异常重启
// 预期：查询成功
TEST_F(TsdbSupportMemoryTableNULL, Timing_064_009)
{
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("rm -rf ./gmdb");
    system("mkdir -p ./gmdb");
    system("cp -r test.gmsql ./gmdb");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts recover");
    system("sh $TEST_HOME/tools/start.sh -ts");
    fdServerBeforeOffset1 = GetViewFieldResultValue(g_fdClient);
    fdServerBeforeOffset2 = fdServerBeforeOffset1;
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char fdCmd[256] = {0};
    (void)sprintf(fdCmd, "ls -l /proc/`pidof gmserver_ts`/fd |wc -l");
    int32_t fdBefore = 0;
    int32_t fdAfter = 0;
    fdBefore = GetViewFieldResultValue(fdCmd);
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 1, fdAfter);
    AW_CHECK_LOG_BEGIN();
    ret =
        TestGetResultCommand("cat ../common/logicTablePath.txt | tr -d '$\r'", NULL, g_cStoreDir, sizeof(g_cStoreDir));
    EXPECT_EQ(0, ret);
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "create table %s(id integer, time integer, name char(64), ip inet,"
        " description text, message blob(160),  id1 integer, time1 integer, " 
        "INDEX idx1(time1))with (enGine = 'mEmOry', max_size =10000, time_col = 'time', interval = '1 hour');",
        g_tableName); 
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t count = 9999;
    ret = insertOrderData(stmt, g_tableName, count);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 1, fdAfter);
    const char *cmd1 = "pwd";
    FILE *fptr = popen(cmd1, "r");
    if (fptr == NULL) {
        ASSERT_TRUE(false);
    }
    char tempFilePath[250] = {0};
    char g_dataFilePath[300] = {0};
    fgets(tempFilePath, sizeof(tempFilePath), fptr);
    fclose(fptr);
    tempFilePath[strlen(tempFilePath) - 1] = '\0'; //替换结尾换行符
    (void)sprintf(g_dataFilePath, "%s/gmdb", tempFilePath);
    char cmd[200] = {0};
    (void)sprintf(cmd,
        "sh $TEST_HOME/tools/modifyCfg.sh -ts \"schemaPath=/etc;/etc;%s;/etc;\"", 
        g_dataFilePath);
    
    GmcUnInit();
    system("pkill gmserver");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"schemaLoader=1\"");
    system(cmd);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(0, fdAfter);
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdServerBeforeOffset2 = GetViewFieldResultValue(g_fdClient);
    GmcInit();
    usleep(300 * 1000);
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 自动导表逻辑修改，不需要等到表导入完成才可以建连，所以需要等待几秒重新建表，避免偶现失败
    sleep(10);
    ret = returnDataCount(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 1 - fdServerBeforeOffset1, fdAfter - fdServerBeforeOffset2);
    // 再次注入数据后重启，此时不通过文件自动导数
    ret = insertOrderData(stmt, g_tableName, count);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(ret, 9999);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts recover");
    system("sh $TEST_HOME/tools/start.sh -ts");
    fdServerBeforeOffset2 = GetViewFieldResultValue(g_fdClient);
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 预留可能的建表时间
    sleep(5);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 1 - fdServerBeforeOffset1, fdAfter - fdServerBeforeOffset2);
    // 创建同名表
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(sqlCmd, "create table %s(id integer, time integer, name char(64), ip inet,"
        " description text, message blob(160),  id1 integer, time1 integer, " 
        "INDEX idx1(time1))with (enGine = 'mEmOry', max_size =10000, time_col = 'time', interval = '1 hour');",
        g_tableName); 
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DUPLICATE_TABLE, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 1 - fdServerBeforeOffset1, fdAfter - fdServerBeforeOffset2);
    ret = returnDataCount(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 1 - fdServerBeforeOffset1, fdAfter - fdServerBeforeOffset2);
    // schemaPath设置问题，其他目录没有权限,设置为用例目录后会导致超长建连失败
    AW_ADD_ERRNUM_WHITE_LIST(3, GMERR_FILE_OPERATE_FAILED, GMERR_DIRECTORY_OPERATE_FAILED, GMERR_INVALID_NAME);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_DUPLICATE_TABLE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010.max_size设置为10000，注入9999条数据后，此时重启，独立重启场景
// 预期：查询成功
TEST_F(TsdbSupportMemoryTableNULL, Timing_064_010)
{
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("rm -rf ./gmdb");
    system("mkdir -p ./gmdb");
    system("cp -r test.gmsql ./gmdb");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts recover");
    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
    ret =
        TestGetResultCommand("cat ../common/logicTablePath.txt | tr -d '$\r'", NULL, g_cStoreDir, sizeof(g_cStoreDir));
    EXPECT_EQ(0, ret);
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "create table %s(id integer, time integer, name char(64), ip inet,"
        " description text, message blob(160),  id1 integer, time1 integer, " 
        "INDEX idx1(time1))with (enGine = 'mEmOry', max_size =10000,  time_col = 'time', interval = '1 hour');",
        g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t count = 9999;
    ret = insertOrderData(stmt, g_tableName, count);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *cmd1 = "pwd";
    FILE *fptr = popen(cmd1, "r");
    if (fptr == NULL) {
        ASSERT_TRUE(false);
    }
    char tempFilePath[250] = {0};
    char g_dataFilePath[300] = {0};
    fgets(tempFilePath, sizeof(tempFilePath), fptr);
    fclose(fptr);
    tempFilePath[strlen(tempFilePath) - 1] = '\0'; //替换结尾换行符
    (void)sprintf(g_dataFilePath, "%s/gmdb", tempFilePath);
    char cmd[200] = {0};
    system("rm -rf /root/ts/memorytable");
    system("mkdir -p /root/ts/memorytable");
    (void)sprintf(cmd,
        "sh $TEST_HOME/tools/modifyCfg.sh -ts"
        " \"schemaPath=/root/ts/memorytable;/root/ts/memorytable;%s;/root/ts/memorytable;\"",
        g_dataFilePath);
    // 重启db
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"schemaLoader=1\"");
    system(cmd);
    system("sh $TEST_HOME/tools/start.sh -ts");
    // 自动导表逻辑修改，不需要等到表导入完成才可以建连，所以需要等待几秒
    sleep(5);
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);

    // 再次注入数据后重启，此时不通过文件自动导数
    ret = insertOrderData(stmt, g_tableName, count);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(ret, 9999);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts recover");
    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 预留可能的建表时间
    sleep(5);
    // 创建同名表
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(sqlCmd, "create table %s(id integer, time integer, name char(64), ip inet,"
        " description text, message blob(160),  id1 integer, time1 integer, " 
        "INDEX idx1(time1))with (enGine = 'mEmOry', max_size =10000, time_col = 'time', interval = '1 hour');",
        g_tableName); 
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DUPLICATE_TABLE, ret);
    ret = returnDataCount(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // schemaPath设置问题，其他目录没有权限,设置为用例目录后会导致超长建连失败
    AW_ADD_ERRNUM_WHITE_LIST(3, GMERR_FILE_OPERATE_FAILED, GMERR_DIRECTORY_OPERATE_FAILED, GMERR_INVALID_NAME);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_DUPLICATE_TABLE);
    system("rm -rf /root/ts/memorytable");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011.建表注入数据后进行全表查询，其中select 语句包含a+b表达式
// 预期：查询成功
TEST_F(TsdbSupportMemoryTable, Timing_064_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "create table %s(id integer, time integer, name char(64), ip inet,"
        " description text, message blob(160),  id1 integer, time1 integer, " 
        "INDEX idx1(time1))with (enGine = 'mEmOry', max_size =1000000,  time_col = 'time', interval = '1 hour');",
        g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t count = 9999;
    ret = insertOrderData(stmt, g_tableName, count);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)sprintf(sqlCmd, "select iD, id + time, ip, name, description, id1, time1, message  from %s"
        " order by id", g_tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 构造数据
    int64_t ids[count] = {0};
    int64_t checkTimes[count] = {0};
    int64_t ids2[count] = {0};
    int64_t checkTimes2[count] = {0};
    for (int i = 0; i < count; i++) {
        ids[i] = i;
        checkTimes[i] = 10000 + i + i;
        ids2[i] = i;
        checkTimes2[i] = 10000 + i;
    }
    // 验证查询结果
    QueryData data1 = {.ids = ids, .checkTimes = checkTimes, .names = g_name, .ips = g_ip, .blobs = g_message, 
        .texts = g_desText, .ids2 = ids2, .times2 = checkTimes2, .coefficients = 1, .dataCount = count};
    checkQueryDataBlob(stmt, data1);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012.建表注入数据后进行全表查询并进行排序
// 预期：查询成功
TEST_F(TsdbSupportMemoryTable, Timing_064_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "create table %s(id integer, time integer, name char(64), ip inet,"
        " description text, message blob(160),  id1 integer, time1 integer, " 
        "INDEX idx1(time1))with (enGine = 'mEmOry', max_size =1000000, time_col = 'time', interval = '1 hour');",
        g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t count = 9999;
    ret = insertOrderData(stmt, g_tableName, count);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(ret, count);


    (void)sprintf(sqlCmd, "select iD, time, ip, name, description, id1, time1, message from %s"
        " order by time desc", g_tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 构造数据
    int64_t ids[count] = {0};
    int64_t checkTimes[count] = {0};
    int64_t ids2[count] = {0};
    int64_t checkTimes2[count] = {0};
    for (int i = 0; i < count; i++) {
        ids[i] = count - 1 - i;
        checkTimes[i] = 10000 + count - 1 - i;
        ids2[i] = count - 1 - i;
        checkTimes2[i] = 10000 + count - 1 - i;
    }
    // 验证查询结果
    QueryData data1 = {.ids = ids, .checkTimes = checkTimes, .names = g_name, .ips = g_ip, .blobs = g_message, 
        .texts = g_desText, .ids2 = ids2, .times2 = checkTimes2, .coefficients = 1, .dataCount = count};
    checkQueryDataBlob(stmt, data1);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013.建表注入数据后，查询语句中使用min,max,first,last,count聚合函数
// 预期：查询成功
TEST_F(TsdbSupportMemoryTable, Timing_064_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "create table %s(id integer, time integer, name char(64), ip inet,"
        " description text, message blob(160),  id1 integer, time1 integer, " 
        "INDEX idx1(time1))with (enGine = 'mEmOry', max_size =1000000, time_col = 'time', interval = '1 hour');",
        g_tableName); 
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t count = 9999;
    ret = insertOrderData(stmt, g_tableName, count);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(ret, count);


    (void)sprintf(sqlCmd, "select iD, max(time), first(ip), last(name), min(description), "
        "count(id1), min(time1) from %s group by id order by time ", g_tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 构造数据
    int64_t ids[count] = {0};
    int64_t checkTimes[count] = {0};
    int64_t ids2[count] = {0};
    int64_t checkTimes2[count] = {0};
    for (int i = 0; i < count; i++) {
        ids[i] = i;
        checkTimes[i] = 10000 + i;
        ids2[i] = 1;
        checkTimes2[i] = 10000 + i;
    }
    // 验证查询结果
    QueryData1 data1 = {.ids = ids, .checkTimes = checkTimes, .names = g_name, .ips = g_ip,
        .texts = g_desText, .ids2 = ids2, .times2 = checkTimes2, .coefficients = 1, .dataCount = count};
    checkQueryDataWithoutBlob(stmt, data1);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014.建表注入数据后，查询语句中使用length,sum,colset聚合函数
// 预期：查询成功
TEST_F(TsdbSupportMemoryTable, Timing_064_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "create table %s(id integer, time integer, name char(64), ip inet,"
        " description text, message blob(160),  id1 integer, time1 integer, " 
        "INDEX idx1(time1))with (enGine = 'mEmOry', max_size =1000000, time_col = 'time', interval = '1 hour');",
        g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t count = 9999;
    ret = insertOrderData(stmt, g_tableName, count);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(ret, count);


    (void)sprintf(sqlCmd, "select iD, sum(time), first(ip), last(name), min(description), "
        "count(id1), length(time1), colset(id) from %s group by id order by time ", g_tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 构造数据
    int64_t ids[count] = {0};
    int64_t checkTimes[count] = {0};
    int64_t ids2[count] = {0};
    int64_t checkTimes2[count] = {0};
    for (int i = 0; i < count; i++) {
        ids[i] = i;
        checkTimes[i] = 10000 + i;
        ids2[i] = 1;
        checkTimes2[i] = 5;
    }
    // 验证查询结果
    QueryData1 data1 = {.ids = ids, .checkTimes = checkTimes, .names = g_name, .ips = g_ip,  
        .texts = g_desText, .ids2 = ids2, .times2 = checkTimes2, .coefficients = 1, .dataCount = count};
    checkQueryDataWithoutBlob(stmt, data1);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015.建表注入数据后，查询语句中组合使用groyp by 和order by
// 预期：查询成功
TEST_F(TsdbSupportMemoryTable, Timing_064_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char fdCmd[256] = {0};
    (void)sprintf(fdCmd, "ls -l /proc/`pidof gmserver_ts`/fd |wc -l");
    int32_t fdBefore = 0;
    int32_t fdAfter = 0;
    fdBefore = GetViewFieldResultValue(fdCmd);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "create table %s(id integer, time integer, name char(64), ip inet,"
        " description text, message blob(160),  id1 integer, time1 integer, " 
        "INDEX idx1(time1))with (enGine = 'mEmOry', max_size =1000000, time_col = 'time', interval = '1 hour');",
        g_tableName); 
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore, fdAfter);
    constexpr int64_t count = 9999;
    ret = insertOrderData(stmt, g_tableName, count);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(ret, count);


    (void)sprintf(sqlCmd, "select iD, max(time), first(ip), last(name), min(description), "
        "count(id1), min(time1) from %s group by id order by time ", g_tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore, fdAfter);
    // 构造数据
    int64_t ids[count] = {0};
    int64_t checkTimes[count] = {0};
    int64_t ids2[count] = {0};
    int64_t checkTimes2[count] = {0};
    for (int i = 0; i < count; i++) {
        ids[i] = i;
        checkTimes[i] = 10000 + i;
        ids2[i] = 1;
        checkTimes2[i] = 10000 + i;
    }
    // 验证查询结果
    QueryData1 data1 = {.ids = ids, .checkTimes = checkTimes, .names = g_name, .ips = g_ip,
        .texts = g_desText, .ids2 = ids2, .times2 = checkTimes2, .coefficients = 1, .dataCount = count};
    checkQueryDataWithoutBlob(stmt, data1);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore, fdAfter);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore, fdAfter);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016.copy to中查询语句为全表查询
// 预期：查询成功
TEST_F(TsdbSupportMemoryTable, Timing_064_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf ./data.csv");
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "create table %s(id integer, time integer, name char(64), ip inet,"
        " description text, message blob(160),  id1 integer, time1 integer, " 
        "INDEX idx1(time1))with (enGine = 'mEmOry', max_size =1000000, time_col = 'time', interval = '1 hour');",
        g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10;
    ret = insertOrderData(stmt, g_tableName, count);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(ret, count);

    (void)sprintf(sqlCmd, 
        "COPY (select iD, time, name, ip, description, message  from %s ) TO"
        "'%s/test/sdv/testcases/25_Timing/064_tsdb_support_memory_table/data.csv';", g_tableName, dir);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *expectContent =
R"(0,10000,david,10101040,test data of the text type:0,0071 3520
1,10001,nut,10101040,test data of the text type:1,0010 0000
2,10002,bob,98765432,test data of the text type:2,
3,10003,olivia,78000000,test data of the text type:3,3102 0021
4,10004,tim,ffaa0021,test data of the text type:4,0000 0000
5,10005,lucy,11111111,test data of the text type:5,9021 6537
6,10006,apple,11111111,test data of the text type:6,0071 3521
7,10007,bob,33333333,test data of the text type:7,0010 0001
8,10008,bob,ff000077,test data of the text type:8,
9,10009,omgd,f0d0c0a0,test data of the text type:9,3102 0022
)";
    // 检查csv文件是否生成
    AW_MACRO_ASSERT_EQ_INT(true, isDataCsvExist());
    char actualContent[4028] = {0};
    (void)GetCsvFileContent(actualContent);
    // 检查获取到内容是否正确
    AW_MACRO_ASSERT_EQ_STR(expectContent, actualContent);

    
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 017.copy to中查询语句中组合使用聚合函数和group by以及order by
// 预期：查询成功
TEST_F(TsdbSupportMemoryTable, Timing_064_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char fdCmd[256] = {0};
    (void)sprintf(fdCmd, "ls -l /proc/`pidof gmserver_ts`/fd |wc -l");
    int32_t fdBefore = 0;
    int32_t fdAfter = 0;
    fdBefore = GetViewFieldResultValue(fdCmd);
    system("rm -rf ./data.csv");
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "create table %s(id integer, time integer, name char(64), ip inet,"
        " description text, message blob(160),  id1 integer, time1 integer, " 
        "INDEX idx1(time1))with (enGine = 'mEmOry', max_size =1000000, time_col = 'time', interval = '1 hour');",
        g_tableName); 
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore, fdAfter);
    int64_t count = 10;
    ret = insertOrderData(stmt, g_tableName, count);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(ret, count);

    sprintf(sqlCmd, 
        "COPY (select iD, time, name, ip, description from %s group by id order by id) TO"
        "'%s/test/sdv/testcases/25_Timing/064_tsdb_support_memory_table/data.csv';", g_tableName, dir);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore, fdAfter);
    const char *expectContent =
R"(0,10000,david,10101040,test data of the text type:0
1,10001,nut,10101040,test data of the text type:1
2,10002,bob,98765432,test data of the text type:2
3,10003,olivia,78000000,test data of the text type:3
4,10004,tim,ffaa0021,test data of the text type:4
5,10005,lucy,11111111,test data of the text type:5
6,10006,apple,11111111,test data of the text type:6
7,10007,bob,33333333,test data of the text type:7
8,10008,bob,ff000077,test data of the text type:8
9,10009,omgd,f0d0c0a0,test data of the text type:9
)";
    // 检查csv文件是否生成
    AW_MACRO_ASSERT_EQ_INT(true, isDataCsvExist());
    char actualContent[4028] = {0};
    (void)GetCsvFileContent(actualContent);
    // 检查获取到内容是否正确
    AW_MACRO_ASSERT_EQ_STR(expectContent, actualContent);

    
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore, fdAfter);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018.insert into 目标表为逻辑表，源表为内存表
// 预期：查询成功
TEST_F(TsdbSupportMemoryTable, Timing_064_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char fdCmd[256] = {0};
    (void)sprintf(fdCmd, "ls -l /proc/`pidof gmserver_ts`/fd |wc -l");
    int32_t fdBefore = 0;
    int32_t fdAfter = 0;
    fdBefore = GetViewFieldResultValue(fdCmd);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "create table %s(id integer, time integer, name char(64), ip inet,"
        " description text, message blob(160),  id1 integer, time1 integer, " 
        "INDEX idx1(time1))with (enGine = 'mEmOry', max_size =1000000, time_col = 'time', interval = '1 hour');",
        g_tableName); 
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore, fdAfter);
    (void)sprintf(sqlCmd, "create table %s(id integer, time integer, name char(64), ip inet, message blob(160),"
        " description text, id1 integer, time1 integer)with (time_col = 'time', interval = '1 hour', "
        " compression = 'fast(rapidlz)');",
        g_tableName2);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t count = 1000;
    ret = insertOrderData(stmt, g_tableName, count);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(ret, count);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 1, fdAfter);
    // 执行insert into语句
    (void)sprintf(sqlCmd,
        "insert into %s (id, time, name, ip, description, message) "
        "select id, time, name, ip, description, message from %s"
        " ;",
        g_tableName2, g_tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 1, fdAfter);
    // 构造数据
    int64_t ids[count] = {0};
    int64_t checkTimes[count] = {0};
    int64_t ids2[count] = {0};
    int64_t checkTimes2[count] = {0};
    for (int i = 0; i < count; i++) {
        ids[i] = i;
        checkTimes[i] = 10000 + i;
        ids2[i] = 0;
        checkTimes2[i] = 0;
    }
    (void)sprintf(sqlCmd, "select iD, time, ip, name, description, id1, time1, message from %s"
        " order by time asc", g_tableName2);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 验证查询结果
    QueryData data1 = {.ids = ids, .checkTimes = checkTimes, .names = g_name, .ips = g_ip, .blobs = g_message, 
        .texts = g_desText, .ids2 = ids2, .times2 = checkTimes2, .coefficients = 1, .dataCount = count};
    checkQueryDataBlob(stmt, data1);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 1, fdAfter);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 1, fdAfter);
    ret = DropTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore, fdAfter);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 019.全表查询时，对内存表使用参数化查询
// 预期：查询成功
TEST_F(TsdbSupportMemoryTable, Timing_064_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "create table %s(id integer, time integer, name char(64), ip inet,"
        " description text, message blob(160),  id1 integer, time1 integer, " 
        "INDEX idx1(time1))with (enGine = 'mEmOry', max_size =1000000, time_col = 'time', interval = '1 hour');",
        g_tableName); 
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t count = 9999;
    ret = insertOrderData(stmt, g_tableName, count);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(ret, count);


    (void)sprintf(sqlCmd, "select iD, time, ip, name, description, id1, time1, message from %s"
        " where time > ? and id != ? order by time desc", g_tableName);
    ret = GmcPrepareSql(stmt, sqlCmd, strlen(sqlCmd));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t idPara1 = 10000;
    int64_t idPara2 = 9998;
    ret = GmcBindPara(stmt, 1, GMC_DATATYPE_INT64, &idPara1, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindPara(stmt, 2, GMC_DATATYPE_INT64, &idPara2, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 构造数据
    int64_t ids[count] = {0};
    int64_t checkTimes[count] = {0};
    int64_t ids2[count] = {0};
    int64_t checkTimes2[count] = {0};
    for (int i = 0; i < count -2; i++) {
        ids[i] = count - 2 - i;
        checkTimes[i] = 10000 + count - 2 - i;
        ids2[i] = count - 2 - i;
        checkTimes2[i] = 10000 + count - 2 - i;
    }
    // 验证查询结果
    QueryData data1 = {.ids = ids, .checkTimes = checkTimes, .names = g_name, .ips = g_ip, .blobs = g_message, 
        .texts = g_desText, .ids2 = ids2, .times2 = checkTimes2, .coefficients = 1, .dataCount = count - 2};
    checkQueryDataBlob(stmt, data1);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 020.分组查询时，对内存表使用参数化查询
// 预期：查询成功
TEST_F(TsdbSupportMemoryTable, Timing_064_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "create table %s(id integer, time integer, name char(64), ip inet,"
        " description text, message blob(160),  id1 integer, time1 integer, " 
        "INDEX idx1(name))with (enGine = 'mEmOry', max_size =1000000, time_col = 'time', interval = '1 hour');",
        g_tableName); 
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t count = 9999;
    ret = insertOrderData(stmt, g_tableName, count);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(ret, count);


    (void)sprintf(sqlCmd, "select iD, time, ip, name, description, id1, time1 from %s"
        " where time > ? and id != ? group by time order by time desc", g_tableName);
    ret = GmcPrepareSql(stmt, sqlCmd, strlen(sqlCmd));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t idPara1 = 10000;
    int64_t idPara2 = 9998;
    ret = GmcBindPara(stmt, 1, GMC_DATATYPE_INT64, &idPara1, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindPara(stmt, 2, GMC_DATATYPE_INT64, &idPara2, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 构造数据
    int64_t ids[count] = {0};
    int64_t checkTimes[count] = {0};
    int64_t ids2[count] = {0};
    int64_t checkTimes2[count] = {0};
    for (int i = 0; i < count -2; i++) {
        ids[i] = count - 2 - i;
        checkTimes[i] = 10000 + count - 2 - i;
        ids2[i] = count - 2 - i;
        checkTimes2[i] = 10000 + count - 2 - i;
    }
    // 验证查询结果
    QueryData1 data1 = {.ids = ids, .checkTimes = checkTimes, .names = g_name, .ips = g_ip,
        .texts = g_desText, .ids2 = ids2, .times2 = checkTimes2, .coefficients = 1, .dataCount = count - 2};
    checkQueryDataWithoutBlob(stmt, data1);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 021、gmsysview查询内存表
// 预期：查询成功
TEST_F(TsdbSupportMemoryTable, Timing_064_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "create table %s(id integer, time integer, name char(64), ip inet,"
        " description text, message blob(160),  id1 integer, time1 integer, " 
        "INDEX idx1(time1))with (enGine = 'mEmOry', max_size =1000000, time_col = 'time', interval = '1 hour');",
        g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t count = 200;
    ret = insertOrderData(stmt, g_tableName, count);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(ret, count);
    // 减小查询数量，避免blob字段过长导致resStr字段过长，DTS2504020028913继续跟踪最终结论
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from testdb0 where id < 50\" -s %s", g_connServerTsdb);
    
    char resStr[30000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "index = 49") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "index = 50") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    pclose(pResultStr);    
    
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 022、insert into 和查询内存表并发
// 预期：查询成功
TEST_F(TsdbSupportMemoryTable, Timing_064_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "create table %s(id integer, time integer, name char(64), ip inet,"
        " description text, message blob(160),  id1 integer, time1 integer, " 
        "INDEX idx1(time1))with (enGine = 'mEmOry', max_size =1000000, time_col = 'time', interval = '1 hour');",
        g_tableName); 
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)sprintf(sqlCmd, "create table %s(id integer, time integer, name char(64), ip inet, message blob(160),"
        " description text, id1 integer, time1 integer)with (time_col = 'time', interval = '1 hour', "
        " compression = 'fast(rapidlz)');",
        g_tableName2);
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t count = 1000000;
    ret = insertOrderData(stmt, g_tableName, count);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(ret, count);

    int64_t threadCount = 2;
    pthread_t tid[threadCount];
    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt2 = NULL;
    ret = TestTsGmcConnect(&conn2, &stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    QueryDataType queryDataType_1 =
        {stmt2, g_tableName, g_tableName2};
    QueryDataType queryDataType_2 =
    {stmt, g_tableName2, g_tableName};
    pthread_create(&tid[1], NULL, NormalQueryData, &queryDataType_1);
    pthread_create(&tid[0], NULL, NormalInsertInto, &queryDataType_2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 并发copy to数据
void *NormalCopyTo(void *arg)
{
    QueryDataType queryDataType = *(QueryDataType *)arg;
    GmcStmtT *stmttemp = queryDataType.stmt;
    system("rm -rf ./data.csv");
    char sqlCmd[400] = {0};
    (void)sprintf(sqlCmd, 
        "COPY (select iD, time from %s ) TO"
        "'%s/test/sdv/testcases/25_Timing/064_tsdb_support_memory_table/data.csv';", queryDataType.tableName1, dir);
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    EXPECT_EQ(GMERR_OK, ret);
    // 检查csv文件是否生成
    EXPECT_EQ(true, isDataCsvExist());
    return nullptr;
}

// 023、copy to 和查询内存表并发
// 预期：查询成功
TEST_F(TsdbSupportMemoryTable, Timing_064_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "create table %s(id integer, time integer, name char(64), ip inet,"
        " description text, message blob(160),  id1 integer, time1 integer, " 
        "INDEX idx1(time1))with (enGine = 'mEmOry', max_size =1000000, time_col = 'time', interval = '1 hour');",
        g_tableName); 
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)sprintf(sqlCmd, "create table %s(id integer, time integer, name char(64), ip inet, message blob(160),"
        " description text, id1 integer, time1 integer)with (time_col = 'time', interval = '1 hour', "
        " compression = 'fast(rapidlz)');",
        g_tableName2);
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t count = 1000000;
    ret = insertOrderData(stmt, g_tableName, count);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(ret, count);

    int64_t threadCount = 2;
    pthread_t tid[threadCount];
    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt2 = NULL;
    ret = TestTsGmcConnect(&conn2, &stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    QueryDataType queryDataType_1 =
        {stmt2, g_tableName, g_tableName2};
    QueryDataType queryDataType_2 =
    {stmt, g_tableName, g_tableName2};
    pthread_create(&tid[1], NULL, NormalQueryData, &queryDataType_1);
    pthread_create(&tid[0], NULL, NormalCopyTo, &queryDataType_2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 024、注入数据和查询内存表并发
// 预期：查询成功
TEST_F(TsdbSupportMemoryTable, Timing_064_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "create table %s(id integer, time integer, name char(64), ip inet,"
        " description text, message blob(160),  id1 integer, time1 integer, " 
        "INDEX idx1(time1))with (enGine = 'mEmOry', max_size =1100000000, time_col = 'time', interval = '1 hour');",
        g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)sprintf(sqlCmd, "create table %s(id integer, time integer, name char(64), ip inet, message blob(160),"
        " description text, id1 integer, time1 integer)with (time_col = 'time', interval = '1 hour', "
        " compression = 'fast(rapidlz)');",
        g_tableName2);
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t count = 1000000;
    ret = insertOrderData(stmt, g_tableName, count);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(ret, count);

    int64_t threadCount = 2;
    pthread_t tid[threadCount];
    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt2 = NULL;
    ret = TestTsGmcConnect(&conn2, &stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ConstructDataType constructDataType_1 = 
        {stmt, g_tableName, 10000, 10};
    QueryDataType queryDataType_2 =
    {stmt2, g_tableName, g_tableName2};
    pthread_create(&tid[1], NULL, InsertDataToTable, &constructDataType_1);
    pthread_create(&tid[0], NULL, NormalQueryData, &queryDataType_2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 025、并发查询不同数据
// 预期：查询成功
TEST_F(TsdbSupportMemoryTable, Timing_064_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "create table %s(id integer, time integer, name char(64), ip inet,"
        " description text, message blob(160),  id1 integer, time1 integer, " 
        "INDEX idx1(time1))with (enGine = 'mEmOry', max_size =1000000, time_col = 'time', interval = '1 hour');",
        g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int64_t count = 1000000;
    ret = insertOrderData(stmt, g_tableName, count);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(ret, count);

    int64_t threadCount = 2;
    pthread_t tid[threadCount];
    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt2 = NULL;
    ret = TestTsGmcConnect(&conn2, &stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd1[512] = {0};
    (void)sprintf(sqlCmd1, "select iD, time, ip, name, description, id1, time1 from %s"
        " where time > 20000 and id < 20000 group by time order by time desc", g_tableName);
    char sqlCmd2[512] = {0};
    (void)sprintf(sqlCmd2, "select iD, time, ip, name, description, id1, time1 from %s"
        " where time < 28000  group by time order by time asc", g_tableName);

    QueryDataTypeDif queryDataType_1 = 
        {stmt, g_tableName, sqlCmd1};
    QueryDataTypeDif queryDataType_2 =
    {stmt2, g_tableName, sqlCmd2};
    pthread_create(&tid[1], NULL, NormalQueryDataDif, &queryDataType_1);
    pthread_create(&tid[0], NULL, NormalQueryDataDif, &queryDataType_2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 026、并发查询相同的数据
// 预期：查询成功
TEST_F(TsdbSupportMemoryTable, Timing_064_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "create table %s(id integer, time integer, name char(64), ip inet,"
        " description text, message blob(160),  id1 integer, time1 integer, " 
        "INDEX idx1(time1))with (enGine = 'mEmOry', max_size =1000000, time_col = 'time', interval = '1 hour');",
        g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int64_t count = 1000000;
    ret = insertOrderData(stmt, g_tableName, count);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(ret, count);

    int64_t threadCount = 2;
    pthread_t tid[threadCount];
    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt2 = NULL;
    ret = TestTsGmcConnect(&conn2, &stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    QueryDataType queryDataType_1 =
    {stmt, g_tableName, g_tableName2};
    QueryDataType queryDataType_2 =
    {stmt2, g_tableName, g_tableName2};
    pthread_create(&tid[1], NULL, NormalQueryData, &queryDataType_1);
    pthread_create(&tid[0], NULL, NormalQueryData, &queryDataType_2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 027、并发查询含索引列和不含索引列
// 预期：查询成功
TEST_F(TsdbSupportMemoryTable, Timing_064_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "create table %s(id integer, time integer, name char(64), ip inet,"
        " description text, message blob(160),  id1 integer, time1 integer, " 
        "INDEX idx1(time1))with (enGine = 'mEmOry', max_size =1000000, time_col = 'time', interval = '1 hour');",
        g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int64_t count = 1000000;
    ret = insertOrderData(stmt, g_tableName, count);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(ret, count);

    int64_t threadCount = 2;
    pthread_t tid[threadCount];
    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt2 = NULL;
    ret = TestTsGmcConnect(&conn2, &stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd1[512] = {0};
    (void)sprintf(sqlCmd1, "select iD from %s" // , ip, name, description, id1, time1, message
        "  group by id order by id desc", g_tableName);
    char sqlCmd2[512] = {0};
    (void)sprintf(sqlCmd2, "select iD, time from %s" // , ip, name, description, id1, time1, message
        " where time < 28000  group by time order by time asc", g_tableName);

    QueryDataTypeDif queryDataType_1 = 
        {stmt, g_tableName, sqlCmd1};
    QueryDataTypeDif queryDataType_2 =
    {stmt2, g_tableName, sqlCmd2};
    pthread_create(&tid[1], NULL, NormalQueryDataDif, &queryDataType_1);
    pthread_create(&tid[0], NULL, NormalQueryDataDif, &queryDataType_2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 028、并发对索引列进行升序和降序查询
// 预期：查询成功
TEST_F(TsdbSupportMemoryTable, Timing_064_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "create table %s(id integer, time integer, name char(64), ip inet,"
        " description text, message blob(160),  id1 integer, time1 integer, " 
        "INDEX idx1(time1))with (enGine = 'mEmOry', max_size =1000000, time_col = 'time', interval = '1 hour');",
        g_tableName); 
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int64_t count = 1000000;
    ret = insertOrderData(stmt, g_tableName, count);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(ret, count);

    int64_t threadCount = 2;
    pthread_t tid[threadCount];
    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt2 = NULL;
    ret = TestTsGmcConnect(&conn2, &stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd1[512] = {0};
    (void)sprintf(sqlCmd1, "select iD, time from %s" // , ip, name, description, id1, time1, message
        "  group by id order by time desc", g_tableName);
    char sqlCmd2[512] = {0};
    (void)sprintf(sqlCmd2, "select iD, time from %s" // , ip, name, description, id1, time1, message
        " where time < 28000  group by time order by time desc", g_tableName);

    QueryDataTypeDif queryDataType_1 = 
        {stmt, g_tableName, sqlCmd1};
    QueryDataTypeDif queryDataType_2 =
    {stmt2, g_tableName, sqlCmd2};
    pthread_create(&tid[1], NULL, NormalQueryDataDif, &queryDataType_1);
    pthread_create(&tid[0], NULL, NormalQueryDataDif, &queryDataType_2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 029、循环建表注入数据删表操作
// 预期：查询成功
TEST_F(TsdbSupportMemoryTable, Timing_064_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char fdCmd[256] = {0};
    (void)sprintf(fdCmd, "ls -l /proc/`pidof gmserver_ts`/fd |wc -l");
    int32_t fdBefore = 0;
    int32_t fdAfter = 0;
    fdBefore = GetViewFieldResultValue(fdCmd);
    char sqlCmd[512] = {0};
    int64_t count = 0;
    for (int i = 0; i < 20; i++) {
        (void)sprintf(sqlCmd, "create table %s(id integer, time integer, name char(64), ip inet,"
            " description text, message blob(160),  id1 integer, time1 integer, " 
            "INDEX idx1(time1))with (enGine = 'mEmOry', max_size =1000000, time_col = 'time', interval = '1 hour');",
            g_tableName);
        ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        fdAfter = GetViewFieldResultValue(fdCmd);
        // DTS2025053009960,redoFileSize调小，导致redo写满切换,且内存表不落盘不会回收redo文件
        EXPECT_LE(fdAfter - fdBefore , 1);
        count = 20000;
        ret = insertOrderData(stmt, g_tableName, count);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = returnDataCount(stmt, g_tableName);
        AW_MACRO_ASSERT_EQ_INT(ret, count);
        ret = DropTable(stmt, g_tableName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        fdAfter = GetViewFieldResultValue(fdCmd);
        EXPECT_LE(fdAfter - fdBefore , 1);
        (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$CATA_VERTEX_LABEL_INFO'"
            " WHERE VERTEX_LABEL_NAME = 'testdb0' \" -s %s", g_connServerTsdb);
        char resStr[30000] = {0};
        FILE *pResultStr = NULL;
        pResultStr = popen(sqlCmd, "r");
        fread(resStr, 1, sizeof(resStr), pResultStr);
        isTrue = strstr(resStr, "index = 0") == NULL;
        AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
        isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
        AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
        pclose(pResultStr);
        fdAfter = GetViewFieldResultValue(fdCmd);
        EXPECT_LE(fdAfter - fdBefore , 1);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 030、高基数语法下查询是否会走高基数
// 预期：不走高基数
TEST_F(TsdbSupportMemoryTable, Timing_064_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "create table %s(id integer, time integer, name char(64), ip inet,"
        " description text, message blob(160),  id1 integer, time1 integer, " 
        "INDEX idx1(time1))with (enGine = 'mEmOry', max_size =1000000, time_col = 'time', interval = '1 hour');",
        g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t count = 9999;
    ret = insertOrderData(stmt, g_tableName, count);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(ret, count);


    (void)sprintf(sqlCmd, "select iD, time, ip, name, description, id1, time1 from %s" // 
        " GROUP BY ID order by time desc", g_tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char const *viewName = "select iD, time from TESTDb0 group by id";
    (void)sprintf(sqlCmd, "gmsysview -explain \"%s\" -s %s", viewName, g_connServerTsdb);
    ret = executeCommand(sqlCmd, "HashAggregate");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 031、通过gmimport工具建内存表
// 预期：建表成功，查询成功，结果正确
TEST_F(TsdbSupportMemoryTable, Timing_064_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf /home/<USER>/");
    int ret = 0;
    char filePath[128] = {0};
    char fileName[128] = "test";
    char g_command[512] = {0};
    (void)sprintf(filePath, "%s.gmsql", fileName);
    (void)snprintf(g_command, 128, "gmimport -c sQl -f %s -s %s", filePath, g_connServerTsdb);
    ret = executeCommand(g_command, "gmimport create sql total success num: 1.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "select iD, time, ip, name, description, id1, time1 from %s"
        " GROUP BY ID order by time", g_tableName);
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t count = 9999;
    ret = insertOrderData(stmt, g_tableName, count);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(ret, count);
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 构造数据
    int64_t ids[count] = {0};
    int64_t checkTimes[count] = {0};
    int64_t ids2[count] = {0};
    int64_t checkTimes2[count] = {0};
    for (int i = 0; i < count; i++) {
        ids[i] = i;
        checkTimes[i] = 10000 + i;
        ids2[i] = i;
        checkTimes2[i] = 10000 + i;
    }
    // 验证查询结果
    QueryData1 data1 = {.ids = ids, .checkTimes = checkTimes, .names = g_name, .ips = g_ip,
        .texts = g_desText, .ids2 = ids2, .times2 = checkTimes2, .coefficients = 1, .dataCount = count};
    checkQueryDataWithoutBlob(stmt, data1);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

class TsdbSupportMemoryTable_test1 : public testing::Test {
protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}

public:
    virtual void SetUp();
    virtual void TearDown();
};

void TsdbSupportMemoryTable_test1::SetUp()
{
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建epoll监听线程
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TsdbSupportMemoryTable_test1::TearDown()
{
    int ret = 0;
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}


void *ThreadStart(void *args)
{
    int ret = 0;
    ret = system("./tsdb_support_memory_table --gtest_also_run_disabled_tests "
                 "--gtest_filter=TsdbSupportMemoryTable_test1.DISABLED_ProcessLongScan >Timing_064_032.txt");
    return nullptr;
}

// 客户端异常退出子进程
TEST_F(TsdbSupportMemoryTable_test1, DISABLED_ProcessLongScan)
{
    AW_FUN_Log(LOG_STEP, "DISABLED_ProcessLongScan start.");
    int ret = 0;
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    char sqlCmd[256] = {0};
    int64_t count = 1000000;
    ret = TestTsGmcConnect(&conn1, &stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "DISABLED_ProcessLongScan wait for be kill.");
    
    for (int i = 0; i < 20; i++) {
        (void)sprintf(sqlCmd, "create table %s(id integer, time integer, name char(64), ip inet,"
            " description text, message blob(160),  id1 integer, time1 integer, " 
            "INDEX idx1(time1))with (enGine = 'mEmOry', max_size =1000000, time_col = 'time', interval = '1 hour');",
            g_tableName); 
        ret = GmcExecDirect(stmt1, sqlCmd, strlen(sqlCmd));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = insertOrderData(stmt1, g_tableName, count);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        system("cat signal_true.txt >>a.log");
        sleep(2);
        (void)sprintf(sqlCmd, "select iD, time from %s"
            " GROUP BY ID order by time", g_tableName);
        ret = GmcExecDirect(stmt1, sqlCmd, strlen(sqlCmd));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = returnDataCount(stmt1, g_tableName);
        AW_MACRO_ASSERT_EQ_INT(ret, count);
        ret = DropTable(stmt1, g_tableName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        
        (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$CATA_VERTEX_LABEL_INFO'"
            " WHERE VERTEX_LABEL_NAME = 'testdb0' \" -s %s", g_connServerTsdb);
        char resStr[30000] = {0};
        FILE *pResultStr = NULL;
        pResultStr = popen(sqlCmd, "r");
        fread(resStr, 1, sizeof(resStr), pResultStr);
        isTrue = strstr(resStr, "index = 0") == NULL;
        AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
        isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
        AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
        pclose(pResultStr);
    }

    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "DISABLED_ProcessLongScan end.");
}

// 构造客户端异常退出
// 032、循环建表注入数据删表操作时，客户端异常退出
// 预期：删表前退出后再次查询仍可查询出数据
TEST_F(TsdbSupportMemoryTable, Timing_064_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf /home/<USER>/");
    system("rm -rf a.log");
    // 客户端异常退出，仿真环境有15004错误码
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_CONNECTION_RESET_BY_PEER);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_NO_DATA);
    (void)snprintf(g_errorCode03, MAX_CMD_SIZE, "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
#ifdef ENV_RTOSV2 
    AW_ADD_ERR_WHITE_LIST(3, g_errorCode01, g_errorCode02, g_errorCode03);
#else
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
#endif
    char sqlCmd[512] = {0};
    int64_t count = 0;

    for (int i = 0; i < MAX_LOOP_NUM; i++) {
        // 开启一个线程，拉起子进程场景，执行长查询场景
        int threadNum = 1;
        pthread_t thr_arr[threadNum];
        ret = pthread_create(&thr_arr[0], NULL, ThreadStart, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        AW_FUN_Log(LOG_STEP, "等待客户端启动");
        (void)snprintf(g_command, MAX_CMD_SIZE, "cat a.log");
        ret = executeCommand(g_command, "true");
        while (ret != GMERR_OK) {
            sleep(1);
            ret = executeCommand(g_command, "true");
        }
        int pid = 0;
#if defined(RUN_INDEPENDENT) || defined(ENV_RTOSV2)
        ret = TestGetResultCommand("ps -ef |grep gtest_also_run_disabled_tests "
                                   "|grep -v grep |grep -v sh |awk '{print $2}'",
            &pid);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        system("ps -ef |grep gtest_also_run_disabled_tests |grep -v grep");
        AW_FUN_Log(LOG_STEP, "pid is %d\n", pid);
#else
        ret = GtExecSystemCmd("ps |grep gtest_also* |grep -v grep |grep -v sh |awk '{print $1}'", &pid);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        system("ps |grep gtest_also* |grep -v grep");
#endif
        // kill客户端进程
        ret = kill(pid, SIGKILL);
    }
    (void)sprintf(sqlCmd, "select iD, time, ip, name, description, id1, time1 from %s" 
        " GROUP BY ID order by time", g_tableName);
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

}

// 033.设置schemaLoader=0，1，3，建表注入数据后重启
// 预期：查询成功
TEST_F(TsdbSupportMemoryTableNULL, Timing_064_033)
{
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("rm -rf ./gmdb");
    system("mkdir -p ./gmdb");
    system("cp -r test.gmsql ./gmdb");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts recover");
    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
    ret =
        TestGetResultCommand("cat ../common/logicTablePath.txt | tr -d '$\r'", NULL, g_cStoreDir, sizeof(g_cStoreDir));
    EXPECT_EQ(0, ret);
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "create table %s(id integer, time integer, name char(64), ip inet,"
        " description text, message blob(160),  id1 integer, time1 integer, " 
        "INDEX idx1(time1))with (enGine = 'mEmOry', max_size =1000000, time_col = 'time', interval = '1 hour');",
        g_tableName); 
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t count = 9999;
    ret = insertOrderData(stmt, g_tableName, count);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *cmd1 = "pwd";
    FILE *fptr = popen(cmd1, "r");
    if (fptr == NULL) {
        ASSERT_TRUE(false);
    }
    char tempFilePath[250] = {0};
    char g_dataFilePath[300] = {0};
    fgets(tempFilePath, sizeof(tempFilePath), fptr);
    fclose(fptr);
    tempFilePath[strlen(tempFilePath) - 1] = '\0'; //替换结尾换行符
    (void)sprintf(g_dataFilePath, "%s/gmdb", tempFilePath);
    char cmd[1024] = {0};
    system("rm -rf /root/ts/memorytable");
    system("mkdir -p /root/ts/memorytable");
    (void)sprintf(cmd,
        "sh $TEST_HOME/tools/modifyCfg.sh -ts"
        " \"schemaPath=/root/ts/memorytable;/root/ts/memorytable;%s;/root/ts/memorytable;\"",
        g_dataFilePath);
    // 重启db
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"schemaLoader=0\"");
    system(cmd);
    system("sh $TEST_HOME/tools/start.sh -ts");
    // 预留可能的建表时间
    sleep(5);
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);

    // 重启db
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"schemaLoader=2\"");
    system(cmd);
    system("sh $TEST_HOME/tools/start.sh -ts");
    // 预留可能的建表时间
    sleep(5);
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);

    // 重启db
    system("sh $TEST_HOME/tools/stop.sh -ts");
    // 时序场景下不支持，Unable to start schema loader in rsmem or ts mode
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"schemaLoader=3\"");
    system(cmd);
    system("sh $TEST_HOME/tools/start.sh -ts");
    // 预留可能的建表时间
    sleep(5);
    ret = TestTsGmcConnect(&conn, &stmt);
    // 原校验逻辑错误，应该建连成功
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(ret, 0);
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts recover");
    // schemaPath设置问题，其他目录没有权限,设置为用例目录后会导致超长建连失败
    AW_ADD_ERRNUM_WHITE_LIST(3, GMERR_CONNECTION_FAILURE, GMERR_UNDEFINED_TABLE, GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_FILE_OPERATE_FAILED);
    system("rm -rf /root/ts/memorytable");
    AW_FUN_Log(LOG_STEP, "test end.");
}
