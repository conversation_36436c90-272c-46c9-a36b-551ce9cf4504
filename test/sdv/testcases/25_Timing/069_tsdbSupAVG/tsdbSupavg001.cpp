/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2024. All rights reserved.
 Description  : GMDB 506.0.0 迭代一TSDB支持avg聚合函数
 Notes        :
 History      :
 Author       : luyang/l00618033
 Create       : [2025.02.08]
*****************************************************************************/
#include "gtest/gtest.h"
#include "t_rd_ts.h"
#include "tsdbSupavg.h"

class tsdbSupAvg_001_test : public testing::Test {
protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}

public:
    virtual void SetUp();
    virtual void TearDown();
};

void tsdbSupAvg_001_test::SetUp()
{
    InitTsCiCfg();
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建epoll监听线程
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
    ret = TestTsGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void tsdbSupAvg_001_test::TearDown()
{
    int ret = 0;
    // 断开连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    RecoverTsCiCfg();
    system("sh $TEST_HOME/tools/stop.sh -ts");
}

class tsdbSupAvg_001_test2 : public testing::Test {
protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}

public:
    virtual void SetUp();
    virtual void TearDown();
};

void tsdbSupAvg_001_test2::SetUp()
{
    RecoverTsCiCfg();
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableVectorizedPushDown=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建epoll监听线程
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
    ret = TestTsGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void tsdbSupAvg_001_test2::TearDown()
{
    int ret = 0;
    // 断开连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    RecoverTsCiCfg();
    system("sh $TEST_HOME/tools/stop.sh -ts");
}

/* ****************************************************************************
 Description  : 001.逻辑表，select语句中，使用avg函数，avg函数中仅含一个整数列，预期查询成功
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    constexpr int64_t count = 20;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    char ip[count][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc", "lucy",
        "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    char messagetemp[count][64] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537", "0071 3521",
        "0010 0001", "", "3102 0022", "0000 0001", "9021 6538", "0071 3522", "0010 0002", "0010 0000", "3102 0023",
        "0000 0002", "9021 6539", "0071 3523", "0010 0003"};
    char *message[count] = {0};
    char *ns[count] = {0};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704067200 + i;
        ns[i] = name[i];
        message[i] = messagetemp[i];
    }

    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0', disk_limit = '50 MB',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    C2Int8C4StrT obj1 = {.id = id, .time = time, .name = name, .ip = ip};
    for (int i = 0; i < count; i++) {
        obj1.ns[i] = ns[i];
        obj1.message[i] = message[i];
    }
    // 插入数据
    ret = writeRecordTs(g_conn, g_stmt, g_tableName, &obj1, count, C2Int8C4StrSet2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询gmsysview -sql "select time, avg(id) from tsdb1，预期查询ok
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select time, avg(id) from %s limit 100\" -s %s",
        g_tableName, g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "avg(id): 9.5", "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 002.逻辑表，select语句中，使用avg函数，avg函数中仅含一个char类型列，预期查询报错
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    // 查询gmsysview -sql "select time, avg(name) from tsdb1，预期查询ok
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select time, avg(name) from %s limit 100\" -s %s",
        g_tableName, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_FEATURE_NOT_SUPPORTED);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 003.逻辑表，select语句中，使用avg函数，avg函数中仅含一个inet类型列，预期查询报错
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    // 查询gmsysview -sql "select time, avg(ip) from tsdb1
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select time, avg(ip) from %s limit 100\" -s %s",
        g_tableName, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_FEATURE_NOT_SUPPORTED);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 004.逻辑表，select语句中，使用avg函数，avg函数中仅含一个blob类型列，预期查询报错
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    // 查询gmsysview -sql "select time, avg(message) from tsdb1
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select time, avg(message) from %s limit 100\" -s %s",
        g_tableName, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_FEATURE_NOT_SUPPORTED);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 005.逻辑表，select语句中，使用avg函数，avg函数中仅含一个text类型列，预期查询报错
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    // 查询gmsysview -sql "select time, avg(ns) from tsdb1
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select time, avg(ns) from %s limit 100\" -s %s",
        g_tableName, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_FEATURE_NOT_SUPPORTED);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 006.逻辑表，select语句中，使用avg函数，avg函数中仅含一个常量整型，预期查询成功
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    // 查询gmsysview -sql "select time, avg(1111) from tsdb1，预期查询ok
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select time, avg(1111) from %s limit 100\" -s %s",
        g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "time", "avg(1111): 1111");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 007.逻辑表，select语句中，使用avg函数，avg函数中仅含一个常量字符，预期查询报错
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_DATATYPE_MISMATCH);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    // 查询gmsysview -sql "select time, avg('aaaa') from tsdb1
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select time, avg('aaa') from %s limit 100\" -s %s",
        g_tableName, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_DATATYPE_MISMATCH);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 008.逻辑表，select语句中，使用avg函数，avg函数中含两个整型参数avg(id, time)，预期查询报错
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_SYNTAX_ERROR);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    // 查询gmsysview -sql "select time, avg(id, time) from tsdb1
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select time, avg(id, time) from %s limit 100\" -s %s",
        g_tableName, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_SYNTAX_ERROR);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 009.逻辑表，select语句中，使用avg函数，avg函数中含无参数avg()，预期查询报错
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_SYNTAX_ERROR);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    // 查询gmsysview -sql "select time, avg() from tsdb1
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select time, avg() from %s limit 100\" -s %s",
        g_tableName, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_SYNTAX_ERROR);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 010.逻辑表，select语句中，使用avg函数，avg函数中参数为*avg(*)，预期查询报错
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    // 查询gmsysview -sql "select time, avg(*) from tsdb1
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select time, avg(*) from %s limit 100\" -s %s",
        g_tableName, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_FEATURE_NOT_SUPPORTED);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 011.逻辑表，select语句中，使用avg函数，avg函数中为一个不存在的列，avg(xx)，预期查询报错
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_SEMANTIC_ERROR);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    // 查询gmsysview -sql "select time, avg(*) from tsdb1
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select time, avg(name1) from %s limit 100\" -s %s",
        g_tableName, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 012.创建表名为avg或者字段名为avg，表创建成功
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_SEMANTIC_ERROR);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table avg(avg integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');");

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, (char *)"avg", 1000);

    // 查询gmsysview -sql "select avg(time), avg(avg) from tsdb1
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select avg(time), avg(avg) from avg limit 100\" -s %s",
        g_connServerTsdb);
    ret = executeCommand(g_command, "avg(time):", "avg(avg): 499.5");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, (char *)"avg");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 013.逻辑表，select语句中，使用avg函数，avg函数大小写混合AvG(id)，预期查询成功
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    // 查询gmsysview -sql "select time, AvG(id) from tsdb1，预期查询ok
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select time, AvG(id) from %s limit 100\" -s %s",
        g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "time:", "avg(id): 499.5");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 014.逻辑表，select语句中，使用avgavg(id)，预期查询报错
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    // 查询gmsysview -sql "select time, avg(*) from tsdb1
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select time, avgavg(id) from %s limit 100\" -s %s",
        g_tableName, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_FEATURE_NOT_SUPPORTED);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 015.逻辑表，select语句中，使用avg函数，avg函数中参数为avg(id time)，预期查询报错
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_SYNTAX_ERROR);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    // 查询gmsysview -sql "select time, avg(id time) from tsdb1
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select time, avg(id time) from %s limit 100\" -s %s",
        g_tableName, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_SYNTAX_ERROR);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 016.逻辑表，select语句中，使用avg函数，avg函数中参数为avg(整型-整型)，预期查询报错
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_SEMANTIC_ERROR);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    // 查询gmsysview -sql "select time, avg(time -id) from tsdb1
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select time, avg(time - id) from %s limit 100\" -s %s",
        g_tableName, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 017.逻辑表，select语句中，使用avg函数，avg函数中参数为avg(整型*整型)，预期查询报错
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_SEMANTIC_ERROR);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    // 查询gmsysview -sql "select time, avg(time * id) from tsdb1
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select time, avg(time * id) from %s limit 100\" -s %s",
        g_tableName, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 018.逻辑表，select语句中，使用avg函数，avg函数中参数为avg(整型%整型)，预期查询报错
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_SEMANTIC_ERROR);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    // 查询gmsysview -sql "select time, avg(time % id) from tsdb1
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select time, avg(time %% id) from %s limit 100\" -s %s",
        g_tableName, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 019.逻辑表，select语句中，使用avg函数，avg函数中参数为avg(整型/整型)，预期查询报错
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_SEMANTIC_ERROR);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    // 查询gmsysview -sql "select time, avg(time / id) from tsdb1
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select time, avg(time / id) from %s limit 100\" -s %s",
        g_tableName, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 020.逻辑表，select语句中，使用avg函数，avg函数中参数为avg(整型|整型)，预期查询报错
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_SEMANTIC_ERROR);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    // 查询gmsysview -sql "select time, avg(time | id) from tsdb1
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select time, avg(time | id) from %s limit 100\" -s %s",
        g_tableName, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 021.逻辑表，select语句中，使用avg函数，avg函数中参数为avg(整型&整型)，预期查询报错
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_SEMANTIC_ERROR);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    // 查询gmsysview -sql "select time, avg(time & id) from tsdb1
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select time, avg(time & id) from %s limit 100\" -s %s",
        g_tableName, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 022.逻辑表，select语句中，使用avg函数，avg函数中参数为avg(!整型)，预期查询报错
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_SYNTAX_ERROR);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    // 查询gmsysview -sql "select time, avg(!id) from tsdb1
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select time, avg(!id) from %s limit 100\" -s %s",
        g_tableName, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_SYNTAX_ERROR);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 服务端日志不够具体，太抽象
/* ****************************************************************************
 Description  : 023.逻辑表，select语句中，使用avg函数，avg函数中参数为avg(整型+整型+整型)，预期查询报错
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    // 查询gmsysview -sql "select time, avg(time + id + 1) from tsdb1
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select time, avg(time + id + 1) from %s limit 100\" -s %s",
        g_tableName, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_FEATURE_NOT_SUPPORTED);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 024.逻辑表，select语句中，使用avg函数，avg函数中参数为avg(整型+整型)，预期查询成功
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    // 查询gmsysview -sql "select time, avg(id + id) from tsdb1
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select avg(id + id), 1, id, avg(id), sum(id), * from %s "
        "limit 100\" -s %s", g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "avg(id + id): 999", "avg(id): 499.5", "sum(id): 499500", "id: 0", "ip: 10101040");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 服务端日志不够清晰，可读性查，建议整改
/* ****************************************************************************
 Description  : 025.逻辑表，select语句中，使用avg函数，avg函数中参数为avg(整型+inet)，预期查询报错
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_SEMANTIC_ERROR);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    // 查询gmsysview -sql "select time, avg(id + ip) from tsdb1
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select time, avg(id + ip) from %s limit 100\" -s %s",
        g_tableName, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 026.逻辑表，select语句中，使用avg函数，avg函数中参数为avg(整型+char)，预期查询报错
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_SEMANTIC_ERROR);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    // 查询gmsysview -sql "select time, avg(id + name) from tsdb1
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select time, avg(id + name) from %s limit 100\" -s %s",
        g_tableName, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 027.逻辑表，select语句中，使用avg函数，avg函数中参数为avg(整型+text)，预期查询报错
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_SEMANTIC_ERROR);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    // 查询gmsysview -sql "select time, avg(id + ns) from tsdb1
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select time, avg(id + ns) from %s limit 100\" -s %s",
        g_tableName, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 028.逻辑表，select语句中，使用avg函数，avg函数中参数为avg(整型+blob)，预期查询报错
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_SEMANTIC_ERROR);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    // 查询gmsysview -sql "select time, avg(id + message) from tsdb1
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select time, avg(id + message) from %s limit 100\" -s %s",
        g_tableName, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 029.逻辑表，select语句中，使用avg函数，avg函数中参数为avg(整型+1)，预期查询成功
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    // 查询gmsysview -sql "select avg(id + 1) from tsdb1
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select avg(id + 1), 1, id, avg(id), sum(id), * from %s "
        "limit 100\" -s %s", g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "avg(id + 1): 500.5", "avg(id): 499.5", "sum(id): 499500", "id: 0", "ip: 10101040");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 030.逻辑表，select语句中，使用avg函数，avg函数中参数为avg(1+整型)，预期查询成功
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    // 查询gmsysview -sql "select avg(1 + id) from tsdb1
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select avg(1 + id), 1, id, avg(id), sum(id), * from %s "
        "limit 100\" -s %s", g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "avg(1 + id): 500.5", "avg(id): 499.5", "sum(id): 499500", "id: 0", "ip: 10101040");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 031.逻辑表，select语句中，使用avg函数，avg函数中参数为avg(整型-1)，预期查询报错
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_SEMANTIC_ERROR);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    // 查询gmsysview -sql "select time, avg(id - 1) from tsdb1
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select time, avg(id - 1) from %s limit 100\" -s %s",
        g_tableName, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 032.逻辑表，select语句中，使用avg函数，avg函数中参数为avg(整型****)，预期查询报错
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_SEMANTIC_ERROR);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    // 查询gmsysview -sql "select time, avg(id + 1.1) from tsdb1
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select time, avg(id + 1.1) from %s limit 100\" -s %s",
        g_tableName, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 033.逻辑表，select语句中，使用avg函数，avg函数中参数为avg(整型+'aa')，预期查询报错
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_SEMANTIC_ERROR);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    // 查询gmsysview -sql "select time, avg(id + 'aaa') from tsdb1
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select time, avg(id + 'aaa') from %s limit 100\" -s %s",
        g_tableName, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 034.逻辑表，select语句中，使用avg函数，avg函数中参数为avg(-11)，预期查询报错
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_SEMANTIC_ERROR);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    // 查询gmsysview -sql "select time, avg(-11) from tsdb1
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select time, avg(-11) from %s limit 100\" -s %s",
        g_tableName, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 035.逻辑表，select语句中，使用avg函数，avg函数中参数为avg(11.11)，预期查询报错
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_DATATYPE_MISMATCH);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0', disk_limit = '1 GB',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    // 查询gmsysview -sql "select time, avg(1.1) from tsdb1
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select time, avg(1.1) from %s limit 100\" -s %s",
        g_tableName, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_DATATYPE_MISMATCH);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 036.逻辑表，含所有字段类型，select语句中，使用select id,avg(id), avg(id+time) from tsdb1，预期查询成功
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    // 查询gmsysview -sql "select id,avg(id), avg(id+time) from tsdb1
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select id, avg(id), avg(id+time), sum(id), * from %s "
        "limit 100\" -s %s", g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "avg(id): 499.5", "avg(id+time)", "sum(id): 499500", "id: 0", "ip: 10101040");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 037.逻辑表，select语句中，使用avg函数，avg函数中参数为avg(1+3)，预期查询成功
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    // 查询gmsysview -sql "select id,avg(id), avg(1+3) from tsdb1
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select id, avg(id), avg(1+3), sum(id), * from %s "
        "limit 100\" -s %s", g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "avg(id): 499.5", "avg(1+3): 4", "sum(id): 499500", "id: 0", "ip: 10101040");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 038.逻辑表，含所有字段类型，select语句中，使用select id,avg(id), avg(id+time) from tsdb1 where id > 0，预期查询成功
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    // 查询select id,avg(id), avg(id+time) from tsdb1 where id > 499
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select id, avg(id), avg(id+time), sum(id), * from %s "
        "where id > 499 limit 100\" -s %s", g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "avg(id): 749.5", "avg(id+time):", "sum(id): 374750", "id: 500", "ip: 10101040");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 039.逻辑表，含所有字段类型，select语句中，使用select id,avg(id), avg(id+time) from tsdb1 where id > 0含sum、count、length函数，预期查询成功
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    // 查询select id,avg(id), avg(id+time) from tsdb1 where id > 499
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select id, avg(id), count(*), avg(id+time), length(id), "
        "sum(id), * from %s where id > 499 limit 100\" -s %s", g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "avg(id): 749.5", "count(*): 500", "sum(id): 374750", "id: 500", "ip: 10101040");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 040.逻辑表，含所有字段类型，select语句中，使用select id,avg(id), avg(id+time) from tsdb1 where id > 0
 含sum、count、length函数及order by id和group by，预期查询成功
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0, dataCnt = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select id, avg(id), count(id), avg(id+time), length(id), "
        "sum(id), ip from %s where id > 499 group by ip order by id asc, ip \" -s %s", g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "first(id): 500", "avg(id): 740.5", "count(id): 50", "sum(id): 37025",
        "ip: 10101040");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    system(g_command);
    // 查询接口查询
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select id, avg(id), count(id), avg(id+time), length(id), "
        "sum(id), ip from %s where id > 499 group by ip order by id asc, ip;", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCnt, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(17, dataCnt);

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 041.逻辑表，含所有字段类型，select语句中，where avg(id) > 0，预期查询失败
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select time, avg(id) from %s where avg(id) > 0 "
        "limit 100\" -s %s", g_tableName, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_FEATURE_NOT_SUPPORTED);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 042.逻辑表，含所有字段类型，select语句中，group by avg(id)，预期查询失败
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_SYNTAX_ERROR);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select time, avg(id) from %s where id > 0 "
        "group by avg(id) limit 100\" -s %s", g_tableName, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_SYNTAX_ERROR);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 服务端日志，不够具体
/* ****************************************************************************
 Description  : 043.逻辑表，含所有字段类型，select语句中，sum(avg(id))，预期查询失败
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_SEMANTIC_ERROR);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select time, avg(id), sum(avg(id)) from %s where id > 0 "
        "limit 100\" -s %s", g_tableName, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 服务端日志，不够具体
/* ****************************************************************************
 Description  : 044.逻辑表，含所有字段类型，select语句中，max(avg(id))，预期查询失败
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_SEMANTIC_ERROR);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select time, avg(id), max(avg(id)) from %s where id > 0 "
        "limit 100\" -s %s", g_tableName, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 045.逻辑表，含所有字段类型，select语句中，count(avg(id))，预期查询失败
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_SEMANTIC_ERROR);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select time, avg(id), count(avg(id)) from %s "
        "where id > 0 limit 100\" -s %s", g_tableName, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 046.逻辑表，含所有字段类型，select语句中，first(avg(id))，预期查询失败
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_SEMANTIC_ERROR);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select time, avg(id), first(avg(id)) from %s "
        "where id > 0 limit 100\" -s %s", g_tableName, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 047.逻辑表，含所有字段类型，select语句中，length(avg(id))，预期查询失败
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select time, avg(id), length(avg(id)) from %s "
        "where id > 0 limit 100\" -s %s", g_tableName, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_FEATURE_NOT_SUPPORTED);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 048.逻辑表，开启高基数配置项，deploy打开，含所有字段类型，select语句中，select id,
  avg(id) from tsdb1 where time> 0 group by ip，预期走高基数,数据符合预期 gmsysview explain走下推 无agg算子
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test2, Timing_069_001_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select id, avg(id), count(id), ip from %s "
        "where id > 499 group by ip order by id asc, ip \" -s %s", g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "first(id): 500", "avg(id): 740.5", "count(id): 50", "ip: 10101040");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 无hashaggregate算子
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -explain \"select id, avg(id), count(id), ip from %s "
        "where id > 499 group by ip order by id asc, ip \" -s %s", g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "HashAggregate");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    if (ret == GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 049.逻辑表，开启高基数配置项，deploy打开，含所有字段类型，select语句中，select id, 
 avg(2) from tsdb1 where time> 0 group by ip，预期走高基数,数据符合预期 gmsysview explain走下推 无agg算子
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test2, Timing_069_001_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select id, avg(2), count(id), ip from %s "
        "where id > 499 group by ip order by id asc, ip \" -s %s", g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "first(id): 500", "avg(2): 2", "count(id): 50", "ip: 10101040");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -explain \"select id, avg(2), count(id), ip from %s "
        "where id > 499 group by ip order by id asc, ip \" -s %s", g_tableName, g_connServerTsdb);
    // 无hashaggregate算子
    ret = executeCommand(g_command, "HashAggregate");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    if (ret == GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 050.逻辑表，开启高基数配置项，含所有字段类型，select语句中，select id, avg(id+time) from tsdb1 
 where time> 0 group by id，预期不走高基数,数据符合预期
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test2, Timing_069_001_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select id, avg(2), avg(id+time), count(id), ip from %s "
        "where id > 499 group by ip order by id asc, ip \" -s %s", g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "first(id): 500", "avg(2): 2", "avg(id+time):", "count(id): 50", "ip: 10101040");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -explain \"select id, avg(2), avg(id+time), count(id), ip "
        "from %s where id > 499 group by ip order by id asc, ip \" -s %s", g_tableName, g_connServerTsdb);
    // 有hashaggregate算子
    ret = executeCommand(g_command, "HashAggregate");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 051.内存表，含所有字段类型，select语句中，select id, avg(id) from tsdb1 where time> 0 group by id，预期数据符合预期
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0, dataCnt = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text, index idx(id))"
        " with (engine = 'memory', max_size = 9223372036854775807, time_col = 'time', interval = '1 hour');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select id, avg(id), count(id), length(id), "
        "sum(id), ip from %s where id > 499 group by ip order by id asc, ip \" -s %s", g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "first(id): 500", "avg(id): 740.5", "count(id): 50", "sum(id): 37025",
        "ip: 10101040");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    system(g_command);

    // 查询接口查询
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "select id, avg(id), count(id), length(id), "
        "sum(id), ip from %s where id > 499 group by ip order by id asc, ip;", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCnt, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(17, dataCnt);

    // 删除时序内存表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 052.内存表，含所有字段类型，select语句中，select id, avg(id+time) from tsdb1 where time> 0 group by id，预期数据符合预期
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text, index idx(id))"
        " with (engine = 'memory', max_size = 9223372036854775807, time_col = 'time', interval = '1 hour');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select id, avg(id), count(id), avg(id+time), length(id), "
        "sum(id), ip from %s where id > 499 group by ip order by id asc, ip \" -s %s", g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "first(id): 500", "avg(id): 740.5", "count(id): 50", "sum(id): 37025",
        "ip: 10101040");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序内存表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 053.逻辑表，含所有字段类型，插入大量数据（1000w）select语句中，
 select id, avg(id+time) from tsdb1 where time> 0 group by id，预期数据符合预期；1个分组
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0, cycleCnt = 100;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    for (int i = 0; i < cycleCnt; i++){
        InsertData3(g_stmt, g_tableName, 100000, i * 100000);
    }

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select id, avg(id), count(id), avg(id+time), length(id), "
        "sum(id), ip from %s where id > 5000000 group by ip order by id asc, ip limit 100\" -s %s", g_tableName,
        g_connServerTsdb);
    ret = executeCommand(g_command, "first(id): 5000001", "avg(id): 7499995.499991", "count(id): 499999",
        "sum(id): 3749990250000", "ip: 10101040");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
 
    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 054.内存表，含所有字段类型，插入大量数据（100w）select语句中，select id, avg(id+time) 
 from tsdb1 where time> 0 group by id，预期数据符合预期
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0, cycleCnt = 10;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text, index idx(id))"
        " with (engine = 'memory', max_size = 9223372036854775807, time_col = 'time', interval = '1 hour');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    for (int i = 0; i < cycleCnt; i++){
        InsertData3(g_stmt, g_tableName, 100000, i * 100000);
    }

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select id, avg(id), count(id), avg(id+time), length(id), "
        "sum(id), ip from %s where id > 500000 group by ip order by id asc, ip limit 100\" -s %s", g_tableName,
        g_connServerTsdb);
    ret = executeCommand(g_command, "first(id): 500001", "avg(id): 749995.499910", "count(id): 49999",
        "sum(id): 37499025000", "ip: 10101040");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
 
    // 删除时序内存表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 055.逻辑表，含所有字段类型，插入数据select语句中，select id, avg(id+time) from 
 tsdb1 where time> 0 group by id，独立重启后，再进行avg查询，预期数据符合预期
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select id, avg(id), count(id), ip from %s "
        "where id > 499 group by ip order by id asc, ip \" -s %s", g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "first(id): 500", "avg(id): 740.5", "count(id): 50", "ip: 10101040");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 独立重启
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    // 重新建连
    ret = TestTsGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select id, avg(id), count(id), ip from %s "
        "where id > 499 group by ip order by id asc, ip \" -s %s", g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "first(id): 500", "avg(id): 740.5", "count(id): 50", "ip: 10101040");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 056.逻辑表，开启高基数配置项，含所有字段类型，select语句中，select id, 
 avg(id+time) from tsdb1 where time> 0 group by id，预期不走高基数,通过insert into到另外一张表，预期执行失败
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test2, Timing_069_001_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATATYPE_MISMATCH);
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName2);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select id, avg(id+time), name, ip from %s "
        "where id > 499 group by ip order by id asc, ip \" -s %s", g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "first(id): 500", "avg(id+time):", "ip: 10101040");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // insert into
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "insert into %s select id, avg(id+time), name, ip from %s group by id "
        "limit 10000", g_tableName2, g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATATYPE_MISMATCH, ret);

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropCmTable(g_stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 逻辑表->内存表；服务端日志有歧义
/* ****************************************************************************
 Description  : 057.内存表，含所有字段类型，select语句中，select id, avg(id+time) from tsdb1 where time> 0 
 group by id，通过insert into到另外一张表逻辑表，预期执行失败
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATATYPE_MISMATCH);
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text, index idx(id))"
        " with (engine = 'memory', max_size = 9223372036854775807, time_col = 'time', interval = '1 hour');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName2);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);
    InsertData3(g_stmt, g_tableName2, 1000);

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select id, time, avg(id+time), name, ip from %s "
        "where id > 499 group by ip order by id asc, ip \" -s %s", g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "first(id): 500", "avg(id+time):", "ip: 10101040");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // insert into
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "insert into %s select id, avg(id +time), name, ip from %s group by id "
        "limit 10000", g_tableName2, g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATATYPE_MISMATCH, ret);

    // 逻辑表->内存表
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "insert into %s select id, time, name, ip from %s group by id "
        "limit 10000", g_tableName, g_tableName2);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropCmTable(g_stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 058.avg函数覆盖模糊查询like
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select id, avg(id), count(id), ip from %s "
        "where name like 'bo%%' and id > 499 group by ip order by id asc, ip \" -s %s", g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "first(id): 502", "avg(id): 742.000000", "count(id): 25", "ip: 98765432");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// DTS2025021013200
/* ****************************************************************************
 Description  : 059.系统视图使用avg函数测试
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_059)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select avg(INITIATE_DELAY), sum(INITIATE_dELAY), *,"
        "cpU_usAge from 'V\\$DB_SERVER' where 1 != 0 \" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "avg(initiate_delay):", "sum(initiate_delay):", "CPU_USAGE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}

// DTS2025021011031
/* ****************************************************************************
 Description  : 060.avg聚合函数叠加别名测试
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_060)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select id as ns1, avg(id) as n2, count(id), ip, 1 "
        "from %s where name like 'bo%%' and id > 499 group by ip order by id asc, ip \" -s %s",
        g_tableName, g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "first(id): 502", "avg(id): 742.000000", "count(id): 25", "ip: 98765432");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select id as ns1, avg(id) as n2, count(id) as n3, ip "
        "from %s where name like 'bo%%' and id > 499 group by ip order by id asc, ip \" -s %s",
        g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "first(id): 502", "avg(id): 742.000000", "count(id): 25", "ip: 98765432");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 联调问题
// DTS2025020817095
/* ****************************************************************************
 Description  : 061.对系统函数进行视图测试，使用where过滤，左边是常数
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_061)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);
    //
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select count(*), avg(DISK_LIMIT), "
        "sum(DISK_USAGE) from 'V\\$STORAGE_DISK_USAGE' where 1 != 1 \" -s %s", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "count(*): 0", "avg(disk_limit):");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select TABLE_NAME as ns1, avg(DISK_LIMIT), "
        "sum(DISK_USAGE), * from 'V\\$STORAGE_DISK_USAGE' where 1 == 1 or 1 != 1 or ROW_CNT = 10000  \" -s %s",
        g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "TABLE_NAME: tsdb1", "ROW_CNT: 1000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// DTS2025022224384
/* ****************************************************************************
 Description  : 061.对系统函数进行视图测试，select语句中含avg(1.1)与sum(1.1)
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_062)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATATYPE_MISMATCH);
    int ret = 0, tryCnt = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);
    //
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select avg(1.1), avg(DISK_LIMIT), "
        "sum(DISK_USAGE) from 'V\\$STORAGE_DISK_USAGE' where 1 != 1 \" -s %s", g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_DATATYPE_MISMATCH);
    system(g_command);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select TABLE_NAME, sum(1.1), * "
        "from 'V\\$STORAGE_DISK_USAGE' where 1 == 1 or 1 != 1 or ROW_CNT = 10000  \" -s %s",
        g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select avg(id), avg(1.1), sum(1.1), 1 "
        "from %s where name like 'bo%%' and id > 499 group by ip order by id asc, ip \" -s %s",
        g_tableName, g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 校验服务端错误日志
    (void)snprintf(g_command, MAX_CMD_SIZE, "grep -r \"Datatype mismatch.\" %s", SERVER_LOG_PATH);
    while (tryCnt < MAX_TRY_CNT) {
        ret = executeCommand(g_command, "ERROR", "Datatype mismatch. data type 12 unsupport in avg(1.1).");
        if (ret == GMERR_OK) {
            break;
        }
        sleep(1);
        tryCnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// DTS2025022411974
/* ****************************************************************************
 Description  : 063.对系统视图和逻辑表进行视图测试，select语句中过滤条件与浮点数比较，系统视图不报错，逻辑表报错
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_063)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select TABLE_NAME, sum(1), * "
        "from 'V\\$STORAGE_DISK_USAGE' where 1 == 1 or 1 != 1 or ROW_CNT > 1.1  \" -s %s",
        g_connServerTsdb);
    ret = executeCommand(g_command, "TABLE_NAME: tsdb1", "sum(1): 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select avg(id), avg(1), sum(1), 1 "
        "from %s where name like 'bo%%' or id > 1.1 group by ip order by id asc, ip \" -s %s",
        g_tableName, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_FEATURE_NOT_SUPPORTED);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// DTS2025030513906
/* ****************************************************************************
 Description  : 064.对系统视图和逻辑表进行视图测试，select语句中投影列含浮点数，预期查询成功
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_064)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATATYPE_MISMATCH);
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select TABLE_NAME, sum(1),*,1.11, 22222.1111, 1 "
        "from 'V\\$STORAGE_DISK_USAGE' where 1 == 1 or 1 != 1 or ROW_CNT > 1.1  \" -s %s",
        g_connServerTsdb);
    ret = executeCommand(g_command, "TABLE_NAME: tsdb1", "sum(1): 1", "1.11: 1.110000", "22222.1111: 22222.1111",
        "1: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select avg(id), avg(1), sum(1), 1, 1.11, 22222.1111, 1 "
        "from %s where name like 'bo%%' or id > 977 group by ip order by id asc, ip limit 5\" -s %s",
        g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "avg(id): 492", "avg(1): 1", "1: 1", "22222.1111: 22222.111100",
        "1: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// DTS2025022412606
/* ****************************************************************************
 Description  : 065.对系统视图和逻辑表进行视图测试，where子句是整型列与常量相加减过滤，预期报错
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_065)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SEMANTIC_ERROR);
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select TABLE_NAME, sum(1),*,1.11, 22222.1111, 1 "
        "from 'V\\$STORAGE_DISK_USAGE' where 1 == 1 or 1 != 1 or ROW_CNT + 1 > 0  \" -s %s",
        g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_SEMANTIC_ERROR);
    system(g_command);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select avg(id), avg(1), sum(1), 1, 1.11, 22222.1111, 1 "
        "from %s where name like 'bo%%' or id - 1> 977 group by ip order by id asc, ip limit 5\" -s %s",
        g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select avg(id), avg(1), sum(1), 1, 1.11, 22222.1111, 1 "
        "from %s where name like 'bo%%' or id + id + id > 977 group by ip order by id asc, ip limit 5\" -s %s",
        g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 066.对系统视图和逻辑表进行视图测试，投影列含整型列与常量相减，预期报错
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_066)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SEMANTIC_ERROR);
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select TABLE_NAME, sum(1),*,row_cnt - 1, 22222.1111, 1 "
        "from 'V\\$STORAGE_DISK_USAGE' where 1 == 1 or 1 != 1\" -s %s",
        g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_SEMANTIC_ERROR);
    system(g_command);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select avg(id), id - 1, sum(1), 1, 1.11, 22222.1111, 1 "
        "from %s where name like 'bo%%' or id + id> 977 group by ip order by id asc, ip limit 5\" -s %s",
        g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select avg(id), id - id, sum(1), 1, 1.11, 22222.1111, 1 "
        "from %s where name like 'bo%%' or id + id + id > 977 group by ip order by id asc, ip limit 5\" -s %s",
        g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// DTS2025031124172
/* ****************************************************************************
 Description  : 067.对系统视图和逻辑表进行视图测试，含-fmt hexxx,预期报错
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_067)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SYNTAX_ERROR);
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select TABLE_NAME, sum(1),*, 22222.1111, 1 "
        "from 'V\\$STORAGE_DISK_USAGE' where 1 == 1 or 1 != 1 -fmt hexxxxx\" -s %s",
        g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_SYNTAX_ERROR);
    system(g_command);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select avg(id), sum(1), 1, 1.11, 22222.1111, 1 "
        "from %s where name like 'bo%%' or id + id> 977 group by ip order by id asc, ip limit 5 -fmt hexxxx\" -s %s",
        g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 068.对系统视图和逻辑表进行视图测试，select语句中含order by id+time及limit，预期查询成功
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_068)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select TABLE_NAME, sum(1),*,1.11, 22222.1111, 1 "
        "from 'V\\$STORAGE_DISK_USAGE' where 1 == 1 or 1 != 1 or ROW_CNT > 1.1  order by ROW_CNT + 1 limit 5\" -s %s",
        g_connServerTsdb);
    ret = executeCommand(g_command, "TABLE_NAME: tsdb1", "sum(1): 1", "1.11: 1.110000", "22222.1111: 22222.1111",
        "1: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select avg(id), avg(1), sum(1), 1, 1.11, 22222.1111, 1 "
        "from %s where name like 'bo%%' or id > 977 group by ip order by id + time asc, ip limit 5\" -s %s",
        g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "avg(id): 492", "avg(1): 1", "1: 1", "22222.1111: 22222.111100",
        "1: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // DTS2025061109413
    // order by 1+1，执行报错
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select TABLE_NAME, sum(1),*,1.11, 22222.1111, 1 "
        "from 'V\\$STORAGE_DISK_USAGE' where 1 == 1 or 1 != 1 or ROW_CNT > 1.1  order by 1 + 1 limit 5\" -s %s",
        g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_FEATURE_NOT_SUPPORTED);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// DTS2025041403444
/* ****************************************************************************
 Description  : 069.时序查询一键诊断视图，diagnostic_view 
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_069)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);
    // 查询一键诊断视图
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select diagnostic_view()\" -s %s",
        g_connServerTsdb);
    ret = executeCommand(g_command, "query key view: \'V$DB_SERVER\'", "SERVER_VERSION", "CPU_USAGE",
        "TABLE_NAME: tsdb1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    
    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
 
// DTS2025040139580
/* ****************************************************************************
 Description  : 070.时序内存表自定义创建64个索引，校验服务端日志
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_070)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    int ret = 0, tryCnt = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text, index idx(id), "
        "index idx1(id), index idx2(id), index idx3(id), index idx4(id), index idx5(id), index idx6(id), "
        "index idx7(id), index idx8(id), index idx9(id), index idx10(id), index idx11(id), index idx12(id), "
        "index idx13(id), index idx14(id), index idx15(id), index idx16(id), index idx17(id), index idx18(id), "
        "index idx19(id), index idx20(id), index idx21(id), index idx22(id), index idx23(id), index idx24(id), "
        "index idx25(id), index idx26(id), index idx27(id), index idx28(id), index idx29(id), index idx30(id), "
        "index idx31(id), index idx32(id), index idx33(id), index idx34(id), index idx35(id), index idx36(id), "
        "index idx37(id), index idx38(id), index idx39(id), index idx40(id), index idx41(id), index idx42(id), "
        "index idx43(id), index idx44(id), index idx45(id), index idx46(id), index idx47(id), index idx48(id), "
        "index idx49(id), index idx50(id), index idx51(id), index idx52(id), index idx53(id), index idx54(id), "
        "index idx55(id), index idx56(id), index idx57(id), index idx58(id), index idx59(id), index idx60(id), "
        "index idx61(id), index idx62(id), index idx63(id))"
        " with (engine = 'memory', max_size = 9223372036854775807, time_col = 'time', interval = '1 hour');",
        g_tableName);
 
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
 
    // 校验服务端错误日志
    (void)snprintf(g_command, MAX_CMD_SIZE, "grep -r \"ts idxNum\" %s", SERVER_LOG_PATH);
    while (tryCnt < MAX_TRY_CNT) {
        ret = executeCommand(g_command, "GMERR", "ts idxNum should less than 64");
        if (ret == GMERR_OK) {
            break;
        }
        sleep(1);
        tryCnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
 
// DTS2025040139580
/* ****************************************************************************
 Description  : 071.时序内存表engine为Memo，服务端日志符合预期
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_071)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_INVALID_JSON_CONTENT);
    int ret = 0, tryCnt = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text, index idx(id))"
        " with (engine = 'Memo', max_size = 9223372036854775807, time_col = 'time', interval = '1 hour');",
        g_tableName);
 
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_JSON_CONTENT, ret);
 
    // 校验服务端错误日志
    (void)snprintf(g_command, MAX_CMD_SIZE, "grep -r \"engine support\" %s", SERVER_LOG_PATH);
    while (tryCnt < MAX_TRY_CNT) {
        ret = executeCommand(g_command, "GMERR", "Incorrect json content", "engine support memory only");
        if (ret == GMERR_OK) {
            break;
        }
        sleep(1);
        tryCnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DTS2025053045529
/* ****************************************************************************
 Description  : 057.内存表，只含oder by查询，查询的列无序 limit 10，预期正常
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_072)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text, index idx(id))"
        " with (engine = 'memory', max_size = 9223372036854775807, time_col = 'time', interval = '1 hour');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);
    InsertData3(g_stmt, g_tableName, 1000);

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from %s "
        "order by id asc, name limit 10\" -s %s", g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "id: 0", "ns: david");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// DTS2025060407492
/* ****************************************************************************
 Description  : 073.时序表进行where子句过滤，过滤条件单列为大于-+100,预期服务端正常
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_073)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text, index idx(id))"
        " with (engine = 'memory', max_size = 9223372036854775807, time_col = 'time', interval = '1 hour');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName2);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);
    InsertData3(g_stmt, g_tableName2, 1000);

    // 查内存表
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select id, time, avg(id+time), name, ip from %s "
        "where id > -+499 group by ip order by id asc, ip \" -s %s", g_tableName, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_FEATURE_NOT_SUPPORTED);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 查时序逻辑表
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select id, time, avg(id+time), name, ip from %s "
        "where id > -+499 group by ip order by id asc, ip \" -s %s", g_tableName2, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_FEATURE_NOT_SUPPORTED);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropCmTable(g_stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// DTS2025060506075
/* ****************************************************************************
 Description  : 074.时序表进行where子句过滤，过滤条件为大于++++++++-100或者++++++++100,预期查询正常
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_074)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text, index idx(id))"
        " with (engine = 'memory', max_size = 9223372036854775807, time_col = 'time', interval = '1 hour');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName2);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);
    InsertData3(g_stmt, g_tableName2, 1000);

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select id, time, avg(id+time), name, ip from %s "
        "where id > ++++++++++++++-100 group by ip order by id asc, ip limit 5\" -s %s", g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "first(id): 0", "first(name): david", "ip: 10101040");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select id, time, avg(id+time), name, ip from %s "
        "where id > +++++++++++100 group by ip order by id asc, ip limit 5\" -s %s", g_tableName2, g_connServerTsdb);
    ret = executeCommand(g_command, "first(id): 101", "ip: 10101040");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropCmTable(g_stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// DTS2025060524357
/* ****************************************************************************
 Description  : 075.对gmexport -h帮助信息进行测试
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_075)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmexport -h");
    ret = executeCommand(g_command, "-h", "-c", "<cmd_type>", "-s", "<server_locator>");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 076.对gmimport -h帮助信息进行测试
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_076)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -h");
#if defined(FEATURE_MULTI_TS)
    ret = executeCommand(g_command, "-h", "-c", "<cmd_type>", "-s", "<server_locator>");
#else
    ret = executeCommand(g_command, "[ERROR] invalid option, ts only support gmimport -c sql -f xxx/xxx.gmsql format "
        "-s <server_locator>.");
#endif
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
//DTS2025060905786
/* ****************************************************************************
 Description  : 077.时序表进行where子句过滤，过滤条件算术表达式大于-+100或+-100,预期服务端正常
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_077)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SEMANTIC_ERROR);
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text, index idx(id))"
        " with (engine = 'memory', max_size = 9223372036854775807, time_col = 'time', interval = '1 hour');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName2);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);
    InsertData3(g_stmt, g_tableName2, 1000);

    // 查内存表
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select id, time, avg(id+time), name, ip from %s "
        "where id + id > -+499 group by ip order by id asc, ip \" -s %s", g_tableName, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 查时序逻辑表
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select id, time, avg(id+time), name, ip from %s "
        "where id + id > +++++-499 group by ip order by id asc, ip limit 5\" -s %s", g_tableName2, g_connServerTsdb);
    ret = executeCommand(g_command, "first(id): 0", "ip: 10101040");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 查询系统视图
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from %s where INTERVAL + INTERVAL > -+499 "
        "group by  TABLE_NAME limit 5\" -s %s", "\'V\\$TS_TBL_PROPS\'", g_connServerTsdb);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from %s where INTERVAL + INTERVAL > +++++-499 "
        "group by  TABLE_NAME limit 5\" -s %s", "\'V\\$TS_TBL_PROPS\'", g_connServerTsdb);
    ret = executeCommand(g_command, "TABLE_NAME: tsdb2", "first(INTERVAL): 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropCmTable(g_stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// DTS2025060910754
/* ****************************************************************************
 Description  : 078.时序表进行别名测试，投影别名在列名后面，预期查询成功
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_078)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text, index idx(id))"
        " with (engine = 'memory', max_size = 9223372036854775807, time_col = 'time', interval = '1 hour');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName2);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);
    InsertData3(g_stmt, g_tableName2, 1000);

    // 查内存表
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select id, time as n0, avg(id+time) ns1, name, ip ns2 "
        "from %s where id + id > 0 group by n0,name,n0 order by id,n0 asc, name, ns2 limit 5\" -s %s",
        g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "first(id): 1", "avg(id+time):", "first(ip): 10101040");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 查时序逻辑表
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select id, time as n0, avg(id+time) ns1, name, ip ns2 "
        "from %s where id + id > 0 group by n0,name,n0 order by id,n0 asc, name, ns2 limit 5\" -s %s",
        g_tableName2, g_connServerTsdb);
    ret = executeCommand(g_command, "first(id): 1", "avg(id+time):", "first(ip): 10101040");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 查询系统视图
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select CPU_CYCLE_PER_USEC as ns, * from 'V\\$DB_SERVER' "
        "group by ns, ns,CPU_CYCLE_PER_USEC,ns\" -s %s", g_connServerTsdb);
    ret = executeCommand(g_command, "CPU_CYCLE_PER_USEC:", "first(CPU_USAGE)");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropCmTable(g_stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// DTS2025060909875
/* ****************************************************************************
 Description  : 077.时序表进行sql查询，group by或者order by含列名.列名，预期报错
**************************************************************************** */
TEST_F(tsdbSupAvg_001_test, Timing_069_001_079)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SEMANTIC_ERROR);
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text, index idx(id))"
        " with (engine = 'memory', max_size = 9223372036854775807, time_col = 'time', interval = '1 hour');",
        g_tableName);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(64), ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)');",
        g_tableName2);

    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    InsertData3(g_stmt, g_tableName, 1000);
    InsertData3(g_stmt, g_tableName2, 1000);

    // 查内存表
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select id, time, avg(id+time), name, ip from %s "
        "where id + id > 0 group by tsdb1.id, id.id order by id asc, ip \" -s %s", g_tableName, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 查时序逻辑表
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select id, time, avg(id+time), name, ip from %s "
        "where id + id > +++++-499 order by tsdb2.id asc, id.ip limit 5\" -s %s", g_tableName2, g_connServerTsdb);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 查询系统视图
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from %s where INTERVAL + INTERVAL > 0 "
        "group by  TABLE_NAME.TABLE_NAME limit 5\" -s %s", "\'V\\$TS_TBL_PROPS\'", g_connServerTsdb);
    ret = executeCommand(g_command, "[WARN] Can not exec sysview sql", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 删除时序逻辑表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropCmTable(g_stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
