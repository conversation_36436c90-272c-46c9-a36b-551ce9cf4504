/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 【交付增强】支持BufferPool和B树 可靠测试测试
 * Author: lushiguang
 * Create: 2024-06-20
 */
#include "buffer_pool_btree.h"

class TsdbBfpoolRel : public testing::Test {
protected:
    static void SetUpTestCase() {}
    static void TearDownTestCase() {}

public:
    virtual void SetUp();
    virtual void TearDown();
};


void TsdbBfpoolRel::SetUp()
{
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        printf("get env PWD fail.\n");
    }
    (void)sprintf(g_dbFilePath, "%s/gmdb", pwdDir);
    (void)sprintf(g_cStoreDir, "%s/cstore", g_dbFilePath);
    (void)sprintf(g_tempFilePath, "%s/tempfile", pwdDir);

    system("sh $TEST_HOME/tools/stop.sh -f");
    (void)Rmdir(g_dbFilePath);
    (void)Rmdir(g_tempFilePath);
    int ret = mkdir(g_dbFilePath, S_IRUSR | S_IWUSR);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = mkdir(g_cStoreDir, S_IRUSR | S_IWUSR);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = mkdir(g_tempFilePath, S_IRUSR | S_IWUSR);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeTsGmserverCfg((char *)"recover", NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeTsGmserverCfg((char *)"dataFileDirPath", g_dbFilePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeTsGmserverCfg((char *)"cStoreDir", g_cStoreDir);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sysGMDBCfg[300];
    char suffix[] = "_ts.ini";
    (void)strncpy(sysGMDBCfg, g_sysGMDBCfg, strlen(g_sysGMDBCfg));
    char *pos = strstr(sysGMDBCfg, ".ini");
    if (pos != NULL) {
        strcpy(pos, suffix);
    }
    ret = GtExecSystemCmd("sed -i '/forceUseTempFileForQueryResult/d' %s", sysGMDBCfg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd("echo 'forceUseTempFileForQueryResult=0' >> %s", sysGMDBCfg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeTsGmserverCfg((char *)"tempFileDir", g_tempFilePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = StartEnvWithConfig(
        (char *)"bufferPoolSize=1024 pageSize=8 bufferPoolPolicy=0 tsLcmCheckPeriod=30 maxSeMem=512");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void TsdbBfpoolRel::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = ChangeTsGmserverCfg((char *)"recover", NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 断链
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 001. 时序表写入数据，数据小于bufferPoolSize，异常重启，验证数据  预期：数据无异常
// 单条数据大概80byte，1024KB大概10W数据
TEST_F(TsdbBfpoolRel, Timing_032_001_06_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int insertCount = 50000;
    (void)DropCmTable(g_stmt, g_tableName);
    ret = CommonOperate(g_stmt, g_tableName, insertCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 异常重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh -ts");
    ret = StartEnvWithConfig((char *)"bufferPoolSize=1024 pageSize=8 bufferPoolPolicy=0 tsLcmCheckPeriod=30");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 查询数据
    int expectCount = 50000;
    char queryCond[40] = "num1 >= 0";
    ret = QueryExpectRecord(g_stmt, g_tableName, queryCond, expectCount, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 002. 时序表写入数据，数据超过bufferPoolSize，异常重启，验证数据  预期：数据无异常
// 单条数据大概80byte，1024KB大概10W数据
TEST_F(TsdbBfpoolRel, Timing_032_001_06_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int insertCount = 1000000;
    (void)DropCmTable(g_stmt, g_tableName);
    ret = CommonOperate(g_stmt, g_tableName, insertCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 异常重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh -ts");
    ret = StartEnvWithConfig((char *)"bufferPoolSize=1024 pageSize=8 bufferPoolPolicy=0 tsLcmCheckPeriod=30");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 查询数据
    int expectCount = 50000;
    char queryCond[40] = "num1 >= 950000";
    ret = QueryExpectRecord(g_stmt, g_tableName, queryCond, expectCount, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 003. 时序表写入数据，数据小于bufferPoolSize，shutdown重启，验证数据  预期：数据无异常
// 单条数据大概80byte，1024KB大概10W数据
TEST_F(TsdbBfpoolRel, Timing_032_001_06_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int insertCount = 50000;
    (void)DropCmTable(g_stmt, g_tableName);
    ret = CommonOperate(g_stmt, g_tableName, insertCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // shutdown重启
    ret = ShutdownByGmadmin();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)system("sh $TEST_HOME/tools/start.sh -ts");
    ReConnEnv();
    // 查询数据
    int expectCount = 50000;
    char queryCond[40] = "num1 >= 0";
    ret = QueryExpectRecord(g_stmt, g_tableName, queryCond, expectCount, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_RESET_BY_PEER);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_MEMORY_OPERATE_FAILED);
}

// 004. 时序表写入数据，数据超过bufferPoolSize，shutdown重启，验证数据  预期：数据无异常
// 单条数据大概80byte，1024KB大概10W数据
TEST_F(TsdbBfpoolRel, Timing_032_001_06_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int insertCount = 1000000;
    (void)DropCmTable(g_stmt, g_tableName);
    ret = CommonOperate(g_stmt, g_tableName, insertCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // shutdown重启
    ret = ShutdownByGmadmin();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)system("sh $TEST_HOME/tools/start.sh -ts");
    ReConnEnv();

    // 查询数据
    int expectCount = 50000;
    char queryCond[40] = "num1 >= 950000";
    ret = QueryExpectRecord(g_stmt, g_tableName, queryCond, expectCount, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_RESET_BY_PEER);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_MEMORY_OPERATE_FAILED);
}

// 005. 时序表写入数据，过程中，异常重启，验证数据  预期：数据无异常
TEST_F(TsdbBfpoolRel, Timing_032_001_06_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int insertCount = 10000000;
    (void)DropCmTable(g_stmt, g_tableName);
    ret = CreateCmTable(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    pthread_t th;
    DMLArgsT arg;

    arg.insertCount = insertCount;
    arg.stmt = g_stmt;
    arg.tableName = g_tableName;
    ret = pthread_create(&th, NULL, InsertTsdbAsync, &arg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    sleep(3);
    (void)system("sh $TEST_HOME/tools/stop.sh -ts");
    ret = pthread_join(th, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)testGmcDisconnect(g_conn, g_stmt);
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    sleep(2);
    (void)system("sh $TEST_HOME/tools/start.sh -ts");
    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestTsGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int expectCount = 50000;
    char queryCond[100] = "num1 >= 0 and num1 < 50000";
    ret = QueryExpectRecord(g_stmt, g_tableName, queryCond, expectCount, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_RESET_BY_PEER);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_MEMORY_OPERATE_FAILED);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNEXPECTED_NULL_VALUE);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_NULL_VALUE_NOT_ALLOWED);
}

// 006. 时序表写入数据，过程中，shutdown重启，验证数据  预期：数据无异常
TEST_F(TsdbBfpoolRel, Timing_032_001_06_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int insertCount = 10000000;
    (void)DropCmTable(g_stmt, g_tableName);
    ret = CreateCmTable(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    pthread_t th;
    DMLArgsT arg;

    arg.insertCount = insertCount;
    arg.stmt = g_stmt;
    arg.tableName = g_tableName;
    ret = pthread_create(&th, NULL, InsertTsdbAsync, &arg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    sleep(3);
    // shutdown重启
    ret = ShutdownByGmadmin();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = pthread_join(th, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    (void)system("sh $TEST_HOME/tools/start.sh -ts");
    ReConnEnv();

    int expectCount = 50000;
    char queryCond[100] = "num1 >= 0 and num1 < 50000";
    ret = QueryExpectRecord(g_stmt, g_tableName, queryCond, expectCount, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_RESET_BY_PEER);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_MEMORY_OPERATE_FAILED);
}

// 007. 写入超过bufferPoolSize大小数据，构造磁盘满故障，大量查询数据，取消磁盘满后，大量查询数据，验证数据
// 预期：3、查询数据异常 4、正常查询
// 单条数据大概80byte，1024KB大概10W数据
TEST_F(TsdbBfpoolRel, Timing_032_001_06_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int insertCount = 1000000;
    (void)DropCmTable(g_stmt, g_tableName);
    ret = CommonOperate(g_stmt, g_tableName, insertCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 构造磁盘满故障
    ret = CfeInjectFaults((char *)"inject rfile_full (diskname) values (/dev/vda4)");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 查询数据
    int expectCount = 1000;
    char queryCond[100] = "num1 >= 0 and num1 < 1000";
    ret = QueryExpectRecord(g_stmt, g_tableName, queryCond, expectCount, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FILE_OPERATE_FAILED, ret);

    // 取消磁盘满后
    ret = CfeInjectFaults((char *)"clean rfile_full where diskname=/dev/vda4");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char queryCond2[100] = "num1 >= 500000 and num1 < 501000";
    ret = QueryExpectRecord(g_stmt, g_tableName, queryCond2, expectCount, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_FILE_OPERATE_FAILED);
}

// 008. 写入超过bufferPoolSize大小数据，构造系统文件句柄耗尽故障，大量查询数据，取消故障后，大量查询数据，验证数据
// 预期：3、查询数据异常 4、正常查询
// 单条数据大概80byte，1024KB大概10W数据
TEST_F(TsdbBfpoolRel, Timing_032_001_06_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int insertCount = 1000000;
    (void)DropCmTable(g_stmt, g_tableName);
    ret = CommonOperate(g_stmt, g_tableName, insertCount);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 构造系统文件句柄耗尽
    ret = CfeInjectFaults((char *)"inject rfile_deplenish (timeout) values (2)");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 查询数据
    int expectCount = 1000;
    char queryCond[100] = "num1 >= 0 and num1 < 1000";
    ret = QueryExpectRecord(g_stmt, g_tableName, queryCond, expectCount, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FILE_OPERATE_FAILED, ret);

    // 取消系统文件句柄耗尽
    sleep(20);

    char queryCond2[100] = "num1 >= 500000 and num1 < 501000";
    ret = QueryExpectRecord(g_stmt, g_tableName, queryCond2, expectCount, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(3, GMERR_DATA_CORRUPTION, GMERR_DIRECTORY_OPERATE_FAILED, GMERR_FILE_OPERATE_FAILED);
}

// 009.CPU满载场景，写入大量数据，触发btree分裂, 预期 数据无异常
TEST_F(TsdbBfpoolRel, Timing_032_001_06_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 注入CPU过载
    ret = CfeInjectFaults((char *)"inject rCPU_Overloadap (cpuid1, cpuid2) values(0, 7)");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int insertCount = 1000000;
    (void)DropCmTable(g_stmt, g_tableName);
    ret = CommonOperate(g_stmt, g_tableName, insertCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询数据
    int expectCount = 1000;
    char queryCond[100] = "num1 >= 0 and num1 < 1000";
    ret = QueryExpectRecord(g_stmt, g_tableName, queryCond, expectCount, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char queryCond2[100] = "num1 >= 500000 and num1 < 501000";
    ret = QueryExpectRecord(g_stmt, g_tableName, queryCond2, expectCount, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消注入CPU过载
    ret = CfeInjectFaults((char *)"clean rCPU_Overloadap");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 010.大数据量写入、不强制使用临时文件，大结果集查询, 预期 数据无异常
TEST_F(TsdbBfpoolRel, Timing_032_001_06_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    int insertCount = 1000000;
    (void)DropCmTable(g_stmt, g_tableName);
    ret = CommonOperate(g_stmt, g_tableName, insertCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询数据
    int expectCount = 100000;
    char queryCond[100] = "num1 >= 0 and num1 < 100000";
    ret = QueryExpectRecord(g_stmt, g_tableName, queryCond, expectCount, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 011.大数据量写入、强制使用临时文件，大结果集查询, 预期 数据无异常
TEST_F(TsdbBfpoolRel, Timing_032_001_06_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // shutdown重启
    ret = ShutdownByGmadmin();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeTsGmserverCfg((char *)"forceUseTempFileForQueryResult", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -ts");
    ReConnEnv();
    int insertCount = 1000000;
    (void)DropCmTable(g_stmt, g_tableName);
    ret = CommonOperate(g_stmt, g_tableName, insertCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询数据
    int expectCount = 100000;
    char queryCond[100] = "num1 >= 0 and num1 < 100000";
    ret = QueryExpectRecord(g_stmt, g_tableName, queryCond, expectCount, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_RESET_BY_PEER);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_MEMORY_OPERATE_FAILED);
}

// 012.大量访问数据，触及换入换出，持续一段时间，查看内存状态(执行时间较长，会在长稳覆盖), 预期 内存无异常
TEST_F(TsdbBfpoolRel, Timing_032_001_06_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int insertCount = 1000000;
    (void)DropCmTable(g_stmt, g_tableName);
    ret = CommonOperate(g_stmt, g_tableName, insertCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询数据
    int expectCount;
    char queryCond[100] = {0};
    int index = 0;
    uint32_t seed = (uint32_t)time(NULL);
    srand(seed);
    for (int i = 0; i < 500; i++) {
        if (i % 10 == 0) {
            AW_FUN_Log(LOG_STEP, "random QueryExpectRecord time: %d.", i);
        }
        expectCount = rand() % 10000;
        index = rand() % 100;
        (void)snprintf(queryCond, sizeof(queryCond), "num1 >= %d and num1 < %d", index * expectCount,
            (index + 1) * expectCount);
        ret = QueryExpectRecord(g_stmt, g_tableName, queryCond, expectCount, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 013. 时序表查询数据，过程中，异常退出后重启，验证数据  预期：正常重启
TEST_F(TsdbBfpoolRel, Timing_032_001_06_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int insertCount = 1000000;
    (void)DropCmTable(g_stmt, g_tableName);
    ret = CreateCmTable(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = InsertCommon(g_stmt, g_tableName, insertCount, ASC);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    pthread_t th;
    DMLArgsT arg;
    char qCond[] = "num1 > 0";
    arg.expectCount = 1000000;
    arg.stmt = g_stmt;
    arg.tableName = g_tableName;
    arg.queryCond = qCond;
    ret = pthread_create(&th, NULL, QueryRecordAsync, &arg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    sleep(1);
    (void)system("sh $TEST_HOME/tools/stop.sh -ts");
    ret = pthread_join(th, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)testGmcDisconnect(g_conn, g_stmt);
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    sleep(2);
    (void)system("sh $TEST_HOME/tools/start.sh -ts");
    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestTsGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int expectCount = 50000;
    char queryCond[100] = "num1 >= 0 and num1 < 50000";
    ret = QueryExpectRecord(g_stmt, g_tableName, queryCond, expectCount, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_RESET_BY_PEER);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_MEMORY_OPERATE_FAILED);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNEXPECTED_NULL_VALUE);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_NULL_VALUE_NOT_ALLOWED);
}

// 014. 时序表查询数据，过程中，shutdown退出后重启，验证数据  预期：正常重启
TEST_F(TsdbBfpoolRel, Timing_032_001_06_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int insertCount = 1000000;
    (void)DropCmTable(g_stmt, g_tableName);
    ret = CreateCmTable(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = InsertCommon(g_stmt, g_tableName, insertCount, ASC);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    pthread_t th;
    DMLArgsT arg;
    char qCond[] = "num1 > 0";
    arg.expectCount = 1000000;
    arg.stmt = g_stmt;
    arg.tableName = g_tableName;
    arg.queryCond = qCond;
    ret = pthread_create(&th, NULL, QueryRecordAsync, &arg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    sleep(1);
    // shutdown重启
    ret = ShutdownByGmadmin();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = pthread_join(th, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    (void)system("sh $TEST_HOME/tools/start.sh -ts");
    ReConnEnv();

    int expectCount = 50000;
    char queryCond[100] = "num1 >= 0 and num1 < 50000";
    ret = QueryExpectRecord(g_stmt, g_tableName, queryCond, expectCount, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_RESET_BY_PEER);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_MEMORY_OPERATE_FAILED);
}

// 015. 建表删表过程中，异常退出后重启，验证数据  预期：正常重启
TEST_F(TsdbBfpoolRel, Timing_032_001_06_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int insertCount = 1000000;
    (void)DropCmTable(g_stmt, g_tableName);
    ret = CreateCmTable(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = InsertCommon(g_stmt, g_tableName, insertCount, ASC);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int threadCount = 6;
    GmcConnT *conn[threadCount];
    GmcStmtT *stmt[threadCount];
    pthread_t th[threadCount];
    DDLArgsT arg[threadCount];
    for (int i = 0; i < threadCount; i++) {
        ret = TestTsGmcConnect(&conn[i], &stmt[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = 0; i < threadCount; i++) {
        arg[i].beginIndex = 0;
        arg[i].endIndex = 100;
        arg[i].stmt = stmt[i];
        arg[i].tableName = (char *)"testdb";
    }
    ret = pthread_create(&th[0], NULL, CreateTableAsync, &arg[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&th[1], NULL, CreateTableAsync, &arg[1]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&th[2], NULL, DropTableAsync, &arg[2]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&th[3], NULL, DropTableAsync, &arg[3]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&th[4], NULL, CreateTableAsync, &arg[4]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&th[5], NULL, DropTableAsync, &arg[5]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    sleep(1);
    (void)system("sh $TEST_HOME/tools/stop.sh -ts");

    for (int i = 0; i < threadCount; i++) {
        ret = pthread_join(th[i], NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    (void)testGmcDisconnect(g_conn, g_stmt);
    for (int i = 0; i < threadCount; i++) {
        (void)testGmcDisconnect(conn[i], stmt[i]);
    }
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    sleep(2);
    (void)system("sh $TEST_HOME/tools/start.sh -ts");
    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestTsGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int expectCount = 50000;
    char queryCond[100] = "num1 >= 0 and num1 < 50000";
    ret = QueryExpectRecord(g_stmt, g_tableName, queryCond, expectCount, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_RESET_BY_PEER);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DUPLICATE_TABLE, GMERR_UNDEFINED_TABLE);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_MEMORY_OPERATE_FAILED);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNEXPECTED_NULL_VALUE);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_LOCK_NOT_AVAILABLE);
}

// 016. 建表删表过程中，shutdown退出后重启，验证数据  预期：正常重启
TEST_F(TsdbBfpoolRel, Timing_032_001_06_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int insertCount = 1000000;
    (void)DropCmTable(g_stmt, g_tableName);
    ret = CreateCmTable(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = InsertCommon(g_stmt, g_tableName, insertCount, ASC);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int threadCount = 6;
    GmcConnT *conn[threadCount];
    GmcStmtT *stmt[threadCount];
    pthread_t th[threadCount];
    DDLArgsT arg[threadCount];
    for (int i = 0; i < threadCount; i++) {
        ret = TestTsGmcConnect(&conn[i], &stmt[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = 0; i < threadCount; i++) {
        arg[i].beginIndex = 0;
        arg[i].endIndex = 100;
        arg[i].stmt = stmt[i];
        arg[i].tableName = (char *)"testdb";
    }
    ret = pthread_create(&th[0], NULL, CreateTableAsync, &arg[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&th[1], NULL, CreateTableAsync, &arg[1]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&th[2], NULL, DropTableAsync, &arg[2]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&th[3], NULL, DropTableAsync, &arg[3]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&th[4], NULL, CreateTableAsync, &arg[4]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&th[5], NULL, DropTableAsync, &arg[5]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    sleep(1);
    // shutdown重启
    ret = ShutdownByGmadmin();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < threadCount; i++) {
        ret = pthread_join(th[i], NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    sleep(2);
    (void)system("sh $TEST_HOME/tools/start.sh -ts");
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < threadCount; i++) {
        ret = testGmcDisconnect(conn[i], stmt[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = TestTsGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int expectCount = 50000;
    char queryCond[100] = "num1 >= 0 and num1 < 50000";
    ret = QueryExpectRecord(g_stmt, g_tableName, queryCond, expectCount, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_RESET_BY_PEER);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DUPLICATE_TABLE, GMERR_UNDEFINED_TABLE);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_MEMORY_OPERATE_FAILED);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_DATA_EXCEPTION);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_DUPLICATE_OBJECT);
}

// 017. 老化删除过程中，异常退出后重启，验证数据  预期：正常重启
TEST_F(TsdbBfpoolRel, Timing_032_001_06_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int insertCount = 1000000;
    (void)DropCmTable(g_stmt, g_tableName);
    (void)snprintf(g_otherTableConfig, sizeof(g_otherTableConfig), ", ttl = '1 hour'");
    ret = CreateCmTable(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = InsertCommon(g_stmt, g_tableName, insertCount, ASC);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 等待老化出现
    int timeout = 40;
    int expectCount = 0;
    char queryCond2[100] = "num1 = 0";
    for (int i = 0; i < timeout; i++) {
        ret = QueryExpectRecord(g_stmt, g_tableName, queryCond2, expectCount, false);
        if (ret == GMERR_OK) {
            break;
        }
        sleep(1);
        AW_FUN_Log(LOG_STEP, "wait Aging time: %d.", i);
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)system("sh $TEST_HOME/tools/stop.sh -ts");

    (void)testGmcDisconnect(g_conn, g_stmt);
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    sleep(2);
    (void)system("sh $TEST_HOME/tools/start.sh -ts");
    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestTsGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_RESET_BY_PEER);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_MEMORY_OPERATE_FAILED);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNEXPECTED_NULL_VALUE);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_NULL_VALUE_NOT_ALLOWED);
}

// 018. 老化删除过程中，shutdown退出后重启，验证数据  预期：正常重启
TEST_F(TsdbBfpoolRel, Timing_032_001_06_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int insertCount = 1000000;
    (void)DropCmTable(g_stmt, g_tableName);
    (void)snprintf(g_otherTableConfig, sizeof(g_otherTableConfig), ", ttl = '1 hour'");
    ret = CreateCmTable(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = InsertCommon(g_stmt, g_tableName, insertCount, ASC);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 等待老化出现
    int timeout = 40;
    int expectCount = 0;
    char queryCond2[100] = "num1 = 0";
    for (int i = 0; i < timeout; i++) {
        ret = QueryExpectRecord(g_stmt, g_tableName, queryCond2, expectCount, false);
        if (ret == GMERR_OK) {
            break;
        }
        sleep(1);
        AW_FUN_Log(LOG_STEP, "wait Aging time: %d.", i);
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // shutdown重启
    ret = ShutdownByGmadmin();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    (void)system("sh $TEST_HOME/tools/start.sh -ts");
    ReConnEnv();

    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_RESET_BY_PEER);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_MEMORY_OPERATE_FAILED);
}

// 019. 大量写入数据，异常退出后重启，等待老化删除，验证数据
TEST_F(TsdbBfpoolRel, Timing_032_001_06_019)
{
    // 建表
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int insertCount = 2000000;
    (void)DropCmTable(g_stmt, g_tableName);
    (void)snprintf(g_otherTableConfig, sizeof(g_otherTableConfig), ", ttl = '1 hour'");
    ret = CreateCmTable(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 写数据
    ret = InsertCommon(g_stmt, g_tableName, insertCount, ASC);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 异常退出后重启
    (void)system("sh $TEST_HOME/tools/stop.sh -ts");

    (void)testGmcDisconnect(g_conn, g_stmt);
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    sleep(2);
    (void)system("sh $TEST_HOME/tools/start.sh -ts");
    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestTsGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 等待老化
    int timeout = 10;
    // 重启时，会清理过期数据，查询
    int expectCount = 0;
    char queryCond[100] = "num1 >= 0";
    for (int i = 0; i < timeout; i++) {
        ret = QueryExpectRecord(g_stmt, g_tableName, queryCond, expectCount, false);
        if (ret == GMERR_OK) {
            break;
        }
        sleep(4);
        AW_FUN_Log(LOG_STEP, "wait Aging time: %d.", i * 4);
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 重新写数据
    ret = InsertCommon(g_stmt, g_tableName, insertCount, ASC);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 查询
    char queryCond2[100] = "num1 >= 0";
    // 等待老化
    for (int i = 0; i < timeout; i++) {
        ret = QueryExpectRecord(g_stmt, g_tableName, queryCond2, expectCount, false);
        if (ret == GMERR_OK) {
            break;
        }
        sleep(4);
        AW_FUN_Log(LOG_STEP, "wait Aging time: %d.", i * 4);
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 删表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_RESET_BY_PEER);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_MEMORY_OPERATE_FAILED);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNEXPECTED_NULL_VALUE);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_REQUEST_TIME_OUT);
}

// 020. 大量写入数据，shutdown退出后重启，等待老化删除，验证数据
TEST_F(TsdbBfpoolRel, Timing_032_001_06_020)
{
    // 建表
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int insertCount = 2000000;
    (void)DropCmTable(g_stmt, g_tableName);
    (void)snprintf(g_otherTableConfig, sizeof(g_otherTableConfig), ", ttl = '1 hour'");
    ret = CreateCmTable(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 写数据
    ret = InsertCommon(g_stmt, g_tableName, insertCount, ASC);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // shutdown退出后重启
    ret = ShutdownByGmadmin();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    (void)system("sh $TEST_HOME/tools/start.sh -ts");
    ReConnEnv();

    int expectCount = 0;
    char queryCond2[100] = "num1 >= 0";

    // 等待老化
    int timeout = 10;
    for (int i = 0; i < timeout; i++) {
        ret = QueryExpectRecord(g_stmt, g_tableName, queryCond2, expectCount, false);
        if (ret == GMERR_OK) {
            break;
        }
        sleep(4);
        AW_FUN_Log(LOG_STEP, "wait Aging time: %d.", i * 4);
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 删表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_RESET_BY_PEER);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_MEMORY_OPERATE_FAILED);
}

// 021. 大量建表，异常退出后重启后，删表
TEST_F(TsdbBfpoolRel, Timing_032_001_06_021)
{
    // 建大量表并且写入数据
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    char tableName[100] = {0};
    int maxTableCount = 500;
    int insertCount = 10000;
    for (int i = 0; i < maxTableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "testdb%d", i);
        (void)DropCmTable(g_stmt, tableName);
        ret = CreateCmTable(g_stmt, tableName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = InsertCommon(g_stmt, tableName, insertCount, ASC);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 异常退出后重启
    (void)system("sh $TEST_HOME/tools/stop.sh -ts");

    (void)testGmcDisconnect(g_conn, g_stmt);
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    sleep(2);
    (void)system("sh $TEST_HOME/tools/start.sh -ts");
    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestTsGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 删表
    for (int i = 0; i < maxTableCount / 10; i++) {
        (void)snprintf(tableName, sizeof(tableName), "testdb%d", i);
        ret = DropCmTable(g_stmt, tableName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_ADD_ERRNUM_WHITE_LIST(3, GMERR_CONNECTION_RESET_BY_PEER, GMERR_DATA_EXCEPTION, GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_MEMORY_OPERATE_FAILED);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNEXPECTED_NULL_VALUE);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_NULL_VALUE_NOT_ALLOWED);
}


// 022. 大量建表，shutdown重启后，删表
TEST_F(TsdbBfpoolRel, Timing_032_001_06_022)
{
    // 建大量表并且写入数据
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    char tableName[100] = {0};
    int maxTableCount = 500;
    int insertCount = 10000;
    for (int i = 0; i < maxTableCount; i++) {
        (void)snprintf(tableName, sizeof(tableName), "testdb%d", i);
        (void)DropCmTable(g_stmt, tableName);
        ret = CreateCmTable(g_stmt, tableName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = InsertCommon(g_stmt, tableName, insertCount, ASC);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // shutdown退出后重启
    ret = ShutdownByGmadmin();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    (void)system("sh $TEST_HOME/tools/start.sh -ts");
    ReConnEnv();

    // 删表
    for (int i = 0; i < maxTableCount / 10; i++) {
        (void)snprintf(tableName, sizeof(tableName), "testdb%d", i);
        ret = DropCmTable(g_stmt, tableName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_RESET_BY_PEER);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_MEMORY_OPERATE_FAILED);
}

