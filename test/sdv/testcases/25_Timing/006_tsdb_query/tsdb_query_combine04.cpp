/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2024. All rights reserved.
 * Description: 【技术转交付】TSDB 基础查询 测试
 * Author: jiangjincheng
 * Create: 2024-03-19
 */
#include "gtest/gtest.h"
#include "t_rd_ts.h"
#include "../006_tsdb_query/tsdb_query.h"

char tabelName[] = "testdb";
char tabelName_1[] = "testdb_1";
char compressModeFast[] = "fast";
char compressModeRapidlz[] = "fast(rapidlz)";
char compressModeZstar[] = "fast(zstar)";
char compressModeNo[] = "no";

static GmcConnOptionsT *connOptions = NULL;
static GmcConnT *conn = NULL;
static GmcStmtT *stmt = NULL;
Status ret = 0;
bool eof = false;
bool isNull = false;

class TsdbQueryCombine : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        InitTsCiCfg();
        // 注释system("sh $TEST_HOME/tools/stop.sh -f");
        // 当前CI默认配置为3,4,5导致处理时间过长时会导致hang，然后服务端报core，后面转需求
        // 注释system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=20,299,300\"");
        // 将算子执行时申请内存设置小一点，使得触发外排和堆排场景
        // 注释system("sh $TEST_HOME/tools/modifyCfg.sh \"operatorMemory=15\" ");
        // 注释system("sh $TEST_HOME/tools/start.sh");
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        // 创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);

        ret = TestTsGmcConnect(&conn, &stmt, 0);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        DropCmTable(stmt, tabelName);
    }
    static void TearDownTestCase()
    {
        GmcConnOptionsDestroy(connOptions);
        GmcFreeStmt(stmt);
        GmcDisconnect(conn);
        close_epoll_thread();
        testEnvClean();
        RecoverTsCiCfg();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void TsdbQueryCombine::SetUp()
{
    ret = 0;
    AW_CHECK_LOG_BEGIN();
}

void TsdbQueryCombine::TearDown()
{
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
}


// 大量数据，压缩模式为fast（rapidlz），运算符and与>，<组合查询01 预期：功能正常
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_190)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char querySql[] = "SELECT time, num2 FROM testdb WHERE num2 < 2 and time < 1695102000";

    ret = TsdbQueryCombineLotsofDataExecDirectResult(stmt, querySql);
    // 3*(20000+10000)
    AW_MACRO_ASSERT_EQ_INT(90000, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），运算符and与>=，<=组合查询01 预期：功能正常
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_191)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char querySql[] = "SELECT id, time FROM testdb WHERE num2 >= 2 and time <= 1695092000";

    ret = TsdbQueryCombineLotsofDataExecDirectResult(stmt, querySql);
    // 3*()
    AW_MACRO_ASSERT_EQ_INT(90000, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），运算符and与=组合查询 预期：功能正常
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_192)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char querySql[] = "SELECT id, time FROM testdb WHERE num1 = 2000000002 and num2 = 2";
    
    ret = TsdbQueryCombineLotsofDataExecDirectResult(stmt, querySql);
    AW_MACRO_ASSERT_EQ_INT(150000, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），运算符or与>，<组合查询01 预期：功能正常
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_193)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char querySql[] = "SELECT id, time FROM testdb WHERE num2 > 2 or time < 1695092000";

    ret = TsdbQueryCombineLotsofDataExecDirectResult(stmt, querySql);
    AW_MACRO_ASSERT_EQ_INT(270000, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），运算符or与>=，<=组合查询01 预期：功能正常
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_194)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char querySql[] = "SELECT id, time FROM testdb WHERE num2 >= 3 or time <= 1695072000";

    ret = TsdbQueryCombineLotsofDataExecDirectResult(stmt, querySql);
    AW_MACRO_ASSERT_EQ_INT(240003, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），运算符or与=组合查询 预期：功能正常
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_195)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char querySql[] = "SELECT id, time FROM testdb WHERE id = 5 or num2 = 3";
    
    ret = TsdbQueryCombineLotsofDataExecDirectResult(stmt, querySql);
    AW_MACRO_ASSERT_EQ_INT(150003, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），多个运算符and组合查询 预期：功能正常
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_196)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char querySql[] = "SELECT id, time FROM testdb WHERE id < 5 and time <= 1695052000 and num2 = 1 and  num1 >= 2000000000";
    ret = TsdbQueryCombineLotsofDataExecDirectResult(stmt, querySql);
    // 3*4
    AW_MACRO_ASSERT_EQ_INT(12, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），多个运算符or组合查询 预期：功能正常
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_197)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char querySql[] = "SELECT id, time FROM testdb WHERE id < 5000 or time <= 1695043000 or num2 = 3 or  id > 245000";

    ret = TsdbQueryCombineLotsofDataExecDirectResult(stmt, querySql);
    // 3*(4999+5*10000)
    AW_MACRO_ASSERT_EQ_INT(164997, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），多个运算符and与or组合查询 预期：功能正常
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_198)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char querySql[] = "SELECT id, time FROM testdb WHERE id < 1001 and time <= 1695045000 or num2 = 3 or  num1 > 2000000004";
    ret = TsdbQueryCombineLotsofDataExecDirectResult(stmt, querySql);
    // 3*(5*10000+1000)
    AW_MACRO_ASSERT_EQ_INT(153000, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），运算符and、or与比较运算符组合查询加括号改变逻辑01 预期：功能正常
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_199)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char querySql[] = "SELECT id, time FROM testdb WHERE id > 248999 or (time <= 1695043000 or num2 = 3) and num1 < 2000000002";
    ret = TsdbQueryCombineLotsofDataExecDirectResult(stmt, querySql);
    // 3*(1000+1001)
    AW_MACRO_ASSERT_EQ_INT(6006, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），聚合函数MIN与范围运算符组合查询 预期：功能正常
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_200)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char querySql[] = "SELECT MIN(id), time FROM testdb WHERE num2 <= 1 group by time";
    ret = TsdbQueryCombineLotsofDataExecDirectResult(stmt, querySql);
    // 5*20000
    AW_MACRO_ASSERT_EQ_INT(100000, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），聚合函数MIN与逻辑运算符组合查询 预期：功能正常
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_201)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char querySql[] = "SELECT MIN(id), time FROM testdb WHERE id <= 8 and time >= 1695042000 group by time";
    ret = TsdbQueryCombineLotsofDataExecDirectResult(stmt, querySql);
    // 1*8
    AW_MACRO_ASSERT_EQ_INT(8, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），聚合函数MIN与多个运算符组合查询 预期：功能正常
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_202)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char querySql[] = "SELECT MIN(id), time FROM testdb WHERE id <= 5 and num1 <= 2000000001 or time >= 1695291000 group by time";
    ret = TsdbQueryCombineLotsofDataExecDirectResult(stmt, querySql);
    // 5+1000
    AW_MACRO_ASSERT_EQ_INT(1005, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），聚合函数MAX与范围运算符组合查询 预期：功能正常
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_203)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char querySql[] = "SELECT MAX(id), time FROM testdb WHERE id <= 8 group by time";
    ret = TsdbQueryCombineLotsofDataExecDirectResult(stmt, querySql);
    // 8
    AW_MACRO_ASSERT_EQ_INT(8, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），聚合函数MAX与逻辑运算符组合查询 预期：功能正常
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_204)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char querySql[] = "SELECT MAX(id), time FROM testdb WHERE id <= 8 or time >= 1695291950 group by time";
    ret = TsdbQueryCombineLotsofDataExecDirectResult(stmt, querySql);
    // 50+8
    AW_MACRO_ASSERT_EQ_INT(58, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），聚合函数MAX与多个运算符组合查询 预期：功能正常
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_205)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char querySql[] = "SELECT MAX(id), time FROM testdb WHERE id <= 5 or time >= 1695142000 and num1 <= 2000000001 group by time";
    ret = TsdbQueryCombineLotsofDataExecDirectResult(stmt, querySql);
    // 2*20000+5
    AW_MACRO_ASSERT_EQ_INT(60005, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），聚合函数SUM（单个参数）范围运算符组合查询 预期：功能正常
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_206)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char querySql[] = "SELECT SUM(id), time FROM testdb WHERE id <= 120008 group by time";
    ret = TsdbQueryCombineLotsofDataExecDirectResult(stmt, querySql);
    // 8
    AW_MACRO_ASSERT_EQ_INT(120008, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），聚合函数SUM（单个参数）逻辑运算符组合查询 预期：功能正常
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_207)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char querySql[] = "SELECT SUM(id), time FROM testdb WHERE num2 <= 2 and time >= 1695159000 group by time";
    ret = TsdbQueryCombineLotsofDataExecDirectResult(stmt, querySql);
    // 120000 - 10700
    AW_MACRO_ASSERT_EQ_INT(103000, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），聚合函数SUM（单个参数）多个运算符组合查询 预期：功能正常
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_208)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char querySql[] = "SELECT SUM(id), time FROM testdb WHERE id <= 5 or time >= 1695049850 and num1 <= 2000000000 group by time";
    ret = TsdbQueryCombineLotsofDataExecDirectResult(stmt, querySql);
    // 5+40000+10000-7850
    AW_MACRO_ASSERT_EQ_INT(42155, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），聚合函数COUNT（单个参数）范围运算符组合查询 预期：功能正常
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_209)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char querySql[] = "SELECT COUNT(id), time FROM testdb WHERE num1 <= 2000000001 group by time";
    ret = TsdbQueryCombineLotsofDataExecDirectResult(stmt, querySql);
    // 5*20000
    AW_MACRO_ASSERT_EQ_INT(100000, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），聚合函数COUNT（单个参数）逻辑运算符组合查询 预期：功能正常
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_210)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char querySql[] = "SELECT COUNT(id), time FROM testdb WHERE id >= 8 and time <= 1695052000 group by time";
    ret = TsdbQueryCombineLotsofDataExecDirectResult(stmt, querySql);
    // 10001-7
    AW_MACRO_ASSERT_EQ_INT(9994, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），聚合函数COUNT（单个参数）多个运算符组合查询 预期：功能正常
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_211)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char querySql[] = "SELECT COUNT(id), time FROM testdb WHERE  time < 1695053000 or id >= 5 and num1 <= 2000000000 group by time";
    ret = TsdbQueryCombineLotsofDataExecDirectResult(stmt, querySql);
    // 11000 + 5  + 40000 - 5
    AW_MACRO_ASSERT_EQ_INT(51000, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），聚合函数MIN与MAX组合查询 预期：查询成功
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_212)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char querySql[] = "SELECT MIN(id), MAX(time), time FROM testdb WHERE id <= 5 or time >= 1695275000 and num1 <= 2000000004 group by time";
    ret = TsdbQueryCombineLotsofDataExecDirectResult(stmt, querySql);
    // 5+1695292000-1695275000
    AW_MACRO_ASSERT_EQ_INT(17005, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），聚合函数MIN与SUM（单个参数）组合查询 预期：查询成功
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_213)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char querySql[] = "SELECT SUM(id), MIN(time), time FROM testdb WHERE id <= 8 or num1 >= 2000000004 group by time";
    ret = TsdbQueryCombineLotsofDataExecDirectResult(stmt, querySql);
    // 10000*5+8
    AW_MACRO_ASSERT_EQ_INT(50008, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），聚合函数MIN与COUNT（单个参数）组合查询 预期：查询成功
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_214)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char querySql[] = "SELECT COUNT(id), MIN(time), time FROM testdb WHERE id <= 8 or num1 >= 2000000004 group by time";
    ret = TsdbQueryCombineLotsofDataExecDirectResult(stmt, querySql);
    // 5*10000+8
    AW_MACRO_ASSERT_EQ_INT(50008, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），聚合函数MAX与SUM（单个参数）组合查询 预期：查询成功
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_215)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char querySql[] = "SELECT SUM(id), MAX(time), time FROM testdb WHERE id <= 8888 and time >= 1695042345 group by time";
    ret = TsdbQueryCombineLotsofDataExecDirectResult(stmt, querySql);
    // 8888-345
    AW_MACRO_ASSERT_EQ_INT(8543, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），聚合函数MAX与COUNT（单个参数）组合查询 预期：查询成功
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_216)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char querySql[] = "SELECT COUNT(id), MAX(time), time FROM testdb WHERE id <= 8888 and time >= 1695042388 group by time";
    ret = TsdbQueryCombineLotsofDataExecDirectResult(stmt, querySql);
    // 8888-388
    AW_MACRO_ASSERT_EQ_INT(8500, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），聚合函数SUM（单列参数）与COUNT（单列参数）组合查询 预期：查询成功
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_217)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char querySql[] = "SELECT SUM(id), COUNT(time), time FROM testdb WHERE num1 = 2000000000 or time < 1695055000 group by time";
    ret = TsdbQueryCombineLotsofDataExecDirectResult(stmt, querySql);
    AW_MACRO_ASSERT_EQ_INT(53000, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），多项聚合函数间组合查询01 预期：查询成功
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_218)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char querySql[] = "SELECT SUM(num1), COUNT(time), MAX(id), time FROM testdb WHERE id <= 200008 and time >= 1695142000 and num1 < 2000000003 group by time";
    ret = TsdbQueryCombineLotsofDataExecDirectResult(stmt, querySql);
    AW_MACRO_ASSERT_EQ_INT(60008, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），order by与范围运算符组合查询 预期：查询成功
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_219)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char querySql[] = "SELECT id, time FROM testdb WHERE id <= 118000 order by time ASC";
    int SortCheckMode = 0;
    ret = TsdbQueryCombineLotsofDataExecDirectResultSortCheck(stmt, querySql, SortCheckMode);
    // 118000*3
    AW_MACRO_ASSERT_EQ_INT(354000, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），order by与逻辑运算符组合查询 预期：查询成功
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_220)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char querySql[] = "SELECT id, time FROM testdb WHERE id <= 80000 or num2 = 3 order by time ASC";
    int SortCheckMode = 0;
    ret = TsdbQueryCombineLotsofDataExecDirectResultSortCheck(stmt, querySql, SortCheckMode);
    // 3*(80000+50000-10000)
    AW_MACRO_ASSERT_EQ_INT(360000, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），order by与多个运算符组合查询 预期：查询成功
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_221)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 采用no压缩模式
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char querySql[] = "SELECT time FROM testdb WHERE id <= 75000 and time > 1695052000 or num2 = 4 order by time DESC";
    int SortCheckMode = 1;
    ret = TsdbQueryCombineLotsofDataExecDirectResultSortCheck(stmt, querySql, SortCheckMode);
    AW_MACRO_ASSERT_EQ_INT(194997, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），order by与聚合函数MIN组合查询 预期：查询成功
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_222)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char querySql[] = "SELECT MIN(id), MIN(num1), num2 FROM testdb WHERE id <= 8 or time <= 1695287500 and num1 >= 2000000001 group by num2 order by MIN(id) ASC";
    int SortCheckMode = 0;
    ret = TsdbQueryCombineLotsofDataExecDirectResultSortCheck(stmt, querySql, SortCheckMode);
    AW_MACRO_ASSERT_EQ_INT(3, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），order by与聚合函数MAX组合查询 预期：查询成功
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_223)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char querySql[] = "SELECT MAX(id), MAX(num1), time FROM testdb WHERE id <= 8 or time <= 1695287500 and num1 >= 2000000001 group by time order by MAX(id) ASC";
    int SortCheckMode = 0;
    ret = TsdbQueryCombineLotsofDataExecDirectResultSortCheck(stmt, querySql, SortCheckMode);
    // 8+287500-252000+4*40000+1
    AW_MACRO_ASSERT_EQ_INT(195509, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），order by与聚合函数SUM（单列参数）组合查询 预期：查询成功
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_224)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char querySql[] = "SELECT SUM(id), SUM(num1), time FROM testdb WHERE id <= 8 or time <= 1695287500 and num1 >= 2000000001 group by time order by SUM(id) DESC";
    int SortCheckMode = 1;
    ret = TsdbQueryCombineLotsofDataExecDirectResultSortCheck(stmt, querySql, SortCheckMode);
    // 8+287500-252000+4*40000+1
    AW_MACRO_ASSERT_EQ_INT(195509, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），order by与聚合函数COUNT（单列参数）组合查询 预期：查询成功
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_225)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char querySql[] = "SELECT COUNT(id), COUNT(num1), time FROM testdb WHERE id <= 8 and time > 1695042000 or num2 >= 2 group by time order by COUNT(id) ASC";
    int SortCheckMode = 0;
    ret = TsdbQueryCombineLotsofDataExecDirectResultSortCheck(stmt, querySql, SortCheckMode);
    // 7+5*30000
    AW_MACRO_ASSERT_EQ_INT(150007, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），order by与多个聚合函数组合查询 预期：查询成功
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_226)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 排序的时候需要将对应的聚合函数放到查询的第一项（.h头文件判断是否排序检查的第一项）
    char querySql[] = "SELECT COUNT(id), SUM(num2), time FROM testdb WHERE id <= 8 or time >= 1695042015 group by time order by COUNT(id) DESC";
    int SortCheckMode = 1;
    ret = TsdbQueryCombineLotsofDataExecDirectResultSortCheck(stmt, querySql, SortCheckMode);
    // 250000-15+8
    AW_MACRO_ASSERT_EQ_INT(249993, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），order by多参数全为增序 预期：查询成功
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_227)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfDataToOrderByWithTwoParameter(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 排序的时候需要将对应的聚合函数放到查询的第一项（.h头文件判断是否排序检查的第一项）
    char querySql[] = "SELECT num2, time FROM testdb WHERE id >= 95000 and time <= 1695147000 order by num2 ASC, time ASC";
    int SortCheckMode = 2;
    ret = TsdbQueryCombineLotsofDataExecDirectResultSortCheck(stmt, querySql, SortCheckMode);
    // 3*(5000+1+5000+1)
    AW_MACRO_ASSERT_EQ_INT(30006, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），order by多参数全为降序 预期：查询成功
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_228)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfDataToOrderByWithTwoParameter(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 排序的时候需要将对应的聚合函数放到查询的第一项（.h头文件判断是否排序检查的第一项）
    char querySql[] = "SELECT num2, time FROM testdb WHERE id >= 195000 and time <= 1695247000 order by num2 DESC, time DESC";
    int SortCheckMode = 3;
    ret = TsdbQueryCombineLotsofDataExecDirectResultSortCheck(stmt, querySql, SortCheckMode);
    // 3*(5000+1+5000+1)
    AW_MACRO_ASSERT_EQ_INT(30006, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），order by多参数为先增序再降序 预期：查询成功
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_229)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfDataToOrderByWithTwoParameter(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 排序的时候需要将对应的聚合函数放到查询的第一项（.h头文件判断是否排序检查的第一项）
    char querySql[] = "SELECT num2, time FROM testdb WHERE id >= 195001 and time <= 1695246999 order by num2 ASC, time DESC";
    int SortCheckMode = 4;
    ret = TsdbQueryCombineLotsofDataExecDirectResultSortCheck(stmt, querySql, SortCheckMode);
    // 3*(5000+5000)
    AW_MACRO_ASSERT_EQ_INT(30000, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），order by多参数为先降序再增序 预期：查询成功
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_230)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfDataToOrderByWithTwoParameter(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 排序的时候需要将对应的聚合函数放到查询的第一项（.h头文件判断是否排序检查的第一项）
    char querySql[] = "SELECT num2, time FROM testdb WHERE id >= 145001 and time <= 1695246999 order by num2 DESC, time ASC";
    int SortCheckMode = 5;
    ret = TsdbQueryCombineLotsofDataExecDirectResultSortCheck(stmt, querySql, SortCheckMode);
    // 3*(55000+5000)
    AW_MACRO_ASSERT_EQ_INT(180000, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），group by多参数查询 预期：查询成功
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_231)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfDataToOrderByWithTwoParameter(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 排序的时候需要将对应的聚合函数放到查询的第一项（.h头文件判断是否排序检查的第一项）
    char querySql[] = "SELECT num2, time, sum(id), MAX(num1) FROM testdb WHERE id >= 5001 and time <= 1695146999 group by num2, time";
    ret = TsdbQueryCombineLotsofDataExecDirectResult(stmt, querySql);
    // 1695147000-1695042000-5000
    AW_MACRO_ASSERT_EQ_INT(100000, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），group by多参数查询与order by单参数组合查询 预期：查询成功
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_232)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfDataToOrderByWithTwoParameter(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 排序的时候需要将对应的聚合函数放到查询的第一项（.h头文件判断是否排序检查的第一项）
    char querySql[] = "SELECT sum(id), num2, time, count(num1) FROM testdb WHERE id >= 5001 and time <= 1695146999 group by num2, time order by sum(id) ASC";
    int SortCheckMode = 0;
    ret = TsdbQueryCombineLotsofDataExecDirectResultSortCheck(stmt, querySql, SortCheckMode);
    // 1695147000-1695042000-5000
    AW_MACRO_ASSERT_EQ_INT(100000, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），group by多参数查询与order by多参数组合查询 预期：查询成功
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_233)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfDataToOrderByWithTwoParameter(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 排序的时候需要将对应的聚合函数放到查询的第一项（.h头文件判断是否排序检查的第一项）
    char querySql[] = "SELECT count(num1), sum(id), num2, time FROM testdb WHERE id >= 5001 and time <= 1695146999 group by num2, time"
    " order by count(num1)DESC, sum(id) ASC";
    int SortCheckMode = 5;
    ret = TsdbQueryCombineLotsofDataExecDirectResultSortCheck(stmt, querySql, SortCheckMode);
    // 1695147000-1695042000-5000
    AW_MACRO_ASSERT_EQ_INT(100000, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），order by与多个聚合函数组合查询后limit值比查询到数据量少 预期：查询成功
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_234)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfDataToOrderByWithTwoParameter(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 排序的时候需要将对应的聚合函数放到查询的第一项（.h头文件判断是否排序检查的第一项）
    char querySql[] = "SELECT count(num1), sum(id), num2, time FROM testdb WHERE id >= 5001 and time <= 1695146999 group by num2, time"
    " order by count(num1)DESC, sum(id) ASC limit 5000";
    int SortCheckMode = 5;
    ret = TsdbQueryCombineLotsofDataExecDirectResultSortCheck(stmt, querySql, SortCheckMode);
    // 1695147000-1695042000-5000但limit 5000
    AW_MACRO_ASSERT_EQ_INT(5000, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），order by与多个聚合函数组合查询后limit值比查询到数据量多 预期：查询成功
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_235)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfDataToOrderByWithTwoParameter(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 排序的时候需要将对应的聚合函数放到查询的第一项（.h头文件判断是否排序检查的第一项）
    char querySql[] = "SELECT count(num1), sum(id), num2, time FROM testdb WHERE id >= 5001 and time <= 1695146999 group by num2, time"
    " order by count(num1)DESC, sum(id) ASC limit 150000;";
    int SortCheckMode = 5;
    ret = TsdbQueryCombineLotsofDataExecDirectResultSortCheck(stmt, querySql, SortCheckMode);
    // 31695147000-1695042000-5000
    AW_MACRO_ASSERT_EQ_INT(100000, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），定长字符串的多个运算符and组合查询 预期：功能正常
TEST_F(TsdbQueryCombine, DISABLED_Timing_006_TsdbQueryCombine_236)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char querySql[] = "SELECT name, time, num2 FROM testdb WHERE name >= 'david' and time < 1695102000;";

    ret = TsdbQueryCombineStringLotsofDataResult(stmt, querySql);
    // 3*(20000+10000)
    AW_MACRO_ASSERT_EQ_INT(90000, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），定长字符串的多个运算符or组合查询 预期：功能正常
TEST_F(TsdbQueryCombine, DISABLED_Timing_006_TsdbQueryCombine_237)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char querySql[] = "SELECT id, time FROM testdb WHERE id < 5000 or time <= 1695043000 or name <= 'david';";

    ret = TsdbQueryCombineStringLotsofDataResult(stmt, querySql);
    // 3*(4999+5*10000)
    AW_MACRO_ASSERT_EQ_INT(164997, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），定长字符串的多个运算符and与or组合查询 预期：功能正常
TEST_F(TsdbQueryCombine, DISABLED_Timing_006_TsdbQueryCombine_238)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char querySql[] = "SELECT name, id, time FROM testdb WHERE id < 1001 and time <= 1695045000 or num2 = 3 and name <= 'david';";
    ret = TsdbQueryCombineStringLotsofDataResult(stmt, querySql);
    // 3*(5*10000+1000)
    AW_MACRO_ASSERT_EQ_INT(153000, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），定长字符串的运算符and、or与比较运算符组合查询加括号改变逻辑 预期：功能正常
TEST_F(TsdbQueryCombine, DISABLED_Timing_006_TsdbQueryCombine_239)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char querySql[] = "SELECT id, time FROM testdb WHERE id > 248999 or (time <= 1695043000 or num2 = 3 and name <= 'david') and num1 < 2000000002;";
    ret = TsdbQueryCombineStringLotsofDataResult(stmt, querySql);
    // 3*(1000+1001)
    AW_MACRO_ASSERT_EQ_INT(6006, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），定长字符串的group by多参数查询 预期：查询成功
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_240)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfDataToOrderByWithTwoParameter(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 排序的时候需要将对应的聚合函数放到查询的第一项（.h头文件判断是否排序检查的第一项）
    char querySql[] = "SELECT name, num2, time, sum(id) FROM testdb WHERE id >= 5001 and time <= 1695146999 group by name, num2, time";
    ret = TsdbQueryCombineStringLotsofDataResult(stmt, querySql);
    // 1695147000-1695042000-5000
    AW_MACRO_ASSERT_EQ_INT(100000, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），定长字符串的order by与多个运算符组合查询 预期：查询成功
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_241)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 采用no压缩模式
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char querySql[] = "SELECT name FROM testdb WHERE id <= 75000 and time > 1695052000 or num2 = 4 order by name DESC";
    int SortCheckMode = 1;
    ret = TsdbQueryStringCombineSimpleSortCheck(stmt, querySql, SortCheckMode);
    AW_MACRO_ASSERT_EQ_INT(194997, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），定长字符串的order by与聚合函数MIN组合查询 预期：查询成功
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_242)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char querySql[] = "SELECT name, MIN(num1) FROM testdb WHERE id <= 8 or time <= 1695287500 and num1 >= 2000000001 group by name order by name ASC";
    int SortCheckMode = 0;
    ret = TsdbQueryStringCombineSimpleSortCheck(stmt, querySql, SortCheckMode);
    AW_MACRO_ASSERT_EQ_INT(10, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），定长字符串的order by与聚合函数MAX组合查询 预期：查询成功
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_243)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char querySql[] = "SELECT name, MAX(num1) FROM testdb WHERE id <= 8 and num1 >= 2000000000 group by name order by name ASC";
    int SortCheckMode = 0;
    ret = TsdbQueryStringCombineSimpleSortCheck(stmt, querySql, SortCheckMode);
    // 8+287500-252000+4*40000+1
    AW_MACRO_ASSERT_EQ_INT(8, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），定长字符串的order by与多个聚合函数组合查询 预期：查询成功
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_244)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 排序的时候需要将对应的聚合函数放到查询的第一项（.h头文件判断是否排序检查的第一项）
    char querySql[] = "SELECT name, MAX(time), MIN(id) FROM testdb WHERE id <= 1015 or time >= 1695042015 group by name order by name DESC";
    int SortCheckMode = 1;
    ret = TsdbQueryStringCombineSimpleSortCheck(stmt, querySql, SortCheckMode);
    // 250000-15+8
    AW_MACRO_ASSERT_EQ_INT(10, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），order by多参数全为增序 预期：查询成功
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_245)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 排序的时候需要将对应的聚合函数放到查询的第一项（.h头文件判断是否排序检查的第一项）
    char querySql[] = "SELECT name, id FROM testdb WHERE id >= 95000 and time <= 1695147000 order by name ASC, id ASC";
    int SortCheckMode = 2;
    ret = TsdbQueryStringCombineSimpleSortCheck(stmt, querySql, SortCheckMode);
    // 3*(5000+1+5000+1)
    AW_MACRO_ASSERT_EQ_INT(30006, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），order by多参数全为降序 预期：查询成功
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_246)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 排序的时候需要将对应的聚合函数放到查询的第一项（.h头文件判断是否排序检查的第一项）
    char querySql[] = "SELECT name, time FROM testdb WHERE id >= 195000 and time <= 1695247000 order by name DESC, time DESC";
    int SortCheckMode = 3;
    ret = TsdbQueryStringCombineSimpleSortCheck(stmt, querySql, SortCheckMode);
    // 3*(5000+1+5000+1)
    AW_MACRO_ASSERT_EQ_INT(30006, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），order by多参数为先增序再降序 预期：查询成功
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_247)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 排序的时候需要将对应的聚合函数放到查询的第一项（.h头文件判断是否排序检查的第一项）
    char querySql[] = "SELECT name, time FROM testdb WHERE id >= 195001 and time <= 1695246999 order by name ASC, time DESC";
    int SortCheckMode = 4;
    ret = TsdbQueryStringCombineSimpleSortCheck(stmt, querySql, SortCheckMode);
    // 3*(5000+5000)
    AW_MACRO_ASSERT_EQ_INT(30000, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），order by多参数为先降序再增序 预期：查询成功
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_248)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 排序的时候需要将对应的聚合函数放到查询的第一项（.h头文件判断是否排序检查的第一项）
    char querySql[] = "SELECT name, time FROM testdb WHERE id >= 145001 and time <= 1695246999 order by name DESC, time ASC";
    int SortCheckMode = 5;
    ret = TsdbQueryStringCombineSimpleSortCheck(stmt, querySql, SortCheckMode);
    // 3*(55000+5000)
    AW_MACRO_ASSERT_EQ_INT(180000, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），group by多参数查询与order by单参数组合查询 预期：查询成功
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_249)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 排序的时候需要将对应的聚合函数放到查询的第一项（.h头文件判断是否排序检查的第一项）
    char querySql[] = "SELECT name, time, sum(id), count(num1) FROM testdb WHERE id >= 5001 and time <= 1695146999 group by name, time order by name ASC";
    int SortCheckMode = 0;
    ret = TsdbQueryStringCombineSimpleSortCheck(stmt, querySql, SortCheckMode);
    // 1695147000-1695042000-5000
    AW_MACRO_ASSERT_EQ_INT(100000, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），group by多参数查询与order by多参数组合查询 预期：查询成功
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_250)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 排序的时候需要将对应的聚合函数放到查询的第一项（.h头文件判断是否排序检查的第一项）
    char querySql[] = "SELECT name, max(id), num1 FROM testdb WHERE id >= 5001 and time <= 1695146999 group by name, num1"
    " order by name DESC, max(id) ASC";
    int SortCheckMode = 5;
    ret = TsdbQueryStringCombineSimpleSortCheck(stmt, querySql, SortCheckMode);
    // 1695147000-1695042000-5000
    AW_MACRO_ASSERT_EQ_INT(50, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），order by与多个聚合函数组合查询后limit值比查询到数据量少 预期：查询成功
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_251)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 排序的时候需要将对应的聚合函数放到查询的第一项（.h头文件判断是否排序检查的第一项）
    char querySql[] = "SELECT name, COUNT(id), num2 FROM testdb WHERE id >= 5001 and time <= 1695146999 group by num2, name"
    " order by name DESC, COUNT(id) ASC limit 10";
    int SortCheckMode = 5;
    ret = TsdbQueryStringCombineSimpleSortCheck(stmt, querySql, SortCheckMode);
    AW_MACRO_ASSERT_EQ_INT(10, ret);
    DropCmTable(stmt, tabelName);
}

// 大量数据，压缩模式为fast（rapidlz），order by与多个聚合函数组合查询后limit值比查询到数据量多 预期：查询成功
TEST_F(TsdbQueryCombine, Timing_006_TsdbQueryCombine_252)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateAndInsert_LostOfData(stmt, tabelName, compressModeRapidlz);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 排序的时候需要将对应的聚合函数放到查询的第一项（.h头文件判断是否排序检查的第一项）
    char querySql[] = "SELECT name, SUM(time), num2 FROM testdb WHERE id >= 5001 and time <= 1695146999 group by num2, name"
    " order by name DESC, SUM(time) ASC limit 150000;";
    int SortCheckMode = 5;
    ret = TsdbQueryStringCombineSimpleSortCheck(stmt, querySql, SortCheckMode);
    // 31695147000-1695042000-5000
    AW_MACRO_ASSERT_EQ_INT(30, ret);
    DropCmTable(stmt, tabelName);
}
