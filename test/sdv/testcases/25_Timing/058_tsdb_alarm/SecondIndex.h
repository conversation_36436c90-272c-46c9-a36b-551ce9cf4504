#ifndef _SECONDINDEX_H
#define _SECONDINDEX_H


#include <stdint.h>
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <pthread.h>
#include <semaphore.h>
#include <errno.h>
#include <time.h>
#include "t_datacom_lite.h"

#ifdef __cplusplus
extern "C" {
#endif

void set_IndexKeyValue_F7(GmcStmtT *stmt, uint64_t value)
{
    int ret = 0;
    uint64_t f7_value = value;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &f7_value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void set_IndexKeyValue_lpm4(GmcStmtT *stmt, uint32_t vrid, uint32_t vrfid, uint32_t ipaddr, uint8_t mask_len)
{
    int ret = 0;
    uint32_t t_vrid = vrid;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &t_vrid, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t t_vrfid = vrfid;
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &t_vrfid, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t t_ipaddr = ipaddr;
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &t_ipaddr, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t t_mask_len = mask_len;
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &t_mask_len, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void set_IndexKeyValue_lpm6(GmcStmtT *stmt, uint32_t vrid, uint32_t vrfid, uint8_t *ipaddr, uint8_t mask_len)
{
    int ret = 0;
    uint32_t t_vrid = vrid;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &t_vrid, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t t_vrfid = vrfid;
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &t_vrfid, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, ipaddr, 16);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t t_mask_len = mask_len;
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &t_mask_len, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void set_VertexProperty_F7(GmcStmtT *stmt, uint64_t i)
{
    int ret = 0;
    uint64_t f7_value = i;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT64, &f7_value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void set_VertexProperty_lpm4(GmcStmtT *stmt, uint32_t vrid, uint32_t vrfid, uint32_t ipaddr, uint8_t mask_len)
{
    int ret = 0;
    uint32_t t_vrid = vrid;
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &t_vrid, sizeof(t_vrid));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t t_vrfid = vrfid;
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &t_vrfid, sizeof(t_vrfid));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t t_ipaddr = ipaddr;
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &t_ipaddr, sizeof(t_ipaddr));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t t_mask_len = mask_len;
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &t_mask_len, sizeof(t_mask_len));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void set_VertexProperty_lpm6(GmcStmtT *stmt, uint32_t vrid, uint32_t vrfid, uint8_t *ipaddr, uint8_t mask_len)
{
    int ret = 0;
    uint32_t t_vrid = vrid;
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &t_vrid, sizeof(t_vrid));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t t_vrfid = vrfid;
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &t_vrfid, sizeof(t_vrfid));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, ipaddr, 16);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t t_mask_len = mask_len;
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &t_mask_len, sizeof(t_mask_len));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void set_VertexProperty_localhash(GmcStmtT *stmt, uint64_t i)
{
    int ret = 0;
    int8_t f2_value = i;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &f2_value, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int16_t f4_value = i + 2;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void set_VertexProperty_hashcluster(GmcStmtT *stmt, uint64_t i, uint64_t *local = NULL)
{
    int ret = 0;
    uint8_t f3_value = i;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &f3_value, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint16_t f5_value = i + 2;
    if (local)
        f5_value = *local;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void set_VertexProperty(GmcStmtT *stmt, uint64_t i, bool bool_value, char *f14_value, bool isupdate = false)
{
    int ret = 0;
    char f0_value = i;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &f0_value, sizeof(char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    unsigned char f1_value = i + 1;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &f1_value, sizeof(unsigned char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t f6_value = i + 2;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &f6_value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool f8_value = bool_value;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t f9_value = i + 3;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &f9_value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t f10_value = i + 4;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &f10_value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    float f11_value = i + 5;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &f11_value, sizeof(float));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    double f12_value = i + 6;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &f12_value, sizeof(double));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t f13_value = i + 7;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &f13_value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t f17_value = (i + 8) % 255;  // 2^8
    ret = GmcSetVertexProperty(stmt, "F17", GMC_DATATYPE_BITFIELD8, &f17_value, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint16_t f18_value = (i + 9) % 4095;  // 2^12
    ret = GmcSetVertexProperty(stmt, "F18", GMC_DATATYPE_BITFIELD16, &f18_value, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t f19_value = (i + 10) % 268435455;  // 2^28
    ret = GmcSetVertexProperty(stmt, "F19", GMC_DATATYPE_BITFIELD32, &f19_value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t f20_value = i + 11;  // 2^58
    ret = GmcSetVertexProperty(stmt, "F20", GMC_DATATYPE_BITFIELD64, &f20_value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

}
int get_VertexProperty(GmcStmtT *stmt, uint64_t i, bool bool_value, char *f14_value, uint64_t *f7_value = NULL,
    uint64_t *localhash = NULL, uint64_t *hashcluster = NULL, uint64_t *local = NULL)
{
    int ret = 0;
    bool isNull = false;

    char f0_value, f0_insert = i;
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F0", &f0_value, sizeof(f0_value), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_CHAR, &f0_insert, &f0_value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    unsigned char f1_value, f1_insert = i + 1;
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F1", &f1_value, sizeof(f1_value), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UCHAR, &f1_insert, &f1_value);
    if (ret != 0) {
        printf("f1_insert %d f1_value %d \n", f1_insert, f1_value);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t f6_value, f6_insert = i + 2;
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F6", &f6_value, sizeof(f6_value), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &f6_insert, &f6_value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool f8_value, f8_insert = bool_value;
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F8", &f8_value, sizeof(f8_value), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BOOL, &f8_insert, &f8_value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int64_t f9_value, f9_insert = i + 3;
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F9", &f9_value, sizeof(f9_value), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &f9_insert, &f9_value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t f10_value, f10_insert = i + 4;
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F10", &f10_value, sizeof(f10_value), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &f10_insert, &f10_value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    float f11_value, f11_insert = i + 5;
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F11", &f11_value, sizeof(f11_value), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FLOAT, &f11_insert, &f11_value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    double f12_value, f12_insert = i + 6;
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F12", &f12_value, sizeof(f12_value), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_DOUBLE, &f12_insert, &f12_value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t f13_value, f13_insert = i + 7;
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F13", &f13_value, sizeof(f13_value), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_TIME, &f13_insert, &f13_value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char f14_temp[strlen(f14_value) + 1];
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F14", &f14_temp, strlen(f14_value) + 1, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_STRING, f14_value, f14_temp, strlen(f14_value) + 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t f19_value, f19_insert = (i + 10) % 268435455;  // 2^28
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F19", &f19_value, sizeof(f19_value), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BITFIELD32, &f19_insert, &f19_value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t f20_value, f20_insert = i + 11;  // 2^58
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F20", &f20_value, sizeof(f20_value), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BITFIELD64, &f20_insert, &f20_value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (f7_value) {
        uint64_t f7_temp, f7_insert = *f7_value;
        ret = GmcGetVertexPropertyByName(stmt, (char *)"F7", &f7_temp, sizeof(f7_temp), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(false, isNull);
        ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &f7_insert, &f7_temp);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    if (localhash) {
        int8_t f2_value, f2_insert = *localhash;
        ret = GmcGetVertexPropertyByName(stmt, (char *)"F2", &f2_value, sizeof(f2_value), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(false, isNull);
        ret = CompareVertexPropertyValue(GMC_DATATYPE_INT8, &f2_insert, &f2_value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int16_t f4_value, f4_insert = *localhash + 2;
        ret = GmcGetVertexPropertyByName(stmt, (char *)"F4", &f4_value, sizeof(f4_value), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(false, isNull);
        ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &f4_insert, &f4_value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    if (hashcluster) {
        uint8_t f3_value, f3_insert = *hashcluster;
        ret = GmcGetVertexPropertyByName(stmt, (char *)"F3", &f3_value, sizeof(f3_value), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(false, isNull);
        ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &f3_insert, &f3_value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (local == NULL) {
            uint16_t f5_value, f5_insert = *hashcluster + 2;
            ret = GmcGetVertexPropertyByName(stmt, (char *)"F5", &f5_value, sizeof(f5_value), &isNull);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(false, isNull);
            ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &f5_insert, &f5_value);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    if (local) {
        uint16_t f5_value, f5_insert = *local;
        ret = GmcGetVertexPropertyByName(stmt, (char *)"F5", &f5_value, sizeof(f5_value), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(false, isNull);
        ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &f5_insert, &f5_value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}
bool get_VertexProperty_lpm4(GmcStmtT *stmt, uint32_t vrid, uint32_t vrfid, uint32_t ipaddr, uint8_t mask_len)
{
    int ret = 0;
    bool isNull = false;

    uint32_t t_vrid;
    ret = GmcGetVertexPropertyByName(stmt, (char *)"vr_id", &t_vrid, sizeof(t_vrid), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    if (!(vrid == t_vrid)) {
        printf("vrid :%ud, t_vrid :%ud\n", vrid, t_vrid);
        return false;
    }
    uint32_t t_vrfid;
    ret = GmcGetVertexPropertyByName(stmt, (char *)"vrf_index", &t_vrfid, sizeof(t_vrfid), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    if (!(vrfid == t_vrfid)) {
        printf("vrfid :%ud, t_vrfid :%ud\n", vrfid, t_vrfid);
        return false;
    }
    uint32_t t_ipaddr;
    ret = GmcGetVertexPropertyByName(stmt, (char *)"dest_ip_addr", &t_ipaddr, sizeof(t_ipaddr), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    if (!(ipaddr == t_ipaddr)) {
        printf("ipaddr :%ud, t_ipaddr :%ud\n", ipaddr, t_ipaddr);
        return false;
    }
    uint8_t t_mask_len = mask_len;
    ret = GmcGetVertexPropertyByName(stmt, (char *)"mask_len", &t_mask_len, sizeof(t_mask_len), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    if (!(mask_len == t_mask_len)) {
        printf("mask_len :%ud, t_mask_len :%ud\n", mask_len, t_mask_len);
        return false;
    }
    return true;
}
bool get_VertexProperty_lpm6(GmcStmtT *stmt, uint32_t vrid, uint32_t vrfid, uint8_t *ipaddr, uint8_t mask_len)
{
    int ret = 0;
    bool isNull = false;

    uint32_t t_vrid;
    ret = GmcGetVertexPropertyByName(stmt, (char *)"vr_id", &t_vrid, sizeof(t_vrid), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    if (!(vrid == t_vrid)) {
        printf("vrid :%ud, t_vrid :%ud\n", vrid, t_vrid);
        return false;
    }
    uint32_t t_vrfid;
    ret = GmcGetVertexPropertyByName(stmt, (char *)"vrf_index", &t_vrfid, sizeof(t_vrfid), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    if (!(vrfid == t_vrfid)) {
        printf("vrfid :%ud, t_vrfid :%ud\n", vrfid, t_vrfid);
        return false;
    }
    uint8_t t_ipaddr[16] = {0};
    ret = GmcGetVertexPropertyByName(stmt, (char *)"dest_ip_addr", t_ipaddr, 16, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    for (int i = 0; i < 16; i++) {
        if (ipaddr[i] != t_ipaddr[i]) {
            int j = 16;
            while (j-- > 0)
                printf("%3x ", ipaddr[j]);
            printf("<<--  ipaddr[%d] \n", i);
            j = 16;
            while (j-- > 0)
                printf("%3x ", t_ipaddr[j]);
            printf("<<--  t_ipaddr[%d] \n", i);
            return false;
        }
    }
    uint8_t t_mask_len = mask_len;
    ret = GmcGetVertexPropertyByName(stmt, (char *)"mask_len", &t_mask_len, sizeof(t_mask_len), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    if (!(mask_len == t_mask_len)) {
        printf("mask_len :%ud, t_mask_len :%ud\n", mask_len, t_mask_len);
        return false;
    }
    return true;
}

void WriteRecord_All(GmcStmtT *stmt, uint32_t vrid, uint32_t vrfid, void *ipaddr, uint8_t mask_len, uint64_t *f7,
    uint64_t *value, uint64_t *locahash = NULL, uint64_t *hashcluster = NULL, uint64_t *local = NULL,
    bool IsLpm6OrPartition = true)
{
    int ret = 0;
    uint64_t temp = 0;
    if (f7) {
        temp = *f7;
        set_VertexProperty_F7(stmt, temp);
    }
    if (value) {
        temp = *value;
        if ((ipaddr == NULL) && (IsLpm6OrPartition == false)) {
            set_VertexProperty(stmt, temp, 0, (char *)"string", true);
        } else {
            set_VertexProperty(stmt, temp, 0, (char *)"string", false);
        }
    }
    if (ipaddr) {
        if (IsLpm6OrPartition)
            set_VertexProperty_lpm6(stmt, vrid, vrfid, (uint8_t *)ipaddr, mask_len);
        else {
            uint32_t *ipv4_addr = (uint32_t *)ipaddr;
            set_VertexProperty_lpm4(stmt, vrid, vrfid, *ipv4_addr, mask_len);
        }
    }
    if (locahash) {
        temp = *locahash;
        set_VertexProperty_localhash(stmt, temp);
    }
    if (hashcluster) {
        uint8_t f3_value = *hashcluster;
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &f3_value, sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if ((ipaddr) && (local == NULL)) {
            uint16_t f5_value = *hashcluster + 2;
            ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    if (local) {
        uint16_t f5_value = *local;
        ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

#ifdef __cplusplus
}
#endif

#endif
