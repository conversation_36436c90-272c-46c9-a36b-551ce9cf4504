/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2025. All rights reserved.
 * Description: 【技术转交付】TSDB 测试
 * Author: qinjianhua
 * Create: 2024-12-09
 */

#include "gtest/gtest.h"
#include "t_rd_common.h"
#include "../../common/include/component/t_rd_ts.h"

char tabelName[] = "testdb";

static GmcConnOptionsT *connOptions = NULL;
static GmcConnT *conn = NULL;
static GmcStmtT *stmt = NULL;
Status ret = 0;

char *dir = getenv("GMDB_HOME");

bool isDataCsvExist()
{
    if (FILE *file = fopen("data.csv", "r")) {
        fclose(file);
        return true;
    }
    return false;
}

void GetCsvFileContent(char returnValue[])
{
    FILE *file = fopen("data.csv", "r");
    char row[128];
    while (fgets(row, 128, file) != NULL) {
        strcat(returnValue, row);
    }
    fclose(file);
}

#define COUNT 10000
typedef struct TagC2Int8C4Str {
    int64_t *id;
    int64_t *time;
    char (*name)[64];
    char (*ip)[33];
    char *ns[COUNT];
} C2Int8C4StrT;

int C2Int8C4StrSet(GmcStmtT *stmt, void *t)
{
    int ret = 0;
    C2Int8C4StrT *objT = (C2Int8C4StrT *)t;
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, objT->id, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, objT->time, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, objT->name, sizeof(objT->name[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_FIXED, objT->ip, sizeof(objT->ip[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_STRING, objT->ns, sizeof(objT->ns[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}
typedef int (*FuncWrite)(GmcStmtT *stmt, void *t);
template <typename StructObjT>
int writeRecordTs(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, StructObjT *obj, int objLen, FuncWrite func)
{
    int ret = 0;

    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &objLen, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = func(stmt, obj);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

int DropCmTable(char *tableName)
{
    int ret = 0;
    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    return ret;
}

void ExecQueryCmd(GmcStmtT *stmt, const char *queryCommand)
{
    assert(queryCommand != NULL);
    Status ret = GmcExecDirect(stmt, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
}

class tsdb_high_base : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        InitTsCiCfg();
        system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableVectorizedPushDown=1\"");  // 下推开启
        system("sh $TEST_HOME/tools/stop.sh -ts");
        TsDefulatDbFileClean();
        system("sh $TEST_HOME/tools/start.sh -ts");
        ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableVectorizedPushDown=0\"");  // 下推关闭
        testEnvClean();
        RecoverTsCiCfg();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void tsdb_high_base::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    YangConnOptionT tsConnOptions = {0};
    tsConnOptions.isCsMode = true;
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = DropCmTable("testdb0");

    char g_sqlCmd[512] = {0};

    (void)snprintf(g_sqlCmd, sizeof(g_sqlCmd),
        "create table %s(id integer, time integer, name char(64), ip inet, ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0', disk_limit = '100 MB',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)', table_path = '/data/gmdb/');",
        "testdb0");

    ret = GmcExecDirect(stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    constexpr int64_t count = 20;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    char ip[count][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc", "lucy",
        "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};

    uint32_t data_num = 1;
    char *ns[count] = {0};
    for (int j = 0; j < data_num; j++) {
        for (int i = 0; i < count; i++) {
            id[i] = i + (count * j);
            time[i] = 1704067200 + i;
            ns[i] = name[i];
        }
        // insert 数据
        // 读输入表的数据进行校验
        C2Int8C4StrT obj1 = {.id = id, .time = time, .name = name, .ip = ip};
        for (int i = 0; i < count; i++) {
            obj1.ns[i] = ns[i];
        }
        // 插入数据
        ret = writeRecordTs(conn, stmt, "testdb0", &obj1, count, C2Int8C4StrSet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

void tsdb_high_base::TearDown()
{
    ret = DropCmTable("testdb0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcConnOptionsDestroy(connOptions);
    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
}

// 高基数聚合场景对bool类型group by操作
TEST_F(tsdb_high_base, Timing_031_001_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 查询
    char g_command[512];
    (void)snprintf(g_command, sizeof(g_command), "gmsysview -sql \"select colset(ip)  from %s group by ip \" -s %s",
        "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "Can not exec sysview sql, ret = 1003000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_FEATURE_NOT_SUPPORTED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 高基数聚合场景对text类型group by操作
TEST_F(tsdb_high_base, Timing_031_001_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 查询
    char g_command[512];
    (void)snprintf(g_command, sizeof(g_command), "gmsysview -sql \"select ns  from %s group by ip \" -s %s", "testdb0",
        g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "index = 16", "first(ns):");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));

    // 通过gmsysview -explain查询是否执行高基数优化
    (void)snprintf(g_command, sizeof(g_command), "gmsysview -explain \"select ns  from %s group by ip \" -s %s",
        "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "HashAggregate");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 高基数聚合场景投影列 a，a是常量
TEST_F(tsdb_high_base, Timing_031_001_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 查询
    char g_command[512];
    (void)snprintf(g_command, sizeof(g_command), "gmsysview -sql \"select 1  from %s group by ip \" -s %s", "testdb0",
        g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "index = 16", "1:");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));

    // 通过gmsysview -explain查询是否执行高基数优化
    (void)snprintf(g_command, sizeof(g_command), "gmsysview -explain \"select 1  from %s group by ip \" -s %s",
        "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "HashAggregate");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 高基数聚合场景投影列 b，b是表达式，如sum（id+id）
TEST_F(tsdb_high_base, Timing_031_001_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 查询
    char g_command[512];
    (void)snprintf(g_command, sizeof(g_command), "gmsysview -sql \"select count(id+1) from %s group by ip \" -s %s",
        "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "index = 16", "count(id+1):");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));

    (void)snprintf(g_command, sizeof(g_command), "gmsysview -sql \"select sum(id+1) from %s group by ip \" -s %s",
        "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "index = 16", "sum(id+1):");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));

    (void)snprintf(g_command, sizeof(g_command), "gmsysview -sql \"select first(id+1) from %s group by ip \" -s %s",
        "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command,
        "Can not exec sysview sql, ret = 1003000");  //【时序交付】TSDB语法增强新增约束，只支持count和sum函数有表达式
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));

    (void)snprintf(g_command, sizeof(g_command), "gmsysview -sql \"select last(id+1) from %s group by ip \" -s %s",
        "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "Can not exec sysview sql, ret = 1003000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));

    // 通过gmsysview -explain查询是否执行高基数优化 DTS2024121017621
    (void)snprintf(g_command, sizeof(g_command), "gmsysview -explain \"select count(id+1) from %s group by ip \" -s %s",
        "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "HashAggregate");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));

    // 通过gmsysview -explain查询是否执行高基数优化
    (void)snprintf(g_command, sizeof(g_command), "gmsysview -explain \"select sum(id+1) from %s group by ip \" -s %s",
        "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "HashAggregate");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));

    // 通过gmsysview -explain查询是否执行高基数优化
    (void)snprintf(g_command, sizeof(g_command), "gmsysview -explain \"select first(id+1) from %s group by ip \" -s %s",
        "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "Can not exec sysview sql, ret = 1003000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));

    // 通过gmsysview -explain查询是否执行高基数优化
    (void)snprintf(g_command, sizeof(g_command), "gmsysview -explain \"select last(id+1) from %s group by ip \" -s %s",
        "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "Can not exec sysview sql, ret = 1003000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_FEATURE_NOT_SUPPORTED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 高基数聚合场景group by a，b
TEST_F(tsdb_high_base, Timing_031_001_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 查询
    char g_command[512];
    (void)snprintf(g_command, sizeof(g_command), "gmsysview -sql \"select id  from %s group by ip ,id \" -s %s",
        "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "index = 19", "id:");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));

    // 通过gmsysview -explain查询是否执行高基数优化
    (void)snprintf(g_command, sizeof(g_command), "gmsysview -explain \"select id  from %s group by ip ,id \" -s %s",
        "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "HashAggregate");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 高基数聚合场景对int类型colset操作
TEST_F(tsdb_high_base, Timing_031_001_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 查询
    char g_command[512];
    (void)snprintf(g_command, sizeof(g_command), "gmsysview -sql \"select colset(id)  from %s group by ip \" -s %s",
        "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "index = 16", "colset(id):");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));

    // 通过gmsysview -explain查询是否执行高基数优化
    (void)snprintf(g_command, sizeof(g_command), "gmsysview -explain \"select colset(id)  from %s group by ip \" -s %s",
        "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "HashAggregate");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 高基数聚合场景对int类型length操作
TEST_F(tsdb_high_base, Timing_031_001_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 查询
    char g_command[512];
    (void)snprintf(g_command, sizeof(g_command), "gmsysview -sql \"select length(id)  from %s group by ip \" -s %s",
        "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "index = 16", "length(id):");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));

    // 通过gmsysview -explain查询是否执行高基数优化
    (void)snprintf(g_command, sizeof(g_command), "gmsysview -explain \"select length(id)  from %s group by ip \" -s %s",
        "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "HashAggregate");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 高基数聚合场景对ip类型colset操作
TEST_F(tsdb_high_base, Timing_031_001_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 查询
    char g_command[512];
    (void)snprintf(g_command, sizeof(g_command), "gmsysview -sql \"select colset(ip)  from %s group by ip \" -s %s",
        "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "Can not exec sysview sql, ret = 1003000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_FEATURE_NOT_SUPPORTED);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 高基数聚合场景对ip类型length操作
TEST_F(tsdb_high_base, Timing_031_001_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 查询
    char g_command[512];
    (void)snprintf(g_command, sizeof(g_command), "gmsysview -sql \"select length(ip)  from %s group by ip \" -s %s",
        "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "Can not exec sysview sql, ret = 1003000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_FEATURE_NOT_SUPPORTED);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 高基数聚合场景对ip类型sum操作
TEST_F(tsdb_high_base, Timing_031_001_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 查询
    char g_command[512];
    (void)snprintf(g_command, sizeof(g_command), "gmsysview -sql \"select sum(ip)  from %s group by ip \" -s %s",
        "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "Can not exec sysview sql, ret = 1003000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_FEATURE_NOT_SUPPORTED);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 高基数聚合场景对ip类型max、min操作
TEST_F(tsdb_high_base, Timing_031_001_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 查询
    char g_command[512];
    (void)snprintf(g_command, sizeof(g_command), "gmsysview -sql \"select max(ip)  from %s group by ip \" -s %s",
        "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "Can not exec sysview sql, ret = 1003000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));

    (void)snprintf(g_command, sizeof(g_command), "gmsysview -sql \"select min(ip)  from %s group by ip \" -s %s",
        "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "Can not exec sysview sql, ret = 1003000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_FEATURE_NOT_SUPPORTED);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 高基数聚合场景对ip类型first、last操作
TEST_F(tsdb_high_base, Timing_031_001_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 查询
    char g_command[512];
    (void)snprintf(g_command, sizeof(g_command),
        "gmsysview -sql \"select first(ip),last(ip)  from %s group by ip \" -s %s", "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "index = 16", "first(ip):", "last(ip):");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));

    // 通过gmsysview -explain查询是否执行高基数优化
    (void)snprintf(g_command, sizeof(g_command),
        "gmsysview -explain \"select first(ip),last(ip)  from %s group by ip \" -s %s", "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "HashAggregate");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 高基数聚合场景对ip类型隐式first操作
TEST_F(tsdb_high_base, Timing_031_001_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 查询
    char g_command[512];
    (void)snprintf(g_command, sizeof(g_command), "gmsysview -sql \"select ip  from %s group by id \" -s %s", "testdb0",
        g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "index = 19", "first(ip):");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));

    // 通过gmsysview -explain查询是否执行高基数优化
    (void)snprintf(g_command, sizeof(g_command), "gmsysview -explain \"select ip  from %s group by id \" -s %s",
        "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "HashAggregate");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 高基数聚合场景对ip类型count(ip)操作
TEST_F(tsdb_high_base, Timing_031_001_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 查询
    char g_command[512];
    (void)snprintf(g_command, sizeof(g_command), "gmsysview -sql \"select count(ip)  from %s group by ip \" -s %s",
        "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "index = 16", "count(ip):");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));

    // 通过gmsysview -explain查询是否执行高基数优化
    (void)snprintf(g_command, sizeof(g_command), "gmsysview -explain \"select count(ip) from %s group by ip \" -s %s",
        "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "HashAggregate");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 高基数聚合场景对ip类型count(*)，group by int 操作
TEST_F(tsdb_high_base, Timing_031_001_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 查询
    char g_command[512];
    (void)snprintf(g_command, sizeof(g_command), "gmsysview -sql \"select count(*)  from %s group by ip \" -s %s",
        "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "Can not exec sysview sql, ret = 1003000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_FEATURE_NOT_SUPPORTED);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 高基数聚合场景对int类型sum操作
TEST_F(tsdb_high_base, Timing_031_001_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 查询
    char g_command[512];
    (void)snprintf(g_command, sizeof(g_command), "gmsysview -sql \"select sum(id)  from %s group by ip \" -s %s",
        "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "index = 16", "sum(id):");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));

    // 通过gmsysview -explain查询是否执行高基数优化
    (void)snprintf(g_command, sizeof(g_command), "gmsysview -explain \"select sum(id)  from %s group by ip \" -s %s",
        "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "HashAggregate");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 高基数聚合场景对int类型max、min操作
TEST_F(tsdb_high_base, Timing_031_001_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 查询
    char g_command[512];
    (void)snprintf(g_command, sizeof(g_command),
        "gmsysview -sql \"select max(id),min(id)  from %s group by ip \" -s %s", "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "index = 16", "max(id):", "min(id):");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));

    // 通过gmsysview -explain查询是否执行高基数优化
    (void)snprintf(g_command, sizeof(g_command),
        "gmsysview -explain \"select max(id),min(id)  from %s group by ip \" -s %s", "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "HashAggregate");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 高基数聚合场景对int类型first、last操作
TEST_F(tsdb_high_base, Timing_031_001_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 查询
    char g_command[512];
    (void)snprintf(g_command, sizeof(g_command),
        "gmsysview -sql \"select first(id),last(id)  from %s group by ip \" -s %s", "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "index = 16", "first(id):", "last(id):");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));

    // 通过gmsysview -explain查询是否执行高基数优化
    (void)snprintf(g_command, sizeof(g_command),
        "gmsysview -explain \"select first(id),last(id)   from %s group by ip \" -s %s", "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "HashAggregate");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 高基数聚合场景对int类型隐式first、隐式last操作
TEST_F(tsdb_high_base, Timing_031_001_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 查询
    char g_command[512];
    (void)snprintf(g_command, sizeof(g_command), "gmsysview -sql \"select id  from %s group by ip \" -s %s", "testdb0",
        g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "index = 16", "first(id):");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));

    // 通过gmsysview -explain查询是否执行高基数优化
    (void)snprintf(g_command, sizeof(g_command), "gmsysview -explain \"select id  from %s group by ip \" -s %s",
        "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "HashAggregate");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 高基数聚合场景对int类型count(ip)操作
TEST_F(tsdb_high_base, Timing_031_001_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 查询
    char g_command[512];
    (void)snprintf(g_command, sizeof(g_command), "gmsysview -sql \"select count(ip)  from %s group by id \" -s %s",
        "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "index = 19", "count(ip):");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));

    // 通过gmsysview -explain查询是否执行高基数优化
    (void)snprintf(g_command, sizeof(g_command), "gmsysview -explain \"select count(ip)  from %s group by id \" -s %s",
        "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "HashAggregate");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 高基数聚合场景对int类型count(*)操作
TEST_F(tsdb_high_base, Timing_031_001_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 查询
    char g_command[512];
    (void)snprintf(g_command, sizeof(g_command), "gmsysview -sql \"select count(*)  from %s group by id \" -s %s",
        "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "Can not exec sysview sql, ret = 1003000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_FEATURE_NOT_SUPPORTED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 高基数聚合场景条件组合where time >100 group by ip order by sum（a+a）limit 10
TEST_F(tsdb_high_base, Timing_031_001_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 查询
    char g_command[512];
    (void)snprintf(g_command, sizeof(g_command),
        "gmsysview -sql \"select id  from %s group by ip order by sum(id+id) limit 10 \" -s %s", "testdb0",
        g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "index = 9", "first(id):");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));

    // 通过gmsysview -explain查询是否执行高基数优化
    (void)snprintf(g_command, sizeof(g_command),
        "gmsysview -explain \"select id  from %s group by ip order by sum(id+id) limit 10  \" -s %s", "testdb0",
        g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "HashAggregate");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 高基数聚合场景copy to操作，copy (select count(id) from testdb0 group by ip) to './a.csv';
TEST_F(tsdb_high_base, Timing_031_001_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char ddlCommand[512];
    sprintf(ddlCommand,
        "COPY (SELECT count(id) FROM testdb0 group by ip) TO"
        "'%s/test/sdv/testcases/25_Timing/031_tsdb_blob/data.csv';",
        dir);

    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 高基数聚合场景条件组合where time >100 group by ip order by count(id) limit 10
TEST_F(tsdb_high_base, Timing_031_001_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 查询
    char g_command[512];
    (void)snprintf(g_command, sizeof(g_command),
        "gmsysview -sql \"select id  from %s group by ip order by count(id) limit 10 \" -s %s", "testdb0",
        g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "index = 9", "first(id)");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));

    // 通过gmsysview -explain查询是否执行高基数优化
    (void)snprintf(g_command, sizeof(g_command),
        "gmsysview -explain \"select id  from %s group by ip order by count(id) limit 10  \" -s %s", "testdb0",
        g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "HashAggregate");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 高基数聚合场景对ip类型操作，ip列为空值
TEST_F(tsdb_high_base, Timing_031_001_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = DropCmTable("testdb0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char g_sqlCmd[512] = {0};

    (void)snprintf(g_sqlCmd, sizeof(g_sqlCmd),
        "create table %s(id integer, time integer, name char(64), ip inet, ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0', disk_limit = '100 MB',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)', table_path = '/data/gmdb/');",
        "testdb0");

    ret = GmcExecDirect(stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 查询
    char g_command[512];
    (void)snprintf(g_command, sizeof(g_command), "gmsysview -sql \"select ip  from %s group by ip \" -s %s", "testdb0",
        g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));

    // 通过gmsysview -explain查询是否执行高基数优化
    (void)snprintf(g_command, sizeof(g_command), "gmsysview -explain \"select ip  from %s group by ip \" -s %s",
        "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "HashAggregate");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 高基数聚合场景对ip类型操作，int列为空值
TEST_F(tsdb_high_base, Timing_031_001_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = DropCmTable("testdb0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char g_sqlCmd[512] = {0};

    (void)snprintf(g_sqlCmd, sizeof(g_sqlCmd),
        "create table %s(id integer, time integer, name char(64), ip inet,  ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0', disk_limit = '100 MB',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)', table_path = '/data/gmdb/');",
        "testdb0");

    ret = GmcExecDirect(stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 查询
    char g_command[512];
    (void)snprintf(g_command, sizeof(g_command), "gmsysview -sql \"select id  from %s group by id \" -s %s", "testdb0",
        g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));

    // 通过gmsysview -explain查询是否执行高基数优化
    (void)snprintf(g_command, sizeof(g_command), "gmsysview -explain \"select id  from %s group by id \" -s %s",
        "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "HashAggregate");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 高基数聚合场景对ip类型count(ip)操作，group by ip，ip列值全相同，数据量10w
TEST_F(tsdb_high_base, Timing_031_001_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = DropCmTable("testdb0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char g_sqlCmd[512] = {0};

    (void)snprintf(g_sqlCmd, sizeof(g_sqlCmd),
        "create table %s(id integer, time integer, name char(64), ip inet,  ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0', disk_limit = '100 MB',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)', table_path = '/data/gmdb/');",
        "testdb0");

    ret = GmcExecDirect(stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    constexpr int64_t count = 20;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    char ip[count][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc", "lucy",
        "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};

    uint32_t data_num = 5000;
    char *ns[count] = {0};
    for (int j = 0; j < data_num; j++) {
        for (int i = 0; i < count; i++) {
            id[i] = i + (count * j);
            time[i] = 1704067200 + i;
            ns[i] = name[i];
        }
        // insert 数据
        // 读输入表的数据进行校验
        C2Int8C4StrT obj1 = {.id = id, .time = time, .name = name, .ip = ip};
        for (int i = 0; i < count; i++) {
            obj1.ns[i] = ns[i];
        }
        // 插入数据
        ret = writeRecordTs(conn, stmt, "testdb0", &obj1, count, C2Int8C4StrSet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 查询
    char g_command[512];
    (void)snprintf(g_command, sizeof(g_command), "gmsysview -sql \"select count(ip)  from %s group by ip \" -s %s",
        "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "index = 16", "count(ip)");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));

    // 通过gmsysview -explain查询是否执行高基数优化
    (void)snprintf(g_command, sizeof(g_command), "gmsysview -explain \"select count(ip)  from %s group by ip  \" -s %s",
        "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "HashAggregate");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 高基数聚合场景对int类型count(ip)操作，group by id，int列值全相同，数据量10w
TEST_F(tsdb_high_base, Timing_031_001_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = DropCmTable("testdb0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char g_sqlCmd[512] = {0};

    (void)snprintf(g_sqlCmd, sizeof(g_sqlCmd),
        "create table %s(id integer, time integer, name char(64), ip inet,  ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0', disk_limit = '100 MB',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)', table_path = '/data/gmdb/');",
        "testdb0");

    ret = GmcExecDirect(stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    constexpr int64_t count = 20;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    char ip[count][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc", "lucy",
        "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};

    uint32_t data_num = 5000;
    char *ns[count] = {0};
    for (int j = 0; j < data_num; j++) {
        for (int i = 0; i < count; i++) {
            id[i] = 1;
            time[i] = 1704067200 + i;
            ns[i] = name[i];
        }
        // insert 数据
        // 读输入表的数据进行校验
        C2Int8C4StrT obj1 = {.id = id, .time = time, .name = name, .ip = ip};
        for (int i = 0; i < count; i++) {
            obj1.ns[i] = ns[i];
        }
        // 插入数据
        ret = writeRecordTs(conn, stmt, "testdb0", &obj1, count, C2Int8C4StrSet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 查询
    char g_command[512];
    (void)snprintf(g_command, sizeof(g_command), "gmsysview -sql \"select count(ip)  from %s group by id \" -s %s",
        "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "index = 0", "count(ip): 100000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));

    // 通过gmsysview -explain查询是否执行高基数优化
    (void)snprintf(g_command, sizeof(g_command), "gmsysview -explain \"select count(ip)  from %s group by id  \" -s %s",
        "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "HashAggregate");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 高基数聚合场景对ip类型select max(1),min(1)操作，group by ip，ip列值全相同，数据量10w
TEST_F(tsdb_high_base, Timing_031_001_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = DropCmTable("testdb0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char g_sqlCmd[512] = {0};

    (void)snprintf(g_sqlCmd, sizeof(g_sqlCmd),
        "create table %s(id integer, time integer, name char(64), ip inet, ns text)"
        " with (time_col = 'time', interval = '1 hour', cache_size = '0', disk_limit = '100 MB',"
        " sensitive_col = 'name', compression = 'fast(rapidlz)', table_path = '/data/gmdb/');",
        "testdb0");

    ret = GmcExecDirect(stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    constexpr int64_t count = 20;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    char ip[count][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111",
        "33333333", "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc", "lucy",
        "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};

    uint32_t data_num = 5000;
    char *ns[count] = {0};
    for (int j = 0; j < data_num; j++) {
        for (int i = 0; i < count; i++) {
            id[i] = i + (count * j);
            time[i] = 1704067200 + i;
            ns[i] = name[i];
        }
        // insert 数据
        // 读输入表的数据进行校验
        C2Int8C4StrT obj1 = {.id = id, .time = time, .name = name, .ip = ip};
        for (int i = 0; i < count; i++) {
            obj1.ns[i] = ns[i];
        }
        // 插入数据
        ret = writeRecordTs(conn, stmt, "testdb0", &obj1, count, C2Int8C4StrSet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 查询
    char g_command[512];
    (void)snprintf(g_command, sizeof(g_command), "gmsysview -sql \"select max(1),min(1)  from %s group by ip \" -s %s",
        "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "index = 16", "max(1):", "min(1):");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));

    // 通过gmsysview -explain查询是否执行高基数优化
    (void)snprintf(g_command, sizeof(g_command),
        "gmsysview -explain \"select max(1),min(1) from %s group by ip  \" -s %s", "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "HashAggregate");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 高基数聚合场景系统视图验证 gmsysview -sql "select * from 'V\$DB_SERVER' group by ip"
TEST_F(tsdb_high_base, Timing_031_001_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char const *view_name = "V\\$DRT_CONN_STAT";
    // 查询
    char g_command[512];
    (void)snprintf(g_command, sizeof(g_command), "gmsysview -sql \"select * from '%s' group by id \" -s %s", view_name,
        g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "Can not exec sysview sql, ret = 1009011");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNDEFINE_COLUMN);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 高基数聚合场景多条件混合复杂场景，常量，变量 group by ip，ip非唯一
TEST_F(tsdb_high_base, Timing_031_001_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 查询
    char g_command[512];
    (void)snprintf(g_command, sizeof(g_command),
        "gmsysview -sql \"select time,first(2),first(time), "
        "max(1),min(1),id,sum(1),count(ip),max(id),min(id),first(id),last(id),ip  from %s where time>0 and "
        "id !=0 group by ip order by first(id),count(id) limit 10\" -s %s",
        "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "index = 9", "first(time):", "first(2):", "first(time):", "max(1):");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "min(1):", "first(id):", "sum(1):", "count(ip):", "max(id):");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "min(id):", "first(id):", "last(id):", "ip:");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));

    // 通过gmsysview -explain查询是否执行高基数优化
    (void)snprintf(g_command, sizeof(g_command),
        "gmsysview -explain \"select time,first(2),first(time), "
        "max(1),min(1),id,sum(1),count(ip),max(id),min(id),first(id),last(id),ip from %s where time>0 and "
        "id !=0 group by ip order by first(id),count(id) limit 10\" -s %s",
        "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "HashAggregate");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

int thr_count = 0;
void *thread_check_Data(void *args)
{

    int i = *(int *)args;

    // 查询
    char g_command[512];
    (void)snprintf(g_command, sizeof(g_command), "gmsysview -sql \"select id  from %s group by ip \" -s %s", "testdb0",
        g_connServerTsdb);
    system(g_command);
    int ret = executeCommand(g_command, "index = 16");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "first(id): 1", "first(id): 6", "first(id): 17");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));

    (void)snprintf(g_command, sizeof(g_command), "gmsysview -sql \"select ip  from %s group by ip \" -s %s", "testdb0",
        g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "index = 16");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));

    // 通过gmsysview -explain查询是否执行高基数优化
    (void)snprintf(g_command, sizeof(g_command), "gmsysview -explain \"select id  from %s group by ip \" -s %s",
        "testdb0", g_connServerTsdb);
    system(g_command);
    ret = executeCommand(g_command, "HashAggregate");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    thr_count++;
    return 0;
}

// 高基数聚合场景16线程并发读场景，数据量无关
TEST_F(tsdb_high_base, Timing_031_001_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret1 = 0;
    int thr_num = 16;
    pthread_t thr1;
    int index[thr_num];
    for (int k = 0; k < thr_num; k++) {
        index[k] = k;
        ret1 = pthread_create(&thr1, NULL, thread_check_Data, (void *)&index[k]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    for (int k = 0; k < thr_num; k++) {
        ret1 = pthread_join(thr1, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    while (thr_count < thr_num) {
        sleep(3);
        AW_FUN_Log(LOG_STEP, "thr_count=%d\n", thr_count);
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}
