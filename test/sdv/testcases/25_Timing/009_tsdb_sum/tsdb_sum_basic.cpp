/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2025. All rights reserved.
 * Description: 【技术转交付】TSDB SUM 测试
 * Author: jiangjincheng
 * Create: 2024-03-19
 */

#include "gtest/gtest.h"
#include "t_rd_common.h"
#include "../../common/include/component/t_rd_ts.h"

char tabelName[] = "testdb";
char tabelName_1[] = "testdb_1";
char compressModeFast[] = "fast";
char compressModeRapidlz[] = "fast(rapidlz)";
char compressModeZstar[] = "fast(zstar)";
char compressModeNo[] = "no";

static GmcConnOptionsT *connOptions = NULL;
static GmcConnT *conn = NULL;
static GmcStmtT *stmt = NULL;
Status ret = 0;
bool eof = false;
bool isNull = false;

typedef struct {
    char (*name)[10];
    int64_t *age;
    int64_t *id;
    int64_t *worktime;
    int64_t *salary;
    char (*str)[64];
    int64_t *worktime8;
    int64_t *worktime16;
    int64_t *worktime32;
    uint32_t rowNum;
} Data;

void CreateTableAndBulkInsert(GmcStmtT *stmt, Data data, const char *tableName)
{
    assert(tableName != NULL);
    int8_t worktimes8[] = {24, 24, 11, 11, 12, 11};
    int16_t worktimes16[] = {24, 24, 11, 11, 12, 11};
    int32_t worktimes32[] = {24, 24, 11, 11, 12, 11};
    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer, str "
        "char(64), worktime8 integer, worktime16 integer, worktime32 integer) "
        "with (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        tableName);
    Status ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data.rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data.id, sizeof(data.id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data.age, sizeof(data.age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, data.worktime, sizeof(data.worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, data.salary, sizeof(data.salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, data.name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_FIXED, data.str, 64, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 6, (GmcDataTypeE)DB_DATATYPE_INT8, worktimes8, sizeof(worktimes8), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 7, (GmcDataTypeE)DB_DATATYPE_INT16, worktimes16, sizeof(worktimes16), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 8, (GmcDataTypeE)DB_DATATYPE_INT32, worktimes32, sizeof(worktimes32), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
}
void ExecQueryCmd(GmcStmtT *stmt, const char *queryCommand)
{
    assert(queryCommand != NULL);
    Status ret = GmcExecDirect(stmt, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
}
int DropCmTable(char *tableName)
{
    int ret = 0;
    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    return ret;
}

class tsdb_sum_basic_enableVectorizedPushDown : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        InitTsCiCfg();
        ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        close_epoll_thread();
        testEnvClean();
        RecoverTsCiCfg();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void tsdb_sum_basic_enableVectorizedPushDown::SetUp()
{

    AW_CHECK_LOG_BEGIN();
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char names[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t ids[] = {1, 2, 4, 9, 8, 14};
    int64_t worktimes[] = {24, 24, 11, 11, 12, 11};
    int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000};
    char strs[][64] = {"str3", "str3", "str1", "str1", "str2", "str1"};
    uint32_t rowNum = 6;

    Data data = {.name = names,
        .age = ages,
        .id = ids,
        .worktime = worktimes,
        .salary = salaries,
        .str = strs,
        .worktime8 = worktimes,
        .worktime16 = worktimes,
        .worktime32 = worktimes,
        .rowNum = rowNum};

    CreateTableAndBulkInsert(stmt, data, "tablequery4");
}

void tsdb_sum_basic_enableVectorizedPushDown::TearDown()
{
    ret = DropCmTable("tablequery4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
}

class tsdb_sum_basic : public testing::Test {
protected:
    static void SetUpTestCase()
    {
// euler和仿真环境清共享内存，清理原有DB文件
#if defined RUN_INDEPENDENT
        system("sh $TEST_HOME/tools/stop.sh -f");
#endif
        system("sh $TEST_HOME/tools/stop.sh -ts");
        TsDefulatDbFileClean();
        system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableVectorizedPushDown=0\"");  // 下推关闭
        system("sh $TEST_HOME/tools/start.sh -ts");
        ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        close_epoll_thread();
        testEnvClean();
        RecoverTsCiCfg();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void tsdb_sum_basic::SetUp()
{
    AW_CHECK_LOG_BEGIN();

    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char names[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t ids[] = {1, 2, 4, 9, 8, 14};
    int64_t worktimes[] = {24, 24, 11, 11, 12, 11};
    int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000};
    char strs[][64] = {"str3", "str3", "str1", "str1", "str2", "str1"};
    uint32_t rowNum = 6;

    Data data = {.name = names,
        .age = ages,
        .id = ids,
        .worktime = worktimes,
        .salary = salaries,
        .str = strs,
        .worktime8 = worktimes,
        .worktime16 = worktimes,
        .worktime32 = worktimes,
        .rowNum = rowNum};

    CreateTableAndBulkInsert(stmt, data, "tablequery4");
}

void tsdb_sum_basic::TearDown()
{
    ret = DropCmTable("tablequery4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
}

// 001、group by a和b都是int8，sum(a+b)
TEST_F(tsdb_sum_basic, Timing_009_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    int64_t sumRes = 0;
    int64_t resultWorktime[] = {11, 12, 24};
    int64_t sum[] = {66, 24, 96};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select worktime8, sum(worktime8 + worktime8) from tablequery4 group by worktime8;";
    // expact result: 11   11
    //                11   11
    //                11   11   总 66
    //                12   12   总 24
    //                24   24
    //                24   24   总 96
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }

        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &sumRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);
        AW_MACRO_EXPECT_EQ_INT(sumRes, sum[i]);
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)3);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002、group by a和b都是int16，sum(a+b)
TEST_F(tsdb_sum_basic, Timing_009_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    int64_t sumRes = 0;
    int64_t resultWorktime[] = {11, 12, 24};
    int64_t sum[] = {66, 24, 96};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select worktime16, sum(worktime16 + worktime16) from tablequery4 group by worktime16;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }

        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &sumRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);
        AW_MACRO_EXPECT_EQ_INT(sumRes, sum[i]);
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)3);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003、group by a和b都是int32，sum(a+b)
TEST_F(tsdb_sum_basic, Timing_009_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    int64_t sumRes = 0;
    int64_t resultWorktime[] = {11, 12, 24};
    int64_t sum[] = {66, 24, 96};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select worktime32, sum(worktime32 + worktime32) from tablequery4 group by worktime32;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }

        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &sumRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);
        AW_MACRO_EXPECT_EQ_INT(sumRes, sum[i]);
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)3);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004、group by a和b都是int64，sum(a+b)
TEST_F(tsdb_sum_basic, Timing_009_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    int64_t sumRes = 0;
    int64_t resultWorktime[] = {11, 12, 24};
    int64_t sum[] = {44033, 31012, 30048};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select worktime, sum(worktime+ salary ) from tablequery4 group by worktime;";
    // expact result: 11   4000
    //                11   30000
    //                11   10000   总 44033
    //                12   31000   总 31012
    //                24   20000
    //                24   10000   总 30048
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }

        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &sumRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);
        AW_MACRO_EXPECT_EQ_INT(sumRes, sum[i]);
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)3);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005、group by后order by a和b都是int8，sum(a+b)
TEST_F(tsdb_sum_basic, Timing_009_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    int64_t sumRes = 0;
    int64_t resultWorktime[] = {11, 12, 24};
    int64_t sum[] = {66, 24, 96};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand =
        "select worktime8, sum(worktime8 + worktime8) from tablequery4 group by worktime8 order by worktime8;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }

        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &sumRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);
        AW_MACRO_EXPECT_EQ_INT(sumRes, sum[i]);
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)3);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006、group by后order by a和b都是int16，sum(a+b)
TEST_F(tsdb_sum_basic, Timing_009_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    int64_t sumRes = 0;
    int64_t resultWorktime[] = {11, 12, 24};
    int64_t sum[] = {66, 24, 96};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand =
        "select worktime16, sum(worktime16 + worktime16) from tablequery4 group by worktime16 order by worktime16;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }

        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &sumRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);
        AW_MACRO_EXPECT_EQ_INT(sumRes, sum[i]);
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)3);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007、group by后order by a和b都是int32，sum(a+b)
TEST_F(tsdb_sum_basic, Timing_009_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    int64_t sumRes = 0;
    int64_t resultWorktime[] = {11, 12, 24};
    int64_t sum[] = {66, 24, 96};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand =
        "select worktime32, sum(worktime32 + worktime32) from tablequery4 group by worktime32 order by worktime32;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }

        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &sumRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);
        AW_MACRO_EXPECT_EQ_INT(sumRes, sum[i]);
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)3);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008、group by后order by a和b都是int64，sum(a+b)
TEST_F(tsdb_sum_basic, Timing_009_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    int64_t sumRes = 0;
    int64_t resultWorktime[] = {11, 12, 24};
    int64_t sum[] = {44033, 31012, 30048};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand =
        "select worktime, sum(worktime+ salary ) from tablequery4 group by worktime order by worktime;";
    // expact result: 11   4000
    //                11   30000
    //                11   10000   总 44033
    //                12   31000   总 31012
    //                24   20000
    //                24   10000   总 30048
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }

        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &sumRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);
        AW_MACRO_EXPECT_EQ_INT(sumRes, sum[i]);
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)3);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009、group by a和b分别是不同int类型，如int8、int64， sum(a+b)
TEST_F(tsdb_sum_basic, Timing_009_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    int64_t sumRes = 0;
    int64_t resultWorktime[] = {11, 12, 24};
    int64_t sum[] = {44033, 31012, 30048};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand =
        "select worktime8, sum(worktime8+ salary ) from tablequery4 group by worktime8 order by worktime8;";
    // expact result: 11   4000
    //                11   30000
    //                11   10000   总 44033
    //                12   31000   总 31012
    //                24   20000
    //                24   10000   总 30048
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }

        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &sumRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);
        AW_MACRO_EXPECT_EQ_INT(sumRes, sum[i]);
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)3);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010、group by a和b都是int64，sum(a+a)
TEST_F(tsdb_sum_basic, Timing_009_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    int64_t sumRes = 0;
    int64_t resultWorktime[] = {11, 12, 24};
    int64_t sum[] = {66, 24, 96};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand =
        "select worktime, sum(worktime + worktime) from tablequery4 group by worktime order by worktime;";
    // expact result: 11   11
    //                11   11
    //                11   11   总 66
    //                12   12   总 24
    //                24   24
    //                24   24   总 96
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }

        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &sumRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);
        AW_MACRO_EXPECT_EQ_INT(sumRes, sum[i]);
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)3);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011、select, a, b, sum(a+b) group by a, b order by sum(a+b)
TEST_F(tsdb_sum_basic, Timing_009_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    int64_t sumRes = 0;
    int64_t salaryRes = 0;
    int64_t resultWorktime[] = {11, 11, 24, 24, 11, 12};
    int64_t sum[] = {4011, 10011, 10024, 20024, 30011, 31012};
    int64_t resultSalary[] = {4000, 10000, 10000, 20000, 30000, 31000};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select worktime, salary,sum(worktime + salary) from tablequery4 group by worktime, "
                               "salary order by sum(worktime + salary);";
    // expact result: 11   4000
    //                11   30000
    //                11   10000   总 44033
    //                12   31000   总 31012
    //                24   20000
    //                24   10000   总 30048
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }

        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &salaryRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &sumRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);
        AW_MACRO_EXPECT_EQ_INT(salaryRes, resultSalary[i]);
        AW_MACRO_EXPECT_EQ_INT(sumRes, sum[i]);
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)6);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012、select, a, b ，count(a) group by a, b order by sum(a+b)
TEST_F(tsdb_sum_basic, Timing_009_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    int64_t countRes = 0;
    int64_t salaryRes = 0;
    int64_t resultWorktime[] = {11, 11, 24, 24, 11, 12};
    int64_t count[] = {1, 1, 1, 1, 1, 1};
    int64_t resultSalary[] = {4000, 10000, 10000, 20000, 30000, 31000};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select worktime, salary,count(worktime) from tablequery4 group by worktime, "
                               "salary order by sum(worktime + salary);";
    // expact result: 11   4000
    //                11   30000
    //                11   10000   总 44033
    //                12   31000   总 31012
    //                24   20000
    //                24   10000   总 30048
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }

        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &salaryRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &countRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);
        AW_MACRO_EXPECT_EQ_INT(salaryRes, resultSalary[i]);
        AW_MACRO_EXPECT_EQ_INT(countRes, count[i]);
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)6);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013、group by a和b都是string，或其中有个是string， sum(a+b)
TEST_F(tsdb_sum_basic, Timing_009_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    int64_t sumRes = 0;
    int64_t resultWorktime[] = {11, 12, 24};
    int64_t sum[] = {44033, 31012, 30048};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select worktime, sum(worktime+ str ) from tablequery4 group by worktime;";
    // expact result: 11   4000
    //                11   30000
    //                11   10000   总 44033
    //                12   31000   总 31012
    //                24   20000
    //                24   10000   总 30048
    ret = GmcExecDirect(stmt, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_SEMANTIC_ERROR);

    queryCommand = "select worktime, sum(worktime+ str ) from tablequery4 group by worktime order by worktime;";
    // expact result: 11   4000
    //                11   30000
    //                11   10000   总 44033
    //                12   31000   总 31012
    //                24   20000
    //                24   10000   总 30048
    ret = GmcExecDirect(stmt, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_SEMANTIC_ERROR);

    queryCommand =
        "select worktime, sum(worktime+ str ) from tablequery4 group by worktime order by sum(worktime+ str );";
    // expact result: 11   4000
    //                11   30000
    //                11   10000   总 44033
    //                12   31000   总 31012
    //                24   20000
    //                24   10000   总 30048
    ret = GmcExecDirect(stmt, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_SEMANTIC_ERROR);

    queryCommand = "select worktime, count(worktime) from tablequery4 group by worktime order by sum(worktime+ str );";
    // expact result: 11   4000
    //                11   30000
    //                11   10000   总 44033
    //                12   31000   总 31012
    //                24   20000
    //                24   10000   总 30048
    ret = GmcExecDirect(stmt, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_SEMANTIC_ERROR);

    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_SEMANTIC_ERROR, GMERR_DATA_EXCEPTION);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014、大数据量，sum(a+b)
TEST_F(tsdb_sum_basic, Timing_009_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DropCmTable("tablequery4");

    constexpr int count = 10000;
    int64_t worktimes64[count];
    int64_t salaries[count];

    for (int i = 0; i < count; i++) {
        worktimes64[i] = i + 1;
        salaries[i] = i + 1;
    }
    int64_t worktimes[count] = {24, 24, 11, 11, 12, 11};
    char names[count][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[count] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t ids[count] = {1, 2, 4, 9, 8, 14};

    char strs[count][64] = {"str3", "str3", "str1", "str1", "str2", "str1"};
    uint32_t rowNum = count;

    Data data = {.name = names,
        .age = ages,
        .id = ids,
        .worktime = worktimes64,
        .salary = salaries,
        .str = strs,
        .worktime8 = worktimes,
        .worktime16 = worktimes,
        .worktime32 = worktimes,
        .rowNum = rowNum};

    int8_t worktimes8[count] = {24, 24, 11, 11, 12, 11};
    int16_t worktimes16[count] = {24, 24, 11, 11, 12, 11};
    int32_t worktimes32[count] = {24, 24, 11, 11, 12, 11};
    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer, str "
        "char(64), worktime8 integer, worktime16 integer, worktime32 integer) "
        "with (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery4");
    Status ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data.rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data.id, sizeof(data.id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data.age, sizeof(data.age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, data.worktime, sizeof(data.worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, data.salary, sizeof(data.salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, data.name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_FIXED, data.str, 64, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 6, (GmcDataTypeE)DB_DATATYPE_INT8, worktimes8, sizeof(worktimes8), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 7, (GmcDataTypeE)DB_DATATYPE_INT16, worktimes16, sizeof(worktimes16), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 8, (GmcDataTypeE)DB_DATATYPE_INT32, worktimes32, sizeof(worktimes32), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    int64_t sumRes = 0;
    int64_t resultWorktime[count];
    int64_t sum[count];

    for (int i = 0; i < count; i++) {
        resultWorktime[i] = i + 1;
        sum[i] = 2 * (i + 1);
    }

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand =
        "select worktime, sum(worktime+ salary ) from tablequery4 group by worktime order by worktime;";
    // expact result: 11   4000
    //                11   30000
    //                11   10000   总 44033
    //                12   31000   总 31012
    //                24   20000
    //                24   10000   总 30048
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &sumRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);
        AW_MACRO_EXPECT_EQ_INT(sumRes, sum[i]);
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, count);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015、查询的表不存在
TEST_F(tsdb_sum_basic, Timing_009_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DropCmTable("tablequery4");

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    int64_t sumRes = 0;
    int64_t resultWorktime[] = {11, 12, 24};
    int64_t sum[] = {44033, 31012, 30048};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand =
        "select worktime, sum(worktime+ salary ) from tablequery4 group by worktime order by worktime;";
    // expact result: 11   4000
    //                11   30000
    //                11   10000   总 44033
    //                12   31000   总 31012
    //                24   20000
    //                24   10000   总 30048
    ret = GmcExecDirect(stmt, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_UNDEFINED_TABLE);

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer, str "
        "char(64), worktime8 integer, worktime16 integer, worktime32 integer) "
        "with (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery4");
    Status ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_UNDEFINED_TABLE, GMERR_DATA_EXCEPTION);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016、表中无数据
TEST_F(tsdb_sum_basic, Timing_009_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    DropCmTable("tablequery4");
    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer, str "
        "char(64), worktime8 integer, worktime16 integer, worktime32 integer) "
        "with (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery4");
    Status ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    int64_t sumRes = 0;
    int64_t resultWorktime[] = {11, 12, 24};
    int64_t sum[] = {44033, 31012, 30048};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand =
        "select worktime, sum(worktime+ salary ) from tablequery4 group by worktime order by worktime;";
    // expact result: 11   4000
    //                11   30000
    //                11   10000   总 44033
    //                12   31000   总 31012
    //                24   20000
    //                24   10000   总 30048
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }

        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &sumRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);
        AW_MACRO_EXPECT_EQ_INT(sumRes, sum[i]);
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)0);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 017、group by a、b和c都是int64，sum(a+b+c)
TEST_F(tsdb_sum_basic, Timing_009_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    int64_t sumRes = 0;
    int64_t resultWorktime[] = {11, 12, 24};
    int64_t sum[] = {44033, 31012, 30048};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand =
        "select worktime, sum(worktime+ salary + id ) from tablequery4 group by worktime order by worktime;";
    // expact result: 11   4000
    //                11   30000
    //                11   10000   总 44033
    //                12   31000   总 31012
    //                24   20000
    //                24   10000   总 30048
    ret = GmcExecDirect(stmt, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_FEATURE_NOT_SUPPORTED);

    AW_ADD_ERRNUM_WHITE_LIST(3, GMERR_FEATURE_NOT_SUPPORTED, GMERR_INTERNAL_ERROR, GMERR_DATA_EXCEPTION);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018、group by a和b都是int64，sum(a+b)，值溢出
TEST_F(tsdb_sum_basic, Timing_009_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    int64_t sumRes = 0;
    int64_t resultWorktime[] = {11, 12, 24};
    int64_t sum[] = {44033, 31012, 30048};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand =
        "select worktime, sum(worktime + age ) from tablequery4 group by worktime order by worktime;";
    // expact result: 11   4000
    //                11   30000
    //                11   10000   总 44033
    //                12   31000   总 31012
    //                24   20000
    //                24   10000   总 30048
    ret = GmcExecDirect(stmt, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_FIELD_OVERFLOW);

    AW_ADD_ERRNUM_WHITE_LIST(3, GMERR_FIELD_OVERFLOW, GMERR_INTERNAL_ERROR, GMERR_DATA_EXCEPTION);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 019、下推开启，group by a和b都是int64，sum(a+b),不支持下推功能，未适配，此处sum(a+b)成功
TEST_F(tsdb_sum_basic_enableVectorizedPushDown, Timing_009_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    int64_t sumRes = 0;
    int64_t resultWorktime[] = {11, 12, 24};
    int64_t sum[] = {44033, 31012, 30048};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select worktime, sum(worktime+ salary ) from tablequery4 group by worktime;";
    // expact result: 11   4000
    //                11   30000
    //                11   10000   总 44033
    //                12   31000   总 31012
    //                24   20000
    //                24   10000   总 30048
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }

        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &sumRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);
        AW_MACRO_EXPECT_EQ_INT(sumRes, sum[i]);
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)3);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 020、下推开启，group by a和b都是int64，sum(a+a),不支持下推功能，未适配，此处sum(a+a)失败
TEST_F(tsdb_sum_basic_enableVectorizedPushDown, Timing_009_020)
{
    ret = 0;
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    int64_t sumRes = 0;
    int64_t resultWorktime[] = {11, 12, 24};
    int64_t sum[] = {66, 24, 96};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand =
        "select worktime, sum(worktime+ worktime ) from tablequery4 group by worktime order by worktime;";

    ret = GmcExecDirect(stmt, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);  // DTS2024052001907
    AW_FUN_Log(LOG_STEP, "test end.");
}
