/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 【交付增强】TSDB日志磁盘超规格处理
 * Author: chenbangjun
 * Create: 2024-05-10
 */
#include "gtest/gtest.h"
#include "t_rd_ts.h"

static GmcConnOptionsT *connOptions = NULL;
static GmcConnT *conn = NULL;
static GmcStmtT *stmt = NULL;
Status ret = 0;
char g_cStoreDir[64] = {0};
char tableName[] = "testdb";
char tableName2[] = "testdb1";

char g_fdClient[512] = "ls -l /proc/`pidof tsdb_disk_specifications_onlinechange`/fd |wc -l";
char g_fdServer[512] = "ls -l /proc/`pidof gmserver_ts`/fd |wc -l";
int32_t fdClientBefore = 0;
int32_t fdClientAfter = 0;
int32_t fdServerBefore = 0;
int32_t fdServerAfter = 0;

int32_t GetViewFieldResultValue(const char *viewName)
{
    // DTS2025051319092,开发建议等环境稳定后再进行获取
    sleep(4);
    int maxCmdSize = 256;
    char cmdOutput[maxCmdSize];
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));

    char command[maxCmdSize];
    (void)snprintf(command, maxCmdSize, "%s", viewName);

    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        printf("popen(%s) error./n", command);
    }
    EXPECT_NE((void *)NULL, pf);
    while (NULL != fgets(cmdOutput, maxCmdSize, pf)) {
    };
    pclose(pf);

    return atoi(cmdOutput);
}

class tsdbDiskOnlineChange : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        InitTsCiCfg();
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        // 创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        fdClientBefore = GetViewFieldResultValue(g_fdClient);
        fdServerBefore = GetViewFieldResultValue(g_fdServer);
        ret = TestTsGmcConnect(&conn, &stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        GmcConnOptionsDestroy(connOptions);
        GmcFreeStmt(stmt);
        GmcDisconnect(conn);
        fdClientAfter = GetViewFieldResultValue(g_fdClient);
        // 开始写日志的时机在增量编译和非增量编译下不一样，增量编译下会多一个日志fd，冕泓建议按照两张差值小于1校验
        EXPECT_LE(fdClientAfter - fdClientBefore, 1);
        fdServerAfter = GetViewFieldResultValue(g_fdServer);
        AW_MACRO_EXPECT_EQ_INT(fdServerBefore, fdServerAfter);
        close_epoll_thread();
        testEnvClean();
        RecoverTsCiCfg();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void tsdbDiskOnlineChange::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    ret =
        TestGetResultCommand("cat ../common/logicTablePath.txt | tr -d '$\r'", NULL, g_cStoreDir, sizeof(g_cStoreDir));
    EXPECT_EQ(0, ret);
}

void tsdbDiskOnlineChange::TearDown()
{
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
}

void CreateAndInsert(char *tabelName)
{
    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "create table %s(id integer, time integer) with (time_col = 'time', interval = '1 hour', "
        "disk_limit = '6 MB', compression = 'no');",
        tabelName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);

    constexpr int64_t count = 10;
    int64_t id[count] = {0};
    int64_t time[count] = {0};

    for (int i = 0; i < count; i++) {
        id[i] = i + 1;
        time[i] = 1695042000 + i;
    }

    ret = BlukInsert(stmt, tabelName, count, 2, id, time);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
}

int DropCmTable(char *tableName)
{
    int ret = 0;
    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    return ret;
}

// 001.disk_limit设置为4KB，  预期：变更成功
TEST_F(tsdbDiskOnlineChange, Timing_025_tsdbDiskOnlineChange_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    CreateAndInsert(tableName);
    char sqlCmd[512] = {0};
    // 变更disk_limit
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = ' 4 KB ')", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 等待5秒触发后台变更
    sleep(5);
    (void)sprintf(sqlCmd, "SELECT * FROM %s ;", tableName);
    int64_t dataCount = 0;
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(0, dataCount);
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNDEFINED_TABLE);
}

// 002.disk_limit设置为1 TB，  预期：变更成功
TEST_F(tsdbDiskOnlineChange, Timing_025_tsdbDiskOnlineChange_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    CreateAndInsert(tableName);
    char sqlCmd[512] = {0};
    // 变更disk_limit
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = '1 TB')", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, (char *)"SELECT id, time FROM testdb", 128);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(10, dataCount);
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 003.disk_limit设置为4096 B，  预期：变更成功
TEST_F(tsdbDiskOnlineChange, Timing_025_tsdbDiskOnlineChange_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    CreateAndInsert(tableName);
    char sqlCmd[512] = {0};
    // 变更disk_limit
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = '4096 B')", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    ret = GmcExecDirect(stmt, (char *)"SELECT id, time FROM testdb", 128);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(0, dataCount);
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 004.disk_limit设置为1024 GB，  预期：变更成功
TEST_F(tsdbDiskOnlineChange, Timing_025_tsdbDiskOnlineChange_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    CreateAndInsert(tableName);
    char sqlCmd[512] = {0};
    // 变更disk_limit
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = '1024 GB')", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    ret = GmcExecDirect(stmt, (char *)"SELECT id, time FROM testdb", 128);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(10, dataCount);
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 005.disk_limit设置为1048576MB，  预期：变更成功
TEST_F(tsdbDiskOnlineChange, Timing_025_tsdbDiskOnlineChange_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    CreateAndInsert(tableName);
    char sqlCmd[512] = {0};
    // 变更disk_limit
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = '1048576 MB')", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    ret = GmcExecDirect(stmt, (char *)"SELECT id, time FROM testdb", 128);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(10, dataCount);
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 006.disk_limit设置为1073741824KB，  预期：变更成功
TEST_F(tsdbDiskOnlineChange, Timing_025_tsdbDiskOnlineChange_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    CreateAndInsert(tableName);
    char sqlCmd[512] = {0};
    // 变更disk_limit
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = '1073741824 KB')", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    ret = GmcExecDirect(stmt, (char *)"SELECT id, time FROM testdb", 128);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(10, dataCount);
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 007.disk_limit设置为1099511627776B，  预期：变更成功
TEST_F(tsdbDiskOnlineChange, Timing_025_tsdbDiskOnlineChange_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    CreateAndInsert(tableName);
    char sqlCmd[512] = {0};
    // 变更disk_limit
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = '1099511627776 B')", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    ret = GmcExecDirect(stmt, (char *)"SELECT id, time FROM testdb", 128);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(10, dataCount);
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 008.disk_limit变更比原始值大，设置为10 MB，  预期：变更成功
TEST_F(tsdbDiskOnlineChange, Timing_025_tsdbDiskOnlineChange_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    CreateAndInsert(tableName);
    char sqlCmd[512] = {0};
    // 变更disk_limit
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = '10 MB')", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    ret = GmcExecDirect(stmt, (char *)"SELECT id, time FROM testdb", 128);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(10, dataCount);
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 009.disk_limit变更比原始值小，但大于目前磁盘占用的空间  预期：变更成功
TEST_F(tsdbDiskOnlineChange, Timing_025_tsdbDiskOnlineChange_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    CreateAndInsert(tableName);
    int64_t dataSize = GetDirSize(g_cStoreDir);
    char sqlCmd[512] = {0};
    // 变更disk_limit
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = '5 MB')", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(5);
    dataSize = GetDirSize(g_cStoreDir);
    ret = GmcExecDirect(stmt, (char *)"SELECT id, time FROM testdb", 128);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(10, dataCount);
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 010.disk_limit变更比原始值小，且小于目前磁盘占用的空间，  预期：变更成功,最先注入数据查询结果为空
TEST_F(tsdbDiskOnlineChange, Timing_025_tsdbDiskOnlineChange_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    CreateAndInsert(tableName);
    int64_t dataSize = GetDirSize(g_cStoreDir);
    int insertTimes = 50;
    constexpr int64_t count = 50000;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    for (int j = 0; j < insertTimes; j++) {
        for (int i = 0; i < count; i++) {
            // id需要区分最先注入的数据
            id[i] = i + j * count + 11;
            time[i] = 1704038400 + i + j * insertTimes;
        }
        ret = BlukInsert(stmt, tableName, count, 2, id, time);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    dataSize = GetDirSize(g_cStoreDir);
    char sqlCmd[512] = {0};
    // 变更disk_limit
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = '5 MB')", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(2);
    dataSize = GetDirSize(g_cStoreDir);
    // 系统默认占1MB - 8KB
    if (dataSize >= 1048576) {
        AW_FUN_Log(LOG_ERROR, "disk_limit not in effect.\n");
        ASSERT_EQ(false, true);
    }
    int64_t dataCount = 0;
    (void)sprintf(sqlCmd, "SELECT * FROM %s where id < 11;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(0, dataCount);
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 011.disk_limit变更为 0;   先注入超量数据，然后变更disk_limit，再注入数据
// 预期：变更成功，磁盘占用空间未收到限制
TEST_F(tsdbDiskOnlineChange, Timing_025_tsdbDiskOnlineChange_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    char fdCmd[256] = {0};
    (void)sprintf(fdCmd, "ls -l /proc/`pidof gmserver_ts`/fd |wc -l");
    int32_t fdBefore = 0;
    int32_t fdAfter = 0;
    fdBefore = GetViewFieldResultValue(fdCmd);
    CreateAndInsert(tableName);
    // 写数据
    int insertTimes = 50;
    constexpr int64_t count = 50000;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    for (int j = 0; j < insertTimes; j++) {
        for (int i = 0; i < count; i++) {
            id[i] = i + j * count + 11;
            time[i] = 1704038400 + i + j * insertTimes;
        }
        ret = BlukInsert(stmt, tableName, count, 2, id, time);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // 等待3s使其触发限制，删除数据
    sleep(3);
    int64_t dataCount = 0;
    char sqlCmd[512] = {0};
    uint32_t cmdLen = strlen(sqlCmd);
    (void)sprintf(sqlCmd, "SELECT * FROM %s where id < 11;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 判断最先注入的数据是否删除
    AW_MACRO_ASSERT_EQ_INT(0, dataCount);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 1, fdAfter);
    // 变更disk_limit
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = '0')", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 1, fdAfter);
    // 写数据
    insertTimes = 30;
    constexpr int64_t count1 = 10000;
    id[count1] = {0};
    time[count1] = {0};
    for (int j = 0; j < insertTimes; j++) {
        for (int i = 0; i < count1; i++) {
            id[i] = i + j * count1 + 5200000;
            time[i] = 1704038400 + i + j * count1;
        }
        ret = BlukInsert(stmt, tableName, count1, 2, id, time);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // 等待5s后查询磁盘空间
    sleep(5);
    int64_t dataSize = GetDirSize(g_cStoreDir);
    // 调试用
    // 判断是否超过6 MB,减去系统占用的4 MB
    if (dataSize <= 2097152) {
        AW_FUN_Log(LOG_ERROR, "disk_limit not in effect.\n");
        ASSERT_EQ(false, true);
    }
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 1, fdAfter);
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 清理需要时间，直接查询fd存在未释放的情况
    sleep(5);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore, fdAfter);
}

// 012.多次变更同一张逻辑表  预期：变更成功
TEST_F(tsdbDiskOnlineChange, Timing_025_tsdbDiskOnlineChange_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    CreateAndInsert(tableName);
    int32_t changeTimes = 100;
    int32_t diskLimitSize = 5;
    uint32_t cmdLen;
    char sqlCmd[512] = {0};
    // 多次变更disk_limit
    for (int j = 0; j < changeTimes; j++) {

        diskLimitSize = rand() % 1000 + 5;
        (void)sprintf(sqlCmd, "alter table %s set (disk_limit = '%d MB')", tableName, diskLimitSize);
        cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    sleep(5);
    int64_t dataCount = 0;
    ret = GmcExecDirect(stmt, (char *)"SELECT id, time FROM testdb", 128);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(10, dataCount);
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

typedef struct {
    GmcStmtT *stmt;
    int limitSize;
    char tableNames[128];
} DiskLimitBody;

void *CreatePthreadTable(void *arg)
{
    DiskLimitBody diskLimitBody = *(DiskLimitBody *)arg;
    char sqlCmd[512] = {0};
    (void)sprintf(
        sqlCmd, "alter table %s set (disk_limit = '%ld MB')", diskLimitBody.tableNames, diskLimitBody.limitSize);
    GmcExecDirect(diskLimitBody.stmt, sqlCmd, strlen(sqlCmd));
    return nullptr;
}

// 013.并发调用disk_limit变更同一张时序逻辑表  预期：变更成功
TEST_F(tsdbDiskOnlineChange, Timing_025_tsdbDiskOnlineChange_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    CreateAndInsert(tableName);
    int threadCount = 2;
    pthread_t tid[threadCount];
    GmcConnT *conn_1 = NULL;
    GmcStmtT *stmt_1 = NULL;
    ret = TestTsGmcConnect(&conn_1, &stmt_1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    static GmcConnT *conn_2 = NULL;
    static GmcStmtT *stmt_2 = NULL;
    ret = TestTsGmcConnect(&conn_2, &stmt_2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd1[512] = {0};
    char sqlCmd2[512] = {0};
    int32_t limitSize1 = 5;
    int32_t limitSize2 = 10;
    DiskLimitBody diskLimitBody_1;
    DiskLimitBody diskLimitBody_2;
    diskLimitBody_1.stmt = stmt_1;
    diskLimitBody_1.limitSize = limitSize1;
    strcpy(diskLimitBody_1.tableNames, tableName);
    diskLimitBody_2.stmt = stmt_2;
    diskLimitBody_2.limitSize = limitSize2;
    strcpy(diskLimitBody_2.tableNames, tableName);
    pthread_create(&tid[0], NULL, CreatePthreadTable, (void *)&diskLimitBody_1);
    pthread_create(&tid[1], NULL, CreatePthreadTable, (void *)&diskLimitBody_2);

    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    ret = testGmcDisconnect(conn_1, stmt_1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_2, stmt_2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 等待3秒变更disk_limit
    sleep(3);
    int64_t dataCount = 0;
    ret = GmcExecDirect(stmt, (char *)"SELECT id, time FROM testdb", 128);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(10, dataCount);
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 014.并发调用disk_limit变更不同时序逻辑表  预期：变更成功
TEST_F(tsdbDiskOnlineChange, Timing_025_tsdbDiskOnlineChange_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    CreateAndInsert(tableName);
    CreateAndInsert(tableName2);
    int threadCount = 2;
    pthread_t tid[threadCount];
    GmcConnT *conn_1 = NULL;
    GmcStmtT *stmt_1 = NULL;
    ret = TestTsGmcConnect(&conn_1, &stmt_1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    static GmcConnT *conn_2 = NULL;
    static GmcStmtT *stmt_2 = NULL;
    ret = TestTsGmcConnect(&conn_2, &stmt_2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd1[512] = {0};
    char sqlCmd2[512] = {0};
    int32_t limitSize1 = 4;
    int32_t limitSize2 = 1024;
    DiskLimitBody diskLimitBody_1;
    DiskLimitBody diskLimitBody_2;
    diskLimitBody_1.stmt = stmt_1;
    diskLimitBody_1.limitSize = limitSize1;
    strcpy(diskLimitBody_1.tableNames, tableName);
    diskLimitBody_2.stmt = stmt_2;
    diskLimitBody_2.limitSize = limitSize2;
    strcpy(diskLimitBody_2.tableNames, tableName2);
    pthread_create(&tid[0], NULL, CreatePthreadTable, (void *)&diskLimitBody_1);
    pthread_create(&tid[1], NULL, CreatePthreadTable, (void *)&diskLimitBody_2);

    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    // 等待3秒变更disk_limit
    sleep(3);
    int64_t dataCount = 0;
    ret = GmcExecDirect(stmt, (char *)"SELECT id, time FROM testdb", 128);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(0, dataCount);

    dataCount = 0;
    ret = GmcExecDirect(stmt, (char *)"SELECT id, time FROM testdb1", 128);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(10, dataCount);

    ret = testGmcDisconnect(conn_1, stmt_1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_2, stmt_2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropCmTable(tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 015.注入超过原始disk_limit数据，然后将disk_limit变更为超过注入数据和原始disk_limt之和（在disk_limit范围内）
// 预期：变更成功
TEST_F(tsdbDiskOnlineChange, Timing_025_tsdbDiskOnlineChange_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    CreateAndInsert(tableName);
    // 写数据
    int insertTimes = 50;
    constexpr int64_t count = 50000;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    for (int j = 0; j < insertTimes; j++) {
        for (int i = 0; i < count; i++) {
            id[i] = i + j * count + 11;
            time[i] = 1704038400 + i + j * insertTimes;
        }
        ret = BlukInsert(stmt, tableName, count, 2, id, time);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // 等待5s使其触发限制，删除数据
    sleep(3);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "SELECT * FROM %s where id < 11;", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(0, dataCount);
    // 变更disk_limit
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = '1 GB')", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int j = 0; j < insertTimes; j++) {
        for (int i = 0; i < count; i++) {
            id[i] = i + j * count + 11;
            time[i] = 1704038400 + i + j * insertTimes;
        }
        ret = BlukInsert(stmt, tableName, count, 2, id, time);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    int64_t dataSize = GetDirSize(g_cStoreDir);
    // 调试用
    // 判断是否超过6 MB,减去系统占用的4 MB
    if (dataSize <= 2097152) {
        AW_FUN_Log(LOG_ERROR, "disk_limit not in effect.\n");
        ASSERT_EQ(false, true);
    }
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 016.disk_limit设置为3KB，  预期：变更失败
TEST_F(tsdbDiskOnlineChange, Timing_025_tsdbDiskOnlineChange_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    CreateAndInsert(tableName);
    char sqlCmd[512] = {0};
    // 变更disk_limit
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = '3 KB')", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 017.disk_limit设置为2 TB，  预期：变更失败
TEST_F(tsdbDiskOnlineChange, Timing_025_tsdbDiskOnlineChange_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    CreateAndInsert(tableName);
    char sqlCmd[512] = {0};
    // 变更disk_limit
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = '2 TB')", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 018.disk_limit设置为4095 B，  预期：变更失败
TEST_F(tsdbDiskOnlineChange, Timing_025_tsdbDiskOnlineChange_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    CreateAndInsert(tableName);
    char sqlCmd[512] = {0};
    // 变更disk_limit
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = '4095 B')", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 019.disk_limit设置为1025 GB，  预期：变更失败
TEST_F(tsdbDiskOnlineChange, Timing_025_tsdbDiskOnlineChange_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    CreateAndInsert(tableName);
    char sqlCmd[512] = {0};
    // 变更disk_limit
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = '1025 GB')", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 020.disk_limit设置为1048577MB，  预期：变更失败
TEST_F(tsdbDiskOnlineChange, Timing_025_tsdbDiskOnlineChange_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    CreateAndInsert(tableName);
    char sqlCmd[512] = {0};
    // 变更disk_limit
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = '1048577 MB')", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 021.disk_limit设置为1073741825KB，  预期：变更失败
TEST_F(tsdbDiskOnlineChange, Timing_025_tsdbDiskOnlineChange_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    CreateAndInsert(tableName);
    char sqlCmd[512] = {0};
    // 变更disk_limit
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = '1073741825 KB')", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 022.disk_limit设置为1099511627777B，  预期：变更失败
TEST_F(tsdbDiskOnlineChange, Timing_025_tsdbDiskOnlineChange_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    CreateAndInsert(tableName);
    char sqlCmd[512] = {0};
    // 变更disk_limit
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = '1099511627777 B')", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 023.设置时alter关键字错误  预期：变更失败
TEST_F(tsdbDiskOnlineChange, Timing_025_tsdbDiskOnlineChange_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    CreateAndInsert(tableName);
    char sqlCmd[512] = {0};
    // 变更disk_limit
    (void)sprintf(sqlCmd, "alte table %s set (disk_limit = '5 MB')", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_SYNTAX_ERROR);
}

// 024.设置时disk_limit关键字错误  预期：变更失败
TEST_F(tsdbDiskOnlineChange, Timing_025_tsdbDiskOnlineChange_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    CreateAndInsert(tableName);
    char sqlCmd[512] = {0};
    // 变更disk_limit
    (void)sprintf(sqlCmd, "alter table %s set (disk_limi = '5 MB')", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_FEATURE_NOT_SUPPORTED);
}

// 025.设置时set关键字错误  预期：变更失败
TEST_F(tsdbDiskOnlineChange, Timing_025_tsdbDiskOnlineChange_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    CreateAndInsert(tableName);
    char sqlCmd[512] = {0};
    // 变更disk_limit
    (void)sprintf(sqlCmd, "alter table %s st (disk_limit = '5 MB')", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_SYNTAX_ERROR);
}

// 026.设置时tablename缺失  预期：变更失败
TEST_F(tsdbDiskOnlineChange, Timing_025_tsdbDiskOnlineChange_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    CreateAndInsert(tableName);
    char sqlCmd[512] = {0};
    // 变更disk_limit
    (void)sprintf(sqlCmd, "alter table  set (disk_limit = '5 MB')");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_SYNTAX_ERROR);
}

// 027.设置时disk_limit缺失  预期：变更失败
TEST_F(tsdbDiskOnlineChange, Timing_025_tsdbDiskOnlineChange_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    CreateAndInsert(tableName);
    char sqlCmd[512] = {0};
    // 变更disk_limit
    (void)sprintf(sqlCmd, "alter table %s set ( = '5 MB')", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_SYNTAX_ERROR);
}

// 028.设置时set关键字缺失  预期：变更失败
TEST_F(tsdbDiskOnlineChange, Timing_025_tsdbDiskOnlineChange_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    CreateAndInsert(tableName);
    char sqlCmd[512] = {0};
    // 变更disk_limit
    (void)sprintf(sqlCmd, "alter table %s  (disk_limit = '5 MB')", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_SYNTAX_ERROR);
}

// 029.设置时alter关键字缺失  预期：变更失败
TEST_F(tsdbDiskOnlineChange, Timing_025_tsdbDiskOnlineChange_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    CreateAndInsert(tableName);
    char sqlCmd[512] = {0};
    // 变更disk_limit
    (void)sprintf(sqlCmd, " table %s set (disk_limit = '5 MB')", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_SYNTAX_ERROR);
}

// 030.设置时变更值为空  预期：变更失败
TEST_F(tsdbDiskOnlineChange, Timing_025_tsdbDiskOnlineChange_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    CreateAndInsert(tableName);
    char sqlCmd[512] = {0};
    // 变更disk_limit
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = ' ')", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 031.设置时等号为空  预期：变更失败
TEST_F(tsdbDiskOnlineChange, Timing_025_tsdbDiskOnlineChange_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    CreateAndInsert(tableName);
    char sqlCmd[512] = {0};
    // 变更disk_limit
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit  '5 MB')", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_SYNTAX_ERROR);
}

// 032.设置时数字和单位间无空格  预期：变更失败
TEST_F(tsdbDiskOnlineChange, Timing_025_tsdbDiskOnlineChange_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    CreateAndInsert(tableName);
    char sqlCmd[512] = {0};
    // 变更disk_limit
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = '5MB')", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 033.设置时缺失单位  预期：变更失败
TEST_F(tsdbDiskOnlineChange, Timing_025_tsdbDiskOnlineChange_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    CreateAndInsert(tableName);
    char sqlCmd[512] = {0};
    // 变更disk_limit
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = '5 ')", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 034.设置时缺失数值  预期：变更失败
TEST_F(tsdbDiskOnlineChange, Timing_025_tsdbDiskOnlineChange_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    CreateAndInsert(tableName);
    char sqlCmd[512] = {0};
    // 变更disk_limit
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = ' GB')", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 035.disk_limit参数使用小数  预期：变更失败
TEST_F(tsdbDiskOnlineChange, Timing_025_tsdbDiskOnlineChange_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    CreateAndInsert(tableName);
    char sqlCmd[512] = {0};
    // 变更disk_limit
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = '5.5 MB')", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 036.设置时参数中的数值无效  预期：变更失败
TEST_F(tsdbDiskOnlineChange, Timing_025_tsdbDiskOnlineChange_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    CreateAndInsert(tableName);
    char sqlCmd[512] = {0};
    // 变更disk_limit
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = 'aaaa MB')", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 037.设置时参数中的单位无效  预期：变更失败
TEST_F(tsdbDiskOnlineChange, Timing_025_tsdbDiskOnlineChange_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    CreateAndInsert(tableName);
    char sqlCmd[512] = {0};
    // 变更disk_limit
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = '5 hsdg')", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 038.重复设置set  预期：变更失败
TEST_F(tsdbDiskOnlineChange, Timing_025_tsdbDiskOnlineChange_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    CreateAndInsert(tableName);
    char sqlCmd[512] = {0};
    // 变更disk_limit
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = '5 MB' , disk_limit = '10 MB');", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DUPLICATE_OBJECT, ret);
    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_DUPLICATE_OBJECT);
}

// 039.设置disk_limit时，操作的表不是时序逻辑表  预期：变更失败
TEST_F(tsdbDiskOnlineChange, Timing_025_tsdbDiskOnlineChange_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *labelJson = NULL;
    char g_configJson[128] = "{\"max_record_count\" : 1000}";
    char tableName3[] = "test_cbj02";
    ret = DropCmTable(tableName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    readJanssonFile("VertexLabel_test.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    ret = TestTsGmcConnect(&conn1, &stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt1, labelJson, g_configJson);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    // 变更disk_limit
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = '5 MB' )", tableName3);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt1, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = GmcDropVertexLabel(stmt, tableName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(labelJson);
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_UNDEFINED_TABLE, GMERR_FEATURE_NOT_SUPPORTED);
}

// 040.设置disk_limit时，操作的表不存在  预期：变更失败
TEST_F(tsdbDiskOnlineChange, Timing_025_tsdbDiskOnlineChange_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char tableName1[] = "testdb_test";
    char sqlCmd[512] = {0};
    // 变更disk_limit
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = '5 KB')", tableName1);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
}
