/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 【技术转交付】TSDB 批量读 测试
 * Author: jiangjincheng
 * Create: 2024-03-14
 */
#include "gtest/gtest.h"
#include "t_rd_ts.h"

static GmcConnOptionsT *connOptions = NULL;
static GmcConnT *conn = NULL;
static GmcStmtT *stmt = NULL;
Status ret = 0;
char tabelName[] = "testdb";
char *dir = getenv("GMDB_HOME");
bool eof = false;
char *returnValue = {0};

class TsdbBatchReadBasic : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        InitTsCiCfg();
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        // 创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        ret = TestTsGmcConnect(&conn, &stmt, 0);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {   
        GmcConnOptionsDestroy(connOptions);
        GmcFreeStmt(stmt);
        GmcDisconnect(conn);
        close_epoll_thread();
        testEnvClean();
        RecoverTsCiCfg();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void TsdbBatchReadBasic::SetUp()
{
    AW_CHECK_LOG_BEGIN();
}

void TsdbBatchReadBasic::TearDown()
{
    CsvFileDelete();
    AW_CHECK_LOG_END();
}

void CreateAndInsert(char *tabelName){
    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "create table %s(id integer, time integer, num1 integer) with (time_col = 'time', "
        "interval= '1 hour', compression='fast(rapidlz)');",
        tabelName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int64_t count = 10;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t num1[count] = {1, 1, 1, 1, 2, 2, 2, 3, 3, 4};

    for (int i = 0; i < count; i++) {
        id[i] = i + 1;
        time[i] = 1695042000 + i;
    }

    ret = BlukInsert(stmt, tabelName, count, 3, id, time, num1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

bool isDataCsvExist()
{
    if (FILE *file = fopen("data.csv", "r")) {
        fclose(file);
        return true;
    }
    return false;
}

void GetCsvFileContent(char returnValue[])
{
    FILE *file = fopen("data.csv", "r");
    char row[128];
    while (fgets(row, 128, file) != NULL) {
        strcat(returnValue, row);
    }
    fclose(file);
}

// COPY TO中查询语句and，>=，<=运算符功能验证 预期：功能正常
TEST_F(TsdbBatchReadBasic, Timing_002_TsdbBatchReadBasic_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int i = 0;
    int64_t cPersonId = 0;
    int64_t cPersonTime = 0;
    int64_t cPersonNum1 = 0;
    CreateAndInsert(tabelName);

    char sqlCmd[256] = {0};
    sprintf(sqlCmd, 
        "COPY (SELECT id, time, num1 FROM testdb WHERE id >= 2 and time <= 1695042008) TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/data.csv';", dir);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, 256);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *expectContent = R"(2,1695042001,1
3,1695042002,1
4,1695042003,1
5,1695042004,2
6,1695042005,2
7,1695042006,2
8,1695042007,3
9,1695042008,3
)";

    // 检查csv文件是否生成
    AW_MACRO_ASSERT_EQ_INT(true, isDataCsvExist());
    char actualContent[1024] = {0};
    (void)GetCsvFileContent(actualContent);
    // 检查获取到内容是否正确
    AW_MACRO_ASSERT_EQ_STR(expectContent, actualContent);
    // 补充删表清理动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tabelName));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// COPY TO中查询语句or,>,<运算符功能验证 预期：功能正常
TEST_F(TsdbBatchReadBasic, Timing_002_TsdbBatchReadBasic_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int i = 0;
    int64_t cPersonId = 0;
    int64_t cPersonTime = 0;
    int64_t cPersonNum1 = 0;
    CreateAndInsert(tabelName);

    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "COPY (SELECT id, time, num1 FROM testdb WHERE id>8 or time<1695042002) TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/data.csv';", dir);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, 256);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *expectContent = R"(1,1695042000,1
2,1695042001,1
9,1695042008,3
10,1695042009,4
)";

    // 检查csv文件是否生成
    AW_MACRO_ASSERT_EQ_INT(true, isDataCsvExist());
    char actualContent[1024] = {0};
    (void)GetCsvFileContent(actualContent);
    // 检查获取到内容是否正确
    AW_MACRO_ASSERT_EQ_STR(expectContent, actualContent);

    // 补充删表清理动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tabelName));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// COPY TO中查询语句or，>=，=运算符功能验证 预期：功能正常
TEST_F(TsdbBatchReadBasic, Timing_002_TsdbBatchReadBasic_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int i = 0;
    int64_t cPersonId = 0;
    int64_t cPersonTime = 0;
    int64_t cPersonNum1 = 0;
    CreateAndInsert(tabelName);

    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "COPY (SELECT id, time, num1 FROM testdb WHERE id>=8 or time=1695042002) TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/data.csv';", dir);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, 256);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *expectContent = R"(3,1695042002,1
8,1695042007,3
9,1695042008,3
10,1695042009,4
)";

    // 检查csv文件是否生成
    AW_MACRO_ASSERT_EQ_INT(true, isDataCsvExist());
    char actualContent[1024] = {0};
    (void)GetCsvFileContent(actualContent);
    // 检查获取到内容是否正确
    AW_MACRO_ASSERT_EQ_STR(expectContent, actualContent);
    // 补充删表清理动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tabelName));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// COPY TO中查询语句结果列中，COUNT聚合功能验证 预期：功能正常
TEST_F(TsdbBatchReadBasic, Timing_002_TsdbBatchReadBasic_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int i = 0;
    int64_t cPersonId = 0;
    int64_t cPersonTime = 0;
    int64_t cPersonNum1 = 0;
    int64_t count = 0;
    CreateAndInsert(tabelName);

    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "COPY (SELECT num1, COUNT(id) FROM testdb group by num1) TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/data.csv';", dir);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, 256);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *expectContent = R"(1,4
2,3
3,2
4,1
)";

    // 检查csv文件是否生成
    AW_MACRO_ASSERT_EQ_INT(true, isDataCsvExist());
    char actualContent[1024] = {0};
    (void)GetCsvFileContent(actualContent);
    // 检查获取到内容是否正确
    AW_MACRO_ASSERT_EQ_STR(expectContent, actualContent);
    // 补充删表清理动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tabelName));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// COPY TO中查询语句结果列中，SUM聚合功能验证 预期：功能正常
TEST_F(TsdbBatchReadBasic, Timing_002_TsdbBatchReadBasic_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int i = 0;
    int64_t cPersonId = 0;
    int64_t cPersonTime = 0;
    int64_t cPersonNum1 = 0;
    int64_t sumTime = 0;
    CreateAndInsert(tabelName);

    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "COPY (SELECT num1, SUM(time) FROM testdb group by num1) TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/data.csv';", dir);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, 256);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *expectContent = R"(1,6780168006
2,5085126015
3,3390084015
4,1695042009
)";

    // 检查csv文件是否生成
    AW_MACRO_ASSERT_EQ_INT(true, isDataCsvExist());
    char actualContent[1024] = {0};
    (void)GetCsvFileContent(actualContent);
    // 检查获取到内容是否正确
    AW_MACRO_ASSERT_EQ_STR(expectContent, actualContent);
    // 补充删表清理动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tabelName));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// COPY TO中查询语句结果列中，MAX,MIN聚合功能验证 预期：功能正常
TEST_F(TsdbBatchReadBasic, Timing_002_TsdbBatchReadBasic_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int i = 0;
    int64_t minId = 0;
    int64_t cPersonNum1 = 0;
    int64_t maxTime = 0;
    CreateAndInsert(tabelName);

    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "COPY (SELECT num1, MAX(time), MIN(id) FROM testdb group by num1) TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/data.csv';", dir);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, 256);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *expectContent = R"(1,1695042003,1
2,1695042006,5
3,1695042008,8
4,1695042009,10
)";

    // 检查csv文件是否生成
    AW_MACRO_ASSERT_EQ_INT(true, isDataCsvExist());
    char actualContent[1024] = {0};
    (void)GetCsvFileContent(actualContent);
    // 检查获取到内容是否正确
    AW_MACRO_ASSERT_EQ_STR(expectContent, actualContent);
    // 补充删表清理动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tabelName));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// COPY TO中查询语句表达式中，COUNT聚合功能验证 预期：功能正常
TEST_F(TsdbBatchReadBasic, Timing_002_TsdbBatchReadBasic_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int i = 0;
    int64_t cPersonId = 0;
    int64_t count = 0;
    int64_t cPersonNum1 = 0;
    CreateAndInsert(tabelName);

    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "COPY (SELECT num1, COUNT(id) FROM testdb group by num1 order by COUNT(id) DESC) TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/data.csv';", dir);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, 256);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *expectContent = R"(1,4
2,3
3,2
4,1
)";

    // 检查csv文件是否生成
    AW_MACRO_ASSERT_EQ_INT(true, isDataCsvExist());
    char actualContent[1024] = {0};
    (void)GetCsvFileContent(actualContent);
    // 检查获取到内容是否正确
    AW_MACRO_ASSERT_EQ_STR(expectContent, actualContent);
    // 补充删表清理动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tabelName));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// COPY TO中查询语句表达式中，SUM聚合功能验证 预期：功能正常
TEST_F(TsdbBatchReadBasic, Timing_002_TsdbBatchReadBasic_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int i = 0;
    int64_t cPersonId = 0;
    int64_t sumId = 0;
    int64_t cPersonNum1 = 0;
    CreateAndInsert(tabelName);

    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "COPY (SELECT num1, SUM(id) FROM testdb group by num1 order by SUM(id) DESC) TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/data.csv';", dir);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, 256);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *expectContent = R"(2,18
3,17
1,10
4,10
)";

    // 检查csv文件是否生成
    AW_MACRO_ASSERT_EQ_INT(true, isDataCsvExist());
    char actualContent[1024] = {0};
    (void)GetCsvFileContent(actualContent);
    // 检查获取到内容是否正确
    AW_MACRO_ASSERT_EQ_STR(expectContent, actualContent);
    // 补充删表清理动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tabelName));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// COPY TO中查询语句绑定列的数量和查询列的数量不一致 预期：返回成功
TEST_F(TsdbBatchReadBasic, Timing_002_TsdbBatchReadBasic_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int i = 0;
    int64_t cPersonId = 0;
    int64_t cPersonTime = 0;
    int64_t cPersonNum1 = 0;
    CreateAndInsert(tabelName);

    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "COPY (SELECT id, time FROM testdb WHERE id >= 2 and time <= 1695042008) TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/data.csv';", dir);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, 256);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *expectContent = R"(2,1695042001
3,1695042002
4,1695042003
5,1695042004
6,1695042005
7,1695042006
8,1695042007
9,1695042008
)";

    // 检查csv文件是否生成
    AW_MACRO_ASSERT_EQ_INT(true, isDataCsvExist());
    char actualContent[1024] = {0};
    (void)GetCsvFileContent(actualContent);
    // 检查获取到内容是否正确
    AW_MACRO_ASSERT_EQ_STR(expectContent, actualContent);
    // 补充删表清理动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tabelName));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// COPY TO中查询语句绑定列的数据类型和查询列返回的数据类型不一致 预期：返回成功
TEST_F(TsdbBatchReadBasic, Timing_002_TsdbBatchReadBasic_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int i = 0;
    int64_t cPersonId = 0;
    int64_t cPersonTime = 0;
    int64_t cPersonNum1 = 0;
    char cName[64] = {0};

    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64)) with (time_col = 'time', "
        "interval= '1 hour', compression='fast');",
        tabelName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int64_t count = 10;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    char name[][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};

    for (int i = 0; i < count; i++) {
        id[i] = i + 1;
        time[i] = 1695042000 + i;
    }

    ret = BlukInsert_char(stmt, tabelName, count, 3, id, time, name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    sprintf(sqlCmd,
        "COPY (SELECT id, name, time FROM testdb WHERE id >= 2 and time <= 1695042008) TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/data.csv';", dir);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, 256);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *expectContent = R"(2,nut,1695042001
3,bob,1695042002
4,olivia,1695042003
5,tim,1695042004
6,lucy,1695042005
7,Tom,1695042006
8,deft,1695042007
9,night,1695042008
)";

    // 检查csv文件是否生成
    AW_MACRO_ASSERT_EQ_INT(true, isDataCsvExist());
    char actualContent[1024] = {0};
    (void)GetCsvFileContent(actualContent);
    // 检查获取到内容是否正确
    AW_MACRO_ASSERT_EQ_STR(expectContent, actualContent);

    // 补充删表清理动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tabelName));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// COPY TO中DQL语句关键字缺失 预期：返回错误
TEST_F(TsdbBatchReadBasic, Timing_002_TsdbBatchReadBasic_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int i = 0;
    int64_t cPersonId = 0;
    int64_t cPersonTime = 0;
    int64_t cPersonNum1 = 0;
    CreateAndInsert(tabelName);

    // 关键字缺失
    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "COPY (SELECT id, time, num1 FROM testdb id >= 2 and time <= 1695042008) TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/data.csv';", dir);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, 256);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    AW_MACRO_ASSERT_EQ_INT(false, isDataCsvExist());

    (void)memset(sqlCmd, 0, 256);
    sprintf(sqlCmd,
        "COPY (SELECT id, time, num1 FROM testdb WHERE id >= 2 and time <= 1695042006) TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/data.csv';", dir);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, 256);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(true, isDataCsvExist());

    // 补充删表清理动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tabelName));
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_SYNTAX_ERROR, GMERR_DATA_EXCEPTION);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// COPY TO中DQL语句表名缺失 预期：返回错误
TEST_F(TsdbBatchReadBasic, Timing_002_TsdbBatchReadBasic_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int i = 0;
    int64_t cPersonId = 0;
    int64_t cPersonTime = 0;
    int64_t cPersonNum1 = 0;
    CreateAndInsert(tabelName);

    // 表名缺失
    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "COPY (SELECT id, time, num1 FROM WHERE id >= 2 and time <= 1695042008) TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/data.csv';", dir);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, 256);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    AW_MACRO_ASSERT_EQ_INT(false, isDataCsvExist());

    (void)memset(sqlCmd, 0, 256);
    sprintf(sqlCmd,
        "COPY (SELECT id, time, num1 FROM testdb WHERE id >= 2 and time <= 1695042006) TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/data.csv';", dir);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, 256);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(true, isDataCsvExist());

    // 补充删表清理动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tabelName));
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_SYNTAX_ERROR, GMERR_DATA_EXCEPTION);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// COPY TO中DQL语句目标列缺失 预期：返回错误
TEST_F(TsdbBatchReadBasic, Timing_002_TsdbBatchReadBasic_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int i = 0;
    int64_t cPersonId = 0;
    int64_t cPersonTime = 0;
    int64_t cPersonNum1 = 0;
    CreateAndInsert(tabelName);

    // 目标列缺失
    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "COPY (SELECT FROM testdb WHERE id >= 2 and time <= 1695042008) TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/data.csv';", dir);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, 256);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    AW_MACRO_ASSERT_EQ_INT(false, isDataCsvExist());

    (void)memset(sqlCmd, 0, 256);
    sprintf(sqlCmd,
        "COPY (SELECT id, time, num1 FROM testdb WHERE id >= 2 and time <= 1695042006) TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/data.csv';", dir);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, 256);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(true, isDataCsvExist());

    // 补充删表清理动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tabelName));
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_SYNTAX_ERROR, GMERR_DATA_EXCEPTION);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 指定文件后缀名为.txt 预期：返回错误
TEST_F(TsdbBatchReadBasic, Timing_002_TsdbBatchReadBasic_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int i = 0;
    int64_t cPersonId = 0;
    int64_t cPersonTime = 0;
    int64_t cPersonNum1 = 0;
    CreateAndInsert(tabelName);

    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "COPY (SELECT id, time, num1 FROM testdb WHERE id >= 2 and time <= 1695042006) TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/data.txt';", dir);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, 256);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SEMANTIC_ERROR, ret);
    AW_MACRO_ASSERT_EQ_INT(false, isDataCsvExist());

    (void)memset(sqlCmd, 0, 256);
    sprintf(sqlCmd,
        "COPY (SELECT id, time, num1 FROM testdb WHERE id >= 2 and time <= 1695042006) TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/data.csv';", dir);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, 256);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(true, isDataCsvExist());

    // 补充删表清理动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tabelName));
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_SEMANTIC_ERROR, GMERR_DATA_EXCEPTION);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 指定文件后缀名为.cpp 预期：返回错误
TEST_F(TsdbBatchReadBasic, Timing_002_TsdbBatchReadBasic_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int i = 0;
    int64_t cPersonId = 0;
    int64_t cPersonTime = 0;
    int64_t cPersonNum1 = 0;
    CreateAndInsert(tabelName);

    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "COPY (SELECT id, time, num1 FROM testdb WHERE id >= 2 and time <= 1695042006) TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/data.cpp';", dir);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, 256);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SEMANTIC_ERROR, ret);
    AW_MACRO_ASSERT_EQ_INT(false, isDataCsvExist());

    (void)memset(sqlCmd, 0, 256);
    sprintf(sqlCmd,
        "COPY (SELECT id, time, num1 FROM testdb WHERE id >= 2 and time <= 1695042006) TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/data.csv';", dir);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, 256);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(true, isDataCsvExist());

    // 补充删表清理动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tabelName));
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_SEMANTIC_ERROR, GMERR_DATA_EXCEPTION);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 指定文件后缀名为空 预期：返回错误
TEST_F(TsdbBatchReadBasic, Timing_002_TsdbBatchReadBasic_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int i = 0;
    int64_t cPersonId = 0;
    int64_t cPersonTime = 0;
    int64_t cPersonNum1 = 0;
    CreateAndInsert(tabelName);

    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "COPY (SELECT id, time, num1 FROM testdb WHERE id >= 2 and time <= 1695042006) TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/data';", dir);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, 256);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SEMANTIC_ERROR, ret);
    AW_MACRO_ASSERT_EQ_INT(false, isDataCsvExist());

    (void)memset(sqlCmd, 0, 256);
    sprintf(sqlCmd,
        "COPY (SELECT id, time, num1 FROM testdb WHERE id >= 2 and time <= 1695042006) TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/data.csv';", dir);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, 256);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(true, isDataCsvExist());

    // 补充删表清理动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tabelName));
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_SEMANTIC_ERROR, GMERR_DATA_EXCEPTION);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 指定文件所属目录不存在 预期：返回错误
TEST_F(TsdbBatchReadBasic, Timing_002_TsdbBatchReadBasic_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int i = 0;
    int64_t cPersonId = 0;
    int64_t cPersonTime = 0;
    int64_t cPersonNum1 = 0;
    CreateAndInsert(tabelName);

    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "COPY (SELECT id, time, num1 FROM testdb WHERE id >= 2 and time <= 1695042006) TO"
        "'%s/test/sdv/testcases_rd/003_ts/007_tsdb_batch_read/data.csv';", dir);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, 256);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DIRECTORY_OPERATE_FAILED, ret);
    AW_MACRO_ASSERT_EQ_INT(false, isDataCsvExist());

    (void)memset(sqlCmd, 0, 256);
    sprintf(sqlCmd,
        "COPY (SELECT id, time, num1 FROM testdb WHERE id >= 2 and time <= 1695042006) TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/006_tsdb_batch_read/data.csv';", dir);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, 256);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DIRECTORY_OPERATE_FAILED, ret);
    AW_MACRO_ASSERT_EQ_INT(false, isDataCsvExist());

    (void)memset(sqlCmd, 0, 256);
    sprintf(sqlCmd,
        "COPY (SELECT id, time, num1 FROM testdb WHERE id >= 2 and time <= 1695042006) TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/data.csv';", dir);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, 256);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(true, isDataCsvExist());

    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DIRECTORY_OPERATE_FAILED, GMERR_DATA_EXCEPTION);
    // 补充删表清理动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tabelName));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// COPY关键字缺失 预期：返回错误
TEST_F(TsdbBatchReadBasic, Timing_002_TsdbBatchReadBasic_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int i = 0;
    int64_t cPersonId = 0;
    int64_t cPersonTime = 0;
    int64_t cPersonNum1 = 0;
    CreateAndInsert(tabelName);

    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "(SELECT id, time, num1 FROM testdb WHERE id >= 2 and time <= 1695042006) TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/data.csv';", dir);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, 256);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    AW_MACRO_ASSERT_EQ_INT(false, isDataCsvExist());

    (void)memset(sqlCmd, 0, 256);
    sprintf(sqlCmd,
        "COPY (SELECT id, time, num1 FROM testdb WHERE id >= 2 and time <= 1695042006) TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/data.csv';", dir);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, 256);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(true, isDataCsvExist());

    // 补充删表清理动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tabelName));
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_SYNTAX_ERROR, GMERR_DATA_EXCEPTION);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// TO关键字缺失 预期：返回错误
TEST_F(TsdbBatchReadBasic, Timing_002_TsdbBatchReadBasic_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int i = 0;
    int64_t cPersonId = 0;
    int64_t cPersonTime = 0;
    int64_t cPersonNum1 = 0;
    CreateAndInsert(tabelName);

    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "COPY (SELECT id, time, num1 FROM testdb WHERE id >= 2 and time <= 1695042006)"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/data.csv';", dir);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, 256);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    AW_MACRO_ASSERT_EQ_INT(false, isDataCsvExist());

    (void)memset(sqlCmd, 0, 256);
    sprintf(sqlCmd,
        "COPY (SELECT id, time, num1 FROM testdb WHERE id >= 2 and time <= 1695042006) TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/data.csv';", dir);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, 256);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(true, isDataCsvExist());

    // 补充删表清理动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tabelName));
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_SYNTAX_ERROR, GMERR_DATA_EXCEPTION);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DQL的（缺失 预期：返回错误
TEST_F(TsdbBatchReadBasic, Timing_002_TsdbBatchReadBasic_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int i = 0;
    int64_t cPersonId = 0;
    int64_t cPersonTime = 0;
    int64_t cPersonNum1 = 0;
    CreateAndInsert(tabelName);

    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "COPY SELECT id, time, num1 FROM testdb WHERE id >= 2 and time <= 1695042006 TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/data.csv';", dir);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, 256);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    AW_MACRO_ASSERT_EQ_INT(false, isDataCsvExist());

    (void)memset(sqlCmd, 0, 256);
    sprintf(sqlCmd,
        "COPY (SELECT id, time, num1 FROM testdb WHERE id >= 2 and time <= 1695042006) TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/data.csv';", dir);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, 256);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(true, isDataCsvExist());

    // 补充删表清理动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tabelName));
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_SYNTAX_ERROR, GMERR_DATA_EXCEPTION);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 目标表不是已有时序逻辑表 预期：执行失败
TEST_F(TsdbBatchReadBasic, Timing_002_TsdbBatchReadBasic_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int i = 0;
    int64_t cPersonId = 0;
    int64_t cPersonTime = 0;
    int64_t cPersonNum1 = 0;
    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "COPY (SELECT id, time, num1 FROM testdb WHERE id >= 2 and time <= 1695042006) TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/data.csv';", dir);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, 256);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    AW_MACRO_ASSERT_EQ_INT(false, isDataCsvExist());

    CreateAndInsert(tabelName);

    (void)memset(sqlCmd, 0, 256);
    sprintf(sqlCmd,
        "COPY (SELECT id, time, num1 FROM testdb WHERE id >= 2 and time <= 1695042006) TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/data.csv';", dir);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, 256);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(true, isDataCsvExist());

    // 补充删表清理动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tabelName));
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNDEFINED_TABLE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 指定文件路径长度超过255个字符 预期：执行失败
TEST_F(TsdbBatchReadBasic, Timing_002_TsdbBatchReadBasic_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int i = 0;
    int64_t cPersonId = 0;
    int64_t cPersonTime = 0;
    int64_t cPersonNum1 = 0;
    CreateAndInsert(tabelName);

    char sqlStr[256] = "data.csv";
    for (int i = 0; i < 252; i++) {
        if (i % 4 == 0){
           sqlStr[i] = 'a';
           sqlStr[i+1] = 'b';
           sqlStr[i+2] = 'c';
           sqlStr[i+3] = 'd';
        }
    }
    sqlStr[251] = '.';
    sqlStr[252] = 'c';
    sqlStr[253] = 's';
    sqlStr[254] = 'v';
    sqlStr[255] = '\0';

    char sqlCmd[512] = {0};
    sprintf(sqlCmd,
        "COPY (SELECT id, time, num1 FROM testdb WHERE id >= 2 and time <= 1695042006) TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/%s';", dir, sqlStr);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, 256);
    AW_MACRO_ASSERT_EQ_INT(GMERR_FIELD_OVERFLOW, ret);
    AW_MACRO_ASSERT_EQ_INT(false, isDataCsvExist());

    (void)memset(sqlCmd, 0, 512);
    sprintf(sqlCmd,
        "COPY (SELECT id, time, num1 FROM testdb WHERE id >= 2 and time <= 1695042006) TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/data.csv';", dir);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, 512);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(true, isDataCsvExist());
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_FIELD_OVERFLOW);
    // 补充删表清理动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tabelName));
    AW_FUN_Log(LOG_STEP, "test end.");
}

void *CopyToConcurrent(void *arg)
{
    GmcStmtT *stmt = *(GmcStmtT **)arg;

    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "COPY (SELECT id, time, num1 FROM testdb WHERE id >= 2 and time <= 1695050000) TO"
        "'%s/test/sdv/testcases/25_Timing/002_tsdb_batch_read/data.csv';", dir);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, 256);
    if (ret != GMERR_OK) {
        // 执行失败时判断为csv文件不存在
        AW_MACRO_EXPECT_EQ_INT(false, isDataCsvExist());
    }else {
        // 执行成功时判断为csv文件存在
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    printf("data.csv is existed? answer is %d\n", isDataCsvExist());
    return nullptr;
}

void *BulkInsertConcurrent(void *arg)
{
    GmcStmtT *stmt = *(GmcStmtT **)arg;
    constexpr int64_t count = 100;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t num1[count] = {0};
    int64_t num2[count] = {0};

    for (int i = 0; i < count; i++) {
        id[i] = i + 100;
        time[i] = 1695042000 + i;
        if (i < 3) {
            num1[i] = 2000000005;
        }else if (i < 8) {
            num1[i] = 2000000006;
        }else if (i < 11) {
            num1[i] = 2000000007;
        }
        else {
            num1[i] = 2000000008;
        }
    }
    ret = BlukInsert(stmt, tabelName, count, 3, id, time, num1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    return nullptr;
}

// copy to语句与DML并发 预期：执行成功
TEST_F(TsdbBatchReadBasic, Timing_002_TsdbBatchReadBasic_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int i = 0;
    int threadCount = 2;
    pthread_t tid[threadCount];
    CreateAndInsert(tabelName);

    static GmcConnT *conn_1 = NULL;
    static GmcStmtT *stmt_1 = NULL;
    ret = TestTsGmcConnect(&conn_1, &stmt_1, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建线程一
    pthread_create(&tid[0], NULL, CopyToConcurrent, &stmt);
    // 创建线程二
    pthread_create(&tid[1], NULL, BulkInsertConcurrent, &stmt_1);
    // 开启线程，运行结束后释放句柄
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    GmcFreeStmt(stmt_1);
    GmcDisconnect(conn_1);

    // 补充删表清理动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tabelName));
    AW_FUN_Log(LOG_STEP, "test end.");
}

void *QueryConcurrent(void *arg)
{
    GmcStmtT *stmt = *(GmcStmtT **)arg;
    int i = 0;
    constexpr int64_t count = 100;
    int64_t cPersonId = 0;
    int64_t cPersonTime = 0;
    int64_t cPersonNum1 = 0;
    eof = false;
    bool isNull = false;
    uint32_t propSize = sizeof(cPersonTime);

    ret = GmcExecDirect(stmt,
    "SELECT id, time, num1 FROM testdb WHERE id >= 2 and time <= 1695042008", 256);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (i=0; ; i++) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof) {
            break;
        }
        if (ret == GMERR_OK) {
            ret = GmcGetPropertyById(stmt, 0, &cPersonId, &propSize, &isNull);
            EXPECT_EQ(ret, GMERR_OK);
            ret = GmcGetPropertyById(stmt, 1, &cPersonTime, &propSize, &isNull);
            EXPECT_EQ(ret, GMERR_OK);
            ret = GmcGetPropertyById(stmt, 2, &cPersonNum1, &propSize, &isNull);
            EXPECT_EQ(ret, GMERR_OK);

            printf("Record %d: %ld, %ld, %ld\n", i+1, (int64_t)cPersonId, (int64_t)cPersonTime, (int64_t)cPersonNum1);
        }
    }
    return nullptr;
}

// copy to语句与DQL并发 预期：执行成功
TEST_F(TsdbBatchReadBasic, Timing_002_TsdbBatchReadBasic_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int i = 0;
    int threadCount = 2;
    pthread_t tid[threadCount];
    CreateAndInsert(tabelName);
    
    static GmcConnT *conn_1 = NULL;
    static GmcStmtT *stmt_1 = NULL;
    ret = TestTsGmcConnect(&conn_1, &stmt_1, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建线程一
    pthread_create(&tid[0], NULL, CopyToConcurrent, &stmt);
    // 创建线程二
    pthread_create(&tid[1], NULL, QueryConcurrent, &stmt_1);
    // 开启线程，运行结束后释放句柄
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    GmcFreeStmt(stmt_1);
    GmcDisconnect(conn_1);

    // 补充删表清理动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tabelName));
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_INTERNAL_ERROR);
    AW_FUN_Log(LOG_STEP, "test end.");
}

void *DropCmTableConcurrent(void *arg)
{
    GmcStmtT *stmt = *(GmcStmtT **)arg;
    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table testdb;");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return nullptr;
}

// copy to语句与DDL并发 预期：执行失败
TEST_F(TsdbBatchReadBasic, Timing_002_TsdbBatchReadBasic_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int i = 0;
    int threadCount = 2;
    pthread_t tid[threadCount];

    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "create table %s(id integer, time integer, num1 integer) with (time_col = 'time', "
        "interval= '1 hour', compression='fast(rapidlz)');",
        tabelName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int64_t count = 100000;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t num1[count] = {0};

    for (int i = 0; i < count; i++) {
        id[i] = i + 1;
        time[i] = 1695042000 + i;
        num1[i] = 2000000000 - i;
    }

    ret = BlukInsert(stmt, tabelName, count, 3, id, time, num1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    static GmcConnT *conn_1 = NULL;
    static GmcStmtT *stmt_1 = NULL;
    ret = TestTsGmcConnect(&conn_1, &stmt_1, 0);

    // 创建线程一
    pthread_create(&tid[0], NULL, CopyToConcurrent, &stmt);
    // 创建线程二
    pthread_create(&tid[1], NULL, DropCmTableConcurrent, &stmt_1);

    // 开启线程，运行结束后释放句柄
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    GmcFreeStmt(stmt_1);
    GmcDisconnect(conn_1);

    ret = GmcExecDirect(stmt,
    "SELECT id, time, num1 FROM testdb WHERE id >= 2 and time <= 1695042008", 256);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);

    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_UNDEFINED_TABLE, GMERR_UNEXPECTED_NULL_VALUE);
    // 补充删表清理动作
    DropTsTable(stmt, tabelName);
    AW_FUN_Log(LOG_STEP, "test end.");
}
