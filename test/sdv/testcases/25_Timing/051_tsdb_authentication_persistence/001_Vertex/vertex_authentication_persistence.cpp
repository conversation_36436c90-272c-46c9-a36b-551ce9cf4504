extern "C" {}
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "tree_tools.h"

int startNum = 0;
int endNum = 10;
int arrayNum = 3;
int vectorNum = 3;

class vertex_authentication_persistence : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void vertex_authentication_persistence::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/stop.sh");
    system("mkdir -p /data/gmdb");
    system("rm -rf /data/gmdb/*");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"persistentMode=0\"");  // 按需持久化模式
    system("sh $TEST_HOME/tools/modifyCfg.sh \"userPolicyMode=2\"");  // 鉴权模式
    system("sh $TEST_HOME/tools/modifyCfg.sh \"DBA=root:gmrule;gmips;gmids;gmadmin;tsdbdba1;tsdbdba2;tsdbdba3;tsdbdba4;tsdbdba5\"");  // DBA用户
    system("sh $TEST_HOME/tools/start.sh -f ");

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void vertex_authentication_persistence::TearDownTestCase()
{
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

void vertex_authentication_persistence::SetUp()
{
    AW_CHECK_LOG_BEGIN();

    // 导入白名单用户，创建用户或用户组
    ret = system("gmrule -c import_allowlist -f ./alluser.gmuser -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 导入系统权限，赋予白名单用户系统权限
    ret = system("gmrule -c import_policy -f ./allSysPolicy.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropGraphLabel(stmt, g_labelName1);
    EXPECT_TRUE(ret == GMERR_OK || ret == GMERR_UNDEFINED_TABLE);
    ret = GmcDropGraphLabel(stmt, g_labelName2);
    EXPECT_TRUE(ret == GMERR_OK || ret == GMERR_UNDEFINED_TABLE);

    readJanssonFile("./OP_T0.gmjson", &g_testSchema1);
    AW_MACRO_EXPECT_NE_INT((void *)NULL, g_testSchema1);
    ret = GmcCreateVertexLabel(stmt, g_testSchema1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    readJanssonFile("./DST_T0.gmjson", &g_testSchema2);
    AW_MACRO_EXPECT_NE_INT((void *)NULL, g_testSchema2);
    ret = GmcCreateVertexLabel(stmt, g_testSchema2, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void vertex_authentication_persistence::TearDown()
{
    AW_CHECK_LOG_END();

    free(g_testSchema1);
    free(g_testSchema2);
    GmcDropGraphLabel(stmt, g_labelName1);
    GmcDropGraphLabel(stmt, g_labelName2);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 001.进程A创建表，gmrule导入进程B所有系统权限，重启DB，CREATE
TEST_F(vertex_authentication_persistence, Timing_051_001_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./multi_process --gtest_filter=*001 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002.进程A创建表，gmrule导入进程B所有系统权限，重启DB，DROP
TEST_F(vertex_authentication_persistence, Timing_051_001_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./multi_process --gtest_filter=*002 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003.进程A创建表，gmrule导入进程B所有系统权限，重启DB，GET
TEST_F(vertex_authentication_persistence, Timing_051_001_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./multi_process --gtest_filter=*003 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004.进程A创建表，gmrule导入进程B所有系统权限，重启DB、INSERT_ANY
TEST_F(vertex_authentication_persistence, Timing_051_001_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./multi_process --gtest_filter=*004 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005.进程A创建表，gmrule导入进程B所有系统权限，重启DB，UPDATE_ANY
TEST_F(vertex_authentication_persistence, Timing_051_001_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./multi_process --gtest_filter=*005 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006.进程A创建表，gmrule导入进程B所有系统权限，重启DB，DELETE_ANY
TEST_F(vertex_authentication_persistence, Timing_051_001_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./multi_process --gtest_filter=*006 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007.进程A创建表，gmrule导入进程B所有系统权限，重启DB，MERGE_ANY
TEST_F(vertex_authentication_persistence, Timing_051_001_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./multi_process --gtest_filter=*007 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008.进程A创建表，gmrule导入进程B所有系统权限，重启DB，REPLACE_ANY
TEST_F(vertex_authentication_persistence, Timing_051_001_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./multi_process --gtest_filter=*008 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009.进程A创建表，gmrule导入进程B所有系统权限，重启DB，SELECT_ANY
TEST_F(vertex_authentication_persistence, Timing_051_001_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./multi_process --gtest_filter=*009 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010.进程A创建表，gmrule导入进程B所有系统权限，重启DB，ALTER
TEST_F(vertex_authentication_persistence, Timing_051_001_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./multi_process --gtest_filter=*010 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011.进程A创建表，gmrule导入进程B所有系统权限，重启DB，TRUNCATE
TEST_F(vertex_authentication_persistence, Timing_051_001_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./multi_process --gtest_filter=*011 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012.进程A创建表，gmrule导入进程B所有系统权限，然后撤销所有权限，重启DB，CREATE、DROP、GET、INSERT_ANY、UPDATE_ANY、DELETE_ANY、MERGE_ANY、REPLACE_ANY、SELECT_ANY、ALTER、TRUNCATE
TEST_F(vertex_authentication_persistence, Timing_051_001_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 撤销系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c revoke_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./multi_process --gtest_filter=*012 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_ADD_ERRNUM_WHITE_LIST(1, 1018004);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013.进程A创建表，gmrule导入进程B所有系统权限，然后撤销部分权限，重启DB，CREATE、DROP、GET、INSERT_ANY、UPDATE_ANY、DELETE_ANY、MERGE_ANY、REPLACE_ANY、SELECT_ANY、ALTER、TRUNCATE
TEST_F(vertex_authentication_persistence, Timing_051_001_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 撤销系统权限，赋予白名单用户系统权限
    ret = system(
        "gmrule -c revoke_policy -f ./allSysPolicy_multi_process_part.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./multi_process --gtest_filter=*013 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_ADD_ERRNUM_WHITE_LIST(1, 1018004);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014.进程A创建表，gmrule导入进程B所有系统权限，撤销所有权限，重启DB，gmrule导入进程B所有系统权限，CREATE
TEST_F(vertex_authentication_persistence, Timing_051_001_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 撤销系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c revoke_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 重新导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 多进程
    ret = system("./multi_process --gtest_filter=*001 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015.进程A创建表，gmrule导入进程B所有系统权限，撤销所有权限，重启DB，gmrule导入进程B所有系统权限，DROP
TEST_F(vertex_authentication_persistence, Timing_051_001_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 撤销系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c revoke_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 重新导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 多进程
    ret = system("./multi_process --gtest_filter=*002 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016.进程A创建表，gmrule导入进程B所有系统权限，撤销所有权限，重启DB，gmrule导入进程B所有系统权限，GET
TEST_F(vertex_authentication_persistence, Timing_051_001_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 撤销系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c revoke_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 重新导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 多进程
    ret = system("./multi_process --gtest_filter=*003 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 017.进程A创建表，gmrule导入进程B所有系统权限，撤销所有权限，重启DB，gmrule导入进程B所有系统权限，INSERT_ANY
TEST_F(vertex_authentication_persistence, Timing_051_001_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 撤销系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c revoke_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 重新导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 多进程
    ret = system("./multi_process --gtest_filter=*004 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018.进程A创建表，gmrule导入进程B所有系统权限，撤销所有权限，重启DB，gmrule导入进程B所有系统权限，UPDATE_ANY
TEST_F(vertex_authentication_persistence, Timing_051_001_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 撤销系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c revoke_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 重新导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 多进程
    ret = system("./multi_process --gtest_filter=*005 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 019.进程A创建表，gmrule导入进程B所有系统权限，撤销所有权限，重启DB，gmrule导入进程B所有系统权限，DELETE_ANY
TEST_F(vertex_authentication_persistence, Timing_051_001_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 撤销系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c revoke_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 重新导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 多进程
    ret = system("./multi_process --gtest_filter=*006 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 020.进程A创建表，gmrule导入进程B所有系统权限，撤销所有权限，重启DB，gmrule导入进程B所有系统权限，MERGE_ANY
TEST_F(vertex_authentication_persistence, Timing_051_001_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 撤销系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c revoke_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 重新导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 多进程
    ret = system("./multi_process --gtest_filter=*007 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 021.进程A创建表，gmrule导入进程B所有系统权限，撤销所有权限，重启DB，gmrule导入进程B所有系统权限，REPLACE_ANY
TEST_F(vertex_authentication_persistence, Timing_051_001_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 撤销系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c revoke_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 重新导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 多进程
    ret = system("./multi_process --gtest_filter=*008 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 022.进程A创建表，gmrule导入进程B所有系统权限，撤销所有权限，重启DB，gmrule导入进程B所有系统权限，SELECT_ANY
TEST_F(vertex_authentication_persistence, Timing_051_001_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 撤销系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c revoke_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 重新导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 多进程
    ret = system("./multi_process --gtest_filter=*009 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 023.进程A创建表，gmrule导入进程B所有系统权限，撤销所有权限，重启DB，gmrule导入进程B所有系统权限，ALTER
TEST_F(vertex_authentication_persistence, Timing_051_001_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 撤销系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c revoke_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 重新导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 多进程
    ret = system("./multi_process --gtest_filter=*010 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 024.进程A创建表，gmrule导入进程B所有系统权限，撤销所有权限，重启DB，gmrule导入进程B所有系统权限，TRUNCATE
TEST_F(vertex_authentication_persistence, Timing_051_001_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 撤销系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c revoke_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 重新导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 多进程
    ret = system("./multi_process --gtest_filter=*011 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 025.进程A创建表，gmrule导入进程B所有系统权限，重启DB，继续撤销所有权限，CREATE、DROP、GET、INSERT_ANY、UPDATE_ANY、DELETE_ANY、MERGE_ANY、REPLACE_ANY、SELECT_ANY、ALTER、TRUNCATE
TEST_F(vertex_authentication_persistence, Timing_051_001_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 撤销系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c revoke_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 多进程
    ret = system("./multi_process --gtest_filter=*012 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_ADD_ERRNUM_WHITE_LIST(1, 1018004);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 026.进程A创建表，重启DB，DROP、GET、INSERT_ANY、UPDATE_ANY、DELETE_ANY、MERGE_ANY、REPLACE_ANY、SELECT_ANY、ALTER、TRUNCATE
TEST_F(vertex_authentication_persistence, Timing_051_001_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 撤销系统权限，赋予白名单用户系统权限
    ret = system("gmrule -c revoke_policy -f ./allSysPolicy.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 导入系统权限，赋予白名单用户系统权限
    ret = system("gmrule -c import_policy -f ./allSysPolicy_create.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 系统权限操作
    GmcDropVertexLabel(stmt, g_labelName5);
    readJanssonFile("./multi_process.gmjson", &g_testSchema5);
    AW_MACRO_EXPECT_NE_INT((void *)NULL, g_testSchema5);

    ret = GmcCreateVertexLabel(stmt, g_testSchema5, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName1);
    AW_MACRO_EXPECT_EQ_INT(1018004, ret);

    // truncate
    ret = GmcTruncateVertexLabel(stmt, g_labelName1);
    AW_MACRO_EXPECT_EQ_INT(1018004, ret);

    // 对象权限操作
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName1, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(1018004, ret);

    AW_ADD_ERRNUM_WHITE_LIST(1, 1018004);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 027.进程A创建表，gmrule导入进程B所有对象权限，重启DB，INSERT
TEST_F(vertex_authentication_persistence, Timing_051_001_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcCreateNamespace(stmt, g_namespace1, NULL));
    // 导入对象权限，赋予白名单用户系统权限
    ret = system("gmrule -c import_policy -f ./allObjPolicy.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./multi_process --gtest_filter=*004 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 028.进程A创建表，gmrule导入进程B所有对象权限，重启DB，UPDATE
TEST_F(vertex_authentication_persistence, Timing_051_001_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcCreateNamespace(stmt, g_namespace1, NULL));
    // 导入对象权限，赋予白名单用户系统权限
    ret = system("gmrule -c import_policy -f ./allObjPolicy.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./multi_process --gtest_filter=*005 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 029.进程A创建表，gmrule导入进程B所有对象权限，重启DB，DELETE
TEST_F(vertex_authentication_persistence, Timing_051_001_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcCreateNamespace(stmt, g_namespace1, NULL));
    // 导入对象权限，赋予白名单用户系统权限
    ret = system("gmrule -c import_policy -f ./allObjPolicy.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./multi_process --gtest_filter=*006 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 030.进程A创建表，gmrule导入进程B所有对象权限，重启DB，MERGE
TEST_F(vertex_authentication_persistence, Timing_051_001_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcCreateNamespace(stmt, g_namespace1, NULL));
    // 导入对象权限，赋予白名单用户系统权限
    ret = system("gmrule -c import_policy -f ./allObjPolicy.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./multi_process --gtest_filter=*007 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 031.进程A创建表，gmrule导入进程B所有对象权限，重启DB，REPLACE
TEST_F(vertex_authentication_persistence, Timing_051_001_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcCreateNamespace(stmt, g_namespace1, NULL));
    // 导入对象权限，赋予白名单用户系统权限
    ret = system("gmrule -c import_policy -f ./allObjPolicy.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./multi_process --gtest_filter=*008 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 032.进程A创建表，gmrule导入进程B所有对象权限，重启DB，SELECT
TEST_F(vertex_authentication_persistence, Timing_051_001_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcCreateNamespace(stmt, g_namespace1, NULL));
    // 导入对象权限，赋予白名单用户系统权限
    ret = system("gmrule -c import_policy -f ./allObjPolicy.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./multi_process --gtest_filter=*009 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 033.进程A创建表，gmrule导入进程B所有对象权限，然后撤销所有权限，重启DB，INSERT、UPDATE、DELETE、REPLACE、SELECT、MERGE
TEST_F(vertex_authentication_persistence, Timing_051_001_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcCreateNamespace(stmt, g_namespace1, NULL));
    // 导入对象权限，赋予白名单用户系统权限
    ret = system("gmrule -c import_policy -f ./allObjPolicy.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 撤销对象权限，赋予白名单用户系统权限
    ret = system("gmrule -c revoke_policy -f ./allObjPolicy.gmpolicy -s usocket:/run/verona/unix_emserver -d");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./multi_process --gtest_filter=*033 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_ADD_ERRNUM_WHITE_LIST(1, 1018004);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 034.进程A创建表，gmrule导入进程B所有对象权限，然后撤销部分权限，重启DB，INSERT、UPDATE、DELETE、REPLACE、SELECT、MERGE
TEST_F(vertex_authentication_persistence, Timing_051_001_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcCreateNamespace(stmt, g_namespace1, NULL));
    // 导入对象权限，赋予白名单用户系统权限
    ret = system("gmrule -c import_policy -f ./allObjPolicy.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 撤销对象权限，赋予白名单用户系统权限
    ret = system("gmrule -c revoke_policy -f ./allObjPolicy_part.gmpolicy -s usocket:/run/verona/unix_emserver -d");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./multi_process --gtest_filter=*034 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_ADD_ERRNUM_WHITE_LIST(1, 1018004);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 035.进程A创建表，重启DB，INSERT、UPDATE、DELETE、REPLACE、SELECT、MERGE
TEST_F(vertex_authentication_persistence, Timing_051_001_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 撤销系统权限，赋予白名单用户系统权限
    ret = system("gmrule -c revoke_policy -f ./allSysPolicy.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 导入系统权限，赋予白名单用户系统权限
    ret = system("gmrule -c import_policy -f ./allSysPolicy_create.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 系统权限操作
    GmcDropVertexLabel(stmt, g_labelName5);
    readJanssonFile("./multi_process.gmjson", &g_testSchema5);
    AW_MACRO_EXPECT_NE_INT((void *)NULL, g_testSchema5);

    ret = GmcCreateVertexLabel(stmt, g_testSchema5, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName1);
    AW_MACRO_EXPECT_EQ_INT(1018004, ret);

    // truncate
    ret = GmcTruncateVertexLabel(stmt, g_labelName1);
    AW_MACRO_EXPECT_EQ_INT(1018004, ret);

    // 对象权限操作
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName1, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(1018004, ret);

    AW_ADD_ERRNUM_WHITE_LIST(1, 1018004);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 036.进程A创建表，gmrule只导入进程B系统权限INSERT_ANY，重启DB，GET
TEST_F(vertex_authentication_persistence, Timing_051_001_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcCreateNamespace(stmt, g_namespace1, NULL));

    // 导入对象权限，赋予白名单用户系统权限
    ret = system("gmrule -c import_policy -f ./allSysPolicy_insert_any.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./multi_process --gtest_filter=*009 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 037.进程A创建表，gmrule只导入对象权限INSERT，重启DB，GET
TEST_F(vertex_authentication_persistence, Timing_051_001_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcCreateNamespace(stmt, g_namespace1, NULL));
    // 导入对象权限，赋予白名单用户系统权限
    ret = system("gmrule -c import_policy -f ./allObjPolicy_insert.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 插入数据
    TestGmcInsertVertex(stmt, 1, 0, (char *)"string", startNum, endNum, arrayNum, vectorNum, g_labelName1);

    // 读取数据
    TestGmcDirectFetchVertex(
        stmt, 1, 0, (char *)"string", startNum, endNum, arrayNum, vectorNum, g_labelName1, g_lalableNamePK1, true);

    AW_ADD_ERRNUM_WHITE_LIST(2, 1004001, 1001000);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 038.DBA 用户，重启DB，CREATE
TEST_F(vertex_authentication_persistence, Timing_051_001_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./tsdbdba2 --gtest_filter=*001 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 039.DBA 用户，重启DB，DROP
TEST_F(vertex_authentication_persistence, Timing_051_001_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./tsdbdba2 --gtest_filter=*002 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 040.DBA 用户，重启DB，GET
TEST_F(vertex_authentication_persistence, Timing_051_001_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./tsdbdba2 --gtest_filter=*003 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 041.DBA 用户，重启DB，INSERT_ANY
TEST_F(vertex_authentication_persistence, Timing_051_001_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./tsdbdba2 --gtest_filter=*004 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 042.DBA 用户，重启DB，UPDATE_ANY
TEST_F(vertex_authentication_persistence, Timing_051_001_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./tsdbdba2 --gtest_filter=*005 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 043.DBA 用户，重启DB，DELETE_ANY
TEST_F(vertex_authentication_persistence, Timing_051_001_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./tsdbdba2 --gtest_filter=*006 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 044.DBA 用户，重启DB，MERGE_ANY
TEST_F(vertex_authentication_persistence, Timing_051_001_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./tsdbdba2 --gtest_filter=*007 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 045.DBA 用户，重启DB，REPLACE_ANY
TEST_F(vertex_authentication_persistence, Timing_051_001_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./tsdbdba2 --gtest_filter=*008 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 046.DBA 用户，重启DB，SELECT_ANY
TEST_F(vertex_authentication_persistence, Timing_051_001_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./tsdbdba2 --gtest_filter=*009 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 047.DBA 用户，重启DB，ALTER
TEST_F(vertex_authentication_persistence, Timing_051_001_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./tsdbdba2 --gtest_filter=*010 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 048.DBA 用户，重启DB，TRUNCATE
TEST_F(vertex_authentication_persistence, Timing_051_001_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./tsdbdba2 --gtest_filter=*011 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 049.DBA 用户对象权限，重启DB，INSERT
TEST_F(vertex_authentication_persistence, Timing_051_001_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./tsdbdba2 --gtest_filter=*004 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 050.DBA 用户对象权限，重启DB，UPDATE
TEST_F(vertex_authentication_persistence, Timing_051_001_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./tsdbdba2 --gtest_filter=*005 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 051.DBA 用户对象权限，重启DB，DELETE
TEST_F(vertex_authentication_persistence, Timing_051_001_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./tsdbdba2 --gtest_filter=*006 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 052.DBA 用户对象权限，重启DB，MERGE
TEST_F(vertex_authentication_persistence, Timing_051_001_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./tsdbdba2 --gtest_filter=*007 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 053.DBA 用户对象权限，重启DB，REPLACE
TEST_F(vertex_authentication_persistence, Timing_051_001_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./tsdbdba2 --gtest_filter=*008 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 054.DBA 用户对象权限，重启DB，SELECT
TEST_F(vertex_authentication_persistence, Timing_051_001_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./tsdbdba2 --gtest_filter=*009 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 055.进程A创建表，gmrule导入进程B所有系统权限，重启DB，CREATE
TEST_F(vertex_authentication_persistence, Timing_051_001_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./multi_process --gtest_filter=*055 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 056.进程A创建表，gmrule导入进程B所有系统权限，重启DB，DROP
TEST_F(vertex_authentication_persistence, Timing_051_001_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./multi_process --gtest_filter=*056 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 057.进程A创建表，gmrule导入进程B所有系统权限，重启DB，ALTER
TEST_F(vertex_authentication_persistence, Timing_051_001_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./multi_process --gtest_filter=*057 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 058.进程A创建表，gmrule导入进程B所有系统权限，重启DB，USE权限验证
TEST_F(vertex_authentication_persistence, Timing_051_001_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./multi_process --gtest_filter=*058 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 059.进程A创建表，gmrule导入进程B所有系统权限，撤销所有权限，重启DB，CREATE、DROP、ALTER、USE权限验证
TEST_F(vertex_authentication_persistence, Timing_051_001_059)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 撤销系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c revoke_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./multi_process --gtest_filter=*059 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1018004);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 060.进程A创建表，gmrule导入进程B所有系统权限，撤销部分权限，重启DB，CREATE、DROP、ALTER、USE权限验证
TEST_F(vertex_authentication_persistence, Timing_051_001_060)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 撤销系统权限，赋予白名单用户系统权限
    ret = system(
        "gmrule -c revoke_policy -f ./allSysPolicy_multi_process_part.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./multi_process --gtest_filter=*060 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1018004);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 061.DBA 用户，重启DB，CREATE
TEST_F(vertex_authentication_persistence, Timing_051_001_061)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./tsdbdba2 --gtest_filter=*055 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 062.DBA 用户，重启DB，DROP
TEST_F(vertex_authentication_persistence, Timing_051_001_062)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./tsdbdba2 --gtest_filter=*056 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 063.DBA 用户，重启DB，ALTER
TEST_F(vertex_authentication_persistence, Timing_051_001_063)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./tsdbdba2 --gtest_filter=*057 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 064.进程A创建表，gmrule导入进程B所有系统权限，重启DB，CREATE
TEST_F(vertex_authentication_persistence, Timing_051_001_064)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./multi_process --gtest_filter=*064 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 065.进程A创建表，gmrule导入进程B所有系统权限，重启DB，DROP
TEST_F(vertex_authentication_persistence, Timing_051_001_065)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./multi_process --gtest_filter=*065 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 066.进程A创建表，gmrule导入进程B所有系统权限，重启DB，GET
TEST_F(vertex_authentication_persistence, Timing_051_001_066)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./multi_process --gtest_filter=*066 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 067.进程A创建表，gmrule导入进程B所有系统权限，撤销所有权限，重启DB，CREATE、DROP、GET
TEST_F(vertex_authentication_persistence, Timing_051_001_067)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 撤销系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c revoke_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./multi_process --gtest_filter=*067 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_ADD_ERRNUM_WHITE_LIST(1, 1018004);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 068.进程A创建表，gmrule导入进程B所有系统权限，撤销部分权限，重启DB，CREATE、DROP、GET
TEST_F(vertex_authentication_persistence, Timing_051_001_068)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 撤销系统权限，赋予白名单用户系统权限
    ret = system(
        "gmrule -c revoke_policy -f ./allSysPolicy_multi_process_part.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./multi_process --gtest_filter=*068s >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_ADD_ERRNUM_WHITE_LIST(1, 1018004);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 069.DBA 用户，重启DB，CREATE
TEST_F(vertex_authentication_persistence, Timing_051_001_069)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./tsdbdba2 --gtest_filter=*064 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 070.DBA 用户，重启DB，DROP
TEST_F(vertex_authentication_persistence, Timing_051_001_070)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./tsdbdba2 --gtest_filter=*065 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 071.DBA 用户，重启DB，GET
TEST_F(vertex_authentication_persistence, Timing_051_001_071)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./tsdbdba2 --gtest_filter=*066 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 072.进程A创建表，gmrule导入进程B所有系统权限，写入数据，重启DB，CREATE、DROP、GET
TEST_F(vertex_authentication_persistence, Timing_051_001_072)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 创建kv表
    ret = GmcKvCreateTable(stmt, g_tableName, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 打开kv表
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // kv表中插入数据
    GmcKvTupleT kvInfo = {0};
    char key[] = "apple";
    uint32_t value = 30;
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(uint32_t);
    ret = GmcKvSet(stmt, key, strlen(key), &value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询kv表中记录数
    uint32_t count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, count);

    // 导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./multi_process --gtest_filter=*072 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 073.进程A创建表，gmrule导入进程B所有系统权限，撤销全部权限，写入数据，重启DB，CREATE、DROP、GET
TEST_F(vertex_authentication_persistence, Timing_051_001_073)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 创建kv表
    ret = GmcKvCreateTable(stmt, g_tableName, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 打开kv表
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // kv表中插入数据
    GmcKvTupleT kvInfo = {0};
    char key[] = "apple";
    uint32_t value = 30;
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(uint32_t);
    ret = GmcKvSet(stmt, key, strlen(key), &value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询kv表中记录数
    uint32_t count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, count);

    // 导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 撤销系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c revoke_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./multi_process --gtest_filter=*073 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_ADD_ERRNUM_WHITE_LIST(1, 1018004);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 073.进程A创建表，gmrule导入进程B所有系统权限，撤销部分权限，写入数据，重启DB，CREATE、DROP、GET
TEST_F(vertex_authentication_persistence, Timing_051_001_074)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 创建kv表
    ret = GmcKvCreateTable(stmt, g_tableName, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 打开kv表
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // kv表中插入数据
    GmcKvTupleT kvInfo = {0};
    char key[] = "apple";
    uint32_t value = 30;
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(uint32_t);
    ret = GmcKvSet(stmt, key, strlen(key), &value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询kv表中记录数
    uint32_t count = 0;
    ret = GmcKvTableRecordCount(stmt, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, count);

    // 导入系统权限，赋予白名单用户系统权限
    ret =
        system("gmrule -c import_policy -f ./allSysPolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 撤销系统权限，赋予白名单用户系统权限
    ret = system(
        "gmrule -c revoke_policy -f ./allSysPolicy_multi_process_part.gmpolicy -s usocket:/run/verona/unix_emserver");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DBA用户落盘
    ret = system("./tsdbdba2 --gtest_filter=*000 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    testEnvClean();  // 重启之前释放锁，清理环境等
    system("ps -ef |grep gmserver");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ps -ef |grep gmserver");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&conn, &stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009012);  // testEnvInit会重复导入白名单

    // 多进程
    ret = system("./multi_process --gtest_filter=*074 >test.log");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != 0) {
        system("cat test.log");
    }
    sleep(3);

    AW_ADD_ERRNUM_WHITE_LIST(1, 1018004);
    AW_FUN_Log(LOG_STEP, "test end.");
}
