/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2025. All rights reserved.
 * Description: 【技术转交付】TSDB colset 测试
 * Author: qinjianhua
 * Create: 2024-05-15
 */

#include "gtest/gtest.h"
#include "t_rd_common.h"
#include "../../common/include/component/t_rd_ts.h"

char tabelName[] = "testdb";

static GmcConnOptionsT *connOptions = NULL;
static GmcConnT *conn = NULL;
static GmcStmtT *stmt = NULL;
Status ret = 0;

typedef struct {
    char (*name)[256];
    int64_t *age;
    int64_t *id;
    int64_t *worktime;
    int64_t *salary;
    uint32_t rowNum;
} Data;

class tsdb_colset_basic_enableVectorizedPushDown : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableVectorizedPushDown=1\"");  // 下推开启
        InitTsCiCfg();
        ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableVectorizedPushDown=0\"");  // 下推关闭
        close_epoll_thread();
        testEnvClean();
        RecoverTsCiCfg();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void CreateTableAndBulkInsert(GmcStmtT *stmt, Data data, const char *tableName)
{
    assert(tableName != NULL);
    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(256), age integer, id integer, worktime integer, salary integer) "
        "with (time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        tableName);
    Status ret = GmcExecDirect(stmt, ddlCommand, 256);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data.rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data.id, sizeof(data.id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data.age, sizeof(data.age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, data.worktime, sizeof(data.worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, data.salary, sizeof(data.salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, data.name, 256, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
}
void ExecQueryCmd(GmcStmtT *stmt, const char *queryCommand)
{
    assert(queryCommand != NULL);
    Status ret = GmcExecDirect(stmt, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
}
int DropCmTable(char *tableName)
{
    int ret = 0;
    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    return ret;
}

void tsdb_colset_basic_enableVectorizedPushDown::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char names[][256] = {"david", "nut", "bob", "olivia", "tim", "lucy", "lucy1", "lucy2", "lucy3", "lucy4", "lucy5",
        "lucy6", "lucy7", "lucy8"};
    int64_t ages[] = {1, 1, 29, 30, 19, 23, 45, 34, 34, 34, 34, 34, 99, 99};
    int64_t ids[] = {0, 0, 1, 1, 2, 2, 3, 2, 2, 2, 2, 2, 4, 4};
    int64_t worktimes[] = {0, 0, 24, 24, 11, 11, 12, 11, 11, 11, 11, 11, 0, 0};
    int64_t salaries[] = {0, 0, 20000, 10000, 4000, 30000, 31000, 10000, 10000, 10000, 10000, 10000, 0, 0};
    uint32_t rowNum = 14;

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    CreateTableAndBulkInsert(stmt, data, "tablequery4");
}

void tsdb_colset_basic_enableVectorizedPushDown::TearDown()
{
    ret = DropCmTable("tablequery4");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcConnOptionsDestroy(connOptions);
    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
}

class tsdb_colset_basic : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        InitTsCiCfg();
        ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        close_epoll_thread();
        testEnvClean();
        RecoverTsCiCfg();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void tsdb_colset_basic::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char names[][256] = {"david", "nut", "bob", "olivia", "tim", "lucy", "lucy1", "lucy2", "lucy3", "lucy4", "lucy5",
        "lucy6", "lucy7", "lucy8"};
    int64_t ages[] = {1, 1, 29, 30, 19, 23, 45, 34, 34, 34, 34, 34, 99, 99};
    int64_t ids[] = {0, 0, 1, 1, 2, 2, 3, 2, 2, 2, 2, 2, 4, 4};
    int64_t worktimes[] = {0, 0, 24, 24, 11, 11, 12, 11, 11, 11, 11, 11, 0, 0};
    int64_t salaries[] = {0, 0, 20000, 10000, 4000, 30000, 31000, 10000, 10000, 10000, 10000, 10000, 0, 0};
    uint32_t rowNum = 14;

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    CreateTableAndBulkInsert(stmt, data, "tablequery4");
}

void tsdb_colset_basic::TearDown()
{
    ret = DropCmTable("tablequery4");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcConnOptionsDestroy(connOptions);
    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
}

// 001、单一字段分组聚合字段去重
TEST_F(tsdb_colset_basic, Timing_027_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *queryCommand = "select worktime, colset(salary) from "
                               "tablequery4 group by worktime order by worktime;";

    ExecQueryCmd(stmt, queryCommand);

    // expect result: 0, "0"
    //                11, "4000, 30000, 10000"
    //                12, "31000"
    //                24, "20000, 10000"
    uint32_t propSize = 0;
    bool eof = false;
    int64_t worktimeRes = 0;
    char res[256] = {0};
    char *res0 = "0";
    char *res1 = "4000, 30000, 10000";
    char *res2 = "31000";
    char *res3 = "20000, 10000";
    bool isNull = false;

    Status ret = GMERR_OK;
    uint32_t i = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = 19;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &res, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        switch (i) {
            case 0: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 0);
                AW_MACRO_ASSERT_EQ_STR(res, res0);
                break;
            }
            case 1: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 11);
                AW_MACRO_ASSERT_EQ_STR(res, res1);
                break;
            }
            case 2: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 12);
                AW_MACRO_ASSERT_EQ_STR(res, res2);
                break;
            }
            case 3: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 24);
                AW_MACRO_ASSERT_EQ_STR(res, res3);
                break;
            }
            default:
                AW_MACRO_EXPECT_EQ_INT(1, 0);
        }
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)4);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002、多字段分组聚合字段去重
TEST_F(tsdb_colset_basic, Timing_027_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *queryCommand = "select worktime, age,colset(salary) from "
                               "tablequery4 group by worktime,age order by worktime,age;";
    ExecQueryCmd(stmt, queryCommand);

    uint32_t propSize = 0;
    bool eof = false;
    int64_t worktimeRes = 0;
    int64_t ageRes = 0;
    char res[256] = {0};
    char *res0 = "0";
    char *res1 = "4000";
    char *res2 = "30000";
    char *res3 = "10000";
    char *res4 = "31000";
    char *res5 = "20000";
    char *res6 = "10000";
    bool isNull = false;

    Status ret = GMERR_OK;
    uint32_t i = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &ageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = 19;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &res, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        switch (i) {
            case 0: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 0);
                AW_MACRO_EXPECT_EQ_INT(ageRes, 1);
                AW_MACRO_ASSERT_EQ_STR(res, res0);
                break;
            }
            case 1: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 0);
                AW_MACRO_EXPECT_EQ_INT(ageRes, 99);
                AW_MACRO_ASSERT_EQ_STR(res, res0);
                break;
            }
            case 2: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 11);
                AW_MACRO_EXPECT_EQ_INT(ageRes, 19);
                AW_MACRO_ASSERT_EQ_STR(res, res1);
                break;
            }
            case 3: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 11);
                AW_MACRO_EXPECT_EQ_INT(ageRes, 23);
                AW_MACRO_ASSERT_EQ_STR(res, res2);
                break;
            }
            case 4: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 11);
                AW_MACRO_EXPECT_EQ_INT(ageRes, 34);
                AW_MACRO_ASSERT_EQ_STR(res, res3);
                break;
            }
            case 5: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 12);
                AW_MACRO_EXPECT_EQ_INT(ageRes, 45);
                AW_MACRO_ASSERT_EQ_STR(res, res4);
                break;
            }
            case 6: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 24);
                AW_MACRO_EXPECT_EQ_INT(ageRes, 29);
                AW_MACRO_ASSERT_EQ_STR(res, res5);
                break;
            }
            case 7: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 24);
                AW_MACRO_EXPECT_EQ_INT(ageRes, 30);
                AW_MACRO_ASSERT_EQ_STR(res, res6);
                break;
            }
            default:
                AW_MACRO_EXPECT_EQ_INT(1, 0);
        }
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)8);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003、大写COLSET聚合字段去重
TEST_F(tsdb_colset_basic, Timing_027_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *queryCommand = "select worktime, COLSET(salary) from "
                               "tablequery4 group by worktime order by worktime;";

    ExecQueryCmd(stmt, queryCommand);

    uint32_t propSize = 0;
    bool eof = false;
    int64_t worktimeRes = 0;
    char res[256] = {0};
    char *res0 = "0";
    char *res1 = "4000, 30000, 10000";
    char *res2 = "31000";
    char *res3 = "20000, 10000";
    bool isNull = false;

    Status ret = GMERR_OK;
    uint32_t i = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = 19;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &res, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        switch (i) {
            case 0: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 0);
                AW_MACRO_ASSERT_EQ_STR(res, res0);
                break;
            }
            case 1: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 11);
                AW_MACRO_ASSERT_EQ_STR(res, res1);
                break;
            }
            case 2: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 12);
                AW_MACRO_ASSERT_EQ_STR(res, res2);
                break;
            }
            case 3: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 24);
                AW_MACRO_ASSERT_EQ_STR(res, res3);
                break;
            }
            default:
                AW_MACRO_EXPECT_EQ_INT(1, 0);
        }
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)4);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004、colset与max组合使用
TEST_F(tsdb_colset_basic, Timing_027_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *queryCommand = "select worktime, colset(salary), max(age) from "
                               "tablequery4 group by worktime order by worktime;";

    ExecQueryCmd(stmt, queryCommand);

    uint32_t propSize = 0;
    bool eof = false;
    int64_t worktimeRes = 0;
    int64_t Res = 0;
    char res[256] = {0};
    char *res0 = "0";
    char *res1 = "4000, 30000, 10000";
    char *res2 = "31000";
    char *res3 = "20000, 10000";
    bool isNull = false;

    Status ret = GMERR_OK;
    uint32_t i = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = 19;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &res, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &Res, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        switch (i) {
            case 0: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 0);
                AW_MACRO_ASSERT_EQ_STR(res, res0);
                AW_MACRO_EXPECT_EQ_INT(Res, 99);
                break;
            }
            case 1: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 11);
                AW_MACRO_ASSERT_EQ_STR(res, res1);
                AW_MACRO_EXPECT_EQ_INT(Res, 34);
                break;
            }
            case 2: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 12);
                AW_MACRO_ASSERT_EQ_STR(res, res2);
                AW_MACRO_EXPECT_EQ_INT(Res, 45);
                break;
            }
            case 3: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 24);
                AW_MACRO_ASSERT_EQ_STR(res, res3);
                AW_MACRO_EXPECT_EQ_INT(Res, 30);
                break;
            }
            default:
                AW_MACRO_EXPECT_EQ_INT(1, 0);
        }
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)4);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004、colset与mim组合使用
TEST_F(tsdb_colset_basic, Timing_027_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *queryCommand = "select worktime, colset(salary), min(age) from "
                               "tablequery4 group by worktime order by worktime;";

    ExecQueryCmd(stmt, queryCommand);

    uint32_t propSize = 0;
    bool eof = false;
    int64_t worktimeRes = 0;
    int64_t Res = 0;
    char res[256] = {0};
    char *res0 = "0";
    char *res1 = "4000, 30000, 10000";
    char *res2 = "31000";
    char *res3 = "20000, 10000";
    bool isNull = false;

    Status ret = GMERR_OK;
    uint32_t i = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = 19;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &res, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &Res, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        switch (i) {
            case 0: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 0);
                AW_MACRO_ASSERT_EQ_STR(res, res0);
                AW_MACRO_EXPECT_EQ_INT(Res, 1);
                break;
            }
            case 1: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 11);
                AW_MACRO_ASSERT_EQ_STR(res, res1);
                AW_MACRO_EXPECT_EQ_INT(Res, 19);
                break;
            }
            case 2: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 12);
                AW_MACRO_ASSERT_EQ_STR(res, res2);
                AW_MACRO_EXPECT_EQ_INT(Res, 45);
                break;
            }
            case 3: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 24);
                AW_MACRO_ASSERT_EQ_STR(res, res3);
                AW_MACRO_EXPECT_EQ_INT(Res, 29);
                break;
            }
            default:
                AW_MACRO_EXPECT_EQ_INT(1, 0);
        }
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)4);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006、colset与count(age)组合使用
TEST_F(tsdb_colset_basic, Timing_027_006)
{

    AW_FUN_Log(LOG_STEP, "test start.");

    const char *queryCommand = "select worktime, colset(salary), count(age) from "
                               "tablequery4 group by worktime order by worktime;";

    ExecQueryCmd(stmt, queryCommand);

    uint32_t propSize = 0;
    bool eof = false;
    int64_t worktimeRes = 0;
    int64_t Res = 0;
    char res[256] = {0};
    char *res0 = "0";
    char *res1 = "4000, 30000, 10000";
    char *res2 = "31000";
    char *res3 = "20000, 10000";
    bool isNull = false;

    Status ret = GMERR_OK;
    uint32_t i = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = 19;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &res, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &Res, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        switch (i) {
            case 0: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 0);
                AW_MACRO_ASSERT_EQ_STR(res, res0);
                AW_MACRO_EXPECT_EQ_INT(Res, 4);
                break;
            }
            case 1: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 11);
                AW_MACRO_ASSERT_EQ_STR(res, res1);
                AW_MACRO_EXPECT_EQ_INT(Res, 7);
                break;
            }
            case 2: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 12);
                AW_MACRO_ASSERT_EQ_STR(res, res2);
                AW_MACRO_EXPECT_EQ_INT(Res, 1);
                break;
            }
            case 3: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 24);
                AW_MACRO_ASSERT_EQ_STR(res, res3);
                AW_MACRO_EXPECT_EQ_INT(Res, 2);
                break;
            }
            default:
                AW_MACRO_EXPECT_EQ_INT(1, 0);
        }
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)4);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006、colset与sum()组合使用
TEST_F(tsdb_colset_basic, Timing_027_007)
{

    AW_FUN_Log(LOG_STEP, "test start.");

    const char *queryCommand = "select worktime, colset(salary), sum(worktime+salary) from "
                               "tablequery4 group by worktime order by worktime;";

    ExecQueryCmd(stmt, queryCommand);

    uint32_t propSize = 0;
    bool eof = false;
    int64_t worktimeRes = 0;
    int64_t Res = 0;
    char res[256] = {0};
    char *res0 = "0";
    char *res1 = "4000, 30000, 10000";
    char *res2 = "31000";
    char *res3 = "20000, 10000";
    bool isNull = false;

    Status ret = GMERR_OK;
    uint32_t i = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = 19;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &res, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &Res, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        switch (i) {
            case 0: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 0);
                AW_MACRO_ASSERT_EQ_STR(res, res0);
                AW_MACRO_EXPECT_EQ_INT(Res, 0);
                break;
            }
            case 1: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 11);
                AW_MACRO_ASSERT_EQ_STR(res, res1);
                AW_MACRO_EXPECT_EQ_INT(Res, 84077);
                break;
            }
            case 2: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 12);
                AW_MACRO_ASSERT_EQ_STR(res, res2);
                AW_MACRO_EXPECT_EQ_INT(Res, 31012);
                break;
            }
            case 3: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 24);
                AW_MACRO_ASSERT_EQ_STR(res, res3);
                AW_MACRO_EXPECT_EQ_INT(Res, 30048);
                break;
            }
            default:
                AW_MACRO_EXPECT_EQ_INT(1, 0);
        }
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)4);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008、大量重复数据(1w)去重
TEST_F(tsdb_colset_basic, Timing_027_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    DropCmTable("tablequery4");

    constexpr int count = 10000;
    int64_t worktimes[count];
    int64_t salaries[count];

    for (int i = 0; i < count; i++) {
        worktimes[i] = 10;
        salaries[i] = 10;
    }

    char names[count][256] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[count] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t ids[count] = {1, 2, 4, 9, 8, 14};

    uint32_t rowNum = count;

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    CreateTableAndBulkInsert(stmt, data, "tablequery4");

    const char *queryCommand = "select worktime, colset(salary) from "
                               "tablequery4 group by worktime order by worktime;";

    ExecQueryCmd(stmt, queryCommand);

    uint32_t propSize = 0;
    bool eof = false;
    int64_t worktimeRes = 0;
    char res[256] = {0};
    char *res0 = "10";
    bool isNull = false;

    Status ret = GMERR_OK;
    uint32_t i = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = 19;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &res, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        switch (i) {
            case 0: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 10);
                AW_MACRO_ASSERT_EQ_STR(res, res0);
                break;
            }
            default:
                AW_MACRO_EXPECT_EQ_INT(1, 0);
        }
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)1);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009、列数据存在空值去重
TEST_F(tsdb_colset_basic, Timing_027_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    DropCmTable("tablequery4");

    constexpr int count = 10;
    int64_t worktimes[count];
    int64_t salaries[count];

    for (int i = 0; i < count; i++) {
        worktimes[i] = 10;
        salaries[i] = 10;
    }

    char names[count][256] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[count] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t ids[count] = {1, 2, 4, 9, 8, 14};

    uint32_t rowNum = count;

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    CreateTableAndBulkInsert(stmt, data, "tablequery4");

    const char *queryCommand = "select worktime, colset(id) from "
                               "tablequery4 group by worktime order by worktime;";

    ExecQueryCmd(stmt, queryCommand);

    uint32_t propSize = 0;
    bool eof = false;
    int64_t worktimeRes = 0;
    char res[256] = {0};
    char *res0 = "1, 2, 4, 9, 8, 14, 0";
    bool isNull = false;

    Status ret = GMERR_OK;
    uint32_t i = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = 100;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &res, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        switch (i) {
            case 0: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 10);
                AW_MACRO_ASSERT_EQ_STR(res, res0);
                break;
            }
            default:
                AW_MACRO_EXPECT_EQ_INT(1, 0);
        }
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)1);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010、colset对不存在的列去重
TEST_F(tsdb_colset_basic, Timing_027_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *queryCommand = "select worktime, colset(test) from "
                               "tablequery4 group by worktime order by worktime;";

    ret = GmcExecDirect(stmt, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_SEMANTIC_ERROR);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_SEMANTIC_ERROR);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011、colset错误参数去重
TEST_F(tsdb_colset_basic, Timing_027_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *queryCommand = "select worktime, colset() from "
                               "tablequery4 group by worktime order by worktime;";

    ret = GmcExecDirect(stmt, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_SYNTAX_ERROR);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_SYNTAX_ERROR);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012、非聚合语句使用colset
TEST_F(tsdb_colset_basic, Timing_027_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *queryCommand = "select worktime, colset(salary) from "
                               "tablequery4;";

    ret = GmcExecDirect(stmt, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_FEATURE_NOT_SUPPORTED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013、多列去重
TEST_F(tsdb_colset_basic, Timing_027_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *queryCommand = "select worktime, colset(salary,age) from "
                               "tablequery4 group by worktime order by worktime;";

    ret = GmcExecDirect(stmt, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_SYNTAX_ERROR);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_SYNTAX_ERROR);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014、string字段去重
TEST_F(tsdb_colset_basic, Timing_027_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *queryCommand = "select worktime, colset(name) from "
                               "tablequery4 group by worktime order by worktime;";

    ret = GmcExecDirect(stmt, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_FEATURE_NOT_SUPPORTED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015、多个colset函数去重
TEST_F(tsdb_colset_basic, Timing_027_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *queryCommand = "select worktime, colset(salary) ,colset(salary) from "
                               "tablequery4 group by worktime order by worktime;";

    ret = GmcExecDirect(stmt, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    uint32_t propSize = 0;
    bool eof = false;
    int64_t worktimeRes = 0;
    char res[256] = {0};
    char res10[256] = {0};
    char *res0 = "0";
    char *res1 = "4000, 30000, 10000";
    char *res2 = "31000";
    char *res3 = "20000, 10000";
    bool isNull = false;

    Status ret = GMERR_OK;
    uint32_t i = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = 19;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &res, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = 19;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &res10, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        switch (i) {
            case 0: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 0);
                AW_MACRO_ASSERT_EQ_STR(res, res0);
                AW_MACRO_ASSERT_EQ_STR(res10, res0);
                break;
            }
            case 1: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 11);
                AW_MACRO_ASSERT_EQ_STR(res, res1);
                AW_MACRO_ASSERT_EQ_STR(res10, res1);
                break;
            }
            case 2: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 12);
                AW_MACRO_ASSERT_EQ_STR(res, res2);
                AW_MACRO_ASSERT_EQ_STR(res10, res2);
                break;
            }
            case 3: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 24);
                AW_MACRO_ASSERT_EQ_STR(res, res3);
                AW_MACRO_ASSERT_EQ_STR(res10, res3);
                break;
            }
            default:
                AW_MACRO_EXPECT_EQ_INT(1, 0);
        }
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)4);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016、colset在order by中使用
TEST_F(tsdb_colset_basic, Timing_027_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *queryCommand = "select worktime  from "
                               "tablequery4 group by worktime order by colset(salary);";

    ret = GmcExecDirect(stmt, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_SEMANTIC_ERROR);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_SEMANTIC_ERROR);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 017、colset在where中使用
TEST_F(tsdb_colset_basic, Timing_027_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *queryCommand = "select worktime from "
                               "tablequery4 where colset(salary) > 1;";

    ret = GmcExecDirect(stmt, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_FEATURE_NOT_SUPPORTED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 001、int类型字段隐式first
TEST_F(tsdb_colset_basic, Timing_027_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *queryCommand = "select worktime, salary from "
                               "tablequery4 group by worktime order by worktime;";

    ExecQueryCmd(stmt, queryCommand);

    uint32_t propSize = 0;
    bool eof = false;
    int64_t worktimeRes = 0;
    int64_t Res = 0;
    bool isNull = false;

    Status ret = GMERR_OK;
    uint32_t i = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &Res, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        switch (i) {
            case 0: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 0);
                AW_MACRO_EXPECT_EQ_INT(Res, 0);
                break;
            }
            case 1: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 11);
                AW_MACRO_EXPECT_EQ_INT(Res, 4000);
                break;
            }
            case 2: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 12);
                AW_MACRO_EXPECT_EQ_INT(Res, 31000);
                break;
            }
            case 3: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 24);
                AW_MACRO_EXPECT_EQ_INT(Res, 20000);
                break;
            }
            default:
                AW_MACRO_EXPECT_EQ_INT(1, 0);
        }
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)4);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002、string类型字段隐式first
TEST_F(tsdb_colset_basic, Timing_027_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *queryCommand = "select worktime, name from "
                               "tablequery4 group by worktime order by worktime;";

    ExecQueryCmd(stmt, queryCommand);

    uint32_t propSize = 0;
    bool eof = false;
    int64_t worktimeRes = 0;
    int64_t Res = 0;
    char res[256] = {0};
    bool isNull = false;

    Status ret = GMERR_OK;
    uint32_t i = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = 256;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &res, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        switch (i) {
            case 0: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 0);
                AW_MACRO_ASSERT_EQ_STR(res, "david");
                break;
            }
            case 1: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 11);
                AW_MACRO_ASSERT_EQ_STR(res, "tim");
                break;
            }
            case 2: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 12);
                AW_MACRO_ASSERT_EQ_STR(res, "lucy1");
                break;
            }
            case 3: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 24);
                AW_MACRO_ASSERT_EQ_STR(res, "bob");
                break;
            }
            default:
                AW_MACRO_EXPECT_EQ_INT(1, 0);
        }
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)4);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003、隐式first与max组合使用
TEST_F(tsdb_colset_basic, Timing_027_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *queryCommand = "select worktime, salary ,max(age) from "
                               "tablequery4 group by worktime order by worktime;";

    ExecQueryCmd(stmt, queryCommand);

    uint32_t propSize = 0;
    bool eof = false;
    int64_t worktimeRes = 0;
    int64_t Res = 0;
    int64_t Res2 = 0;
    bool isNull = false;

    Status ret = GMERR_OK;
    uint32_t i = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &Res, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &Res2, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        switch (i) {
            case 0: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 0);
                AW_MACRO_EXPECT_EQ_INT(Res, 0);
                AW_MACRO_EXPECT_EQ_INT(Res2, 99);
                break;
            }
            case 1: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 11);
                AW_MACRO_EXPECT_EQ_INT(Res, 4000);
                AW_MACRO_EXPECT_EQ_INT(Res2, 34);
                break;
            }
            case 2: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 12);
                AW_MACRO_EXPECT_EQ_INT(Res, 31000);
                AW_MACRO_EXPECT_EQ_INT(Res2, 45);
                break;
            }
            case 3: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 24);
                AW_MACRO_EXPECT_EQ_INT(Res2, 30);
                break;
            }
            default:
                AW_MACRO_EXPECT_EQ_INT(1, 0);
        }
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)4);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004、隐式first与min组合使用
TEST_F(tsdb_colset_basic, Timing_027_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *queryCommand = "select worktime, salary ,min(age) from "
                               "tablequery4 group by worktime order by worktime;";

    ExecQueryCmd(stmt, queryCommand);

    uint32_t propSize = 0;
    bool eof = false;
    int64_t worktimeRes = 0;
    int64_t Res = 0;
    int64_t Res2 = 0;
    bool isNull = false;

    Status ret = GMERR_OK;
    uint32_t i = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &Res, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &Res2, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        switch (i) {
            case 0: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 0);
                AW_MACRO_EXPECT_EQ_INT(Res, 0);
                AW_MACRO_EXPECT_EQ_INT(Res2, 1);
                break;
            }
            case 1: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 11);
                AW_MACRO_EXPECT_EQ_INT(Res, 4000);
                AW_MACRO_EXPECT_EQ_INT(Res2, 19);
                break;
            }
            case 2: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 12);
                AW_MACRO_EXPECT_EQ_INT(Res, 31000);
                AW_MACRO_EXPECT_EQ_INT(Res2, 45);
                break;
            }
            case 3: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 24);
                AW_MACRO_EXPECT_EQ_INT(Res2, 29);
                break;
            }
            default:
                AW_MACRO_EXPECT_EQ_INT(1, 0);
        }
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)4);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005、隐式first与count(a)组合使用
TEST_F(tsdb_colset_basic, Timing_027_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *queryCommand = "select worktime, salary ,count(age) from "
                               "tablequery4 group by worktime order by worktime;";

    ExecQueryCmd(stmt, queryCommand);

    uint32_t propSize = 0;
    bool eof = false;
    int64_t worktimeRes = 0;
    int64_t Res = 0;
    int64_t Res2 = 0;
    bool isNull = false;

    Status ret = GMERR_OK;
    uint32_t i = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &Res, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &Res2, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        switch (i) {
            case 0: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 0);
                AW_MACRO_EXPECT_EQ_INT(Res, 0);
                AW_MACRO_EXPECT_EQ_INT(Res2, 4);
                break;
            }
            case 1: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 11);
                AW_MACRO_EXPECT_EQ_INT(Res, 4000);
                AW_MACRO_EXPECT_EQ_INT(Res2, 7);
                break;
            }
            case 2: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 12);
                AW_MACRO_EXPECT_EQ_INT(Res, 31000);
                AW_MACRO_EXPECT_EQ_INT(Res2, 1);
                break;
            }
            case 3: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 24);
                AW_MACRO_EXPECT_EQ_INT(Res2, 2);
                break;
            }
            default:
                AW_MACRO_EXPECT_EQ_INT(1, 0);
        }
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)4);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006、隐式first与sum()组合使用
TEST_F(tsdb_colset_basic, Timing_027_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *queryCommand = "select worktime, salary ,sum(worktime+salary) from "
                               "tablequery4 group by worktime order by worktime;";

    ExecQueryCmd(stmt, queryCommand);

    uint32_t propSize = 0;
    bool eof = false;
    int64_t worktimeRes = 0;
    int64_t Res = 0;
    int64_t Res2 = 0;
    bool isNull = false;

    Status ret = GMERR_OK;
    uint32_t i = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &Res, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &Res2, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        switch (i) {
            case 0: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 0);
                AW_MACRO_EXPECT_EQ_INT(Res, 0);
                AW_MACRO_EXPECT_EQ_INT(Res2, 0);
                break;
            }
            case 1: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 11);
                AW_MACRO_EXPECT_EQ_INT(Res, 4000);
                AW_MACRO_EXPECT_EQ_INT(Res2, 84077);
                break;
            }
            case 2: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 12);
                AW_MACRO_EXPECT_EQ_INT(Res, 31000);
                AW_MACRO_EXPECT_EQ_INT(Res2, 31012);
                break;
            }
            case 3: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 24);
                AW_MACRO_EXPECT_EQ_INT(Res2, 30048);
                break;
            }
            default:
                AW_MACRO_EXPECT_EQ_INT(1, 0);
        }
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)4);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007、隐式first列没有数据
TEST_F(tsdb_colset_basic, Timing_027_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    DropCmTable("tablequery4");

    constexpr int count = 10;
    int64_t worktimes[count];

    for (int i = 0; i < count; i++) {
        worktimes[i] = 10;
    }

    int64_t salaries[count] = {};
    char names[count][256] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[count] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t ids[count] = {1, 2, 4, 9, 8, 14};

    uint32_t rowNum = count;

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    CreateTableAndBulkInsert(stmt, data, "tablequery4");

    const char *queryCommand = "select worktime, salary from "
                               "tablequery4 group by worktime order by worktime;";

    ExecQueryCmd(stmt, queryCommand);

    uint32_t propSize = 0;
    bool eof = false;
    int64_t worktimeRes = 0;
    int64_t Res = 0;
    bool isNull = false;

    Status ret = GMERR_OK;
    uint32_t i = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &Res, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        switch (i) {
            case 0: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 10);
                AW_MACRO_EXPECT_EQ_INT(Res, 0);
                break;
            }
            default:
                AW_MACRO_EXPECT_EQ_INT(1, 0);
        }
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)1);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008、隐式first列不存在
TEST_F(tsdb_colset_basic, Timing_027_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *queryCommand = "select worktime, test from "
                               "tablequery4 group by worktime order by worktime;";

    ret = GmcExecDirect(stmt, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_SEMANTIC_ERROR);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_SEMANTIC_ERROR);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009、隐式first不支持下推
TEST_F(tsdb_colset_basic_enableVectorizedPushDown, Timing_027_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *queryCommand = "select worktime, salary from "
                               "tablequery4 group by worktime order by worktime;";

    ExecQueryCmd(stmt, queryCommand);

    uint32_t propSize = 0;
    bool eof = false;
    int64_t worktimeRes = 0;
    int64_t Res = 0;
    bool isNull = false;

    Status ret = GMERR_OK;
    uint32_t i = 0;
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &Res, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        switch (i) {
            case 0: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 0);
                AW_MACRO_EXPECT_EQ_INT(Res, 0);
                break;
            }
            case 1: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 11);
                AW_MACRO_EXPECT_EQ_INT(Res, 4000);
                break;
            }
            case 2: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 12);
                AW_MACRO_EXPECT_EQ_INT(Res, 31000);
                break;
            }
            case 3: {
                AW_MACRO_EXPECT_EQ_INT(worktimeRes, 24);
                AW_MACRO_EXPECT_EQ_INT(Res, 20000);
                break;
            }
            default:
                AW_MACRO_EXPECT_EQ_INT(1, 0);
        }
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)4);

    AW_FUN_Log(LOG_STEP, "test end.");
}
