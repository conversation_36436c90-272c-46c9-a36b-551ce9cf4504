/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 Description  : TSDB支持SQLite数据导入工具
 Notes        : 工具测试
 Author       : chenbangjun
 Modification :
 create       : 2025/02/07
**************************************************************************** */
#include "gtest/gtest.h"
#include "t_rd_ts.h"
#include "t_datacom_lite.h"

#define MAX_CMD_SIZE 1024

Status ret = 0;
GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
char g_command[MAX_CMD_SIZE];
char g_grepDbName[20] = {"Alarm_Org.db"};
char g_grepTxtName[20] = {".txt"};
static char g_tempFilePath[250] = {0};
bool g_isGmextimportDone = false;

char g_gmextimport[20] = {0};

int32_t GetViewFieldResultValue(const char *viewName)
{
    int maxCmdSize = 256;
    char cmdOutput[maxCmdSize];
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));

    char command[maxCmdSize];
    (void)snprintf(command, maxCmdSize, "%s", viewName);

    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        printf("popen(%s) error./n", command);
    }
    EXPECT_NE((void *)NULL, pf);
    while (NULL != fgets(cmdOutput, maxCmdSize, pf)) {
    };
    pclose(pf);

    return atoi(cmdOutput);
}

class GmextImportTool : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        InitTsCiCfg();
#ifdef FEATURE_MULTI_TS
    (void)sprintf(g_gmextimport, "gmextimport");
#else
    (void)sprintf(g_gmextimport, "gmextimport_ts");
#endif
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
        RecoverTsCiCfg();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

// 时序接口客户端建连设置msgReadTimeout
int TestTsGmcConnectTime(GmcConnT **conn, GmcStmtT **stmt, int syncMode = 0, char *connName = NULL)
{
    int ret = 0;
    YangConnOptionT tsConnOptions = {0};
    tsConnOptions.msgReadTimeout = 180 * 1000;
    tsConnOptions.isCsMode = true;
    tsConnOptions.serverLocator = g_connServerTsdb;
    ret = TestYangGmcConnect(conn, stmt, syncMode, &tsConnOptions);
    return ret;
}

void GmextImportTool::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    ret = TestTsGmcConnectTime(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *cmd = "pwd";
    FILE *fptr = popen(cmd, "r");
    if (fptr == NULL) {
        ASSERT_TRUE(false);
    }
    fgets(g_tempFilePath, sizeof(g_tempFilePath), fptr);
    fclose(fptr);
    g_tempFilePath[strlen(g_tempFilePath) - 1] = '\0';  //替换结尾换行符
    system("rm -rf ./dbfile");
    system("mkdir -p ./dbfile");
    system("rm -rf ./progress.json");
    // 执行前先kill掉可能存在的工具进程
    system("pkill gmextimport");
    system("pkill gmextimport_ts");
    // 增加截断日志，本地复现时发现有大量其他用例出现截断问题，在此统一添加
    AW_ADD_TRUNCATION_WHITE_LIST(2, "TsServiceEntry try prepare sql", "TsServiceEntry try execute ddl sql");
}

void GmextImportTool::TearDown()
{
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();
}

int getDbfileNum(char *grepFileNum)
{
    char command[128];
    if (grepFileNum == NULL) {
        (void)snprintf(command, 128, "ls -1 %s/dbfile | grep .db | wc -l", g_tempFilePath);
    } else {
        (void)snprintf(command, 128, "ls -1 %s/dbfile | grep %s | wc -l", g_tempFilePath, grepFileNum);
    }
    FILE *fp = popen(command, "r");
    if (fp == NULL) {
        AW_FUN_Log(LOG_STEP, "db文件地址错误");
        return -1;
    }
    char result[20] = {0};
    char *flag1 = fgets(result, sizeof(result), fp);
    if (flag1 == NULL) {
        AW_FUN_Log(LOG_STEP, "获取db文件数量错误");
        return -1;
    }
    int ret = atoi(result);
    pclose(fp);
    return ret;
}

int getGmextimportNum()
{
    char command[128];
    (void)snprintf(command, 128, "ps -ef|grep gmextimport | wc -l");
    
    FILE *fp = popen(command, "r");
    if (fp == NULL) {
        AW_FUN_Log(LOG_STEP, "db文件地址错误");
        return -1;
    }
    char result[20] = {0};
    char *flag1 = fgets(result, sizeof(result), fp);
    if (flag1 == NULL) {
        AW_FUN_Log(LOG_STEP, "获取db文件数量错误");
        return -1;
    }
    int ret = atoi(result);
    pclose(fp);
    return ret;
}

int getTableDataNumber(char *sqlTemp)
{
    int ret = 0;
    ret = GmcExecDirect(g_stmt, sqlTemp, strlen(sqlTemp));
    EXPECT_EQ(GMERR_OK, ret);
    bool eof = false;
    ret = GmcFetch(g_stmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);
    int64_t res = 0;
    uint32_t propSize = strlen(sqlTemp);
    bool isNull = false;
    ret = GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &res, &propSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    return res;
}

typedef struct {
    GmcStmtT *stmt;
    char *sqlCmd;
} ConstructDataType;

// 执行导数命令
void *gmextImportInsert(void *arg)
{
    AW_FUN_Log(LOG_STEP, "导数开始");
    ConstructDataType constructData = *(ConstructDataType *)arg;
    int ret = system(constructData.sqlCmd);
    if (ret == 0) {
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        AW_FUN_Log(LOG_STEP, "gmextimport进程被kill或者时序服务被kill");
    }
    g_isGmextimportDone = true;
    AW_FUN_Log(LOG_STEP, "导数结束");
    return nullptr;
}

// 获取关键字value
int32_t *GetViewFieldResultValue(const char *jsonName, const char *key)
{
    char cmdOutput[MAX_CMD_SIZE] = {0};
    static int32_t result[MAX_CMD_SIZE] = {0};
    int i = 0;
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));

    char command[MAX_CMD_SIZE];
    (void)snprintf(command, MAX_CMD_SIZE, "cat ./dbfile1/%s |grep %s |awk -F ':' '{print $2}'", jsonName, key);

    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        printf("popen(%s) error./n", command);
    }
    EXPECT_NE((void *)NULL, pf);
    while (NULL != fgets(cmdOutput, MAX_CMD_SIZE, pf) && i < MAX_CMD_SIZE) {
        result[i] = atoi(cmdOutput);
        i++;
    };
    pclose(pf);

    return result;
}

// 001.指定json文件路径不存在
// 预期：执行报错，db文件未被删除
TEST_F(GmextImportTool, Timing_080_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_alarm080 -p ./dbfile/Alarm_Org.db -n 151000");
    system("./t_contlog080 -p ./dbfile/Content_Org.db -n 151000");
    char sqlCmd[1024] = {0};
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    // 校验逻辑修改为检查错误日志，不是简单校验返回值避免返回值修改导致频繁适配，下同
    ret = executeCommand(sqlCmd, "GmeiInitSchemas with problems");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char getSizeStmt[] = "select count(*) from t_alarm_TSDB";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(2, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002.指定json文件路径为空文件夹
// 预期：执行报错，db文件未被删除
TEST_F(GmextImportTool, Timing_080_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_alarm080 -p ./dbfile/Alarm_Org.db -n 151000");
    system("./t_contlog080 -p ./dbfile/Content_Org.db -n 151000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[1024] = {0};
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    // DTS2025050825290,文件夹粒度导入时前台不显示错误
    system(sqlCmd);
    char getSizeStmt[] = "select count(*) from t_alarm_TSDB";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(2, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003.指定json文件路径下不含json文件存在其他文件
// 预期：执行报错，db文件未被删除
TEST_F(GmextImportTool, Timing_080_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_alarm080 -p ./dbfile/Alarm_Org.db -n 151000");
    system("./t_contlog080 -p ./dbfile/Content_Org.db -n 151000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./dbfile/Alarm_Org.db ./dbfile1/");
    system("cp ./dbfile/Content_Org.db ./dbfile1/");
    system("echo 1 >> ./dbfile1/alarm.txt");
    char sqlCmd[1024] = {0};
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    // DTS2025050825290,文件夹粒度导入时前台不显示错误
    system(sqlCmd);
    char getSizeStmt[] = "select count(*) from t_alarm_TSDB";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(2, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004.指定json文件路径下含json和同名的txt文件
// 预期：导入成功，数据正确，对应db文件被删除
TEST_F(GmextImportTool, Timing_080_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_alarm080 -p ./dbfile/Alarm_Org.db -n 151000");
    system("./t_contlog080 -p ./dbfile/Content_Org.db -n 151000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp alarm.txt ./dbfile1");
    system("cp ./table_schema/alarm.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    ret = system(sqlCmd);
    // DB文件找不到对应的json文件提示错误
    // 此处因顺序不一致用到导致返回值不一致，不进行校验AW_MACRO_ASSERT_NE_INT(GMERR_OK, ret);
    char getSizeStmt[] = "select count(*) from t_alarm_TSDB";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 151000);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(1, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005.json文件在指定目录的子目录中
// 预期：导入报错，对应db文件被删除
TEST_F(GmextImportTool, Timing_080_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_alarm080 -p ./dbfile/Alarm_Org.db -n 151000");
    system("./t_contlog080 -p ./dbfile/Content_Org.db -n 151000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1/json");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/alarm.json ./dbfile1/json");
    char sqlCmd[1024] = {0};
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    // DTS2025050825290,文件夹粒度导入时前台不显示错误
    system(sqlCmd);
    char getSizeStmt[] = "select count(*) from t_alarm_TSDB";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(2, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006.json文件中src_file指定前缀，db目录存在多个类似前缀的db文件，通过 --file指定某个db文件
// 预期：导入成功，数据正确，对应db文件被删除，仅删除--file指定的db文件
TEST_F(GmextImportTool, Timing_080_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_alarm080 -p ./dbfile/Alarm_Org.db -n 1510000");
    system("./t_alarm080 -p ./dbfile/Alarm_Org11.db -n 151000");
    system("./t_alarm080 -p ./dbfile/Alarm_Org2.db -n 151000");
    system("./t_contlog080 -p ./dbfile/Content_Org.db -n 151000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp alarm.txt ./dbfile1");
    system("cp ./table_schema/alarm.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)sprintf(sqlCmd, "%s --file %s/dbfile/Alarm_Org.db --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char getSizeStmt[] = "select count(*) from t_alarm_TSDB";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 1510000);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(3, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007.json文件中src_def定义的和对应sqlite表结构不一致，字段顺序不一致但是按顺序排列的字段类型一致
// 预期：导入成功，数据正确，db文件被删除
TEST_F(GmextImportTool, Timing_080_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_alarm080 -p ./dbfile/Alarm_Org.db -n 151000");
    system("./t_alarm080 -p ./dbfile/Alarm_Org11.db -n 151000");
    system("./t_alarm080 -p ./dbfile/Alarm_Org2.db -n 151000");
    system("./t_contlog080 -p ./dbfile/Content_Org.db -n 151000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/alarm_basic007.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    ret = system(sqlCmd);
    // 受不同db执行顺序影响，此处不进行校验返回值AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(*) from t_alarm_TSDB";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 453000);
    char getSizeStmt1[] = "select count(*) from t_contlog";
    ret = GmcExecDirect(g_stmt, getSizeStmt1, sizeof(getSizeStmt1));
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(1, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008.json文件中src_def定义的和对应sqlite表结构不一致，构造字段顺序不一致类型不一致但是类型可以转换
// 预期：导入成功，数据正确，db文件被删除
TEST_F(GmextImportTool, Timing_080_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_alarm080 -p ./dbfile/Alarm_Org.db -n 151000");
    system("./t_alarm080 -p ./dbfile/Alarm_Org11.db -n 151000");
    system("./t_alarm080 -p ./dbfile/Alarm_Org2.db -n 151000");
    system("./t_contlog080 -p ./dbfile/Content_Org.db -n 151000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/alarm_basic008.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    ret = system(sqlCmd);
    // 受不同db执行顺序影响，此处不进行校验返回值AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(*) from t_alarm";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 453000);
    char getSizeStmt1[] = "select count(*) from t_contlog";
    ret = GmcExecDirect(g_stmt, getSizeStmt1, sizeof(getSizeStmt1));
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(1, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009.json文件中src_def定义的和对应sqlite表结构不一致，构造字段顺序不一致类型不一致且类型转换失败
// 预期：导入失败，db文件被删除
TEST_F(GmextImportTool, Timing_080_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_alarm080 -p ./dbfile/Alarm_Org.db -n 151000");
    system("./t_alarm080 -p ./dbfile/Alarm_Org11.db -n 151000");
    system("./t_alarm080 -p ./dbfile/Alarm_Org2.db -n 151000");
    system("./t_contlog080 -p ./dbfile/Content_Org.db -n 151000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/alarm_basic009.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    ret = system(sqlCmd);
    // 受不同db执行顺序影响，此处不进行校验返回值AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(*) from t_alarm_tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 453000);
    // 增加校验
    char getSizeStmt1[] = "select count(*) from t_contlog";
    ret = GmcExecDirect(g_stmt, getSizeStmt1, sizeof(getSizeStmt1));
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(1, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010.json文件中dst_def定义的和对应时序表结构不一致，字段顺序不一致但是按顺序排列的字段类型一致
// 预期：导入成功，数据正确，db文件被删除
TEST_F(GmextImportTool, Timing_080_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_alarm080 -p ./dbfile/Alarm_Org.db -n 151000");
    system("./t_alarm080 -p ./dbfile/Alarm_Org11.db -n 151000");
    system("./t_alarm080 -p ./dbfile/Alarm_Org2.db -n 151000");
    system("./t_contlog080 -p ./dbfile/Content_Org.db -n 151000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/alarm_basic010.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd,
        "create table t_alarm_tsdb(log_level1  integer, log_time1 integer, log_brief1 char(64), content_en1 char(1632), "
        " content_ch1 char(1632), module_name1 char(33), log_id1 integer)with (time_col = 'log_time1',  "
        " interval = '1 hour', compression = 'fast(rapidlz)');");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    ret = system(sqlCmd);
    // 受不同db执行顺序影响，此处不进行校验返回值AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(*) from t_alarm_tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 453000);
    char getSizeStmt1[] = "select count(*) from t_contlog";
    ret = GmcExecDirect(g_stmt, getSizeStmt1, sizeof(getSizeStmt1));
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(1, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011.json文件中dst_def定义的和对应时序表结构不一致，构造字段类型不一致但是类型可以转换
// 预期：导入成功，数据正确，db文件被删除
TEST_F(GmextImportTool, Timing_080_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char fdCmd[256] = {0};
    (void)sprintf(fdCmd, "ls -l /proc/`pidof gmserver_ts`/fd |wc -l");
    int32_t fdBefore = 0;
    int32_t fdAfter = 0;
    fdBefore = GetViewFieldResultValue(fdCmd);
    system("./t_dbtest_nomal080 -p ./dbfile/Dbtest_Org.db -n 10000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic011.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd,
        "create table t_dbtest_tsdb(log_id1 integer, log_time1 integer, int1 integer, int2 integer, int3 integer,"
        "char1 char(64), char2 char(64), char3 char(64), blob1 blob, blob2 blob, blob3 blob, charint1 char(64),"
        "charint2 char(64), charint3 char(64), charipv41 integer, charipv42 integer, charipv43 integer, "
        "intip1 inet, intip2 inet, intip3 inet, charip1 inet, charip2 inet, charip3 inet)"
        "with (time_col = 'log_time1',   interval = '1 hour');");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 1, fdAfter);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(*) from t_dbtest_tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 10000);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 1, fdAfter);
    char getSizeStmt1[] = "drop table t_dbtest_tsdb";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, getSizeStmt1, sizeof(getSizeStmt1)));
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore, fdAfter);
    AW_ADD_TRUNCATION_WHITE_LIST(2, "TsServiceEntry try prepare sql", "TsServiceEntry try execute ddl sql");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012.json文件中dst_def定义的和对应时序表结构不一致，构造字段顺序不一致类型不一致且类型转换失败
// 预期：导入失败，db文件未被删除
TEST_F(GmextImportTool, Timing_080_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest_nomal080 -p ./dbfile/Dbtest_Org.db -n 100");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic012.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd,
        "create table t_dbtest_tsdb(log_id1 integer, log_time1 integer, int1 integer, int2 integer, int3 integer,"
        "char1 char(64), char2 char(64), char3 char(64), blob1 blob(160), blob2 blob(160), blob3 blob(160), charint1 char(64),"
        "charint2 char(64), charint3 char(64), charipv41 integer, charipv42 integer, charipv43 integer, "
        "intip1 inet, intip2 inet, intip3 inet, charip1 inet, charip2 inet, charip3 inet)"
        "with (time_col = 'log_time1', interval = '1 hour');");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    // DTS2025050825290,文件夹粒度导入时前台不显示错误
    system(sqlCmd);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from t_dbtest_tsdb \" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(*) from t_dbtest_tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 0);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(1, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_CONNECTION_RESET_BY_PEER, GMERR_NO_DATA);
    AW_ADD_TRUNCATION_WHITE_LIST(2, "TsServiceEntry try prepare sql", "TsServiceEntry try execute ddl sql");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013.json文件中src_def定义的和对应sqlite表结构不一致,多字段
// 预期：导入成功，db文件被删除
TEST_F(GmextImportTool, Timing_080_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_alarm080 -p ./dbfile/Alarm_Org.db -n 151000");
    system("./t_alarm080 -p ./dbfile/Alarm_Org11.db -n 151000");
    system("./t_alarm080 -p ./dbfile/Alarm_Org2.db -n 151000");
    system("./t_contlog080 -p ./dbfile/Content_Org.db -n 151000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/alarm_basic013.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd,
        "create table t_alarm_tsdb(log_id1 integer, log_time1 integer, log_brief1 char(64), content_en1 char(1632), "
        " content_ch1 char(1632), module_name1 char(33), log_level1 integer)with (time_col = 'log_time1',  "
        " interval = '1 hour', compression = 'fast(rapidlz)');");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    ret = system(sqlCmd);
    // 受不同db执行顺序影响，此处不进行校验返回值AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(*) from t_alarm_tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 453000);
    char getSizeStmt1[] = "select count(*) from t_contlog";
    ret = GmcExecDirect(g_stmt, getSizeStmt1, sizeof(getSizeStmt1));
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(1, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014.json文件中src_def定义的和对应sqlite表结构不一致,少字段
// 预期：导入成功，db文件被删除
TEST_F(GmextImportTool, Timing_080_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_alarm080 -p ./dbfile/Alarm_Org.db -n 151000");
    system("./t_alarm080 -p ./dbfile/Alarm_Org11.db -n 151000");
    system("./t_alarm080 -p ./dbfile/Alarm_Org2.db -n 151000");
    system("./t_contlog080 -p ./dbfile/Content_Org.db -n 151000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/alarm_basic014.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd,
        "create table t_alarm_tsdb(log_id1 integer, log_time1 integer, log_brief1 char(64), content_en1 char(1632), "
        " content_ch1 char(1632), module_name1 char(33), log_level1 integer)with (time_col = 'log_time1',  "
        " interval = '1 hour', compression = 'fast(rapidlz)');");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    ret = system(sqlCmd);
    // 受不同db执行顺序影响，此处不进行校验返回值AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(*) from t_alarm_tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 453000);
    char getSizeStmt1[] = "select count(*) from t_contlog";
    ret = GmcExecDirect(g_stmt, getSizeStmt1, sizeof(getSizeStmt1));
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(1, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015.json文件中dst_def定义的和对应时序表结构不一致,多字段
// 预期：导入成功，db文件被删除
TEST_F(GmextImportTool, Timing_080_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_alarm080 -p ./dbfile/Alarm_Org.db -n 151000");
    system("./t_alarm080 -p ./dbfile/Alarm_Org11.db -n 151000");
    system("./t_alarm080 -p ./dbfile/Alarm_Org2.db -n 151000");
    system("./t_contlog080 -p ./dbfile/Content_Org.db -n 151000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/alarm_basic015.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd,
        "create table t_alarm_tsdb(log_id1 integer, log_time1 integer, log_brief1 char(64), content_en1 char(1632), "
        " content_ch1 char(1632), module_name1 char(33), log_level1 integer)with (time_col = 'log_time1',  "
        " interval = '1 hour', compression = 'fast(rapidlz)');");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    ret = system(sqlCmd);
    // 受不同db执行顺序影响，此处不进行校验返回值AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);


    char getSizeStmt[] = "select count(*) from t_alarm_tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 453000);
    char getSizeStmt1[] = "select count(*) from t_contlog";
    ret = GmcExecDirect(g_stmt, getSizeStmt1, sizeof(getSizeStmt1));
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(1, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016.json文件中dst_def定义的和对应时序表结构不一致,少字段
// 预期：导入成功，db文件被删除
TEST_F(GmextImportTool, Timing_080_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_alarm080 -p ./dbfile/Alarm_Org.db -n 151000");
    system("./t_alarm080 -p ./dbfile/Alarm_Org11.db -n 151000");
    system("./t_alarm080 -p ./dbfile/Alarm_Org2.db -n 151000");
    system("./t_contlog080 -p ./dbfile/Content_Org.db -n 151000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/alarm_basic016.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd,
        "create table t_alarm_tsdb(log_id1 integer, log_time1 integer, log_brief1 char(64), content_en1 char(1632), "
        " content_ch1 char(1632), module_name1 char(33), log_level1 integer)with (time_col = 'log_time1',  "
        " interval = '1 hour', compression = 'fast(rapidlz)');");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    ret = system(sqlCmd);
    // 受不同db执行顺序影响，此处不进行校验返回值AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(*) from t_alarm_tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 453000);
    char getSizeStmt1[] = "select count(*) from t_contlog";
    ret = GmcExecDirect(g_stmt, getSizeStmt1, sizeof(getSizeStmt1));
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(1, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 017.存在多个json文件和db文件，构造不同的sqlite表对应同一个时序表
// 预期：导入成功，db文件被删除
TEST_F(GmextImportTool, Timing_080_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_alarm080 -p ./dbfile/Alarm_Org.db -n 151000");
    system("./t_idmlog080 -p ./dbfile/Idm_Org.db -n 151000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/alarm_basic017.json ./dbfile1");
    system("cp ./table_schema/idm_basic017.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s --chunk_capacity 1000", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(log_time1) from t_alarm_tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 302000);
    char getSizeStmt1[] = "select count(*) from t_idm";
    ret = GmcExecDirect(g_stmt, getSizeStmt1, sizeof(getSizeStmt1));
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018.存在多个json文件和db文件，构造相同的sqlite表对应不同的时序表，多次运行，通过file指定db文件
// 预期：导入成功，db文件被删除
TEST_F(GmextImportTool, Timing_080_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_alarm080 -p ./dbfile/Alarm_Org2.db -n 151000");
    system("./t_alarm080 -p ./dbfile/Alarm_Org3.db -n 151000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/alarm_basic018.json ./dbfile1");
    system("cp ./table_schema/idm_basic018.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd,
        "create table t_alarm_TSDB(log_id1 integer, log_time1 integer, log_brief1 char(64), content_en1 char(1632), "
        " content_ch1 char(1632), module_name1 char(33), log_level1 integer)with (time_col = 'log_time1',  "
        " interval = '1 hour', compression = 'fast(rapidlz)');");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd,
        "create table t_alarm_TSDB2(log_id1 integer, log_time1 integer, log_brief1 char(64), content_en1 char(1632), "
        " content_ch1 char(1632), module_name1 char(33), log_level1 integer)with (time_col = 'log_time1',  "
        " interval = '1 hour', compression = 'fast(rapidlz)');");
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(*) from t_alarm_TSDB";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 151000);
    char getSizeStmt1[] = "select count(*) from t_alarm_TSDB2";
    ret = GmcExecDirect(g_stmt, getSizeStmt1, sizeof(getSizeStmt1));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    res = getTableDataNumber(getSizeStmt1);
    AW_MACRO_ASSERT_EQ_INT(res, 151000);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 019.json文件中src_table名和src_file对应的db文件对应的表名称不一致src_table名为:idm_ip,db文件的对应的表名为  sqlite_idm_ip
// 预期：导入失败，db文件未被删除
TEST_F(GmextImportTool, Timing_080_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_alarm080 -p ./dbfile/Alarm_Org2.db -n 151000");
    system("./t_alarm080 -p ./dbfile/Alarm_Org3.db -n 151000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/alarm_basic019.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    // DTS2025050825290,文件夹粒度导入时前台不显示错误
    system(sqlCmd);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(*) from t_alarm_tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(2, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 020.json中使用chunk_capacity，执行命令中不设置
// 预期：以json文件中设置的条数导入
TEST_F(GmextImportTool, Timing_080_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_alarm080 -p ./dbfile/Alarm_Org2.db -n 151000");
    system("./t_alarm080 -p ./dbfile/Alarm_Org3.db -n 151000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/alarm.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    ret = executeCommand(sqlCmd, "ChunkCapacity for t_alarm is 9000");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(*) from t_alarm_tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 302000);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 021.json中使用chunk_capacity，执行命令中设置不同的chunk_capacity
// 预期：以json文件中设置的条数导入
TEST_F(GmextImportTool, Timing_080_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_alarm080 -p ./dbfile/Alarm_Org2.db -n 151000");
    system("./t_alarm080 -p ./dbfile/Alarm_Org3.db -n 151000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/alarm.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s --chunk_capacity 5000", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    ret = executeCommand(sqlCmd, "ChunkCapacity for t_alarm is 9000");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(*) from t_alarm_tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 302000);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 022.json中未使用chunk_capacity，执行命令中设置chunk_capacity
// 预期：以命令行中设置的条数导入
TEST_F(GmextImportTool, Timing_080_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_idmlog080 -p ./dbfile/Idm_Org.db -n 151000");
    system("./t_idmlog080 -p ./dbfile/Idm_Org2.db -n 151000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/idm.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s --chunk-capacity 5000", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    ret = executeCommand(sqlCmd, "chunk-capacity: 5000"); // ChunkCapacity for t_idmlog is 5000
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(*) from t_idmlog";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 302000);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 023.json中未使用chunk_capacity，执行命令中不设置chunk_capacity
// 预期：以默认一万条数据进行导入
TEST_F(GmextImportTool, Timing_080_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_idmlog080 -p ./dbfile/Idm_Org.db -n 151000");
    system("./t_idmlog080 -p ./dbfile/Idm_Org2.db -n 151000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/idm.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s ", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    ret = executeCommand(sqlCmd, "chunk-capacity: 10000");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(*) from t_idmlog";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 302000);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 024.json中部分使用chunk_capacity，执行命令中不设置chunk_capacity
// 预期：设置的部分以json文件为准，未设置的以默认为准
TEST_F(GmextImportTool, Timing_080_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_alarm080 -p ./dbfile/Alarm_Org2.db -n 151000");
    system("./t_alarm080 -p ./dbfile/Alarm_Org3.db -n 151000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/alarm.json ./dbfile1");
    char sqlCmd[1024] = {0};
    
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s ", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    
    ret = executeCommand(sqlCmd, "ChunkCapacity for t_alarm is 9000");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("rm -rf ./dbfile1/*");
    system("cp ./table_schema/idm.json ./dbfile1");
    system("./t_idmlog080 -p ./dbfile/Idm_Org.db -n 151000");
    system("./t_idmlog080 -p ./dbfile/Idm_Org2.db -n 151000");
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s ", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    ret = executeCommand(sqlCmd, "chunk-capacity: 10000");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(*) from t_idmlog";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 302000);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 025.json中部分使用chunk_capacity，执行命令中设置不同的chunk_capacity
// 预期：设置的部分以json文件为准，未设置的以默认为准
TEST_F(GmextImportTool, Timing_080_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_alarm080 -p ./dbfile/Alarm_Org2.db -n 151000");
    system("./t_alarm080 -p ./dbfile/Alarm_Org3.db -n 151000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/alarm.json ./dbfile1");
    char sqlCmd[1024] = {0};
    
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s --chunk-capacity 5000", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    ret = executeCommand(sqlCmd, "ChunkCapacity for t_alarm is 9000");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("rm -rf ./dbfile1/*");
    system("cp ./table_schema/idm.json ./dbfile1");
    system("./t_idmlog080 -p ./dbfile/Idm_Org.db -n 151000");
    system("./t_idmlog080 -p ./dbfile/Idm_Org2.db -n 151000");
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s --chunk-capacity 5000", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    ret = executeCommand(sqlCmd, "chunk-capacity: 5000");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(*) from t_idmlog";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 302000);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 026.命令中使用--table-process-progress指定记录进度的json并指定到对应文件
// 预期：progress.json内容正确
TEST_F(GmextImportTool, Timing_080_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_idmlog080 -p ./dbfile/Idm_Org.db -n 151000");
    system("./t_idmlog080 -p ./dbfile/Idm_Org2.db -n 151000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/idm.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s --table-process-progress ./dbfile1/progress1.json",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "cat ./dbfile1/progress1.json "); // |awk -F ',' '{print $1$2}'
    ret = executeCommand(sqlCmd, "\"total_file_num\": 2", "\"imported_file_num\": 2", "\"status\": \"done\"");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = executeCommand(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(*) from t_idmlog";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 302000);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 027.命令中使用--table-process-progress指定记录进度的json并指定到对应文件夹
// 预期：执行报错
TEST_F(GmextImportTool, Timing_080_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_idmlog080 -p ./dbfile/Idm_Org.db -n 151000");
    system("./t_idmlog080 -p ./dbfile/Idm_Org2.db -n 151000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/idm.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s --table-process-progress ./dbfile1",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    ret = executeCommand(sqlCmd, "GmeiSinglethreadMode with problems");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char getSizeStmt[] = "select count(*) from t_idmlog";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(2, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 028.json文件中进行int64转int64
// 预期：导入成功，数据正确，db文件被删除
TEST_F(GmextImportTool, Timing_080_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest_nomal080 -p ./dbfile/Dbtest_Org.db -n 10000");
    system("./t_dbtest_nomal080 -p ./dbfile/Dbtest_Org1.db -n 10000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic028.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s ",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(*) from t_dbtest_tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 20000);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 029.json文件中进行字符串转字符串
// 预期：导入成功，数据正确，db文件被删除
TEST_F(GmextImportTool, Timing_080_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest_nomal080 -p ./dbfile/Dbtest_Org.db -n 10000");
    system("./t_dbtest_nomal080 -p ./dbfile/Dbtest_Org1.db -n 10000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic029.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s ",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from t_dbtest_tsdb\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(*) from t_dbtest_tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 20000);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 030.json文件中进行blob转blob
// 预期：导入成功，数据正确，db文件被删除
TEST_F(GmextImportTool, Timing_080_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest_nomal080 -p ./dbfile/Dbtest_Org.db -n 10000");
    system("./t_dbtest080 -p ./dbfile/Dbtest_Org1.db -n 10000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic030.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s ",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(*) from t_dbtest_tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 20000);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 031.json文件中进行字符串数字转int64
// 预期：导入成功，数据正确，db文件被删除
TEST_F(GmextImportTool, Timing_080_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest_nomal080 -p ./dbfile/Dbtest_Org.db -n 10000");
    system("./t_dbtest_nomal080 -p ./dbfile/Dbtest_Org1.db -n 10000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic031.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s ",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(*) from t_dbtest_tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 20000);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 032.json文件中进行字符串ipv4转int64
// 预期：导入成功，数据正确，db文件被删除
TEST_F(GmextImportTool, Timing_080_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest_nomal080 -p ./dbfile/Dbtest_Org.db -n 10000");
    system("./t_dbtest_nomal080 -p ./dbfile/Dbtest_Org1.db -n 10000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic032.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s ",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(*) from t_dbtest_tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 20000);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 033.json文件中进行数字ip类型转inet
// 预期：导入成功，数据正确，db文件被删除
TEST_F(GmextImportTool, Timing_080_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest_nomal080 -p ./dbfile/Dbtest_Org.db -n 10000");
    system("./t_dbtest_nomal080 -p ./dbfile/Dbtest_Org1.db -n 10000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic033.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s ",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(*) from t_dbtest_tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 20000);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 034.json文件中进行字符串ip类型转inet
// 预期：导入成功，数据正确，db文件被删除
TEST_F(GmextImportTool, Timing_080_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest_nomal080 -p ./dbfile/Dbtest_Org.db -n 10000");
    system("./t_dbtest_nomal080 -p ./dbfile/Dbtest_Org1.db -n 10000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic034.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s ",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(*) from t_dbtest_tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 20000);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 035.json文件中dst_def下 src_data有多列分别为int64，int64，int64转int64,且数据中构造分别取第一列，第二列，第三列，且存在不互斥的情况
// 预期：导入成功，数据正确，db文件被删除
TEST_F(GmextImportTool, Timing_080_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest080 -p ./dbfile/Dbtest_Org.db -n 10000");
    system("./t_dbtest080 -p ./dbfile/Dbtest_Org1.db -n 10000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic035.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s ",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(*) from t_dbtest_tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 20000);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 036.json文件中dst_def下 src_data有多列分别为字符串，字符串，字符串转字符串，且数据中构造分别取第一列，第二列，第三列，且存在不互斥的情况
// 预期：导入成功，数据正确，db文件被删除
TEST_F(GmextImportTool, Timing_080_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest080 -p ./dbfile/Dbtest_Org.db -n 10000");
    system("./t_dbtest080 -p ./dbfile/Dbtest_Org1.db -n 10000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic036.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s ",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(*) from t_dbtest_tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 20000);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 037.json文件中dst_def下 src_data有多列分别为blob,blob,blob转blob，且数据中构造分别取第一列，第二列，第三列，且存在不互斥的情况
// 预期：导入失败，db文件未被删除
TEST_F(GmextImportTool, Timing_080_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest080 -p ./dbfile/Dbtest_Org.db -n 10000");
    system("./t_dbtest080 -p ./dbfile/Dbtest_Org1.db -n 10000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic037.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s ",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    // DTS2025050825290,文件夹粒度导入时前台不显示错误
    system(sqlCmd);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(*) from t_dbtest_tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 0);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(2, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_CONNECTION_RESET_BY_PEER, GMERR_NO_DATA);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 038.json文件中dst_def下 src_data有多列分别为数字ipv4,字符串数字,数字ipv6转int64，且数据中构造分别取第一列，第二列，第三列，且存在不互斥的情况
// 预期：导入成功，数据正确，db文件被删除
TEST_F(GmextImportTool, Timing_080_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest080 -p ./dbfile/Dbtest_Org.db -n 10000");
    system("./t_dbtest080 -p ./dbfile/Dbtest_Org1.db -n 10000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic038.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s ",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(*) from t_dbtest_tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 20000);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 039.json文件中dst_def下 src_data有多列分别为数字ipv4,字符串ip,数字ipv6转inet，且数据中构造分别取第一列，第二列，第三列，且存在不互斥的情况
// 预期：导入失败，db文件未被删除
TEST_F(GmextImportTool, Timing_080_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest080 -p ./dbfile/Dbtest_Org.db -n 10000");
    system("./t_dbtest080 -p ./dbfile/Dbtest_Org1.db -n 10000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic039.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s ",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    // DTS2025050825290,文件夹粒度导入时前台不显示错误
    system(sqlCmd);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(*) from t_dbtest_tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 0);

    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(2, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_CONNECTION_RESET_BY_PEER, GMERR_NO_DATA);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 040.json文件中dst_def下 src_data有多列分别为int64,字符串（不符合数字）转int64，且数据中构造分别取第一列，第二列，且存在不互斥的情况
// 预期：导入成功，db文件被删除，不符合的int数据以0填充
TEST_F(GmextImportTool, Timing_080_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest080 -p ./dbfile/Dbtest_Org.db -n 1000");
    system("./t_dbtest080 -p ./dbfile/Dbtest_Org1.db -n 1000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic040.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s ",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(*) from t_dbtest_tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 041.json文件中dst_def下 src_data有多列分别为数字ip，字符串（name, 不符合ip的情况）转inet,且数据中构造分别取第一列，第二列，且存在不互斥的情况
// 预期：导入失败，db文件未被删除
TEST_F(GmextImportTool, Timing_080_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest080 -p ./dbfile/Dbtest_Org.db -n 1000");
    system("./t_dbtest080 -p ./dbfile/Dbtest_Org1.db -n 1000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic041.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s ",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    // DTS2025050825290,文件夹粒度导入时前台不显示错误
    system(sqlCmd);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(*) from t_dbtest_tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(2, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_CONNECTION_RESET_BY_PEER, GMERR_NO_DATA);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 042.json中指定cache_size，时序表中未指定cache_size
// 预期：导入成功，数据正确，db文件被删除
TEST_F(GmextImportTool, Timing_080_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_idmlog080 -p ./dbfile/Idm_Org1.db -n 1000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/idm_basic042.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd,
        "create table t_idm_Tsdb(log_id1 integer, log_time1 integer, vsys_id1 integer, user_name1 char(256), "
        " on_time1 integer, off_time1 integer, src_ip1 char(33), log_type1 integer, login_mode1 integer, "
        "access_mode1 integer, device_category1 char(64), reason1 integer)with (time_col = 'log_time1',  "
        " interval = '1 hour', compression = 'fast(rapidlz)');");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s ",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(*) from t_idm_Tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 1000);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_TRUNCATION_WHITE_LIST(2, "TsServiceEntry try prepare sql", "TsServiceEntry try execute ddl sql");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 043.json中指定compression，时序表中未指定compression
// 预期：导入成功，数据正确，db文件被删除
TEST_F(GmextImportTool, Timing_080_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_idmlog080 -p ./dbfile/Idm_Org1.db -n 1000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/idm_basic043.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd,
        "create table t_idm_Tsdb(log_id1 integer, log_time1 integer, vsys_id1 integer, user_name1 char(256), "
        " on_time1 integer, off_time1 integer, src_ip1 char(33), log_type1 integer, login_mode1 integer, "
        "access_mode1 integer, device_category1 char(64), reason1 integer)with (time_col = 'log_time1',  "
        " interval = '1 hour');");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s ",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(*) from t_idm_Tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 1000);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 044.json中指定disk_limit，时序表中未指定disk_limit
// 预期：导入成功，数据正确，db文件被删除
TEST_F(GmextImportTool, Timing_080_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_idmlog080 -p ./dbfile/Idm_Org1.db -n 151000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/idm_basic044.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd,
        "create table t_idm_Tsdb(log_id1 integer, log_time1 integer, vsys_id1 integer, user_name1 char(256), "
        " on_time1 integer, off_time1 integer, src_ip1 char(33), log_type1 integer, login_mode1 integer, "
        "access_mode1 integer, device_category1 char(64), reason1 integer)with (time_col = 'log_time1',  "
        " interval = '1 hour');");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s ",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(*) from t_idm_Tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 151000);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 045.json中指定sensitive_col，时序表中未指定sensitive_col
// 预期：导入成功，数据正确，db文件被删除
TEST_F(GmextImportTool, Timing_080_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_idmlog080 -p ./dbfile/Idm_Org1.db -n 1000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/idm_basic045.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd,
        "create table t_idm_Tsdb(log_id1 integer, log_time1 integer, vsys_id1 integer, user_name1 char(256), "
        " on_time1 integer, off_time1 integer, src_ip1 char(33), log_type1 integer, login_mode1 integer, "
        "access_mode1 integer, device_category1 char(64), reason1 integer)with (time_col = 'log_time1',  "
        " interval = '1 hour');");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s ",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(*) from t_idm_Tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 1000);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 046.json中指定table_path，时序表中未指定table_path
// 预期：导入成功，数据正确，db文件被删除
TEST_F(GmextImportTool, Timing_080_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_idmlog080 -p ./dbfile/Idm_Org2.db -n 1000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/idm_basic046.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd,
        "create table t_idm_Tsdb(log_id1 integer, log_time1 integer, vsys_id1 integer, user_name1 char(256), "
        " on_time1 integer, off_time1 integer, src_ip1 char(33), log_type1 integer, login_mode1 integer, "
        "access_mode1 integer, device_category1 char(64), reason1 integer)with (time_col = 'log_time1',  "
        " interval = '1 hour');");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s ",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(*) from t_idm_Tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 1000);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 047.json中指定ttl，时序表中未指定ttl
// 预期：导入成功，数据正确，db文件被删除
TEST_F(GmextImportTool, Timing_080_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_idmlog080 -p ./dbfile/Idm_Org2.db -n 1000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/idm_basic047.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd,
        "create table t_idm_Tsdb(log_id1 integer, log_time1 integer, vsys_id1 integer, user_name1 char(256), "
        " on_time1 integer, off_time1 integer, src_ip1 char(33), log_type1 integer, login_mode1 integer, "
        "access_mode1 integer, device_category1 char(64), reason1 integer)with (time_col = 'log_time1',  "
        " interval = '1 hour');");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s ",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(*) from t_idm_Tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 1000);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_TRUNCATION_WHITE_LIST(2, "TsServiceEntry try prepare sql", "TsServiceEntry try execute ddl sql");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 048.json中指定is_volatile_label，时序表中未指定is_volatile_label
// 预期：导入成功，数据正确，db文件被删除
TEST_F(GmextImportTool, Timing_080_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_idmlog080 -p ./dbfile/Idm_Org2.db -n 1000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/idm_basic048.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd,
        "create table t_idm_Tsdb(log_id1 integer, log_time1 integer, vsys_id1 integer, user_name1 char(256), "
        " on_time1 integer, off_time1 integer, src_ip1 char(33), log_type1 integer, login_mode1 integer, "
        "access_mode1 integer, device_category1 char(64), reason1 integer)with (time_col = 'log_time1',  "
        " interval = '1 hour');");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s ",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(*) from t_idm_Tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 1000);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 049. 设置json目录导数后，删除json文件（删除未被导入的json文件）
// 预期：导入成功，数据正确，db文件被删除,全部导入
TEST_F(GmextImportTool, Timing_080_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_idmlog080 -p ./dbfile/Idm_Org1.db -n 3500000");
    system("./t_idmlog080 -p ./dbfile/Idm_Org2.db -n 10000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/idm_basic042.json ./dbfile1");
    system("cp ./table_schema/idm_basic048.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd,
        "create table t_idm_Tsdb(log_id1 integer, log_time1 integer, vsys_id1 integer, user_name1 char(256), "
        " on_time1 integer, off_time1 integer, src_ip1 char(33), log_type1 integer, login_mode1 integer, "
        "access_mode1 integer, device_category1 char(64), reason1 integer)with (time_col = 'log_time1',  "
        " interval = '1 hour');");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s ",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    int conn_num = 1;
    GmcConnT *conn_t = NULL;
    GmcStmtT *stmt_t = NULL;
    pthread_t tid[conn_num];
    ret = TestTsGmcConnectTime(&conn_t, &stmt_t);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConstructDataType constructDataType = {stmt_t, sqlCmd};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    // 等待导数开始
    sleep(5);
    system("rm -rf ./dbfile1/idm_basic048.json");
    pthread_join(tid[0], NULL);

    char getSizeStmt[] = "select count(*) from t_idm_Tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 3510000);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 050.设置json目录导数后，删除某个db文件（未被导入的db文件）
// 预期：导入成功，数据正确，删除的db文件数据未被导入
TEST_F(GmextImportTool, Timing_080_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_idmlog080 -p ./dbfile/Idm_Org1.db -n 1500000");
    system("./t_idmlog080 -p ./dbfile/Idm_Org2.db -n 10000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/idm_basic042.json ./dbfile1");
    system("cp ./table_schema/idm_basic048.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd,
        "create table t_idm_Tsdb(log_id1 integer, log_time1 integer, vsys_id1 integer, user_name1 char(256), "
        " on_time1 integer, off_time1 integer, src_ip1 char(33), log_type1 integer, login_mode1 integer, "
        "access_mode1 integer, device_category1 char(64), reason1 integer)with (time_col = 'log_time1',  "
        " interval = '1 hour');");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s ",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    int conn_num = 1;
    GmcConnT *conn_t = NULL;
    GmcStmtT *stmt_t = NULL;
    pthread_t tid[conn_num];
    ret = TestTsGmcConnectTime(&conn_t, &stmt_t);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConstructDataType constructDataType = {stmt_t, sqlCmd};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    // 等待导数开始
    sleep(8);
    system("rm -rf ./dbfile/Idm_Org2.db");
    pthread_join(tid[0], NULL);

    char getSizeStmt[] = "select count(*) from t_idm_Tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    // 不同环境执行速度和执行顺序不一致，且在068中已有相关场景校验此处修改校验逻辑
    if (res != 1500000 && res != 1510000) {
        AW_MACRO_ASSERT_EQ_INT(1, 0);
    }
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 051.设置json目录导数后，删除正在导入的json文件
// 预期：导入成功，数据正确，db文件被删除
TEST_F(GmextImportTool, Timing_080_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_idmlog080 -p ./dbfile/Idm_Org1.db -n 1500000");
    system("./t_idmlog080 -p ./dbfile/Idm_Org2.db -n 10000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/idm_basic042.json ./dbfile1");
    system("cp ./table_schema/idm_basic048.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd,
        "create table t_idm_Tsdb(log_id1 integer, log_time1 integer, vsys_id1 integer, user_name1 char(256), "
        " on_time1 integer, off_time1 integer, src_ip1 char(33), log_type1 integer, login_mode1 integer, "
        "access_mode1 integer, device_category1 char(64), reason1 integer)with (time_col = 'log_time1',  "
        " interval = '1 hour');");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s ",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    int conn_num = 1;
    GmcConnT *conn_t = NULL;
    GmcStmtT *stmt_t = NULL;
    pthread_t tid[conn_num];
    ret = TestTsGmcConnectTime(&conn_t, &stmt_t);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConstructDataType constructDataType = {stmt_t, sqlCmd};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    // 等待导数开始
    sleep(8);
    system("rm -rf ./dbfile1/idm_basic042.json");
    pthread_join(tid[0], NULL);

    char getSizeStmt[] = "select count(*) from t_idm_Tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 1510000);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 052.导数命令中设置 sqlite3-lib-path
// 预期：导入成功，数据正确，db文件被删除,全部导入
TEST_F(GmextImportTool, Timing_080_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_idmlog080 -p ./dbfile/Idm_Org1.db -n 1500000");
    system("./t_idmlog080 -p ./dbfile/Idm_Org2.db -n 10000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/idm_basic042.json ./dbfile1");
    system("cp ./table_schema/idm_basic048.json ./dbfile1");
    
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd,
        "create table t_idm_Tsdb(log_id1 integer, log_time1 integer, vsys_id1 integer, user_name1 char(256), "
        " on_time1 integer, off_time1 integer, src_ip1 char(33), log_type1 integer, login_mode1 integer, "
        "access_mode1 integer, device_category1 char(64), reason1 integer)with (time_col = 'log_time1',  "
        " interval = '1 hour');");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s --sqlite3-lib-path /data/gmdb/libsqlite3.so",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    system("cp /usr/lib64/libsqlite3.so /data/gmdb/");
    int conn_num = 1;
    GmcConnT *conn_t = NULL;
    GmcStmtT *stmt_t = NULL;
    pthread_t tid[conn_num];
    ret = TestTsGmcConnectTime(&conn_t, &stmt_t);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConstructDataType constructDataType = {stmt_t, sqlCmd};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    pthread_join(tid[0], NULL);

    char getSizeStmt[] = "select count(*) from t_idm_Tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 1510000);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 053.导数命令中sqlite3-lib-path 设置为默认值
// 预期：导入成功，数据正确，db文件被删除,全部导入
TEST_F(GmextImportTool, Timing_080_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_idmlog080 -p ./dbfile/Idm_Org1.db -n 1500000");
    system("./t_idmlog080 -p ./dbfile/Idm_Org2.db -n 10000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/idm_basic042.json ./dbfile1");
    system("cp ./table_schema/idm_basic048.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd,
        "create table t_idm_Tsdb(log_id1 integer, log_time1 integer, vsys_id1 integer, user_name1 char(256), "
        " on_time1 integer, off_time1 integer, src_ip1 char(33), log_type1 integer, login_mode1 integer, "
        "access_mode1 integer, device_category1 char(64), reason1 integer)with (time_col = 'log_time1',  "
        " interval = '1 hour');");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s --sqlite3-lib-path /usr/lib64/libsqlite3.so ",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    int conn_num = 1;
    GmcConnT *conn_t = NULL;
    GmcStmtT *stmt_t = NULL;
    pthread_t tid[conn_num];
    ret = TestTsGmcConnectTime(&conn_t, &stmt_t);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConstructDataType constructDataType = {stmt_t, sqlCmd};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    pthread_join(tid[0], NULL);

    char getSizeStmt[] = "select count(*) from t_idm_Tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 1510000);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 054.导数命令中sqlite3-lib-path，但是路径下没有对应so文件
// 预期：导入报错
TEST_F(GmextImportTool, Timing_080_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_idmlog080 -p ./dbfile/Idm_Org1.db -n 1500000");
    system("./t_idmlog080 -p ./dbfile/Idm_Org2.db -n 10000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/idm_basic042.json ./dbfile1");
    system("cp ./table_schema/idm_basic048.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd,
        "create table t_idm_Tsdb(log_id1 integer, log_time1 integer, vsys_id1 integer, user_name1 char(256), "
        " on_time1 integer, off_time1 integer, src_ip1 char(33), log_type1 integer, login_mode1 integer, "
        "access_mode1 integer, device_category1 char(64), reason1 integer)with (time_col = 'log_time1',  "
        " interval = '1 hour');");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s --sqlite3-lib-path /data/gmdb/libsqlite3.so",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    int conn_num = 1;
    GmcConnT *conn_t = NULL;
    GmcStmtT *stmt_t = NULL;
    pthread_t tid[conn_num];
    ret = TestTsGmcConnectTime(&conn_t, &stmt_t);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConstructDataType constructDataType = {stmt_t, sqlCmd};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    pthread_join(tid[0], NULL);

    char getSizeStmt[] = "select count(*) from t_idm_Tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 0);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(2, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 055.通过file 指定不同db文件，并发导入
// 预期：导数成功，db文件被删除
TEST_F(GmextImportTool, Timing_080_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_idmlog080 -p ./dbfile/Idm_Org1.db -n 1500000");
    system("./t_idmlog080 -p ./dbfile/Idm_Org2.db -n 1500000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/idm.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd,
        "create table t_idmlog(log_id1 integer, log_time1 integer, vsys_id1 integer, user_name1 char(256), "
        " on_time1 integer, off_time1 integer, src_ip1 char(33), log_type1 integer, login_mode1 integer, "
        "access_mode1 integer, device_category1 char(64), reason1 integer)with (time_col = 'log_time1',  "
        " interval = '1 hour');");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --file %s/dbfile/Idm_Org1.db --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s ",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    char sqlCmd1[1024] = {0};
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1, "%s --file %s/dbfile/Idm_Org2.db --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s ",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    int conn_num = 2;
    GmcConnT *conn_t = NULL;
    GmcStmtT *stmt_t = NULL;
    pthread_t tid[conn_num];
    ret = TestTsGmcConnectTime(&conn_t, &stmt_t);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConstructDataType constructDataType1 = {stmt_t, sqlCmd};
    ConstructDataType constructDataType2 = {g_stmt, sqlCmd1};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType1);
    pthread_create(&tid[1], NULL, gmextImportInsert, &constructDataType2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    char getSizeStmt[] = "select count(*) from t_idmlog";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 并发导数可能会导致写./progress.json冲突，从而某一个线程导数失败
    ret = getDbfileNum(NULL);
    int res = getTableDataNumber(getSizeStmt);
    if (ret == 1) {
        AW_MACRO_EXPECT_EQ_INT(res, 1500000);
    } else {
        AW_MACRO_EXPECT_EQ_INT(res, 3000000);
        AW_MACRO_EXPECT_EQ_INT(0, ret);
    }
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 056.设置json文件后，通过directory指定相同目录进行并发导入数据
// 预期：导入成功，db文件被删除
TEST_F(GmextImportTool, Timing_080_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_idmlog080 -p ./dbfile/Idm_Org1.db -n 150000");
    system("./t_idmlog080 -p ./dbfile/Idm_Org2.db -n 150000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/idm.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd,
        "create table t_idmlog(log_id1 integer, log_time1 integer, vsys_id1 integer, user_name1 char(256), "
        " on_time1 integer, off_time1 integer, src_ip1 char(33), log_type1 integer, login_mode1 integer, "
        "access_mode1 integer, device_category1 char(64), reason1 integer)with (time_col = 'log_time1',  "
        " interval = '1 hour');");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s ",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    int conn_num = 2;
    GmcConnT *conn_t = NULL;
    GmcStmtT *stmt_t = NULL;
    pthread_t tid[conn_num];
    ret = TestTsGmcConnectTime(&conn_t, &stmt_t);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConstructDataType constructDataType = {stmt_t, sqlCmd};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    pthread_create(&tid[1], NULL, gmextImportInsert, &constructDataType);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 057.相同json文件后，指定不同目录进行并发导入数据
// 预期：导入成功，db文件被删除
TEST_F(GmextImportTool, Timing_080_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("./t_idmlog080 -p ./dbfile/Idm_Org1.db -n 1500000");
    system("./t_idmlog080 -p ./dbfile1/Idm_Org2.db -n 1500000");
    system("cp ./table_schema/idm_basic042.json ./dbfile1");
    system("cp ./table_schema/idm_basic048.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd,
        "create table t_idm_Tsdb(log_id1 integer, log_time1 integer, vsys_id1 integer, user_name1 char(256), "
        " on_time1 integer, off_time1 integer, src_ip1 char(33), log_type1 integer, login_mode1 integer, "
        "access_mode1 integer, device_category1 char(64), reason1 integer)with (time_col = 'log_time1',  "
        " interval = '1 hour');");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s ",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    char sqlCmd2[1024] = {0};
    (void)memset(sqlCmd2, 0, 1024);
    (void)sprintf(sqlCmd2, "%s --directory %s/dbfile1/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s ",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    int conn_num = 2;
    GmcConnT *conn_t = NULL;
    GmcStmtT *stmt_t = NULL;
    pthread_t tid[conn_num];
    ret = TestTsGmcConnectTime(&conn_t, &stmt_t);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConstructDataType constructDataType1 = {stmt_t, sqlCmd};
    ConstructDataType constructDataType2 = {stmt_t, sqlCmd2};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType1);
    pthread_create(&tid[1], NULL, gmextImportInsert, &constructDataType2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    char getSizeStmt[] = "select count(*) from t_idm_Tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 3000000);

    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 058.json中指定sensitive_col，导入后验证设置敏感词是否生效
// 预期：导入成功，db文件被删除
TEST_F(GmextImportTool, Timing_080_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_idmlog080 -p ./dbfile/Idm_Org1.db -n 1000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/idm_basic045.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s ",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(*) from t_idm_Tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 1000);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_TRUNCATION_WHITE_LIST(2, "TsServiceEntry try prepare sql", "TsServiceEntry try execute ddl sql");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 059.json中指定ttl，导入后验证ttl
// 预期：导入成功，db文件被删除
TEST_F(GmextImportTool, Timing_080_059)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_idmlog080 -p ./dbfile/Idm_Org1.db -n 1000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/idm_basic045.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s ",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(*) from t_idm_Tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 1000);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_TRUNCATION_WHITE_LIST(2, "TsServiceEntry try prepare sql", "TsServiceEntry try execute ddl sql");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 060.json中指定disk_limit，导入后验证disk_limit
// 预期：导入成功，db文件被删除
TEST_F(GmextImportTool, Timing_080_060)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_idmlog080 -p ./dbfile/Idm_Org1.db -n 1000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/idm_basic045.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s ",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(*) from t_idm_Tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 1000);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_TRUNCATION_WHITE_LIST(2, "TsServiceEntry try prepare sql", "TsServiceEntry try execute ddl sql");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 061.json中指定table_path，导入后验证table_path
// 预期：导入成功，db文件被删除
TEST_F(GmextImportTool, Timing_080_061)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_idmlog080 -p ./dbfile/Idm_Org1.db -n 1000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/idm_basic045.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s ",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(*) from t_idm_Tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 1000);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 062.json中指定cache_size，导入后验证cache_size
// 预期：导入成功，db文件被删除
TEST_F(GmextImportTool, Timing_080_062)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_idmlog080 -p ./dbfile/Idm_Org1.db -n 1000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/idm_basic045.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s ",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(*) from t_idm_Tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 1000);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_TRUNCATION_WHITE_LIST(2, "TsServiceEntry try prepare sql", "TsServiceEntry try execute ddl sql");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 063.json中指定isVolatileLabel，导入后验证isVolatileLabel
// 预期：导入成功，db文件被删除
TEST_F(GmextImportTool, Timing_080_063)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_idmlog080 -p ./dbfile/Idm_Org1.db -n 1000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/idm_basic045.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s ",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(*) from t_idm_Tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 1000);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_TRUNCATION_WHITE_LIST(2, "TsServiceEntry try prepare sql", "TsServiceEntry try execute ddl sql");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 064.设置json目录导数时，服务端异常重启，重启后立马启动服务
// 预期：导入成功，db文件被删除
TEST_F(GmextImportTool, Timing_080_064)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("./t_idmlog080 -p ./dbfile/Idm_Org1.db -n 5000000");
    system("cp ./table_schema/idm_basic042.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s ",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    g_isGmextimportDone = false;
    int conn_num = 1;
    GmcConnT *conn_t = NULL;
    GmcStmtT *stmt_t = NULL;
    pthread_t tid[conn_num];
    ret = TestTsGmcConnectTime(&conn_t, &stmt_t);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConstructDataType constructDataType1 = {stmt_t, sqlCmd};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType1);
    // 等待导数服务启动
    sleep(8);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    sleep(2);
    system("sh $TEST_HOME/tools/start.sh -ts");

    pthread_join(tid[0], NULL);
     
    while (!g_isGmextimportDone) {
        sleep(1);
    }
    ret = TestTsGmcConnectTime(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char getSizeStmt[] = "select count(*) from t_idm_Tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    // 机制问题，重启会偶现多导入一批次数据，此处没有设置以默认值10000
    if (res != 5000000 && res != 5010000) {
        AW_MACRO_ASSERT_EQ_INT(1, 0);
    }

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from t_idm_Tsdb where log_id1 > 4999000 order by log_id1 \" -s %s", g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);


    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_RESET_BY_PEER);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 065.设置json目录导数时，服务端异常重启，退出后等待15秒启动服务
// 预期：导入成功，db文件被删除
TEST_F(GmextImportTool, Timing_080_065)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("./t_idmlog080 -p ./dbfile/Idm_Org1.db -n 5000000");
    system("cp ./table_schema/idm_basic042.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s ",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    g_isGmextimportDone = false;
    int conn_num = 1;
    GmcConnT *conn_t = NULL;
    GmcStmtT *stmt_t = NULL;
    pthread_t tid[conn_num];
    ret = TestTsGmcConnectTime(&conn_t, &stmt_t);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConstructDataType constructDataType1 = {stmt_t, sqlCmd};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType1);
    // 等待导数服务启动
    sleep(8);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    sleep(15);
    system("sh $TEST_HOME/tools/start.sh -ts");

    pthread_join(tid[0], NULL);
     
    while (!g_isGmextimportDone) {
        sleep(1);
    }
    ret = TestTsGmcConnectTime(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char getSizeStmt[] = "select count(*) from t_idm_Tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    // 之前适配有误，场景逻辑为没有导完，数据应小于5000000
    AW_MACRO_ASSERT_NE_INT(res, 5000000);


    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(1, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_RESET_BY_PEER);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 066. 设置json目录导数时，新增列
// 预期：导入成功，db文件被删除
TEST_F(GmextImportTool, Timing_080_066)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("./t_idmlog080 -p ./dbfile/Idm_Org1.db -n 5000000");
    system("cp ./table_schema/idm_basic042.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s ",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    g_isGmextimportDone = false;
    int conn_num = 1;
    GmcConnT *conn_t = NULL;
    GmcStmtT *stmt_t = NULL;
    pthread_t tid[conn_num];
    ret = TestTsGmcConnectTime(&conn_t, &stmt_t);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConstructDataType constructDataType1 = {stmt_t, sqlCmd};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType1);
    // 等待导数服务启动
    sleep(8);
    char getSizeStmt1[] = "ALTER TABLE t_idm_Tsdb add num integer; ";
    ret = GmcExecDirect(g_stmt, getSizeStmt1, sizeof(getSizeStmt1));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    pthread_join(tid[0], NULL);
     
    while (!g_isGmextimportDone) {
        sleep(1);
    }
    ret = TestTsGmcConnectTime(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char getSizeStmt[] = "select count(*) from t_idm_Tsdb";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 5000000);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 067. 设置json目录导数时，新增列
// 预期：导入成功，db文件被删除
TEST_F(GmextImportTool, Timing_080_067)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("./t_idmlog080 -p ./dbfile/Idm_Org1.db -n 5000000");
    system("cp ./table_schema/idm_basic042.json ./dbfile1");
    char sqlCmd[1024] = {0};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s ",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    g_isGmextimportDone = false;
    int conn_num = 1;
    GmcConnT *conn_t = NULL;
    GmcStmtT *stmt_t = NULL;
    pthread_t tid[conn_num];
    ret = TestTsGmcConnectTime(&conn_t, &stmt_t);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ConstructDataType constructDataType1 = {stmt_t, sqlCmd};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType1);
    // 等待导数服务启动
    sleep(8);
    char getSizeStmt1[] = "ALTER TABLE t_idm_Tsdb SET(DISK_LIMIT = '10 MB')";
    ret = GmcExecDirect(g_stmt, getSizeStmt1, sizeof(getSizeStmt1));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    pthread_join(tid[0], NULL);
     
    while (!g_isGmextimportDone) {
        sleep(1);
    }
    ret = TestTsGmcConnectTime(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char getSizeStmt[] = "select count(*) from t_idm_Tsdb where log_id1 > 4999000";
    ret = GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int res = getTableDataNumber(getSizeStmt);
    AW_MACRO_ASSERT_EQ_INT(res, 1000);
    ret = getDbfileNum(NULL);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
