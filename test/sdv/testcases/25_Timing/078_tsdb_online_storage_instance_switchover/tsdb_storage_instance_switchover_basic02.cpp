/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 时序交付，TSDB支持在线存储实例切换
 * Author: chenbangjun
 * Create: 2025-02-27
 */
#include "gtest/gtest.h"
#include "t_rd_ts.h"
#include "tsdb_storage_instance_switchover.h"

static GmcConnOptionsT *connOptions = NULL;
static GmcConnT *conn = NULL;
static GmcStmtT *stmt = NULL;
GmcStmtViewT *stmtView = NULL;
char g_cStoreDir[64] = {0};
bool isTrue = false;
char *dir = getenv("GMDB_HOME");
Status ret = 0;

class TsdbOnlineSwitchStorageBasic : public testing::Test {
protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}

public:
    virtual void SetUp();
    virtual void TearDown();
};

void TsdbOnlineSwitchStorageBasic::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    (void)sprintf(g_dataFilePath1, "%s/data/gmdb1", g_filePath);
    (void)sprintf(g_dataFilePath2, "%s/data/gmdb2", g_filePath);
    (void)sprintf(g_dataFilePath3, "%s/data/gmdb3", g_filePath);
    (void)sprintf(g_dataFilePath, "%s", TABLE_PATH);
    TsDefulatDbFileClean();
    system("rm -rf ./data/gmdb1");
    system("rm -rf ./data/gmdb2");
    system("rm -rf ./data/gmdb3");
    system("mkdir -p ./data/gmdb1");
    system("mkdir -p ./data/gmdb2");
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll监听线程
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    ret =
        TestGetResultCommand("cat ../common/logicTablePath.txt | tr -d '$\r'", NULL, g_cStoreDir, sizeof(g_cStoreDir));
    EXPECT_EQ(0, ret);
}

void TsdbOnlineSwitchStorageBasic::TearDown()
{
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
    GmcConnOptionsDestroy(connOptions);
    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    close_epoll_thread();
    testEnvClean();
    RecoverTsCiCfg();
}

// 029.在目录A启动服务，建表A写入数据A，切换至目录B，目录B下没有任何文件，切换成功后新建表A，表B，注入数据，进行insert
// into，表A为源表 预期：切换成功，insert into成功
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    InitTsCiCfgModify();
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t dataCount = 1000;
    constexpr int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t startTime2 = 1288630830;
    ret = rowInsertData(stmt, g_tableName2, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(sqlCmd, "insert into %s select * from %s", g_tableName, g_tableName2);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(sqlCmd,
        "select iD + iD1, time + tiMe1, ip, name, message, id1, time  from %s"
        " order by id ",
        g_tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t ids[dataCount] = {0};
    int64_t checkTimes[dataCount] = {0};
    int64_t ids2[dataCount] = {0};
    int64_t checkTimes2[dataCount] = {0};
    for (int i = 0; i < dataCount; i++) {
        ids[i] = i * 2;
        checkTimes[i] = startTime * 2 + i * 6;
        ids2[i] = i;
        checkTimes2[i] = startTime + i * 3;
    }
    QueryData data1 = {.ids = ids,
        .checkTimes = checkTimes,
        .names = g_name,
        .ips = g_ip,
        .blobs = g_message,
        .ids2 = ids2,
        .times2 = checkTimes2,
        .coefficients = 2,
        .dataCount = dataCount};
    checkQueryData(stmt, sqlCmd, data1);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 030.在目录A启动服务，建表A写入数据A，切换至目录B，目录B下没有任何文件，切换成功后新建表A，表B，注入数据，进行insert
// into，表B为源表 预期：切换成功，insert into成功
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    InitTsCiCfgModify();
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t dataCount = 1000;
    constexpr int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t startTime2 = 1288630830;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(sqlCmd, "insert into %s select * from %s", g_tableName2, g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)sprintf(sqlCmd, "select iD + iD1, time + tiMe1, ip, name, message, id1, time  from %s", g_tableName2);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t ids[dataCount] = {0};
    int64_t checkTimes[dataCount] = {0};
    int64_t ids2[dataCount] = {0};
    int64_t checkTimes2[dataCount] = {0};
    for (int i = 0; i < dataCount; i++) {
        ids[i] = i * 2;
        checkTimes[i] = startTime2 * 2 + i * 6;
        ids2[i] = i;
        checkTimes2[i] = startTime2 + i * 3;
    }
    QueryData data1 = {.ids = ids,
        .checkTimes = checkTimes,
        .names = g_name,
        .ips = g_ip,
        .blobs = g_message,
        .ids2 = ids2,
        .times2 = checkTimes2,
        .coefficients = 2,
        .dataCount = dataCount};
    checkQueryData(stmt, sqlCmd, data1);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 031.在目录A启动服务，建表A写入数据A，切换至目录B，目录B下没有任何文件，切换成功后新建表A，注入数据，进行copy
// to操作，表B为源表 预期：切换成功，copy to成功
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf ./data.csv");
    InitTsCiCfgModify();
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t dataCount = 1000;
    constexpr int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t startTime2 = 1288630830;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)memset(sqlCmd, 0, 512);
    sprintf(sqlCmd,
        "COPY (SELECT id, time FROM testdb0 WHERE id >= 2 and time <= 1288630839) TO"
        "'%s/test/sdv/testcases/25_Timing/078_tsdb_online_storage_instance_switchover/data.csv';",
        dir);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, 256);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *expectContent = R"(2,1288630836
3,1288630839
)";
    // 检查csv文件是否生成
    AW_MACRO_ASSERT_EQ_INT(true, isDataCsvExist());
    char actualContent[1024] = {0};
    (void)GetCsvFileContent(actualContent);
    // 检查获取到内容是否正确
    AW_MACRO_ASSERT_EQ_STR(expectContent, actualContent);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 032.在目录A启动服务，建表A写入数据A，切换至目录B，目录B下没有任何文件，切换成功后新建表A，注入超过disk_limit限制的数据
// 预期：切换成功，成功触发disk_limit
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    InitTsCiCfgModify();
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t dataCount = 1000000;
    constexpr int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, "20 MB", NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 注入数据B
    constexpr int64_t startTime2 = 1288630830;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(sqlCmd, "select iD + iD1, time + tiMe1, ip, name, message, id, time  from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int64_t dataNum = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataNum, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_NE_INT(dataNum, dataCount);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 033.在目录A启动服务，建表A写入数据A，切换至目录B，目录B下没有任何文件，切换成功后新建表A，注入数据并配置ttl
// 预期：切换成功，切换后触发失败
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 对于NEVER_TTL和TTL_POST_BULK_INSERT这两种情况，需要在TTL之前完成数据注入
    // 对于TTL_DURING_BULK_INSERT，需要在注入过程中执行TTL
    // 因此tsLcmCheckPeriod不能设置过大也不能过小。这里折中取5秒
    // 获取当前的小时数和分钟数，例如当前09:30分在，则修改tsLcmCheckPeriod修改为30分5秒，即1805
    struct tm nowTm = {0};
    time_t curTime0 = (time_t)time(NULL);
    if (curTime0 == 0) {
        AW_FUN_Log(LOG_STEP, "get current time failed.\n");
        return;
    }
    (void)DB_LOCAL_TIME_R(&curTime0, &nowTm);
    int period = nowTm.tm_min * 60 + nowTm.tm_sec + 30;
    if (period > 3600) {
        period -= 3600;
        if (period < 3) {
            period += 3;
        }
    }
    char cmd[512] = {0};
    (void)memset(cmd, 0, 512);
    (void)sprintf(cmd, "sh $TEST_HOME/tools/modifyCfg.sh -ts \"tsLcmCheckPeriod=%ld\" \"diskLessBoot=1\"", period);
    ret = emptyPathStartup(&conn, &stmt, NULL, cmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, g_intervalDay);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t dataCount = 1000;
    constexpr int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, g_intervalDay);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t startTime2 = 1288630830;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd1[512] = {0};
    (void)memset(sqlCmd1, 0, 512);
    (void)sprintf(sqlCmd1, "select id, time, name, description, ip from %s ;", g_tableName);
    uint32_t sqlStateLen1 = strlen(sqlCmd1);
    ret = GmcExecDirect(stmt, sqlCmd1, sqlStateLen1);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    int64_t dataNum = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataNum, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, dataNum);
    // 等待触发ttl
    sleep(40);
    ret = GmcExecDirect(stmt, sqlCmd1, sqlStateLen1);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    dataNum = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataNum, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataNum, 0);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 034.在目录A启动服务，建表A写入数据A，切换至目录B，目录B下没有任何文件，切换成功后新建表A，注入数据后进行主动老化
// 预期：切换成功，主动老化成功
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    InitTsCiCfgModify();
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t dataCount = 1000;
    constexpr int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t startTime2 = 1288630830;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    char sqlCmd1[512] = {0};
    (void)memset(sqlCmd1, 0, 512);
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(sqlCmd1, "select id, time, name, description, ip, message from %s ;", g_tableName);
    uint32_t sqlStateLen1 = strlen(sqlCmd1);
    ret = GmcExecDirect(stmt, sqlCmd1, sqlStateLen1);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    int dataNum = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataNum, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataCount, dataNum);

    (void)sprintf(sqlCmd, "SELECT tsdb_aging('%s');", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, sqlCmd1, sqlStateLen1);
    AW_MACRO_ASSERT_EQ_INT(ret, GMERR_OK);
    dataNum = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataNum, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_NE_INT(dataNum, dataCount);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 035.在目录A启动服务，建表A写入数据A，切换至目录B，目录B下没有任何文件，切换成功后新建表A，新增一列
// 预期：切换成功，新增列成功
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    InitTsCiCfgModify();
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t dataCount = 1000;
    constexpr int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(160),"
        " description text, id1 integer)with (time_col = 'time', interval = '1 hour', "
        " disk_limit = '1024 MB', compression = 'fast(rapidlz)');",
        g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(sqlCmd, "alter table testdb0 add time1 integer;");
    ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t startTime2 = 1288630830;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(sqlCmd, "select iD + iD1, time + tiMe1, ip, name, message, id1, time  from %s", g_tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int64_t ids[dataCount] = {0};
    int64_t checkTimes[dataCount] = {0};
    int64_t ids2[dataCount] = {0};
    int64_t checkTimes2[dataCount] = {0};
    for (int i = 0; i < dataCount; i++) {
        ids[i] = i * 2;
        checkTimes[i] = startTime2 * 2 + i * 6;
        ids2[i] = i;
        checkTimes2[i] = startTime2 + i * 3;
    }
    QueryData data1 = {.ids = ids,
        .checkTimes = checkTimes,
        .names = g_name,
        .ips = g_ip,
        .blobs = g_message,
        .ids2 = ids2,
        .times2 = checkTimes2,
        .coefficients = 2,
        .dataCount = dataCount};
    checkQueryData(stmt, sqlCmd, data1);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 036.在目录A启动服务，建表A写入数据A，切换至目录B，目录B下没有任何文件，切换成功后新建表A，alter变更disk_limit
// 预期：切换成功，变更disk_limit成功
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    InitTsCiCfgModify();
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t dataCount = 100000;
    constexpr int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 注入数据B
    constexpr int64_t startTime2 = 1288630830;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = '5 MB');", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 增加等待时间，避免因机器性能原因清理不及时查询出错
    sleep(5);
    (void)sprintf(sqlCmd, "select iD + iD1, time + tiMe1, ip, name, message, message, time  from %s", g_tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int dataNum = 1;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataNum, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_NE_INT(dataNum, dataCount);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 037.在目录A启动服务，建表A写入数据A，切换至目录B，目录B下没有任何文件，切换成功后新建表A，注入数据后进行Truncate操作
// 预期：切换成功，Truncate成功
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    InitTsCiCfgModify();
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t dataCount = 1000;
    constexpr int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 注入数据B
    constexpr int64_t startTime2 = 1288630830;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(sqlCmd, "truncate table %s;", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)sprintf(sqlCmd, "select iD + iD1, time + tiMe1, ip, name, message, id, time  from %s", g_tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int dataNum = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataNum, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(0, dataNum);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 038.在目录A启动服务，建表A写入数据A，切换至目录B，目录B下没有任何文件，切换成功后新建表A，注入数据后进行group
// by和order by操作 预期：切换成功，查询成功数据正确
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    InitTsCiCfgModify();
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t dataCount = 1000;
    constexpr int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 注入数据B
    constexpr int64_t startTime2 = 1288630830;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)memset(sqlCmd, 0, 512);

    (void)sprintf(sqlCmd, "select iD + iD1, time + tiMe1, ip, name, message, id, time  from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int dataNum = 3;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataNum, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(dataNum, dataCount);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 039.GmcSwapDataDir中ctrlFilePath传入有效但是不存在的目录
// 预期：切换成功，查询成功数据正确
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    InitTsCiCfgModify();
    // 在目录/data/gmdb启动建表
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t dataCount = 1000;
    constexpr int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 切换目录
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath3, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 切换目录后新建表A
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 注入数据B
    constexpr int64_t dataCount2 = 1000;
    constexpr int64_t startTime2 = 1288630830;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(sqlCmd,
        "select iD + iD1, time + tiMe1, ip, name, message, id, time  from %s"
        " order by id ",
        g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int64_t ids[dataCount] = {0};
    int64_t checkTimes[dataCount] = {0};
    int64_t ids2[dataCount] = {0};
    int64_t checkTimes2[dataCount] = {0};
    for (int i = 0; i < dataCount; i++) {
        ids[i] = i * 2;
        checkTimes[i] = 2577261660 + i * 6;
        ids2[i] = i;
        checkTimes2[i] = 1288630830 + i * 3;
    }
    QueryData data1 = {.ids = ids,
        .checkTimes = checkTimes,
        .names = g_name,
        .ips = g_ip,
        .blobs = g_message,
        .ids2 = ids2,
        .times2 = checkTimes2,
        .coefficients = 2,
        .dataCount = dataCount2};
    checkQueryData(stmt, sqlCmd, data1);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 040.GmcSwapDataDir中tempFilePath传入有效但是不存在的目录，也会创建
// 预期：切换成功，查询成功数据正确
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char cmd[512] = {0};
    (void)memset(cmd, 0, 512);
    (void)sprintf(cmd, "sh $TEST_HOME/tools/modifyCfg.sh -ts \"forceUseTempFileForQueryResult=1\" \"diskLessBoot=1\"");
    ret = emptyPathStartup(&conn, &stmt, NULL, cmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 在目录/data/gmdb启动建表
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t dataCount = 1000;
    constexpr int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 切换目录
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath3, g_dataFilePath2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 切换目录后新建表A
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 注入数据B
    constexpr int64_t dataCount2 = 10000;
    constexpr int64_t startTime2 = 1288630830;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(sqlCmd, "select iD + iD1, time + tiMe1, ip, name, message, id, time  from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GetTempFileNumber(g_dataFilePath2);
    AW_MACRO_ASSERT_EQ_INT(1, ret);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 044.在目录A启动服务，建100张表，切换至目录B，目录B下有100张与目录A不同名的表，循环100次进行目录A、B之前的切换
// 预期：切换成功
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    char cmd[512] = {0};
    (void)memset(cmd, 0, 512);
    (void)sprintf(cmd, "sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    // 构造目录B下存在表100张表
    ret = emptyPathStartup(&conn, &stmt, g_dataFilePath1, cmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint32_t cmdLen = 0;
    for (int i = 0; i < 100; i++) {
        (void)memset(sqlCmd, 0, 512);
        (void)sprintf(sqlCmd,
            "create table testdb%ld(id integer, time integer, name char(64), ip inet, message blob(160),"
            " description text, id1 integer, time1 integer)with (time_col = 'time', interval = '%s', "
            " disk_limit = '1024 MB', compression = 'fast(rapidlz)');",
            i, g_intervalHour);
        cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
        if (i == 198) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_RESOURCES, ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    constexpr int64_t dataCount = 1000;
    constexpr int64_t startTime2 = 1262275200;
    ret = rowInsertData(stmt, g_tableName2, dataCount, startTime2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = emptyPathRecover(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 构造后重置环境
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    ret = emptyPathStartup(&conn, &stmt, g_dataFilePath2, cmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 100; i++) {
        (void)memset(sqlCmd, 0, 512);
        (void)sprintf(sqlCmd,
            "create table testdb_TEST%ld(id integer, time integer, name char(64), ip inet, message blob(160),"
            " description text, id1 integer, time1 integer)with (time_col = 'time', interval = '%s', "
            " disk_limit = '1024 MB', compression = 'fast(rapidlz)');",
            i, g_intervalHour);
        cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
        if (i == 198) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_RESOURCES, ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    constexpr int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName3, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char tempTableName1[100] = {0};
    char tempTableName2[100] = {0};
    char sqlCmd2[512] = {0};
    for (int i = 1; i <= 30; i++) {
        int tempId = rand() % 99;
        (void)memset(tempTableName1, 0, 100);
        (void)memset(tempTableName2, 0, 100);
        (void)memset(sqlCmd, 0, 512);
        (void)memset(sqlCmd2, 0, 512);
        (void)sprintf(tempTableName2, "testdb_TEST%ld", tempId);
        (void)sprintf(tempTableName1, "testdb%ld", tempId);
        (void)sprintf(sqlCmd, "select * from %s", tempTableName1);
        (void)sprintf(sqlCmd2, "select * from %s", tempTableName2);
        if (i % 2 == 0) {
            // 切换目录
            ret = GmcSwapDataDirSleep(stmt, g_dataFilePath2, NULL);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GmcExecDirect(stmt, sqlCmd2, strlen(sqlCmd2));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = rowInsertData(stmt, tempTableName2, dataCount, startTime2);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
            AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
        } else {
            // 切换目录
            ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = rowInsertData(stmt, tempTableName1, dataCount, startTime);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GmcExecDirect(stmt, sqlCmd2, strlen(sqlCmd2));
            AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
        }
    }
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNDEFINED_TABLE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 045.在目录A启动服务，建100张表，切换至目录B，目录B下有100张与目录A同名的表，循环100次进行目录A、B之前的切换
// 预期：切换成功
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    (void)memset(sqlCmd, 0, 512);
    char cmd[512] = {0};
    (void)memset(cmd, 0, 512);
    (void)sprintf(cmd, "sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    // 构造目录B下存在表100张表
    ret = emptyPathStartup(&conn, &stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint32_t cmdLen = 0;
    for (int i = 0; i < 100; i++) {
        (void)memset(sqlCmd, 0, 512);
        (void)sprintf(sqlCmd,
            "create table testdb%ld(id integer, time integer, name char(64), ip inet, message blob(160),"
            " description text, id1 integer, time1 integer)with (time_col = 'time', interval = '%s', "
            " disk_limit = '1024 MB', compression = 'fast(rapidlz)');",
            i, g_intervalHour);
        cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
        if (i == 198) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_RESOURCES, ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    constexpr int64_t dataCount = 1000;
    constexpr int64_t startTime2 = 1262275200;
    ret = rowInsertData(stmt, g_tableName2, dataCount, startTime2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = emptyPathRecover(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 构造后重置环境
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    ret = emptyPathStartup(&conn, &stmt, g_dataFilePath2, cmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 100; i++) {
        (void)memset(sqlCmd, 0, 512);
        (void)sprintf(sqlCmd,
            "create table testdb%ld(id integer, time integer, name char(64), ip inet, message blob(160),"
            " description text, id1 integer, time1 integer)with (time_col = 'time', interval = '%s', "
            " disk_limit = '1024 MB', compression = 'fast(rapidlz)');",
            i, g_intervalHour);
        cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
        if (i == 198) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_RESOURCES, ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    constexpr int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName2, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char tempTableName1[100] = {0};
    char sqlCmd2[512] = {0};
    for (int i = 1; i <= 30; i++) {
        int tempId = rand() % 99;
        (void)memset(tempTableName1, 0, 100);
        (void)memset(sqlCmd, 0, 512);
        (void)sprintf(tempTableName1, "testdb%ld", tempId);
        (void)sprintf(sqlCmd, "select * from %s", tempTableName1);
        if (i % 2 == 0) {
            // 切换目录
            ret = GmcSwapDataDirSleep(stmt, g_dataFilePath2, NULL);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = rowInsertData(stmt, tempTableName1, dataCount, startTime2);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        } else {
            // 切换目录
            ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = rowInsertData(stmt, tempTableName1, dataCount, startTime2);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNDEFINED_TABLE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 046.在目录A启动服务，建100张表，切换至空目录B，循环100次进行目录A、B之前的切换
// 预期：切换成功
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    char cmd[512] = {0};
    (void)memset(cmd, 0, 512);
    (void)sprintf(cmd, "sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    // 构造目录B下存在表100张表
    ret = emptyPathStartup(&conn, &stmt, g_dataFilePath1, cmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint32_t cmdLen = 0;
    for (int i = 0; i < 100; i++) {
        (void)memset(sqlCmd, 0, 512);
        (void)sprintf(sqlCmd,
            "create table testdb%ld(id integer, time integer, name char(64), ip inet, message blob(160),"
            " description text, id1 integer, time1 integer)with (time_col = 'time', interval = '%s', "
            " disk_limit = '1024 MB', compression = 'fast(rapidlz)');",
            i, g_intervalHour);
        cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
        if (i == 198) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_RESOURCES, ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    constexpr int64_t startTime = 1262275200;
    constexpr int64_t startTime2 = 1288630830;
    constexpr int64_t dataCount = 10000;
    ret = rowInsertData(stmt, g_tableName2, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char tempTableName1[100] = {0};
    char sqlCmd2[512] = {0};
    for (int i = 1; i <= 30; i++) {
        int tempId = rand() % 99;
        (void)memset(tempTableName1, 0, 100);
        (void)memset(sqlCmd, 0, 512);
        (void)sprintf(tempTableName1, "testdb%ld", tempId);
        (void)sprintf(sqlCmd, "select * from %s", tempTableName1);
        if (i % 2 == 0) {
            // 切换目录
            ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = rowInsertData(stmt, tempTableName1, dataCount, startTime2);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        } else {
            // 切换目录
            ret = GmcSwapDataDirSleep(stmt, g_dataFilePath2, NULL);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
            AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
            ret = rowInsertData(stmt, tempTableName1, dataCount, startTime2);
            AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
        }
    }
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNDEFINED_TABLE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 047.在目录A启动服务，建100张表，切换至空目录B，构造异常重启
// 预期：切换过程中重启则切换失败,切换后重启，切换成功
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    uint32_t cmdLen = 0;
    InitTsCiCfgModify();
    // 在目录/data/gmdb启动
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t startTime = 1262275200;
    constexpr int64_t startTime2 = 1288630830;
    constexpr int64_t dataCount = 10000;
    for (int i = 0; i < 100; i++) {
        (void)memset(sqlCmd, 0, 512);
        (void)sprintf(sqlCmd,
            "create table testdb%ld(id integer, time integer, name char(64), ip inet, message blob(160),"
            " description text, id1 integer, time1 integer)with (time_col = 'time', interval = '%s', "
            " disk_limit = '1024 MB', compression = 'fast(rapidlz)');",
            i, g_intervalHour);
        ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        (void)memset(sqlCmd, 0, 512);
        (void)sprintf(sqlCmd, "testdb%ld", i);
        ret = rowInsertData(stmt, sqlCmd, dataCount, startTime);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    ret = TestTsGmcConnect(&conn1, &stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    pthread_t tid;
    InsertQueryDataType constructDataType_1 = {stmt1, g_dataFilePath1, 0, 0};
    pthread_create(&tid, NULL, ConcurrentSwapDataDir, &constructDataType_1);
    // DTS2503190006571 异常重启场景下，不需要GmcUnInit();
    system("pkill gmserver_ts");
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -ts");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // DTS2503190006571 异常重启场景下，不需要GmcInit();
    sleep(1);
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    pthread_join(tid, NULL);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 048.在目录A启动服务，建100张表，切换至空目录B，构造异常重启
// 预期：切换过程中重启则切换失败,切换后重启，切换成功
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    uint32_t cmdLen = 0;
    InitTsCiCfgModify();
    // 在目录/data/gmdb启动
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t startTime = 1262275200;
    constexpr int64_t startTime2 = 1288630830;
    constexpr int64_t dataCount = 10000;
    for (int i = 0; i < 20; i++) {
        (void)memset(sqlCmd, 0, 512);
        (void)sprintf(sqlCmd,
            "create table testdb%ld(id integer, time integer, name char(64), ip inet, message blob(160),"
            " description text, id1 integer, time1 integer)with (time_col = 'time', interval = '%s', "
            " disk_limit = '1024 MB', compression = 'fast(rapidlz)');",
            i, g_intervalHour);
        ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        (void)memset(sqlCmd, 0, 512);
        (void)sprintf(sqlCmd, "testdb%ld", i);
        ret = rowInsertData(stmt, sqlCmd, dataCount, startTime);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    ret = TestTsGmcConnect(&conn1, &stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    pthread_t tid;
    InsertQueryDataType constructDataType_1 = {stmt1, g_dataFilePath1, 0, 0};
    pthread_create(&tid, NULL, ConcurrentSwapDataDir, &constructDataType_1);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    pthread_join(tid, NULL);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 049.在目录A启动服务，并发切换至空目录B，C
// 预期：切换成功，路径不确定
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    uint32_t cmdLen = 0;
    InitTsCiCfgModify();
    // 在目录/data/gmdb启动
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t startTime = 1262275200;
    constexpr int64_t startTime2 = 1288630830;
    constexpr int64_t dataCount = 10000;
    for (int i = 0; i < 20; i++) {
        (void)memset(sqlCmd, 0, 512);
        (void)sprintf(sqlCmd,
            "create table testdb%ld(id integer, time integer, name char(64), ip inet, message blob(160),"
            " description text, id1 integer, time1 integer)with (time_col = 'time', interval = '%s', "
            " disk_limit = '1024 MB', compression = 'fast(rapidlz)');",
            i, g_intervalHour);
        ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        (void)memset(sqlCmd, 0, 512);
        (void)sprintf(sqlCmd, "testdb%ld", i);
        ret = rowInsertData(stmt, sqlCmd, dataCount, startTime);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    ret = TestTsGmcConnect(&conn1, &stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t threadCount = 2;
    pthread_t tid[threadCount];
    InsertQueryDataType constructDataType_1 = {stmt1, g_dataFilePath1, 0, 0};
    InsertQueryDataType constructDataType_2 = {stmt, g_dataFilePath2, 0, 0};
    pthread_create(&tid[0], NULL, ConcurrentSwapDataDir, &constructDataType_1);
    pthread_create(&tid[1], NULL, ConcurrentSwapDataDir, &constructDataType_2);

    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 050.建表后进行schemaOnly备份，并切换至备份目录
// 预期：切换成功
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    InitTsCiCfgModify();
    // 在目录/data/gmdb启动建表
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t dataCount = 1000;
    constexpr int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char newDataDir[200] = {0};
    (void)sprintf(newDataDir, "%s/newData", g_dataFilePath1);
    // 备份目录
    ret = GmcFlushDataBackup(stmt, newDataDir, false, GMC_DATABASE_BACKUP_SCHEMA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 切换目录
    ret = GmcSwapDataDirSleep(stmt, newDataDir, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 注入数据B
    constexpr int64_t startTime2 = 1288630830;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(
        sqlCmd, "select iD + iD1, time + tiMe1, ip, name, message, count(message), time  from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int64_t ids[dataCount] = {0};
    int64_t checkTimes[dataCount] = {0};
    int64_t ids2[dataCount] = {0};
    int64_t checkTimes2[dataCount] = {0};
    for (int i = 0; i < 1; i++) {
        ids[i] = i * 2;
        checkTimes[i] = 2577261660 + i * 6;
        ids2[i] = dataCount;
        checkTimes2[i] = 1288630830 + i * 3;
    }
    QueryData data1 = {.ids = ids,
        .checkTimes = checkTimes,
        .names = g_name,
        .ips = g_ip,
        .blobs = g_message,
        .ids2 = ids2,
        .times2 = checkTimes2,
        .coefficients = 2,
        .dataCount = 1};
    checkQueryData(stmt, sqlCmd, data1);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNDEFINED_TABLE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 051.切换前，保存快照stmt，切换后，设置快照
// 预期：切换目录后设置快照失败
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 构造目录B下存在表数据
    ret = emptyPathStartup(&conn, &stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t dataCount = 1000;
    constexpr int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = emptyPathRecover(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 构造后重置环境
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    InitTsCiCfgModify();
    // 在目录/data/gmdb启动建表
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(
        sqlCmd, "select iD + iD1, time + tiMe1, ip, name, message, count(message), time  from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 获取快照
    ret = GmcGetStmtView(stmt, &stmtView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    constexpr int64_t count = 10;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t num[count] = {1, 1, 1, 1, 2, 2, 2, 3, 3, 4};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};

    for (int i = 0; i < count; i++) {
        id[i] = i + 1;
        time[i] = 1695042000 + i;
    }

    ret = GmcPrepareStmtByLabelName(stmt, g_tableName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, sizeof(time[0]), 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 6, (GmcDataTypeE)DB_DATATYPE_INT64, num, sizeof(num[0]), 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, name, sizeof(name[0]), 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 切换目录
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 设置快照后执行GmcExecute
    ret = GmcSetStmtView(stmt, stmtView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_WRONG_STMT_OBJECT, ret);
    // 释放快照
    GmcFreeStmtView(stmtView);

    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(
        sqlCmd, "select iD + iD1, time + tiMe1, ip, name, message, count(message), time  from %s", g_tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int64_t ids[dataCount] = {0};
    int64_t checkTimes[dataCount] = {0};
    int64_t ids2[dataCount] = {0};
    int64_t checkTimes2[dataCount] = {0};
    for (int i = 0; i < 1; i++) {
        ids[i] = i * 2;
        checkTimes[i] = startTime * 2 + i * 6;
        ids2[i] = dataCount;
        checkTimes2[i] = startTime + i * 3;
    }
    QueryData data1 = {.ids = ids,
        .checkTimes = checkTimes,
        .names = g_name,
        .ips = g_ip,
        .blobs = g_message,
        .ids2 = ids2,
        .times2 = checkTimes2,
        .coefficients = 2,
        .dataCount = 1};
    checkQueryData(stmt, sqlCmd, data1);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 052.切换后通过视图查询存在哪些表
// 预期：只存在切换目录的表
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 构造目录B下存在表数据
    ret = emptyPathStartup(&conn, &stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t dataCount = 1000;
    constexpr int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName2, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = emptyPathRecover(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 构造后重置环境
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    InitTsCiCfgModify();
    // 在目录/data/gmdb启动建表
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 切换目录
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char const *viewName = "select * from 'V\\$STORAGE_DISK_USAGE' ";
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "gmsysview -sql \"%s\" -s %s", viewName, g_connServerTsdb);

    char resStr[30000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    pclose(pResultStr);
    isTrue = strstr(resStr, "TABLE_NAME: testdb1") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "TABLE_NAME: testdb0") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    ret = DropTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 053.切换A\B\C\D
// 预期：切换成功
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 构造目录B下存在表数据
    ret = emptyPathStartup(&conn, &stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t dataCount = 1000;
    constexpr int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = emptyPathRecover(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 构造后重置环境
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    InitTsCiCfgModify();
    // 在目录/data/gmdb启动建表
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 切换目录
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 切换目录
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath2, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 切换目录
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath3, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 切换目录
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 054.空A路径启动DB，B路径是空文件夹，切换到B路径，插入数据，重启DB
// 预期：重启后再B路径启动，查询成功
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    InitTsCiCfgModify();
    // 在目录/data/gmdb启动建表
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t dataCount = 1000;
    constexpr int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_dataSize1Before = GetDirSize(g_dataFilePath);
    // 切换目录
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_dataSize2Before = GetDirSize(g_dataFilePath1);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 注入数据B
    constexpr int64_t startTime2 = 1288630830;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_dataSize1After = GetDirSize(g_dataFilePath);
    g_dataSize2After = GetDirSize(g_dataFilePath1);
    AW_MACRO_ASSERT_EQ_INT(g_dataSize1Before, g_dataSize1After);
    AW_MACRO_ASSERT_NE_INT(g_dataSize2Before, g_dataSize2After);
    char sqlCmd[512] = {0};
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(
        sqlCmd, "select iD + iD1, time + tiMe1, ip, name, message, count(message), time  from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    conn = NULL;
    stmt = NULL;
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");

    g_dataSize2After = GetDirSize(g_dataFilePath1);
    sleep(1);
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(
        sqlCmd, "select iD + iD1, time + tiMe1, ip, name, message, count(message), time  from %s", g_tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int64_t ids[dataCount] = {0};
    int64_t checkTimes[dataCount] = {0};
    int64_t ids2[dataCount] = {0};
    int64_t checkTimes2[dataCount] = {0};
    for (int i = 0; i < 1; i++) {
        ids[i] = i * 2;
        checkTimes[i] = startTime2 * 2 + i * 6;
        ids2[i] = dataCount;
        checkTimes2[i] = startTime2 + i * 3;
    }
    QueryData data1 = {.ids = ids,
        .checkTimes = checkTimes,
        .names = g_name,
        .ips = g_ip,
        .blobs = g_message,
        .ids2 = ids2,
        .times2 = checkTimes2,
        .coefficients = 2,
        .dataCount = 1};
    checkQueryData(stmt, sqlCmd, data1);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 055.在目录A启动服务，建表A写入数据A，切换至目录B，且目录B下存在表A数据A的文件插入数据，重启DB
// 预期：重启后再B路径启动，查询成功
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 构造目录B下存在表数据
    ret = emptyPathStartup(&conn, &stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t dataCount = 1000;
    constexpr int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = emptyPathRecover(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 构造后重置环境
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    InitTsCiCfgModify();
    // 在目录/data/gmdb启动建表
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName2, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    g_dataSize1Before = GetDirSize(g_dataFilePath);
    g_dataSize2Before = GetDirSize(g_dataFilePath1);
    // 切换目录
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_dataSize1After = GetDirSize(g_dataFilePath);
    g_dataSize2After = GetDirSize(g_dataFilePath1);
    AW_MACRO_ASSERT_EQ_INT(g_dataSize1Before, g_dataSize1After);

    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(1);
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char sqlCmd[512] = {0};
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(
        sqlCmd, "select iD + iD1, time + tiMe1, ip, name, message, count(message), time  from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int64_t ids[dataCount] = {0};
    int64_t checkTimes[dataCount] = {0};
    int64_t ids2[dataCount] = {0};
    int64_t checkTimes2[dataCount] = {0};
    for (int i = 0; i < 1; i++) {
        ids[i] = i * 2;
        checkTimes[i] = startTime * 2 + i * 6;
        ids2[i] = dataCount;
        checkTimes2[i] = startTime + i * 3;
    }
    QueryData data1 = {.ids = ids,
        .checkTimes = checkTimes,
        .names = g_name,
        .ips = g_ip,
        .blobs = g_message,
        .ids2 = ids2,
        .times2 = checkTimes2,
        .coefficients = 2,
        .dataCount = 1};
    checkQueryData(stmt, sqlCmd, data1);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 056.空A路径启动DB，B路径是空文件夹，切换到B路径，插入数据，删除B路径，查询数据报错19004切换路径回A路径，重新插入查询不报错
// 预期：重启后再B路径启动，查询成功
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    InitTsCiCfgModify();
    // 在目录/data/gmdb启动建表
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 切换目录
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 注入数据B
    constexpr int64_t dataCount = 1000;
    constexpr int64_t startTime = 1262275200;
    constexpr int64_t startTime2 = 1288630830;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("rm -rf ./data/gmdb1/*");

    char sqlCmd[512] = {0};
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(
        sqlCmd, "select iD + iD1, time + tiMe1, ip, name, message, count(message), time  from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DATA_CORRUPTION, ret);

    // 切换目录A
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int64_t ids[dataCount] = {0};
    int64_t checkTimes[dataCount] = {0};
    int64_t ids2[dataCount] = {0};
    int64_t checkTimes2[dataCount] = {0};
    for (int i = 0; i < 1; i++) {
        ids[i] = i * 2;
        checkTimes[i] = startTime * 2 + i * 6;
        ids2[i] = dataCount;
        checkTimes2[i] = startTime + i * 3;
    }
    QueryData data1 = {.ids = ids,
        .checkTimes = checkTimes,
        .names = g_name,
        .ips = g_ip,
        .blobs = g_message,
        .ids2 = ids2,
        .times2 = checkTimes2,
        .coefficients = 2,
        .dataCount = 1};
    checkQueryData(stmt, sqlCmd, data1);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_DATA_CORRUPTION);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 057.空A路径启动DB，B路径已有老数据，切换到B路径，插入数据，删除B路径，查询数据报错19004，切换路径回A路径，重新插入查询不报错
// 预期：重启后再B路径启动，查询成功
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char cmd[64] = {0};
    (void)memset(cmd, 0, 64);
    (void)sprintf(cmd, "sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    // 构造目录B下存在表数据
    ret = emptyPathStartup(&conn, &stmt, g_dataFilePath1, cmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t dataCount = 1000;
    constexpr int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = emptyPathRecover(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 构造后重置环境
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    conn = NULL;
    stmt = NULL;
    InitTsCiCfgModify();
    // 在目录/data/gmdb启动建表
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    g_dataSize1Before = GetDirSize(g_dataFilePath);
    g_dataSize2Before = GetDirSize(g_dataFilePath1);
    // 切换目录
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_dataSize1After = GetDirSize(g_dataFilePath);
    g_dataSize2After = GetDirSize(g_dataFilePath1);
    AW_MACRO_ASSERT_EQ_INT(g_dataSize1Before, g_dataSize1After);

    system("rm -rf ./data/gmdb1/*");

    char sqlCmd[512] = {0};
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(
        sqlCmd, "select iD + iD1, time + tiMe1, ip, name, message, count(message), time  from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DATA_CORRUPTION, ret);

    // 切换目录
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int64_t ids[dataCount] = {0};
    int64_t checkTimes[dataCount] = {0};
    int64_t ids2[dataCount] = {0};
    int64_t checkTimes2[dataCount] = {0};
    for (int i = 0; i < 1; i++) {
        ids[i] = i * 2;
        checkTimes[i] = startTime * 2 + i * 6;
        ids2[i] = dataCount;
        checkTimes2[i] = startTime + i * 3;
    }
    QueryData data1 = {.ids = ids,
        .checkTimes = checkTimes,
        .names = g_name,
        .ips = g_ip,
        .blobs = g_message,
        .ids2 = ids2,
        .times2 = checkTimes2,
        .coefficients = 2,
        .dataCount = 1};
    checkQueryData(stmt, sqlCmd, data1);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_DATA_CORRUPTION);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 058.易失性路径表，切换到另一个路径，删除易失性路径，切换回原目录
// 预期：切换成功
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char sqlCmd[512] = {0};
    char sysCmd[512] = {0};
    char tempDiskLimit[20] = {0};
    // 使用目录1起服务
    InitTsCiCfgModify();
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建易失性路径表并插入数据
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(160),"
        " description text, id1 integer, time1 integer)with (time_col = 'time', interval = '1 h', "
        " compression = 'fast(rapidlz)', table_path = '%s/', is_volatile_label = 'true')",
        g_tableName, g_dataFilePath3);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t dataCount = 10000;
    constexpr int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 切换至目录2
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删除易失性路径
    (void)sprintf(sysCmd, "rm -rf %s", g_dataFilePath3);
    system(sysCmd);
    // 切换回原目录
    ret = GmcSwapDataDirSleep(stmt, TABLE_PATH, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 059.DB在空路径1启动，目录切换到空路径2，创建内存表、普通逻辑表（路径2）、易失性逻辑表（路径3）,分别三张表写入数据，验证数据，KILL
// DB，
// 删除路径3，清理共享内存，GmcUninit，GmcInit，在路径1重启DB，切换到路径2，重新创建内存表（9013）、普通逻辑表（9013）、易失性逻辑表（9013）；
// 分别三张表写入数据，验证数据
// 预期：切换成功，数据验证ok
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_059)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char sqlCmdMemory[512] = {0};
    char sqlCmd[512] = {0};
    char sqlCmdTablePath[512] = {0};
    char sysCmd[512] = {0};
    char tempDiskLimit[20] = {0};
    // 使用目录1起服务
    InitTsCiCfgModify();
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 切换至目录2
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath2, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建内存表，普通逻辑表（目录2）、易失性逻辑表（目录3）
    (void)sprintf(sqlCmdMemory,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(160),"
        " description text, id1 integer, time1 integer)with (time_col = 'time', interval = '1 h', "
        " enGine = 'mEmOry')",
        g_tableName);
    uint32_t cmdLen = strlen(sqlCmdMemory);
    ret = GmcExecDirect(stmt, sqlCmdMemory, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(160),"
        " description text, id1 integer, time1 integer)with (time_col = 'time', interval = '1 h')",
        g_tableName2);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)sprintf(sqlCmdTablePath,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(160),"
        " description text, id1 integer, time1 integer)with (time_col = 'time', interval = '1 h', "
        " compression = 'fast(rapidlz)', table_path = '%s/', is_volatile_label = 'true')",
        g_tableName3, g_dataFilePath3);
    cmdLen = strlen(sqlCmdTablePath);
    ret = GmcExecDirect(stmt, sqlCmdTablePath, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 10000;
    constexpr int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName2, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName3, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcUnInit();
    system("sh $TEST_HOME/tools/stop.sh -f");
    // 删除易失性路径
    (void)sprintf(sysCmd, "rm -rf %s", g_dataFilePath3);
    system(sysCmd);
    system("sh $TEST_HOME/tools/start.sh -ts");
    GmcInit();

    conn = NULL;
    stmt = NULL;
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 切换至目录2
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath2, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    cmdLen = strlen(sqlCmdMemory);
    ret = GmcExecDirect(stmt, sqlCmdMemory, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DUPLICATE_TABLE, ret);

    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DUPLICATE_TABLE, ret);

    cmdLen = strlen(sqlCmdTablePath);
    ret = GmcExecDirect(stmt, sqlCmdTablePath, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DUPLICATE_TABLE, ret);

    (void)sprintf(sqlCmdTablePath, "select * from %s", g_tableName3);
    cmdLen = strlen(sqlCmdTablePath);
    ret = GmcExecDirect(stmt, sqlCmdTablePath, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, dataCount);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, g_tableName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_DUPLICATE_TABLE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 060.DB在空路径1启动，目录切换到空路径2，创建内存表、普通逻辑表（路径2）、易失性逻辑表（路径3）,分别三张表写入数据，验证数据，预期OK；
// 重启DB，切换回路径1，创建普通逻辑表；
// 切换到路径2，重新创建内存表（9013）、普通逻辑表（9013）、易失性逻辑表（9013）；
// 分别三张表写入数据，验证数据
// 预期：切换成功，数据验证ok
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_060)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char sqlCmdMemory[512] = {0};
    char sqlCmd[512] = {0};
    char sqlCmdTablePath[512] = {0};
    char sysCmd[512] = {0};
    char tempDiskLimit[20] = {0};
    // 使用目录1起服务
    InitTsCiCfgModify();
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 切换至目录2
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath2, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建内存表，普通逻辑表（目录2）、易失性逻辑表（目录3）
    (void)sprintf(sqlCmdMemory,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(160),"
        " description text, id1 integer, time1 integer)with (time_col = 'time', interval = '1 h', "
        " enGine = 'mEmOry')",
        g_tableName);
    uint32_t cmdLen = strlen(sqlCmdMemory);
    ret = GmcExecDirect(stmt, sqlCmdMemory, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(160),"
        " description text, id1 integer, time1 integer)with (time_col = 'time', interval = '1 h')",
        g_tableName2);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)sprintf(sqlCmdTablePath,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(160),"
        " description text, id1 integer, time1 integer)with (time_col = 'time', interval = '1 h', "
        " compression = 'fast(rapidlz)', table_path = '%s/', is_volatile_label = 'true')",
        g_tableName3, g_dataFilePath3);
    cmdLen = strlen(sqlCmdTablePath);
    ret = GmcExecDirect(stmt, sqlCmdTablePath, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 10000;
    constexpr int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName2, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName3, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcUnInit();
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -ts");
    GmcInit();

    conn = NULL;
    stmt = NULL;
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建普通逻辑表
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 切换至目录2
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath2, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    cmdLen = strlen(sqlCmdMemory);
    ret = GmcExecDirect(stmt, sqlCmdMemory, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DUPLICATE_TABLE, ret);

    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DUPLICATE_TABLE, ret);

    cmdLen = strlen(sqlCmdTablePath);
    ret = GmcExecDirect(stmt, sqlCmdTablePath, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DUPLICATE_TABLE, ret);

    (void)sprintf(sqlCmdTablePath, "select * from %s", g_tableName3);
    cmdLen = strlen(sqlCmdTablePath);
    ret = GmcExecDirect(stmt, sqlCmdTablePath, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(10000, dataCount);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, g_tableName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_DUPLICATE_TABLE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// A路径启动,创建内存表,插入10000条数据
// 切换到B路径,创建内存表,插入10000条数据
// 以下循环100遍
// 切换到A路径,插入10000条数据
// 切换到B路径,插入10000条数据
// 检测devMgrMemctx内存不上涨
TEST_F(TsdbOnlineSwitchStorageBasic, Timing_078_Basic_061)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    InitTsCiCfgModify();
    // 在目录/data/gmdb启动建表
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName, g_intervalHour, NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t dataCount = 1000;
    constexpr int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建内存表C
    char sqlCmd[512] = {0};
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(sqlCmd, "create table %s(id integer, time integer,  name char(64), ip inet, message blob(160),  "
        " description text, id1 integer, time1 integer, INDEX idx1(time1))"
        " with (enGine = 'mEmOry', max_size =800000, time_col = 'time', interval = '1 hour');",
        g_tableName3); 
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOutOfOrderData(stmt, g_tableName3, count);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(stmt, g_tableName3);
    AW_MACRO_ASSERT_EQ_INT(count, ret);

    // 切换目录
    ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    // 创建内存表C
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(sqlCmd, "create table %s(id integer, time integer,  name char(64), ip inet, message blob(160),  "
        " description text, id1 integer, time1 integer, INDEX idx1(time1))"
        " with (enGine = 'mEmOry', max_size =800000, time_col = 'time', interval = '1 hour');",
        g_tableName2); 
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = insertOutOfOrderData(stmt, g_tableName2, count);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(count, ret);
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$COM_SHMEM_CTX' where CTX_NAME = 'devMgrMemctx'\" -s"
        "  %s | grep PEAK_ALLOC_SIZE |awk -F ':' '{print $2}' |awk -F ' ' '{print $1}' |awk -F '[' '{print $2}' "
        "|awk -F ']' '{print $1}'",
        g_connServerTsdb);
    int32_t fdBefore = 0;
    int32_t fdAfter = 0;
    fdBefore = GetViewFieldResultValue(sqlCmd);
    for (int i = 1; i <= 100; i++) {
        if (i % 2 == 0) {
            // 切换目录
            ret = GmcSwapDataDirSleep(stmt, g_dataFilePath1, NULL);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = insertOutOfOrderData(stmt, g_tableName2, count);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        } else {
            // 切换目录
            ret = GmcSwapDataDirSleep(stmt, g_dataFilePath, NULL);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = insertOutOfOrderData(stmt, g_tableName3, count);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
        fdAfter = GetViewFieldResultValue(sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
