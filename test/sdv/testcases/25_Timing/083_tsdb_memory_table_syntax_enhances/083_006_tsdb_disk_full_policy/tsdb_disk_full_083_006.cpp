/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: 内存表支持tsAllowDiskClean配置项
 * Author: qinjianhua
 * Create: 2025-04-03
 */
#include <time.h>
#include <errno.h>
#include "gtest/gtest.h"
#include "t_rd_ts.h"
#include "tsdb_disk_full_policy.h"

static GmcConnOptionsT *connOptions = NULL;
static GmcConnT *conn = NULL;
static GmcStmtT *stmt = NULL;
Status ret = 0;
char g_cStoreDir[64] = {0};
char *dir = getenv("GMDB_HOME");
char tableName[20] = "testdb";
bool eof = false;
bool isNull = false;

class tsdb_disk_full_083_006 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        InitTsCiCfg();
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        // 创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        ret = TestTsGmcConnect(&conn, &stmt, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        testGmcDisconnect(conn, stmt);
        close_epoll_thread();
        testEnvClean();
        RecoverTsCiCfg();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void tsdb_disk_full_083_006::SetUp()
{
    AW_CHECK_LOG_BEGIN();
}

void tsdb_disk_full_083_006::TearDown()
{
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
}

class tsdb_disk_full_083_006Value0 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
// euler和环境环境清共享内存
#if defined RUN_INDEPENDENT
        system("sh $TEST_HOME/tools/stop.sh -f");
#endif
        system("sh $TEST_HOME/tools/stop.sh -ts");
        TsDefulatDbFileClean();
        system("sh $TEST_HOME/tools/modifyCfg.sh -ts recover");
        system("sh $TEST_HOME/tools/modifyCfg.sh -ts tsAllowDiskClean=0");
        system("sh $TEST_HOME/tools/start.sh -ts");
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        // 创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        ret = TestTsGmcConnect(&conn, &stmt, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        testGmcDisconnect(conn, stmt);
        close_epoll_thread();
        testEnvClean();
        RecoverTsCiCfg();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void tsdb_disk_full_083_006Value0::SetUp()
{
    AW_CHECK_LOG_BEGIN();
}

void tsdb_disk_full_083_006Value0::TearDown()
{
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
}

// 001.tsAllowDiskClean默认值为1，建表设置disk_limit为M，写入数据磁盘占用大小N，N<=M继续写入数据直至N>M
// 预期：部分老数据被删除
TEST_F(tsdb_disk_full_083_006, Timing_083_006_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    char viewStatement[256] = {0};
    uint32_t cmdLen = 0;
    // 建表
    int diskLimit = 20;

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, CreateDiskLimitTable(stmt, tableName, diskLimit));

    // 写数据
    int insertTimes = 30;
    int64_t count = 10000;
    int startTime = 1704038400;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, insertDataToDiskLimitTable(stmt, tableName, insertTimes, count, startTime));
    // 等待3s使其触发限制，删除数据
    sleep(3);

    // 查视图
    (void)sprintf(viewStatement, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    int *rowCount = GetViewFieldResultValue(viewStatement, "ROW_CNT");
    AW_MACRO_EXPECT_EQ_INT(228000, rowCount[0]);

    (void)sprintf(sqlCmd, "SELECT * FROM %s order by id ASC;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(rowCount[0], dataCount);
    AW_MACRO_EXPECT_EQ_INT(228000, dataCount);

    (void)sprintf(sqlCmd, "SELECT * FROM %s where id < 3600;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, dataCount);
    // 删表
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableName));
}

// 002.tsAllowDiskClean默认值为1，在线修改disk_limit为M，写入数据磁盘占用大小N，N<=M继续写入数据直至N>M<=M继续写入数据直至N>M
// 预期：部分老数据被删除
TEST_F(tsdb_disk_full_083_006, Timing_083_006_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    char viewStatement[256] = {0};
    uint32_t cmdLen = 0;
    // 建表
    int diskLimit = 30;

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, CreateDiskLimitTable(stmt, tableName, diskLimit));
    // 在线修改disk_limit
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = '20 MB')", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int insertTimes = 30;
    int64_t count = 10000;
    int startTime = 1704038400;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, insertDataToDiskLimitTable(stmt, tableName, insertTimes, count, startTime));
    // 等待3s使其触发限制，删除数据
    sleep(3);

    // 查视图
    (void)sprintf(viewStatement, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    int *rowCount = GetViewFieldResultValue(viewStatement, "ROW_CNT");
    AW_MACRO_EXPECT_EQ_INT(228000, rowCount[0]);

    (void)sprintf(sqlCmd, "SELECT * FROM %s order by id ASC;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(rowCount[0], dataCount);
    AW_MACRO_EXPECT_EQ_INT(228000, dataCount);

    (void)sprintf(sqlCmd, "SELECT * FROM %s where id < 3600;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, dataCount);
    // 删表
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableName));
}

// 003.tsAllowDiskClean默认值为1，写入数据磁盘占用大小N，在线修改disk_limit为M，N<=M
// 预期：部分老数据被删除
TEST_F(tsdb_disk_full_083_006, Timing_083_006_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    char viewStatement[256] = {0};
    uint32_t cmdLen = 0;
    // 建表
    int diskLimit = 30;

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, CreateDiskLimitTable(stmt, tableName, diskLimit));

    // 写数据
    int insertTimes = 30;
    int64_t count = 10000;
    int startTime = 1704038400;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, insertDataToDiskLimitTable(stmt, tableName, insertTimes, count, startTime));
    // 等待3s使其触发限制，删除数据
    sleep(3);

    // 查视图
    (void)sprintf(viewStatement, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    int *rowCount = GetViewFieldResultValue(viewStatement, "ROW_CNT");
    AW_MACRO_EXPECT_EQ_INT(insertTimes * count, rowCount[0]);

    // 在线修改disk_limit
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = '20 MB')", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 查视图
    rowCount = GetViewFieldResultValue(viewStatement, "ROW_CNT");
    AW_MACRO_EXPECT_EQ_INT(228000, rowCount[0]);

    int dataCount = 0;
    (void)sprintf(sqlCmd, "SELECT * FROM %s where id < 3600;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, dataCount);
    // 删表
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableName));
}

// 004.tsAllowDiskClean默认值为1，写入数据磁盘占用大小N，在线修改disk_limit为M，N>M，继续写入数据再次触发限制
// 预期：部分老数据被删除，重新触发限制时，部分老数据被删除
TEST_F(tsdb_disk_full_083_006, Timing_083_006_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    char viewStatement[256] = {0};
    uint32_t cmdLen = 0;
    // 建表
    int diskLimit = 30;

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, CreateDiskLimitTable(stmt, tableName, diskLimit));

    // 写数据
    int insertTimes = 30;
    int64_t count = 10000;
    int startTime = 1704038400;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, insertDataToDiskLimitTable(stmt, tableName, insertTimes, count, startTime));
    // 等待3s使其触发限制，删除数据
    sleep(3);

    // 查视图
    (void)sprintf(viewStatement, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    int *rowCount = GetViewFieldResultValue(viewStatement, "ROW_CNT");
    AW_MACRO_EXPECT_EQ_INT(insertTimes * count, rowCount[0]);

    // 在线修改disk_limit
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = '20 MB')", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 查视图
    rowCount = GetViewFieldResultValue(viewStatement, "ROW_CNT");
    AW_MACRO_EXPECT_EQ_INT(228000, rowCount[0]);

    // 再次写数据触发限制
    insertTimes = 1;
    count = 10000;
    startTime = 1704338400;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, insertDataToDiskLimitTable(stmt, tableName, insertTimes, count, startTime));
    // 等待3s使其触发限制，删除数据
    sleep(3);
    // 查视图
    rowCount = GetViewFieldResultValue(viewStatement, "ROW_CNT");
    AW_MACRO_EXPECT_EQ_INT(238000, rowCount[0]);

    int dataCount = 0;
    (void)sprintf(sqlCmd, "SELECT * FROM %s where time < 1704042000;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, dataCount);
    // 删表
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableName));
}

// 005.设置配置项tsAllowDiskClean为0，建表设置disk_limit为M，写入数据磁盘占用大小N，N<=M继续写入数据直至N>M
// 预期：达到disklimit上限后注入数据失败且报错1009022
TEST_F(tsdb_disk_full_083_006Value0, Timing_083_006_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    char viewStatement[256] = {0};
    uint32_t cmdLen = 0;
    // 建表
    int diskLimit = 20;

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, CreateDiskLimitTable(stmt, tableName, diskLimit));

    // 写数据
    int insertTimes = 27;
    constexpr int64_t count = 10000;
    int startTime = 1704038400;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, insertDataToDiskLimitTable(stmt, tableName, insertTimes, count, startTime));
    // 等待3s使其触发限制，删除数据
    sleep(3);

    // 查视图
    (void)sprintf(viewStatement, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    int *rowCount = GetViewFieldResultValue(viewStatement, "ROW_CNT");
    AW_MACRO_EXPECT_EQ_INT(insertTimes * count, rowCount[0]);

    char g_command[1024] = {0};
    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE''\" -s %s", g_connServerTsdb);
    system(g_command);

    int64_t id[count] = {0};
    int64_t time[count] = {0};
    char *name = (char *)malloc(count * 64);
    if (name == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    char nameSource[10][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = startTime + i / 10;
        if (i % 10 == 0) {
            memcpy((name + i * 64), (char *)nameSource, 640);
        }
    }

    // 写入第25w数据时，触发disk_limit，由于tsAllowDiskClean设为0，所以执行报错
    ret = BlukInsert_char(stmt, tableName, count, 3, id, time, name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TABLE_EXCEED_DISK_LIMIT, ret);
    free(name);

    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE''\" -s %s", g_connServerTsdb);
    system(g_command);

    // 查视图验证数据未注入成功
    rowCount = GetViewFieldResultValue(viewStatement, "ROW_CNT");
    AW_MACRO_EXPECT_EQ_INT(insertTimes * count, rowCount[0]);

    int dataCount = 0;
    (void)sprintf(sqlCmd, "SELECT * FROM %s where id < 3600;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(3600, dataCount);
    // 删表
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableName));
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_TABLE_EXCEED_DISK_LIMIT);
}

// 006.设置配置项tsAllowDiskClean为0，在线修改disk_limit为M，写入数据磁盘占用大小N，N<=M继续写入数据直至N>M
// 预期：达到disklimit上限后注入数据失败且报错1009022
TEST_F(tsdb_disk_full_083_006Value0, Timing_083_006_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    char viewStatement[256] = {0};
    uint32_t cmdLen = 0;
    // 建表
    int diskLimit = 30;

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, CreateDiskLimitTable(stmt, tableName, diskLimit));
    // 在线修改disk_limit
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = '20 MB')", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int insertTimes = 27;
    constexpr int64_t count = 10000;
    int startTime = 1704038400;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, insertDataToDiskLimitTable(stmt, tableName, insertTimes, count, startTime));

    // 查视图
    (void)sprintf(viewStatement, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    int *rowCount = GetViewFieldResultValue(viewStatement, "ROW_CNT");

    int64_t id[count] = {0};
    int64_t time[count] = {0};
    char *name = (char *)malloc(count * 64);
    if (name == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    char nameSource[10][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = startTime + i / 10;
        if (i % 10 == 0) {
            memcpy((name + i * 64), (char *)nameSource, 640);
        }
    }
    // 写入第25w数据时，触发disk_limit，由于tsAllowDiskClean设为0，所以执行报错
    ret = BlukInsert_char(stmt, tableName, count, 3, id, time, name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TABLE_EXCEED_DISK_LIMIT, ret);
    free(name);
    // 查视图验证数据未注入成功
    rowCount = GetViewFieldResultValue(viewStatement, "ROW_CNT");
    AW_MACRO_EXPECT_EQ_INT(insertTimes * count, rowCount[0]);

    int64_t dataCount = 0;
    (void)sprintf(sqlCmd, "SELECT * FROM %s where id < 3600;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(3600, dataCount);
    // 删表
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableName));
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_TABLE_EXCEED_DISK_LIMIT);
}

// 007.设置配置项tsAllowDiskClean为0，写入数据磁盘占用大小N，在线修改disk_limit为M，N<=M
// 预期：修改disk_limit后，不触发删除物理表动作
TEST_F(tsdb_disk_full_083_006Value0, Timing_083_006_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    char viewStatement[256] = {0};
    uint32_t cmdLen = 0;
    // 建表
    int diskLimit = 30;

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, CreateDiskLimitTable(stmt, tableName, diskLimit));

    // 写数据
    int insertTimes = 30;
    int64_t count = 10000;
    int startTime = 1704038400;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, insertDataToDiskLimitTable(stmt, tableName, insertTimes, count, startTime));
    // 等待3s使其触发限制，删除数据
    sleep(3);

    // 查视图
    (void)sprintf(viewStatement, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    int *rowCount = GetViewFieldResultValue(viewStatement, "ROW_CNT");
    AW_MACRO_EXPECT_EQ_INT(insertTimes * count, rowCount[0]);

    // 在线修改disk_limit
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = '20 MB')", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 查视图，此时磁盘占用大小大于20M，但是由于tsAllowDiskClean设为0，所以不会删除数据
    int *diskUsage = GetViewFieldResultValue(viewStatement, "DISK_USAGE");
    EXPECT_LT(20971520, diskUsage[0]);

    int dataCount = 0;
    (void)sprintf(sqlCmd, "SELECT * FROM %s where id < 3600;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(3600, dataCount);
    // 删表
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableName));
}

// 008.写入数据磁盘占用大小N，在线修改disk_limit为M，N>M，继续写入数据再次触发限制
// 预期：修改disk_limit后，不触发删除物理表动作，再次写入数据时报错1009022
TEST_F(tsdb_disk_full_083_006Value0, Timing_083_006_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    char viewStatement[256] = {0};
    uint32_t cmdLen = 0;
    // 建表
    int diskLimit = 30;

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, CreateDiskLimitTable(stmt, tableName, diskLimit));

    // 写数据
    int insertTimes = 30;
    constexpr int64_t count = 10000;
    int startTime = 1704038400;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, insertDataToDiskLimitTable(stmt, tableName, insertTimes, count, startTime));
    // 等待3s使其触发限制，删除数据
    sleep(3);

    // 查视图
    (void)sprintf(viewStatement, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    int *rowCount = GetViewFieldResultValue(viewStatement, "ROW_CNT");
    AW_MACRO_EXPECT_EQ_INT(insertTimes * count, rowCount[0]);

    // 在线修改disk_limit
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = '20 MB')", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 查视图，此时磁盘占用大小大于20M，但是由于tsAllowDiskClean设为0，所以不会删除数据
    int *diskUsage = GetViewFieldResultValue(viewStatement, "DISK_USAGE");
    EXPECT_LT(20971520, diskUsage[0]);

    int64_t id[count] = {0};
    int64_t time[count] = {0};
    char *name = (char *)malloc(count * 64);
    if (name == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    char nameSource[10][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = startTime + i / 10;
        if (i % 10 == 0) {
            memcpy((name + i * 64), (char *)nameSource, 640);
        }
    }
    // 写入数据时，触发disk_limit，由于tsAllowDiskClean设为0，所以执行报错
    ret = BlukInsert_char(stmt, tableName, count, 3, id, time, name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TABLE_EXCEED_DISK_LIMIT, ret);
    free(name);
    // 查视图验证数据未注入成功
    rowCount = GetViewFieldResultValue(viewStatement, "ROW_CNT");
    AW_MACRO_EXPECT_EQ_INT(insertTimes * count, rowCount[0]);

    int dataCount = 0;
    (void)sprintf(sqlCmd, "SELECT * FROM %s where id < 3600;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(3600, dataCount);
    // 删表
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableName));
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_TABLE_EXCEED_DISK_LIMIT);
}

// 009.tsAllowDiskClean值为0，建表设置disk_limit为M，写入数据磁盘占用大小N，N>=M;在线修改tsAllowDiskClean值为1，继续写入数据
// 预期：达到disklimit上限后注入数据失败且报错1009022
TEST_F(tsdb_disk_full_083_006Value0, Timing_083_006_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    char viewStatement[256] = {0};
    uint32_t cmdLen = 0;
    // 建表
    int diskLimit = 20;

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, CreateDiskLimitTable(stmt, tableName, diskLimit));

    // 写数据
    int insertTimes = 27;
    constexpr int64_t count = 10000;
    int startTime = 1704038400;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, insertDataToDiskLimitTable(stmt, tableName, insertTimes, count, startTime));
    // 等待3s使其触发限制，删除数据
    sleep(3);

    // 查视图
    (void)sprintf(viewStatement, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    int *rowCount = GetViewFieldResultValue(viewStatement, "ROW_CNT");
    AW_MACRO_EXPECT_EQ_INT(insertTimes * count, rowCount[0]);

    int64_t id[count] = {0};
    int64_t time[count] = {0};
    char *name = (char *)malloc(count * 64);
    if (name == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    char nameSource[10][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = startTime + i / 10;
        if (i % 10 == 0) {
            memcpy((name + i * 64), (char *)nameSource, 640);
        }
    }
    // 写入第25w数据时，触发disk_limit，由于tsAllowDiskClean设为0，所以执行报错
    ret = BlukInsert_char(stmt, tableName, count, 3, id, time, name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TABLE_EXCEED_DISK_LIMIT, ret);

    // 查视图验证数据未注入成功
    rowCount = GetViewFieldResultValue(viewStatement, "ROW_CNT");
    AW_MACRO_EXPECT_EQ_INT(insertTimes * count, rowCount[0]);
    // 修改配置项tsAllowDiskClean为1
    uint32_t setConfigValue = 1;
    ret = GmcSetCfg(stmt, "tsAllowDiskClean", GMC_DATATYPE_INT32, &setConfigValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BlukInsert_char(stmt, tableName, count, 3, id, time, name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查视图验证数据注入成功
    rowCount = GetViewFieldResultValue(viewStatement, "ROW_CNT");
    EXPECT_LT(rowCount[0], insertTimes * count);

    // 查视图，验证配置项修改成功
    (void)sprintf(viewStatement,
        "gmsysview -sql \"select * from 'V\\$CONFIG_PARAMETERS' where NAME='tsAllowDiskClean'\" -s %s",
        g_connServerTsdb);
    int *configValue = GetViewFieldResultValue(viewStatement, "VALUE");
    AW_MACRO_EXPECT_EQ_INT(1, configValue[0]);

    free(name);
    // 删表
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableName));
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_TABLE_EXCEED_DISK_LIMIT);
}

// 010.tsAllowDiskClean值为0，写入数据磁盘占用大小N，在线修改disk_limit为M，N>=M;在线修改tsAllowDiskClean值为1
// 预期：达到disklimit上限后注入数据失败且报错1009022
TEST_F(tsdb_disk_full_083_006Value0, Timing_083_006_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    char viewStatement[256] = {0};
    uint32_t cmdLen = 0;
    // 建表
    int diskLimit = 30;

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, CreateDiskLimitTable(stmt, tableName, diskLimit));

    // 写数据
    int insertTimes = 30;
    constexpr int64_t count = 10000;
    int startTime = 1704038400;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, insertDataToDiskLimitTable(stmt, tableName, insertTimes, count, startTime));
    // 等待3s使其触发限制，删除数据
    sleep(3);

    // 查视图
    (void)sprintf(viewStatement, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    int *rowCount = GetViewFieldResultValue(viewStatement, "ROW_CNT");
    AW_MACRO_EXPECT_EQ_INT(insertTimes * count, rowCount[0]);

    // 在线修改disk_limit
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = '20 MB')", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 确保新写入的数据在已有数据后面
    startTime += 36000;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    char *name = (char *)malloc(count * 64);
    if (name == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    char nameSource[10][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = startTime + i / 10;
        if (i % 10 == 0) {
            memcpy((name + i * 64), (char *)nameSource, 640);
        }
    }
    // 磁盘占用超过disklimit，注入报错
    ret = BlukInsert_char(stmt, tableName, count, 3, id, time, name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TABLE_EXCEED_DISK_LIMIT, ret);

    // 查视图验证数据未注入成功
    rowCount = GetViewFieldResultValue(viewStatement, "ROW_CNT");
    AW_MACRO_EXPECT_EQ_INT(insertTimes * count, rowCount[0]);
    // 修改配置项tsAllowDiskClean为1，立刻出发disklimit检验
    uint32_t setConfigValue = 1;
    ret = GmcSetCfg(stmt, "tsAllowDiskClean", GMC_DATATYPE_INT32, &setConfigValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查视图验证disklimit触发成功
    rowCount = GetViewFieldResultValue(viewStatement, "ROW_CNT");
    int checkTimes = 10;
    while (rowCount[0] == insertTimes * count && checkTimes > 0) {
        rowCount = GetViewFieldResultValue(viewStatement, "ROW_CNT");
        checkTimes--;
        sleep(0.5);
    }
    EXPECT_LT(rowCount[0], insertTimes * count);

    ret = BlukInsert_char(stmt, tableName, count, 3, id, time, name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BlukInsert_char(stmt, tableName, count, 3, id, time, name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查视图验证disklimit生效
    rowCount = GetViewFieldResultValue(viewStatement, "ROW_CNT");
    EXPECT_LT(rowCount[0], insertTimes * count);

    // 查视图，验证配置项修改成功
    (void)sprintf(viewStatement,
        "gmsysview -sql \"select * from 'V\\$CONFIG_PARAMETERS' where NAME='tsAllowDiskClean'\" -s %s",
        g_connServerTsdb);
    int *configValue = GetViewFieldResultValue(viewStatement, "VALUE");
    AW_MACRO_EXPECT_EQ_INT(1, configValue[0]);

    free(name);
    // 删表
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableName));
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_TABLE_EXCEED_DISK_LIMIT);
}

// 011.tsAllowDiskClean值为0，新建多个表，每个表写入数据磁盘占用大小N，在线修改disk_limit为M，N>=M;在线修改tsAllowDiskClean值为1
// 预期：达到disklimit上限后注入数据失败且报错1009022
TEST_F(tsdb_disk_full_083_006Value0, Timing_083_006_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    char viewStatement[256] = {0};
    uint32_t cmdLen = 0;
    char queryTableName[20] = {0};
    int tableCount = 10;
    // 建表
    int diskLimit = 30;

    // 写数据
    int insertTimes = 30;
    int64_t count = 10000;
    int startTime = 1704038400;
    for (int i = 0; i < tableCount; i++) {
        (void)sprintf(queryTableName, "testdb%d", i);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, CreateDiskLimitTable(stmt, queryTableName, diskLimit));
        AW_MACRO_EXPECT_EQ_INT(
            GMERR_OK, insertDataToDiskLimitTable(stmt, queryTableName, insertTimes, count, startTime));
    }

    // 查视图
    (void)sprintf(viewStatement, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    int *rowCount = GetViewFieldResultValue(viewStatement, "ROW_CNT");
    for (int i = 0; i < tableCount; i++) {
        AW_MACRO_EXPECT_EQ_INT(insertTimes * count, rowCount[i]);
    }

    for (int i = 0; i < tableCount; i++) {
        (void)sprintf(queryTableName, "testdb%d", i);
        // 在线修改disk_limit
        (void)sprintf(sqlCmd, "alter table %s set (disk_limit = '20 MB')", queryTableName);
        cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // 修改配置项tsAllowDiskClean为1，立刻出发disklimit检验
    uint32_t setConfigValue = 1;
    ret = GmcSetCfg(stmt, "tsAllowDiskClean", GMC_DATATYPE_INT32, &setConfigValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 等待3s使其触发限制，删除数据
    sleep(3);
    // 查视图验证disklimit触发成功
    rowCount = GetViewFieldResultValue(viewStatement, "ROW_CNT");
    // 删表
    for (int i = 0; i < tableCount; i++) {
        (void)sprintf(queryTableName, "testdb%d", i);
        EXPECT_LT(rowCount[0], insertTimes * count);
    }

    // 查视图，验证配置项修改成功
    (void)sprintf(viewStatement,
        "gmsysview -sql \"select * from 'V\\$CONFIG_PARAMETERS' where NAME='tsAllowDiskClean'\" -s %s",
        g_connServerTsdb);
    int *configValue = GetViewFieldResultValue(viewStatement, "VALUE");
    AW_MACRO_EXPECT_EQ_INT(1, configValue[0]);

    // 删表
    for (int i = 0; i < tableCount; i++) {
        (void)sprintf(queryTableName, "testdb%d", i);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, queryTableName));
    }
}

// 012.tsAllowDiskClean值为1，建表设置disk_limit为M，写入数据磁盘占用大小N，N>=M;在线修改tsAllowDiskClean值为0，继续写入数据
// 预期：达到disklimit上限后注入数据失败且报错1009022
TEST_F(tsdb_disk_full_083_006, Timing_083_006_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[256] = {0};
    char viewStatement[256] = {0};
    uint32_t cmdLen = 0;
    // 建表
    int diskLimit = 20;

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, CreateDiskLimitTable(stmt, tableName, diskLimit));

    // 写数据
    int insertTimes = 30;
    constexpr int64_t count = 10000;
    int startTime = 1704038400;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, insertDataToDiskLimitTable(stmt, tableName, insertTimes, count, startTime));
    // 等待3s使其触发限制，删除数据
    sleep(3);

    // 查视图
    (void)sprintf(viewStatement, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    int *rowCount = GetViewFieldResultValue(viewStatement, "ROW_CNT");
    EXPECT_LT(rowCount[0], insertTimes * count);

    char g_command[1024];
    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE''\" -s %s", g_connServerTsdb);
    system(g_command);

    // 修改配置项tsAllowDiskClean为0
    uint32_t setConfigValue = 0;
    ret = GmcSetCfg(stmt, "tsAllowDiskClean", GMC_DATATYPE_INT32, &setConfigValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

      AW_MACRO_EXPECT_EQ_INT(GMERR_TABLE_EXCEED_DISK_LIMIT, insertDataToDiskLimitTable(stmt, tableName, insertTimes, count, startTime));

    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE''\" -s %s", g_connServerTsdb);
    system(g_command);

    // 查视图验证数据注入成功
    rowCount = GetViewFieldResultValue(viewStatement, "ROW_CNT");
    EXPECT_LT(rowCount[0], insertTimes * count);

    // 查视图，验证配置项修改成功
    (void)sprintf(viewStatement,
        "gmsysview -sql \"select * from 'V\\$CONFIG_PARAMETERS' where NAME='tsAllowDiskClean'\" -s %s",
        g_connServerTsdb);
    int *configValue = GetViewFieldResultValue(viewStatement, "VALUE");
    AW_MACRO_EXPECT_EQ_INT(0, configValue[0]);

    // 删表
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableName));
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_TABLE_EXCEED_DISK_LIMIT);
}
