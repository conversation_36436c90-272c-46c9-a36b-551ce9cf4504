/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2025. All rights reserved.
 * Description: 【技术转交付】TSDB authentication 测试
 * Author: qinjianhua
 * Create: 2024-06-28
 */

#include "gtest/gtest.h"
#include "t_rd_common.h"
#include "../../common/include/component/t_rd_ts.h"

char tabelName[] = "testdb";
char tabelName_1[] = "testdb_1";
char compressModeFast[] = "fast";
char compressModeRapidlz[] = "fast(rapidlz)";
char compressModeZstar[] = "fast(zstar)";
char compressModeNo[] = "no";

static GmcConnOptionsT *connOptions = NULL;
static GmcConnT *conn = NULL;
static GmcStmtT *stmt = NULL;
Status ret = 0;
bool eof = false;
bool isNull = false;
char *dir = getenv("GMDB_HOME");

bool isDataCsvExist()
{
    if (FILE *file = fopen("data.csv", "r")) {
        fclose(file);
        return true;
    }
    return false;
}

void GetCsvFileContent(char returnValue[])
{
    FILE *file = fopen("data.csv", "r");
    char row[128];
    while (fgets(row, 128, file) != NULL) {
        strcat(returnValue, row);
    }
    fclose(file);
}

typedef struct {
    char (*name)[10];
    int64_t *age;
    int64_t *id;
    int64_t *worktime;
    int64_t *salary;
    uint32_t rowNum;
} Data;

typedef struct {
    char (*name)[10];
    int64_t *age;
    int64_t *id;
    int64_t *worktime;
    int64_t *salary;
    uint32_t rowNum;
} Data2;

typedef struct {
    char (*name)[10];
    int64_t *age;
    int64_t *id;
    int64_t *worktime;
    int64_t *salary;
    uint32_t rowNum;
} Data3;

void ExecQueryCmd(GmcStmtT *stmt, const char *queryCommand)
{
    assert(queryCommand != NULL);
    Status ret = GmcExecDirect(stmt, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
}
int DropCmTable(char *tableName)
{
    int ret = 0;
    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    return ret;
}

class multi_process : public testing::Test {
protected:
    static void SetUpTestCase()
    {

        ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        close_epoll_thread();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void multi_process::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void multi_process::TearDown()
{
    GmcConnOptionsDestroy(connOptions);
    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
}

// 007、导入进程A CREATE权限，导入进程B ALTER，DROP权限，系统权限验证
TEST_F(multi_process, Timing_040_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char ddlCommand[512];
    // 新增列
    sprintf(ddlCommand, "alter table tablequery4 add message3 blob(1600);");
    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", "tablequery4");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008、导入进程A CREATE权限，未导入进程B ALTER，DROP权限，系统权限验证
TEST_F(multi_process, Timing_040_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char ddlCommand[512];
    // 新增列
    sprintf(ddlCommand, "alter table tablequery4 add message3 blob(1600);");
    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_ASSERT_EQ_INT(1018004, ret);

    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", "tablequery4");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(ret, 1018004);

    AW_ADD_ERRNUM_WHITE_LIST(1, 1018004);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011、进程A创建表，进程A GRANT INSERT权限给进程B，进程B插入数据
TEST_F(multi_process, Timing_040_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char names[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t ids[] = {1, 2, 4, 9, 8, 14};
    int64_t worktimes[] = {24, 24, 11, 11, 12, 11};
    int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, data.name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data.rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data.age, sizeof(data.age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data.id, sizeof(data.id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, data.worktime, sizeof(data.worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, data.salary, sizeof(data.salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[65535] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select message, worktime from tablequery4;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 65535;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message[i], 65535), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)6);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012、进程A创建表，进程A 未GRANT INSERT权限给进程B，进程B插入数据
TEST_F(multi_process, Timing_040_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, 1018004);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1018004);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015、进程A创建表a,b,进程A GRANT 进程B表b INSERT权限，GRANT 进程B表a
// SELETE权限，进程B从表a中读取数据插入到表b中，INSERT INTO操作
TEST_F(multi_process, Timing_040_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    // insert into 相当于不清除表tablequery5的数据，然后把tablequery4的数据往tablequery5后按数据插入
    char commandInsert[512] = "insert into tablequery5 select * from tablequery4;";
    ret = GmcExecDirect(stmt, commandInsert, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    uint8_t message3[][65535] = {"9021 6537", "0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "0071 3520",
        "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    int64_t resultWorktime3[] = {24, 24, 11, 11, 12, 11, 24, 24, 11, 11, 12, 11};
    // 查询insert into后表tablequery5数据
    isNull = false;
    uint32_t i = 0;
    const char *queryCommand = "select message, worktime from tablequery5;";
    ExecQueryCmd(stmt, queryCommand);

    uint8_t messageRes[65535] = {0};
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 65535;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message3[i], 65535), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime3[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)12);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016、进程A创建表a,b,进程A GRANT 进程B表b INSERT权限，未GRANT 进程B表a
// SELETE权限，进程B从表a中读取数据插入到表b中，INSERT INTO操作
TEST_F(multi_process, Timing_040_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    // insert into 相当于不清除表tablequery5的数据，然后把tablequery4的数据往tablequery5后按数据插入
    char commandInsert[512] = "insert into tablequery5 select * from tablequery4;";
    ret = GmcExecDirect(stmt, commandInsert, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, 1018004);

    AW_ADD_ERRNUM_WHITE_LIST(1, 1018004);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 017、进程A创建表a,b,进程A 未GRANT 进程B表b INSERT权限，未GRANT 进程B表a
// SELETE权限，进程B从表a中读取数据插入到表b中，INSERT INTO操作
TEST_F(multi_process, Timing_040_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    // insert into 相当于不清除表tablequery5的数据，然后把tablequery4的数据往tablequery5后按数据插入
    char commandInsert[512] = "insert into tablequery5 select * from tablequery4;";
    ret = GmcExecDirect(stmt, commandInsert, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, 1018004);

    AW_ADD_ERRNUM_WHITE_LIST(1, 1018004);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 024、进程A创建表，进程A，REVOKE 对象权限进程B priv_type为ALL
TEST_F(multi_process, Timing_040_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char names[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t ids[] = {1, 2, 4, 9, 8, 14};
    int64_t worktimes[] = {24, 24, 11, 11, 12, 11};
    int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    Data data = {.name = names,
        .age = ages,
        .id = ids,
        .worktime = worktimes,
        .salary = salaries,

        .rowNum = rowNum};

    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, data.name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data.rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data.age, sizeof(data.age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data.id, sizeof(data.id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, data.worktime, sizeof(data.worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, data.salary, sizeof(data.salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[65535] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select message, worktime from tablequery4;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 65535;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message[i], 65535), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)6);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 024、进程A创建表，进程A，REVOKE 对象权限进程B priv_type为ALL
TEST_F(multi_process, Timing_040_024_2)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, 1018004);

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[65535] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select message, worktime from tablequery4;";
    ret = GmcExecDirect(stmt, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, 1018004);

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand), "SELECT tsdb_aging('tablequery4');");
    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_ASSERT_EQ_INT(1018004, ret);
    sleep(1);

    AW_ADD_ERRNUM_WHITE_LIST(1, 1018004);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 029、进程A创建表，进程A，REVOKE 对象权限进程B C，user_name为多个进程（进程B 进程C）
TEST_F(multi_process, Timing_040_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char names[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t ids[] = {1, 2, 4, 9, 8, 14};
    int64_t worktimes[] = {24, 24, 11, 11, 12, 11};
    int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, data.name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data.rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data.age, sizeof(data.age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data.id, sizeof(data.id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, data.worktime, sizeof(data.worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, data.salary, sizeof(data.salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[65535] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select message, worktime from tablequery4;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 65535;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message[i], 65535), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)6);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 032、进程A创建表，进程A GRANT DELETE权限给进程B，进程B老化数据
TEST_F(multi_process, Timing_040_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    uint32_t propSize = 0;
    bool eof = false;

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand), "SELECT tsdb_aging('tablequery4');");
    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(1);

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select message, worktime from tablequery4;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)0);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 033、进程A创建表，进程A未 GRANT DELETE权限给进程B，进程B老化数据
TEST_F(multi_process, Timing_040_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, 1018004);

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[65535] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select message, worktime from tablequery4;";
    ret = GmcExecDirect(stmt, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, 1018004);

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand), "SELECT tsdb_aging('tablequery4');");
    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_ASSERT_EQ_INT(1018004, ret);
    sleep(1);

    AW_ADD_ERRNUM_WHITE_LIST(1, 1018004);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 040、进程A创建表，进程A GRANT SELETE权限给进程B，进程B COPY TO查询数据
TEST_F(multi_process, Timing_040_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char names[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t ids[] = {1, 2, 4, 9, 8, 14};
    int64_t worktimes[] = {24, 24, 11, 11, 12, 11};
    int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    Data data = {.name = names,
        .age = ages,
        .id = ids,
        .worktime = worktimes,
        .salary = salaries,

        .rowNum = rowNum};

    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, data.name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data.rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data.age, sizeof(data.age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data.id, sizeof(data.id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, data.worktime, sizeof(data.worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, data.salary, sizeof(data.salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char ddlCommand[512];
    sprintf(ddlCommand,
        "COPY (SELECT id, message, worktime FROM tablequery4) TO"
        "'%s/test/sdv/testcases/25_Timing/040_tsdb_authentication/data.csv';",
        dir);

    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *expectContent = R"(1,0071 3520,24
2,0010 0000,24
4,,11
9,3102 0021,11
8,0000 0000,12
14,9021 6537,11
)";

    // 检查csv文件是否生成
    AW_MACRO_ASSERT_EQ_INT(true, isDataCsvExist());
    char actualContent[1024] = {0};
    (void)GetCsvFileContent(actualContent);
    // 检查获取到内容是否正确
    AW_MACRO_ASSERT_EQ_STR(expectContent, actualContent);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 041、进程A创建表，进程A 未GRANT SELETE权限给进程B，进程B COPY TO查询数据
TEST_F(multi_process, Timing_040_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char ddlCommand[512];
    sprintf(ddlCommand,
        "COPY (SELECT id, message, worktime FROM tablequery4) TO"
        "'%s/test/sdv/testcases/25_Timing/040_tsdb_authentication/data.csv';",
        dir);

    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_ASSERT_EQ_INT(1018004, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1018004);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 045、进程A创建表，进程B GRANT INSERT/DELETE/SELETE对象权限
TEST_F(multi_process, Timing_040_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char GrantCmd[512] = {0};
    // 授权对象权限，赋予白名单用户对象权限
    sprintf_s(GrantCmd, 512, "GRANT INSERT ON tablequery4 TO 'root:multi_process';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, 1004004);

    AW_ADD_ERRNUM_WHITE_LIST(1, 1004004);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 049、重复10次GRANT，REVOKE 对象权限，每次GRANT，REVOKE权限后dml操作
TEST_F(multi_process, Timing_040_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char names[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t ids[] = {1, 2, 4, 9, 8, 14};
    int64_t worktimes[] = {24, 24, 11, 11, 12, 11};
    int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, data.name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data.rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data.age, sizeof(data.age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data.id, sizeof(data.id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, data.worktime, sizeof(data.worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, data.salary, sizeof(data.salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[65535] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select message, worktime from tablequery4;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 65535;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message[i], 65535), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)6);

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand), "SELECT tsdb_aging('tablequery4');");
    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(1);

    isNull = false;
    i = 0;

    queryCommand = "select message, worktime from tablequery4;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)0);

    AW_FUN_Log(LOG_STEP, "test end.");
}
