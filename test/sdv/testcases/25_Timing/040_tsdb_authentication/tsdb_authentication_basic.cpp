/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2025. All rights reserved.
 * Description: 【技术转交付】TSDB authentication 测试
 * Author: qinjianhua
 * Create: 2024-06-28
 */

#include "gtest/gtest.h"
#include "t_rd_common.h"
#include "../../common/include/component/t_rd_ts.h"

char tabelName[] = "testdb";
char tabelName_1[] = "testdb_1";
char compressModeFast[] = "fast";
char compressModeRapidlz[] = "fast(rapidlz)";
char compressModeZstar[] = "fast(zstar)";
char compressModeNo[] = "no";

static GmcConnOptionsT *connOptions = NULL;
static GmcConnT *conn = NULL;
static GmcStmtT *stmt = NULL;
Status ret = 0;
bool eof = false;
bool isNull = false;
char *dir = getenv("GMDB_HOME");

bool isDataCsvExist()
{
    if (FILE *file = fopen("data.csv", "r")) {
        fclose(file);
        return true;
    }
    return false;
}

void GetCsvFileContent(char returnValue[])
{
    FILE *file = fopen("data.csv", "r");
    char row[128];
    while (fgets(row, 128, file) != NULL) {
        strcat(returnValue, row);
    }
    fclose(file);
}

typedef struct {
    char (*name)[10];
    int64_t *age;
    int64_t *id;
    int64_t *worktime;
    int64_t *salary;
    uint32_t rowNum;
} Data;

void ExecQueryCmd(GmcStmtT *stmt, const char *queryCommand)
{
    assert(queryCommand != NULL);
    Status ret = GmcExecDirect(stmt, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
}
int DropCmTable(char *tableName)
{
    int ret = 0;
    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    return ret;
}

class tsdb_authentication_basic : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/stop.sh -ts");  // 修改配置，先停服务
        system("ipcrm -a");
        system("sh $TEST_HOME/tools/modifyCfg.sh -ts recover");  // 恢复默认值
        TsDefulatDbFileClean();
        system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"userPolicyMode=2\"");  // 鉴权模式
        system("sh $TEST_HOME/tools/modifyCfg.sh -ts "
               "\"DBA=root:gmrule;gmips;gmids;gmadmin;tsdbdba1;tsdbdba2;tsdbdba3;tsdbdba4;tsdbdba5\"");  // DBA用户
        system("sh $TEST_HOME/tools/start.sh -ts");

        ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        close_epoll_thread();
        testEnvClean();
        RecoverTsCiCfg();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void tsdb_authentication_basic::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    // 导入白名单用户，创建用户或用户组
    int ret = 0;
#if defined(ENV_RTOSV2) || defined(ENV_RTOSV2X)
    ret = system("gmrule -c import_allowlist -f ./tsdbuser.gmuser -s channel:ctl_channel_tsdb");
#else
    ret = system("gmrule -c import_allowlist -f ./tsdbuser.gmuser -s usocket:/run/verona/unix_emserver_tsdb");
#endif
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 导入系统权限，赋予白名单用户系统权限
#if defined(ENV_RTOSV2) || defined(ENV_RTOSV2X)
    ret = system("gmrule -c import_policy -f ./tsdbpolicy.gmpolicy -s channel:ctl_channel_tsdbb");
#else
    ret = system("gmrule -c import_policy -f ./tsdbpolicy.gmpolicy -s usocket:/run/verona/unix_emserver_tsdb");
#endif
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    DropCmTable("tablequery4");
    DropCmTable("tablequery5");
    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery4");

    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
}

void tsdb_authentication_basic::TearDown()
{
    DropCmTable("tablequery4");
    GmcConnOptionsDestroy(connOptions);
    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
}

class tsdb_authentication_basic_02 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/stop.sh -ts");  // 修改配置，先停服务
        system("ipcrm -a");
        system("sh $TEST_HOME/tools/modifyCfg.sh -ts recover");  // 恢复默认值
        TsDefulatDbFileClean();
        system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"userPolicyMode=2\"");  // 鉴权模式
        system("sh $TEST_HOME/tools/modifyCfg.sh -ts "
               "\"DBA=root:gmrule;gmips;gmids;gmadmin;tsdbdba1;tsdbdba2;tsdbdba3;tsdbdba4;tsdbdba5\"");
        system("sh $TEST_HOME/tools/start.sh -ts");

        ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        close_epoll_thread();
        testEnvClean();
        RecoverTsCiCfg();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void tsdb_authentication_basic_02::SetUp()
{
    AW_CHECK_LOG_BEGIN();
}

void tsdb_authentication_basic_02::TearDown()
{
    DropCmTable("tablequery4");
    GmcConnOptionsDestroy(connOptions);
    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
}

class tsdb_authentication_basic_03 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/stop.sh -ts");  // 修改配置，先停服务
        system("ipcrm -a");
        system("sh $TEST_HOME/tools/modifyCfg.sh -ts recover");  // 恢复默认值
        TsDefulatDbFileClean();
        system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"userPolicyMode=2\"");  // 鉴权模式
        system("sh $TEST_HOME/tools/modifyCfg.sh -ts "
               "\"DBA=root:gmrule;gmips;gmids;gmadmin;tsdbdba1;tsdbdba2;tsdbdba3;tsdbdba4;tsdbdba5\"");
        system("sh $TEST_HOME/tools/start.sh -ts");

        ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);

        DropCmTable("tablequery4");
    }
    static void TearDownTestCase()
    {
        close_epoll_thread();
        testEnvClean();
        RecoverTsCiCfg();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void tsdb_authentication_basic_03::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    // 导入白名单用户，创建用户或用户组
#if defined(ENV_RTOSV2) || defined(ENV_RTOSV2X)
    ret = system("gmrule -c import_allowlist -f ./tsdbuser.gmuser -s channel:ctl_channel_tsdb");
#else
    ret = system("gmrule -c import_allowlist -f ./tsdbuser.gmuser -s usocket:/run/verona/unix_emserver_tsdb");
#endif
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void tsdb_authentication_basic_03::TearDown()
{
    DropCmTable("tablequery4");
    GmcConnOptionsDestroy(connOptions);
    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
}

class tsdb_authentication_basic_04 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/stop.sh -ts");                             // 修改配置，先停服务
        system("ipcrm -a");
        system("sh $TEST_HOME/tools/modifyCfg.sh -ts recover");  // 恢复默认值
        system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"userPolicyMode=1\"");  // 鉴权模式
        system("sh $TEST_HOME/tools/modifyCfg.sh -ts "
               "\"DBA=root:gmrule;gmips;gmids;gmadmin;tsdbdba1;tsdbdba2;tsdbdba3;tsdbdba4;tsdbdba5\"");
        system("sh $TEST_HOME/tools/start.sh -ts");
        ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        close_epoll_thread();
        testEnvClean();
        RecoverTsCiCfg();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void tsdb_authentication_basic_04::SetUp()
{
    AW_CHECK_LOG_BEGIN();
}

void tsdb_authentication_basic_04::TearDown()
{
    DropCmTable("tablequery4");
    GmcConnOptionsDestroy(connOptions);
    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
}

// 001、建表支持blob类型，blob列默认长度
TEST_F(tsdb_authentication_basic_02, Timing_040_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(1018004, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, 1018004, 1009009);

    // 导入白名单用户，创建用户或用户组
#if defined(ENV_RTOSV2) || defined(ENV_RTOSV2X)
    ret = system("gmrule -c import_allowlist -f ./tsdbuser.gmuser -s channel:ctl_channel_tsdb");
#else
    ret = system("gmrule -c import_allowlist -f ./tsdbuser.gmuser -s usocket:/run/verona/unix_emserver_tsdb");
#endif
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002、导入白名单，连接验证
TEST_F(tsdb_authentication_basic, Timing_040_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = DropCmTable("tablequery4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 导入白名单用户，创建用户或用户组

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 003、导入CREATE权限，系统权限验证
TEST_F(tsdb_authentication_basic_03, Timing_040_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 未导入系统权限
    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery4");

    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, 1018004);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1018004);

    // 导入系统权限，赋予白名单用户系统权限
#if defined(ENV_RTOSV2) || defined(ENV_RTOSV2X)
    ret = system("gmrule -c import_policy -f ./tsdbpolicy.gmpolicy -s channel:ctl_channel_tsdb");
#else
    ret = system("gmrule -c import_policy -f ./tsdbpolicy.gmpolicy -s usocket:/run/verona/unix_emserver_tsdb");
#endif
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004、导入ALTER权限，系统权限验证
TEST_F(tsdb_authentication_basic, Timing_040_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char ddlCommand[512];

    // 新增列
    sprintf(ddlCommand, "alter table tablequery4 add message3 blob(1600);");
    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005、导入DROP权限，系统权限验证
TEST_F(tsdb_authentication_basic, Timing_040_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char ddlCommand[512];

    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", "tablequery4");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006、导入进程A CREATE权限，未导入ALTER，DROP权限，系统权限验证
TEST_F(tsdb_authentication_basic_03, Timing_040_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 导入系统权限，赋予白名单用户系统权限
#if defined(ENV_RTOSV2) || defined(ENV_RTOSV2X)
    ret = system("gmrule -c import_policy -f ./tsdbpolicy_create.gmpolicy -s channel:ctl_channel_tsdb");
#else
    ret = system("gmrule -c import_policy -f ./tsdbpolicy_create.gmpolicy -s usocket:/run/verona/unix_emserver_tsdb");
#endif
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery4");

    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 新增列
    sprintf(ddlCommand, "alter table tablequery4 add message3 blob(1600);");
    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", "tablequery4");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007、导入进程A CREATE权限，导入进程B ALTER，DROP权限，系统权限验证
TEST_F(tsdb_authentication_basic, Timing_040_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 导入系统权限，赋予白名单用户系统权限
#if defined(ENV_RTOSV2) || defined(ENV_RTOSV2X)
    ret = system(
        "gmrule -c import_policy -f ./tsdbpolicy_multi_process.gmpolicy -s channel:ctl_channel_tsdb");
#else
    ret = system(
        "gmrule -c import_policy -f ./tsdbpolicy_multi_process.gmpolicy -s usocket:/run/verona/unix_emserver_tsdb");
#endif
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int conn_id;
    for (conn_id = 0; conn_id < 1; conn_id++) {
        if (conn_id == 0) {
            // 进程1
            ret = system("./multi_process --gtest_filter=*007 &");
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        }
    }

    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008、导入进程A CREATE权限，未导入进程B ALTER，DROP权限，系统权限验证
TEST_F(tsdb_authentication_basic, Timing_040_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int conn_id;
    for (conn_id = 0; conn_id < 1; conn_id++) {
        if (conn_id == 0) {
            // 进程1
            ret = system("./multi_process --gtest_filter=*008 &");
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        }
    }

    sleep(3);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1018004);
    ret = DropCmTable("tablequery4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009、GRANT INSERT权限，插入数据
TEST_F(tsdb_authentication_basic, Timing_040_009)
{
    char names[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t ids[] = {1, 2, 4, 9, 8, 14};
    int64_t worktimes[] = {24, 24, 11, 11, 12, 11};
    int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    char GrantCmd[512] = {0};
    // 授权对象权限，赋予白名单用户对象权限
    sprintf_s(GrantCmd, 512, "GRANT INSERT ON tablequery4 TO 'root:tsdb_authentica';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, 1004004);
    sprintf_s(GrantCmd, 512, "GRANT SELECT ON tablequery4 TO 'root:tsdb_authentica';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, 1004004);
    sprintf_s(GrantCmd, 512, "GRANT DELETE ON tablequery4 TO 'root:tsdb_authentica';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, 1004004);

    sprintf_s(GrantCmd, 512, "GRANT ALL ON tablequery4 TO 'root:tsdb_authentica';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, 1004004);

    sprintf_s(GrantCmd, 512, "REVOKE INSERT ON tablequery4 FROM 'root:tsdb_authentica';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, 1004004);

    sprintf_s(GrantCmd, 512, "REVOKE SELECT ON tablequery4 FROM 'root:tsdb_authentica';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, 1004004);

    sprintf_s(GrantCmd, 512, "REVOKE DELETE ON tablequery4 FROM 'root:tsdb_authentica';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, 1004004);

    sprintf_s(GrantCmd, 512, "REVOKE ALL ON tablequery4 FROM 'root:tsdb_authentica';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, 1004004);

    AW_MACRO_EXPECT_EQ_INT(ret, 1004004);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1004004);

    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, data.name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data.rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data.age, sizeof(data.age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data.id, sizeof(data.id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, data.worktime, sizeof(data.worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, data.salary, sizeof(data.salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[65535] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select message, worktime from tablequery4;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 65535;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message[i], 65535), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)6);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010、未GRANT INSERT权限，插入数据
TEST_F(tsdb_authentication_basic, Timing_040_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char names[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t ids[] = {1, 2, 4, 9, 8, 14};
    int64_t worktimes[] = {24, 24, 11, 11, 12, 11};
    int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, data.name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data.rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data.age, sizeof(data.age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data.id, sizeof(data.id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, data.worktime, sizeof(data.worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, data.salary, sizeof(data.salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[65535] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select message, worktime from tablequery4;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 65535;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message[i], 65535), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)6);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011、进程A创建表，进程A GRANT INSERT权限给进程B，进程B插入数据
TEST_F(tsdb_authentication_basic, Timing_040_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char GrantCmd[512] = {0};

    // 授权对象权限，赋予白名单用户对象权限
    sprintf_s(GrantCmd, 512, "GRANT INSERT ON tablequery4 TO 'root:multi_process';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    sprintf_s(GrantCmd, 512, "GRANT SELECT ON tablequery4 TO 'root:multi_process';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    int conn_id;
    for (conn_id = 0; conn_id < 1; conn_id++) {
        if (conn_id == 0) {
            // 进程1
            ret = system("./multi_process --gtest_filter=*011 &");
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        }
    }

    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012、进程A创建表，进程A 未GRANT INSERT权限给进程B，进程B插入数据
TEST_F(tsdb_authentication_basic, Timing_040_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int conn_id;
    for (conn_id = 0; conn_id < 1; conn_id++) {
        if (conn_id == 0) {
            // 进程1
            ret = system("./multi_process --gtest_filter=*012 &");
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        }
    }

    sleep(3);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1018004);
    ret = DropCmTable("tablequery4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013、GRANT 表b INSERT权限，GRANT 表a SELETE权限，从表a中读取数据插入到表b中，INSERT INTO操作
TEST_F(tsdb_authentication_basic, Timing_040_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char names[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t ids[] = {1, 2, 4, 9, 8, 14};
    int64_t worktimes[] = {24, 24, 11, 11, 12, 11};
    int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, data.name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data.rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data.age, sizeof(data.age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data.id, sizeof(data.id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, data.worktime, sizeof(data.worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, data.salary, sizeof(data.salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char ddlCommand2[512];
    // 建表b
    snprintf_s(ddlCommand2, sizeof(ddlCommand2), sizeof(ddlCommand2),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message "
        "blob(65535)) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery5");

    ret = GmcExecDirect(stmt, ddlCommand2, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char name[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t age[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t id[] = {1, 2, 4, 9, 8, 14};
    int64_t worktime[] = {24, 24, 11, 11, 12, 11};
    int64_t salary[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message2[][65535] = {"9021 6537", "0071 3520", "0010 0000", "", "3102 0021", "0000 0000"};
    rowNum = 6;

    uint8_t *newmessage2[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage2[j] = message2[j];
    }

    ret = GmcPrepareStmtByLabelName(stmt, "tablequery5", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, salary, sizeof(salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage2, sizeof(newmessage2[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    // insert into 相当于不清除表tablequery5的数据，然后把tablequery4的数据往tablequery5后按数据插入
    char commandInsert[512] = "insert into tablequery5 select * from tablequery4;";
    ret = GmcExecDirect(stmt, commandInsert, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    uint8_t message3[][65535] = {"9021 6537", "0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "0071 3520",
        "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    int64_t resultWorktime3[] = {24, 24, 11, 11, 12, 11, 24, 24, 11, 11, 12, 11};
    // 查询insert into后表tablequery5数据
    isNull = false;
    uint32_t i = 0;
    const char *queryCommand = "select message, worktime from tablequery5;";
    ExecQueryCmd(stmt, queryCommand);

    uint8_t messageRes[65535] = {0};
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 65535;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message3[i], 65535), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime3[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)12);

    DropCmTable("tablequery5");

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014、GRANT 表b INSERT权限，未GRANT 表a SELETE权限，从表a中读取数据插入到表b中，INSERT INTO操作
TEST_F(tsdb_authentication_basic, Timing_040_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char names[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t ids[] = {1, 2, 4, 9, 8, 14};
    int64_t worktimes[] = {24, 24, 11, 11, 12, 11};
    int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, data.name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data.rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data.age, sizeof(data.age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data.id, sizeof(data.id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, data.worktime, sizeof(data.worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, data.salary, sizeof(data.salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char ddlCommand2[512];
    // 建表b
    snprintf_s(ddlCommand2, sizeof(ddlCommand2), sizeof(ddlCommand2),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message "
        "blob(65535)) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery5");

    ret = GmcExecDirect(stmt, ddlCommand2, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char name[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t age[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t id[] = {1, 2, 4, 9, 8, 14};
    int64_t worktime[] = {24, 24, 11, 11, 12, 11};
    int64_t salary[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message2[][65535] = {"9021 6537", "0071 3520", "0010 0000", "", "3102 0021", "0000 0000"};
    rowNum = 6;

    uint8_t *newmessage2[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage2[j] = message2[j];
    }

    ret = GmcPrepareStmtByLabelName(stmt, "tablequery5", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, salary, sizeof(salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage2, sizeof(newmessage2[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    // insert into 相当于不清除表tablequery5的数据，然后把tablequery4的数据往tablequery5后按数据插入
    char commandInsert[512] = "insert into tablequery5 select * from tablequery4;";
    ret = GmcExecDirect(stmt, commandInsert, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    uint8_t message3[][65535] = {"9021 6537", "0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "0071 3520",
        "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    int64_t resultWorktime3[] = {24, 24, 11, 11, 12, 11, 24, 24, 11, 11, 12, 11};
    // 查询insert into后表tablequery5数据
    isNull = false;
    uint32_t i = 0;
    const char *queryCommand = "select message, worktime from tablequery5;";
    ExecQueryCmd(stmt, queryCommand);

    uint8_t messageRes[65535] = {0};
    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 65535;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message3[i], 65535), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime3[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)12);

    DropCmTable("tablequery5");

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015、进程A创建表a,b,进程A GRANT 进程B表b INSERT权限，GRANT 进程B表a
// SELETE权限，进程B从表a中读取数据插入到表b中，INSERT INTO操作
TEST_F(tsdb_authentication_basic, Timing_040_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char names[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t ids[] = {1, 2, 4, 9, 8, 14};
    int64_t worktimes[] = {24, 24, 11, 11, 12, 11};
    int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    char GrantCmd[512] = {0};
    sprintf_s(GrantCmd, 512, "GRANT SELECT ON tablequery4 TO 'root:multi_process';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, data.name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data.rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data.age, sizeof(data.age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data.id, sizeof(data.id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, data.worktime, sizeof(data.worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, data.salary, sizeof(data.salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char ddlCommand2[512];
    // 建表b
    snprintf_s(ddlCommand2, sizeof(ddlCommand2), sizeof(ddlCommand2),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message "
        "blob(65535)) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery5");

    ret = GmcExecDirect(stmt, ddlCommand2, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 授权对象权限，赋予白名单用户对象权限
    sprintf_s(GrantCmd, 512, "GRANT INSERT ON tablequery5 TO 'root:multi_process';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sprintf_s(GrantCmd, 512, "GRANT SELECT ON tablequery5 TO 'root:multi_process';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char name[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t age[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t id[] = {1, 2, 4, 9, 8, 14};
    int64_t worktime[] = {24, 24, 11, 11, 12, 11};
    int64_t salary[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message2[][65535] = {"9021 6537", "0071 3520", "0010 0000", "", "3102 0021", "0000 0000"};
    rowNum = 6;

    uint8_t *newmessage2[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage2[j] = message2[j];
    }

    ret = GmcPrepareStmtByLabelName(stmt, "tablequery5", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, age, sizeof(age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, worktime, sizeof(worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, salary, sizeof(salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage2, sizeof(newmessage2[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    int conn_id;
    for (conn_id = 0; conn_id < 1; conn_id++) {
        if (conn_id == 0) {
            // 进程1
            ret = system("./multi_process --gtest_filter=*015 &");
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        }
    }

    sleep(3);

    DropCmTable("tablequery5");

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016、进程A创建表a,b,进程A GRANT 进程B表b INSERT权限，未GRANT 进程B表a
// SELETE权限，进程B从表a中读取数据插入到表b中，INSERT INTO操作
TEST_F(tsdb_authentication_basic, Timing_040_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char names[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t ids[] = {1, 2, 4, 9, 8, 14};
    int64_t worktimes[] = {24, 24, 11, 11, 12, 11};
    int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, data.name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data.rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data.age, sizeof(data.age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data.id, sizeof(data.id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, data.worktime, sizeof(data.worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, data.salary, sizeof(data.salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char ddlCommand2[512];
    // 建表b
    snprintf_s(ddlCommand2, sizeof(ddlCommand2), sizeof(ddlCommand2),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message "
        "blob(65535)) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery5");

    ret = GmcExecDirect(stmt, ddlCommand2, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char GrantCmd[512] = {0};
    // 授权对象权限，赋予白名单用户对象权限
    sprintf_s(GrantCmd, 512, "GRANT INSERT ON tablequery5 TO 'root:multi_process';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    int conn_id;
    for (conn_id = 0; conn_id < 1; conn_id++) {
        if (conn_id == 0) {
            // 进程1
            ret = system("./multi_process --gtest_filter=*016 &");
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        }
    }

    sleep(3);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1018004);
    DropCmTable("tablequery5");

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 017、进程A创建表a,b,进程A 未GRANT 进程B表b INSERT权限，未GRANT 进程B表a
// SELETE权限，进程B从表a中读取数据插入到表b中，INSERT INTO操作
TEST_F(tsdb_authentication_basic, Timing_040_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char GrantCmd[512] = {0};
    // 授权对象权限，赋予白名单用户对象权限
    sprintf_s(GrantCmd, 512, "GRANT SELECT ON tablequery4 TO 'root:multi_process';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char ddlCommand2[512];
    // 建表b
    snprintf_s(ddlCommand2, sizeof(ddlCommand2), sizeof(ddlCommand2),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message "
        "blob(65535)) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery5");

    ret = GmcExecDirect(stmt, ddlCommand2, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    int conn_id;
    for (conn_id = 0; conn_id < 1; conn_id++) {
        if (conn_id == 0) {
            // 进程1
            ret = system("./multi_process --gtest_filter=*017 &");
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        }
    }

    sleep(3);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1018004);
    DropCmTable("tablequery5");

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 018、进程A创建表，进程A，GRANT 对象权限进程Bpriv_type为ALL
TEST_F(tsdb_authentication_basic, Timing_040_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char GrantCmd[512] = {0};
    // 授权对象权限，赋予白名单用户对象权限
    sprintf_s(GrantCmd, 512, "GRANT ALL ON tablequery4 TO 'root:multi_process';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    int conn_id;
    for (conn_id = 0; conn_id < 1; conn_id++) {
        if (conn_id == 0) {
            // 进程1
            ret = system("./multi_process --gtest_filter=*024 &");
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        }
    }

    sleep(3);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 019、进程A创建表，进程A，GRANT 对象权限进程Bpriv_type为ALL PRIVILEGES
TEST_F(tsdb_authentication_basic, Timing_040_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char GrantCmd[512] = {0};
    // 授权对象权限，赋予白名单用户对象权限
    sprintf_s(GrantCmd, 512, "GRANT ALL PRIVILEGES ON tablequery4 TO 'root:multi_process';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    int conn_id;
    for (conn_id = 0; conn_id < 1; conn_id++) {
        if (conn_id == 0) {
            // 进程1
            ret = system("./multi_process --gtest_filter=*024 &");
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        }
    }

    sleep(3);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 020、GRANT 对象权限table_name为多表
TEST_F(tsdb_authentication_basic, Timing_040_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char ddlCommand2[512];

    // 建表b
    snprintf_s(ddlCommand2, sizeof(ddlCommand2), sizeof(ddlCommand2),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message "
        "blob(65535)) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery5");

    ret = GmcExecDirect(stmt, ddlCommand2, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char GrantCmd[512] = {0};
    // 授权对象权限，赋予白名单用户对象权限
    sprintf_s(GrantCmd, 512, "GRANT ALL ON tablequery4 tablequery5 TO 'root:tsdb_authentica';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, 1009000);

    AW_ADD_ERRNUM_WHITE_LIST(1, 1009000);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 021、GRANT 对象权限table_name不存在
TEST_F(tsdb_authentication_basic, Timing_040_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    DropCmTable("tablequery4");
    char GrantCmd[512] = {0};
    // 授权对象权限，赋予白名单用户对象权限
    sprintf_s(GrantCmd, 512, "GRANT ALL ON tablequery4 TO 'root:tsdb_authentica';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, 1009010);

    AW_ADD_ERRNUM_WHITE_LIST(1, 1009010);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 022、进程A创建表，进程A GRANT 对象权限，user_name进程名长度最大校验
TEST_F(tsdb_authentication_basic, Timing_040_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char GrantCmd[512] = {0};

    // 授权对象权限，user_name进程名长度小于进程名（因为这里进程名最大为15，所以小于的话为14）
    sprintf_s(GrantCmd, 512, "GRANT INSERT ON tablequery4 TO 'root:tsdb_authentic';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, 1009009);

    // 授权对象权限，user_name进程名长度大于进程名（因为这里进程名最大为15，所以大于的话为16）
    sprintf_s(GrantCmd, 512, "GRANT INSERT ON tablequery4 TO 'root:tsdb_authenticat';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, 1004009);

    AW_ADD_ERRNUM_WHITE_LIST(2, 1009009, 1004009);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 023、进程A创建表，进程A GRANT 对象权限，user_name为多个进程（进程B 进程C）
TEST_F(tsdb_authentication_basic, Timing_040_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char GrantCmd[512] = {0};
    // 授权对象权限，赋予白名单用户对象权限
    sprintf_s(GrantCmd, 512, "GRANT ALL ON tablequery4 TO 'root:multi_process','root:multi_process_2';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    int conn_id;
    for (conn_id = 0; conn_id < 1; conn_id++) {
        if (conn_id == 0) {
            // 进程1
            ret = system("./multi_process --gtest_filter=*029 &");
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        }
    }

    sleep(3);

    for (conn_id = 0; conn_id < 1; conn_id++) {
        if (conn_id == 0) {
            // 进程1
            ret = system("./multi_process_2 --gtest_filter=*029 &");
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        }
    }

    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 024、进程A创建表，进程A，REVOKE 对象权限进程B priv_type为ALL
TEST_F(tsdb_authentication_basic, Timing_040_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char GrantCmd[512] = {0};
    // 授权对象权限，赋予白名单用户对象权限
    sprintf_s(GrantCmd, 512, "GRANT ALL ON tablequery4 TO 'root:multi_process';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    int conn_id;
    for (conn_id = 0; conn_id < 1; conn_id++) {
        if (conn_id == 0) {
            // 进程1
            ret = system("./multi_process --gtest_filter=*024 &");
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        }
    }

    sleep(3);

    sprintf_s(GrantCmd, 512, "REVOKE ALL ON tablequery4 FROM 'root:multi_process';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    conn_id;
    for (conn_id = 0; conn_id < 1; conn_id++) {
        if (conn_id == 0) {
            // 进程1
            ret = system("./multi_process --gtest_filter=*024_2 &");
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        }
    }

    sleep(3);

    AW_ADD_ERRNUM_WHITE_LIST(1, 1018004);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 025、进程A创建表，进程A，REVOKE 对象权限进程B priv_type为ALL PRIVILEGES
TEST_F(tsdb_authentication_basic, Timing_040_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char GrantCmd[512] = {0};
    // 授权对象权限，赋予白名单用户对象权限
    sprintf_s(GrantCmd, 512, "GRANT ALL PRIVILEGES ON tablequery4 TO 'root:multi_process';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    int conn_id;
    for (conn_id = 0; conn_id < 1; conn_id++) {
        if (conn_id == 0) {
            // 进程1
            ret = system("./multi_process --gtest_filter=*024 &");
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        }
    }

    sleep(3);

    sprintf_s(GrantCmd, 512, "REVOKE ALL PRIVILEGES ON tablequery4 FROM 'root:multi_process';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    conn_id;
    for (conn_id = 0; conn_id < 1; conn_id++) {
        if (conn_id == 0) {
            // 进程1
            ret = system("./multi_process --gtest_filter=*024_2 &");
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        }
    }

    sleep(3);

    AW_ADD_ERRNUM_WHITE_LIST(1, 1018004);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 026、REVOKE 对象权限table_name为多表
TEST_F(tsdb_authentication_basic, Timing_040_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char ddlCommand2[512];

    // 建表b
    snprintf_s(ddlCommand2, sizeof(ddlCommand2), sizeof(ddlCommand2),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message "
        "blob(65535)) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery5");

    ret = GmcExecDirect(stmt, ddlCommand2, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char GrantCmd[512] = {0};
    // 授权对象权限，赋予白名单用户对象权限

    sprintf_s(GrantCmd, 512, "REVOKE  ALL ON tablequery4 tablequery5 FROM 'root:tsdb_authentica';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, 1009000);

    AW_ADD_ERRNUM_WHITE_LIST(1, 1009000);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 027、REVOKE对象权限table_name不存在
TEST_F(tsdb_authentication_basic, Timing_040_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    DropCmTable("tablequery4");
    char GrantCmd[512] = {0};
    // 授权对象权限，赋予白名单用户对象权限
    sprintf_s(GrantCmd, 512, "REVOKE  ALL ON tablequery4  FROM 'root:tsdb_authentica';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, 1009010);

    AW_ADD_ERRNUM_WHITE_LIST(1, 1009010);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 028、进程A创建表，进程A REVOKE 对象权限，user_name进程名长度最大校验
TEST_F(tsdb_authentication_basic, Timing_040_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char GrantCmd[512] = {0};

    // 授权对象权限，user_name进程名长度小于进程名（因为这里进程名最大为15，所以小于的话为14）
    sprintf_s(GrantCmd, 512, "REVOKE  INSERT ON tablequery4 FROM 'root:tsdb_authentic';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, 1009009);

    // 授权对象权限，user_name进程名长度大于进程名（因为这里进程名最大为15，所以大于的话为16）
    sprintf_s(GrantCmd, 512, "REVOKE  INSERT ON tablequery4 FROM 'root:tsdb_authenticat';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, 1004009);

    AW_ADD_ERRNUM_WHITE_LIST(2, 1009009, 1004009);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 028、进程A创建表，进程A REVOKE 对象权限，user_name进程名长度最大校验
TEST_F(tsdb_authentication_basic, Timing_040_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char GrantCmd[512] = {0};

    // 授权对象权限，赋予白名单用户对象权限
    sprintf_s(GrantCmd, 512, "GRANT ALL ON tablequery4 TO 'root:multi_process','root:multi_process_2';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    int conn_id;
    for (conn_id = 0; conn_id < 1; conn_id++) {
        if (conn_id == 0) {
            // 进程1
            ret = system("./multi_process --gtest_filter=*029 &");
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        }
    }

    sleep(3);

    for (conn_id = 0; conn_id < 1; conn_id++) {
        if (conn_id == 0) {
            // 进程1
            ret = system("./multi_process_2 --gtest_filter=*029 &");
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        }
    }

    sleep(3);

    sprintf_s(GrantCmd, 512, "REVOKE ALL PRIVILEGES ON tablequery4 FROM 'root:multi_process','root:multi_process_2';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    for (conn_id = 0; conn_id < 1; conn_id++) {
        if (conn_id == 0) {
            // 进程1
            ret = system("./multi_process --gtest_filter=*024_2 &");
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        }
    }

    for (conn_id = 0; conn_id < 1; conn_id++) {
        if (conn_id == 0) {
            // 进程1
            ret = system("./multi_process_2 --gtest_filter=*024_2 &");
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        }
    }

    sleep(3);

    AW_ADD_ERRNUM_WHITE_LIST(2, 1018004, 1001000);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 030、GRANT DELETE权限，老化数据
TEST_F(tsdb_authentication_basic, Timing_040_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char names[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t ids[] = {1, 2, 4, 9, 8, 14};
    int64_t worktimes[] = {24, 24, 11, 11, 12, 11};
    int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, data.name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data.rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data.age, sizeof(data.age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data.id, sizeof(data.id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, data.worktime, sizeof(data.worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, data.salary, sizeof(data.salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char ddlCommand[512] = {0};
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand), "SELECT tsdb_aging('tablequery4');");
    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(1);

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[65535] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select message, worktime from tablequery4;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 65535;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message[i], 65535), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)0);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 031、未GRANT DELETE权限，老化数据
TEST_F(tsdb_authentication_basic, Timing_040_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char names[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t ids[] = {1, 2, 4, 9, 8, 14};
    int64_t worktimes[] = {24, 24, 11, 11, 12, 11};
    int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, data.name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data.rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data.age, sizeof(data.age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data.id, sizeof(data.id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, data.worktime, sizeof(data.worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, data.salary, sizeof(data.salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char ddlCommand[512] = {0};
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand), "SELECT tsdb_aging('tablequery4');");
    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(1);

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[65535] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select message, worktime from tablequery4;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 65535;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message[i], 65535), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)0);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 032、进程A创建表，进程A GRANT DELETE权限给进程B，进程B老化数据
TEST_F(tsdb_authentication_basic, Timing_040_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char names[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t ids[] = {1, 2, 4, 9, 8, 14};
    int64_t worktimes[] = {24, 24, 11, 11, 12, 11};
    int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    char GrantCmd[512] = {0};

    sprintf_s(GrantCmd, 512, "GRANT SELECT ON tablequery4 TO 'root:multi_process';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    sprintf_s(GrantCmd, 512, "GRANT DELETE ON tablequery4 TO 'root:multi_process';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, data.name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data.rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data.age, sizeof(data.age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data.id, sizeof(data.id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, data.worktime, sizeof(data.worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, data.salary, sizeof(data.salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    int conn_id;
    for (conn_id = 0; conn_id < 1; conn_id++) {
        if (conn_id == 0) {
            // 进程1
            ret = system("./multi_process --gtest_filter=*032 &");
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        }
    }

    sleep(3);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 033、进程A创建表，进程A未 GRANT DELETE权限给进程B，进程B老化数据
TEST_F(tsdb_authentication_basic, Timing_040_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char names[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t ids[] = {1, 2, 4, 9, 8, 14};
    int64_t worktimes[] = {24, 24, 11, 11, 12, 11};
    int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, data.name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data.rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data.age, sizeof(data.age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data.id, sizeof(data.id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, data.worktime, sizeof(data.worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, data.salary, sizeof(data.salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    int conn_id;
    for (conn_id = 0; conn_id < 1; conn_id++) {
        if (conn_id == 0) {
            // 进程1
            ret = system("./multi_process --gtest_filter=*033 &");
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        }
    }

    sleep(3);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1018004);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 034、GRANT SELETE权限，查询数据
TEST_F(tsdb_authentication_basic, Timing_040_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char names[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t ids[] = {1, 2, 4, 9, 8, 14};
    int64_t worktimes[] = {24, 24, 11, 11, 12, 11};
    int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, data.name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data.rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data.age, sizeof(data.age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data.id, sizeof(data.id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, data.worktime, sizeof(data.worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, data.salary, sizeof(data.salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[65535] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select message, worktime from tablequery4;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 65535;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message[i], 65535), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)6);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 035、未GRANT SELETE权限，查询数据
TEST_F(tsdb_authentication_basic, Timing_040_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char names[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t ids[] = {1, 2, 4, 9, 8, 14};
    int64_t worktimes[] = {24, 24, 11, 11, 12, 11};
    int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, data.name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data.rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data.age, sizeof(data.age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data.id, sizeof(data.id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, data.worktime, sizeof(data.worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, data.salary, sizeof(data.salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[65535] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select message, worktime from tablequery4;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 65535;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message[i], 65535), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)6);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 036、进程A创建表，进程A GRANT SELETE权限给进程B，进程B查询数据
TEST_F(tsdb_authentication_basic, Timing_040_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char GrantCmd[512] = {0};

    // 授权对象权限，赋予白名单用户对象权限
    sprintf_s(GrantCmd, 512, "GRANT INSERT ON tablequery4 TO 'root:multi_process';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    sprintf_s(GrantCmd, 512, "GRANT SELECT ON tablequery4 TO 'root:multi_process';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    int conn_id;
    for (conn_id = 0; conn_id < 1; conn_id++) {
        if (conn_id == 0) {
            // 进程1
            ret = system("./multi_process --gtest_filter=*011 &");
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        }
    }

    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 037、进程A创建表，进程A未 GRANT SELETE权限给进程B，进程B查询数据
TEST_F(tsdb_authentication_basic, Timing_040_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int conn_id;
    for (conn_id = 0; conn_id < 1; conn_id++) {
        if (conn_id == 0) {
            // 进程1
            ret = system("./multi_process --gtest_filter=*012 &");
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        }
    }

    sleep(3);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1018004);
    ret = DropCmTable("tablequery4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 038、GRANT SELETE权限，COPY TO查询数据
TEST_F(tsdb_authentication_basic, Timing_040_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char names[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t ids[] = {1, 2, 4, 9, 8, 14};
    int64_t worktimes[] = {24, 24, 11, 11, 12, 11};
    int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, data.name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data.rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data.age, sizeof(data.age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data.id, sizeof(data.id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, data.worktime, sizeof(data.worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, data.salary, sizeof(data.salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char ddlCommand[512] = {0};
    sprintf(ddlCommand,
        "COPY (SELECT id, message, worktime FROM tablequery4) TO"
        "'%s/test/sdv/testcases/25_Timing/040_tsdb_authentication/data.csv';",
        dir);

    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *expectContent = R"(1,0071 3520,24
2,0010 0000,24
4,,11
9,3102 0021,11
8,0000 0000,12
14,9021 6537,11
)";

    // 检查csv文件是否生成
    AW_MACRO_ASSERT_EQ_INT(true, isDataCsvExist());
    char actualContent[1024] = {0};
    (void)GetCsvFileContent(actualContent);
    // 检查获取到内容是否正确
    AW_MACRO_ASSERT_EQ_STR(expectContent, actualContent);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 039、未GRANT SELETE权限，COPY TO查询数据
TEST_F(tsdb_authentication_basic, Timing_040_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char names[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t ids[] = {1, 2, 4, 9, 8, 14};
    int64_t worktimes[] = {24, 24, 11, 11, 12, 11};
    int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, data.name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data.rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data.age, sizeof(data.age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data.id, sizeof(data.id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, data.worktime, sizeof(data.worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, data.salary, sizeof(data.salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char ddlCommand[512] = {0};
    sprintf(ddlCommand,
        "COPY (SELECT id, message, worktime FROM tablequery4) TO"
        "'%s/test/sdv/testcases/25_Timing/040_tsdb_authentication/data.csv';",
        dir);

    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *expectContent = R"(1,0071 3520,24
2,0010 0000,24
4,,11
9,3102 0021,11
8,0000 0000,12
14,9021 6537,11
)";

    // 检查csv文件是否生成
    AW_MACRO_ASSERT_EQ_INT(true, isDataCsvExist());
    char actualContent[1024] = {0};
    (void)GetCsvFileContent(actualContent);
    // 检查获取到内容是否正确
    AW_MACRO_ASSERT_EQ_STR(expectContent, actualContent);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 040、进程A创建表，进程A GRANT SELETE权限给进程B，进程B COPY TO查询数据
TEST_F(tsdb_authentication_basic, Timing_040_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char GrantCmd[512] = {0};

    // 授权对象权限，赋予白名单用户对象权限
    sprintf_s(GrantCmd, 512, "GRANT INSERT ON tablequery4 TO 'root:multi_process';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    sprintf_s(GrantCmd, 512, "GRANT SELECT ON tablequery4 TO 'root:multi_process';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    int conn_id;
    for (conn_id = 0; conn_id < 1; conn_id++) {
        if (conn_id == 0) {
            // 进程1
            ret = system("./multi_process --gtest_filter=*040 &");
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        }
    }

    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 041、进程A创建表，进程A 未GRANT SELETE权限给进程B，进程B COPY TO查询数据
TEST_F(tsdb_authentication_basic, Timing_040_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int conn_id;
    for (conn_id = 0; conn_id < 1; conn_id++) {
        if (conn_id == 0) {
            // 进程1
            ret = system("./multi_process --gtest_filter=*041 &");
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        }
    }

    sleep(3);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1018004);
    ret = DropCmTable("tablequery4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 042、进程A创建表，进程A REVOKE  INSERT/DELETE/SELETE对象权限给进程B，dml操作
TEST_F(tsdb_authentication_basic, Timing_040_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char GrantCmd[512] = {0};
    // 授权对象权限，赋予白名单用户对象权限
    sprintf_s(GrantCmd, 512, "GRANT INSERT ON tablequery4 TO 'root:multi_process';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    sprintf_s(GrantCmd, 512, "GRANT SELECT ON tablequery4 TO 'root:multi_process';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    sprintf_s(GrantCmd, 512, "GRANT DELETE  ON tablequery4 TO 'root:multi_process';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    int conn_id;
    for (conn_id = 0; conn_id < 1; conn_id++) {
        if (conn_id == 0) {
            // 进程1
            ret = system("./multi_process --gtest_filter=*024 &");
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        }
    }

    sleep(3);

    sprintf_s(GrantCmd, 512, "REVOKE INSERT ON tablequery4 FROM 'root:multi_process';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sprintf_s(GrantCmd, 512, "REVOKE SELECT ON tablequery4 FROM 'root:multi_process';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sprintf_s(GrantCmd, 512, "REVOKE DELETE ON tablequery4 FROM 'root:multi_process';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    conn_id;
    for (conn_id = 0; conn_id < 1; conn_id++) {
        if (conn_id == 0) {
            // 进程1
            ret = system("./multi_process --gtest_filter=*024_2 &");
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        }
    }

    sleep(3);

    AW_ADD_ERRNUM_WHITE_LIST(1, 1018004);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 043、进程A创建表，进程A，REVOKE 对象权限进程B priv_type为多个组合INSERT,SELECT
TEST_F(tsdb_authentication_basic, Timing_040_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char GrantCmd[512] = {0};
    // 授权对象权限，赋予白名单用户对象权限
    sprintf_s(GrantCmd, 512, "GRANT INSERT,SELETE,DELETE ON tablequery4 TO 'root:multi_process';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, 1009000);

    AW_ADD_ERRNUM_WHITE_LIST(1, 1009000);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 044、GRANT CREATE/ALTER/DROP系统权限
TEST_F(tsdb_authentication_basic, Timing_040_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char GrantCmd[512] = {0};
    // 授权对象权限，赋予白名单用户对象权限
    sprintf_s(GrantCmd, 512, "GRANT CREATE ON tablequery4 TO 'root:multi_process';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, 1009000);

    AW_ADD_ERRNUM_WHITE_LIST(1, 1009000);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 045、进程A创建表，进程B GRANT INSERT/DELETE/SELETE对象权限
TEST_F(tsdb_authentication_basic, Timing_040_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int conn_id;
    for (conn_id = 0; conn_id < 1; conn_id++) {
        if (conn_id == 0) {
            // 进程1
            ret = system("./multi_process --gtest_filter=*045 &");
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        }
    }

    sleep(3);

    AW_ADD_ERRNUM_WHITE_LIST(1, 1018004);
    ret = DropCmTable("tablequery4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 046、重复GRANT对象权限
TEST_F(tsdb_authentication_basic, Timing_040_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char GrantCmd[512] = {0};

    // 授权对象权限，赋予白名单用户对象权限
    sprintf_s(GrantCmd, 512, "GRANT INSERT ON tablequery4 TO 'root:multi_process';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    sprintf_s(GrantCmd, 512, "GRANT SELECT ON tablequery4 TO 'root:multi_process';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    sprintf_s(GrantCmd, 512, "GRANT INSERT ON tablequery4 TO 'root:multi_process';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, 1018001);

    sprintf_s(GrantCmd, 512, "GRANT SELECT ON tablequery4 TO 'root:multi_process';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, 1018001);

    int conn_id;
    for (conn_id = 0; conn_id < 1; conn_id++) {
        if (conn_id == 0) {
            // 进程1
            ret = system("./multi_process --gtest_filter=*011 &");
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        }
    }

    sleep(3);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1018001);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 047、重复REVOKE对象权限
TEST_F(tsdb_authentication_basic, Timing_040_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char GrantCmd[512] = {0};

    // 授权对象权限，赋予白名单用户对象权限
    sprintf_s(GrantCmd, 512, "GRANT INSERT ON tablequery4 TO 'root:multi_process';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    sprintf_s(GrantCmd, 512, "GRANT SELECT ON tablequery4 TO 'root:multi_process';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    sprintf_s(GrantCmd, 512, "REVOKE INSERT ON tablequery4 FROM 'root:multi_process';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    sprintf_s(GrantCmd, 512, "REVOKE SELECT ON tablequery4 FROM 'root:multi_process';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    sprintf_s(GrantCmd, 512, "REVOKE INSERT ON tablequery4 FROM 'root:multi_process';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, 1009009);

    sprintf_s(GrantCmd, 512, "REVOKE SELECT ON tablequery4 FROM 'root:multi_process';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, 1009009);

    int conn_id;
    for (conn_id = 0; conn_id < 1; conn_id++) {
        if (conn_id == 0) {
            // 进程1
            ret = system("./multi_process --gtest_filter=*012 &");
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        }
    }

    sleep(3);
    AW_ADD_ERRNUM_WHITE_LIST(2, 1009009, 1018004);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 048、GRANT或REVOKE不存在的对象权限
TEST_F(tsdb_authentication_basic, Timing_040_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char GrantCmd[512] = {0};

    // 授权对象权限，赋予白名单用户对象权限
    sprintf_s(GrantCmd, 512, "GRANT INSERT111 ON tablequery4 TO 'root:multi_process';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, 1009000);

    sprintf_s(GrantCmd, 512, "REVOKE INSERT ON tablequery4 FROM 'root:multi_process';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, 1009009);

    int conn_id;
    for (conn_id = 0; conn_id < 1; conn_id++) {
        if (conn_id == 0) {
            // 进程1
            ret = system("./multi_process --gtest_filter=*012 &");
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        }
    }

    sleep(3);
    AW_ADD_ERRNUM_WHITE_LIST(3, 1009000, 1009009, 1018004);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 049、重复10次GRANT，REVOKE 对象权限，每次GRANT，REVOKE权限后dml操作
TEST_F(tsdb_authentication_basic, Timing_040_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    for (int count = 0; count < 10; count++) {
        char GrantCmd[512] = {0};
        // 授权对象权限，赋予白名单用户对象权限
        sprintf_s(GrantCmd, 512, "GRANT ALL ON tablequery4 TO 'root:multi_process';");
        ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        int conn_id;
        for (conn_id = 0; conn_id < 1; conn_id++) {
            if (conn_id == 0) {
                // 进程1
                ret = system("./multi_process --gtest_filter=*049 &");
                AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
            }
        }

        sleep(3);

        sprintf_s(GrantCmd, 512, "REVOKE ALL ON tablequery4 FROM 'root:multi_process';");
        ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        conn_id;
        for (conn_id = 0; conn_id < 1; conn_id++) {
            if (conn_id == 0) {
                // 进程1
                ret = system("./multi_process --gtest_filter=*024_2 &");
                AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
            }
        }

        sleep(3);
    }

    AW_ADD_ERRNUM_WHITE_LIST(1, 1018004);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 050、userPolicyMode为1，打日志
TEST_F(tsdb_authentication_basic_04, Timing_040_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    GmcConnOptionsCreate(&connOptions);
    GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver_tsdb");
    GmcConnOptionsSetCSRead(connOptions);
    GmcConnOptionsSetCSMode(connOptions);
    GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    GmcAllocStmt(conn, &stmt);

    char names[][10] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t ages[] = {29, 30, 19, 23, 45, INT64_MAX};
    int64_t ids[] = {1, 2, 4, 9, 8, 14};
    int64_t worktimes[] = {24, 24, 11, 11, 12, 11};
    int64_t salaries[] = {20000, 10000, 4000, 30000, 31000, 10000};
    uint8_t message[][65535] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537"};
    uint32_t rowNum = 6;
    uint8_t *newmessage[rowNum];
    for (uint32_t j = 0; j < rowNum; j++) {
        newmessage[j] = message[j];
    }

    Data data = {.name = names, .age = ages, .id = ids, .worktime = worktimes, .salary = salaries, .rowNum = rowNum};

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(10), age integer, id integer, worktime integer, salary integer,  message blob) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours');",
        "tablequery4");

    ret = GmcExecDirect(stmt, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = GmcPrepareStmtByLabelName(stmt, "tablequery4", GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_FIXED, data.name, 10, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &data.rowNum, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, data.age, sizeof(data.age[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, data.id, sizeof(data.id[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, data.worktime, sizeof(data.worktime[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, data.salary, sizeof(data.salary[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, newmessage, sizeof(newmessage[0]), NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    uint32_t propSize = 0;
    bool eof = false;

    int64_t worktimeRes = 0;
    uint8_t messageRes[65535] = {0};
    int64_t resultWorktime[] = {24, 24, 11, 11, 12, 11};

    bool isNull = false;
    uint32_t i = 0;

    const char *queryCommand = "select message, worktime from tablequery4;";
    ExecQueryCmd(stmt, queryCommand);

    eof = false;
    while (!eof) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = 65535;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &messageRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &worktimeRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(memcmp(messageRes, message[i], 65535), 0);
        AW_MACRO_EXPECT_EQ_INT(worktimeRes, resultWorktime[i]);

        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)6);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNDEFINED_OBJECT);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 051、GRANT 对象权限table_name为namespace_name:table_name
// tsdb中 namespace_name:table_name能否成功?
// tsdb只有默认的namespace，因此不支持指定此种方式
TEST_F(tsdb_authentication_basic, Timing_040_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char GrantCmd[512] = {0};
    // 授权对象权限，赋予白名单用户对象权限
    sprintf_s(GrantCmd, 512, "GRANT INSERT ON pulic:tablequery4 TO 'root:tsdb_authentica';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, 1009000);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1009000);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 052、GRANT REVOKE命令格式不对，如少字符，多空格等
TEST_F(tsdb_authentication_basic, Timing_040_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char GrantCmd[512] = {0};
    // 授权对象权限，赋予白名单用户对象权限
    sprintf_s(GrantCmd, 512, "GRANT INSERT tablequery4 TO 'root:tsdb_authentica';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, 1009000);

    sprintf_s(GrantCmd, 512, "GRANT INSERT ON tablequery4 TO ':tsdb_authentica';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, 1004009);

    sprintf_s(GrantCmd, 512, "GRANT INSERT ON tablequery4 TO 'root:';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, 1004009);

    sprintf_s(GrantCmd, 512, "GRANT INSERT ON tablequery4 TO 'root: tsdb_authentica';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, 1004009);

    sprintf_s(GrantCmd, 512, "GRANT INSERT ON tablequery4 TO 'root:tsdb_authentica ';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, 1004009);

    AW_ADD_ERRNUM_WHITE_LIST(2, 1009000, 1004009);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 053、进程名改为DBA,不赋权，执行dml
TEST_F(tsdb_authentication_basic, Timing_040_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int conn_id;
    for (conn_id = 0; conn_id < 1; conn_id++) {
        if (conn_id == 0) {
            // 进程1
            ret = system("./tsdbdba1 --gtest_filter=*049 &");
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        }
    }

    sleep(3);
    ret = DropCmTable("tablequery4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 054、GRANT 对象权限错误后，执行dml
TEST_F(tsdb_authentication_basic, Timing_040_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char GrantCmd[512] = {0};
    // 授权对象权限，赋予白名单用户对象权限
    sprintf_s(GrantCmd, 512, "GRANT ALL ON tablequery4 TO 'root:multi_process';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    sprintf_s(GrantCmd, 512, "GRANT ALL ON tablequery4 TO 'root:multi_process';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, 1018001);

    int conn_id;
    for (conn_id = 0; conn_id < 1; conn_id++) {
        if (conn_id == 0) {
            // 进程1
            ret = system("./multi_process --gtest_filter=*049 &");
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        }
    }

    sleep(3);
    AW_ADD_ERRNUM_WHITE_LIST(1, 1018001);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 055、重启之后权限不生效
TEST_F(tsdb_authentication_basic, Timing_040_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char GrantCmd[512] = {0};
    // 授权对象权限，赋予白名单用户对象权限
    sprintf_s(GrantCmd, 512, "GRANT ALL ON tablequery4 TO 'root:multi_process';");
    ret = GmcExecDirect(stmt, GrantCmd, strlen(GrantCmd));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 进程内不支持重启操作，gdb到这里到这里，，手动重启服务，继续执行进程multi_process连接会报1018004错误，重启后权限不生效
    int conn_id;
    for (conn_id = 0; conn_id < 1; conn_id++) {
        if (conn_id == 0) {
            // 进程1
            ret = system("./multi_process --gtest_filter=*049 &");
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        }
    }

    sleep(3);

    AW_FUN_Log(LOG_STEP, "test end.");
}
