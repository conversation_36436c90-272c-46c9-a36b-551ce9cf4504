/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 Description  : 505时序支持Text类型
 Notes        : 新字段类型Text测试
 Author       : yushijin y30011459
 Modification :
 create       : 2024/09/06
**************************************************************************** */
#include "gtest/gtest.h"
#include "t_rd_ts.h"
#include "tsdb_Text.h"

char g_fdClient[512] = "ls -l /proc/`pidof tsdb_Text`/fd |wc -l";
char g_fdServer[512] = "ls -l /proc/`pidof gmserver_ts`/fd |wc -l";
int32_t fdClientBefore = 0;
int32_t fdClientAfter = 0;
int32_t fdServerBefore = 0;
int32_t fdServerBeforeOffset1 = 0;
int32_t fdServerBeforeOffset2 = 0;
int32_t fdServerAfter = 0;

int32_t GetViewFieldResultValue(const char *viewName)
{
    // DTS2025051319092,开发建议等环境稳定后再进行获取
    sleep(4);
    int maxCmdSize = 256;
    char cmdOutput[maxCmdSize];
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));

    char command[maxCmdSize];
    (void)snprintf(command, maxCmdSize, "%s", viewName);

    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        printf("popen(%s) error./n", command);
    }
    EXPECT_NE((void *)NULL, pf);
    while (NULL != fgets(cmdOutput, maxCmdSize, pf)) {
    };
    pclose(pf);

    return atoi(cmdOutput);
}

class Text : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/stop.sh -ts");
        TsDefulatDbFileClean();
        system("sh $TEST_HOME/tools/modifyCfg.sh -ts recover");
        system("sh $TEST_HOME/tools/start.sh -ts");
        // fd校验值逻辑需要修改，此问题单已跟踪DTS2025042310816
        fdServerBeforeOffset1 = GetViewFieldResultValue(g_fdClient);
        fdServerBeforeOffset2 = fdServerBeforeOffset1;
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        testEnvClean();
        RecoverTsCiCfg();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void Text::SetUp()
{
    AW_CHECK_LOG_BEGIN();

    int ret = 0;
    fdClientBefore = GetViewFieldResultValue(g_fdClient);
    fdServerBefore = GetViewFieldResultValue(g_fdServer);
    ret = TestTsGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void Text::TearDown()
{
    int ret = 0;
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fdClientAfter = GetViewFieldResultValue(g_fdClient);
    // 开始写日志的时机在增量编译和非增量编译下不一样，增量编译下会多一个日志fd，冕泓建议按照两张差值小于1校验
#ifdef RUN_INDEPENDENT
    EXPECT_LE(fdClientAfter - fdClientBefore, 1);
#else
    // USG设备上和其他环境不一致，但是通过循环执行未发现fd泄露
    EXPECT_LE(fdClientAfter - fdClientBefore, 18);
#endif
    fdServerAfter = GetViewFieldResultValue(g_fdServer);
    AW_MACRO_EXPECT_EQ_INT(fdServerBefore - fdServerBeforeOffset1, fdServerAfter - fdServerBeforeOffset2);
    AW_CHECK_LOG_END();
}

int DropCmTable(char *tableName)
{
    int ret = 0;
    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    return ret;
}

// 001.创建含有Text类型字段的时序分区表,interval为1hour，然后删除表
TEST_F(Text, Timing_048_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table testdb(id integer, T1 integer, age integer, description text) "
                           "with (time_col = 'T1', interval = '1 hours');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    sqlCmd = (char *)"drop table testdb;";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002.创建含有Text类型字段的时序分区表,interval为1day，然后删除表
TEST_F(Text, Timing_048_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table testdb(id integer, T1 integer, age integer, description text) "
                           "with (time_col = 'T1', interval = '1 days');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    sqlCmd = (char *)"drop table testdb;";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003.创建含有Text类型字段的时序分区表,interval为1month，然后删除表
TEST_F(Text, Timing_048_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table testdb(id integer, T1 integer, age integer, description text) "
                           "with (time_col = 'T1', interval = '1 months');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    sqlCmd = (char *)"drop table testdb;";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004.创建含有Text类型字段的时序分区表,interval为1year，然后删除表
TEST_F(Text, Timing_048_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table testdb(id integer, T1 integer, age integer, description text) "
                           "with (time_col = 'T1', interval = '1 years');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    sqlCmd = (char *)"drop table testdb;";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008.alter新增text列
TEST_F(Text, Timing_048_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char tblName[] = "testdb";
    char *sqlCmd = (char *)"create table testdb(id integer, time integer, age integer) "
                           "with (time_col = 'time', interval = '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    sqlCmd = (char *)"alter table testdb add column description text;";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = DropCmTable(tblName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009.绑定全部数据
TEST_F(Text, Timing_048_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table testdb(id integer, time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 5;
    constexpr int columns = 4;
    char tbl_name[] = "testdb";
    int64_t id[rows] = {0, 1, 2, 3, 4};
    int64_t time[rows] = {20240900, 20240901, 20240902, 20240903, 20240904};
    int64_t age[rows] = {20, 21, 22, 23, 24};
    char desText[][128] = {"test data of the text type", "test data of the text type", "test data of the text type",
        "test data of the text type", "test data of the text type"};
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        description[i] = desText[i];
    }
    int64_t dataTypes[columns] = {DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen[columns] = {sizeof(uint64_t), sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
    ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, time, age, description);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char getSizeStmt[] = "select count(id) from testdb";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt)));
    bool eof = false;
    EXPECT_EQ(GMERR_OK, GmcFetch(g_stmt, &eof));
    int64_t res = 0;
    uint32_t propSize = sizeof(int64_t);
    bool isNull = false;
    EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &res, &propSize, &isNull));
    EXPECT_EQ(5, res);

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010.不绑定Text数据
TEST_F(Text, Timing_048_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table testdb(id integer, time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char tbl_name[] = "testdb";
    constexpr int rows = 5;
    constexpr int columns = 3;
    int64_t id[rows] = {0, 1, 2, 3, 4};
    int64_t time[rows] = {20240900, 20240901, 20240902, 20240903, 20240904};
    int64_t age[rows] = {20, 21, 22, 23, 24};
    int64_t data_type[columns] = {DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_INT64};
    int64_t bufferLen[columns] = {sizeof(int64_t), sizeof(int64_t), sizeof(int64_t)};
    ret = Insert(g_stmt, tbl_name, rows, columns, data_type, bufferLen, id, time, age);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char getSizeStmt[] = "select count(id) from testdb";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt)));
    bool eof = false;
    EXPECT_EQ(GMERR_OK, GmcFetch(g_stmt, &eof));
    int64_t res = 0;
    uint32_t propSize = sizeof(int64_t);
    bool isNull = false;
    EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &res, &propSize, &isNull));
    EXPECT_EQ(5, res);

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011.绑定全NULL数据
TEST_F(Text, Timing_048_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table testdb(id integer, time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 5;
    constexpr int columns = 2;
    char tbl_name[] = "testdb";
    int64_t time[rows] = {20240900, 20240901, 20240902, 20240903, 20240904};
    char *description[rows] = {NULL, NULL, NULL, NULL, NULL};
    int64_t dataTypes[columns] = {DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen[columns] = {sizeof(int64_t), sizeof(description[0])};
    int columnNo[columns] = {1, 3};
    ret = Insert2(g_stmt, tbl_name, rows, columns, columnNo, dataTypes, bufferLen, time, description);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char getSizeStmt[] = "select count(id) from testdb";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt)));
    bool eof = false;
    EXPECT_EQ(GMERR_OK, GmcFetch(g_stmt, &eof));
    int64_t res = 0;
    uint32_t propSize = sizeof(int64_t);
    bool isNull = false;
    EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &res, &propSize, &isNull));
    EXPECT_EQ(5, res);

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012.绑定字符串长度超64K
TEST_F(Text, Timing_048_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table testdb(id integer, time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 5;
    constexpr int columns = 2;
    char tbl_name[] = "testdb";
    int64_t time[rows] = {20240900, 20240901, 20240902, 20240903, 20240904};
    char textTmp[65] = "64 bytes test data of the text type.1234567890123456789012345678";
    char desText[rows][1024 * 65] = {};
    for (int i = 0; i < 5; ++i) {
        // 设置16会导致超过65K，此处减少一个
        for (int j = 0; j < 15 * 65; ++j) {
            strcat(desText[i], textTmp);
        }
    }
    // 此处增加一个循环，15*65*65+95*50 > 64 * 1024
    for (int i = 0; i < 5; ++i) {
        for (int j = 0; j < 50; ++j) {
            strcat(desText[i], textTmp);
        }
    }
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        description[i] = desText[i];
    }
    int64_t dataTypes[columns] = {DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen[columns] = {sizeof(int64_t), sizeof(description[0])};
    int columnNo[columns] = {1, 3};
    ret = Insert2(g_stmt, tbl_name, rows, columns, columnNo, dataTypes, bufferLen, time, description);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char getSizeStmt[] = "select count(id) from testdb";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt)));
    bool eof = false;
    EXPECT_EQ(GMERR_OK, GmcFetch(g_stmt, &eof));
    int64_t res = 0;
    uint32_t propSize = sizeof(int64_t);
    bool isNull = false;
    EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &res, &propSize, &isNull));
    EXPECT_EQ(5, res);

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013.绑定数据，重启
TEST_F(Text, Timing_048_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table testdb(id integer, time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 5;
    constexpr int columns = 4;
    char tbl_name[] = "testdb";
    int64_t id[rows] = {0, 1, 2, 3, 4};
    int64_t time[rows] = {20240900, 20240901, 20240902, 20240903, 20240904};
    int64_t age[rows] = {20, 21, 22, 23, 24};
    char desText[][128] = {"test data of the text type", "test data of the text type", "test data of the text type",
        "test data of the text type", "test data of the text type"};
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        description[i] = desText[i];
    }
    int64_t dataTypes[columns] = {DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen[columns] = {sizeof(uint64_t), sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
    ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, time, age, description);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    GmcUnInit();
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    fdServerBeforeOffset2 = GetViewFieldResultValue(g_fdClient);
    GmcInit();

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTsGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(id) from testdb";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt)));
    bool eof = false;
    EXPECT_EQ(GMERR_OK, GmcFetch(g_stmt, &eof));
    int64_t res = 0;
    uint32_t propSize = sizeof(int64_t);
    bool isNull = false;
    EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &res, &propSize, &isNull));
    EXPECT_EQ(5, res);

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014.两张相同表结构表，insert into将全表查询结果写入到另一张空表，查询被插入表
TEST_F(Text, Timing_048_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t fdClientBeforeTemp = 0;
    int32_t fdClientAfterTemp = 0;
    int32_t fdServerBeforeTemp = 0;
    int32_t fdServerAfterTemp = 0;
    // 循环5次,判断fd有无增加
    for (int i = 0; i < 5; i++) {
        char *sqlCmd = (char *)"create table tbl_1(id integer, time integer, age integer, description text) "
            "with (time_col = 'time', interval = '1 hour');";
        uint32_t cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        sqlCmd = (char *)"create table tbl_2(id integer, time integer, age integer, description text) "
            "with (time_col = 'time', interval = '1 hour');";
        cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        constexpr int rows = 5;
        constexpr int columns = 4;
        char tbl_name[] = "tbl_1";
        int64_t id[rows] = {0, 1, 2, 3, 4};
        int64_t time[rows] = {20240900, 20240901, 20240902, 20240903, 20240904};
        int64_t age[rows] = {20, 21, 22, 23, 24};
        char desText[][128] = {"test data of the text type", "test data of the text type", "test data of the text type",
            "test data of the text type", "test data of the text type"};
        char *description[rows] = {0};
        for (int i = 0; i < rows; ++i) {
            description[i] = desText[i];
        }
        int64_t dataTypes[columns] = {DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
        int64_t bufferLen[columns] = {sizeof(uint64_t), sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
        ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, time, age, description);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        sqlCmd = (char *)"insert into tbl_2 (id, time, age, description) SELECT * FROM tbl_1";
        cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        char getSizeStmt[] = "select count(id) from tbl_2";
        EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt)));
        bool eof = false;
        EXPECT_EQ(GMERR_OK, GmcFetch(g_stmt, &eof));
        int64_t res = 0;
        uint32_t propSize = sizeof(int64_t);
        bool isNull = false;
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &res, &propSize, &isNull));
        EXPECT_EQ(5, res);

        ret = DropCmTable(tbl_name);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        char tbl_name2[] = "tbl_2";
        ret = DropCmTable(tbl_name2);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (i == 0) {
            fdClientBeforeTemp = GetViewFieldResultValue(g_fdClient);
            fdServerBeforeTemp = GetViewFieldResultValue(g_fdServer);
        } else {
            fdClientBeforeTemp = GetViewFieldResultValue(g_fdClient);
            fdServerBeforeTemp = GetViewFieldResultValue(g_fdServer);
            AW_MACRO_ASSERT_EQ_INT(fdClientBeforeTemp, fdClientAfterTemp);
            AW_MACRO_ASSERT_EQ_INT(fdServerBeforeTemp, fdServerAfterTemp);
        }
        fdClientAfterTemp = fdClientBeforeTemp;
        fdServerAfterTemp = fdServerBeforeTemp;
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015.两张相同表结构表，insert into将全表查询结果写入到另一张有数据表，查询被插入表
TEST_F(Text, Timing_048_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table tbl_1(id integer, time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sqlCmd = (char *)"create table tbl_2(id integer, time integer, age integer, description text) "
                     "with (time_col = 'time', interval = '1 hour');";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 5;
    constexpr int columns = 4;
    char tbl_name1[] = "tbl_1";
    int64_t id[rows] = {0, 1, 2, 3, 4};
    int64_t time[rows] = {20240900, 20240901, 20240902, 20240903, 20240904};
    int64_t age[rows] = {20, 21, 22, 23, 24};
    char desText[][128] = {"test data of the text type", "test data of the text type", "test data of the text type",
        "test data of the text type", "test data of the text type"};
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        description[i] = desText[i];
    }
    int64_t dataTypes[columns] = {DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen[columns] = {sizeof(uint64_t), sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
    ret = Insert(g_stmt, tbl_name1, rows, columns, dataTypes, bufferLen, id, time, age, description);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char tbl_name2[] = "tbl_2";
    ret = Insert(g_stmt, tbl_name2, rows, columns, dataTypes, bufferLen, id, time, age, description);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    sqlCmd = (char *)"insert into tbl_2 (id, time, age, description) SELECT * FROM tbl_1";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(id) from tbl_2";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt)));
    bool eof = false;
    EXPECT_EQ(GMERR_OK, GmcFetch(g_stmt, &eof));
    int64_t res = 0;
    uint32_t propSize = sizeof(int64_t);
    bool isNull = false;
    EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &res, &propSize, &isNull));
    EXPECT_EQ(10, res);

    ret = DropCmTable(tbl_name1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropCmTable(tbl_name2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016.insert into select 常量
TEST_F(Text, Timing_048_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table tbl_1(id integer, time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sqlCmd = (char *)"create table tbl_2(id integer, time integer, age integer, description text) "
                     "with (time_col = 'time', interval = '1 hour');";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 5;
    constexpr int columns = 4;
    char tbl_name[] = "tbl_1";
    int64_t id[rows] = {0, 1, 2, 3, 4};
    int64_t time[rows] = {20240900, 20240901, 20240902, 20240903, 20240904};
    int64_t age[rows] = {20, 21, 22, 23, 24};
    char desText[][128] = {"test data of the text type", "test data of the text type", "test data of the text type",
        "test data of the text type", "test data of the text type"};
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        description[i] = desText[i];
    }
    int64_t dataTypes[columns] = {DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen[columns] = {sizeof(uint64_t), sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
    ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, time, age, description);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    sqlCmd = (char *)"insert into tbl_2 (id, time, age, description) SELECT id, 123, age, description FROM tbl_1";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(id) from tbl_2";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt)));
    bool eof = false;
    EXPECT_EQ(GMERR_OK, GmcFetch(g_stmt, &eof));
    int64_t res = 0;
    uint32_t propSize = sizeof(int64_t);
    bool isNull = false;
    EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &res, &propSize, &isNull));
    EXPECT_EQ(5, res);

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 增加删表操作，避免检测fd时失败
    char tbl_name2[] = "tbl_2";
    ret = DropCmTable(tbl_name2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 017.使用copy to插入数据
TEST_F(Text, Timing_048_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table tbl_1(id integer, time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 5;
    constexpr int columns = 4;
    char tbl_name[] = "tbl_1";
    int64_t id[rows] = {0, 1, 2, 3, 4};
    int64_t time[rows] = {20240900, 20240901, 20240902, 20240903, 20240904};
    int64_t age[rows] = {20, 21, 22, 23, 24};
    char desText[][128] = {"test data of the text type", "test data of the text type", "test data of the text type",
        "test data of the text type", "test data of the text type"};
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        description[i] = desText[i];
    }
    int64_t dataTypes[columns] = {DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen[columns] = {sizeof(uint64_t), sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
    ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, time, age, description);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char *dir = getenv("GMDB_HOME");
    char sqlCmd2[256] = {0};
    sprintf(sqlCmd2, "COPY (SELECT * FROM tbl_1) TO '%s/test/sdv/testcases/25_Timing/048_tsdb_text/data.csv';", dir);
    cmdLen = strlen(sqlCmd2);
    ret = GmcExecDirect(g_stmt, sqlCmd2, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018.两个Text字段，绑定列顺序与插入表顺序相反
TEST_F(Text, Timing_048_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd =
        (char *)"create table tbl_1(id integer, time integer, age integer, description1 text, description2 text) "
                "with (time_col = 'time', interval = '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 5;
    constexpr int columns = 4;
    char tbl_name[] = "tbl_1";
    int64_t time[rows] = {20240900, 20240901, 20240902, 20240903, 20240904};
    char desText[][128] = {"test data of the text type", "test data of the text type", "test data of the text type",
        "test data of the text type", "test data of the text type"};
    char *description1[rows] = {0};
    char *description2[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        description1[i] = desText[i];
        description2[i] = desText[i];
    }

    ret = GmcPrepareStmtByLabelName(g_stmt, tbl_name, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStmtAttr(g_stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rows, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, 0, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 3, (GmcDataTypeE)DB_DATATYPE_STRING, description2, sizeof(description2[0]), NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 4, (GmcDataTypeE)DB_DATATYPE_STRING, description1, sizeof(description1[0]), NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 019.绑定列数目大于表的列数
TEST_F(Text, Timing_048_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table tbl_1(id integer, time integer, age integer, description1 text) "
                           "with (time_col = 'time', interval = '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 5;
    constexpr int columns = 4;
    char tbl_name[] = "tbl_1";
    int64_t id[rows] = {0, 1, 2, 3, 4};
    int64_t time[rows] = {20240900, 20240901, 20240902, 20240903, 20240904};
    int64_t age[rows] = {20, 21, 22, 23, 24};
    char desText[][128] = {"test data of the text type", "test data of the text type", "test data of the text type",
        "test data of the text type", "test data of the text type"};
    char *description1[rows] = {0};
    char *description2[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        description1[i] = desText[i];
        description2[i] = desText[i];
    }

    ret = GmcPrepareStmtByLabelName(g_stmt, tbl_name, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStmtAttr(g_stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rows, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, 0, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, 0, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, age, 0, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 3, (GmcDataTypeE)DB_DATATYPE_STRING, description1, sizeof(description1[0]), NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 4, (GmcDataTypeE)DB_DATATYPE_STRING, description2, sizeof(description2[0]), NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 020.Text列绑定整型数据
TEST_F(Text, Timing_048_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table tbl_1(id integer, time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 5;
    constexpr int columns = 4;
    char tbl_name[] = "tbl_1";
    int64_t id[rows] = {0, 1, 2, 3, 4};
    int64_t time[rows] = {20240900, 20240901, 20240902, 20240903, 20240904};
    int64_t age[rows] = {20, 21, 22, 23, 24};
    int64_t description[rows] = {10000, 10001, 10002, 10003, 10004};
    char desText[][128] = {"test data of the text type", "test data of the text type", "test data of the text type",
        "test data of the text type", "test data of the text type"};
    char *description1[rows] = {0};
    char *description2[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        description1[i] = desText[i];
        description2[i] = desText[i];
    }

    ret = GmcPrepareStmtByLabelName(g_stmt, tbl_name, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStmtAttr(g_stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &rows, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, sizeof(time[0]), NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(g_stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, &description, sizeof(description[0]), NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 021.插入5w行数据，含Text类型，使用no模式压缩
TEST_F(Text, Timing_048_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table tbl_1(id integer, time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour', compression='no');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 50000;
    constexpr int columns = 4;
    char tbl_name[] = "tbl_1";
    int64_t id[rows] = {0};
    int64_t time[rows] = {0};
    int64_t age[rows] = {0};
    char desText[rows][64] = {0};
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        id[i] = i;
        time[i] = 2024090000000 + i;
        age[i] = 20 + i;
        sprintf(desText[i], "test data of the text type: %d", i);
        description[i] = desText[i];
    }
    int64_t dataTypes[columns] = {DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen[columns] = {sizeof(uint64_t), sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
    ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, time, age, description);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char getSizeStmt[] = "select count(id) from tbl_1";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt)));
    bool eof = false;
    EXPECT_EQ(GMERR_OK, GmcFetch(g_stmt, &eof));
    int64_t res = 0;
    uint32_t propSize = sizeof(int64_t);
    bool isNull = false;
    EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &res, &propSize, &isNull));
    EXPECT_EQ(50000, res);

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // no模式压缩下删表缓慢
    sleep(5);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 022.插入5w行数据，含Text类型，使用fast模式压缩
TEST_F(Text, Timing_048_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table tbl_1(id integer, time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour', compression='fast');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 50000;
    constexpr int columns = 4;
    char tbl_name[] = "tbl_1";
    int64_t id[rows] = {0};
    int64_t time[rows] = {0};
    int64_t age[rows] = {0};
    char desText[rows][64] = {0};
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        id[i] = i;
        time[i] = 2024090000000 + i;
        age[i] = 20 + i;
        sprintf(desText[i], "test data of the text type: %d", i);
        description[i] = desText[i];
    }
    int64_t dataTypes[columns] = {DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen[columns] = {sizeof(uint64_t), sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
    ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, time, age, description);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char getSizeStmt[] = "select count(id) from tbl_1";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt)));
    bool eof = false;
    EXPECT_EQ(GMERR_OK, GmcFetch(g_stmt, &eof));
    int64_t res = 0;
    uint32_t propSize = sizeof(int64_t);
    bool isNull = false;
    EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &res, &propSize, &isNull));
    EXPECT_EQ(50000, res);

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 023.插入5w行数据，含Text类型，使用fast(rapidlz)模式压缩
TEST_F(Text, Timing_048_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table tbl_1(id integer, time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour', compression='fast(rapidlz)');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 50000;
    constexpr int columns = 4;
    char tbl_name[] = "tbl_1";
    int64_t id[rows] = {0};
    int64_t time[rows] = {0};
    int64_t age[rows] = {0};
    char desText[rows][64] = {0};
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        id[i] = i;
        time[i] = 2024090000000 + i;
        age[i] = 20 + i;
        sprintf(desText[i], "test data of the text type: %d", i);
        description[i] = desText[i];
    }
    int64_t dataTypes[columns] = {DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen[columns] = {sizeof(uint64_t), sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
    ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, time, age, description);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char getSizeStmt[] = "select count(id) from tbl_1";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt)));
    bool eof = false;
    EXPECT_EQ(GMERR_OK, GmcFetch(g_stmt, &eof));
    int64_t res = 0;
    uint32_t propSize = sizeof(int64_t);
    bool isNull = false;
    EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &res, &propSize, &isNull));
    EXPECT_EQ(50000, res);

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 024.插入5w行数据，含Text类型，使用fast(zstar)模式压缩
TEST_F(Text, Timing_048_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table tbl_1(id integer, time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour', compression='fast(zstar)');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 50000;
    constexpr int columns = 4;
    char tbl_name[] = "tbl_1";
    int64_t id[rows] = {0};
    int64_t time[rows] = {0};
    int64_t age[rows] = {0};
    char desText[rows][64] = {0};
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        id[i] = i;
        time[i] = 2024090000000 + i;
        age[i] = 20 + i;
        sprintf(desText[i], "test data of the text type: %d", i);
        description[i] = desText[i];
    }
    int64_t dataTypes[columns] = {DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen[columns] = {sizeof(uint64_t), sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
    ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, time, age, description);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char getSizeStmt[] = "select count(id) from tbl_1";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt)));
    bool eof = false;
    EXPECT_EQ(GMERR_OK, GmcFetch(g_stmt, &eof));
    int64_t res = 0;
    uint32_t propSize = sizeof(int64_t);
    bool isNull = false;
    EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &res, &propSize, &isNull));
    EXPECT_EQ(50000, res);

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 025.interval设为1hour，ttl参数设为4hour，预期全部数据删除
TEST_F(Text, Timing_048_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcUnInit();
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    UpdateTsLcmCheckPoind();
    system("sh $TEST_HOME/tools/start.sh -ts");
    fdServerBeforeOffset2 = GetViewFieldResultValue(g_fdClient);
    GmcInit();

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = TestTsGmcConnect(&g_conn, &g_stmt, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char *sqlCmd =
        (char *)"create table tbl_1(id integer, time integer, age integer, description text) "
                "with (time_col = 'time', interval = '1 hour',  compression='fast(rapidlz)', ttl ='4 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 10000;
    constexpr int columns = 4;
    char tbl_name[] = "tbl_1";
    int64_t id[rows] = {0};
    int64_t time[rows] = {0};
    int64_t age[rows] = {0};
    char desText[rows][64] = {0};
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        id[i] = i;
        time[i] = i;
        age[i] = 20 + i;
        sprintf(desText[i], "test data of the text type: %d", i);
        description[i] = desText[i];
    }
    int64_t dataTypes[columns] = {DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen[columns] = {sizeof(uint64_t), sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
    ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, time, age, description);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    sleep(10);
    char getSizeStmt[] = "select count(id) from tbl_1";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt)));
    bool eof = false;
    EXPECT_EQ(GMERR_OK, GmcFetch(g_stmt, &eof));
    int64_t res = 0;
    uint32_t propSize = sizeof(int64_t);
    bool isNull = false;
    EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &res, &propSize, &isNull));
    EXPECT_EQ(0, res);

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 026.cache_size设置，写入，查询
TEST_F(Text, Timing_048_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table tbl_1(id integer, time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour', cache_size = 0);";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 10000;
    constexpr int columns = 4;
    char tbl_name[] = "tbl_1";
    int64_t id[rows] = {0};
    int64_t time[rows] = {0};
    int64_t age[rows] = {0};
    char desText[rows][64] = {0};
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        id[i] = i;
        time[i] = i;
        age[i] = 20 + i;
        sprintf(desText[i], "test data of the text type: %d", i);
        description[i] = desText[i];
    }
    int64_t dataTypes[columns] = {DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen[columns] = {sizeof(uint64_t), sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
    ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, time, age, description);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char getSizeStmt[] = "select count(id) from tbl_1";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt)));
    bool eof = false;
    EXPECT_EQ(GMERR_OK, GmcFetch(g_stmt, &eof));
    int64_t res = 0;
    uint32_t propSize = sizeof(int64_t);
    bool isNull = false;
    EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &res, &propSize, &isNull));
    EXPECT_EQ(10000, res);

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 027.整型数据查询
TEST_F(Text, Timing_048_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table tbl_1(id integer, time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 10000;
    constexpr int columns = 4;
    char tbl_name[] = "tbl_1";
    int64_t id[rows] = {0};
    int64_t time[rows] = {0};
    int64_t age[rows] = {0};
    char desText[rows][64] = {0};
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        id[i] = i;
        time[i] = 20240900000 + i;
        age[i] = 20 + i;
        sprintf(desText[i], "test data of the text type: %d", i);
        description[i] = desText[i];
    }
    int64_t dataTypes[columns] = {DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen[columns] = {sizeof(uint64_t), sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
    ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, time, age, description);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char getSizeStmt[] = "select id, time, age from tbl_1";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt)));
    bool eof = false;
    int64_t res = 0;
    uint32_t propSize = sizeof(int64_t);
    bool isNull = false;
    for (int i = 0; i < 10000; ++i) {
        EXPECT_EQ(GMERR_OK, GmcFetch(g_stmt, &eof));
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &res, &propSize, &isNull));
        EXPECT_EQ(id[i], res);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 1, &res, &propSize, &isNull));
        EXPECT_EQ(time[i], res);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 2, &res, &propSize, &isNull));
        EXPECT_EQ(age[i], res);
    }

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 028.定长字符串查询
TEST_F(Text, Timing_048_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table tbl_1(id integer, name char(64), time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 10000;
    constexpr int columns = 5;
    char tbl_name[] = "tbl_1";
    int64_t id[rows] = {0};
    char name[rows][64] = {0};
    int64_t time[rows] = {0};
    int64_t age[rows] = {0};
    char desText[rows][64] = {0};
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        id[i] = i;
        sprintf(name[i], "david: %d", i);
        time[i] = 20240900000 + i;
        age[i] = 20 + i;
        sprintf(desText[i], "test data of the text type: %d", i);
        description[i] = desText[i];
    }
    int64_t dataTypes[columns] = {
        DB_DATATYPE_INT64, DB_DATATYPE_FIXED, DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen[columns] = {sizeof(uint64_t), 64, sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
    ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, name, time, age, description);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select name from tbl_1";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt)));
    bool eof = false;
    char propValue[64] = {0};
    uint32_t propSize = sizeof(propValue);
    bool isNull = false;
    for (int i = 0; i < rows; ++i) {
        EXPECT_EQ(GMERR_OK, GmcFetch(g_stmt, &eof));
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &propValue, &propSize, &isNull));
        EXPECT_STREQ(name[i], propValue);
    }

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 029.运算符>查询
TEST_F(Text, Timing_048_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table tbl_1(id integer, name char(64), time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 10000;
    constexpr int columns = 5;
    char tbl_name[] = "tbl_1";
    int64_t id[rows] = {0};
    char name[rows][64] = {0};
    int64_t time[rows] = {0};
    int64_t age[rows] = {0};
    char desText[rows][64] = {0};
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        id[i] = i;
        sprintf(name[i], "david: %d", i);
        time[i] = 20240900000 + i;
        age[i] = 20 + i;
        sprintf(desText[i], "test data of the text type: %d", i);
        description[i] = desText[i];
    }
    int64_t dataTypes[columns] = {
        DB_DATATYPE_INT64, DB_DATATYPE_FIXED, DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen[columns] = {sizeof(uint64_t), 64, sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
    ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, name, time, age, description);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(id) from tbl_1 where description > 'test data of the text type: 4999'";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt)));
    bool eof = false;
    int64_t propValue = 0;
    uint32_t propSize = sizeof(propValue);
    bool isNull = false;
    EXPECT_EQ(GMERR_OK, GmcFetch(g_stmt, &eof));
    EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &propValue, &propSize, &isNull));
    EXPECT_EQ(5555, propValue);

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 030.运算符<查询
TEST_F(Text, Timing_048_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table tbl_1(id integer, name char(64), time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 10000;
    constexpr int columns = 5;
    char tbl_name[] = "tbl_1";
    int64_t id[rows] = {0};
    char name[rows][64] = {0};
    int64_t time[rows] = {0};
    int64_t age[rows] = {0};
    char desText[rows][64] = {0};
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        id[i] = i;
        sprintf(name[i], "david: %d", i);
        time[i] = 20240900000 + i;
        age[i] = 20 + i;
        sprintf(desText[i], "test data of the text type: %d", i);
        description[i] = desText[i];
    }
    int64_t dataTypes[columns] = {
        DB_DATATYPE_INT64, DB_DATATYPE_FIXED, DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen[columns] = {sizeof(uint64_t), 64, sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
    ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, name, time, age, description);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select count(id) from tbl_1 where description < 'test data of the text type: 4999'";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt)));
    bool eof = false;
    int64_t propValue = 0;
    uint32_t propSize = sizeof(propValue);
    bool isNull = false;
    EXPECT_EQ(GMERR_OK, GmcFetch(g_stmt, &eof));
    EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &propValue, &propSize, &isNull));
    EXPECT_EQ(4444, propValue);

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 031.运算符=查询
TEST_F(Text, Timing_048_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateTblAndInsertData();

    char getSizeStmt[] = "select count(id) from tbl_1 where description = 'test data of the text type: 4999'";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt)));
    bool eof = false;
    int64_t propValue = 0;
    uint32_t propSize = sizeof(propValue);
    bool isNull = false;
    EXPECT_EQ(GMERR_OK, GmcFetch(g_stmt, &eof));
    EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &propValue, &propSize, &isNull));
    EXPECT_EQ(1, propValue);

    char tbl_name[] = "tbl_1";
    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 032.运算符>=查询
TEST_F(Text, Timing_048_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateTblAndInsertData();

    char getSizeStmt[] = "select count(id) from tbl_1 where description >= 'test data of the text type: 4999'";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt)));
    bool eof = false;
    int64_t propValue = 0;
    uint32_t propSize = sizeof(propValue);
    bool isNull = false;
    EXPECT_EQ(GMERR_OK, GmcFetch(g_stmt, &eof));
    EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &propValue, &propSize, &isNull));
    EXPECT_EQ(5556, propValue);

    char tbl_name[] = "tbl_1";
    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 033.运算符<=查询
TEST_F(Text, Timing_048_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateTblAndInsertData();

    char getSizeStmt[] = "select count(id) from tbl_1 where description <= 'test data of the text type: 4999'";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt)));
    bool eof = false;
    int64_t propValue = 0;
    uint32_t propSize = sizeof(propValue);
    bool isNull = false;
    EXPECT_EQ(GMERR_OK, GmcFetch(g_stmt, &eof));
    EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &propValue, &propSize, &isNull));
    EXPECT_EQ(4445, propValue);

    char tbl_name[] = "tbl_1";
    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 034.运算符and查询
TEST_F(Text, Timing_048_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateTblAndInsertData();

    char getSizeStmt[] =
        "select count(id) from tbl_1 where description <= 'test data of the text type: 4999' and id > 999";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt)));
    bool eof = false;
    int64_t propValue = 0;
    uint32_t propSize = sizeof(propValue);
    bool isNull = false;
    EXPECT_EQ(GMERR_OK, GmcFetch(g_stmt, &eof));
    EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &propValue, &propSize, &isNull));
    EXPECT_EQ(4000, propValue);

    char tbl_name[] = "tbl_1";
    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 035.运算符or查询
TEST_F(Text, Timing_048_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    CreateTblAndInsertData();

    char getSizeStmt[] =
        "select count(id) from tbl_1 where description < 'test data of the text type: 4999' or id >= 9000";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt)));
    bool eof = false;
    int64_t propValue = 0;
    uint32_t propSize = sizeof(propValue);
    bool isNull = false;
    EXPECT_EQ(GMERR_OK, GmcFetch(g_stmt, &eof));
    EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &propValue, &propSize, &isNull));
    EXPECT_EQ(5444, propValue);

    char tbl_name[] = "tbl_1";
    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 036.同时间分区查询
TEST_F(Text, Timing_048_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table tbl_1(id integer, name char(64), time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 10000;
    constexpr int columns = 5;
    char tbl_name[] = "tbl_1";
    int64_t id[rows] = {0};
    char name[rows][64] = {0};
    int64_t time[rows] = {0};
    int64_t age[rows] = {0};
    char desText[rows][64] = {0};
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        id[i] = i;
        sprintf(name[i], "david: %d", i);
        time[i] = 1726578000 + i;
        age[i] = 20 + i;
        sprintf(desText[i], "test data of the text type: %d", i);
        description[i] = desText[i];
    }
    int64_t dataTypes[columns] = {
        DB_DATATYPE_INT64, DB_DATATYPE_FIXED, DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen[columns] = {sizeof(uint64_t), 64, sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
    ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, name, time, age, description);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select * from tbl_1 where id > 500 and id <= 3500";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt)));

    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_EQ(3000, dataCount);

    bool eof = false;
    int64_t propValue = 0;
    uint32_t propSize = sizeof(propValue);
    char propNameValue[64] = {0};
    uint32_t propNameSize = sizeof(propNameValue);
    char propDesValue[64] = {0};
    uint32_t propDesSize = sizeof(propDesValue);
    bool isNull = false;
    for (int i = 0; i < dataCount; ++i) {
        propDesSize = 64;
        EXPECT_EQ(GMERR_OK, GmcFetch(g_stmt, &eof));
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &propValue, &propSize, &isNull));
        EXPECT_EQ(id[501 + i], propValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 1, propNameValue, &propNameSize, &isNull));
        EXPECT_STREQ(name[501 + i], propNameValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 2, &propValue, &propSize, &isNull));
        EXPECT_EQ(time[501 + i], propValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 3, &propValue, &propSize, &isNull));
        EXPECT_EQ(age[501 + i], propValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 4, propDesValue, &propDesSize, &isNull));
        EXPECT_STREQ(description[501 + i], propDesValue);
    }

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 037.跨时间分区查询
TEST_F(Text, Timing_048_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table tbl_1(id integer, name char(64), time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 10000;
    constexpr int columns = 5;
    char tbl_name[] = "tbl_1";
    int64_t id[rows] = {0};
    char name[rows][64] = {0};
    int64_t time[rows] = {0};
    int64_t age[rows] = {0};
    char desText[rows][64] = {0};
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        id[i] = i;
        sprintf(name[i], "david: %d", i);
        time[i] = 1726578000 + i;
        age[i] = 20 + i;
        sprintf(desText[i], "test data of the text type: %d", i);
        description[i] = desText[i];
    }
    int64_t dataTypes[columns] = {
        DB_DATATYPE_INT64, DB_DATATYPE_FIXED, DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen[columns] = {sizeof(uint64_t), 64, sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
    ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, name, time, age, description);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select * from tbl_1 where id < 10000";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt)));

    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_EQ(10000, dataCount);

    bool eof = false;
    int64_t propValue = 0;
    uint32_t propSize = sizeof(propValue);
    char propNameValue[64] = {0};
    uint32_t propNameSize = sizeof(propNameValue);
    char propDesValue[64] = {0};
    uint32_t propDesSize = sizeof(propDesValue);
    bool isNull = false;
    for (int i = 0; i < dataCount; ++i) {
        propDesSize = 64;
        EXPECT_EQ(GMERR_OK, GmcFetch(g_stmt, &eof));
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &propValue, &propSize, &isNull));
        EXPECT_EQ(id[i], propValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 1, propNameValue, &propNameSize, &isNull));
        EXPECT_STREQ(name[i], propNameValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 2, &propValue, &propSize, &isNull));
        EXPECT_EQ(time[i], propValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 3, &propValue, &propSize, &isNull));
        EXPECT_EQ(age[i], propValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 4, propDesValue, &propDesSize, &isNull));
        EXPECT_STREQ(description[i], propDesValue);
    }

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 038.压缩格式no模式下查询
TEST_F(Text, Timing_048_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table tbl_1(id integer, name char(64), time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour', compression='no');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 10000;
    constexpr int columns = 5;
    char tbl_name[] = "tbl_1";
    int64_t id[rows] = {0};
    char name[rows][64] = {0};
    int64_t time[rows] = {0};
    int64_t age[rows] = {0};
    char desText[rows][64] = {0};
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        id[i] = i;
        sprintf(name[i], "david: %d", i);
        time[i] = 1726578000 + i;
        age[i] = 20 + i;
        sprintf(desText[i], "test data of the text type: %d", i);
        description[i] = desText[i];
    }
    int64_t dataTypes[columns] = {
        DB_DATATYPE_INT64, DB_DATATYPE_FIXED, DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen[columns] = {sizeof(uint64_t), 64, sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
    ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, name, time, age, description);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select * from tbl_1 where id < 10000";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt)));

    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_EQ(10000, dataCount);

    bool eof = false;
    int64_t propValue = 0;
    uint32_t propSize = sizeof(propValue);
    char propNameValue[64] = {0};
    uint32_t propNameSize = sizeof(propNameValue);
    char propDesValue[64] = {0};
    uint32_t propDesSize = sizeof(propDesValue);
    bool isNull = false;
    for (int i = 0; i < dataCount; ++i) {
        propDesSize = 64;
        EXPECT_EQ(GMERR_OK, GmcFetch(g_stmt, &eof));
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &propValue, &propSize, &isNull));
        EXPECT_EQ(id[i], propValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 1, propNameValue, &propNameSize, &isNull));
        EXPECT_STREQ(name[i], propNameValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 2, &propValue, &propSize, &isNull));
        EXPECT_EQ(time[i], propValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 3, &propValue, &propSize, &isNull));
        EXPECT_EQ(age[i], propValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 4, propDesValue, &propDesSize, &isNull));
        EXPECT_STREQ(description[i], propDesValue);
    }

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 039.压缩格式fast模式下查询
TEST_F(Text, Timing_048_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table tbl_1(id integer, name char(64), time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour', compression='fast');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 10000;
    constexpr int columns = 5;
    char tbl_name[] = "tbl_1";
    int64_t id[rows] = {0};
    char name[rows][64] = {0};
    int64_t time[rows] = {0};
    int64_t age[rows] = {0};
    char desText[rows][64] = {0};
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        id[i] = i;
        sprintf(name[i], "david: %d", i);
        time[i] = 1726578000 + i;
        age[i] = 20 + i;
        sprintf(desText[i], "test data of the text type: %d", i);
        description[i] = desText[i];
    }
    int64_t dataTypes[columns] = {
        DB_DATATYPE_INT64, DB_DATATYPE_FIXED, DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen[columns] = {sizeof(uint64_t), 64, sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
    ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, name, time, age, description);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select * from tbl_1 where id < 10000";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt)));

    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_EQ(10000, dataCount);

    bool eof = false;
    int64_t propValue = 0;
    uint32_t propSize = sizeof(propValue);
    char propNameValue[64] = {0};
    uint32_t propNameSize = sizeof(propNameValue);
    char propDesValue[64] = {0};
    uint32_t propDesSize = sizeof(propDesValue);
    bool isNull = false;
    for (int i = 0; i < dataCount; ++i) {
        propDesSize = 64;
        EXPECT_EQ(GMERR_OK, GmcFetch(g_stmt, &eof));
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &propValue, &propSize, &isNull));
        EXPECT_EQ(id[i], propValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 1, propNameValue, &propNameSize, &isNull));
        EXPECT_STREQ(name[i], propNameValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 2, &propValue, &propSize, &isNull));
        EXPECT_EQ(time[i], propValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 3, &propValue, &propSize, &isNull));
        EXPECT_EQ(age[i], propValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 4, propDesValue, &propDesSize, &isNull));
        EXPECT_STREQ(description[i], propDesValue);
    }

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 040.压缩格式fast(rapidlz)模式下查询
TEST_F(Text, Timing_048_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table tbl_1(id integer, name char(64), time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour', compression='fast(rapidlz)');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 10000;
    constexpr int columns = 5;
    char tbl_name[] = "tbl_1";
    int64_t id[rows] = {0};
    char name[rows][64] = {0};
    int64_t time[rows] = {0};
    int64_t age[rows] = {0};
    char desText[rows][64] = {0};
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        id[i] = i;
        sprintf(name[i], "david: %d", i);
        time[i] = 1726578000 + i;
        age[i] = 20 + i;
        sprintf(desText[i], "test data of the text type: %d", i);
        description[i] = desText[i];
    }
    int64_t dataTypes[columns] = {
        DB_DATATYPE_INT64, DB_DATATYPE_FIXED, DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen[columns] = {sizeof(uint64_t), 64, sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
    ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, name, time, age, description);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select * from tbl_1 where id < 10000";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt)));

    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_EQ(10000, dataCount);

    bool eof = false;
    int64_t propValue = 0;
    uint32_t propSize = sizeof(propValue);
    char propNameValue[64] = {0};
    uint32_t propNameSize = sizeof(propNameValue);
    char propDesValue[64] = {0};
    uint32_t propDesSize = sizeof(propDesValue);
    bool isNull = false;
    for (int i = 0; i < dataCount; ++i) {
        propDesSize = 64;
        EXPECT_EQ(GMERR_OK, GmcFetch(g_stmt, &eof));
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &propValue, &propSize, &isNull));
        EXPECT_EQ(id[i], propValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 1, propNameValue, &propNameSize, &isNull));
        EXPECT_STREQ(name[i], propNameValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 2, &propValue, &propSize, &isNull));
        EXPECT_EQ(time[i], propValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 3, &propValue, &propSize, &isNull));
        EXPECT_EQ(age[i], propValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 4, propDesValue, &propDesSize, &isNull));
        EXPECT_STREQ(description[i], propDesValue);
    }

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 041.压缩格式fast(zstar)模式下查询
TEST_F(Text, Timing_048_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table tbl_1(id integer, name char(64), time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour', compression='fast(zstar)');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 10000;
    constexpr int columns = 5;
    char tbl_name[] = "tbl_1";
    int64_t id[rows] = {0};
    char name[rows][64] = {0};
    int64_t time[rows] = {0};
    int64_t age[rows] = {0};
    char desText[rows][64] = {0};
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        id[i] = i;
        sprintf(name[i], "david: %d", i);
        time[i] = 1726578000 + i;
        age[i] = 20 + i;
        sprintf(desText[i], "test data of the text type: %d", i);
        description[i] = desText[i];
    }
    int64_t dataTypes[columns] = {
        DB_DATATYPE_INT64, DB_DATATYPE_FIXED, DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen[columns] = {sizeof(uint64_t), 64, sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
    ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, name, time, age, description);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select * from tbl_1 where id < 10000";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt)));

    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_EQ(10000, dataCount);

    bool eof = false;
    int64_t propValue = 0;
    uint32_t propSize = sizeof(propValue);
    char propNameValue[64] = {0};
    uint32_t propNameSize = sizeof(propNameValue);
    char propDesValue[64] = {0};
    uint32_t propDesSize = sizeof(propDesValue);
    bool isNull = false;
    for (int i = 0; i < dataCount; ++i) {
        propDesSize = 64;
        EXPECT_EQ(GMERR_OK, GmcFetch(g_stmt, &eof));
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &propValue, &propSize, &isNull));
        EXPECT_EQ(id[i], propValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 1, propNameValue, &propNameSize, &isNull));
        EXPECT_STREQ(name[i], propNameValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 2, &propValue, &propSize, &isNull));
        EXPECT_EQ(time[i], propValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 3, &propValue, &propSize, &isNull));
        EXPECT_EQ(age[i], propValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 4, propDesValue, &propDesSize, &isNull));
        EXPECT_STREQ(description[i], propDesValue);
    }

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 042.聚合函数MIN验证
TEST_F(Text, Timing_048_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table tbl_1(id integer, name char(64), time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 10000;
    constexpr int columns = 5;
    char tbl_name[] = "tbl_1";
    int64_t id[rows] = {0};
    char name[rows][64] = {0};
    int64_t time[rows] = {0};
    int64_t age[rows] = {0};
    char desText[rows][64] = {0};
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        id[i] = i / 1000;
        sprintf(name[i], "david: %d", i);
        time[i] = 1726578000 + i;
        age[i] = 20 + i;
        sprintf(desText[i], "test data of the text type: %d", i);
        description[i] = desText[i];
    }
    int64_t dataTypes[columns] = {
        DB_DATATYPE_INT64, DB_DATATYPE_FIXED, DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen[columns] = {sizeof(uint64_t), 64, sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
    ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, name, time, age, description);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select id, min(description) from tbl_1 group by id order by id";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt)));

    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_EQ(10, dataCount);

    bool eof = false;
    int64_t propValue = 0;
    uint32_t propSize = sizeof(propValue);
    char propDesValue[64] = {0};
    uint32_t propDesSize = sizeof(propDesValue);
    bool isNull = false;
    for (int i = 0; i < dataCount; ++i) {
        propDesSize = 64;
        EXPECT_EQ(GMERR_OK, GmcFetch(g_stmt, &eof));
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &propValue, &propSize, &isNull));
        EXPECT_EQ(id[i * 1000], propValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 1, propDesValue, &propDesSize, &isNull));
        EXPECT_STREQ(description[i * 1000], propDesValue);
    }

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 043.聚合函数MAX验证
TEST_F(Text, Timing_048_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table tbl_1(id integer, name char(64), time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 10000;
    constexpr int columns = 5;
    char tbl_name[] = "tbl_1";
    int64_t id[rows] = {0};
    char name[rows][64] = {0};
    int64_t time[rows] = {0};
    int64_t age[rows] = {0};
    char desText[rows][64] = {0};
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        id[i] = i / 1000;
        sprintf(name[i], "david: %d", i);
        time[i] = 1726578000 + i;
        age[i] = 20 + i;
        sprintf(desText[i], "test data of the text type: %d", i);
        description[i] = desText[i];
    }
    int64_t dataTypes[columns] = {
        DB_DATATYPE_INT64, DB_DATATYPE_FIXED, DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen[columns] = {sizeof(uint64_t), 64, sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
    ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, name, time, age, description);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select id, max(description) from tbl_1 group by id order by id";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt)));

    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_EQ(10, dataCount);

    bool eof = false;
    int64_t propValue = 0;
    uint32_t propSize = sizeof(propValue);
    char propDesValue[64] = {0};
    uint32_t propDesSize = sizeof(propDesValue);
    bool isNull = false;
    for (int i = 1; i <= dataCount; ++i) {
        propDesSize = 64;
        EXPECT_EQ(GMERR_OK, GmcFetch(g_stmt, &eof));
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &propValue, &propSize, &isNull));
        EXPECT_EQ(id[i * 1000 - 1], propValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 1, propDesValue, &propDesSize, &isNull));
        EXPECT_STREQ(description[i * 1000 - 1], propDesValue);
    }

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 044.聚合函数SUM验证
TEST_F(Text, Timing_048_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table tbl_1(id integer, name char(64), time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 10000;
    constexpr int columns = 5;
    char tbl_name[] = "tbl_1";
    int64_t id[rows] = {0};
    char name[rows][64] = {0};
    int64_t time[rows] = {0};
    int64_t age[rows] = {0};
    char desText[rows][64] = {0};
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        id[i] = i / 1000;
        sprintf(name[i], "david: %d", i);
        time[i] = 1726578000 + i;
        age[i] = 20 + i;
        sprintf(desText[i], "test data of the text type: %d", i);
        description[i] = desText[i];
    }
    int64_t dataTypes[columns] = {
        DB_DATATYPE_INT64, DB_DATATYPE_FIXED, DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen[columns] = {sizeof(uint64_t), 64, sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
    ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, name, time, age, description);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select sum(description), description from tbl_1 group by description";
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt)));
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_FEATURE_NOT_SUPPORTED);

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 045.聚合函数COUNT验证
TEST_F(Text, Timing_048_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table tbl_1(id integer, name char(64), time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 10000;
    constexpr int columns = 5;
    char tbl_name[] = "tbl_1";
    int64_t id[rows] = {0};
    char name[rows][64] = {0};
    int64_t time[rows] = {0};
    int64_t age[rows] = {0};
    char desText[rows][64] = {0};
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        id[i] = i / 1000;
        sprintf(name[i], "david: %d", i);
        time[i] = 1726578000 + i;
        age[i] = 20 + i;
        sprintf(desText[i], "test data of the text type: %d", i);
        description[i] = desText[i];
    }
    int64_t dataTypes[columns] = {
        DB_DATATYPE_INT64, DB_DATATYPE_FIXED, DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen[columns] = {sizeof(uint64_t), 64, sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
    ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, name, time, age, description);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select id, count(description) from tbl_1 group by id order by id";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt)));

    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_EQ(10, dataCount);

    bool eof = false;
    int64_t propValue = 0;
    uint32_t propSize = sizeof(propValue);
    char propDesValue[64] = {0};
    uint32_t propDesSize = sizeof(propDesValue);
    bool isNull = false;
    for (int i = 0; i < dataCount; ++i) {
        propDesSize = 64;
        EXPECT_EQ(GMERR_OK, GmcFetch(g_stmt, &eof));
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &propValue, &propSize, &isNull));
        EXPECT_EQ(id[i * 1000], propValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 1, &propValue, &propSize, &isNull));
        EXPECT_EQ(1000, propValue);
    }

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 046.小数据量增序查询
TEST_F(Text, Timing_048_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table tbl_1(id integer, name char(64), time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 10;
    constexpr int columns = 5;
    char tbl_name[] = "tbl_1";
    int64_t id[rows] = {0};
    char name[rows][64] = {0};
    int64_t time[rows] = {0};
    int64_t age[rows] = {0};
    char desText[rows][64] = {0};
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        id[i] = i;
        sprintf(name[i], "david: %d", i);
        time[i] = 1726578000 + i;
        age[i] = 20 + i;
        sprintf(desText[i], "test data of the text type: %d", i);
        description[i] = desText[i];
    }
    int64_t dataTypes[columns] = {
        DB_DATATYPE_INT64, DB_DATATYPE_FIXED, DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen[columns] = {sizeof(uint64_t), 64, sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
    ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, name, time, age, description);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select * from tbl_1 order by description ASC";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt)));

    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_EQ(10, dataCount);

    bool eof = false;
    int64_t propValue = 0;
    uint32_t propSize = sizeof(propValue);
    char propNameValue[64] = {0};
    uint32_t propNameSize = sizeof(propNameValue);
    char propDesValue[64] = {0};
    uint32_t propDesSize = sizeof(propDesValue);
    bool isNull = false;
    int64_t idx = 0;
    while (true) {
        propDesSize = 64;
        EXPECT_EQ(GMERR_OK, GmcFetch(g_stmt, &eof));
        if (eof) {
            break;
        }
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &propValue, &propSize, &isNull));
        EXPECT_EQ(id[idx], propValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 1, propNameValue, &propNameSize, &isNull));
        EXPECT_STREQ(name[idx], propNameValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 2, &propValue, &propSize, &isNull));
        EXPECT_EQ(time[idx], propValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 3, &propValue, &propSize, &isNull));
        EXPECT_EQ(age[idx], propValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 4, propDesValue, &propDesSize, &isNull));
        EXPECT_STREQ(description[idx], propDesValue);
        ++idx;
    }

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 047.小数据量降序查询
TEST_F(Text, Timing_048_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table tbl_1(id integer, name char(64), time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 10;
    constexpr int columns = 5;
    char tbl_name[] = "tbl_1";
    int64_t id[rows] = {0};
    char name[rows][64] = {0};
    int64_t time[rows] = {0};
    int64_t age[rows] = {0};
    char desText[rows][64] = {0};
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        id[i] = i;
        sprintf(name[i], "david: %d", i);
        time[i] = 1726578000 + i;
        age[i] = 20 + i;
        sprintf(desText[i], "test data of the text type: %d", i);
        description[i] = desText[i];
    }
    int64_t dataTypes[columns] = {
        DB_DATATYPE_INT64, DB_DATATYPE_FIXED, DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen[columns] = {sizeof(uint64_t), 64, sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
    ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, name, time, age, description);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select * from tbl_1 order by description DESC";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt)));

    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_EQ(10, dataCount);

    bool eof = false;
    int64_t propValue = 0;
    uint32_t propSize = sizeof(propValue);
    char propNameValue[64] = {0};
    uint32_t propNameSize = sizeof(propNameValue);
    char propDesValue[64] = {0};
    uint32_t propDesSize = sizeof(propDesValue);
    bool isNull = false;
    int64_t idx = 9;
    while (true) {
        propDesSize = 64;
        EXPECT_EQ(GMERR_OK, GmcFetch(g_stmt, &eof));
        if (eof) {
            break;
        }
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &propValue, &propSize, &isNull));
        EXPECT_EQ(id[idx], propValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 1, propNameValue, &propNameSize, &isNull));
        EXPECT_STREQ(name[idx], propNameValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 2, &propValue, &propSize, &isNull));
        EXPECT_EQ(time[idx], propValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 3, &propValue, &propSize, &isNull));
        EXPECT_EQ(age[idx], propValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 4, propDesValue, &propDesSize, &isNull));
        EXPECT_STREQ(description[idx], propDesValue);
        --idx;
    }

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 048.大数据量增序查询
TEST_F(Text, Timing_048_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table tbl_1(id integer, name char(64), time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 50000;
    constexpr int columns = 5;
    char tbl_name[] = "tbl_1";
    int64_t id[rows] = {0};
    char name[rows][64] = {0};
    int64_t time[rows] = {0};
    int64_t age[rows] = {0};
    char desText[rows][64] = {0};
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        id[i] = i;
        sprintf(name[i], "david: %d", i);
        time[i] = 1726578000 + i;
        age[i] = 20 + i;
        sprintf(desText[i], "test data of the text type: %05d", i);
        description[i] = desText[i];
    }
    int64_t dataTypes[columns] = {
        DB_DATATYPE_INT64, DB_DATATYPE_FIXED, DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen[columns] = {sizeof(uint64_t), 64, sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
    ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, name, time, age, description);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select * from tbl_1 order by description ASC";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt)));

    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_EQ(50000, dataCount);

    bool eof = false;
    int64_t propValue = 0;
    uint32_t propSize = sizeof(propValue);
    char propNameValue[64] = {0};
    uint32_t propNameSize = sizeof(propNameValue);
    char propDesValue[64] = {0};
    uint32_t propDesSize = sizeof(propDesValue);
    bool isNull = false;
    int64_t idx = 0;
    while (true) {
        propDesSize = 64;
        EXPECT_EQ(GMERR_OK, GmcFetch(g_stmt, &eof));
        if (eof) {
            break;
        }
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &propValue, &propSize, &isNull));
        EXPECT_EQ(id[idx], propValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 1, propNameValue, &propNameSize, &isNull));
        EXPECT_STREQ(name[idx], propNameValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 2, &propValue, &propSize, &isNull));
        EXPECT_EQ(time[idx], propValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 3, &propValue, &propSize, &isNull));
        EXPECT_EQ(age[idx], propValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 4, propDesValue, &propDesSize, &isNull));
        EXPECT_STREQ(description[idx], propDesValue);
        ++idx;
    }

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 049.大数据量降序查询
TEST_F(Text, Timing_048_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table tbl_1(id integer, name char(64), time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 50000;
    constexpr int columns = 5;
    char tbl_name[] = "tbl_1";
    int64_t id[rows] = {0};
    char name[rows][64] = {0};
    int64_t time[rows] = {0};
    int64_t age[rows] = {0};
    char desText[rows][64] = {0};
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        id[i] = i;
        sprintf(name[i], "david: %d", i);
        time[i] = 1726578000 + i;
        age[i] = 20 + i;
        sprintf(desText[i], "test data of the text type: %05d", i);
        description[i] = desText[i];
    }
    int64_t dataTypes[columns] = {
        DB_DATATYPE_INT64, DB_DATATYPE_FIXED, DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen[columns] = {sizeof(uint64_t), 64, sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
    ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, name, time, age, description);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select * from tbl_1 order by description DESC";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt)));

    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_EQ(50000, dataCount);

    bool eof = false;
    int64_t propValue = 0;
    uint32_t propSize = sizeof(propValue);
    char propNameValue[64] = {0};
    uint32_t propNameSize = sizeof(propNameValue);
    char propDesValue[64] = {0};
    uint32_t propDesSize = sizeof(propDesValue);
    bool isNull = false;
    int64_t idx = 49999;
    while (true) {
        propDesSize = 64;
        EXPECT_EQ(GMERR_OK, GmcFetch(g_stmt, &eof));
        if (eof) {
            break;
        }
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &propValue, &propSize, &isNull));
        EXPECT_EQ(id[idx], propValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 1, propNameValue, &propNameSize, &isNull));
        EXPECT_STREQ(name[idx], propNameValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 2, &propValue, &propSize, &isNull));
        EXPECT_EQ(time[idx], propValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 3, &propValue, &propSize, &isNull));
        EXPECT_EQ(age[idx], propValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 4, propDesValue, &propDesSize, &isNull));
        EXPECT_STREQ(description[idx], propDesValue);
        --idx;
    }

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 050.大数据量查询limit 1
TEST_F(Text, Timing_048_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table tbl_1(id integer, name char(64), time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 50000;
    constexpr int columns = 5;
    char tbl_name[] = "tbl_1";
    int64_t id[rows] = {0};
    char name[rows][64] = {0};
    int64_t time[rows] = {0};
    int64_t age[rows] = {0};
    char desText[rows][64] = {0};
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        id[i] = i;
        sprintf(name[i], "david: %d", i);
        time[i] = 1726578000 + i;
        age[i] = 20 + i;
        sprintf(desText[i], "test data of the text type: %05d", i);
        description[i] = desText[i];
    }
    int64_t dataTypes[columns] = {
        DB_DATATYPE_INT64, DB_DATATYPE_FIXED, DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen[columns] = {sizeof(uint64_t), 64, sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
    ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, name, time, age, description);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select * from tbl_1 order by description DESC limit 1";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt)));

    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_EQ(1, dataCount);

    bool eof = false;
    int64_t propValue = 0;
    uint32_t propSize = sizeof(propValue);
    char propNameValue[64] = {0};
    uint32_t propNameSize = sizeof(propNameValue);
    char propDesValue[64] = {0};
    uint32_t propDesSize = sizeof(propDesValue);
    bool isNull = false;
    int64_t idx = 49999;
    while (true) {
        propDesSize = 64;
        EXPECT_EQ(GMERR_OK, GmcFetch(g_stmt, &eof));
        if (eof) {
            break;
        }
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &propValue, &propSize, &isNull));
        EXPECT_EQ(id[idx], propValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 1, propNameValue, &propNameSize, &isNull));
        EXPECT_STREQ(name[idx], propNameValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 2, &propValue, &propSize, &isNull));
        EXPECT_EQ(time[idx], propValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 3, &propValue, &propSize, &isNull));
        EXPECT_EQ(age[idx], propValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 4, propDesValue, &propDesSize, &isNull));
        EXPECT_STREQ(description[idx], propDesValue);
        --idx;
    }

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 051.大数据量查询limit 10000
TEST_F(Text, Timing_048_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table tbl_1(id integer, name char(64), time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 50000;
    constexpr int columns = 5;
    char tbl_name[] = "tbl_1";
    int64_t id[rows] = {0};
    char name[rows][64] = {0};
    int64_t time[rows] = {0};
    int64_t age[rows] = {0};
    char desText[rows][64] = {0};
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        id[i] = i;
        sprintf(name[i], "david: %d", i);
        time[i] = 1726578000 + i;
        age[i] = 20 + i;
        sprintf(desText[i], "test data of the text type: %05d", i);
        description[i] = desText[i];
    }
    int64_t dataTypes[columns] = {
        DB_DATATYPE_INT64, DB_DATATYPE_FIXED, DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen[columns] = {sizeof(uint64_t), 64, sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
    ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, name, time, age, description);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select * from tbl_1 order by description DESC limit 10000";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt)));

    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_EQ(10000, dataCount);

    bool eof = false;
    int64_t propValue = 0;
    uint32_t propSize = sizeof(propValue);
    char propNameValue[64] = {0};
    uint32_t propNameSize = sizeof(propNameValue);
    char propDesValue[64] = {0};
    uint32_t propDesSize = sizeof(propDesValue);
    bool isNull = false;
    int64_t idx = 49999;
    while (true) {
        propDesSize = 64;
        EXPECT_EQ(GMERR_OK, GmcFetch(g_stmt, &eof));
        if (eof) {
            break;
        }
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &propValue, &propSize, &isNull));
        EXPECT_EQ(id[idx], propValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 1, propNameValue, &propNameSize, &isNull));
        EXPECT_STREQ(name[idx], propNameValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 2, &propValue, &propSize, &isNull));
        EXPECT_EQ(time[idx], propValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 3, &propValue, &propSize, &isNull));
        EXPECT_EQ(age[idx], propValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 4, propDesValue, &propDesSize, &isNull));
        EXPECT_STREQ(description[idx], propDesValue);
        --idx;
    }

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 052.大数据量查询limit超过数据总量
TEST_F(Text, Timing_048_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table tbl_1(id integer, name char(64), time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 50000;
    constexpr int columns = 5;
    char tbl_name[] = "tbl_1";
    int64_t id[rows] = {0};
    char name[rows][64] = {0};
    int64_t time[rows] = {0};
    int64_t age[rows] = {0};
    char desText[rows][64] = {0};
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        id[i] = i;
        sprintf(name[i], "david: %d", i);
        time[i] = 1726578000 + i;
        age[i] = 20 + i;
        sprintf(desText[i], "test data of the text type: %05d", i);
        description[i] = desText[i];
    }
    int64_t dataTypes[columns] = {
        DB_DATATYPE_INT64, DB_DATATYPE_FIXED, DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen[columns] = {sizeof(uint64_t), 64, sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
    ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, name, time, age, description);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select * from tbl_1 order by description DESC limit 100000";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt)));

    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_EQ(50000, dataCount);

    bool eof = false;
    int64_t propValue = 0;
    uint32_t propSize = sizeof(propValue);
    char propNameValue[64] = {0};
    uint32_t propNameSize = sizeof(propNameValue);
    char propDesValue[64] = {0};
    uint32_t propDesSize = sizeof(propDesValue);
    bool isNull = false;
    int64_t idx = 49999;
    while (true) {
        propDesSize = 64;
        EXPECT_EQ(GMERR_OK, GmcFetch(g_stmt, &eof));
        if (eof) {
            break;
        }
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &propValue, &propSize, &isNull));
        EXPECT_EQ(id[idx], propValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 1, propNameValue, &propNameSize, &isNull));
        EXPECT_STREQ(name[idx], propNameValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 2, &propValue, &propSize, &isNull));
        EXPECT_EQ(time[idx], propValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 3, &propValue, &propSize, &isNull));
        EXPECT_EQ(age[idx], propValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 4, propDesValue, &propDesSize, &isNull));
        EXPECT_STREQ(description[idx], propDesValue);
        --idx;
    }

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 053.COPY TO中查询语句and，>=，<=运算符功能验证
TEST_F(Text, Timing_048_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table tbl_1(id integer, name char(64), time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 10000;
    constexpr int columns = 5;
    char tbl_name[] = "tbl_1";
    int64_t id[rows] = {0};
    char name[rows][64] = {0};
    int64_t time[rows] = {0};
    int64_t age[rows] = {0};
    char desText[rows][64] = {0};
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        id[i] = i;
        sprintf(name[i], "david: %d", i);
        time[i] = 1726578000 + i;
        age[i] = 20 + i;
        sprintf(desText[i], "test data of the text type: %d", i);
        description[i] = desText[i];
    }
    int64_t dataTypes[columns] = {
        DB_DATATYPE_INT64, DB_DATATYPE_FIXED, DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen[columns] = {sizeof(uint64_t), 64, sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
    ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, name, time, age, description);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *dir = getenv("GMDB_HOME");
    char sqlCmd2[256] = {0};
    sprintf(sqlCmd2,
        "COPY (SELECT * FROM tbl_1 where id >= 2 and time <= 1726578009) TO "
        "'%s/test/sdv/testcases/25_Timing/048_tsdb_text/data.csv';",
        dir);
    cmdLen = strlen(sqlCmd2);
    ret = GmcExecDirect(g_stmt, sqlCmd2, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *expectContent = R"(2,david: 2,1726578002,22,test data of the text type: 2
3,david: 3,1726578003,23,test data of the text type: 3
4,david: 4,1726578004,24,test data of the text type: 4
5,david: 5,1726578005,25,test data of the text type: 5
6,david: 6,1726578006,26,test data of the text type: 6
7,david: 7,1726578007,27,test data of the text type: 7
8,david: 8,1726578008,28,test data of the text type: 8
9,david: 9,1726578009,29,test data of the text type: 9
)";

    // 检查csv文件是否生成
    bool isExist = false;
    if (FILE *file = fopen("data.csv", "r")) {
        fclose(file);
        isExist = true;
    }
    AW_MACRO_ASSERT_EQ_INT(true, isExist);

    // 检查获取到内容是否正确
    char actualContent[1024] = {0};
    FILE *file = fopen("data.csv", "r");
    char row[128];
    while (fgets(row, 128, file) != NULL) {
        strcat(actualContent, row);
    }
    fclose(file);
    AW_MACRO_ASSERT_EQ_STR(expectContent, actualContent);

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 054.COPY TO中查询语句结果列中，COUNT聚合功能验证
TEST_F(Text, Timing_048_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table tbl_1(id integer, name char(64), time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 10000;
    constexpr int columns = 5;
    char tbl_name[] = "tbl_1";
    int64_t id[rows] = {0};
    char name[rows][64] = {0};
    int64_t time[rows] = {0};
    int64_t age[rows] = {0};
    char desText[rows][64] = {0};
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        id[i] = i / 1000;
        sprintf(name[i], "david: %d", i);
        time[i] = 1726578000 + i;
        age[i] = 20 + i;
        sprintf(desText[i], "test data of the text type: %d", i);
        description[i] = desText[i];
    }
    int64_t dataTypes[columns] = {
        DB_DATATYPE_INT64, DB_DATATYPE_FIXED, DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen[columns] = {sizeof(uint64_t), 64, sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
    ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, name, time, age, description);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *dir = getenv("GMDB_HOME");
    char sqlCmd2[256] = {0};
    sprintf(sqlCmd2,
        "COPY (SELECT id, count(description) FROM tbl_1 group by id order by id) TO "
        "'%s/test/sdv/testcases/25_Timing/048_tsdb_text/data.csv';",
        dir);
    cmdLen = strlen(sqlCmd2);
    ret = GmcExecDirect(g_stmt, sqlCmd2, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *expectContent = R"(0,1000
1,1000
2,1000
3,1000
4,1000
5,1000
6,1000
7,1000
8,1000
9,1000
)";

    // 检查csv文件是否生成
    bool isExist = false;
    if (FILE *file = fopen("data.csv", "r")) {
        fclose(file);
        isExist = true;
    }
    AW_MACRO_ASSERT_EQ_INT(true, isExist);

    // 检查获取到内容是否正确
    char actualContent[1024] = {0};
    FILE *file = fopen("data.csv", "r");
    char row[128];
    while (fgets(row, 128, file) != NULL) {
        strcat(actualContent, row);
    }
    fclose(file);
    AW_MACRO_ASSERT_EQ_STR(expectContent, actualContent);

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 055.空表新增Text列，查询该表，预期新增列成功，查询结果为空
TEST_F(Text, Timing_048_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table tbl_1(id integer, name char(64), time integer, age integer) "
                           "with (time_col = 'time', interval = '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    sqlCmd = (char *)"select id from tbl_1";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_EQ(0, dataCount);

    char tbl_name[] = "tbl_1";
    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 056.非空表新增Text列，写入数据，查询该表，预期新增列成功，查询成功
TEST_F(Text, Timing_048_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table tbl_1(id integer, name char(64), time integer, age integer) "
                           "with (time_col = 'time', interval = '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 10000;
    constexpr int columns = 4;
    char tbl_name[] = "tbl_1";
    int64_t id[rows] = {0};
    char name[rows][64] = {0};
    int64_t time[rows] = {0};
    int64_t age[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        id[i] = i;
        sprintf(name[i], "david: %d", i);
        time[i] = 1726578000 + i;
        age[i] = 20 + i;
    }
    int64_t dataTypes[columns] = {DB_DATATYPE_INT64, DB_DATATYPE_FIXED, DB_DATATYPE_INT64, DB_DATATYPE_INT64};
    int64_t bufferLen[columns] = {sizeof(uint64_t), 64, sizeof(uint64_t), sizeof(uint64_t)};
    ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, name, time, age);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char fdCmd[256] = {0};
    (void)sprintf(fdCmd, "ls -l /proc/`pidof gmserver_ts`/fd |wc -l");
    int32_t fdBefore = 0;
    int32_t fdAfter = 0;
    fdBefore = GetViewFieldResultValue(fdCmd);	
    sqlCmd = (char *)"alter table tbl_1 add column description text;";
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore, fdAfter);
    constexpr int columns1 = 5;
    int64_t id2[rows] = {0};
    char name2[rows][64] = {0};
    int64_t time2[rows] = {0};
    int64_t age2[rows] = {0};
    char desText[rows][64] = {0};
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        id2[i] = i + 10000;
        sprintf(name2[i], "david: %d", i + 10000);
        time2[i] = i + 10000;
        age2[i] = i + 10000;
        sprintf(desText[i], "test data of the text type: %d", i + 10000);
        description[i] = desText[i];
    }
    int64_t dataTypes2[columns1] = {
        DB_DATATYPE_INT64, DB_DATATYPE_FIXED, DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen2[columns1] = {sizeof(uint64_t), 64, sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
    ret = Insert(g_stmt, tbl_name, rows, columns1, dataTypes2, bufferLen2, id2, name2, time2, age2, description);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char getSizeStmt[] = "select * from tbl_1 order by id";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt)));

    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_EQ(20000, dataCount);

    bool eof = false;
    int64_t propValue = 0;
    uint32_t propSize = sizeof(propValue);
    char propNameValue[64] = {0};
    uint32_t propNameSize = sizeof(propNameValue);
    char propDesValue[64] = {0};
    uint32_t propDesSize = sizeof(propDesValue);
    bool isNull = false;
    int64_t idx = 0;
    while (true) {
        propDesSize = 64;
        EXPECT_EQ(GMERR_OK, GmcFetch(g_stmt, &eof));
        if (eof) {
            break;
        }
        if (idx < 10000) {
            EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &propValue, &propSize, &isNull));
            EXPECT_EQ(id[idx], propValue);
            EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 1, propNameValue, &propNameSize, &isNull));
            EXPECT_STREQ(name[idx], propNameValue);
            EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 2, &propValue, &propSize, &isNull));
            EXPECT_EQ(time[idx], propValue);
            EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 3, &propValue, &propSize, &isNull));
            EXPECT_EQ(age[idx], propValue);
            EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 4, propDesValue, &propDesSize, &isNull));
            EXPECT_STREQ("", propDesValue);
        } else {
            EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &propValue, &propSize, &isNull));
            EXPECT_EQ(id2[idx - 10000], propValue);
            EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 1, propNameValue, &propNameSize, &isNull));
            EXPECT_STREQ(name2[idx - 10000], propNameValue);
            EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 2, &propValue, &propSize, &isNull));
            EXPECT_EQ(time2[idx - 10000], propValue);
            EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 3, &propValue, &propSize, &isNull));
            EXPECT_EQ(age2[idx - 10000], propValue);
            EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 4, propDesValue, &propDesSize, &isNull));
            EXPECT_STREQ(description[idx - 10000], propDesValue);
        }
        ++idx;
    }
    fdBefore = GetViewFieldResultValue(fdCmd);
    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore, fdAfter + 1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 057.空表新增多个Text列，写入数据，查询该表，预期新增列成功，查询成功
TEST_F(Text, Timing_048_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t fdClientBeforeTemp = 0;
    int32_t fdClientAfterTemp = 0;
    int32_t fdServerBeforeTemp = 0;
    int32_t fdServerAfterTemp = 0;
    // 循环5次,判断fd有无增加
    for (int i = 0; i < 5; i++) {
        char *sqlCmd = (char *)"create table tbl_1(id integer, name char(64), time integer, age integer) "
                            "with (time_col = 'time', interval = '1 hour');";
        uint32_t cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        sqlCmd = (char *)"alter table tbl_1 add column description1 text;";
        cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        sqlCmd = (char *)"alter table tbl_1 add column description2 text;";
        cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        sqlCmd = (char *)"alter table tbl_1 add column description3 text;";
        cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        constexpr int rows = 10000;
        constexpr int columns = 7;
        char tbl_name[] = "tbl_1";
        int64_t id[rows] = {0};
        char name[rows][64] = {0};
        int64_t time[rows] = {0};
        int64_t age[rows] = {0};
        char desText[rows][64] = {0};
        char *description[rows] = {0};
        for (int i = 0; i < rows; ++i) {
            id[i] = i / 1000;
            sprintf(name[i], "david: %d", i);
            time[i] = 1726578000 + i;
            age[i] = 20 + i;
            sprintf(desText[i], "test data of the text type: %d", i);
            description[i] = desText[i];
        }
        int64_t dataTypes[columns] = {DB_DATATYPE_INT64, DB_DATATYPE_FIXED, DB_DATATYPE_INT64, DB_DATATYPE_INT64,
            DB_DATATYPE_STRING, DB_DATATYPE_STRING, DB_DATATYPE_STRING};
        int64_t bufferLen[columns] = {sizeof(uint64_t), 64, sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0]),
            sizeof(description[0]), sizeof(description[0])};
        ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, name, time, age, description, description,
            description);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        char getSizeStmt[] = "select * from tbl_1";
        EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, getSizeStmt, sizeof(getSizeStmt)));

        int64_t dataCount = 0;
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        EXPECT_EQ(10000, dataCount);

        bool eof = false;
        int64_t propValue = 0;
        uint32_t propSize = sizeof(propValue);
        char propNameValue[64] = {0};
        uint32_t propNameSize = sizeof(propNameValue);
        char propDesValue[64] = {0};
        uint32_t propDesSize = sizeof(propDesValue);
        bool isNull = false;
        int64_t idx = 0;
        while (true) {
            propDesSize = 64;
            EXPECT_EQ(GMERR_OK, GmcFetch(g_stmt, &eof));
            if (eof) {
                break;
            }
            EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &propValue, &propSize, &isNull));
            EXPECT_EQ(id[idx], propValue);
            EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 1, propNameValue, &propNameSize, &isNull));
            EXPECT_STREQ(name[idx], propNameValue);
            EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 2, &propValue, &propSize, &isNull));
            EXPECT_EQ(time[idx], propValue);
            EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 3, &propValue, &propSize, &isNull));
            EXPECT_EQ(age[idx], propValue);
            EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 4, propDesValue, &propDesSize, &isNull));
            EXPECT_STREQ(description[idx], propDesValue);
            EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 5, propDesValue, &propDesSize, &isNull));
            EXPECT_STREQ(description[idx], propDesValue);
            EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 6, propDesValue, &propDesSize, &isNull));
            EXPECT_STREQ(description[idx], propDesValue);
            ++idx;
        }

        ret = DropCmTable(tbl_name);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 058.length函数验证Text字段
TEST_F(Text, Timing_048_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table tbl_1(id integer, name char(64), time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 10000;
    constexpr int columns = 5;
    char tbl_name[] = "tbl_1";
    int64_t id[rows] = {0};
    char name[rows][64] = {0};
    int64_t time[rows] = {0};
    int64_t age[rows] = {0};
    char desText[rows][64] = {0};
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        id[i] = i;
        sprintf(name[i], "david: %d", i);
        time[i] = 1726578000 + i;
        age[i] = 20 + i;
        sprintf(desText[i], "test data of the text type: %d", i);
        description[i] = desText[i];
    }
    int64_t dataTypes[columns] = {
        DB_DATATYPE_INT64, DB_DATATYPE_FIXED, DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen[columns] = {sizeof(uint64_t), 64, sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
    ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, name, time, age, description);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sqlCmd = (char *)"select length(description) from tbl_1";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, sqlCmd, strlen(sqlCmd)));

    bool eof = false;
    int64_t propValue = 0;
    uint32_t propSize = sizeof(propValue);
    char propNameValue[64] = {0};
    uint32_t propNameSize = sizeof(propNameValue);
    char propDesValue[64] = {0};
    uint32_t propDesSize = sizeof(propDesValue);
    bool isNull = false;
    int64_t idx = 0;
    while (true) {
        propDesSize = 64;
        EXPECT_EQ(GMERR_OK, GmcFetch(g_stmt, &eof));
        if (eof) {
            break;
        }
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &propValue, &propSize, &isNull));
        EXPECT_EQ(strlen(description[idx]), propValue);
        ++idx;
    }

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 059.where等值过滤
TEST_F(Text, Timing_048_059)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table tbl_1(id integer, name char(64), time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 10000;
    constexpr int columns = 5;
    char tbl_name[] = "tbl_1";
    int64_t id[rows] = {0};
    char name[rows][64] = {0};
    int64_t time[rows] = {0};
    int64_t age[rows] = {0};
    char desText[rows][64] = {0};
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        id[i] = i;
        sprintf(name[i], "david: %d", i);
        time[i] = 1726578000 + i;
        age[i] = 20 + i;
        sprintf(desText[i], "test data of the text type: %d", i);
        description[i] = desText[i];
    }
    int64_t dataTypes[columns] = {
        DB_DATATYPE_INT64, DB_DATATYPE_FIXED, DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen[columns] = {sizeof(uint64_t), 64, sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
    ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, name, time, age, description);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sqlCmd = (char *)"select * from tbl_1 where length(description) = 31";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, sqlCmd, strlen(sqlCmd)));
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_EQ(900, dataCount);

    sqlCmd = (char *)"select * from tbl_1 where length(description) < 32";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, sqlCmd, strlen(sqlCmd)));
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_EQ(1000, dataCount);

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 060.like功能验证
TEST_F(Text, Timing_048_060)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table tbl_1(id integer, name char(64), time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 10;
    constexpr int columns = 5;
    char tbl_name[] = "tbl_1";
    int64_t id[rows] = {0};
    char name[rows][64] = {0};
    int64_t time[rows] = {0};
    int64_t age[rows] = {0};
    char desText[rows][64] = {"_david", "nut", "bob_", "olivia\%", "\%tim", "lu\%cy", "To_m", "deft", "night", "leyu"};
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        id[i] = i;
        sprintf(name[i], "david: %d", i);
        time[i] = 1726578000 + i;
        age[i] = 20 + i;
        description[i] = desText[i];
    }
    int64_t dataTypes[columns] = {
        DB_DATATYPE_INT64, DB_DATATYPE_FIXED, DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen[columns] = {sizeof(uint64_t), 64, sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
    ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, name, time, age, description);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sqlCmd = (char *)"select * from tbl_1 where description  like '%%leyu' escape '^'";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, sqlCmd, strlen(sqlCmd)));
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_EQ(1, dataCount);

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 061.group by, order by 功能验证
TEST_F(Text, Timing_048_061)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table tbl_1(id integer, name char(64), time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 10000;
    constexpr int columns = 5;
    char tbl_name[] = "tbl_1";
    int64_t id[rows] = {0};
    char name[rows][64] = {0};
    int64_t time[rows] = {0};
    int64_t age[rows] = {0};
    char desText[rows][64] = {0};
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        id[i] = i;
        sprintf(name[i], "david: %d", i);
        time[i] = 1726578000 + i;
        age[i] = 20 + i;
        sprintf(desText[i], "test data of the text type: %d", i / 1000);
        description[i] = desText[i];
    }
    int64_t dataTypes[columns] = {
        DB_DATATYPE_INT64, DB_DATATYPE_FIXED, DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen[columns] = {sizeof(uint64_t), 64, sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
    ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, name, time, age, description);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sqlCmd = (char *)"select description from tbl_1 group by description";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, sqlCmd, strlen(sqlCmd)));
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_EQ(10, dataCount);

    sqlCmd = (char *)"select description from tbl_1 order by description DESC";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, sqlCmd, strlen(sqlCmd)));
    dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_EQ(10000, dataCount);

    bool eof = false;
    char propDesValue[64] = {0};
    uint32_t propDesSize = sizeof(propDesValue);
    bool isNull = false;
    int64_t idx = 9999;
    while (true) {
        propDesSize = 64;
        EXPECT_EQ(GMERR_OK, GmcFetch(g_stmt, &eof));
        if (eof) {
            break;
        }
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &propDesValue, &propDesSize, &isNull));
        EXPECT_STREQ(description[idx], propDesValue);
        --idx;
    }

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 062.验证disk_limit设为5MB时，功能是否正常
TEST_F(Text, Timing_048_062)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char tableName[] = "testdb";
    ret =
        TestGetResultCommand("cat ../common/logicTablePath.txt | tr -d '$\r'", NULL, g_cStoreDir, sizeof(g_cStoreDir));
    EXPECT_EQ(0, ret);

    char sqlCmd[256] = {0};
    uint32_t cmdLen = 0;
    // 建表
    (void)sprintf(sqlCmd,
        "create table testdb(id integer, time integer, description text) with (time_col = 'time', interval = '1 hour', "
        "disk_limit = '5 MB', compression = 'no');");
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int insertTimes = 30;
    constexpr int64_t count = 10000;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    char desText[count][64] = {0};
    char *description[count] = {0};
    for (int j = 0; j < insertTimes; j++) {
        for (int i = 0; i < count; i++) {
            id[i] = i + j * count;
            time[i] = 1704038400 + i + j * count;
            sprintf(desText[i], "test data of the text type: %d", i + j * count);
            description[i] = desText[i];
        }
        int64_t dataTypes[3] = {DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
        int64_t bufferLen[3] = {sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
        ret = Insert(g_stmt, tableName, count, 3, dataTypes, bufferLen, id, time, description);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 等待5s使其触发限制，删除数据
    sleep(5);
    AW_FUN_Log(LOG_DEBUG, "g_cStoreDir is %s\n", g_cStoreDir);
    int64_t dataSize = GetDirSize(g_cStoreDir);
    // 系统默认占1MB - 8KB
    if (dataSize >= 1048576) {
        AW_FUN_Log(LOG_ERROR, "disk_limit not in effect.\n");
        ASSERT_EQ(false, true);
    }
    (void)sprintf(sqlCmd, "SELECT * FROM %s order by id ASC;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_NE_INT(insertTimes * count, dataCount);

    (void)sprintf(sqlCmd, "SELECT * FROM %s where id < 3600;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(0, dataCount);

    ret = DropCmTable(tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 063.查询条件中存在常量
TEST_F(Text, Timing_048_063)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table tbl_1(id integer, name char(64), time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 10000;
    constexpr int columns = 5;
    char tbl_name[] = "tbl_1";
    int64_t id[rows] = {0};
    char name[rows][64] = {0};
    int64_t time[rows] = {0};
    int64_t age[rows] = {0};
    char desText[rows][64] = {0};
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        id[i] = i;
        sprintf(name[i], "david: %d", i);
        time[i] = 1726578000 + i;
        age[i] = 20 + i;
        sprintf(desText[i], "test data of the text type: %d", i);
        description[i] = desText[i];
    }
    int64_t dataTypes[columns] = {
        DB_DATATYPE_INT64, DB_DATATYPE_FIXED, DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen[columns] = {sizeof(uint64_t), 64, sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
    ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, name, time, age, description);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sqlCmd = (char *)"select id, name, time, age, 123, description from tbl_1";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, sqlCmd, strlen(sqlCmd)));

    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_EQ(10000, dataCount);

    bool eof = false;
    int64_t propValue = 0;
    uint32_t propSize = sizeof(propValue);
    char propNameValue[64] = {0};
    uint32_t propNameSize = sizeof(propNameValue);
    char propDesValue[64] = {0};
    uint32_t propDesSize = sizeof(propDesValue);
    bool isNull = false;
    int64_t idx = 0;
    while (true) {
        propDesSize = 64;
        EXPECT_EQ(GMERR_OK, GmcFetch(g_stmt, &eof));
        if (eof) {
            break;
        }
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &propValue, &propSize, &isNull));
        EXPECT_EQ(id[idx], propValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 1, propNameValue, &propNameSize, &isNull));
        EXPECT_STREQ(name[idx], propNameValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 2, &propValue, &propSize, &isNull));
        EXPECT_EQ(time[idx], propValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 3, &propValue, &propSize, &isNull));
        EXPECT_EQ(age[idx], propValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 4, &propValue, &propSize, &isNull));
        EXPECT_EQ(123, propValue);
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 5, propDesValue, &propDesSize, &isNull));
        EXPECT_STREQ(description[idx], propDesValue);
        ++idx;
    }

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 064.Text为空
TEST_F(Text, Timing_048_064)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table tbl_1(id integer, name char(64), time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 10000;
    constexpr int columns = 4;
    char tbl_name[] = "tbl_1";
    int64_t id[rows] = {0};
    char name[rows][64] = {0};
    int64_t time[rows] = {0};
    int64_t age[rows] = {0};
    char desText[rows][64] = {0};
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        id[i] = i;
        sprintf(name[i], "david: %d", i);
        time[i] = 1726578000 + i;
        age[i] = 20 + i;
        sprintf(desText[i], "test data of the text type: %d", i);
        description[i] = desText[i];
    }
    int64_t dataTypes[columns] = {DB_DATATYPE_INT64, DB_DATATYPE_FIXED, DB_DATATYPE_INT64, DB_DATATYPE_INT64};
    int64_t bufferLen[columns] = {sizeof(uint64_t), 64, sizeof(uint64_t), sizeof(uint64_t)};
    ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, name, time, age);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sqlCmd = (char *)"select * from tbl_1 where length(description) = 0";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, sqlCmd, strlen(sqlCmd)));

    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_EQ(10000, dataCount);

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 065.group by order by 下推开启,同时查询Text字段的First、Last
TEST_F(Text, Timing_048_065)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd =
        (char *)"create table tbl_1(id integer, name char(64), time integer, salary integer, description text) "
                "with (time_col = 'time', interval = '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 6;
    constexpr int columns = 5;
    char tbl_name[] = "tbl_1";
    int64_t id[rows] = {1, 2, 4, 9, 8, 14};
    char name[rows][64] = {"david", "nut", "bob", "olivia", "tim", "lucy"};
    int64_t time[rows] = {24, 24, 11, 11, 12, 11};
    int64_t salary[rows] = {20000, 10000, 4000, 30000, 31000, 10000};
    ;
    char desText[rows][64] = {"str3", "str3", "str1", "str1", "str2", "str1"};
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        description[i] = desText[i];
    }
    int64_t dataTypes[columns] = {
        DB_DATATYPE_INT64, DB_DATATYPE_FIXED, DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen[columns] = {sizeof(uint64_t), 64, sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
    ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, name, time, salary, description);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sqlCmd =
        (char *)"select description, first(salary), last(salary) from tbl_1 group by description order by description;";
    ;
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, sqlCmd, strlen(sqlCmd)));

    char strRes[64] = {0};
    int64_t firstSalaryRes = 0;
    int64_t lastSalaryRes = 0;
    uint32_t propSize = sizeof(strRes);
    bool isNull = false;

    uint32_t i = 0;
    bool eof = false;
    while (!eof) {
        ret = GmcFetch(g_stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        propSize = sizeof(strRes);
        ret = GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &strRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)g_stmt, 1, &firstSalaryRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        propSize = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)g_stmt, 2, &lastSalaryRes, &propSize, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        switch (i) {
            case 0: {
                AW_MACRO_ASSERT_EQ_STR(strRes, "str1");
                AW_MACRO_EXPECT_EQ_INT(firstSalaryRes, 4000);
                AW_MACRO_EXPECT_EQ_INT(lastSalaryRes, 10000);
                break;
            }
            case 1: {
                AW_MACRO_ASSERT_EQ_STR(strRes, "str2");
                AW_MACRO_EXPECT_EQ_INT(firstSalaryRes, 31000);
                AW_MACRO_EXPECT_EQ_INT(lastSalaryRes, 31000);
                break;
            }
            case 2: {
                AW_MACRO_ASSERT_EQ_STR(strRes, "str3");
                AW_MACRO_EXPECT_EQ_INT(firstSalaryRes, 20000);
                AW_MACRO_EXPECT_EQ_INT(lastSalaryRes, 10000);
                break;
            }
            default:
                AW_MACRO_EXPECT_EQ_INT(1, 0);
        }
        i++;
    }

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 066.查询count(*)
TEST_F(Text, Timing_048_066)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table tbl_1(id integer, name char(64), time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 10000;
    constexpr int columns = 5;
    char tbl_name[] = "tbl_1";
    int64_t id[rows] = {0};
    char name[rows][64] = {0};
    int64_t time[rows] = {0};
    int64_t age[rows] = {0};
    char desText[rows][64] = {0};
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        id[i] = i;
        sprintf(name[i], "david: %d", i);
        time[i] = 1726578000 + i;
        age[i] = 20 + i;
        sprintf(desText[i], "test data of the text type: %d", i);
        description[i] = desText[i];
    }
    int64_t dataTypes[columns] = {
        DB_DATATYPE_INT64, DB_DATATYPE_FIXED, DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen[columns] = {sizeof(uint64_t), 64, sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
    ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, name, time, age, description);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sqlCmd = (char *)"select count(*) from tbl_1";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, sqlCmd, strlen(sqlCmd)));

    bool eof = false;
    int64_t propValue = 0;
    uint32_t propSize = sizeof(propValue);
    bool isNull = false;
    while (true) {
        EXPECT_EQ(GMERR_OK, GmcFetch(g_stmt, &eof));
        if (eof) {
            break;
        }
        EXPECT_EQ(GMERR_OK, GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &propValue, &propSize, &isNull));
        EXPECT_EQ(10000, propValue);
    }

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 069.加法和大于，a+b > 10  预期：正常运算，查询返回结果准确
TEST_F(Text, Timing_048_069)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table tbl_1(id integer, name char(64), time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 10;
    constexpr int columns = 5;
    char tbl_name[] = "tbl_1";
    int64_t id[rows] = {0};
    char name[rows][64] = {0};
    int64_t time[rows] = {0};
    int64_t age[rows] = {0};
    char desText[rows][64] = {0};
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        id[i] = i;
        sprintf(name[i], "david: %d", i);
        time[i] = 1726578000 + i;
        age[i] = i;
        sprintf(desText[i], "test data of the text type: %d", i);
        description[i] = desText[i];
    }
    int64_t dataTypes[columns] = {
        DB_DATATYPE_INT64, DB_DATATYPE_FIXED, DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen[columns] = {sizeof(uint64_t), 64, sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
    ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, name, time, age, description);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sqlCmd = (char *)"select * from tbl_1 where id + age > 10;";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, sqlCmd, strlen(sqlCmd)));

    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_EQ(4, dataCount);

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 070.建表时不指定ttl，注入数据，调用aging函数，查询数据
TEST_F(Text, Timing_048_070)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *sqlCmd = (char *)"create table tbl_1(id integer, name char(64), time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 10;
    constexpr int columns = 5;
    char tbl_name[] = "tbl_1";
    int64_t id[rows] = {0};
    char name[rows][64] = {0};
    int64_t time[rows] = {0};
    int64_t age[rows] = {0};
    char desText[rows][64] = {0};
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        id[i] = i;
        sprintf(name[i], "david: %d", i);
        time[i] = 1726578000 + i * 3600;
        age[i] = i;
        sprintf(desText[i], "test data of the text type: %d", i);
        description[i] = desText[i];
    }
    int64_t dataTypes[columns] = {
        DB_DATATYPE_INT64, DB_DATATYPE_FIXED, DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen[columns] = {sizeof(uint64_t), 64, sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
    ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, name, time, age, description);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sqlCmd = (char *)"select tsdb_aging('tbl_1');";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, sqlCmd, strlen(sqlCmd)));

    sqlCmd = (char *)"select * from tbl_1;";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(g_stmt, sqlCmd, strlen(sqlCmd)));

    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_EQ(8, dataCount);

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 重启后fd和建连前的不一致
// 071.视图
TEST_F(Text, Timing_048_071)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcUnInit();
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    fdServerBeforeOffset2 = GetViewFieldResultValue(g_fdClient);
    GmcInit();

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestTsGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char *sqlCmd = (char *)"create table tbl_1(id integer, name char(64), time integer, age integer, description text) "
                           "with (time_col = 'time', interval = '1 hour');";
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    constexpr int rows = 1;
    constexpr int columns = 5;
    char tbl_name[] = "tbl_1";
    int64_t id[rows] = {0};
    char name[rows][64] = {0};
    int64_t time[rows] = {0};
    int64_t age[rows] = {0};
    char desText[rows][64] = {0};
    char *description[rows] = {0};
    for (int i = 0; i < rows; ++i) {
        id[i] = i;
        sprintf(name[i], "david: %d", i);
        time[i] = 1726578000 + i * 3600;
        age[i] = i;
        sprintf(desText[i], "test data of the text type: %d", i);
        description[i] = desText[i];
    }
    int64_t dataTypes[columns] = {
        DB_DATATYPE_INT64, DB_DATATYPE_FIXED, DB_DATATYPE_INT64, DB_DATATYPE_INT64, DB_DATATYPE_STRING};
    int64_t bufferLen[columns] = {sizeof(uint64_t), 64, sizeof(uint64_t), sizeof(uint64_t), sizeof(description[0])};
    ret = Insert(g_stmt, tbl_name, rows, columns, dataTypes, bufferLen, id, name, time, age, description);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char sqlCmd1[256] = {0};
    (void)sprintf(sqlCmd1, "gmsysview -sql \"select description from 'tbl_1'\" -s %s", g_connServerTsdb);
    char resStr[30000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd1, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    bool isTrue = strstr(resStr, "index = 0") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "description: test data of the text type: 0") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    pclose(pResultStr);

    ret = DropCmTable(tbl_name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
