
/*****************************************************************************
 Description  : 持久化支持device归还
 Notes        : 
            Timing_042_001 增量持久化写满表空间,刷盘,drop表,再创建临时表
            Timing_042_002 按需持久化写满表空间,刷盘,drop表,再创建临时表
            Timing_042_003 增量持久化写满表空间,刷盘,drop表,再创建临时表,刷盘重启（空数据库重启）
            Timing_042_004 按需持久化写满表空间,刷盘,drop表,再创建临时表,刷盘重启（空数据库重启）
            Timing_042_005 增量持久化写满数据库,刷盘,drop表,再创建临时表,刷盘重启（满数据库重启）
            Timing_042_006 按需持久化写满数据库,刷盘,drop表,再创建临时表,刷盘重启（满数据库重启）
            Timing_042_007 增量持久化写满表空间,刷盘,truncate表,再创建临时表
            Timing_042_008 按需持久化写满表空间,刷盘,truncate记录,再创建临时表
            Timing_042_009 增量持久化写满表空间,刷盘,delete表,再创建临时表
            Timing_042_010 按需持久化写满表空间,刷盘,delete表,再创建临时表
            Timing_042_011 增量持久化写满表空间,drop表,再创建临时表,刷盘重启,创建两个线程一个线程删除一般表,一个线程创建临时表
            Timing_042_012 增量 循环执行10次,以下操作（写满表空间,刷盘,清空数据库,创建临时表,插入数据）
            Timing_042_013 按需 循环执行10次,以下操作（写满表空间,刷盘,清空数据库,创建临时表,插入数据）
            Timing_042_014 创建两个线程,一个线程删除一般表  ,一个线程创建临时表,写入临时表数据
            Timing_042_015 创建两个线程,一个线程删除一般表数据,一个线程查看视图是否device归还,归还后,写入临时表数据


备注：
1、设置表空间20M,一个device 4M,持久化占用3个device,memdata占用1个device固定不动
2、视图校验查询STORAGE_MEMDATA_STAT是否有DEV_FREE

 History      :
 Author       : 潘鹏 pwx860460
 Modification :
 Date         : 2024-08-15
*****************************************************************************/

#include "incre_pst_common.h"

#define DEV_FREE 0
int g_beginIndex = 0;
int g_endIndex = 200;

class Timing : public testing::Test {
protected:
    virtual void SetUp();
    virtual void TearDown();

public:
    static void SetUpTestCase() {}
    static void TearDownTestCase() {}
};

void Timing::SetUp()
{
    int ret;
    AW_ADD_ERRNUM_WHITE_LIST(3, GMERR_CONNECTION_FAILURE, GMERR_INTERNAL_ERROR, GMERR_CONNECTION_RESET_BY_PEER);
    system("sh $TEST_HOME/tools/stop.sh -f");
    memset(g_command, 0, sizeof(g_command));// 适配用例干扰配置
    snprintf(g_command, MAX_CMD_SIZE, "\\cp %s ./gmserver.iniback -rf ", g_sysGMDBCfg);
    system(g_command);
    system("rm gmdb* -rf");
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    (void)sprintf(g_dbFilePath, "%s/gmdb", pwdDir);
    (void)Rmdir(g_dbFilePath);
    ret = mkdir(g_dbFilePath, S_IRUSR | S_IWUSR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"dataFileDirPath", g_dbFilePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"maxSeMem", (char *)"20");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void Timing::TearDown()
{
    int ret;
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_FILE_OPERATE_FAILED , GMERR_OUT_OF_MEMORY);
    AW_CHECK_LOG_END();
    system("chattr -R -i gmdb/");

    memset(g_command, 0, sizeof(g_command));// 适配用例干扰配置
    snprintf(g_command, MAX_CMD_SIZE, "\\cp ./gmserver.iniback %s -rf", g_sysGMDBCfg);
    system(g_command);
    // 断连
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh -f");
    // 停掉服务，恢复配置，清理持久化文件
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    char expectCmd[100] = {0};
    char *homePath = getenv("HOME");
    (void)snprintf(expectCmd, sizeof(expectCmd), "rm %s/../data/gmdb/* -rf", homePath);
    system(expectCmd);
    memset(expectCmd, 0, sizeof(expectCmd));
    char *pwdDir = getenv("PWD");
    (void)sprintf(expectCmd, "rm %s/gmdb -rf;", pwdDir);
    system(expectCmd);
}

char *vertex_label_schema = NULL;
char *vertex_label_schema_temp = NULL;
char labelName[]="vertex_sim";
char labelName_temp[]="vertex_temp";
char g_configJson[128] = "{\"max_record_count\" : 10000000}";
char g_configJson_temp[128] = "{\"max_record_count\" : 10000000, \"persistent\":false}";

// 增量持久化写满表空间,刷盘 drop表,再创建临时表
TEST_F(Timing, Timing_042_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    readJanssonFile("./schema/Vertex_01.gmjson", &vertex_label_schema);
    ASSERT_NE((void *)NULL, vertex_label_schema);
    GmcDropVertexLabel(g_stmt, labelName);
    ret = GmcCreateVertexLabel(g_stmt, vertex_label_schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写满表空间,并统计写入数量
    uint32_t count_sim_01;
    uint32_t count_sim_02;
    uint32_t count_temp_01;
    uint32_t count_temp_02;
    ret = PstInsertValueFull(g_stmt, labelName, &count_sim_01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "count_sim_01 %u", count_sim_01);
    // 删表
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 判断是否device free
    int64_t get_type;
    ret = GetDeviceStatusisFree(&get_type);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(DEV_FREE, get_type);
    // 再次建表写满
    readJanssonFile("./schema/Vertex_02.gmjson", &vertex_label_schema_temp);
    ASSERT_NE((void *)NULL, vertex_label_schema_temp);
    ret = GmcCreateVertexLabel(g_stmt, vertex_label_schema_temp, g_configJson_temp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = PstInsertValueFull(g_stmt, labelName_temp, &count_temp_01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "count_temp_01 %u", count_temp_01);
    EXPECT_GT(count_sim_01, count_temp_01);
    free(vertex_label_schema);
    free(vertex_label_schema_temp);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 按需持久化写满表空间,刷盘,drop表,再创建临时表
TEST_F(Timing, Timing_042_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    readJanssonFile("./schema/Vertex_01.gmjson", &vertex_label_schema);
    ASSERT_NE((void *)NULL, vertex_label_schema);
    GmcDropVertexLabel(g_stmt, labelName);
    ret = GmcCreateVertexLabel(g_stmt, vertex_label_schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写满表空间,并统计写入数量
    uint32_t count_sim_01;
    uint32_t count_sim_02;
    uint32_t count_temp_01;
    uint32_t count_temp_02;
    ret = PstInsertValueFull(g_stmt, labelName, &count_sim_01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "count_sim_01 %u", count_sim_01);
    // 删表
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 判断是否device free
    int64_t get_type;
    ret = GetDeviceStatusisFree(&get_type);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(DEV_FREE, get_type);
    // 再次建表写满
    readJanssonFile("./schema/Vertex_02.gmjson", &vertex_label_schema_temp);
    ASSERT_NE((void *)NULL, vertex_label_schema_temp);
    ret = GmcCreateVertexLabel(g_stmt, vertex_label_schema_temp, g_configJson_temp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = PstInsertValueFull(g_stmt, labelName_temp, &count_temp_01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "count_temp_01 %u", count_temp_01);
    EXPECT_GT(count_sim_01, count_temp_01);
    free(vertex_label_schema);
    free(vertex_label_schema_temp);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 增量持久化写满表空间,刷盘,drop表,再创建临时表,刷盘重启（空数据库重启）
TEST_F(Timing, Timing_042_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    readJanssonFile("./schema/Vertex_01.gmjson", &vertex_label_schema);
    ASSERT_NE((void *)NULL, vertex_label_schema);
    GmcDropVertexLabel(g_stmt, labelName);
    ret = GmcCreateVertexLabel(g_stmt, vertex_label_schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写满表空间,并统计写入数量
    uint32_t count_sim_01;
    uint32_t count_sim_02;
    uint32_t count_temp_01;
    uint32_t count_temp_02;
    ret = PstInsertValueFull(g_stmt, labelName, &count_sim_01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "count_sim_01 %u", count_sim_01);
    // 删表
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 判断是否device free
    int64_t get_type;
    ret = GetDeviceStatusisFree(&get_type);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(DEV_FREE, get_type);
    // 再次建表写满
    readJanssonFile("./schema/Vertex_02.gmjson", &vertex_label_schema_temp);
    ASSERT_NE((void *)NULL, vertex_label_schema_temp);
    ret = GmcCreateVertexLabel(g_stmt, vertex_label_schema_temp, g_configJson_temp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = PstInsertValueFull(g_stmt, labelName_temp, &count_temp_01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "count_temp_01 %u", count_temp_01);

    // 落盘断连重启建连
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RestartAndConn2();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重新写入数据满
    ret = GmcCreateVertexLabel(g_stmt, vertex_label_schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = PstInsertValueFull(g_stmt, labelName, &count_sim_02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删表
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GetDeviceStatusisFree(&get_type);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(DEV_FREE, get_type);
    // 临时表写满
    ret = GmcCreateVertexLabel(g_stmt, vertex_label_schema_temp, g_configJson_temp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = PstInsertValueFull(g_stmt, labelName_temp, &count_temp_02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "count_sim_01  %u", count_sim_01);
    AW_FUN_Log(LOG_STEP, "count_sim_02  %u", count_sim_02);
    AW_FUN_Log(LOG_STEP, "count_temp_01  %u", count_temp_01);
    AW_FUN_Log(LOG_STEP, "count_temp_02  %u", count_temp_02);
    AW_MACRO_EXPECT_EQ_INT(count_sim_01, count_sim_02);
    AW_MACRO_EXPECT_EQ_INT(count_temp_01, count_temp_02);
    EXPECT_GT(count_sim_01, count_temp_01);
    EXPECT_GT(count_sim_02, count_temp_02);
    free(vertex_label_schema);
    free(vertex_label_schema_temp);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 按需持久化写满表空间,刷盘,drop表,再创建临时表,刷盘重启（空数据库重启）
TEST_F(Timing, Timing_042_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    readJanssonFile("./schema/Vertex_01.gmjson", &vertex_label_schema);
    ASSERT_NE((void *)NULL, vertex_label_schema);
    GmcDropVertexLabel(g_stmt, labelName);
    ret = GmcCreateVertexLabel(g_stmt, vertex_label_schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写满表空间,并统计写入数量
    uint32_t count_sim_01;
    uint32_t count_sim_02;
    uint32_t count_temp_01;
    uint32_t count_temp_02;
    ret = PstInsertValueFull(g_stmt, labelName, &count_sim_01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "count_sim_01 %u", count_sim_01);
    // 删表
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 判断是否device free
    int64_t get_type;
    ret = GetDeviceStatusisFree(&get_type);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(DEV_FREE, get_type);
    // 再次建表写满
    readJanssonFile("./schema/Vertex_02.gmjson", &vertex_label_schema_temp);
    ASSERT_NE((void *)NULL, vertex_label_schema_temp);
    ret = GmcCreateVertexLabel(g_stmt, vertex_label_schema_temp, g_configJson_temp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = PstInsertValueFull(g_stmt, labelName_temp, &count_temp_01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "count_temp_01 %u", count_temp_01);

    // 落盘断连重启建连
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RestartAndConn2();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重新写入数据满
    ret = GmcCreateVertexLabel(g_stmt, vertex_label_schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = PstInsertValueFull(g_stmt, labelName, &count_sim_02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删表
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GetDeviceStatusisFree(&get_type);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(DEV_FREE, get_type);
    // 临时表写满
    ret = GmcCreateVertexLabel(g_stmt, vertex_label_schema_temp, g_configJson_temp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = PstInsertValueFull(g_stmt, labelName_temp, &count_temp_02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "count_sim_01  %u", count_sim_01);
    AW_FUN_Log(LOG_STEP, "count_sim_02  %u", count_sim_02);
    AW_FUN_Log(LOG_STEP, "count_temp_01  %u", count_temp_01);
    AW_FUN_Log(LOG_STEP, "count_temp_02  %u", count_temp_02);
    AW_MACRO_EXPECT_EQ_INT(count_sim_01, count_sim_02);
    AW_MACRO_EXPECT_EQ_INT(count_temp_01, count_temp_02);
    EXPECT_GT(count_sim_01, count_temp_01);
    EXPECT_GT(count_sim_02, count_temp_02);
    free(vertex_label_schema);
    free(vertex_label_schema_temp);
    AW_FUN_Log(LOG_STEP, "test end.");
}


// 增量持久化写满数据库,刷盘,drop表,再创建临时表,刷盘重启（满数据库重启）
TEST_F(Timing, Timing_042_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    readJanssonFile("./schema/Vertex_01.gmjson", &vertex_label_schema);
    ASSERT_NE((void *)NULL, vertex_label_schema);
    GmcDropVertexLabel(g_stmt, labelName);
    ret = GmcCreateVertexLabel(g_stmt, vertex_label_schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写满表空间,并统计写入数量
    uint32_t count_sim_01;
    uint32_t count_sim_02;
    uint32_t count_temp_01;
    uint32_t count_temp_02;
    ret = PstInsertValueFull(g_stmt, labelName, &count_sim_01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "count_sim_01 %u", count_sim_01);

    // 落盘断连重启建连
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RestartAndConn2();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删表
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 判断是否device free
    int64_t get_type;
    ret = GetDeviceStatusisFree(&get_type);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(DEV_FREE, get_type);
    // 再次建表写满
    readJanssonFile("./schema/Vertex_02.gmjson", &vertex_label_schema_temp);
    ASSERT_NE((void *)NULL, vertex_label_schema_temp);
    ret = GmcCreateVertexLabel(g_stmt, vertex_label_schema_temp, g_configJson_temp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = PstInsertValueFull(g_stmt, labelName_temp, &count_temp_01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "count_temp_01 %u", count_temp_01);
    AW_FUN_Log(LOG_STEP, "count_sim_01  %u", count_sim_01);
    EXPECT_GT(count_sim_01, count_temp_01);
    free(vertex_label_schema);
    free(vertex_label_schema_temp);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 按需持久化写满数据库,刷盘,drop表,再创建临时表,刷盘重启（满数据库重启）
TEST_F(Timing, Timing_042_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    readJanssonFile("./schema/Vertex_01.gmjson", &vertex_label_schema);
    ASSERT_NE((void *)NULL, vertex_label_schema);
    GmcDropVertexLabel(g_stmt, labelName);
    ret = GmcCreateVertexLabel(g_stmt, vertex_label_schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写满表空间,并统计写入数量
    uint32_t count_sim_01;
    uint32_t count_sim_02;
    uint32_t count_temp_01;
    uint32_t count_temp_02;
    ret = PstInsertValueFull(g_stmt, labelName, &count_sim_01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "count_sim_01 %u", count_sim_01);

    // 落盘断连重启建连
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RestartAndConn2();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删表
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 判断是否device free
    int64_t get_type;
    ret = GetDeviceStatusisFree(&get_type);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(DEV_FREE, get_type);
    // 再次建表写满
    readJanssonFile("./schema/Vertex_02.gmjson", &vertex_label_schema_temp);
    ASSERT_NE((void *)NULL, vertex_label_schema_temp);
    ret = GmcCreateVertexLabel(g_stmt, vertex_label_schema_temp, g_configJson_temp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = PstInsertValueFull(g_stmt, labelName_temp, &count_temp_01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "count_temp_01 %u", count_temp_01);
    AW_FUN_Log(LOG_STEP, "count_sim_01  %u", count_sim_01);
    EXPECT_GT(count_sim_01, count_temp_01);
    free(vertex_label_schema);
    free(vertex_label_schema_temp);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 增量持久化写满表空间,刷盘,truncate表,再创建临时表
TEST_F(Timing, Timing_042_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    readJanssonFile("./schema/Vertex_01.gmjson", &vertex_label_schema);
    ASSERT_NE((void *)NULL, vertex_label_schema);
    GmcDropVertexLabel(g_stmt, labelName);
    ret = GmcCreateVertexLabel(g_stmt, vertex_label_schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写满表空间,并统计写入数量
    uint32_t count_sim_01;
    uint32_t count_sim_02;
    uint32_t count_temp_01;
    uint32_t count_temp_02;
    ret = PstInsertValueFull(g_stmt, labelName, &count_sim_01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "count_sim_01 %u", count_sim_01);

    // 删表
    ret = GmcTruncateVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 判断是否device free
    int64_t get_type;
    ret = GetDeviceStatusisFree(&get_type);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(DEV_FREE, get_type);
    // 再次建表写满
    readJanssonFile("./schema/Vertex_02.gmjson", &vertex_label_schema_temp);
    ASSERT_NE((void *)NULL, vertex_label_schema_temp);
    ret = GmcCreateVertexLabel(g_stmt, vertex_label_schema_temp, g_configJson_temp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = PstInsertValueFull(g_stmt, labelName_temp, &count_temp_01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "count_temp_01 %u", count_temp_01);
    EXPECT_GT(count_sim_01, count_temp_01);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(vertex_label_schema);
    free(vertex_label_schema_temp);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 按需持久化写满表空间,刷盘,truncate记录,再创建临时表
TEST_F(Timing, Timing_042_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    readJanssonFile("./schema/Vertex_01.gmjson", &vertex_label_schema);
    ASSERT_NE((void *)NULL, vertex_label_schema);
    GmcDropVertexLabel(g_stmt, labelName);
    ret = GmcCreateVertexLabel(g_stmt, vertex_label_schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写满表空间,并统计写入数量
    uint32_t count_sim_01;
    uint32_t count_sim_02;
    uint32_t count_temp_01;
    uint32_t count_temp_02;
    ret = PstInsertValueFull(g_stmt, labelName, &count_sim_01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "count_sim_01 %u", count_sim_01);
    // truncate
    ret = GmcTruncateVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 判断是否device free
    int64_t get_type;
    ret = GetDeviceStatusisFree(&get_type);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(DEV_FREE, get_type);
    // 再次建表写满
    readJanssonFile("./schema/Vertex_02.gmjson", &vertex_label_schema_temp);
    ASSERT_NE((void *)NULL, vertex_label_schema_temp);
    ret = GmcCreateVertexLabel(g_stmt, vertex_label_schema_temp, g_configJson_temp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = PstInsertValueFull(g_stmt, labelName_temp, &count_temp_01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "count_temp_01 %u", count_temp_01);
    EXPECT_GT(count_sim_01, count_temp_01);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(vertex_label_schema);
    free(vertex_label_schema_temp);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 增量持久化写满表空间,刷盘,delete表,再创建临时表
TEST_F(Timing, Timing_042_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    readJanssonFile("./schema/Vertex_01.gmjson", &vertex_label_schema);
    ASSERT_NE((void *)NULL, vertex_label_schema);
    GmcDropVertexLabel(g_stmt, labelName);
    ret = GmcCreateVertexLabel(g_stmt, vertex_label_schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写满表空间,并统计写入数量
    uint32_t count_sim_01;
    uint32_t count_sim_02;
    uint32_t count_temp_01;
    uint32_t count_temp_02;
    ret = PstInsertValueFull(g_stmt, labelName, &count_sim_01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "count_sim_01 %u", count_sim_01);
    // delete
    int g_beginIndex = 0;
    int g_endIndex = count_sim_01;
    char g_indexName[] = "PrimaryKey";
    char *g_cond = (char *)"A0(uint32)=%i{0}";
    ret = TestdelVertexSync(g_stmt, labelName, g_indexName, g_cond, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // 判断是否device free
    int64_t get_type;
    ret = GetDeviceStatusisFree(&get_type);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(DEV_FREE, get_type);
    // 再次建表写满
    readJanssonFile("./schema/Vertex_02.gmjson", &vertex_label_schema_temp);
    ASSERT_NE((void *)NULL, vertex_label_schema_temp);
    ret = GmcCreateVertexLabel(g_stmt, vertex_label_schema_temp, g_configJson_temp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = PstInsertValueFull(g_stmt, labelName_temp, &count_temp_01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "count_temp_01 %u", count_temp_01);
    EXPECT_GT(count_sim_01, count_temp_01);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(vertex_label_schema);
    free(vertex_label_schema_temp);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 按需持久化写满表空间,刷盘,delete表,再创建临时表
TEST_F(Timing, Timing_042_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    readJanssonFile("./schema/Vertex_01.gmjson", &vertex_label_schema);
    ASSERT_NE((void *)NULL, vertex_label_schema);
    GmcDropVertexLabel(g_stmt, labelName);
    ret = GmcCreateVertexLabel(g_stmt, vertex_label_schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写满表空间,并统计写入数量
    uint32_t count_sim_01;
    uint32_t count_sim_02;
    uint32_t count_temp_01;
    uint32_t count_temp_02;
    ret = PstInsertValueFull(g_stmt, labelName, &count_sim_01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "count_sim_01 %u", count_sim_01);
    // delete
    int g_beginIndex = 0;
    int g_endIndex = count_sim_01;
    char g_indexName[] = "PrimaryKey";
    char *g_cond = (char *)"A0(uint32)=%i{0}";
    ret = TestdelVertexSync(g_stmt, labelName, g_indexName, g_cond, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    // 判断是否device free
    int64_t get_type;
    ret = GetDeviceStatusisFree(&get_type);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(DEV_FREE, get_type);
    // 再次建表写满
    readJanssonFile("./schema/Vertex_02.gmjson", &vertex_label_schema_temp);
    ASSERT_NE((void *)NULL, vertex_label_schema_temp);
    ret = GmcCreateVertexLabel(g_stmt, vertex_label_schema_temp, g_configJson_temp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = PstInsertValueFull(g_stmt, labelName_temp, &count_temp_01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "count_temp_01 %u", count_temp_01);
    EXPECT_GT(count_sim_01, count_temp_01);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(vertex_label_schema);
    free(vertex_label_schema_temp);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 线程PkInserttemp
void *PkInserttemp(void *args)
{
    GmcConnT *conn_tmp_inserttemp = NULL;
    GmcStmtT *stmt_tmp_inserttemp = NULL;

    int ret = 0;
    uint32_t pthread_count_sim;

    // 新建一个连接
    ret = testGmcConnect(&conn_tmp_inserttemp, &stmt_tmp_inserttemp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = PstInsertValueFull(stmt_tmp_inserttemp, labelName_temp, &pthread_count_sim);
    printf("pthread_count_sim %u\n", pthread_count_sim);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 断掉连接
    ret = testGmcDisconnect(conn_tmp_inserttemp, stmt_tmp_inserttemp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 线程PkDeletesim
void *PkDeletesim(void *args)
{
    GmcConnT *conn_tmp = NULL;
    GmcStmtT *stmt_tmp = NULL;

    int ret = 0;

    // 新建一个连接
    ret = testGmcConnect(&conn_tmp, &stmt_tmp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int g_beginIndex_del = 0;
    int g_endIndex_del = 2000;
    char g_indexName[] = "PrimaryKey";
    char *g_cond = (char *)"A0(uint32)=%i{0}";
    ret = TestdelVertexSync(g_stmt, labelName, g_indexName, g_cond, g_beginIndex_del, g_endIndex_del);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 断掉连接
    ret = testGmcDisconnect(conn_tmp, stmt_tmp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 线程Dropsim
void *Dropsim(void *args)
{
    GmcConnT *conn_tmp = NULL;
    GmcStmtT *stmt_tmp = NULL;

    int ret = 0;

    // 新建一个连接
    ret = testGmcConnect(&conn_tmp, &stmt_tmp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt_tmp, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 断掉连接
    ret = testGmcDisconnect(conn_tmp, stmt_tmp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 线程DeviceRecycle
void *DeviceRecycle(void *args)
{
    char g_command_ap[1024];
    snprintf(g_command_ap, sizeof(g_command_ap), "gmsysview -q V\\$STORAGE_MEMDATA_STAT |grep DEV_FREE |wc -l");
    int ret;
    for (int i =0; i<100; i++) {
        usleep(100000);// 偶现环境性能差 调整等待时间0.1s
        ret = executeCommand(g_command_ap, "1");
        if (ret == GMERR_OK) {
            printf("devFree expect is DEV_FREE acture DEV_FREE********\n");
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$STORAGE_MEMDATA_STAT");
}

// 011 增量持久化写满表空间,drop表,再创建临时表,刷盘重启,创建两个线程一个线程删除一般表,一个线程创建临时表
TEST_F(Timing, Timing_042_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    readJanssonFile("./schema/Vertex_01.gmjson", &vertex_label_schema);
    ASSERT_NE((void *)NULL, vertex_label_schema);
    GmcDropVertexLabel(g_stmt, labelName);
    ret = GmcCreateVertexLabel(g_stmt, vertex_label_schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写满表空间,并统计写入数量
    uint32_t count_sim_01;
    uint32_t count_sim_02;
    uint32_t count_temp_01;
    uint32_t count_temp_02;
    ret = PstInsertValueFull(g_stmt, labelName, &count_sim_01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "count_sim_01 %u", count_sim_01);
    // 删表
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 判断是否device free
    int64_t get_type;
    ret = GetDeviceStatusisFree(&get_type);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(DEV_FREE, get_type);
    // 再次建表写满
    ret = GmcCreateVertexLabel(g_stmt, vertex_label_schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = PstInsertValueFull(g_stmt, labelName, &count_sim_02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "count_sim_02 %u", count_sim_02);
    AW_MACRO_EXPECT_EQ_INT(count_sim_01, count_sim_02);

    // 落盘断连重启建连
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RestartAndConn2();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 再次建表
    readJanssonFile("./schema/Vertex_02.gmjson", &vertex_label_schema_temp);
    ASSERT_NE((void *)NULL, vertex_label_schema_temp);
    ret = GmcCreateVertexLabel(g_stmt, vertex_label_schema_temp, g_configJson_temp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_t client_thr_01, client_thr_02;
    ret = pthread_create(&client_thr_01, NULL, PkInserttemp, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&client_thr_02, NULL, Dropsim, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_join(client_thr_01, NULL);
    pthread_join(client_thr_02, NULL);
    free(vertex_label_schema);
    free(vertex_label_schema_temp);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 增量 循环执行10次,以下操作（写满表空间,刷盘,清空数据库,创建临时表,插入数据）
TEST_F(Timing, Timing_042_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    readJanssonFile("./schema/Vertex_01.gmjson", &vertex_label_schema);
    ASSERT_NE((void *)NULL, vertex_label_schema);
    GmcDropVertexLabel(g_stmt, labelName);
    ret = GmcCreateVertexLabel(g_stmt, vertex_label_schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写满表空间,并统计写入数量
    uint32_t count_sim_01;
    uint32_t count_sim_02;
    uint32_t count_temp_01;
    uint32_t count_temp_02;
    int g_beginIndex = 0;
    int g_endIndex;
    char g_indexName[] = "PrimaryKey";
    char *g_cond = (char *)"A0(uint32)=%i{0}";
    int64_t get_type;
    // 再次建表写满
    readJanssonFile("./schema/Vertex_02.gmjson", &vertex_label_schema_temp);
    ASSERT_NE((void *)NULL, vertex_label_schema_temp);
    for (int i=0; i<10; i++) {
        ret = PstInsertValueFull(g_stmt, labelName, &count_sim_01);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_endIndex = count_sim_01;
        ret = TestdelVertexSync(g_stmt, labelName, g_indexName, g_cond, g_beginIndex, g_endIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GetDeviceStatusisFree(&get_type);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(DEV_FREE, get_type);
        ret = GmcCreateVertexLabel(g_stmt, vertex_label_schema_temp, g_configJson_temp);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = PstInsertValueFull(g_stmt, labelName_temp, &count_temp_02);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        system("gmsysview count");
        AW_FUN_Log(LOG_STEP, "count_sim_01 %u", count_sim_01);
        AW_FUN_Log(LOG_STEP, "count_temp_02 %u", count_temp_02);
        ret = GmcDropVertexLabel(g_stmt, labelName_temp);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(vertex_label_schema);
    free(vertex_label_schema_temp);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 按需 循环执行10次,以下操作（写满表空间,刷盘,清空数据库,创建临时表,插入数据）
TEST_F(Timing, Timing_042_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    readJanssonFile("./schema/Vertex_01.gmjson", &vertex_label_schema);
    ASSERT_NE((void *)NULL, vertex_label_schema);
    GmcDropVertexLabel(g_stmt, labelName);
    ret = GmcCreateVertexLabel(g_stmt, vertex_label_schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写满表空间,并统计写入数量
    uint32_t count_sim_01;
    uint32_t count_sim_02;
    uint32_t count_temp_01;
    uint32_t count_temp_02;
    int g_beginIndex = 0;
    int g_endIndex;
    char g_indexName[] = "PrimaryKey";
    char *g_cond = (char *)"A0(uint32)=%i{0}";
    int64_t get_type;
    // 再次建表写满
    readJanssonFile("./schema/Vertex_02.gmjson", &vertex_label_schema_temp);
    ASSERT_NE((void *)NULL, vertex_label_schema_temp);
    for (int i=0; i<10; i++) {
        ret = PstInsertValueFull(g_stmt, labelName, &count_sim_01);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_endIndex = count_sim_01;
        ret = TestdelVertexSync(g_stmt, labelName, g_indexName, g_cond, g_beginIndex, g_endIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GetDeviceStatusisFree(&get_type);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(DEV_FREE, get_type);
        ret = GmcCreateVertexLabel(g_stmt, vertex_label_schema_temp, g_configJson_temp);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = PstInsertValueFull(g_stmt, labelName_temp, &count_temp_02);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        system("gmsysview count");
        AW_FUN_Log(LOG_STEP, "count_sim_01 %u", count_sim_01);
        AW_FUN_Log(LOG_STEP, "count_temp_02 %u", count_temp_02);
        ret = GmcDropVertexLabel(g_stmt, labelName_temp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(vertex_label_schema);
    free(vertex_label_schema_temp);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建两个线程,一个线程删除一般表,一个线程创建临时表,写入临时表数据
TEST_F(Timing, Timing_042_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"maxSeMem", (char *)"30");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    readJanssonFile("./schema/Vertex_01.gmjson", &vertex_label_schema);
    ASSERT_NE((void *)NULL, vertex_label_schema);
    GmcDropVertexLabel(g_stmt, labelName);
    ret = GmcCreateVertexLabel(g_stmt, vertex_label_schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写满表空间,并统计写入数量
    uint32_t count_sim_01;
    uint32_t count_sim_02;
    uint32_t count_temp_01;
    uint32_t count_temp_02;
    int64_t get_type;
    ret = PstInsertValueFull(g_stmt, labelName, &count_sim_01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "count_sim_01 %u", count_sim_01);

    // 再次建表
    readJanssonFile("./schema/Vertex_02.gmjson", &vertex_label_schema_temp);
    ASSERT_NE((void *)NULL, vertex_label_schema_temp);
    ret = GmcCreateVertexLabel(g_stmt, vertex_label_schema_temp, g_configJson_temp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_t client_thr_01, client_thr_02;
    ret = pthread_create(&client_thr_01, NULL, PkInserttemp, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&client_thr_02, NULL, PkDeletesim, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_join(client_thr_01, NULL);
    pthread_join(client_thr_02, NULL);
    memset(g_command, 0, sizeof(g_command));
    // 多线程校验10次 成功一次即可
    snprintf(g_command, sizeof(g_command), "gmsysview -q V\\$STORAGE_MEMDATA_STAT |grep DEV_FREE |wc -l");
    for (int i =0; i<100; i++) {
        usleep(1000);
        ret = executeCommand(g_command, "1");
        if (ret == GMERR_OK) {
            AW_FUN_Log(LOG_STEP, "devFree check OK! expect is DEV_FREE acture DEV_FREE********");
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GetDeviceStatusisFree(&get_type);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(DEV_FREE, get_type);
    free(vertex_label_schema);
    free(vertex_label_schema_temp);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建两个线程,一个线程删除一般表数据,一个线程查看视图是否device归还,归还后,写入临时表数据
TEST_F(Timing, Timing_042_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"maxSeMem", (char *)"30");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    readJanssonFile("./schema/Vertex_01.gmjson", &vertex_label_schema);
    ASSERT_NE((void *)NULL, vertex_label_schema);
    GmcDropVertexLabel(g_stmt, labelName);
    ret = GmcCreateVertexLabel(g_stmt, vertex_label_schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写满表空间,并统计写入数量
    uint32_t count_sim_01;
    uint32_t count_sim_02;
    uint32_t count_temp_01;
    uint32_t count_temp_02;
    int64_t get_type;
    ret = PstInsertValueFull(g_stmt, labelName, &count_sim_01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "count_sim_01 %u", count_sim_01);

    // 再次建表
    readJanssonFile("./schema/Vertex_02.gmjson", &vertex_label_schema_temp);
    ASSERT_NE((void *)NULL, vertex_label_schema_temp);
    ret = GmcCreateVertexLabel(g_stmt, vertex_label_schema_temp, g_configJson_temp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_t client_thr_01, client_thr_02;
    ret = pthread_create(&client_thr_01, NULL, PkDeletesim, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&client_thr_02, NULL, DeviceRecycle, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_join(client_thr_01, NULL);
    pthread_join(client_thr_02, NULL);
    ret = PstInsertValueFull(g_stmt, labelName_temp, &count_temp_01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "count_temp_01 %u", count_temp_01);
    EXPECT_GT(count_sim_01, count_temp_01);
    free(vertex_label_schema);
    free(vertex_label_schema_temp);
    AW_FUN_Log(LOG_STEP, "test end.");
}
