/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: TSDB支持设置表级老化时间
 * Author: ywx1157510
 * Create: 2025-02-28
 */
#include "gtest/gtest.h"
#include "t_rd_ts.h"
#include "t_rd_sn.h"
#include "gmc_persist.h"
GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;

char *g_sysGMDBCfgTs = (char *)"$HOME/../usr/local/file/gmserver_ts.ini";
char g_command[1024] = {0};
char g_dbFilePath[1024] = {0};
char g_tempFilePath[1024] = {0};
static GmcConnT *conn = NULL;
static GmcStmtT *stmt = NULL;
#define CMD_LENGTH 1024
char g_cStoreDir[64] = {0};
char g_ip[20][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111", "33333333",
    "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
    "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
    "123456789a9887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
    "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
char g_name[20][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc", "lucy",
    "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};

char g_mark[20][32] = {"david", "nut", "bob", "olivia", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc", "lucy",
    "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
uint8_t g_message[20][160] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537", "0071 3521",
    "0010 0001", "", "3102 0022", "0000 0001", "9021 6538", "0071 3522", "0010 0002", "0010 0000", "3102 0023",
    "0000 0002", "9021 6539", "0071 3523", "0010 0003"};
char g_desText[20][64] = {"test data of the text type:0", "test data of the text type:1",
    "test data of the text type:2", "test data of the text type:3", "test data of the text type:4",
    "test data of the text type:5", "test data of the text type:6", "test data of the text type:7",
    "test data of the text type:8", "test data of the text type:9", "test data of the text type:0",
    "test data of the text type:1", "test data of the text type:2", "test data of the text type:3",
    "test data of the text type:4", "test data of the text type:5", "test data of the text type:6",
    "test data of the text type:7", "test data of the text type:8", "test data of the text type:9"};
// 注入顺序数据
int insertOrderData(GmcStmtT *stmt, char *tableName, int64_t dataCount, bool isAlterTable = false, int32_t cycle = 1)
{
    int32_t ret = -1;
    int64_t count = dataCount;
    int64_t *id;
    int64_t *id1;
    int64_t *time;
    int64_t *timet;
    // 申请内存
    char *nameList = (char *)malloc(count * 64);
    if (nameList == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    char *markList = (char *)malloc(count * 32);
    if (markList == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    char *ipList = (char *)malloc(count * 33);
    if (ipList == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    char **textList = (char **)malloc(count * sizeof(char *));
    if (textList == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    for (int i = 0; i < count; i++) {
        textList[i] = (char *)malloc(64 * sizeof(char));
        (void)memset(textList[i], 0, 64);
    }
    id = (int64_t *)malloc(sizeof(int64_t) * count);
    if (id == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    id1 = (int64_t *)malloc(sizeof(int64_t) * count);
    if (id == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    time = (int64_t *)malloc(sizeof(int64_t) * count);
    if (time == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    timet = (int64_t *)malloc(sizeof(int64_t) * count);
    if (timet == NULL) {
        AW_FUN_Log(LOG_DEBUG, "malloc failed.\n");
    }
    uint8_t *blobList[count];
    for (int32_t c = 0; c < cycle; c++) {
        // 构造数据
        for (int i = 0; i < count; i++) {
            id[i] = i + c * count;
            id1[i] = i;
            // 用于构建时序分区过期字段值
            time[i] = 3600 + i;   // 2010年1月1日0点 1262275200 1970,1,1,0,0
            timet[i] = 3600 + i;  // 2010年1月1日0点 1262275200
            int j = i % 20;
            memcpy((ipList + i * 33), (char *)g_ip[j], 33);
            memcpy((nameList + i * 64), (char *)g_name[j], 64);
            memcpy((markList + i * 32), (char *)g_mark[j], 32);
            (void)sprintf(*(textList + i), "%s", (char *)g_desText[j], 64);
        }
        for (uint32_t j = 0; j < count; j++) {
            blobList[j] = g_message[j % 20];
        }
        ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
        RETURN_IFERR(ret);
        ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int64_t));
        RETURN_IFERR(ret);
        ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, sizeof(id[0]), NULL);
        RETURN_IFERR(ret);
        ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, sizeof(time[0]), NULL);
        RETURN_IFERR(ret);
        ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, nameList, 64, NULL);
        RETURN_IFERR(ret);
        ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_FIXED, ipList, 33, NULL);
        RETURN_IFERR(ret);
        ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_STRING, textList, sizeof(textList[0]), NULL);
        RETURN_IFERR(ret);
        ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_BYTES, blobList, sizeof(blobList[0]), NULL);
        RETURN_IFERR(ret);
        ret = GmcBindCol(stmt, 6, (GmcDataTypeE)DB_DATATYPE_INT64, id1, sizeof(id1[0]), 0);
        RETURN_IFERR(ret);
        ret = GmcBindCol(stmt, 7, (GmcDataTypeE)DB_DATATYPE_INT64, timet, sizeof(timet[0]), 0);
        RETURN_IFERR(ret);
        if (isAlterTable) {
            ret = GmcBindCol(stmt, 8, (GmcDataTypeE)DB_DATATYPE_FIXED, markList, 32, NULL);
            RETURN_IFERR(ret);
        }
        ret = GmcExecute(stmt);
    }
    // 释放内存
    free(id);
    free(id1);
    free(time);
    free(timet);
    free(nameList);
    free(ipList);
    free(textList);
    return ret;
}
// 获取表中全部数据
int queryAllData(GmcStmtT *stmt, char *tableName, int64_t dataCountExpect, bool isAlterTable = false)
{
    int32_t ret = -1;
    ret = GmcExecDirect(stmt, (char *)"SELECT * FROM testdb", 128);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCountAcutal = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCountAcutal, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int fetchTimes = 0;
    int64_t id;
    int64_t id1;
    int64_t time;
    int64_t timet;

    // 申请内存
    char nameList[64];
    char markList[32];
    char ipList[33];
    char textList[64];
    char cBlob[160] = {0};

    bool eof = false;
    uint32_t size = 0;
    int i = 0;
    bool isNull = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        fetchTimes++;

        id = 0;
        size = sizeof(int64_t);
        // 首个字段作为主键不校验
        ret = GmcGetPropertyById(stmt, 0, &id, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // id[i] = i;
        // id1[i] = i;
        // 用于构建时序分区过期字段值
        // time[i] = i;           // 2010年1月1日0点 1262275200
        // timet[i] = 10000 + i;  // 2010年1月1日0点 1262275200
        int j = i % 20;
        //  time integer+
        size = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &time, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(3600 + i, time);
        //  name char(64)
        size = 64;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, nameList, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(0, strcmp(nameList, g_name[j]));
        // ip inet
        size = 33;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, ipList, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_STR((const char *)g_ip[j], ipList);
        // description text
        size = sizeof(textList);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, textList, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_STR((const char *)g_desText[j], textList);
        // message blob(160)
        size = sizeof(cBlob);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 5, cBlob, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_STR((const char *)g_message[j % 20], cBlob);
        // id1 integer
        size = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 6, &id, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(i, id);
        // time1 integer
        size = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 7, &timet, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(3600 + i, timet);
        i++;
    }
    // 释放内存
    return fetchTimes;
}
void InitTsCfgChange()
{
// euler和环境环境清共享内存
#if defined RUN_INDEPENDENT
    system("sh $TEST_HOME/tools/stop.sh -f");
#endif
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("rm -rf /data/gmdb");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts recover");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"enableVectorizedPushDown=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
}

int32_t GetViewFieldResultValue(const char *viewName)
{
    int maxCmdSize = 256;
    char cmdOutput[maxCmdSize];
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));

    char command[maxCmdSize];
    (void)snprintf(command, maxCmdSize, "%s", viewName);

    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        printf("popen(%s) error./n", command);
    }
    EXPECT_NE((void *)NULL, pf);
    while (NULL != fgets(cmdOutput, maxCmdSize, pf)) {
    };
    pclose(pf);

    return atoi(cmdOutput);
}

class tsdb_truncate1 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        InitTsCfgChange();
        int32_t ret = -1;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        // 创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        ret = TestTsGmcConnect(&conn, &stmt, 0);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int32_t ret = -1;
        ret = testGmcDisconnect(conn, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        close_epoll_thread();
        testEnvClean();
        RecoverTsCiCfg();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void tsdb_truncate1::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    AW_ADD_TRUNCATION_WHITE_LIST(1, "TsServiceEntry try execute ddl sql");
    int32_t ret =
        TestGetResultCommand("cat ../common/logicTablePath.txt | tr -d '$\r'", NULL, g_cStoreDir, sizeof(g_cStoreDir));
    EXPECT_EQ(0, ret);
char dirpath = '/';
#if defined RUN_INDEPENDENT
    (void)sprintf(g_cStoreDir, "%cdata/gmdb/cstore", dirpath);
#else
    (void)sprintf(g_cStoreDir, "%cmnt/hdd/data/gmdb/cstore", dirpath);
#endif
}

void tsdb_truncate1::TearDown()
{
    AW_CHECK_LOG_END();
}

class tsdb_alter_ttl : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        // euler和环境环境清共享内存
#if defined RUN_INDEPENDENT
        system("sh $TEST_HOME/tools/stop.sh -f");
#endif
        system("sh $TEST_HOME/tools/stop.sh -ts");
        system("rm -rf /data/gmdb");
        system("sh $TEST_HOME/tools/modifyCfg.sh -ts recover");
        TsDefulatDbFileClean();
        // UpdateTsLcmCheckPoind();
        char cmd[64] = {0};
        (void)sprintf(cmd, "sh $TEST_HOME/tools/modifyCfg.sh -ts \"tsLcmCheckPeriod=9\"");
        system(cmd);
        (void)sprintf(cmd, "sh $TEST_HOME/tools/modifyCfg.sh -ts \"spaceMaxNum=20\"");
        system(cmd);
        system("sh $TEST_HOME/tools/start.sh -ts");
        int32_t ret = -1;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        // 创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        ret = TestTsGmcConnect(&conn, &stmt, 0);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int32_t ret = -1;
        ret = testGmcDisconnect(conn, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        close_epoll_thread();
        testEnvClean();
        RecoverTsCiCfg();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void tsdb_alter_ttl::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    AW_ADD_TRUNCATION_WHITE_LIST(1, "TsServiceEntry try execute ddl sql");
    AW_ADD_TRUNCATION_WHITE_LIST(1, "TsServiceEntry try prepare sql");
    AddWhiteList(GMERR_INVALID_JSON_CONTENT);
    int32_t ret =
        TestGetResultCommand("cat ../common/logicTablePath.txt | tr -d '$\r'", NULL, g_cStoreDir, sizeof(g_cStoreDir));
    EXPECT_EQ(0, ret);
}

void tsdb_alter_ttl::TearDown()
{
    AW_CHECK_LOG_END();
}
char oneHour[] = "1 hour";
char oneDay[] = "1 day";
char oneMonth[] = "1 month";
char oneYear[] = "1 year";
char tabelName[] = "testdb";
char compressModeNo[] = "no";
// 001.对不含ttl的表 修改ttl属性时，属性值为2min,预期修改失败，数据不会被删除
TEST_F(tsdb_alter_ttl, Timing_079_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    // 建表
    ret = CreateTableToTtl(stmt, tabelName, compressModeNo, oneHour, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以内
    int64_t startTime = time_T - 3600;
    int64_t curDirSize = InsertDataToTTLTbale(stmt, tabelName, startTime, 2);
    AW_FUN_Log(LOG_DEBUG, "BeforeTTLCheckDirSize is %ld\n", curDirSize);
    AW_MACRO_ASSERT_NE_INT(0, curDirSize);
    int64_t beforeTTLCheckDirSize = curDirSize;

    // 修改ttl属性时，属性值为2min
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '2min');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    // 等待10s确保ttlCheck已经触发
    sleep(10);
    curDirSize = GetDirSize(g_cStoreDir);
    AW_MACRO_EXPECT_EQ_INT(beforeTTLCheckDirSize, curDirSize);
    AW_FUN_Log(LOG_DEBUG, "AfterTTLCheckDirSize is %ld, TTLCheck is worked.\n", curDirSize);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002.对含ttl的表 修改ttl属性时，属性值为2min,预期修改失败，属性维持原值且数据正常被删除
TEST_F(tsdb_alter_ttl, Timing_079_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    // 建表
    ret = CreateTableToTtl(stmt, tabelName, compressModeNo, oneHour, oneHour);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在2h以内
    int64_t startTime = time_T - 10800;
    int64_t curDirSize = InsertDataToTTLTbale(stmt, tabelName, startTime, 2);
    AW_FUN_Log(LOG_DEBUG, "BeforeTTLCheckDirSize is %ld\n", curDirSize);
    AW_MACRO_ASSERT_NE_INT(0, curDirSize);
    int64_t beforeTTLCheckDirSize = curDirSize;

    // 修改ttl属性时，属性值为2min
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '2min');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    // 等待10s确保ttlCheck已经触发
    sleep(10);
    curDirSize = GetDirSize(g_cStoreDir);
    AW_MACRO_ASSERT_NE_INT(0, (beforeTTLCheckDirSize - curDirSize));
    AW_FUN_Log(LOG_DEBUG, "AfterTTLCheckDirSize is %ld, TTLCheck is worked.\n", curDirSize);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 003.对不含ttl的表，修改ttl属性时，属性值为2s,预期修改失败，数据不会被删除
TEST_F(tsdb_alter_ttl, Timing_079_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    AddWhiteList(GMERR_INVALID_JSON_CONTENT);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 hour');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 修改ttl属性时，属性值为2s
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '2 s');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    sleep(10);
    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 004.对含ttl的表，修改ttl属性时，属性值为2s,预期修改失败，属性维持原值且数据正常被删除
TEST_F(tsdb_alter_ttl, Timing_079_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    AddWhiteList(GMERR_INVALID_JSON_CONTENT);
    // 建表
    ret = CreateTableToTtl(stmt, tabelName, compressModeNo, oneHour, oneHour);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在2h以内
    int64_t startTime = time_T - 10800;
    int64_t curDirSize = InsertDataToTTLTbale(stmt, tabelName, startTime, 2);
    AW_FUN_Log(LOG_DEBUG, "BeforeTTLCheckDirSize is %ld\n", curDirSize);
    AW_MACRO_ASSERT_NE_INT(0, curDirSize);
    int64_t beforeTTLCheckDirSize = curDirSize;

    // 修改ttl属性时，属性值为2min
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '2 s');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    // 等待10s确保ttlCheck已经触发
    sleep(10);
    curDirSize = GetDirSize(g_cStoreDir);
    AW_MACRO_ASSERT_NE_INT(0, (beforeTTLCheckDirSize - curDirSize));
    AW_FUN_Log(LOG_DEBUG, "AfterTTLCheckDirSize is %ld, TTLCheck is worked.\n", curDirSize);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 005.对不含ttl的表，修改ttl属性时，属性值为@！,预期修改失败，数据不会被删除
TEST_F(tsdb_alter_ttl, Timing_079_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 hour');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 修改ttl属性时，属性值为@!
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '@!');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    sleep(10);
    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 006.对含ttl的表，修改ttl属性时，属性值为@！,预期修改失败，属性维持原值且数据正常被删除
TEST_F(tsdb_alter_ttl, Timing_079_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    // AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    // AddWhiteList(GMERR_INVALID_JSON_CONTENT);
    // 建表
    ret = CreateTableToTtl(stmt, tabelName, compressModeNo, oneHour, oneHour);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在2h以内
    int64_t startTime = time_T - 7261;
    int64_t curDirSize = InsertDataToTTLTbale(stmt, tabelName, startTime, 2);
    AW_FUN_Log(LOG_DEBUG, "BeforeTTLCheckDirSize is %ld\n", curDirSize);
    AW_MACRO_ASSERT_NE_INT(0, curDirSize);
    int64_t beforeTTLCheckDirSize = curDirSize;

    // 修改ttl属性时，属性值为@！
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '@!');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    // 等待10s确保ttlCheck已经触发
    sleep(10);
    curDirSize = GetDirSize(g_cStoreDir);
    AW_MACRO_ASSERT_NE_INT(0, curDirSize);
    AW_MACRO_ASSERT_NE_INT(0, (beforeTTLCheckDirSize - curDirSize));
    AW_FUN_Log(LOG_DEBUG, "AfterTTLCheckDirSize is %ld, TTLCheck is worked.\n", curDirSize);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 007.对不含ttl的表 修改ttl属性时，属性值为0.4H,预期修改失败，数据不会被删除
TEST_F(tsdb_alter_ttl, Timing_079_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_DATA_EXCEPTION);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 hour');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 修改ttl属性时，属性值为0.4H
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '0.4 H');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 008.对含ttl的表 修改ttl属性时，属性值为0.4H ,预期修改失败，属性维持原值且数据正常被删除
TEST_F(tsdb_alter_ttl, Timing_079_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_DATA_EXCEPTION);
    // 建表
    ret = CreateTableToTtl(stmt, tabelName, compressModeNo, oneHour, oneHour);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在2h以内
    int64_t startTime = time_T - 10800;
    int64_t curDirSize = InsertDataToTTLTbale(stmt, tabelName, startTime, 2);
    AW_FUN_Log(LOG_DEBUG, "BeforeTTLCheckDirSize is %ld\n", curDirSize);
    AW_MACRO_ASSERT_NE_INT(0, curDirSize);
    int64_t beforeTTLCheckDirSize = curDirSize;

    // 修改ttl属性时，属性值为0.4H
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '0.4 H');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 等待10s确保ttlCheck已经触发
    sleep(10);
    curDirSize = GetDirSize(g_cStoreDir);
    AW_MACRO_ASSERT_NE_INT(0, (beforeTTLCheckDirSize - curDirSize));
    AW_FUN_Log(LOG_DEBUG, "AfterTTLCheckDirSize is %ld, TTLCheck is worked.\n", curDirSize);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 009.对不含ttl的表，修改ttl属性时，属性值为100,预期修改失败，数据不会被删除
TEST_F(tsdb_alter_ttl, Timing_079_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 hour');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 修改ttl属性时，属性值为100
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '100');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    sleep(10);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 010.对含ttl的表，修改ttl属性时，属性值为100,预期修改失败，属性维持原值且数据正常被删除
TEST_F(tsdb_alter_ttl, Timing_079_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    // 建表
    ret = CreateTableToTtl(stmt, tabelName, compressModeNo, oneHour, oneHour);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在3h以内
    int64_t startTime = time_T - 10800;
    int64_t curDirSize = InsertDataToTTLTbale(stmt, tabelName, startTime, 2);
    AW_FUN_Log(LOG_DEBUG, "BeforeTTLCheckDirSize is %ld\n", curDirSize);
    AW_MACRO_ASSERT_NE_INT(0, curDirSize);
    int64_t beforeTTLCheckDirSize = curDirSize;

    // 修改ttl属性时，属性值为100
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '100');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    // 等待10s确保ttlCheck已经触发
    sleep(10);
    curDirSize = GetDirSize(g_cStoreDir);
    AW_MACRO_ASSERT_NE_INT(0, (beforeTTLCheckDirSize - curDirSize));
    AW_FUN_Log(LOG_DEBUG, "AfterTTLCheckDirSize is %ld, TTLCheck is worked.\n", curDirSize);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 011.对不含ttl的表，修改ttl属性时，属性值为空,预期修改失败，数据不会被删除
TEST_F(tsdb_alter_ttl, Timing_079_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 hour');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 修改ttl属性时，属性值为空
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = ' ');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    sleep(10);
    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 012.对含ttl的表，修改ttl属性时，属性值为空,预期修改失败，属性维持原值且数据正常被删除
TEST_F(tsdb_alter_ttl, Timing_079_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_INVALID_JSON_CONTENT);
    // 建表
    ret = CreateTableToTtl(stmt, tabelName, compressModeNo, oneHour, oneHour);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在2h以内
    int64_t startTime = time_T - 10800;
    int64_t curDirSize = InsertDataToTTLTbale(stmt, tabelName, startTime, 2);
    AW_FUN_Log(LOG_DEBUG, "BeforeTTLCheckDirSize is %ld\n", curDirSize);
    AW_MACRO_ASSERT_NE_INT(0, curDirSize);
    int64_t beforeTTLCheckDirSize = curDirSize;

    // 修改ttl属性时，属性值为空
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = ' ');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    // 等待10s确保ttlCheck已经触发
    sleep(10);
    curDirSize = GetDirSize(g_cStoreDir);
    AW_MACRO_ASSERT_NE_INT(0, (beforeTTLCheckDirSize - curDirSize));
    AW_FUN_Log(LOG_DEBUG, "AfterTTLCheckDirSize is %ld, TTLCheck is worked.\n", curDirSize);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 013.对不含ttl的表，修改ttl属性时，ttl关键字错误,预期修改失败，数据不会被删除
TEST_F(tsdb_alter_ttl, Timing_079_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 hour');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // ttl关键字错误
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE testdb  SET (tl = '1H');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 014.对含ttl的表，修改ttl属性时，ttl关键字错误,,预期修改失败，属性维持原值且数据正常被删除
TEST_F(tsdb_alter_ttl, Timing_079_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    // 建表
    ret = CreateTableToTtl(stmt, tabelName, compressModeNo, oneHour, oneHour);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在2h以内
    int64_t startTime = time_T - 10800;
    int64_t curDirSize = InsertDataToTTLTbale(stmt, tabelName, startTime, 2);
    AW_FUN_Log(LOG_DEBUG, "BeforeTTLCheckDirSize is %ld\n", curDirSize);
    AW_MACRO_ASSERT_NE_INT(0, curDirSize);
    int64_t beforeTTLCheckDirSize = curDirSize;

    // 修改ttl属性时，ttl关键字错误
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE testdb  SET (tl = '1 month');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);

    // 等待10s确保ttlCheck已经触发
    sleep(10);
    curDirSize = GetDirSize(g_cStoreDir);
    AW_MACRO_ASSERT_NE_INT(0, (beforeTTLCheckDirSize - curDirSize));
    AW_FUN_Log(LOG_DEBUG, "AfterTTLCheckDirSize is %ld, TTLCheck is worked.\n", curDirSize);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 015.对不存在的表，修改ttl属性时，预期报错
TEST_F(tsdb_alter_ttl, Timing_079_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_UNDEFINED_TABLE);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 hour');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 不存在的表，修改ttl属性
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE testdb1  SET (ttl = '1H');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 016.对不含ttl的表，修改ttl属性时，存在IF EXISTS,报错
TEST_F(tsdb_alter_ttl, Timing_079_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 hour');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 修改ttl属性时，存在IF EXISTS
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE IF EXISTS testdb  SET (ttl = '1H');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 017.对含ttl的表，修改ttl属性时，存在IF EXISTS,
TEST_F(tsdb_alter_ttl, Timing_079_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 hour');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 修改ttl属性时，存在IF EXISTS
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE IF EXISTS testdb  SET (ttl = '1H');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 018.对不含ttl的表，修改ttl属性时，alter table小写,
TEST_F(tsdb_alter_ttl, Timing_079_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 hour');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 修改ttl属性时，alter table小写
    char sqlAlterCmd[CMD_LENGTH] = "alter table testdb  SET (ttl = '1 H');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(15);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, 0);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 019.对含ttl的表，修改ttl属性时，alter table小写,
TEST_F(tsdb_alter_ttl, Timing_079_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    char fdCmd[256] = {0};
    (void)sprintf(fdCmd, "ls -l /proc/`pidof gmserver_ts`/fd |wc -l");
    int32_t fdBefore = 0;
    int32_t fdAfter = 0;
    fdBefore = GetViewFieldResultValue(fdCmd);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 hour');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 1, fdAfter);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 修改ttl属性时，alter table小写
    char sqlAlterCmd[CMD_LENGTH] = "alter table testdb  SET (ttl = '1 H');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 1, fdAfter);
    sleep(15);
    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, 0);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 1, fdAfter);
    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore, fdAfter);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 020.对不含ttl的表，修改ttl属性时，set 小写，ttl 大写,
TEST_F(tsdb_alter_ttl, Timing_079_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 hour');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 修改ttl属性时，set 小写，ttl 大写
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE testdb  set (TTL = '1 H');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(15);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, 0);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 021.对含ttl的表，修改ttl属性时，set 小写，ttl 大写,
TEST_F(tsdb_alter_ttl, Timing_079_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 hour');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 修改ttl属性时，set 小写，ttl 大写
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE testdb  set (TTL = '1 H');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(15);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, 0);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 022.interval设置为1H，ttl设置为1year
TEST_F(tsdb_alter_ttl, Timing_079_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 hour');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // interval设置为1H，ttl设置为1year
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '1 year');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    sleep(15);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 023.interval设置为1H，ttl设置为1month
TEST_F(tsdb_alter_ttl, Timing_079_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 hour');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // interval设置为1H，ttl设置为1month
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '1 month');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 024.interval设置为1H，ttl设置为24*365*100+1H(DTS)
TEST_F(tsdb_alter_ttl, Timing_079_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 hour');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 修改ttl属性时，interval设置为1H，ttl设置为24*365*100+1H
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '877601 H');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 025.interval设置为1day，ttl设置为24H
TEST_F(tsdb_alter_ttl, Timing_079_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 day');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // interval设置为1day，ttl设置为24H
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '24 H');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 026.interval设置为1day，ttl设置为1month
TEST_F(tsdb_alter_ttl, Timing_079_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 day');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // interval设置为1day，ttl设置为1month
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '1 month');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 027.interval设置为1day，ttl设置为1year
TEST_F(tsdb_alter_ttl, Timing_079_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 day');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // interval设置为1day，ttl设置为1year
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '1 year');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 028.interval设置为1day，ttl设置为365*100+1day
TEST_F(tsdb_alter_ttl, Timing_079_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 day');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // interval设置为1day，ttl设置为365*100+1day
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '36601 day');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 029.interval设置为1month，ttl设置为1h
TEST_F(tsdb_alter_ttl, Timing_079_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 month');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // interval设置为1month，ttl设置为1h
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '1 H');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 030.interval设置为1month，ttl设置为1day
TEST_F(tsdb_alter_ttl, Timing_079_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 month');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // interval设置为1month，ttl设置为1day
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '1 day');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 031.interval设置为1month，ttl设置为12*100+1month
TEST_F(tsdb_alter_ttl, Timing_079_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 month');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // interval设置为1month，ttl设置为12*100+1month
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '1201 month');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 032.interval设置为1month，ttl设置为100+1year
TEST_F(tsdb_alter_ttl, Timing_079_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 month');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 修改ttl属性时，属性值为2min
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '101 year');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 033.interval设置为1year，ttl设置为1H
TEST_F(tsdb_alter_ttl, Timing_079_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 year');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 033.interval设置为1year，ttl设置为1H
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '1 H');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 034.interval设置为1year，ttl设置为1day
TEST_F(tsdb_alter_ttl, Timing_079_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 year');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // interval设置为1year，ttl设置为1day
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '1 day');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 035.interval设置为1year，ttl设置为1month
TEST_F(tsdb_alter_ttl, Timing_079_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 year');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // interval设置为1year，ttl设置为1month
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '1 month');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 036.interval设置为1year，ttl设置为100+1year
TEST_F(tsdb_alter_ttl, Timing_079_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 year');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // interval设置为1year，ttl设置为100+1year
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '101 yar');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 037.ALTER TABLE table_name  set (ttl= "0h');
TEST_F(tsdb_alter_ttl, Timing_079_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 H', ttl = '1 H');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // ALTER TABLE table_name  set (ttl= "0h');
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '0 H');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    sleep(15);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, 0);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 038.ALTER TABLE table_name  set (ttl= "0month');
TEST_F(tsdb_alter_ttl, Timing_079_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 H', ttl = '1 H');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // ALTER TABLE table_name  set (ttl= "0month');
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '0 month');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    sleep(15);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, 0);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 039.ALTER TABLE table_name  set (ttl= "0year');
TEST_F(tsdb_alter_ttl, Timing_079_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 H', ttl = '1 H');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // ALTER TABLE table_name  set (ttl= "0year');
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '0 year');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    sleep(15);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, 0);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 040.ALTER TABLE table_name  set (ttl= "0day');
TEST_F(tsdb_alter_ttl, Timing_079_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 H', ttl = '1 H');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // ALTER TABLE table_name  set (ttl= "0day');
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '0 day');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    sleep(15);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, 0);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
char diskLimitValue[] = "DISK_LIMIT: 0";
char diskLimitValue1[] = "DISK_LIMIT: 1099511627776";
char diskLimitValue2[] = "DISK_LIMIT: 4096";
void GetDiskLimitCurrentValue(char value[] = NULL, char value1[] = "TABLE_NAME: testdb")
{
    char cmd[256] = "00";
    snprintf(cmd, 1024, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    system(cmd);
    int32_t ret = executeCommand(cmd, value1, value);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, sizeof(cmd));
}
// 041.对内存表使用alter语法增加ttl属性|| disk_limit 预期成功
TEST_F(tsdb_alter_ttl, Timing_079_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  "
        "id1 "
        "integer, time1 integer) with  (enGine = 'mEmOry',time_col = 'time', interval = '1 year');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

     char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 对内存表使用alter语法增加ttl属性|| disk_limit 预期成功
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '99 year');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char sqlAlterCmd1[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '98 year', disk_limit = '1000 MB');";
    ret = GmcExecDirect(stmt, sqlAlterCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 获取磁盘大小
    GetDiskLimitCurrentValue((char *)"1048572096");

    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 042.对存在cache_size字段的表新增ttl属性报错
TEST_F(tsdb_alter_ttl, Timing_079_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 hour', cache_size = 10000);",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对存在cache_size字段的表新增ttl属性报错
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '1 H');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 043.ttl为属性值为空，disk_limit值为空
TEST_F(tsdb_alter_ttl, Timing_079_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  "
        "id1 "
        "integer, time1 integer) with  (enGine = 'mEmOry',time_col = 'time', interval = '1 hour');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // ttl为属性值为空，disk_limit值为空
    char sqlAlterCmd1[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = ' ', disk_limit = ' ');";
    ret = GmcExecDirect(stmt, sqlAlterCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 044.ttl为非interval整数倍，disk_limit值大于1TB
TEST_F(tsdb_alter_ttl, Timing_079_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 month');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // ttl为非interval整数倍，disk_limit值大于1TB
    char sqlAlterCmd1[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '1 H', disk_limit = '2 TB');";
    ret = GmcExecDirect(stmt, sqlAlterCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 045.ttl为非interval整数倍，disk_limit值小于4kb
TEST_F(tsdb_alter_ttl, Timing_079_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 month');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // ttl为非interval整数倍，disk_limit值小于4kb
    char sqlAlterCmd1[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '1 H', disk_limit = '3 KB');";
    ret = GmcExecDirect(stmt, sqlAlterCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 046.ttl为interval整数倍，disk_limit值为空
TEST_F(tsdb_alter_ttl, Timing_079_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 month');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // ttl为非interval整数倍，disk_limit值小于4kb
    char sqlAlterCmd1[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '1 month', disk_limit = ' ');";
    ret = GmcExecDirect(stmt, sqlAlterCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 047.ttl为空，disk_limit值正常
TEST_F(tsdb_alter_ttl, Timing_079_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 month');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // ttl为空，disk_limit值正常
    char sqlAlterCmd1[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = ' ', disk_limit = '4 KB');";
    ret = GmcExecDirect(stmt, sqlAlterCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 048.ttl为interval整数倍，disk_limit值大于1TB
TEST_F(tsdb_alter_ttl, Timing_079_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 month');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // ttl为interval整数倍，disk_limit值大于1TB
    char sqlAlterCmd1[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '1 month', disk_limit = '2 TB');";
    ret = GmcExecDirect(stmt, sqlAlterCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 049.ttl为interval整数倍，disk_limit值小于4kb
TEST_F(tsdb_alter_ttl, Timing_079_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 month');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // ttl为interval整数倍，disk_limit值小于4kb
    char sqlAlterCmd1[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '1 month', disk_limit = '3 kb');";
    ret = GmcExecDirect(stmt, sqlAlterCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 050.原表中含ttl，ttl为非interval整数倍，disk_limit值10MB
TEST_F(tsdb_alter_ttl, Timing_079_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 month', ttl = '1 month');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 原表中含ttl，ttl为非interval整数倍，disk_limit值10MB
    char sqlAlterCmd1[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '1 H', disk_limit = '10 MB');";
    ret = GmcExecDirect(stmt, sqlAlterCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    sleep(15);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, 0);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 051.原表中不含ttl，ttl为非interval整数倍，disk_limit值10MB
TEST_F(tsdb_alter_ttl, Timing_079_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 month');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // ttl为非interval整数倍，disk_limit值10MB
    char sqlAlterCmd1[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '1 H', disk_limit = '10 MB');";
    ret = GmcExecDirect(stmt, sqlAlterCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 052.设置两次ttl为非interval整数倍，第一次异常，第二次异常
TEST_F(tsdb_alter_ttl, Timing_079_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 month');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // ttl为非interval整数倍，disk_limit值10MB
    char sqlAlterCmd1[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '1 H', ttl = '1 day');";
    ret = GmcExecDirect(stmt, sqlAlterCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 053.设置两次disk_limit均不在取值范围内，第一次异常，第二次异常
TEST_F(tsdb_alter_ttl, Timing_079_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 month');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // ttl为非interval整数倍，disk_limit值10MB
    char sqlAlterCmd1[CMD_LENGTH] = "ALTER TABLE testdb  SET (disk_limit = '3 kb', disk_limit = '2 TB');";
    ret = GmcExecDirect(stmt, sqlAlterCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 054.设置两次ttl，第一次正常，第二次异常
TEST_F(tsdb_alter_ttl, Timing_079_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_DUPLICATE_OBJECT);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 month');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置两次ttl，第一次正常，第二次异常
    char sqlAlterCmd1[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '1 month', ttl = '10 day');";
    ret = GmcExecDirect(stmt, sqlAlterCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DUPLICATE_OBJECT, ret);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 055.设置两次ttl，第一次异常，第二次正常
TEST_F(tsdb_alter_ttl, Timing_079_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 month');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置两次ttl，第一次异常，第二次正常
    char sqlAlterCmd1[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '1 H', ttl = '1 month');";
    ret = GmcExecDirect(stmt, sqlAlterCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 056.设置两次disk_limit，第一次异常，第二次正常
TEST_F(tsdb_alter_ttl, Timing_079_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 month');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置两次disk_limit，第一次异常，第二次正常
    char sqlAlterCmd1[CMD_LENGTH] = "ALTER TABLE testdb  SET (disk_limit = '3 kb', disk_limit = '10 MB');";
    ret = GmcExecDirect(stmt, sqlAlterCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 057.设置两次disk_limit，第一次正常，第二次异常
TEST_F(tsdb_alter_ttl, Timing_079_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_DUPLICATE_OBJECT);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 month');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置两次disk_limit，第一次正常，第二次异常
    char sqlAlterCmd1[CMD_LENGTH] = "ALTER TABLE testdb  SET (disk_limit = '10 MB', disk_limit = '10 TB');";
    ret = GmcExecDirect(stmt, sqlAlterCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DUPLICATE_OBJECT, ret);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 058.ttl为interval整数倍，ttl为interval整数倍
TEST_F(tsdb_alter_ttl, Timing_079_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_DUPLICATE_OBJECT);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 month');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // ttl为interval整数倍，ttl为interval整数倍
    char sqlAlterCmd1[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '1 month', ttl = '1 month');";
    ret = GmcExecDirect(stmt, sqlAlterCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DUPLICATE_OBJECT, ret);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 059.disk_limit值正常，disk_limit值正常
TEST_F(tsdb_alter_ttl, Timing_079_059)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_DUPLICATE_OBJECT);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 month');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // disk_limit值正常，disk_limit值正常
    char sqlAlterCmd1[CMD_LENGTH] = "ALTER TABLE testdb  SET (disk_limit = '10 MB', disk_limit = '10 MB');";
    ret = GmcExecDirect(stmt, sqlAlterCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DUPLICATE_OBJECT, ret);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 060.设置ttl属性没有=，预期失败
TEST_F(tsdb_alter_ttl, Timing_079_060)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_SYNTAX_ERROR);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 month');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置ttl属性没有=，预期失败
    char sqlAlterCmd1[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl '1 month');";
    ret = GmcExecDirect(stmt, sqlAlterCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 061.设置ttl属性没有ttl，预期失败
TEST_F(tsdb_alter_ttl, Timing_079_061)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    AddWhiteList(GMERR_SYNTAX_ERROR);
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 month');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置ttl属性没有ttl，预期失败
    char sqlAlterCmd1[CMD_LENGTH] = "ALTER TABLE testdb  SET ( = '1 month');";
    ret = GmcExecDirect(stmt, sqlAlterCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 062.对不存在ttl的表新增ttl属性并设置为interval整数倍4H，并预期能够生效
TEST_F(tsdb_alter_ttl, Timing_079_062)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 H');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对不存在ttl的表新增ttl属性并设置为interval整数倍4H，并预期能够生效
    char sqlAlterCmd1[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '4 H');";
    ret = GmcExecDirect(stmt, sqlAlterCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 间隔一段时间查询，数据过期删除
    sleep(10);
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, 0);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 063.对不存在ttl的表新增ttl属性并设置为interval整数倍4day，并预期能够生效
TEST_F(tsdb_alter_ttl, Timing_079_063)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 day');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对不存在ttl的表新增ttl属性并设置为interval整数倍4day，并预期能够生效
    char sqlAlterCmd1[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '4 day');";
    ret = GmcExecDirect(stmt, sqlAlterCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 间隔一段时间查询，数据过期删除
    sleep(10);
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, 0);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 064.对不存在ttl的表新增ttl属性并设置为interval整数倍4month，并预期能够生效
TEST_F(tsdb_alter_ttl, Timing_079_064)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 month');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对不存在ttl的表新增ttl属性并设置为interval整数倍4month，并预期能够生效
    char sqlAlterCmd1[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '4 month');";
    ret = GmcExecDirect(stmt, sqlAlterCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);
    // 间隔一段时间查询，数据过期删除
    sleep(10);
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, 0);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 065.对不存在ttl的表新增ttl属性并设置为interval整数倍4year，并预期能够生效
TEST_F(tsdb_alter_ttl, Timing_079_065)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 year');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对不存在ttl的表新增ttl属性并设置为interval整数倍4year，并预期能够生效
    char sqlAlterCmd1[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '4 year');";
    ret = GmcExecDirect(stmt, sqlAlterCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 间隔一段时间查询，数据过期删除
    sleep(10);
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, 0);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 066.对不存在ttl的表新增ttl属性并设置为0，并预期能够生效，数据不会删除
TEST_F(tsdb_alter_ttl, Timing_079_066)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 month');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // ttl为非interval整数倍，disk_limit值10MB
    char sqlAlterCmd1[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '0');";
    ret = GmcExecDirect(stmt, sqlAlterCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 间隔一段时间查询，数据还在
    sleep(10);
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 067.对存在ttl的表新增ttl属性并设置为0，并预期能够生效，数据不会删除
TEST_F(tsdb_alter_ttl, Timing_079_067)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 month', ttl = '1 month');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对存在ttl的表新增ttl属性并设置为0，并预期能够生效，数据不会删除
    char sqlAlterCmd1[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '0');";
    ret = GmcExecDirect(stmt, sqlAlterCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(10);

    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, count);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 068.对存在ttl的表新增ttl属性并设置为interval整数倍4H，并预期能够生效
TEST_F(tsdb_alter_ttl, Timing_079_068)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    // 建表
    ret = CreateTableToTtl(stmt, tabelName, compressModeNo, oneHour, oneHour);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在3h以内
    int64_t startTime = time_T - 10800;
    int64_t curDirSize = InsertDataToTTLTbale(stmt, tabelName, startTime, 2);
    AW_FUN_Log(LOG_DEBUG, "BeforeTTLCheckDirSize is %ld\n", curDirSize);
    AW_MACRO_ASSERT_NE_INT(0, curDirSize);
    int64_t beforeTTLCheckDirSize = curDirSize;

    // 对存在ttl的表新增ttl属性并设置为interval整数倍4H，并预期能够生效
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '4 H');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待15s确保ttlCheck已经触发且数据不会过期
    sleep(15);
    curDirSize = GetDirSize(g_cStoreDir);
    AW_MACRO_EXPECT_EQ_INT(beforeTTLCheckDirSize, curDirSize);
    AW_FUN_Log(LOG_DEBUG, "AfterTTLCheckDirSize is %ld, TTLCheck is worked.\n", curDirSize);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 069.对存在ttl的表新增ttl属性并设置为interval整数倍4day，并预期能够生效
TEST_F(tsdb_alter_ttl, Timing_079_069)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    // 建表
    ret = CreateTableToTtl(stmt, tabelName, compressModeNo, oneDay, oneDay);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在3day以内
    int64_t startTime = time_T - 24 * 60 * 60 * 3;
    int64_t curDirSize = InsertDataToTTLTbale(stmt, tabelName, startTime, 2);
    AW_FUN_Log(LOG_DEBUG, "BeforeTTLCheckDirSize is %ld\n", curDirSize);
    AW_MACRO_ASSERT_NE_INT(0, curDirSize);
    int64_t beforeTTLCheckDirSize = curDirSize;

    // 对存在ttl的表新增ttl属性并设置为interval整数倍4day，并预期能够生效
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '4 day');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待15s确保ttlCheck已经触发且数据不会过期
    sleep(15);
    curDirSize = GetDirSize(g_cStoreDir);
    AW_MACRO_EXPECT_EQ_INT(beforeTTLCheckDirSize, curDirSize);
    AW_FUN_Log(LOG_DEBUG, "AfterTTLCheckDirSize is %ld, TTLCheck is worked.\n", curDirSize);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 070.对存在ttl的表新增ttl属性并设置为interval整数倍4month，并预期能够生效
TEST_F(tsdb_alter_ttl, Timing_079_070)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    // 建表
    ret = CreateTableToTtl(stmt, tabelName, compressModeNo, oneMonth, oneMonth);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在3month以内
    int64_t startTime = time_T - 24 * 60 * 60 * 30 * 3;
    int64_t curDirSize = InsertDataToTTLTbale(stmt, tabelName, startTime, 2);
    AW_FUN_Log(LOG_DEBUG, "BeforeTTLCheckDirSize is %ld\n", curDirSize);
    AW_MACRO_ASSERT_NE_INT(0, curDirSize);
    int64_t beforeTTLCheckDirSize = curDirSize;

    // 对存在ttl的表新增ttl属性并设置为interval整数倍4month，并预期能够生效
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '4 month');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待15s确保ttlCheck已经触发且数据不会过期
    sleep(15);
    curDirSize = GetDirSize(g_cStoreDir);
    AW_MACRO_EXPECT_EQ_INT(beforeTTLCheckDirSize, curDirSize);
    AW_FUN_Log(LOG_DEBUG, "AfterTTLCheckDirSize is %ld, TTLCheck is worked.\n", curDirSize);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 071.对存在ttl的表新增ttl属性并设置为interval整数倍4year，并预期能够生效
TEST_F(tsdb_alter_ttl, Timing_079_071)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    // 建表
    ret = CreateTableToTtl(stmt, tabelName, compressModeNo, oneYear, oneYear);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在3year以内
    int64_t startTime = time_T - 24 * 60 * 60 * 30 * 12 * 3;
    int64_t curDirSize = InsertDataToTTLTbale(stmt, tabelName, startTime, 2);
    AW_FUN_Log(LOG_DEBUG, "BeforeTTLCheckDirSize is %ld\n", curDirSize);
    AW_MACRO_ASSERT_NE_INT(0, curDirSize);
    int64_t beforeTTLCheckDirSize = curDirSize;

    // 对存在ttl的表新增ttl属性并设置为interval整数倍4 year，并预期能够生效
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '4 year');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待15s确保ttlCheck已经触发且数据不会过期
    sleep(15);
    curDirSize = GetDirSize(g_cStoreDir);
    AW_MACRO_EXPECT_EQ_INT(beforeTTLCheckDirSize, curDirSize);
    AW_FUN_Log(LOG_DEBUG, "AfterTTLCheckDirSize is %ld, TTLCheck is worked.\n", curDirSize);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 072.对存在ttl的表新增ttl属性并设置为0后再设置为interval整数倍，并预期能够生效，预期为0不删除数据，其它值时数据被删除
TEST_F(tsdb_alter_ttl, Timing_079_072)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    // 建表
    ret = CreateTableToTtl(stmt, tabelName, compressModeNo, oneYear, oneYear);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在3year以内
    int64_t startTime = time_T - 24 * 60 * 60 * 30 * 12 * 3;
    int64_t curDirSize = InsertDataToTTLTbale(stmt, tabelName, startTime, 2);
    AW_FUN_Log(LOG_DEBUG, "BeforeTTLCheckDirSize is %ld\n", curDirSize);
    AW_MACRO_ASSERT_NE_INT(0, curDirSize);
    int64_t beforeTTLCheckDirSize = curDirSize;

    // 对存在ttl的表新增ttl属性并设置为0，并预期能够生效
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '0');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待15s确保ttlCheck已经触发且数据不会过期
    sleep(15);
    curDirSize = GetDirSize(g_cStoreDir);
    AW_MACRO_EXPECT_EQ_INT(beforeTTLCheckDirSize, curDirSize);
    AW_FUN_Log(LOG_DEBUG, "AfterTTLCheckDirSize1 is %ld, TTLCheck is worked.\n", curDirSize);

    // 再次设置为1year，并预期能够生效
    char sqlAlterCmd1[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '1 year');";
    ret = GmcExecDirect(stmt, sqlAlterCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(15);
    curDirSize = GetDirSize(g_cStoreDir);
    AW_MACRO_EXPECT_EQ_INT(0, curDirSize);
    AW_MACRO_ASSERT_NE_INT(0, (beforeTTLCheckDirSize - curDirSize));
    AW_FUN_Log(LOG_DEBUG, "AfterTTLCheckDirSize2 is %ld, TTLCheck is worked.\n", curDirSize);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 073.对不存在ttl属性的表设置完ttl属性重启后属性还能生效
TEST_F(tsdb_alter_ttl, Timing_079_073)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    char sysCmd[400] = {0};
    char newDataDir[256] = {0};
    char *currentDir = getenv("PWD");
    (void)sprintf(newDataDir, "%s/newData", currentDir);
    char sqlCmd[CMD_LENGTH];
    AddWhiteList(GMERR_CONNECTION_RESET_BY_PEER);
    // 建表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, prop1 integer, prop2 integer, prop3 integer, prop4 integer, "
        "prop5 integer, prop6 integer, prop7 integer, prop8 integer, prop9 integer, prop10 integer,"
        " prop11 integer, prop12 integer, prop13 integer, prop14 integer, prop15 integer, prop16 integer, "
        "prop17  integer,  prop18 integer, prop19 integer, prop20 integer, prop21 integer, prop22 integer, "
        "prop23 integer, prop24  integer,  prop25 integer, prop26 integer, prop27 integer, prop28 integer, "
        "prop29 integer, prop30 integer, prop31  integer,  prop32 integer, "
        "prop33 integer) with (time_col = 'time', interval = '%s', compression "
        "= '%s');",
        tabelName, oneYear, compressModeNo);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在3year以内
    int64_t startTime = time_T - 24 * 60 * 60 * 30 * 12 * 3;
    int64_t curDirSize = InsertDataToTTLTbale(stmt, tabelName, startTime, 2);
    AW_FUN_Log(LOG_DEBUG, "BeforeTTLCheckDirSize is %ld\n", curDirSize);
    AW_MACRO_ASSERT_NE_INT(0, curDirSize);
    int64_t beforeTTLCheckDirSize = curDirSize;

    // 对不存在ttl属性的表设置完ttl属性重启后属性还能生效
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '1 year');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 备份目录
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcFlushDataBackup(stmt, newDataDir, false, GMC_DATABASE_BACKUP_SCHEMA));
    // 删旧目录下时序表
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, (char *)"testdb"));
    // 杀掉服务
    system("sh $TEST_HOME/tools/stop.sh -ts");
    // 切换起服务地址
    (void)sprintf(sysCmd, "sh $TEST_HOME/tools/modifyCfg.sh -ts \"dataFileDirPath=%s\"", newDataDir);
    system(sysCmd);
    // 使用新目录重启服务
    system("sh $TEST_HOME/tools/start.sh -ts");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestTsGmcConnect(&conn, &stmt, 0));

    // 等待15s确保ttlCheck已经触发且数据不会过期
    sleep(15);
    curDirSize = GetDirSize(g_cStoreDir);
    AW_MACRO_EXPECT_EQ_INT(0, curDirSize);
    AW_MACRO_ASSERT_NE_INT(0, (beforeTTLCheckDirSize - curDirSize));
    AW_FUN_Log(LOG_DEBUG, "AfterTTLCheckDirSize is %ld, TTLCheck is worked.\n", curDirSize);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 清理环境
    (void)sprintf(sysCmd, "rm -rf %s", newDataDir);
    system(sysCmd);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 074.对存在ttl属性的表设置完ttl属性重启后属性还能生效
TEST_F(tsdb_alter_ttl, Timing_079_074)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    char sysCmd[400] = {0};
    char newDataDir[256] = {0};
    char *currentDir = getenv("PWD");
    (void)sprintf(newDataDir, "%s/newData", currentDir);
    AddWhiteList(GMERR_CONNECTION_RESET_BY_PEER);
    // 建表
    ret = CreateTableToTtl(stmt, tabelName, compressModeNo, oneYear, oneYear);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在3year以内
    int64_t startTime = time_T - 24 * 60 * 60 * 30 * 12 * 3;
    int64_t curDirSize = InsertDataToTTLTbale(stmt, tabelName, startTime, 2);
    AW_FUN_Log(LOG_DEBUG, "BeforeTTLCheckDirSize is %ld\n", curDirSize);
    AW_MACRO_ASSERT_NE_INT(0, curDirSize);
    int64_t beforeTTLCheckDirSize = curDirSize;

    // 对存在ttl属性的表设置完ttl属性重启后属性还能生效
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '1 year');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 备份目录
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcFlushDataBackup(stmt, newDataDir, false, GMC_DATABASE_BACKUP_SCHEMA));
    // 删旧目录下时序表
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, (char *)"testdb"));
    // 杀掉服务
    system("sh $TEST_HOME/tools/stop.sh -ts");
    // 切换起服务地址
    (void)sprintf(sysCmd, "sh $TEST_HOME/tools/modifyCfg.sh -ts \"dataFileDirPath=%s\"", newDataDir);
    system(sysCmd);
    // 使用新目录重启服务
    system("sh $TEST_HOME/tools/start.sh -ts");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestTsGmcConnect(&conn, &stmt, 0));

    // 等待15s确保ttlCheck已经触发且数据不会过期
    sleep(15);
    curDirSize = GetDirSize(g_cStoreDir);
    AW_MACRO_EXPECT_EQ_INT(0, curDirSize);
    AW_MACRO_ASSERT_NE_INT(0, (beforeTTLCheckDirSize - curDirSize));
    AW_FUN_Log(LOG_DEBUG, "AfterTTLCheckDirSize is %ld, TTLCheck is worked.\n", curDirSize);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 清理环境
    (void)sprintf(sysCmd, "rm -rf %s", newDataDir);
    system(sysCmd);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 075.不存在ttl属性的表 disk_limit 为1tb，ttl 为1H预期设置能够生效
TEST_F(tsdb_alter_ttl, Timing_079_075)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    // 建表
    char sqlCmd[CMD_LENGTH];
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, description text, message blob(160),  id1 "
        "integer, time1 integer) with (time_col = 'time', interval = '1 H');",
        "testdb");
    ret = GmcExecDirect(stmt, sqlCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t count = 10000;
    ret = insertOrderData(stmt, (char *)"testdb", count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GetDiskLimitCurrentValue((char *)diskLimitValue);

    // 不存在ttl属性的表 disk_limit 为1tb，ttl 为1H预期设置能够生效
    char sqlAlterCmd1[CMD_LENGTH] = "ALTER TABLE testdb  SET (disk_limit = '1 tb', ttl = '1 H');";
    ret = GmcExecDirect(stmt, sqlAlterCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取磁盘大小
    GetDiskLimitCurrentValue((char *)diskLimitValue1);

    sleep(15);
    char sqlCmd1[CMD_LENGTH];
    (void)sprintf(
        sqlCmd1, "select iD, time, ip, name, description, id1, time1, message from %s order by id", (char *)"testdb");
    ret = GmcExecDirect(stmt, sqlCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, 0);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 076.不存在ttl属性的表 ttl 为1year。disk_limit 为4KB预期设置能够生效
TEST_F(tsdb_alter_ttl, Timing_079_076)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    char sysCmd[400] = {0};
    char newDataDir[256] = {0};
    char *currentDir = getenv("PWD");
    (void)sprintf(newDataDir, "%s/newData", currentDir);
    char sqlCmd[CMD_LENGTH];
    // 建表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, prop1 integer, prop2 integer, prop3 integer, prop4 integer, "
        "prop5 integer, prop6 integer, prop7 integer, prop8 integer, prop9 integer, prop10 integer,"
        " prop11 integer, prop12 integer, prop13 integer, prop14 integer, prop15 integer, prop16 integer, "
        "prop17  integer,  prop18 integer, prop19 integer, prop20 integer, prop21 integer, prop22 integer, "
        "prop23 integer, prop24  integer,  prop25 integer, prop26 integer, prop27 integer, prop28 integer, "
        "prop29 integer, prop30 integer, prop31  integer,  prop32 integer, "
        "prop33 integer) with (time_col = 'time', interval = '%s', compression "
        "= '%s');",
        tabelName, oneYear, compressModeNo);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1year以内
    int64_t startTime = time_T - 24 * 60 * 60 * 30 * 12;
    int64_t curDirSize = InsertDataToTTLTbale(stmt, tabelName, startTime, 2);
    AW_FUN_Log(LOG_DEBUG, "BeforeTTLCheckDirSize is %ld\n", curDirSize);
    AW_MACRO_ASSERT_NE_INT(0, curDirSize);
    int64_t beforeTTLCheckDirSize = curDirSize;

    GetDiskLimitCurrentValue((char *)diskLimitValue);

    // 不存在ttl属性的表 disk_limit 为1tb，ttl 为1H预期设置能够生效
    char sqlAlterCmd1[CMD_LENGTH] = "ALTER TABLE testdb  SET (disk_limit = '4 kb', ttl = '1 year');";
    ret = GmcExecDirect(stmt, sqlAlterCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取磁盘大小
    GetDiskLimitCurrentValue((char *)diskLimitValue2);

    // 等待15s确保ttlCheck已经触发且数据会过期
    sleep(15);
    curDirSize = GetDirSize(g_cStoreDir);
    AW_MACRO_EXPECT_EQ_INT(0, curDirSize);
    AW_MACRO_ASSERT_NE_INT(0, (beforeTTLCheckDirSize - curDirSize));
    AW_FUN_Log(LOG_DEBUG, "AfterTTLCheckDirSize is %ld, TTLCheck is worked.\n", curDirSize);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 077.存在ttl属性的表 ttl 为1year。disk_limit 为4KB预期设置能够生效
TEST_F(tsdb_alter_ttl, Timing_079_077)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    // 建表
    ret = CreateTableToTtl(stmt, tabelName, compressModeNo, oneYear, "10 year");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在半year以内
    int64_t startTime = time_T - 24 * 60 * 60 * 30 * 12 * 3;
    int64_t curDirSize = InsertDataToTTLTbale(stmt, tabelName, startTime, 2);
    AW_FUN_Log(LOG_DEBUG, "BeforeTTLCheckDirSize is %ld\n", curDirSize);
    AW_MACRO_ASSERT_NE_INT(0, curDirSize);
    int64_t beforeTTLCheckDirSize = curDirSize;

    GetDiskLimitCurrentValue((char *)diskLimitValue);

    // 存在ttl属性的表 ttl 为1year。disk_limit 为4KB预期设置能够生效
    char sqlAlterCmd1[CMD_LENGTH] = "ALTER TABLE testdb  SET (disk_limit = '40 mb', ttl = '1 year');";
    ret = GmcExecDirect(stmt, sqlAlterCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取磁盘大小
    GetDiskLimitCurrentValue((char *)(char *)"DISK_LIMIT: 41943040");

    sleep(15);
    curDirSize = GetDirSize(g_cStoreDir);
    AW_MACRO_EXPECT_EQ_INT(0, curDirSize);
    AW_FUN_Log(LOG_DEBUG, "AfterTTLCheckDirSize1 is %ld, TTLCheck is worked.\n", curDirSize);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 078.存在ttl属性的表 disk_limit 为1tb，ttl 为1H预期设置能够生效
TEST_F(tsdb_alter_ttl, Timing_079_078)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    // 建表
    ret = CreateTableToTtl(stmt, tabelName, compressModeNo, oneHour, "10 H");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在5H以内
    int64_t startTime = time_T - 5 * 60 * 60;
    int64_t curDirSize = InsertDataToTTLTbale(stmt, tabelName, startTime, 2);
    AW_FUN_Log(LOG_DEBUG, "BeforeTTLCheckDirSize is %ld\n", curDirSize);
    AW_MACRO_ASSERT_NE_INT(0, curDirSize);
    int64_t beforeTTLCheckDirSize = curDirSize;

    GetDiskLimitCurrentValue((char *)diskLimitValue);

    // 存在ttl属性的表 disk_limit 为1tb，ttl 为1H预期设置能够生效
    char sqlAlterCmd1[CMD_LENGTH] = "ALTER TABLE testdb  SET (disk_limit = '1 tb', ttl = '1 H');";
    ret = GmcExecDirect(stmt, sqlAlterCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取磁盘大小
    GetDiskLimitCurrentValue((char *)diskLimitValue1);

    sleep(15);
    curDirSize = GetDirSize(g_cStoreDir);
    AW_MACRO_EXPECT_EQ_INT(0, curDirSize);
    AW_FUN_Log(LOG_DEBUG, "AfterTTLCheckDirSize1 is %ld, TTLCheck is worked.\n", curDirSize);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 079.存在ttl属性的表 disk_limit 为0，ttl 为0
TEST_F(tsdb_alter_ttl, Timing_079_079)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    // 建表
    ret = CreateTableToTtl(stmt, tabelName, compressModeNo, oneHour, "1 H");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在5H以内
    int64_t startTime = time_T - 60;
    int64_t curDirSize = InsertDataToTTLTbale(stmt, tabelName, startTime, 2);
    AW_FUN_Log(LOG_DEBUG, "BeforeTTLCheckDirSize is %ld\n", curDirSize);
    AW_MACRO_ASSERT_NE_INT(0, curDirSize);
    int64_t beforeTTLCheckDirSize = curDirSize;

    GetDiskLimitCurrentValue((char *)diskLimitValue);

    // 存在ttl属性的表 disk_limit 为0，ttl 为0
    char sqlAlterCmd1[CMD_LENGTH] = "ALTER TABLE testdb  SET (disk_limit = '0', ttl = '0');";
    ret = GmcExecDirect(stmt, sqlAlterCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取磁盘大小
    GetDiskLimitCurrentValue((char *)diskLimitValue);

    sleep(15);
    curDirSize = GetDirSize(g_cStoreDir);
    AW_MACRO_EXPECT_EQ_INT(beforeTTLCheckDirSize, curDirSize);
    AW_FUN_Log(LOG_DEBUG, "AfterTTLCheckDirSize1 is %ld, TTLCheck is worked.\n", curDirSize);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 080.存在ttl属性的表ttl 为0。disk_limit 为0
TEST_F(tsdb_alter_ttl, Timing_079_080)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    // 建表
    ret = CreateTableToTtl(stmt, tabelName, compressModeNo, oneHour, "1 H");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在5H以内
    int64_t startTime = time_T - 60;
    int64_t curDirSize = InsertDataToTTLTbale(stmt, tabelName, startTime, 2);
    AW_FUN_Log(LOG_DEBUG, "BeforeTTLCheckDirSize is %ld\n", curDirSize);
    AW_MACRO_ASSERT_NE_INT(0, curDirSize);
    int64_t beforeTTLCheckDirSize = curDirSize;

    GetDiskLimitCurrentValue((char *)diskLimitValue);

    // 存在ttl属性的表ttl 为0。disk_limit 为0
    char sqlAlterCmd1[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '0', disk_limit = '0');";
    ret = GmcExecDirect(stmt, sqlAlterCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取磁盘大小
    GetDiskLimitCurrentValue((char *)diskLimitValue);

    sleep(15);
    curDirSize = GetDirSize(g_cStoreDir);
    AW_MACRO_EXPECT_EQ_INT(beforeTTLCheckDirSize, curDirSize);
    AW_FUN_Log(LOG_DEBUG, "AfterTTLCheckDirSize1 is %ld, TTLCheck is worked.\n", curDirSize);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 081.不存在ttl属性的表 disk_limit 为0，ttl 为0
TEST_F(tsdb_alter_ttl, Timing_079_081)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    char sysCmd[400] = {0};
    char newDataDir[256] = {0};
    char *currentDir = getenv("PWD");
    (void)sprintf(newDataDir, "%s/newData", currentDir);
    char sqlCmd[CMD_LENGTH];
    // 建表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, prop1 integer, prop2 integer, prop3 integer, prop4 integer, "
        "prop5 integer, prop6 integer, prop7 integer, prop8 integer, prop9 integer, prop10 integer,"
        " prop11 integer, prop12 integer, prop13 integer, prop14 integer, prop15 integer, prop16 integer, "
        "prop17  integer,  prop18 integer, prop19 integer, prop20 integer, prop21 integer, prop22 integer, "
        "prop23 integer, prop24  integer,  prop25 integer, prop26 integer, prop27 integer, prop28 integer, "
        "prop29 integer, prop30 integer, prop31  integer,  prop32 integer, "
        "prop33 integer) with (time_col = 'time', interval = '%s', compression "
        "= '%s');",
        tabelName, oneYear, compressModeNo);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1year以内
    int64_t startTime = time_T - 24 * 60 * 60 * 30 * 12;
    int64_t curDirSize = InsertDataToTTLTbale(stmt, tabelName, startTime, 2);
    AW_FUN_Log(LOG_DEBUG, "BeforeTTLCheckDirSize is %ld\n", curDirSize);
    AW_MACRO_ASSERT_NE_INT(0, curDirSize);
    int64_t beforeTTLCheckDirSize = curDirSize;

    GetDiskLimitCurrentValue((char *)(char *)"DISK_LIMIT: 0");

    // 不存在ttl属性的表 disk_limit 为0，ttl 为0
    char sqlAlterCmd1[CMD_LENGTH] = "ALTER TABLE testdb  SET (disk_limit = '0', ttl = '0');";
    ret = GmcExecDirect(stmt, sqlAlterCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取磁盘大小
    GetDiskLimitCurrentValue((char *)diskLimitValue);

    // 等待15s确保ttlCheck已经触发且数据不会过期
    sleep(15);
    curDirSize = GetDirSize(g_cStoreDir);
    AW_MACRO_EXPECT_EQ_INT(beforeTTLCheckDirSize, curDirSize);
    AW_FUN_Log(LOG_DEBUG, "AfterTTLCheckDirSize is %ld, TTLCheck is worked.\n", curDirSize);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 082.不存在ttl属性的表ttl 为0。disk_limit 为0
TEST_F(tsdb_alter_ttl, Timing_079_082)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    char sysCmd[400] = {0};
    char newDataDir[256] = {0};
    char *currentDir = getenv("PWD");
    (void)sprintf(newDataDir, "%s/newData", currentDir);
    char sqlCmd[1024];
    // 建表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, prop1 integer, prop2 integer, prop3 integer, prop4 integer, "
        "prop5 integer, prop6 integer, prop7 integer, prop8 integer, prop9 integer, prop10 integer,"
        " prop11 integer, prop12 integer, prop13 integer, prop14 integer, prop15 integer, prop16 integer, "
        "prop17  integer,  prop18 integer, prop19 integer, prop20 integer, prop21 integer, prop22 integer, "
        "prop23 integer, prop24  integer,  prop25 integer, prop26 integer, prop27 integer, prop28 integer, "
        "prop29 integer, prop30 integer, prop31  integer,  prop32 integer, "
        "prop33 integer) with (time_col = 'time', interval = '%s', compression "
        "= '%s');",
        tabelName, oneYear, compressModeNo);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1year以内
    int64_t startTime = time_T - 24 * 60 * 60 * 30 * 12;
    int64_t curDirSize = InsertDataToTTLTbale(stmt, tabelName, startTime, 2);
    AW_FUN_Log(LOG_DEBUG, "BeforeTTLCheckDirSize is %ld\n", curDirSize);
    AW_MACRO_ASSERT_NE_INT(0, curDirSize);
    int64_t beforeTTLCheckDirSize = curDirSize;

    GetDiskLimitCurrentValue((char *)diskLimitValue);

    // 不存在ttl属性的表 disk_limit 为0，ttl 为0
    char sqlAlterCmd1[CMD_LENGTH] = "ALTER TABLE testdb  SET ( ttl = '0', disk_limit = '0');";
    ret = GmcExecDirect(stmt, sqlAlterCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取磁盘大小
    GetDiskLimitCurrentValue((char *)diskLimitValue);

    // 等待15s确保ttlCheck已经触发且数据不会过期
    sleep(15);
    curDirSize = GetDirSize(g_cStoreDir);
    AW_MACRO_EXPECT_EQ_INT(beforeTTLCheckDirSize, curDirSize);
    AW_FUN_Log(LOG_DEBUG, "AfterTTLCheckDirSize is %ld, TTLCheck is worked.\n", curDirSize);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 083.对不同的表设置不同ttl属性均能正常生效，5张表（1H,1day，1month，1year,0）
TEST_F(tsdb_alter_ttl, Timing_079_083)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    char tabelName1[] = "testdb1";
    char tabelName2[] = "testdb2";
    char tabelName3[] = "testdb3";
    char tabelName4[] = "testdb4";
    // 建表
    ret = CreateTableToTtl(stmt, tabelName, compressModeNo, oneYear, oneYear);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = CreateTableToTtl(stmt, tabelName1, compressModeNo, oneMonth, oneMonth);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = CreateTableToTtl(stmt, tabelName2, compressModeNo, oneDay, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = CreateTableToTtl(stmt, tabelName3, compressModeNo, oneDay, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = CreateTableToTtl(stmt, tabelName4, compressModeNo, oneHour, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    int64_t beforeTTLCheckDirSize[5];
    int64_t curDirSize[5];
    // 注入数据1day内过期
    int64_t startTime = time_T - 3600 * 24;
    curDirSize[0] = InsertDataToTTLTbale(stmt, tabelName, startTime, 2);
    curDirSize[0] += InsertDataToTTLTbale(stmt, tabelName1, startTime, 2);
    curDirSize[0] += InsertDataToTTLTbale(stmt, tabelName2, startTime, 2);
    curDirSize[0] += InsertDataToTTLTbale(stmt, tabelName3, startTime, 2);
    curDirSize[0] += InsertDataToTTLTbale(stmt, tabelName4, startTime, 2);
    AW_FUN_Log(LOG_DEBUG, "BeforeTTLCheckDirSize is %ld\n", curDirSize[0]);
    AW_MACRO_ASSERT_NE_INT(0, curDirSize[0]);
    beforeTTLCheckDirSize[0] = curDirSize[0];

    GetDiskLimitCurrentValue((char *)diskLimitValue);

    // 对不同的表设置不同ttl属性均能正常生效，5张表（1H,1day，1month，1year,0）
    char sqlAlterCmd1[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '1 year', disk_limit = '0');";
    ret = GmcExecDirect(stmt, sqlAlterCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char sqlAlterCmd2[CMD_LENGTH] = "ALTER TABLE testdb1  SET (ttl = '1 month', disk_limit = '0');";
    ret = GmcExecDirect(stmt, sqlAlterCmd2, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char sqlAlterCmd3[CMD_LENGTH] = "ALTER TABLE testdb2  SET (ttl = '1 day', disk_limit = '0');";
    ret = GmcExecDirect(stmt, sqlAlterCmd3, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char sqlAlterCmd4[CMD_LENGTH] = "ALTER TABLE testdb3  SET (ttl = '1 H', disk_limit = '0');";
    ret = GmcExecDirect(stmt, sqlAlterCmd4, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char sqlAlterCmd5[CMD_LENGTH] = "ALTER TABLE testdb4  SET (ttl = '0', disk_limit = '0');";
    ret = GmcExecDirect(stmt, sqlAlterCmd5, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取磁盘大小
    GetDiskLimitCurrentValue((char *)diskLimitValue);

    sleep(15);
    curDirSize[0] = GetDirSize(g_cStoreDir);
    AW_MACRO_EXPECT_NE_INT(beforeTTLCheckDirSize[0], curDirSize[0]);
    AW_FUN_Log(LOG_DEBUG, "AfterTTLCheckDirSize1 is %ld, TTLCheck is worked.\n", curDirSize);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 084.对不同的表设置ttl和disk_limit对不同的表设置（1H,1day，1month，1year,0），（4kb，1tb，0）（15张表）spaceMaxNum
// 表規格
TEST_F(tsdb_alter_ttl, Timing_079_084)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    char tabelName1[] = "testdb1";
    char tabelName2[] = "testdb2";
    char tabelName3[] = "testdb3";
    char tabelName4[] = "testdb4";
    char tabelName5[] = "testdb5";
    char tabelName6[] = "testdb6";
    char tabelName7[] = "testdb7";
    char tabelName8[] = "testdb8";
    char tabelName9[] = "testdb9";
    char tabelName10[] = "testdb10";
    char tabelName11[] = "testdb11";
    char tabelName12[] = "testdb12";
    char tabelName13[] = "testdb13";
    char tabelName14[] = "testdb14";
    // 建表
    ret = CreateTableToTtl(stmt, tabelName, compressModeNo, oneYear, oneYear);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = CreateTableToTtl(stmt, tabelName5, compressModeNo, oneYear, oneYear);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = CreateTableToTtl(stmt, tabelName6, compressModeNo, oneYear, oneYear);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = CreateTableToTtl(stmt, tabelName1, compressModeNo, oneMonth, oneMonth);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = CreateTableToTtl(stmt, tabelName7, compressModeNo, oneMonth, oneMonth);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = CreateTableToTtl(stmt, tabelName8, compressModeNo, oneMonth, oneMonth);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = CreateTableToTtl(stmt, tabelName2, compressModeNo, oneDay, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = CreateTableToTtl(stmt, tabelName9, compressModeNo, oneDay, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = CreateTableToTtl(stmt, tabelName10, compressModeNo, oneDay, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = CreateTableToTtl(stmt, tabelName3, compressModeNo, oneDay, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = CreateTableToTtl(stmt, tabelName11, compressModeNo, oneDay, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = CreateTableToTtl(stmt, tabelName12, compressModeNo, oneDay, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = CreateTableToTtl(stmt, tabelName4, compressModeNo, oneHour, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = CreateTableToTtl(stmt, tabelName13, compressModeNo, oneHour, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = CreateTableToTtl(stmt, tabelName14, compressModeNo, oneHour, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    int64_t beforeTTLCheckDirSize[5];
    int64_t curDirSize[5];
    // 注入数据1day内过期
    int64_t startTime = time_T - 3600 * 24;
    curDirSize[0] = InsertDataToTTLTbale(stmt, tabelName, startTime, 2);
    curDirSize[0] += InsertDataToTTLTbale(stmt, tabelName1, startTime, 2);
    curDirSize[0] += InsertDataToTTLTbale(stmt, tabelName2, startTime, 2);
    curDirSize[0] += InsertDataToTTLTbale(stmt, tabelName3, startTime, 2);
    curDirSize[0] += InsertDataToTTLTbale(stmt, tabelName4, startTime, 2);
    curDirSize[0] += InsertDataToTTLTbale(stmt, tabelName5, startTime, 2);
    curDirSize[0] += InsertDataToTTLTbale(stmt, tabelName6, startTime, 2);
    curDirSize[0] += InsertDataToTTLTbale(stmt, tabelName7, startTime, 2);
    curDirSize[0] += InsertDataToTTLTbale(stmt, tabelName8, startTime, 2);
    curDirSize[0] += InsertDataToTTLTbale(stmt, tabelName9, startTime, 2);
    curDirSize[0] += InsertDataToTTLTbale(stmt, tabelName10, startTime, 2);
    curDirSize[0] += InsertDataToTTLTbale(stmt, tabelName11, startTime, 2);
    curDirSize[0] += InsertDataToTTLTbale(stmt, tabelName12, startTime, 2);
    curDirSize[0] += InsertDataToTTLTbale(stmt, tabelName13, startTime, 2);
    curDirSize[0] += InsertDataToTTLTbale(stmt, tabelName14, startTime, 2);
    AW_FUN_Log(LOG_DEBUG, "BeforeTTLCheckDirSize is %ld\n", curDirSize[0]);
    AW_MACRO_ASSERT_NE_INT(0, curDirSize[0]);
    beforeTTLCheckDirSize[0] = curDirSize[0];

    GetDiskLimitCurrentValue((char *)diskLimitValue);

    // 对不同的表设置不同ttl属性均能正常生效，5张表（1H,1day，1month，1year,0）
    char sqlAlterCmd1[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '1 year', disk_limit = '0');";
    ret = GmcExecDirect(stmt, sqlAlterCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GetDiskLimitCurrentValue((char *)diskLimitValue, (char *)"testdb");

    (void)sprintf(sqlAlterCmd1, "ALTER TABLE testdb5  SET (ttl = '1 year', disk_limit = '1 tb');");
    ret = GmcExecDirect(stmt, sqlAlterCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GetDiskLimitCurrentValue((char *)diskLimitValue1, (char *)"testdb5");

    (void)sprintf(sqlAlterCmd1, "ALTER TABLE testdb6  SET (ttl = '1 year', disk_limit = '4 kb');");
    ret = GmcExecDirect(stmt, sqlAlterCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GetDiskLimitCurrentValue((char *)diskLimitValue2, (char *)"testdb6");

    char sqlAlterCmd2[CMD_LENGTH] = "ALTER TABLE testdb1  SET (ttl = '1 month', disk_limit = '0');";
    ret = GmcExecDirect(stmt, sqlAlterCmd2, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GetDiskLimitCurrentValue((char *)diskLimitValue, (char *)"testdb1");
    (void)sprintf(sqlAlterCmd2, "ALTER TABLE testdb7  SET (ttl = '1 year', disk_limit = '4 kb');");
    ret = GmcExecDirect(stmt, sqlAlterCmd2, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GetDiskLimitCurrentValue((char *)diskLimitValue2, (char *)"testdb7");

    (void)sprintf(sqlAlterCmd2, "ALTER TABLE testdb8  SET (ttl = '1 year', disk_limit = '1 tb');");
    ret = GmcExecDirect(stmt, sqlAlterCmd2, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GetDiskLimitCurrentValue((char *)diskLimitValue1, (char *)"testdb8");

    char sqlAlterCmd3[CMD_LENGTH] = "ALTER TABLE testdb2  SET (ttl = '1 day', disk_limit = '0');";
    ret = GmcExecDirect(stmt, sqlAlterCmd3, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GetDiskLimitCurrentValue((char *)diskLimitValue, (char *)"testdb2");
    (void)sprintf(sqlAlterCmd3, "ALTER TABLE testdb9  SET (ttl = '1 day', disk_limit = '4 kb');");
    ret = GmcExecDirect(stmt, sqlAlterCmd3, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GetDiskLimitCurrentValue((char *)diskLimitValue2, (char *)"testdb9");
    (void)sprintf(sqlAlterCmd3, "ALTER TABLE testdb10  SET (ttl = '1 day', disk_limit = '1 tb');");
    ret = GmcExecDirect(stmt, sqlAlterCmd3, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GetDiskLimitCurrentValue((char *)diskLimitValue1, (char *)"testdb10");

    char sqlAlterCmd4[CMD_LENGTH] = "ALTER TABLE testdb3  SET (ttl = '1 H', disk_limit = '0');";
    ret = GmcExecDirect(stmt, sqlAlterCmd4, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GetDiskLimitCurrentValue((char *)diskLimitValue, (char *)"testdb3");
    (void)sprintf(sqlAlterCmd4, "ALTER TABLE testdb11  SET (ttl = '1 H', disk_limit = '4 kb');");
    ret = GmcExecDirect(stmt, sqlAlterCmd4, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GetDiskLimitCurrentValue((char *)diskLimitValue2, (char *)"testdb11");

    (void)sprintf(sqlAlterCmd4, "ALTER TABLE testdb12  SET (ttl = '1 H', disk_limit = '1 tb');");
    ret = GmcExecDirect(stmt, sqlAlterCmd4, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GetDiskLimitCurrentValue((char *)diskLimitValue1, (char *)"testdb12");

    char sqlAlterCmd5[CMD_LENGTH] = "ALTER TABLE testdb4  SET (ttl = '0', disk_limit = '0');";
    ret = GmcExecDirect(stmt, sqlAlterCmd5, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GetDiskLimitCurrentValue((char *)diskLimitValue, (char *)"testdb4");
    (void)sprintf(sqlAlterCmd5, "ALTER TABLE testdb13  SET (ttl = '0', disk_limit = '1 tb');");
    ret = GmcExecDirect(stmt, sqlAlterCmd5, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GetDiskLimitCurrentValue((char *)diskLimitValue1, (char *)"testdb13");

    (void)sprintf(sqlAlterCmd5, "ALTER TABLE testdb14  SET (ttl = '0', disk_limit = '4 kb');");
    ret = GmcExecDirect(stmt, sqlAlterCmd5, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GetDiskLimitCurrentValue((char *)diskLimitValue1, (char *)"testdb14");

    // 获取磁盘大小
    GetDiskLimitCurrentValue((char *)diskLimitValue, (char *)"testdb");

    sleep(15);
    curDirSize[0] = GetDirSize(g_cStoreDir);
    AW_MACRO_EXPECT_NE_INT(beforeTTLCheckDirSize[0], curDirSize[0]);
    AW_FUN_Log(LOG_DEBUG, "AfterTTLCheckDirSize1 is %ld, TTLCheck is worked.\n", curDirSize);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 085.对同一张表循环设置ttl属性为不同值均能生效
TEST_F(tsdb_alter_ttl, Timing_079_085)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = -1;
    // 建表
    ret = CreateTableToTtl(stmt, tabelName, compressModeNo, oneYear, oneYear);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在3year以内
    int64_t startTime = time_T - 24 * 60 * 60 * 30 * 12 * 3;
    int64_t curDirSize = InsertDataToTTLTbale(stmt, tabelName, startTime, 2);
    AW_FUN_Log(LOG_DEBUG, "BeforeTTLCheckDirSize is %ld\n", curDirSize);
    AW_MACRO_ASSERT_NE_INT(0, curDirSize);
    int64_t beforeTTLCheckDirSize = curDirSize;

    // 对存在ttl的表新增ttl属性并设置为0，并预期能够生效
    char sqlAlterCmd[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '0');";
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待15s确保ttlCheck已经触发且数据不会过期
    sleep(15);
    curDirSize = GetDirSize(g_cStoreDir);
    AW_MACRO_EXPECT_EQ_INT(beforeTTLCheckDirSize, curDirSize);
    AW_FUN_Log(LOG_DEBUG, "AfterTTLCheckDirSize1 is %ld, TTLCheck is worked.\n", curDirSize);

    // 再次设置为1year，并预期能够生效
    char sqlAlterCmd1[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '1 year');";
    ret = GmcExecDirect(stmt, sqlAlterCmd1, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(15);
    curDirSize = GetDirSize(g_cStoreDir);
    AW_MACRO_EXPECT_EQ_INT(0, curDirSize);
    AW_MACRO_ASSERT_NE_INT(0, (beforeTTLCheckDirSize - curDirSize));
    AW_FUN_Log(LOG_DEBUG, "AfterTTLCheckDirSize2 is %ld, TTLCheck is worked.\n", curDirSize);
    // 对存在ttl的表新增ttl属性并设置为0，并预期能够生效
    ret = GmcExecDirect(stmt, sqlAlterCmd, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    curDirSize = InsertDataToTTLTbale(stmt, tabelName, startTime, 2);
    AW_FUN_Log(LOG_DEBUG, "BeforeTTLCheckDirSize is %ld\n", curDirSize);
    AW_MACRO_ASSERT_NE_INT(0, curDirSize);
    beforeTTLCheckDirSize = curDirSize;
    sleep(15);
    curDirSize = GetDirSize(g_cStoreDir);
    AW_MACRO_EXPECT_EQ_INT(beforeTTLCheckDirSize, curDirSize);
    AW_FUN_Log(LOG_DEBUG, "AfterTTLCheckDirSize1 is %ld, TTLCheck is worked.\n", curDirSize);

    // 再次设置为4year，并预期能够生效
    char sqlAlterCmd2[CMD_LENGTH] = "ALTER TABLE testdb  SET (ttl = '100 year');";
    ret = GmcExecDirect(stmt, sqlAlterCmd2, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(15);

    curDirSize = GetDirSize(g_cStoreDir);
    AW_MACRO_EXPECT_EQ_INT(beforeTTLCheckDirSize, curDirSize);

    // 删表
    char sqlCmdDrop[CMD_LENGTH] = "drop table testdb;";
    ret = GmcExecDirect(stmt, sqlCmdDrop, CMD_LENGTH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
