/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 【交付增强】gmsysview支持使用sql语言查系统视图
 * Author: chenbangjun
 * Create: 2024-08-08
 */
#include "gtest/gtest.h"
#include "t_rd_ts.h"

static GmcConnOptionsT *connOptions = NULL;
static GmcConnT *conn = NULL;
static GmcStmtT *stmt = NULL;
Status ret = 0;
char g_cStoreDir[64] = {0};
char g_tableName[] = "testdb";
char g_tableName2[] = "testdb1";
bool isTrue = false;
static char allActualTableName[41][50] = {"'V\\$CONFIG_PARAMETERS'", "'V\\$DB_SERVER'", "'V\\$QRY_SESSION'",
    "'V\\$COM_DYN_CTX'", "'V\\$COM_MEM_SUMMARY'", "'V\\$COM_SHMEM_CTX'", "'V\\$COM_SHMEM_GROUP'",
    "'V\\$COM_SHMEM_USAGE_STAT'", "'V\\$COM_TABLE_MEM_SUMMARY'", "'V\\$SERVER_MEMORY_OVERHEAD'",
    "'V\\$SYS_MODULE_MEM_INFO'", "'V\\$QRY_DYNMEM'", "'V\\$CST_SHMEM_INFO'", "'V\\$CATA_TABLESPACE_INFO'",
    "'V\\$CATA_GENERAL_INFO'", "'V\\$STORAGE_VERTEX_COUNT'", "'V\\$STORAGE_HASH_COLLISION_STAT'",
    "'V\\$STORAGE_HASH_CLUSTER_INDEX_STAT'", "'V\\$STORAGE_HASH_LINKLIST_INDEX_STAT'",
    "'V\\$STORAGE_INDEX_GLOBAL_STAT'", "'V\\$STORAGE_LOCK_OVERVIEW'", "'V\\$STORAGE_TRX_STAT'",
    "'V\\$QRY_TRX_MONITOR_STAT'", "'V\\$STORAGE_PERSISTENT_STAT'",
    // "'V\\$STORAGE_BTREE_INDEX_STAT'", 此视图不能连跑，单独拉出来执行
    "'V\\$STORAGE_BUFFERPOOL_STAT'", "'V\\$STORAGE_DISK_USAGE'", "'V\\$DRT_DATA_PLANE_CHANNEL_STAT'",
    "'V\\$DRT_LONG_OPERATION_STAT'", "'V\\$DRT_SCHEDULE_STAT'", 
    // 此视图不支持 "'V\\$DRT_SINGLE_WORKER_STAT'",
    "'V\\$DRT_WORKER_POOL_STAT'", "'V\\$STORAGE_UNDO_STAT'", "'V\\$STORAGE_UNDO_PURGER_INFO'",
    "'V\\$CLT_PROCESS_FLOWCTRL_INFO_LABEL'", "'V\\$CLT_PROCESS_INFO'", "'V\\$CLT_PROCESS_LABEL'",
    "'V\\$MEM_COMPACT_TASKS_STAT'", "'V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT'", "'V\\$STORAGE_SHMEM_INFO'",
    "'V\\$DRT_CONN_STAT'", "'V\\$STORAGE_SPACE_INFO'"};

char g_fdClient[512] = "ls -l /proc/`pidof tsdb_gmsysview_support_basic`/fd |wc -l";
char g_fdServer[512] = "ls -l /proc/`pidof gmserver_ts`/fd |wc -l";
int32_t fdClientBefore = 0;
int32_t fdClientAfter = 0;
int32_t fdServerBefore = 0;
int32_t fdServerAfter = 0;

int32_t GetViewFieldResultValue(const char *viewName)
{
    int maxCmdSize = 256;
    char cmdOutput[maxCmdSize];
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));

    char command[maxCmdSize];
    (void)snprintf(command, maxCmdSize, "%s", viewName);

    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        printf("popen(%s) error./n", command);
    }
    EXPECT_NE((void *)NULL, pf);
    while (NULL != fgets(cmdOutput, maxCmdSize, pf)) {
    };
    pclose(pf);

    return atoi(cmdOutput);
}

class TsdbGmsysviewSupportBasic : public testing::Test {
protected:
    static void SetUpTestCase()
    {
// euler和仿真环境清共享内存，清理原有DB文件
#if defined RUN_INDEPENDENT
    system("sh $TEST_HOME/tools/stop.sh -f");
#endif
        system("sh $TEST_HOME/tools/stop.sh -ts");
        TsDefulatDbFileClean();
        system("sh $TEST_HOME/tools/modifyCfg.sh -ts recover");
        system("sh $TEST_HOME/tools/start.sh -ts");
        ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        // 创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        fdClientBefore = GetViewFieldResultValue(g_fdClient);
        fdServerBefore = GetViewFieldResultValue(g_fdServer);
        ret = TestTsGmcConnect(&conn, &stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        GmcConnOptionsDestroy(connOptions);
        GmcFreeStmt(stmt);
        GmcDisconnect(conn);
        fdClientAfter = GetViewFieldResultValue(g_fdClient);
        // 开始写日志的时机在增量编译和非增量编译下不一样，增量编译下会多一个日志fd，冕泓建议按照两张差值小于1校验
        EXPECT_LE(fdClientAfter - fdClientBefore, 1);
        fdServerAfter = GetViewFieldResultValue(g_fdServer);
        AW_MACRO_EXPECT_EQ_INT(fdServerBefore, fdServerAfter);
        close_epoll_thread();
        testEnvClean();
        RecoverTsCiCfg();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void TsdbGmsysviewSupportBasic::SetUp()
{
    AW_CHECK_LOG_BEGIN();
}

void TsdbGmsysviewSupportBasic::TearDown()
{
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
}

int DropTable(GmcStmtT *stmt, char *tableName)
{
    int ret = 0;
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    if (ret != 0) {
        AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNDEFINED_TABLE);
    }
    return ret;
}

void InsertData(GmcStmtT *stmt, char *tableName)
{
    int ret = 0;
    constexpr int64_t count = 20;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    char ip[20][33] = {"10101040", "10101040", "98765432", "78000000", "ffaa0021", "11111111", "11111111", "33333333",
        "ff000077", "f0d0c0a0", "00000000000000000000000000000000", "ffffffffffffffffffffffffffffffff",
        "0000000000000000ffffffffffffffff", "11110000000000000000000000002345", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        "12345678909887665543212343562345", "22222222abcdefabcdefabcdefabcdef", "22222222abcdefabcdefabcdefabcdef",
        "ff0000aaaaaaaa333312324123211231", "f0d0c0a0124332543668542246548622"};
    char name[20][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "apple", "bob", "bob", "omgd", "cc", "lucy",
        "tom", "bob", "toy", "tony", "lily", "jack", "Angela", "John"};
    char message[][160] = {"0071 3520", "0010 0000", "", "3102 0021", "0000 0000", "9021 6537", "0071 3521",
        "0010 0001", "", "3102 0022", "0000 0001", "9021 6538", "0071 3522", "0010 0002", "0010 0000", "3102 0023",
        "0000 0002", "9021 6539", "0071 3523", "0010 0003"};
    char *bolbList[count];
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704067200 + i;
        bolbList[i] = message[i];
    }
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, 0, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, name, 64, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_FIXED, ip, 33, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_BYTES, bolbList, sizeof(bolbList[0]), NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

int CreateTableAndInsertData(GmcStmtT *stmt, char *tableName)
{
    int ret = 0;
    char sqlCmd[512] = {0};
    (void)DropTable(stmt, tableName);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNDEFINED_TABLE);
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(160))"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '50 MB', compression = 'fast(rapidlz)');",
        tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    RETURN_IFERR(ret);

    InsertData(stmt, tableName);
    return ret;
}

int InsertDataToTable(GmcStmtT *stmt, char *tableName, int count)
{
    int ret = 0;
    int64_t id[count];
    (void)memset_s(id, count * sizeof(int64_t), 0, count * sizeof(int64_t));
    int64_t time[count];
    (void)memset_s(time, count * sizeof(int64_t), 0, count * sizeof(int64_t));
    char name[64] = "bob";
    char ipv4[33] = "ff000077";
    char ipv6[33] = "0000000000000000ffffffffffffffff";
    char *nameList = (char *)malloc(count * 64);
    if (nameList == NULL) {
        return FAILED;
    }
    char *ipList = (char *)malloc(count * 33);
    if (ipList == NULL) {
        return FAILED;
    }

    for (int i = 0; i < count; i++) {
        id[i] = i + 100;
        time[i] = 1704068200 + i;
        if ((i * 2) < count) {
            memcpy((ipList + i * 33), (char *)ipv4, 33);
        } else {
            memcpy((ipList + i * 33), (char *)ipv6, 33);
        }
        memcpy((nameList + i * 64), (char *)name, 64);
    }
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    RETURN_IFERR(ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &count, sizeof(int));
    RETURN_IFERR(ret);
    ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, id, 0, 0);
    RETURN_IFERR(ret);
    ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, time, 0, 0);
    RETURN_IFERR(ret);
    ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_FIXED, nameList, 64, NULL);
    RETURN_IFERR(ret);
    ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_FIXED, ipList, 33, NULL);
    RETURN_IFERR(ret);
    ret = GmcExecute(stmt);
    RETURN_IFERR(ret);
    free(nameList);
    free(ipList);
    return ret;
}

// 001.gmsysview -sql 对支持的系统视图使用select * 查询 预期：查询成功，结果正确
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char fdCmd[256] = {0};
    (void)sprintf(fdCmd, "ls -l /proc/`pidof gmserver_ts`/fd |wc -l");
    int32_t fdBefore = 0;
    int32_t fdAfter = 0;
    fdBefore = GetViewFieldResultValue(fdCmd);
    char sqlCmd[512] = {0};
    int64_t count = 40;
    for (int i = 0; i < count; i++) {
        (void)sprintf(sqlCmd, "gmsysview -sql \"select * from %s\" -s %s", allActualTableName[i], g_connServerTsdb);
        ret = executeCommand(sqlCmd, "fetched all records, finish!");
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        sleep(1);
        fdAfter = GetViewFieldResultValue(fdCmd);
        AW_MACRO_EXPECT_EQ_INT(fdBefore, fdAfter);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002.gmsysview -sql 对支持的系统视图使用select * 查询 预期：查询成功，结果正确  ==========
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$STORAGE_BTREE_INDEX_STAT'\" -s %s", g_connServerTsdb);

    ret = executeCommand(sqlCmd, "fetched all records, finish!");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 此视图查询时会循环查询索引，GMERR_NO_DATA 为全部查询完成后的终止符
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_NO_DATA);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003.gmsysview -sql 对系统视图使用等值过滤 预期：查询成功，结果正确
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTableAndInsertData(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char const *viewName = "select * from 'V\\$STORAGE_DISK_USAGE' where TABLE_NAME = 'testdb' ";
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "gmsysview -sql \"%s\" -s %s", viewName, g_connServerTsdb);
    char resStr[30000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "index = 0") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "index = 1") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "TABLE_NAME: testdb") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    pclose(pResultStr);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004.gmsysview -sql 对系统视图使用不等值过滤 预期：查询成功，结果正确
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char const *viewName = "select * from 'V\\$STORAGE_DISK_USAGE' where DISK_LIMIT != '-1'";
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "gmsysview -sql \"%s\" -s %s", viewName, g_connServerTsdb);
    char resStr[30000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    pclose(pResultStr);
    AW_FUN_Log(LOG_STEP, "test end.");
}

int CheckLog(char *expect)
{
    int ret = 0;
    char command2[10240] = {0};
    ret = sprintf_s(command2, sizeof(command2), 
        "cat ./Timing_043_Basic_005.txt | grep \"%s\"", expect);
    if (ret <= 0) {
        return -1;
    }

    char buf[10240] = {0};
    FILE *pf = popen(command2, "r");
    if (pf == NULL) {
        AW_FUN_Log(LOG_STEP, "popen(%s) error./n", command2);
        return -1;
    }
    fgets(buf, 10240, pf);
    if (strlen(buf) == 0) {
        (void)pclose(pf);
        return -1;
    }

    if (pclose(pf) == -1) {
        perror("pclose fail");
    }
    pf = NULL;
    bool check = strstr(buf, expect) != NULL;
    if (check == false) {
        return -1;
    }
    return 0;
}

// 005.gmsysview -sql 对系统视图使用范围查询 预期：查询成功，结果正确
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char const *viewName = "select * from 'V\\$CONFIG_PARAMETERS' where min >= 0 and min <= 2 ";
    system("rm -rf ./Timing_043_Basic_005.txt");
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "gmsysview -sql \"%s\" -s %s >> Timing_043_Basic_005.txt", viewName, g_connServerTsdb);
    ret = system(sqlCmd);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckLog((char *)"fetched all records, finish!");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckLog((char *)"index = 0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckLog((char *)"MIN");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006.gmsysview -sql 对系统视图使用模糊查询 预期：查询成功，结果正确
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char fdCmd[256] = {0};
    (void)sprintf(fdCmd, "ls -l /proc/`pidof gmserver_ts`/fd |wc -l");
    int32_t fdBefore = 0;
    int32_t fdAfter = 0;
    fdBefore = GetViewFieldResultValue(fdCmd);
    ret = CreateTableAndInsertData(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 1, fdAfter);
    char const *viewName = "select * from 'V\\$STORAGE_DISK_USAGE' where table_name like 'testdb%'";
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "gmsysview -sql \"%s\" -s %s", viewName, g_connServerTsdb);
    char resStr[30000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "fetched all records, finish!") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    pclose(pResultStr);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 1, fdAfter);
    // DTS2024090612381,查询无数据此问题单修改成不支持此特性，避免引起误解
    ret = executeCommand(sqlCmd, "Can not exec sysview sql", "ret = 1003000");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 1, fdAfter);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_FEATURE_NOT_SUPPORTED);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore, fdAfter);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007.gmsysview -sql 对系统视图使用算术表达式 预期：查询成功，结果正确  ==========
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTableAndInsertData(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char const *viewName = "select * from 'V\\$STORAGE_DISK_USAGE' where disk_limit + disk_usage > 10";
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "gmsysview -sql \"%s\" -s %s", viewName, g_connServerTsdb);
    char resStr[30000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "TABLE_NAME: testdb") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "index = 1") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    pclose(pResultStr);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008.gmsysview -sql 对系统视图使用count(*) 预期：查询成功，结果正确
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char const *viewName = "select count(*) from 'V\\$DB_SERVER'";
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "gmsysview -sql \"%s\" -s %s", viewName, g_connServerTsdb);
    // 开发反馈，2024-09-21业务联调时提出的约束变更，只有含有record字段的系统视图不支持，其他系统视图支持投影，聚合，排序
    char resStr[50000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "COUNT(*) = 1") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    pclose(pResultStr);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009.gmsysview -sql 对系统视图投影全部列 预期：查询成功，结果正确
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char const *viewName =
        "select INDEX_PK_CACHELINE_LENGTH, INDEX_LOCALHASH_CACHELINE_LENGTH, "
        "INDEX_PK_AVG_COLLISION_RATE,INDEX_LOCALHASH_AVG_COLLISION_RATE from 'V\\$STORAGE_INDEX_GLOBAL_STAT'";
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "gmsysview -sql \"%s\" -s %s", viewName, g_connServerTsdb);
    // 开发反馈，2024-09-21业务联调时提出的约束变更，只有含有record字段的系统视图不支持，其他系统视图支持投影，聚合，排序
    char resStr[50000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "index = 0") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "INDEX_PK_CACHELINE_LENGTH: 0") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "INDEX_LOCALHASH_CACHELINE_LENGTH: 0") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "INDEX_PK_AVG_COLLISION_RATE: 0") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "INDEX_LOCALHASH_AVG_COLLISION_RATE: 0") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "index = 1") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    pclose(pResultStr);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010.gmsysview -sql 对系统视图投影部分列 预期：查询成功，结果正确
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char const *viewName = "select CONN_ID, CUR_TASK_NUM, AVG_WAIT_TIME from 'V\\$DRT_SCHEDULE_STAT'";
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "gmsysview -sql \"%s\" -s %s", viewName, g_connServerTsdb);

    // 开发反馈，2024-09-21业务联调时提出的约束变更，只有含有record字段的系统视图不支持，其他系统视图支持投影，聚合，排序
    char resStr[50000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "index = 1") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "CONN_ID") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "CUR_TASK_NUM") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "AVG_WAIT_TIME") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    pclose(pResultStr);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011.gmsysview -sql 查询系统视图时，查询列包含常量 预期：查询成功，结果正确
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char const *viewName = "select 123 from 'V\\$DB_SERVER'";
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "gmsysview -sql \"%s\" -s %s", viewName, g_connServerTsdb);

    char resStr[50000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "index = 0") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "index = 1") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "123: 123") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    pclose(pResultStr);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012.gmsysview -sql 对系统视图组合使用投影和过滤，过滤列不为投影列 预期：查询成功，结果正确
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char const *viewName = "select PROCESS_PID, INST_MEMORY_SIZE, CACHED_MEMORY_SIZE, CONN_ASYNC_CNT,"
                           "STMT_TOTAL_CNT from 'V\\$CLT_PROCESS_INFO' where CPU_CYCLE_PER_USEC > 0";
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "gmsysview -sql \"%s\" -s %s", viewName, g_connServerTsdb);

    // 开发反馈，2024-09-21业务联调时提出的约束变更，只有含有record字段的系统视图不支持，其他系统视图支持投影，聚合，排序
    char resStr[50000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    pclose(pResultStr);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013.gmsysview -sql 对系统视图组合使用不等值过滤和范围查询(验证and运算) 预期：查询成功，结果正确
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char const *viewName = "select * from 'V\\$CONFIG_PARAMETERS' where max != 1 and max < 2 and min >= 0 ";
    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "gmsysview -sql \"%s\" -s %s", viewName, g_connServerTsdb);
    char resStr[50000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "max = 1") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    pclose(pResultStr);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014.gmsysview -sql 对系统视图组合使用不等值过滤和模糊查询(验证and运算) 预期：没有数据，视图使用模糊查询时没有数据
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTableAndInsertData(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTableAndInsertData(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char const *viewName = "select * from 'V\\$STORAGE_DISK_USAGE' where table_name like 'test%' "
                           " and table_name != 'testdb'";
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "gmsysview -sql \"%s\" -s %s", viewName, g_connServerTsdb);

    char resStr[30000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "fetched all records, finish!") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "index = 0") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    pclose(pResultStr);
    // DTS2024090612381,查询无数据此问题单修改成不支持此特性，避免引起误解
    ret = executeCommand(sqlCmd, "Can not exec sysview sql", "ret = 1003000");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_FEATURE_NOT_SUPPORTED);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015.gmsysview -sql 对系统视图组合使用等值过滤和范围查询(验证or运算) 预期：查询成功，结果正确
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char const *viewName = "select * from 'V\\$COM_SHMEM_CTX' where SEGMENT_NUM = 1 or ALLOC_TIMES < 10";
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "gmsysview -sql \"%s\" -s %s", viewName, g_connServerTsdb);

    char resStr[30000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "index = 0") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "SEGMENT_NUM: 1") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    pclose(pResultStr);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016.gmsysview -sql 对系统视图组合使用等值过滤和模糊查询(验证or运算) 预期：没有数据，视图使用模糊查询时没有数据
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTableAndInsertData(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTableAndInsertData(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char const *viewName = "select * from 'V\\$STORAGE_DISK_USAGE' where table_name like 'testdb%' "
                           " or table_name = 'testdb'";
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "gmsysview -sql \"%s\" -s %s", viewName, g_connServerTsdb);

    // DTS2024090612381,查询无数据此问题单修改成不支持此特性，避免引起误解
    ret = executeCommand(sqlCmd, "Can not exec sysview sql", "ret = 1003000");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_FEATURE_NOT_SUPPORTED);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 017.gmsysview -sql 对系统视图组合使用投影和过滤,以及范围查询和模糊查询 预期：没有数据，视图使用模糊查询时没有数据
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTableAndInsertData(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTableAndInsertData(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char const *viewName = "select * from 'V\\$STORAGE_DISK_USAGE' where table_name like 'testdb%' "
                           " or disk_limit <= 52428800 ";
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "gmsysview -sql \"%s\" -s %s", viewName, g_connServerTsdb);

    // DTS2024090612381,查询无数据此问题单修改成不支持此特性，避免引起误解
    ret = executeCommand(sqlCmd, "Can not exec sysview sql", "ret = 1003000");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_FEATURE_NOT_SUPPORTED);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018.gmsysview -sql 对时序逻辑表全表查询 预期：查询成功，结果正确
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTableAndInsertData(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "gmsysview   -sql \"select * from %s\" -s %s", g_tableName, g_connServerTsdb);
    char resStr[30000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "index = 19") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "index = 20") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "ip: ff000077") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    // DTS2024120411740,适配问题单修改，下同
    isTrue = strstr(resStr, "message: 0010 0003") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);

    pclose(pResultStr);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 019.gmsysview -sql 对时序逻辑表使用等值过滤 预期：查询成功，结果正确
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTableAndInsertData(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from %s where "
        "ip = 'aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa'\" -s %s",
        g_tableName, g_connServerTsdb);

    ret = executeCommand(
        sqlCmd, "index = 0", "id: 14", "ip: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa", "fetched all records, finish!");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char resStr[30000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "index = 0") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "id: 14") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "index = 1") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    pclose(pResultStr);

    ret = executeCommand(sqlCmd, "index = 0", "fetched all records, finish!");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 020.gmsysview -sql 对时序逻辑表使用不等值过滤 预期：查询成功，结果正确
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTableAndInsertData(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from %s where "
        "name != 'bob'\" -s %s",
        g_tableName, g_connServerTsdb);

    char resStr[30000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "index = 15") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "index = 16") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "ip = ff000077") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "message = 9021 6539") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    pclose(pResultStr);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 021.gmsysview -sql 对时序逻辑表使用范围查询 预期：查询成功，结果正确
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTableAndInsertData(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from %s where "
        "id > 1 and id < 3 \" -s %s",
        g_tableName, g_connServerTsdb);

    char resStr[30000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "index = 0") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "index = 1") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "id: 2") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "ip: 98765432") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "name: bob") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "message: ") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);

    pclose(pResultStr);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 022.gmsysview -sql 对时序逻辑表使用模糊查询 预期：查询成功，结果正确
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTableAndInsertData(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from %s where "
        "name like 'bob%' \" -s %s",
        g_tableName, g_connServerTsdb);

    char resStr[30000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "index = 3") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "index = 4") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "id: 13") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "ip: 11110000000000000000000000002345") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "name: bob") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "message: 0010 0002") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);

    pclose(pResultStr);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 023.gmsysview -sql 对时序逻辑表使用算术表达式 预期：查询成功，结果正确
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTableAndInsertData(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from %s where "
        "id + time > 1704067220 \" -s %s",
        g_tableName, g_connServerTsdb);

    char resStr[30000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "index = 8") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "index = 9") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "id: 19") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "time: 1704067219") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "name: John") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "ip: f0d0c0a0124332543668542246548622") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "message: 0010 0003") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);

    pclose(pResultStr);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 024.gmsysview -sql 对时序逻辑表使用count(*) 预期：查询成功，结果正确
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTableAndInsertData(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "gmsysview -sql \"select count(*), count(ip) from %s\" -s %s", g_tableName, g_connServerTsdb);

    char resStr[30000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "index = 0") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "index = 1") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "count(*): 20") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "count(ip): 20") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);

    pclose(pResultStr);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 025.gmsysview -sql 对时序逻辑表组合使用不等值过滤和范围查询(验证and运算) 预期：查询成功，结果正确   ======
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTableAndInsertData(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from %s "
        "where id != 1 and name != 'lucy' and ip <= '11111111' \" -s %s",
        g_tableName, g_connServerTsdb);

    char resStr[30000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "index = 1") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "index = 2") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "id: 6") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "time: 1704067206") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "name: apple") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "ip: 11111111") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);

    pclose(pResultStr);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 026.gmsysview -sql 对时序逻辑表组合使用不等值过滤和模糊查询(验证and运算) 预期：查询成功，结果正确
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTableAndInsertData(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from %s "
        "where id != 2 and name like '%bob%' \" -s %s",
        g_tableName, g_connServerTsdb);

    char resStr[30000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "index = 2") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "index = 3") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "id: 13") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "time: 1704067213") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "name: bob") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "ip: 11110000000000000000000000002345") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "message: 0010 0002") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);

    pclose(pResultStr);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 027.gmsysview -sql 对时序逻辑表组合使用等值过滤和范围查询(验证or运算) 预期：查询成功，结果正确  ===========
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTableAndInsertData(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from %s "
        "where id = 2 or ip <= '10101040' \" -s %s",
        g_tableName, g_connServerTsdb);

    char resStr[30000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "index = 2") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "index = 3") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "id: 2") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "time: 1704067202") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "name: bob") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "ip: 98765432") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);

    pclose(pResultStr);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 028.gmsysview -sql 对时序逻辑表组合使用等值过滤和模糊查询(验证or运算) 预期：查询成功，结果正确
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTableAndInsertData(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from %s "
        "where id = 2 or name like '%bob%' \" -s %s",
        g_tableName, g_connServerTsdb);

    char resStr[30000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "index = 3") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "index = 4") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "id: 13") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "time: 1704067213") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "name: bob") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "ip: 11110000000000000000000000002345") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "message: 0010 0002") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "id: 2") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);

    pclose(pResultStr);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 029.gmsysview -sql 对时序逻辑表组合使用范围查询和模糊查询 预期：查询成功，结果正确
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTableAndInsertData(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from %s "
        "where id <= 2 and name like '%bob%' \" -s %s",
        g_tableName, g_connServerTsdb);

    char resStr[30000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "index = 0") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "index = 1") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "id: 2") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "time: 1704067202") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "name: bob") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "ip: 98765432") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "message: ") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);

    pclose(pResultStr);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 030.gmsysview -sql 对时序逻辑表查询时含有常量 预期：查询成功，结果正确
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTableAndInsertData(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select 123, id, ip, time,"
        " name, message from %s \" -s %s",
        g_tableName, g_connServerTsdb);

    char resStr[30000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "index = 10") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "index = 20") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "id: 19") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "time: 1704067219") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "name: John") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "ip: f0d0c0a0124332543668542246548622") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "message: 0010 0003") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "123: 123") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);

    pclose(pResultStr);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 031.gmsysview -sql 对时序逻辑表查询时使用min函数 预期：查询成功，结果正确
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTableAndInsertData(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select  min(id), ip, min(time),"
        " name from %s group by ip \" -s %s",
        g_tableName, g_connServerTsdb);

    char resStr[30000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "index = 16") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "index = 17") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "min(id): 0") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "min(time): 1704067200") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "first(name): david") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "ip: 10101040") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "min(id): 5") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "min(id): 16") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "min(id): 6") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "min(id): 17") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);

    pclose(pResultStr);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 032.gmsysview -sql 对时序逻辑表查询时使用max函数 预期：查询成功，结果正确
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTableAndInsertData(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select  max(id), ip, max(time),"
        " name from %s group by ip \" -s %s",
        g_tableName, g_connServerTsdb);

    char resStr[30000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "index = 16") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "index = 17") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "max(id): 1") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "max(time): 1704067201") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "first(name): david") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "ip: 10101040") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "max(id): 6") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "max(id): 17") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "max(id): 0") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "max(id): 5") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "max(id): 16") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);

    pclose(pResultStr);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 033.gmsysview -sql 对时序逻辑表查询时使用sum函数 预期：查询成功，结果正确
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTableAndInsertData(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select  sum(id), ip, sum(time),"
        " name from %s group by ip \" -s %s",
        g_tableName, g_connServerTsdb);

    char resStr[30000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "index = 16") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "index = 17") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "sum(id): 1") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "sum(time): 3408134401") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "first(name): david") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "ip: 10101040") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "sum(id): 11") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "sum(id): 33") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);

    pclose(pResultStr);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 034.gmsysview -sql 对时序逻辑表查询时使用sum(a+b)函数 预期：查询成功，结果正确
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTableAndInsertData(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select  sum(id+time), ip,"
        " name from %s group by ip \" -s %s",
        g_tableName, g_connServerTsdb);

    char resStr[30000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "index = 16") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "index = 17") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "sum(id+time): 3408134466") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "ip: 22222222abcdefabcdefabcdefabcdef") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "first(name): lily") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);

    pclose(pResultStr);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 035.gmsysview -sql 对时序逻辑表查询时使用last函数 预期：查询成功，结果正确
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTableAndInsertData(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select  id, time, last(ip),"
        " name from %s group by name \" -s %s",
        g_tableName, g_connServerTsdb);

    char resStr[30000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "index = 15") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "index = 16") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "first(id): 5") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "first(time): 1704067205") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "last(ip): ffffffffffffffffffffffffffffffff") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "name: lucy") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);

    pclose(pResultStr);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 036.gmsysview -sql 对时序逻辑表查询时使用length函数 预期：查询成功，结果正确
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTableAndInsertData(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    // 2024-10-22 开发约束变动，length函数不支持ip类型
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select  id, length(time),"
        " name from %s group by name \" -s %s",
        g_tableName, g_connServerTsdb);

    char resStr[30000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "index = 15") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "index = 16") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "first(id): 5") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "length(time): 10") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "name: lucy") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);

    pclose(pResultStr);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 037.gmsysview -sql 对时序逻辑表查询时使用count函数 预期：查询成功，结果正确
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTableAndInsertData(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select  count(id), count(time), count(ip),"
        " name from %s group by name \" -s %s",
        g_tableName, g_connServerTsdb);

    char resStr[30000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "index = 15") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "index = 16") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "count(id): 4") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "count(time): 4") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "count(ip): 4") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "name: bob") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);

    pclose(pResultStr);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 038.gmsysview -sql 对时序逻辑表查询时使用colset函数 预期：查询成功，结果正确
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTableAndInsertData(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    // 2024-10-22 开发约束变动，length函数不支持ip类型
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select  colset(id), count(time),"
        " name from %s group by name \" -s %s",
        g_tableName, g_connServerTsdb);

    char resStr[30000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "index = 15") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "index = 16") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    // colset(id) 返回的数据顺序可能不一致，不能用 bob对应的 2, 7, 8, 13
    isTrue = strstr(resStr, "colset(id): 15") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "count(time): 4") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "length(ip): 8") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, false);
    isTrue = strstr(resStr, "name: bob") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);

    pclose(pResultStr);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 039.gmsysview -sql 对时序逻辑表查询时使用min,max,sum函数 预期：查询成功，结果正确
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTableAndInsertData(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select  min(id), max(id), sum(time), ip,"
        " name from %s group by name \" -s %s",
        g_tableName, g_connServerTsdb);

    char resStr[30000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "index = 15") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "index = 16") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "min(id): 2") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "max(id): 13") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "sum(time): 6816268830") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "first(ip): 98765432") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "name: bob") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);

    pclose(pResultStr);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 040.gmsysview -sql 对时序逻辑表查询时使用first,last,length函数 预期：查询成功，结果正确
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char fdCmd[256] = {0};
    (void)sprintf(fdCmd, "ls -l /proc/`pidof gmserver_ts`/fd |wc -l");
    int32_t fdBefore = 0;
    int32_t fdAfter = 0;
    fdBefore = GetViewFieldResultValue(fdCmd);
    ret = CreateTableAndInsertData(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 1, fdAfter);
    char sqlCmd[512] = {0};
    // 2024-10-22 开发约束变动，length函数不支持ip类型
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select  last(id), time,"
        " name from %s group by name \" -s %s",
        g_tableName, g_connServerTsdb);

    char resStr[30000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "index = 15") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "index = 16") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "last(id): 13") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "first(time): 1704067202") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "length(ip): 8") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, false);
    isTrue = strstr(resStr, "name: bob") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);

    pclose(pResultStr);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore + 1, fdAfter);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    fdAfter = GetViewFieldResultValue(fdCmd);
    AW_MACRO_EXPECT_EQ_INT(fdBefore, fdAfter);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 041.gmsysview -sql 对时序逻辑表查询时使用count,length,colset函数 预期：查询成功，结果正确
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTableAndInsertData(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select  colset(id), count(time),"
        " name from %s group by name \" -s %s",
        g_tableName, g_connServerTsdb);

    char resStr[30000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "index = 15") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "index = 16") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    // colset(id) 返回的数据顺序可能不一致，不能用 bob对应的 2, 7, 8, 13
    isTrue = strstr(resStr, "colset(id): 15") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "count(time): 4") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "length(ip): 8") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, false);
    isTrue = strstr(resStr, "name: bob") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);

    pclose(pResultStr);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 042.gmsysview -sql 对时序逻辑表查询时使用min,max,sum(a+b),count,leng函数 预期：查询成功，结果正确
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTableAndInsertData(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select  min(id), max(id), sum(id + time), count(time),"
        " name from %s group by name \" -s %s",
        g_tableName, g_connServerTsdb);

    char resStr[30000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "index = 15") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "index = 16") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "min(id): 2") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "max(id): 13") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "sum(id + time): 6816268860") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "count(time): 4") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "length(ip): 8") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, false);
    isTrue = strstr(resStr, "name: bob") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);

    pclose(pResultStr);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 043.gmsysview -sql 对时序逻辑表使用order by语句，order by字段为int类型 预期：查询成功，结果正确
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTableAndInsertData(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select  id, time, ip, message,"
        " name from %s order by  ip, id \" -s %s",
        g_tableName, g_connServerTsdb);

    char resStr[30000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "index = 19") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "index = 20") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);

    pclose(pResultStr);
    // 通过limit限制查询条数为1来验证
    // 适配blob类型改动，此种注入方式会导致message超长导致name显示不出来,下同
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select  id, time, ip, name, message"
        "  from %s order by ip, id limit 1\" -s %s",
        g_tableName, g_connServerTsdb);
    char resStr1[30000] = {0};
    FILE *pResultStr1 = NULL;
    pResultStr1 = popen(sqlCmd, "r");
    fread(resStr1, 1, sizeof(resStr1), pResultStr1);

    isTrue = strstr(resStr1, "index = 0") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr1, "index = 1") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr1, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr1, "id: 10") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr1, "time: 1704067210") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr1, "ip: 00000000000000000000000000000000") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr1, "message: 0000 0001") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr1, "name: cc") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);

    pclose(pResultStr1);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 044.gmsysview -sql 对时序逻辑表使用order by语句，order by字段为字符串类型 预期：查询成功，结果正确
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTableAndInsertData(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[1024] = {0};
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select  id, time, ip, name, message"
        " from %s order by name, id \" -s %s",
        g_tableName, g_connServerTsdb);

    char resStr[30000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "index = 19") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "index = 20") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    pclose(pResultStr);
    // 通过limit限制查询条数为1来验证
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select  id, time, ip, name, message"
        "  from %s order by name, id limit 1\" -s %s",
        g_tableName, g_connServerTsdb);

    char resStr1[30000] = {0};
    FILE *pResultStr1 = NULL;
    pResultStr1 = popen(sqlCmd, "r");
    fread(resStr1, 1, sizeof(resStr1), pResultStr1);
    isTrue = strstr(resStr1, "index = 0") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr1, "index = 1") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr1, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr1, "id: 18") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr1, "time: 1704067218") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr1, "ip: ff0000aaaaaaaa333312324123211231") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr1, "message: 0071 3523") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr1, "name: Angela") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 045.gmsysview -sql 对时序逻辑表使用order by语句，order by字段为整型，字符串类型 预期：查询成功，结果正确
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTableAndInsertData(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select  id, time, ip, message,"
        " name from %s order by name, id \" -s %s",
        g_tableName, g_connServerTsdb);

    char resStr[30000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "index = 19") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "index = 20") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);

    pclose(pResultStr);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 046.gmsysview -sql 对时序逻辑表使用limit语句 预期：查询成功，结果正确
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTableAndInsertData(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select  id, time, ip, name, message"
        " from %s order by id, name limit 1 \" -s %s",
        g_tableName, g_connServerTsdb);

    char resStr[30000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "index = 0") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "index = 1") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "id: 0") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "time: 1704067200") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "ip: 10101040") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "message: 0010 0000") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "name: david") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);

    pclose(pResultStr);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 047.gmsysview -sql "select sql;"中select sql以 ‘ 包围 预期：查询成功，结果正确
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTableAndInsertData(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd,
        "gmsysview -sql 'select  id, time, ip, name, message"
        " from %s order by id limit 3 ' -s %s",
        g_tableName, g_connServerTsdb);

    char resStr[30000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "index = 2") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "index = 3") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "id: 2") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "time: 1704067202") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "ip: 98765432") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "message: 3102 0021") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "name: bob") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "id: 3") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);

    pclose(pResultStr);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 048.gmsysview -sql 查询空时序逻辑表 预期：查询成功，结果正确
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DropTable(stmt, g_tableName);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(160))"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '50 MB', compression = 'fast(rapidlz)');",
        g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)sprintf(sqlCmd,
        "gmsysview -sql 'select  id, time, ip, message,"
        " name from %s order by id limit 3 ' -s %s",
        g_tableName, g_connServerTsdb);

    char resStr[30000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "index = 0") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);

    pclose(pResultStr);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 049.gmsysview -sql 查询含有1001条数据的时序逻辑表 预期：查询成功，结果正确
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DropTable(stmt, g_tableName);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(160))"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '50 MB', compression = 'fast(rapidlz)');",
        g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t count = 1001;
    ret = InsertDataToTable(stmt, g_tableName, count);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)sprintf(sqlCmd, "gmsysview -sql 'select id from %s order by id' -s %s", g_tableName, g_connServerTsdb);

    char resStr[40000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "index = 999") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "index = 1000") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "ts sysview fetched records max num: 1000") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);

    pclose(pResultStr);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 050.gmsysview -sql 查询含有1000条数据的时序逻辑表 预期：查询成功，结果正确   =============
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    DropTable(stmt, g_tableName);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(160))"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '50 MB', compression = 'fast(rapidlz)');",
        g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int64_t count = 1000;
    ret = InsertDataToTable(stmt, g_tableName, count);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)sprintf(sqlCmd, "gmsysview -sql 'select id from %s order by id' -s %s", g_tableName, g_connServerTsdb);

    char resStr[30000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "index = 999") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "index = 1000") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);

    pclose(pResultStr);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 051.对时序逻辑表alter 添加列后，gmsysview -sql 查询全表 预期：查询成功，结果正确
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTableAndInsertData(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "alter table testdb add num integer;");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)sprintf(sqlCmd, "alter table testdb add ip1 inet;");
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd3[512] = {0};
    (void)sprintf(sqlCmd3,
        "gmsysview -sql \"select id, time, ip, name, ip1, num, message"
        " from %s order by id \" -s %s",
        g_tableName, g_connServerTsdb);

    char resStr[30000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd3, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "index = 19") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "index = 20") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "id: 19") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "time: 1704067219") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "ip: f0d0c0a0124332543668542246548622") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "message: 0010 0003") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "name: John") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "num: 0") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "ip1: ") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);

    pclose(pResultStr);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 052.gmsysview -sql 对时序逻辑表组合使用order by语句和limit语句,
// 其中order by 字段有多个且包含int和字符串类型 预期：查询成功，结果正确
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = CreateTableAndInsertData(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[512] = {0};
    // limit5来验证功能正确性
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from %s "
        "order by name, ip, id limit 5 \" -s %s",
        g_tableName, g_connServerTsdb);

    char resStr[30000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "index = 4") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "index = 5") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);

    pclose(pResultStr);
    // 验证数据正确性
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from %s where name != 'Angela' "
        "and name != 'John' and name != 'apple' order by name, ip, id limit 1 \" -s %s",
        g_tableName, g_connServerTsdb);

    char resStr1[30000] = {0};
    FILE *pResultStr1 = NULL;
    pResultStr1 = popen(sqlCmd, "r");
    fread(resStr1, 1, sizeof(resStr1), pResultStr1);
    isTrue = strstr(resStr1, "index = 0") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr1, "index = 1") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr1, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr1, "id: 13") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr1, "time: 1704067213") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr1, "ip: 11110000000000000000000000002345") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr1, "message: 0010 0002") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr1, "name: bob") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);

    pclose(pResultStr1);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 053.gmsysview -sql 对时序逻辑表,insert into 执行前后分别查询(目标表) 预期：查询成功，结果正确
TEST_F(TsdbGmsysviewSupportBasic, Timing_043_Basic_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建立一张空表然后向表中插入数据
    (void)DropTable(stmt, g_tableName);
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(160))"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '50 MB', compression = 'fast(rapidlz)');",
        g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateTableAndInsertData(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 执行insert into前查询
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from %s "
        "order by name, ip, id limit 5 \" -s %s",
        g_tableName, g_connServerTsdb);

    char resStr[3000] = {0};
    FILE *pResultStr = NULL;
    pResultStr = popen(sqlCmd, "r");
    fread(resStr, 1, sizeof(resStr), pResultStr);
    isTrue = strstr(resStr, "index = 0") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    pclose(pResultStr);

    // 执行insert into语句
    (void)sprintf(sqlCmd, "insert into %s select * from %s;", g_tableName, g_tableName2);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 执行insert into后查询
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from %s "
        "order by name, ip, id\" -s %s",
        g_tableName, g_connServerTsdb);

    char resStr1[30000] = {0};
    FILE *pResultStr1 = NULL;
    pResultStr1 = popen(sqlCmd, "r");
    fread(resStr1, 1, sizeof(resStr1), pResultStr1);
    isTrue = strstr(resStr1, "index = 19") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr1, "index = 20") == NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr1, "fetched all records, finish!") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr1, "id: 19") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr1, "time: 1704067219") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr1, "ip: f0d0c0a0124332543668542246548622") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);
    isTrue = strstr(resStr1, "name: John") != NULL;
    AW_MACRO_ASSERT_EQ_BOOL(isTrue, true);

    pclose(pResultStr1);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, g_tableName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
