/*  版权所有 (c) 华为技术有限公司 2021-2022 */
#ifndef SHARE_FUNCTION_H
#define SHARE_FUNCTION_H

#include "t_datacom_lite.h"

#define U8_MAX  255
#define S8_MAX  127

template <typename StructObjT>
int arrycmp(const StructObjT *arry1, const StructObjT *arry2, int len)
{
    int ret = 0;
    for (int i = 0; i < len; i++) {
        if (arry1[i] > arry2[i]) {
            ret = 1;
            break;
        } else if (arry1[i] < arry2[i]) {
            ret = -1;
            break;
        }
    }

    return ret;
}

template <typename StructObjT>
int setArryByValue(StructObjT *arry, int len, uint64_t value, uint64_t max, const StructObjT *arryS = NULL)
{
    for (int i = 0; i < len; i++) {
        if (arryS) {
            arry[i] = (arryS) ? arryS[i] : 0;
        }
    }
    int index = len - 1;
    uint64_t arryTail, arryHead;
    for (int i = 0; i < len - 1; i++, index--) {
        arryHead = (value + arry[index]) / max;
        arryTail = (value + arry[index]) % max;
        arry[index] = (StructObjT)arryTail;
        if (arryHead < max) {
            arry[index-1] = (StructObjT)arryHead;
            break;
        }
    }

    return 0;
}

template <typename StructObjT>
int strToArry(StructObjT *arry, int len, const char* str, int strLen = -1)
{
    int i = 0;
    if (strLen > 0) {
        int lenT = (len > strLen) ? strLen : len;
        while (i < lenT) {
            arry[i] = (StructObjT)str[i];
            i ++;
        }
        return 0;
    }

    while ((i < len) && (str[i] != '\0')) {
        arry[i] = (StructObjT)str[i];
        i ++;
    }
    arry[len-1] = (StructObjT)'\0';
    return 0;
}

#endif
