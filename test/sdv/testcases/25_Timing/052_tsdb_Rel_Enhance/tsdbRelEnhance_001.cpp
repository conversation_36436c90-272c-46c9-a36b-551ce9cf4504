/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2024. All rights reserved.
 Description  : GMDB 505.1.0 迭代三时序可靠性增强dfx--含reocrd类型系统视图测试
 Notes        :
 History      :
 Author       : luyang/l00618033
 Create       : [2024.09.03]
*****************************************************************************/
#include "gtest/gtest.h"
#include "t_rd_ts.h"
#include "tsdbRelEnhance.h"

// 默认是push模式，修改配置项
class tsdbRelEnhance_001_test : public testing::Test {
protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}

public:
    virtual void SetUp();
    virtual void TearDown();
};

void tsdbRelEnhance_001_test::SetUp()
{
    RecoverTsCiCfg();
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("sh ${TEST_HOME}/tools/modifyCfg.sh -ts \"enableDmlPerfStat=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建epoll监听线程
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
    ret = TestTsGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建逻辑表并插入数据
    ret = CreateTableAndInsertData(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询整体数据
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s -sql \"select count(*) from %s\" -s %s;", g_tsgmsysviewName,
        g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "count(*): 20");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
}

void tsdbRelEnhance_001_test::TearDown()
{
    int ret = 0;
    // 删表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 断开连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    RecoverTsCiCfg();
    system("sh $TEST_HOME/tools/stop.sh -ts");
}

// 改成pull模式
class tsdbRelEnhance_001_test1 : public testing::Test {
protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}

public:
    virtual void SetUp();
    virtual void TearDown();
};

void tsdbRelEnhance_001_test1::SetUp()
{
    int ret = 0;
    RecoverTsCiCfg();
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("sh ${TEST_HOME}/tools/modifyCfg.sh -ts \"enableDmlPerfStat=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建epoll监听线程
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();

    ret = TestTsGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void tsdbRelEnhance_001_test1::TearDown()
{
    int ret = 0;
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    RecoverTsCiCfg();
    system("sh $TEST_HOME/tools/stop.sh -ts");
}

class tsdbRelEnhance_001_test2 : public testing::Test {
protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}

public:
    virtual void SetUp();
    virtual void TearDown();
};

void tsdbRelEnhance_001_test2::SetUp()
{
    int ret = 0;
    RecoverTsCiCfg();
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建epoll监听线程
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
    ret = TestTsGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void tsdbRelEnhance_001_test2::TearDown()
{
    int ret = 0;
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    RecoverTsCiCfg();
    system("sh $TEST_HOME/tools/stop.sh -ts");
}

/* ****************************************************************************
 Description  : 001.isPullQueryEnable配置项设置为push，创建1个逻辑表，
 查询所有含record字段类型的视图，预期查询正常
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int count = sizeof(g_allRecordTypeSysView) / sizeof(g_allRecordTypeSysView[0]);
    sleep(2);

    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "gmsysview -sql \"select * from 'V\\$CATA_LABEL_SUBS_INFO' where "
        "LABEL_NAME='tsdb_view';\" -s %s", g_connServerTsdb);
    system(g_sqlCmd);

    for (int i = 0; i < count; i++) {
        (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "gmsysview -sql \"select * from %s\" -s %s", g_allRecordTypeSysView[i],
            g_connServerTsdb);
        ret = executeCommand(g_sqlCmd, "fetched all records, finish!");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 002.isPullQueryEnable配置项设置为pull，创建1个逻辑表，
 查询所有含record字段类型的视图，预期查询正常
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test1, Timing_052_001_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int count = sizeof(g_allRecordTypeSysView) / sizeof(g_allRecordTypeSysView[0]);
    // 创建逻辑表并插入数据
    ret = CreateTableAndInsertData(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < count; i++) {
        (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "gmsysview -sql \"select * from %s\" -s %s", g_allRecordTypeSysView[i],
            g_connServerTsdb);
        ret = executeCommand(g_sqlCmd, "fetched all records, finish!");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 删表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 003.查询yang表相关视图，预期报错
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    int count = sizeof(g_yangSysView) / sizeof(g_yangSysView[0]);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "[WARN] Can not exec sysview sql, ret = %d", GMERR_FEATURE_NOT_SUPPORTED);
    for (int i = 0; i < count; i++) {
        (void)snprintf(
            g_sqlCmd, MAX_CMD_SIZE, "gmsysview -sql \"select * from %s\" -s %s", g_yangSysView[i], g_connServerTsdb);
        ret = executeCommand(g_sqlCmd, g_errorMsg);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret != GMERR_OK) {
            system(g_sqlCmd);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 004.查询datalog表相关视图，预期报错
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    int count = sizeof(g_datalogSysView) / sizeof(g_datalogSysView[0]);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "[WARN] Can not exec sysview sql, ret = %d", GMERR_FEATURE_NOT_SUPPORTED);
    for (int i = 0; i < count; i++) {
        (void)snprintf(
            g_sqlCmd, MAX_CMD_SIZE, "gmsysview -sql \"select * from %s\" -s %s", g_datalogSysView[i], g_connServerTsdb);
        ret = executeCommand(g_sqlCmd, g_errorMsg);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret != GMERR_OK) {
            system(g_sqlCmd);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 以CATA_VERTEX_LABEL_INFO视图为例，全面覆盖
/* ****************************************************************************
 Description  : 005.gmsysview -sql 对含record类型系统视图使用等值过滤（整型）
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "gmsysview -sql \"select * from %s where VERSION=0;\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    ret = executeCommand(g_sqlCmd, "VERTEX_LABEL_NAME: tsdb1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 有问题，视图中的信息待确认
/* ****************************************************************************
 Description  : 006.gmsysview -sql 对含record类型系统视图使用等值过滤（字符串）
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 过滤时序逻辑表
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "gmsysview -sql \"select * from %s where VERTEX_LABEL_NAME = '%s';\" -s %s",
        g_catalogVertexView, g_tableName, g_connServerTsdb);

    system(g_sqlCmd);
    ret = executeCommand(g_sqlCmd, "VERTEX_LABEL_NAME: tsdb1", "VERTEX_LABEL_TYPE: VERTEX_TYPE_TS_LOGICAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 007.gmsysview -sql 对含record类型系统视图使用等值过滤（布尔类型）
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "gmsysview -sql \"select * from %s where IS_DELETED = 0;\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    ret = executeCommand(g_sqlCmd, "VERTEX_LABEL_NAME: tsdb1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 008.gmsysview -sql 对含record类型系统视图使用不等值过滤（整型）
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "gmsysview -sql \"select * from %s where VERSION != 1;\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    ret = executeCommand(g_sqlCmd, "VERTEX_LABEL_NAME: tsdb1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 009.gmsysview -sql 对含record类型系统视图使用不等值过滤（字符串）
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "gmsysview -sql \"select * from %s where VERTEX_LABEL_NAME != '%s';\" -s %s",
        g_catalogVertexView, g_tableName, g_connServerTsdb);
    ret = executeCommand(g_sqlCmd, "VERTEX_LABEL_NAME: tsdb1");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    if (ret == GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 010.gmsysview -sql 对含record类型系统视图使用不等值过滤（布尔类型）
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "gmsysview -sql \"select * from %s where IS_DELETED != -1;\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    ret = executeCommand(g_sqlCmd, "VERTEX_LABEL_NAME: tsdb1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 011.gmsysview -sql 对含record类型系统视图使用范围查询（where min >= 0 and min <= 2）（整型）
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -sql \"select * from %s where VERSION >= 0 and VERSION <= 3;\" "
        "-s %s",
        g_catalogVertexView, g_connServerTsdb);
    ret = executeCommand(g_sqlCmd, "VERTEX_LABEL_NAME: tsdb1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 012.gmsysview -sql 对含record类型系统视图使用范围查询（where min >= 0 and min <= 2）（字符串）
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -sql \"select * from %s where "
        "VERTEX_LABEL_NAME > 'aaaa' and VERTEX_LABEL_NAME <= '%s';\" -s %s",
        g_catalogVertexView, g_tableName, g_connServerTsdb);
    ret = executeCommand(g_sqlCmd, "VERTEX_LABEL_NAME: tsdb1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 013.gmsysview -sql 对含record类型系统视图使用范围查询（where min >= 0 and min <= 2）（布尔类型）
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -sql \"select * from %s where IS_DELETED >= 0 and "
        "IS_DELETED < 3;\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    ret = executeCommand(g_sqlCmd, "VERTEX_LABEL_NAME: tsdb1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 014.gmsysview -sql 对含record类型系统视图使用模糊查询（整型）
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_DATATYPE_MISMATCH);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "gmsysview -sql \"select * from %s where VERSION like 0;\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "[WARN] Can not exec sysview sql, ret = %d", GMERR_DATATYPE_MISMATCH);
    ret = executeCommand(g_sqlCmd, g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 015.gmsysview -sql 对含record类型系统视图使用模糊查询（布尔型）
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_DATATYPE_MISMATCH);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "gmsysview -sql \"select * from %s where IS_DELETED like 0\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "[WARN] Can not exec sysview sql, ret = %d", GMERR_DATATYPE_MISMATCH);
    ret = executeCommand(g_sqlCmd, g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 建议代码查询报3000不支持，后续需要用例
// DTS2024090612381
/* ****************************************************************************
 Description  : 016.gmsysview -sql 对含record类型系统视图使用模糊查询（字符串）
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -sql \"select * from %s where VERTEX_LABEL_NAME like "
        "'tsdb%';\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    system(g_sqlCmd);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "[WARN] Can not exec sysview sql, ret = %d", GMERR_FEATURE_NOT_SUPPORTED);
    ret = executeCommand(g_sqlCmd, g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DTS2024090614164
/* ****************************************************************************
 Description  : 017.gmsysview -sql 对含record类型系统视图使用算术表达式（两个整型）
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_SEMANTIC_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -sql \"select * from %s where MAX_RECORD_COUNT + REF_COUNT;\" "
        "-s %s",
        g_catalogVertexView, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "[WARN] Can not exec sysview sql, ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_sqlCmd, g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 018.gmsysview -sql 对含record类型系统视图使用算术表达式（一个整型+一个bool类型）
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_SEMANTIC_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "gmsysview -sql \"select * from %s where VERSION + IS_DELETED >= 0;\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "[WARN] Can not exec sysview sql, ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_sqlCmd, g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 019.gmsysview -sql 对含record类型系统视图使用算术表达式（一个整型+一个字符串类型）
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_SEMANTIC_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -sql \"select * from %s "
        "where VERSION + VERTEX_LABEL_NAME >= 0;\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "[WARN] Can not exec sysview sql, ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_sqlCmd, g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 020.gmsysview -sql 对含record类型系统视图使用count(*)
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_SEMANTIC_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "gmsysview -sql \"select count(*) from %s;\" -s %s", g_catalogVertexView,
        g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "[WARN] Can not exec sysview sql, ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_sqlCmd, g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 021.对含record类型的系统表，使用投影查询全部列，预期查询失败
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_SEMANTIC_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -sql \"select DB_ID, NAMESPACE_ID, TABLESPACE_ID,"
        "VERTEX_LABEL_ID, VERTEX_LABEL_NAME, VERSION, REF_COUNT, IS_DELETED, MAX_RECORD_COUNT, MAX_RECORD_COUNT_CHECK,"
        "CHECK_VALIDITY, VERTEX_LABEL_LEVEL, SUBS_NUMBER, SUBS_TYPE, PARTITION_PROPERTY_ID, PARTITION_PROPERTY_OFFSET,"
        "SCHEMA_INFO, CONFIG_INFO, EDGE_LABEL_NUM, VERTEX_LABEL_TYPE, CONCURRENCY_CONTROL_TYPE, ISOLATION_LEVEL,"
        "TRX_TYPE, CONTAINER_TYPE, IS_DATA_SYNC_LABEL, ENABLE_DIRECT_WRITE, OBJ_PRIVILEGE, KEYS, CLIENT_REFCOUNT "
        "from %s;\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "[WARN] Can not exec sysview sql, ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_sqlCmd, g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 022.对含record类型的系统表，使用投影查询部分列（整型、字符串、布尔类型），预期查询失败
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_SEMANTIC_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -sql \"select VERTEX_LABEL_NAME, VERSION, IS_DELETED "
        "from %s;\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "[WARN] Can not exec sysview sql, ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_sqlCmd, g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 023.对含record类型的系统视图，使用常量和投影列查询，预期查询失败
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_SEMANTIC_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -sql \"select 111, VERTEX_LABEL_NAME, VERSION, IS_DELETED "
        "from %s;\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "[WARN] Can not exec sysview sql, ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_sqlCmd, g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 024.gmsysview -sql 对含record类型系统视图，使用投影和过滤，预期查询失败
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_SEMANTIC_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -sql \"select VERTEX_LABEL_NAME "
        "from %s where VERSION = 0;\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "[WARN] Can not exec sysview sql, ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_sqlCmd, g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 025.gmsysview -sql
对含record类型系统视图，使用不等值过滤（整型）和范围查询（整型）（and运算），预期查询成功
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -sql \"select * "
        "from %s where VERSION != 1 and REF_COUNT >= 0;\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    ret = executeCommand(g_sqlCmd, "VERTEX_LABEL_NAME: tsdb1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 026.gmsysview -sql
对含record类型系统视图，使用不等值过滤（字符串）和范围查询（整型）（and运算），预期查询成功
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -sql \"select * "
        "from %s where VERTEX_LABEL_NAME != 'tsdb2' and REF_COUNT >= 0;\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    ret = executeCommand(g_sqlCmd, "VERTEX_LABEL_NAME: tsdb1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 027.gmsysview -sql
对含record类型系统视图，使用不等值过滤（布尔型）和范围查询（整型）（and运算），预期查询成功
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -sql \"select * "
        "from %s where IS_DELETED != 1 and REF_COUNT >= 0;\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    ret = executeCommand(g_sqlCmd, "VERTEX_LABEL_NAME: tsdb1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 028.gmsysview -sql
对含record类型系统视图，使用等值过滤（整型）和范围查询（整型）（and运算），预期查询成功
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -sql \"select * "
        "from %s where VERSION = 0 and REF_COUNT >= 0;\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    ret = executeCommand(g_sqlCmd, "VERTEX_LABEL_NAME: tsdb1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 029.gmsysview -sql
对含record类型系统视图，使用等值过滤（字符串）和范围查询（整型）（and运算），预期查询成功
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -sql \"select * "
        "from %s where VERTEX_LABEL_NAME = '%s' and REF_COUNT >= 0;\" -s %s",
        g_catalogVertexView, g_tableName, g_connServerTsdb);
    ret = executeCommand(g_sqlCmd, "VERTEX_LABEL_NAME: tsdb1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 有问题，应该报不支持3000
/* ****************************************************************************
 Description  : 030.gmsysview -sql
对含record类型系统视图，使用不等值过滤（整型）和模糊查询（字符串）（and运算），预期查询失败
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -sql \"select * "
        "from %s where VERSION != 1 and VERTEX_LABEL_NAME like 'tsdb%';\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "[WARN] Can not exec sysview sql, ret = %d", GMERR_FEATURE_NOT_SUPPORTED);
    ret = executeCommand(g_sqlCmd, g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 031.gmsysview -sql
对含record类型系统视图，使用不等值过滤（字符串）和模糊查询（字符串）（and运算），预期查询失败
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -sql \"select * "
        "from %s where VERTEX_LABEL_NAME != 'tsdb1' and VERTEX_LABEL_NAME like 'tsdb%';\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "[WARN] Can not exec sysview sql, ret = %d", GMERR_FEATURE_NOT_SUPPORTED);
    ret = executeCommand(g_sqlCmd, g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 032.gmsysview -sql
对含record类型系统视图，使用等值过滤（整型）和模糊查询（字符串）（and运算），预期查询失败
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -sql \"select * "
        "from %s where VERSION = 0 and VERTEX_LABEL_NAME like 'tsdb%';\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "[WARN] Can not exec sysview sql, ret = %d", GMERR_FEATURE_NOT_SUPPORTED);
    ret = executeCommand(g_sqlCmd, g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 033.gmsysview -sql
对含record类型系统视图，使用等值过滤（字符串）和模糊查询（字符串）（and运算），预期查询失败
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -sql \"select * "
        "from %s where VERTEX_LABEL_NAME = 'tsdb1' and VERTEX_LABEL_NAME like 'tsdb%';\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "[WARN] Can not exec sysview sql, ret = %d", GMERR_FEATURE_NOT_SUPPORTED);
    ret = executeCommand(g_sqlCmd, g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 034.gmsysview -sql
对含record类型系统视图，使用不等值过滤（整型）和范围查询（整型）（or运算），预期查询成功
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -sql \"select * "
        "from %s where VERSION != 1 or REF_COUNT >= 0;\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    ret = executeCommand(g_sqlCmd, "VERTEX_LABEL_NAME: tsdb1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 035.gmsysview -sql
对含record类型系统视图，使用不等值过滤（字符串）和范围查询（整型）（or运算），预期查询成功
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -sql \"select * "
        "from %s where VERTEX_LABEL_NAME != 'tsdb2' or REF_COUNT >= 0;\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    ret = executeCommand(g_sqlCmd, "VERTEX_LABEL_NAME: tsdb1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 036.gmsysview -sql
对含record类型系统视图，使用不等值过滤（布尔型）和范围查询（整型）（or运算），预期查询成功
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -sql \"select * "
        "from %s where IS_DELETED != 1 or REF_COUNT >= 1;\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    ret = executeCommand(g_sqlCmd, "VERTEX_LABEL_NAME: tsdb1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 037.gmsysview -sql
对含record类型系统视图，使用等值过滤（整型）和范围查询（整型）（or运算），预期查询成功
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -sql \"select * "
        "from %s where VERSION = 0 or REF_COUNT < 0;\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    ret = executeCommand(g_sqlCmd, "VERTEX_LABEL_NAME: tsdb1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 038.gmsysview -sql
对含record类型系统视图，使用等值过滤（字符串）和范围查询（整型）（or运算），预期查询成功
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -sql \"select * "
        "from %s where VERTEX_LABEL_NAME = '%s' or REF_COUNT < 0;\" -s %s",
        g_catalogVertexView, g_tableName, g_connServerTsdb);
    ret = executeCommand(g_sqlCmd, "VERTEX_LABEL_NAME: tsdb1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 039.gmsysview -sql
对含record类型系统视图，使用不等值过滤（整型）和模糊查询（字符串）（or运算），预期查询失败
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -sql \"select * "
        "from %s where VERSION != 1 or VERTEX_LABEL_NAME like 'tsdb%';\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "[WARN] Can not exec sysview sql, ret = %d", GMERR_FEATURE_NOT_SUPPORTED);
    ret = executeCommand(g_sqlCmd, g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 040.gmsysview -sql
对含record类型系统视图，使用不等值过滤（字符串）和模糊查询（字符串）（or运算），预期查询成功
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -sql \"select * "
        "from %s where VERTEX_LABEL_NAME != 'tsdb1' or VERTEX_LABEL_NAME like 'tsdb%';\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "[WARN] Can not exec sysview sql, ret = %d", GMERR_FEATURE_NOT_SUPPORTED);
    ret = executeCommand(g_sqlCmd, g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 041.gmsysview -sql
对含record类型系统视图，使用等值过滤（整型）和模糊查询（字符串）（or运算），预期查询失败
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -sql \"select * "
        "from %s where VERSION = 0 or VERTEX_LABEL_NAME like 'tsdb%';\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "[WARN] Can not exec sysview sql, ret = %d", GMERR_FEATURE_NOT_SUPPORTED);
    ret = executeCommand(g_sqlCmd, g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 042.gmsysview -sql
对含record类型系统视图，使用等值过滤（字符串）和模糊查询（字符串）（or运算），预期查询失败
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -sql \"select * "
        "from %s where VERTEX_LABEL_NAME = 'tsdb1' or VERTEX_LABEL_NAME like 'tsdb%';\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "[WARN] Can not exec sysview sql, ret = %d", GMERR_FEATURE_NOT_SUPPORTED);
    ret = executeCommand(g_sqlCmd, g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 043.gmsysview -sql
对含record类型系统视图，使用范围查询（整型）和模糊查询（字符串）（and运算），预期查询失败
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -sql \"select * "
        "from %s where (VERSION >= 0 and VERSION < 2) and VERTEX_LABEL_NAME like 'tsdb%';\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "[WARN] Can not exec sysview sql, ret = %d", GMERR_FEATURE_NOT_SUPPORTED);
    ret = executeCommand(g_sqlCmd, g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 044.gmsysview -sql
对含record类型系统视图，使用范围查询（整型）和模糊查询（字符串）（or运算），预期查询失败
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -sql \"select * "
        "from %s where (VERSION >= 0 and VERSION < 2) or VERTEX_LABEL_NAME like 'tsdb%';\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "[WARN] Can not exec sysview sql, ret = %d", GMERR_FEATURE_NOT_SUPPORTED);
    ret = executeCommand(g_sqlCmd, g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 045.gmsysview -sql 对含record类型系统视图，含sum聚合函数，预期查询失败
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_SEMANTIC_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -sql \"select sum(VERSION) "
        "from %s group by VERSION;\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "[WARN] Can not exec sysview sql, ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_sqlCmd, g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 046.gmsysview -sql 对含record类型系统视图，含min聚合函数（整型），预期查询失败
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_SEMANTIC_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -sql \"select min(VERSION) "
        "from %s group by VERSION;\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "[WARN] Can not exec sysview sql, ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_sqlCmd, g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 047.gmsysview -sql 对含record类型系统视图，含min聚合函数（字符串），预期查询失败
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_SEMANTIC_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -sql \"select min(VERTEX_LABEL_NAME) "
        "from %s group by VERTEX_LABEL_NAME;\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "[WARN] Can not exec sysview sql, ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_sqlCmd, g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 048.gmsysview -sql 对含record类型系统视图，含min聚合函数（布尔型），预期查询失败
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_SEMANTIC_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -sql \"select min(IS_DELETED) "
        "from %s group by IS_DELETED;\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "[WARN] Can not exec sysview sql, ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_sqlCmd, g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 049.gmsysview -sql 对含record类型系统视图，含max聚合函数，预期查询失败
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_SEMANTIC_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -sql \"select max(IS_DELETED) "
        "from %s group by IS_DELETED;\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "[WARN] Can not exec sysview sql, ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_sqlCmd, g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 050.gmsysview -sql 对含record类型系统视图，含first和last聚合函数，预期查询失败
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_SEMANTIC_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -sql \"select first(VERTEX_LABEL_NAME), last(VERTEX_LABEL_NAME) "
        "from %s group by VERTEX_LABEL_NAME;\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "[WARN] Can not exec sysview sql, ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_sqlCmd, g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 051.gmsysview -sql 对含record类型系统视图，含colset聚合函数，预期查询失败
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_SEMANTIC_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -sql \"select colset(VERTEX_LABEL_NAME) "
        "from %s group by VERTEX_LABEL_NAME;\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "[WARN] Can not exec sysview sql, ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_sqlCmd, g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 052.gmsysview -sql 对含record类型系统视图，含tsdb_aging函数，预期查询失败
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_SEMANTIC_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -sql \"select tsdb_aging "
        "from %s;\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "[WARN] Can not exec sysview sql, ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_sqlCmd, g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 053.gmsysview -sql 对含record类型系统视图，含group by（整型）、order by及limit子句，预期查询失败
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_SEMANTIC_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -sql \"select * "
        "from %s group by VERSION order by VERSION limit 100;\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "[WARN] Can not exec sysview sql, ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_sqlCmd, g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 054.gmsysview -sql 对含record类型系统视图，含group by（字符串）、order by及limit子句，预期查询失败
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_SEMANTIC_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -sql \"select * "
        "from %s group by VERTEX_LABEL_NAME order by VERTEX_LABEL_NAME limit 100;\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "[WARN] Can not exec sysview sql, ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_sqlCmd, g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 055.gmsysview -sql 对含record类型系统视图，含length函数（整型），预期查询失败
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_SEMANTIC_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -sql \"select length(VERSION) "
        "from %s group by VERSION order by VERSION limit 100;\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "[WARN] Can not exec sysview sql, ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_sqlCmd, g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 056.gmsysview -sql 对含record类型系统视图，含length函数（字符串），预期查询失败
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_SEMANTIC_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -sql \"select length(VERTEX_LABEL_NAME) "
        "from %s group by VERTEX_LABEL_NAME order by VERTEX_LABEL_NAME limit 100;\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "[WARN] Can not exec sysview sql, ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_sqlCmd, g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 057.gmsysview -sql 对含record类型系统视图，过滤的变量类型是record类型，预期查询失败
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_SEMANTIC_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "gmsysview -sql \"select * from %s where OBJ_PRIVILEGE = '{}'\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "[WARN] Can not exec sysview sql, ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_sqlCmd, g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 058.使用copy to将查询到的含record类型系统视图的值存在csv中，通过GmcExecDirect接口，预期查询失败
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_SYNTAX_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    // 执行COPY TO
    char *dir = getenv("GMDB_HOME");
    sprintf(g_sqlCmd,
        "COPY (SELECT * from V\\$CATA_VERTEX_LABEL_INFO) TO"
        "'%s/test/sdv/testcases/25_Timing/052_tsdb_Rel_Enhance/data.csv';",
        dir);
    uint32_t cmdLen = strlen(g_sqlCmd);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 059.gmsysview -explain对含record类型所有系统视图，进行select *查询，预期查询成功
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_059)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int count = sizeof(g_allRecordTypeSysView) / sizeof(g_allRecordTypeSysView[0]);
    for (int i = 0; i < count; i++) {
        (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "gmsysview -explain \"select * from %s\" -s %s",
            g_allRecordTypeSysView[i], g_connServerTsdb);
        ret = executeCommand(g_sqlCmd, "fetched all records, finish!");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 060.gmsysview
-explain对含record类型系统视图，进行部分列投影查询（整型、字符串、布尔类型，record类型），预期查询失败
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_060)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_SEMANTIC_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;

    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -explain \"select VERTEX_LABEL_NAME, VERSION, IS_DELETED "
        "from %s;\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "[WARN] Can not exec sysview sql, ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_sqlCmd, g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 061.gmsysview -explain对含record类型系统视图，进行等值查询（整型）
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_061)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "gmsysview -explain \"select * from %s where VERSION=0;\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    ret = executeCommand(g_sqlCmd, "  ->  SeqScan on Label(V$CATA_VERTEX_LABEL_INFO)", "        Filter: VERSION = 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 062.gmsysview -explain对含record类型系统视图，进行等值查询（字符串）
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_062)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -explain \"select * from %s where VERTEX_LABEL_NAME='%s';\" "
        "-s %s",
        g_catalogVertexView, g_tableName, g_connServerTsdb);
    ret = executeCommand(
        g_sqlCmd, "  ->  SeqScan on Label(V$CATA_VERTEX_LABEL_INFO)", "        Filter: VERTEX_LABEL_NAME = \"tsdb1\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 063.gmsysview -explain对含record类型系统视图，进行等值查询（布尔类型）
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_063)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "gmsysview -explain \"select * from %s where IS_DELETED=0;\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    ret =
        executeCommand(g_sqlCmd, "  ->  SeqScan on Label(V$CATA_VERTEX_LABEL_INFO)", "        Filter: IS_DELETED = 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 064.gmsysview -explain对含record类型系统视图，进行不等值查询（整型）
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_064)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE, "gmsysview -explain \"select * from %s where VERSION!=0;\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    ret = executeCommand(g_sqlCmd, "  ->  SeqScan on Label(V$CATA_VERTEX_LABEL_INFO)", "        Filter: VERSION != 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 065.gmsysview -explain对含record类型系统视图，进行不等值查询（字符串）
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_065)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -explain \"select * from %s where VERTEX_LABEL_NAME!='%s';\" "
        "-s %s",
        g_catalogVertexView, g_tableName, g_connServerTsdb);
    ret = executeCommand(
        g_sqlCmd, "  ->  SeqScan on Label(V$CATA_VERTEX_LABEL_INFO)", "        Filter: VERTEX_LABEL_NAME != \"tsdb1\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 066.gmsysview -explain对含record类型系统视图，进行范围查询（整型）
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_066)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -explain \"select * from %s where VERSION >=0 and VERSION < 3;\" "
        "-s %s",
        g_catalogVertexView, g_connServerTsdb);
    ret = executeCommand(
        g_sqlCmd, "  ->  SeqScan on Label(V$CATA_VERTEX_LABEL_INFO)", "        Filter: VERSION >= 0 AND VERSION < 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 067.gmsysview -explain对含record类型系统视图，进行范围查询（字符串）
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_067)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -explain \"select * from %s where VERTEX_LABEL_NAME >= 'aaaa' "
        "and VERTEX_LABEL_NAME<='%s';\" -s %s",
        g_catalogVertexView, g_tableName, g_connServerTsdb);
    ret = executeCommand(g_sqlCmd, "  ->  SeqScan on Label(V$CATA_VERTEX_LABEL_INFO)",
        "        Filter: VERTEX_LABEL_NAME >= \"aaaa\" AND VERTEX_LABEL_NAME <= \"tsdb1\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 068.gmsysview -explain对含record类型系统视图，含聚合函数查询，预期查询失败
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_068)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_SEMANTIC_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
        "gmsysview -explain \"select max(IS_DELETED) "
        "from %s group by IS_DELETED;\" -s %s",
        g_catalogVertexView, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "[WARN] Can not exec sysview sql, ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_sqlCmd, g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_sqlCmd);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 069.其它含record类型的时序不支持的系统视图，使用record类型过滤，预期查询失败
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_069)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    int count = sizeof(g_unsupportSysView) / sizeof(g_unsupportSysView[0]);

    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "[WARN] Can not exec sysview sql, ret = %d", GMERR_FEATURE_NOT_SUPPORTED);
    for (int i = 0; i < count; i++) {
        (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
            "gmsysview -sql \"select * "
            "from %s where IS_DELETED = 0;\" -s %s",
            g_unsupportSysView[i], g_connServerTsdb);
        ret = executeCommand(g_sqlCmd, g_errorMsg);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret != GMERR_OK) {
            system(g_sqlCmd);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 070.其它含record类型的时序不支持系统视图，使用含聚合函数查询，预期查询失败
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_070)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    int count = sizeof(g_unsupportSysView) / sizeof(g_unsupportSysView[0]);

    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "[WARN] Can not exec sysview sql, ret = %d", GMERR_FEATURE_NOT_SUPPORTED);
    for (int i = 0; i < count; i++) {
        (void)snprintf(g_sqlCmd, MAX_CMD_SIZE,
            "gmsysview -sql \"select max(IS_DELETED) "
            "from %s group by IS_DELETED;\" -s %s",
            g_unsupportSysView[i], g_connServerTsdb);
        ret = executeCommand(g_sqlCmd, g_errorMsg);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret != GMERR_OK) {
            system(g_sqlCmd);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DTS2024091216838
/* ****************************************************************************
 Description  : 071.时序创建状态合并订阅失败，服务端有日志
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test, Timing_052_001_071)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    GmcConnT *subConn = NULL;
    GmcStmtT *subStmt = NULL;
    // 创建订阅连接，2025年4月17日，时序创建订阅连接报错(DTS2025041628630)
    ret = TestTsGmcConnect(&subConn, &subStmt, 2, (char *)g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);

    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// DTS2024082851984
/* ****************************************************************************
 Description  : 072.拥有缓存空间的表不支持ALTER TABLE
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test2, Timing_052_001_072)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0, tryCnt = 0;
    // 创建逻辑表
    (void)sprintf(g_sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(160))"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '50 MB', cache_size = '1000', "
        "compression = 'fast(rapidlz)');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // alter含缓存空间的时序表，新增一列
    (void)sprintf(g_sqlCmd, "alter table if exists %s add newcol blob;", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = testGmcGetLastError("Feature is not supported. Execute GmcExecDirect.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)sprintf(g_sqlCmd, "alter table if exists %s add newcol blob;", g_tableName2);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // alter含缓存空间的时序表，修改disk_limit值
    (void)sprintf(g_sqlCmd, "alter table if exists %s SET (disk_limit = '1 MB');", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = testGmcGetLastError("Feature is not supported. Execute GmcExecDirect.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验服务端日志
    (void)snprintf(g_command, MAX_CMD_SIZE, "grep -r \"Label tsdb1 alter table\" %s", SERVER_LOG_PATH);
    while (tryCnt < MAX_TRY_CNT) {
        ret =
            executeCommand(g_command, "ERROR", "Feature is not supported", "Label tsdb1 alter table when cache is set");
        if (ret == GMERR_OK) {
            break;
        }
        sleep(1);
        tryCnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// DTS2024071910543
/* ****************************************************************************
 Description  : 073.创建时序逻辑表时，字段类型为char('00111')，预期创建时序逻辑表失败
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test2, Timing_052_001_073)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_CHECK_LOG_END();
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_SYNTAX_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0, tryCnt = 0;
    const char val[][128] = {"'aaa'", "'0064'", "'1111'", "'1aaa'", "'_1111'", "00111a", "aaaa", "0065535&", "  "};
    int count = sizeof(val) / sizeof(val[0]);

    (void)sprintf(g_sqlCmd,
        "create table %s(id integer, time integer, name char(0064), ip inet, message blob(160), newmessage text)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '50 MB', "
        "compression = 'fast(rapidlz)');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // alter时序表，新增一列
    (void)sprintf(g_sqlCmd, "alter table if exists %s add message1 text;", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    while (tryCnt < MAX_TRY_CNT) {
        for (int i = 0; i < count; i++) {
            // 创建逻辑表
            (void)sprintf(g_sqlCmd,
                "create table %s(id integer, time integer, name char(%s), ip inet, message blob(160), "
                "newmessage text(1000)) with (time_col = 'time', interval = '1 hour', disk_limit = '50 MB', "
                "compression = 'fast(rapidlz)');",
                g_tableName2, val[i]);
            ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
            AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
            // alter含缓存空间的时序表，新增一列
            (void)sprintf(g_sqlCmd, "alter table if exists %s add message1 text(100);", g_tableName2);
            ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        tryCnt++;
    }

    // 删除创建的表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// DTS2024090607065，DTS2024090620793
/* ****************************************************************************
 Description  : 074.校验时序相关表、视图，catalog视图VERTEX_LABEL_TYPE类型以及key值
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test2, Timing_052_001_074)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);

    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建逻辑表
    (void)sprintf(g_sqlCmd,
        "create table %s(id integer, time integer, name char(0064), ip inet, message blob(160), newmessage text)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '50 MB', "
        "compression = 'fast(rapidlz)');",
        g_tableName);
    ret = GmcExecDirect(stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查看创建时序表的类型以及key值
    // 逻辑表
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from %s where VERTEX_LABEL_NAME = '%s';\" -s %s",
        g_catalogVertexView, g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "VERTEX_LABEL_TYPE: VERTEX_TYPE_TS_LOGICAL", "KEYS: {}");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除创建的表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 断开连接
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// DTS2024091316189
/* ****************************************************************************
 Description  : 075.创建时序逻辑表，drop表之后，catalog视图资源清理干净
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test2, Timing_052_001_075)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);

    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建逻辑表并插入数据
    CreateAndInsert_LostOfDataToOrderByWithTwoParameter(stmt, g_tableName, (char *)"no");

    // 查看创建时序表的类型以及key值
    // 逻辑表
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from %s where VERTEX_LABEL_NAME = '%s';\" -s %s",
        g_catalogVertexView, g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "VERTEX_LABEL_TYPE: VERTEX_TYPE_TS_LOGICAL", "KEYS: {}");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 删除创建的表
    ret = DropCmTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sleep(10);

    // 查看drop后的时序表，catalog资源是否被清理
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -sql \"select * from %s where VERTEX_LABEL_NAME = '%s';\" -s %s",
        g_catalogVertexView, g_tableName, g_connServerTsdb);
    ret = executeCommand(g_command, "VERTEX_LABEL_TYPE: VERTEX_TYPE_TS_LOGICAL", "KEYS: {}");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    if (ret == GMERR_OK) {
        system(g_command);
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}

// DTS2024090521747
/* ****************************************************************************
 Description  : 076.对系统视图使用整型过滤，过滤后面不填值，以及like查询，预期服务正常
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test2, Timing_052_001_076)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_SEMANTIC_ERROR);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_DATATYPE_MISMATCH);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
    int ret = 0;
    // 创建逻辑表
    (void)sprintf(g_sqlCmd,
        "create table %s(id integer, time integer, name char(0064), ip inet, message blob(160), newmessage text)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '50 MB', "
        "compression = 'fast(rapidlz)');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -sql \"select * from %s where VERTEX_LABEL_NAME = '%s' and "
        "VERSION;\" -s %s",
        g_catalogVertexView, g_tableName, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "[WARN] Can not exec sysview sql, ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_command, g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -sql \"select * from %s where VERTEX_LABEL_NAME = '%s' and "
        "SCHEMA_INFO like 1024;\" -s %s",
        g_catalogVertexView, g_tableName, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "[WARN] Can not exec sysview sql, ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_command, g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 删除创建的表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// DTS2024090614164
/* ****************************************************************************
 Description  : 077.对系统视图使用算术表达式where MAX_RECORD_COUNT + MAX_RECORD_COUNT，预期服务正常
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test2, Timing_052_001_077)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_SEMANTIC_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    // 创建逻辑表
    (void)sprintf(g_sqlCmd,
        "create table %s(id integer, time integer, name char(0064), ip inet, message blob(160), newmessage text)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '50 MB', "
        "compression = 'fast(rapidlz)');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -sql \"select * from %s where VERTEX_LABEL_NAME = '%s' and "
        "MAX_RECORD_COUNT + MAX_RECORD_COUNT;\" -s %s",
        g_catalogVertexView, g_tableName, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "[WARN] Can not exec sysview sql, ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_command, g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 删除创建的表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// DTS2024092019101
/* ****************************************************************************
 Description  : 078.时序逻辑表，使用blob类型过滤，预期报错，服务端有明显日志
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test2, Timing_052_001_078)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_SEMANTIC_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0, tryCnt = 0;
    // 创建逻辑表并插入数据
    ret = CreateTableAndInsertData(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询整体数据
    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -sql \"select * from %s where id >= 18 and name like '%%' and "
        "ip >= 'f0d0c0a0124332543668542246548622' and message = 'aa';\" -s %s",
        g_tableName, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "[WARN] Can not exec sysview sql, ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_command, g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 校验服务端日志
    (void)snprintf(g_command, MAX_CMD_SIZE, "grep -r \"ts where clause do not support blob type\" %s", SERVER_LOG_PATH);
    while (tryCnt < MAX_TRY_CNT) {
        ret = executeCommand(g_command, "ERROR", "ts where clause do not support blob type, name:message");
        if (ret == GMERR_OK) {
            break;
        }
        sleep(1);
        tryCnt++;
    }
    // 删除创建的表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DTS2024092019101
/* ****************************************************************************
 Description  : 079.时序流表，使用多次like模糊查询，预期报错，服务端有明显日志
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test1, DISABLED_Timing_052_001_079)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_SEMANTIC_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0, tryCnt = 0;
    // 创建流表
    (void)sprintf(g_sqlCmd,
        "create table %s(id integer, time integer, name char(64)) with (time_col = 'time',"
        "is_stream = 'true', step_time = '2 s', step_count = 1000, max_size = 5000, delay_time = '2 s');",
        g_streamTableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    constexpr int streamCnt = 1000;
    int64_t id[streamCnt] = {0};
    int64_t time[streamCnt] = {0};
    char names[streamCnt][64] = {0};
    for (int i = 0; i < streamCnt; i++) {
        id[i] = i;
        time[i] = 10000 + i;
        (void)snprintf(names[i], 64, "aaaaaaa%d", i);
    }

    int64_t idCheck[streamCnt] = {1, 1, 1, 1, 1, 1, 1, 1, 1, 1};
    ret = BlukInsert_char(g_stmt, g_streamTableName, streamCnt, 3, id, time, names);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 模糊查询
    (void)snprintf(g_command, MAX_CMD_SIZE,
        "gmsysview -sql \"select * from %s where name like "
        "'a%'\" -s %s",
        g_streamTableName, g_connServerTsdb);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "[WARN] Can not exec sysview sql, ret = %d", GMERR_SEMANTIC_ERROR);
    ret = executeCommand(g_command, g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    ret = executeCommand(g_command, g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 校验服务端日志
    (void)snprintf(g_command, MAX_CMD_SIZE, "grep -r \"ts stream table do not support like\" %s", SERVER_LOG_PATH);
    while (tryCnt < MAX_TRY_CNT) {
        ret = executeCommand(g_command, "ERROR", "ts stream table do not support like expr.");
        if (ret == GMERR_OK) {
            break;
        }
        sleep(1);
        tryCnt++;
    }

    // 删除创建的表
    ret = DropCmTable(g_stmt, g_streamTableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// DTS2024092111203
/* ****************************************************************************
 Description  : 080.时序逻辑表含text(1000)，预期创建表失败
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test2, Timing_052_001_080)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    // 创建逻辑表
    (void)sprintf(g_sqlCmd,
        "create table %s(id integer, time integer, name char(0064), ip inet, message blob(160), newmessage text(1000))"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '50 MB', "
        "compression = 'fast(rapidlz)');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// DTS2024092111203
/* ****************************************************************************
 Description  : 081.对时序逻辑表alter新增字段，字段类型为text(1000)，预期失败
**************************************************************************** */
TEST_F(tsdbRelEnhance_001_test2, Timing_052_001_081)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    // 创建逻辑表
    (void)sprintf(g_sqlCmd,
        "create table %s(id integer, time integer, name char(0064), ip inet, message blob(160), newmessage text)"
        " with (time_col = 'time', interval = '1 hour', disk_limit = '50 MB', "
        "compression = 'fast(rapidlz)');",
        g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)sprintf(g_sqlCmd, "alter table if exists %s add message1 text(1000);", g_tableName);
    ret = GmcExecDirect(g_stmt, g_sqlCmd, strlen(g_sqlCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);

    // 删除创建的表
    ret = DropCmTable(g_stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
