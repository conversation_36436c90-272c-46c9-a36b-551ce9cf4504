/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: incre_pst_common.h
 * Author: lushiguang
 * Create: 2023-09-15
 */
#ifndef INCRE_PST_COMMON_H
#define INCRE_PST_COMMON_H

#include <sys/ipc.h>
#include <sys/shm.h>
#include <sys/mman.h>
#include "gtest/gtest.h"
#include "t_light.h"
#include "gms.h"

#ifdef __cplusplus
extern "C" {
#endif

#define LOG_IFERR(ret)                                                                                        \
    do {                                                                                                      \
        if ((ret) != T_OK) {                                                                                  \
            fprintf(stdout, "Error: %s:%d func:%s " #ret " = %d\n", __FILE__, __LINE__, __FUNCTION__, (ret)); \
        }                                                                                                     \
    } while (0)


GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
GmcStmtT *g_stmtAsync = NULL;
GmcConnT *g_connAsync = NULL;
const char *g_simpleLabel = "vl_simple";
const char *g_complexLabel = "vl_general_complex";
char *g_simpleSchema = NULL;
char *g_complexSchema = NULL;
char g_dbFilePath[1024] = {0};
char g_dbFileCtrlPath[1024] = {0};
char g_newDbFilePath[1024] = {0};
char g_dbFilePathNoExist[1024] = {0};
char g_dbFilePath1[1024] = {0};
char g_dbFilePath2[1024] = {0};

uint8_t *g_aptAddr = NULL;
uint32_t g_aptMemSize = 0;
pthread_t g_svThreadId = 0;
int g_shmid;
const char* g_shmName = "/my_shared_memory";

const char *g_config = R"(
    {
        "max_record_count":1000000
    }
)";

const char *g_config2 = R"(
    {
        "max_record_count":1000000,
        "auto_increment":1
    }
)";

char *g_simpleSet = (char *)R"({"A0": %i{1},
"A1": %i{10},
"A2": %i{100},
"A3": %i{1000},
"A4": %i{1,100000,0.5},
"A5": %i{10,100000,0.5}
})";

char *g_complexSet = (char *)R"({
"A0": %i{1},
"A1": %i{10},
"A2": %i{100},
"A3": %i{1000},
"A4": %i{1,100000,0.5},
"A5": %i{10,100000,0.5},
"A6": "%f{16}0",
"A7": "0x%f{32}0",
"A8": "0x%f{32}0",
"A9": "%f{2000}c",
"M0": [
{ "B0": %i{1}, "B1": %i{10}, "B2": "0x%f{8}0", "B3": "0x%f{16}0", "B4": "%f{50}x1", "B5": "%f{1000}t1" },
{ "B0": %i{2}, "B1": %i{20}, "B2": "0x%f{16}1", "B3": "0x%f{32}1", "B4": "%f{50}x2", "B5": "%f{1000}t2" },
{ "B0": %i{3}, "B1": %i{30}, "B2": "0x%f{32}1", "B3": "0x%f{64}1", "B4": "%f{50}x3", "B5": "%f{1000}t3" }
]
})";

int RestartGmserver()
{
    int ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/stop.sh -f");
    RETURN_IFERR(ret);
    ret = GtExecSystemCmd("sh ${TEST_HOME}/tools/start.sh -f");
    RETURN_IFERR(ret);
}

int CommonDropTable()
{
    int ret = GmcDropVertexLabel(g_stmt, g_complexLabel);
    RETURN_IFERR(ret);
    ret = GmcDropVertexLabel(g_stmt, g_simpleLabel);
    RETURN_IFERR(ret);
    return T_OK;
}

int CommonDDL()
{
    (void)GmcDropVertexLabel(g_stmt, g_simpleLabel);
    (void)GmcDropVertexLabel(g_stmt, g_complexLabel);
    // 简单表
    readJanssonFile("../schema/vl_simple.gmjson", &g_simpleSchema);
    if (g_simpleSchema == NULL) {
        return T_FAILED;
    }
    int ret = GmcCreateVertexLabel(g_stmt, g_simpleSchema, g_config);
    RETURN_IFERR(ret);
    // 复杂表
    readJanssonFile("../schema/vl_general_complex.gmjson", &g_complexSchema);
    if (g_complexSchema == NULL) {
        return T_FAILED;
    }
    ret = GmcCreateVertexLabel(g_stmt, g_complexSchema, g_config);
    RETURN_IFERR(ret);

    free(g_simpleSchema);
    free(g_complexSchema);
    g_simpleSchema = NULL;
    g_complexSchema = NULL;

    return T_OK;
}

int CommonDDLWithOutLocaLKey()
{
    (void)GmcDropVertexLabel(g_stmt, g_simpleLabel);
    (void)GmcDropVertexLabel(g_stmt, g_complexLabel);
    // 简单表
    readJanssonFile("../schema/vl_simple.gmjson", &g_simpleSchema);
    if (g_simpleSchema == NULL) {
        return T_FAILED;
    }
    int ret = GmcCreateVertexLabel(g_stmt, g_simpleSchema, g_config);
    RETURN_IFERR(ret);
    // 复杂表
    readJanssonFile("../schema/vl_general_complex2.gmjson", &g_complexSchema);
    if (g_complexSchema == NULL) {
        return T_FAILED;
    }
    ret = GmcCreateVertexLabel(g_stmt, g_complexSchema, g_config);
    RETURN_IFERR(ret);

    free(g_simpleSchema);
    free(g_complexSchema);
    g_simpleSchema = NULL;
    g_complexSchema = NULL;

    return T_OK;
}

int CommonDDL2()
{
    (void)GmcDropVertexLabel(g_stmt, g_simpleLabel);
    (void)GmcDropVertexLabel(g_stmt, g_complexLabel);
    // 简单表
    readJanssonFile("../schema/vl_simple.gmjson", &g_simpleSchema);
    if (g_simpleSchema == NULL) {
        return T_FAILED;
    }
    int ret = GmcCreateVertexLabel(g_stmt, g_simpleSchema, g_config);
    RETURN_IFERR(ret);
    // 复杂表
    readJanssonFile("../schema/vl_general_complex3.gmjson", &g_complexSchema);
    if (g_complexSchema == NULL) {
        return T_FAILED;
    }
    ret = GmcCreateVertexLabel(g_stmt, g_complexSchema, g_config2);
    RETURN_IFERR(ret);

    free(g_simpleSchema);
    free(g_complexSchema);
    g_simpleSchema = NULL;
    g_complexSchema = NULL;

    return T_OK;
}

int CommonInsert(int beginIndex, int endIndex)
{
    int ret = TestInsVertexSync(g_stmt, g_simpleLabel, g_simpleSet, beginIndex, endIndex);
    RETURN_IFERR(ret);
    ret = TestInsVertexSync(g_stmt, g_complexLabel, g_complexSet, beginIndex, endIndex);
    RETURN_IFERR(ret);
    return T_OK;
}

int CommonUpdate(int beginIndex, int endIndex)
{
    char indexName[] = "PrimaryKey";
    char *cond = (char *)"A0(int32)=%i{1}";
    char *updateFormat = (char *)"A1(int64)=%i{100};A2(uint32)=%i{1};A5(double)=%i{1,100000,0.5}";
    int ret = TestUpdVertexSync(g_stmt, g_simpleLabel, indexName, cond, updateFormat, beginIndex, endIndex);
    RETURN_IFERR(ret);
    updateFormat = (char *)"A1(int64)=%i{100};A2(uint32)=%i{1};A5(double)=%i{1,100000,0.5};M0[0].B0(int32)=%i{10};M0[2]"
        ".B5(string)=%f{20}f3";
    ret = TestUpdVertexSync(g_stmt, g_complexLabel, indexName, cond, updateFormat, beginIndex, endIndex);
    RETURN_IFERR(ret);
    return T_OK;
}

int CommonDelete(int beginIndex, int endIndex)
{
    char indexName[] = "PrimaryKey";
    char *cond = (char *)"A0(int32)=%i{1}";
    int ret = TestdelVertexSync(g_stmt, g_simpleLabel, indexName, cond, beginIndex, endIndex);
    RETURN_IFERR(ret);
    ret = TestdelVertexSync(g_stmt, g_complexLabel, indexName, cond, beginIndex, endIndex);
    RETURN_IFERR(ret);
    return T_OK;
}

int CommonCheckInCount(int expectCount, int beginIndex, int endIndex)
{
    char indexName[] = "PrimaryKey";
    char *cond = (char *)"A0(int32)=%i{1}";
    int ret = TestSelVertexCount(g_stmt, g_simpleLabel, indexName, cond, expectCount, beginIndex, endIndex);
    RETURN_IFERR(ret);
    ret = TestSelVertexCount(g_stmt, g_complexLabel, indexName, cond, expectCount, beginIndex, endIndex);
    RETURN_IFERR(ret);
    return T_OK;
}

int CommonCheckRecord(int beginIndex, int endIndex)
{
    char indexName[] = "PrimaryKey";
    char *cond = (char *)"A0(int32)=%i{1}";
    char *checkFormat = (char *)"A1(int64)=%i{10};A2(uint32)=%i{100};A5(double)=%i{10,100000,0.5}";
    int ret = TestSelVertexRecord(g_stmt, g_simpleLabel, indexName, cond, checkFormat, beginIndex, endIndex);
    RETURN_IFERR(ret);
    checkFormat =
        (char *)"A1(int64)=%i{10};A5(double)=%i{10,100000,0.5};M0[0].B0(int32)=%i{1};M0[2].B5(string)=%f{1000}t3";
    ret = TestSelVertexRecord(g_stmt, g_complexLabel, indexName, cond, checkFormat, beginIndex, endIndex);
    RETURN_IFERR(ret);
    return T_OK;
}

int CommonCheckUpdRecord(int beginIndex, int endIndex)
{
    char indexName[] = "PrimaryKey";
    char *cond = (char *)"A0(int32)=%i{1}";
    char *checkFormat = (char *)"A1(int64)=%i{100};A2(uint32)=%i{1};A5(double)=%i{1,100000,0.5}";
    int ret = TestSelVertexRecord(g_stmt, g_simpleLabel, indexName, cond, checkFormat, beginIndex, endIndex);
    RETURN_IFERR(ret);
    checkFormat = (char *)"A1(int64)=%i{100};A2(uint32)=%i{1};A5(double)=%i{1,100000,0.5};M0[0].B0(int32)=%i{10};M0[2]."
        "B5(string)=%f{20}f3";
    ret = TestSelVertexRecord(g_stmt, g_complexLabel, indexName, cond, checkFormat, beginIndex, endIndex);
    RETURN_IFERR(ret);
    return T_OK;
}

static const uint32_t pageSize = 32 * 1024;
// PersFileHeadT 的大小
static const uint32_t ctrlFileHeadSize = 8;
// PersFileHeadT 结构体4KB对齐
static void DestoryCtrlFileContent(char *filePath, uint32_t pageIndex, uint32_t offset)
{
    FILE *file = fopen(filePath, "r+");
    EXPECT_EQ(true, file != NULL);

    // ctrl file 第一个页为预留页
    // 还有一个文件头大小
    uint32_t fileOffset = ctrlFileHeadSize + pageIndex * pageSize + offset;
    int ret;
    (void)fseek(file, fileOffset, SEEK_SET);

    // 破坏文件的内容
    (void)fwrite("kkkkkkkk", 1, 9, file);

    // 关闭文件
    ret = fclose(file);
    EXPECT_EQ(GMERR_OK, ret);
}

int BuildAndDml(int beginIndex, int endIndex)
{
    // 建表
    int ret = CommonDDL();
    RETURN_IFERR(ret);
    // 写入数据
    ret = CommonInsert(beginIndex, endIndex);
    RETURN_IFERR(ret);
    // 更新
    ret = CommonUpdate(beginIndex, endIndex);
    RETURN_IFERR(ret);
    // 删除部分
    ret = CommonDelete(beginIndex, endIndex / 2);
    RETURN_IFERR(ret);
    // 查询数据量
    ret = CommonCheckInCount(endIndex / 2 - beginIndex, beginIndex, endIndex);
    RETURN_IFERR(ret);
    // 查询更新结果
    ret = CommonCheckUpdRecord(beginIndex, endIndex / 2);
    RETURN_IFERR(ret);
    return T_OK;
}

int ReCheckDml(int beginIndex, int endIndex)
{
    // 查询数据量
    int ret = CommonCheckInCount(endIndex / 2 - beginIndex, beginIndex, endIndex);
    RETURN_IFERR(ret);
    // 查询结果
    ret = CommonCheckUpdRecord(beginIndex, endIndex / 2);
    RETURN_IFERR(ret);
    return T_OK;
}

int CommonPstCheck(int beginIndex, int endIndex)
{
    int ret = testGmcConnect(&g_conn, &g_stmt);
    RETURN_IFERR(ret);
    // 建表跑业务操作
    ret = BuildAndDml(beginIndex, endIndex);

    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    RETURN_IFERR(ret);
    testEnvClean();
    ret = RestartGmserver();
    RETURN_IFERR(ret);
    ret = testEnvInit(-1, false);
    RETURN_IFERR(ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    RETURN_IFERR(ret);

    // 重建表，验证数据
    ret = ReCheckDml(beginIndex, endIndex);
    RETURN_IFERR(ret);

    ret = CommonDropTable();
    RETURN_IFERR(ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    RETURN_IFERR(ret);
    testEnvClean();
    return T_OK;
}

bool RedoIsExists(char *dataFilePath)
{
    char temp[512] = {0};
    (void)sprintf(temp, "%s/gmdb_redo_0", dataFilePath);
    return FileIsExist(temp);
}

int InitAndConn()
{
    int ret = testEnvInit(-1, false);
    RETURN_IFERR(ret);
    ret = create_epoll_thread();
    RETURN_IFERR(ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    RETURN_IFERR(ret);
    return T_OK;
}

int RestartAndConn()
{
    // 重启
    int ret = testGmcDisconnect(g_conn, g_stmt);
    RETURN_IFERR(ret);
    testEnvClean();
    ret = RestartGmserver();
    RETURN_IFERR(ret);
    ret = testEnvInit(-1, false);
    RETURN_IFERR(ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    RETURN_IFERR(ret);
    return T_OK;
}

int RestartAndConn2()
{
    // 重启
    int ret;
    testEnvClean();
    ret = RestartGmserver();
    RETURN_IFERR(ret);
    ret = testEnvInit(-1, false);
    RETURN_IFERR(ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    RETURN_IFERR(ret);
    return T_OK;
}

int DisConnAndClean()
{
    int ret = CommonDropTable();
    RETURN_IFERR(ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    RETURN_IFERR(ret);
    ret = close_epoll_thread();
    RETURN_IFERR(ret);
    testEnvClean();
    return T_OK;
}

/* ****** ipv4地址 转换为 uint32 ****** */
uint32_t TransIp(const char *ipStr)
{
    char strIpIndex[4] = {'\0'};
    uint32_t ipInt;
    uint32_t ipAdd = 0;
    int j = 0, a = 3;
    for (uint32_t i = 0; i <= strlen(ipStr); i++) { // 要用到'\0'
        if (ipStr[i] == '\0' || ipStr[i] == '.') {
            ipInt = atoi(strIpIndex);
            if (ipInt < 0 || ipInt > 255) {
                printf("IP地址有误\n");
                system("pause");
                return 0;
            }
            ipAdd += (ipInt * ((uint32_t)pow(256.0, a)));
            a--;
            memset(strIpIndex, 0, sizeof(strIpIndex));
            j = 0;
            continue;
        }
        strIpIndex[j] = ipStr[i];
        j++;
    }
    return ipAdd;
}

uint32_t TruncateFile(char *fileName, float percentage, char direction)
{
    FILE *fp = fopen(fileName, "rb+");
    if (fp == NULL) {
        printf("Failed to open file %s", fileName);
        return T_FAILED;
    }

    // 获取文件大小
    (void)fseek(fp, 0L, SEEK_END);
    long int fileSize = ftell(fp);
    if (fileSize == 0) {
        return T_FAILED;
    }
    // 计算截断位置
    long int sizeCount;
    char *buffer = NULL;
    long int truncatePos = fileSize * percentage;

    if (direction == 'b') {
        (void)fseek(fp, truncatePos, SEEK_SET);
        sizeCount = fileSize - truncatePos;
        buffer = (char *)malloc(fileSize - truncatePos);
        if (buffer == NULL) {
            (void)fclose(fp);
            return T_FAILED;
        }
        (void)fread(buffer, sizeof(char), fileSize - truncatePos, fp);
    } else if (direction == 'a') {
        (void)fseek(fp, 0, SEEK_SET);
        sizeCount = truncatePos;
        buffer = (char *)malloc(truncatePos);
        if (buffer == NULL) {
            (void)fclose(fp);
            return T_FAILED;
        }
        (void)fread(buffer, sizeof(char), truncatePos, fp);
    } else {
        printf("Invalid direction %c", direction);
        (void)fclose(fp);
        return T_FAILED;
    }
    (void)fseek(fp, 0, SEEK_SET);
    (void)ftruncate(fileno(fp), 0);
    (void)fwrite(buffer, sizeof(char), sizeCount, fp);
    (void)fclose(fp);
    return T_OK;
}

int CreateShareMem()
{
    g_shmid = shm_open(g_shmName, O_CREAT | O_RDWR, 0666);
    if (g_shmid == -1) {
        perror("shm_open");
        return T_FAILED;
    }
    if (ftruncate(g_shmid, g_aptMemSize) == -1) {
        perror("ftruncate");
        return T_FAILED;
    }
    g_aptAddr = (uint8_t *)mmap(NULL, g_aptMemSize, PROT_READ | PROT_WRITE, MAP_SHARED, g_shmid, 0);
    if (g_aptAddr == MAP_FAILED) {
        perror("mmap");
        return T_FAILED;
    }
    (void)memset(g_aptAddr, 0, g_aptMemSize);

    return T_OK;
}

int GetShareMem()
{
    g_shmid = shm_open(g_shmName, O_RDWR, 0666);
    if (g_shmid == -1) {
        perror("shm_open");
        return T_FAILED;
    }
    g_aptAddr = (uint8_t *)mmap(NULL, g_aptMemSize, PROT_READ | PROT_WRITE, MAP_SHARED, g_shmid, 0);
    if (g_aptAddr == MAP_FAILED) {
        perror("mmap");
        return T_FAILED;
    }

    return T_OK;
}

int DetachShareMem()
{
    if (munmap(g_aptAddr, g_aptMemSize) == -1) {
        perror("munmap");
        return T_FAILED;
    }
    if (close(g_shmid) == -1) {
        perror("close");
        return T_FAILED;
    }
    return T_OK;
}

int CleanShareMem()
{
    int ret = DetachShareMem();
    if (ret == T_FAILED) {
        return ret;
    }
    if (shm_unlink(g_shmName) == -1) {
        perror("shm_unlink");
        return T_FAILED;
    }
    return T_OK;
}

#ifdef FEATURE_CLT_SERVER_SAME_PROCESS
int GetRedoBufferRsm(uint8_t **addr, uint32_t *size, bool *forRecovery)
{
    *addr = g_aptAddr;
    *size = g_aptMemSize;
    *forRecovery = true;
    return T_OK;
}

// 破坏共享内存
int GetRedoBufferRsmErr(uint8_t **addr, uint32_t *size, bool *forRecovery)
{
    *addr = g_aptAddr;
    *size = g_aptMemSize;
    *forRecovery = true;
    for (uint32_t i = 0; i < g_aptMemSize; i++) {
        *(*addr + i) = i;
    }
    return T_OK;
}

int PstCompress(uint8_t *dest, uint32_t *destSize, const uint8_t *src, uint32_t srcSize)
{
    uint32_t dstLen = 0;
    uint32_t i = 0;
    while (i < srcSize) {
        uint8_t count = 1;
        while (i + count < srcSize && src[i] == src[i + count] && count < 255) {
            count++;
        }
        if (count > 1 || src[i] == 0xFF) {
            // 如果有重复的字节或者是0xFF，则需要进行压缩
            dest[dstLen++] = 0xFF;
            dest[dstLen++] = count;
            dest[dstLen++] = src[i];
            i += count;
        } else {
            // 否则直接复制
            dest[dstLen++] = src[i++];
        }
    }
    *destSize = dstLen;
    return T_OK;
}

int PstUnCompress(uint8_t *dest, uint32_t *destSize, const uint8_t *src, uint32_t srcSize)
{
    uint32_t dstLen = 0;
    uint32_t i = 0;
    while (i < srcSize) {
        if (src[i] == 0xFF) {
            // 如果是0xFF，则需要进行解压
            uint8_t count = src[i + 1];
            uint8_t value = src[i + 2];
            for (int j = 0; j < count; j++) {
                dest[dstLen++] = value;
            }
            i += 3;
        } else {
            // 否则直接复制
            dest[dstLen++] = src[i++];
        }
    }
    *destSize = dstLen;
    return T_OK;
}
#endif

typedef enum AdaptFunc {
    NONE,
    RSM,
    COMPRESS,
    RSM_AND_COMPRESS,
} AdaptFuncE;

int AdapAndStartServer(AdaptFuncE func)
{
    int ret = 0;
    if (g_runStat == RUN_STAT_SUCC) {
        return 0;
    }
    if (g_runStat == RUN_STAT_INIT) {
        while (ret < 500 && g_runStat == RUN_STAT_INIT) {
            ++ret;
            usleep(10000);
        }
        return 0;
    }
    g_runStat = RUN_STAT_INIT;
    pthread_mutex_init(&g_logLockTest, NULL);
    pthread_mutex_init(&g_logLockClient, NULL);
    pthread_mutex_init(&g_connLock, NULL);
    pthread_mutex_init(&g_connConcurrent, NULL);
    memset(g_logFilter, 0, sizeof(g_logFilter));
    if (g_isReadConfig == false) {
        getSysConfig();
    }

    system("ipcrm -a");
#ifdef FEATURE_CLT_SERVER_SAME_PROCESS
    GmsAdptFuncsT adpt = { 0 };
    if (func == RSM || func == RSM_AND_COMPRESS) {
        adpt.getReservedMemFunc = (GmsGetRedoBufferReservedSegmentFuncT)GetRedoBufferRsm;
    }
    if (func == COMPRESS || func == RSM_AND_COMPRESS) {
        adpt.persistCompressFunc = PstCompress;
        adpt.persistDecompressFunc = PstUnCompress;
    }
    ret = GmsRegAdaptFuncs(&adpt);
    if (ret != 0) {
        AW_FUN_Log(LOG_INFO, "[testInit1] GmsRegAdaptFuncs failed, ret = %d.\n", ret);
    }

    char *cmdString[4] = {(char *)"gmserver", (char *)"-p", (char *)g_sysGMDBCfg};
    ret = GmsServerMain(3, cmdString);
    if (ret != 0) {
        AW_FUN_Log(LOG_INFO, "[testEnvInit] GmsServerMain failed, ret = %d.\n", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
#endif
    ret = OpenEpollFunFromHPE();
    if (ret != 0) {
        AW_FUN_Log(LOG_INFO, "[testInit1] OpenEpollFunFromHPE failed, ret = %d.\n", ret);
        g_runStat = RUN_STAT_FAIL;
        return FAILED;
    }
    ret = GmcInit();
    if (ret != 0) {
        AW_FUN_Log(LOG_INFO, "[testInit1] GmcInit failed, ret = %d.\n", ret);
        g_runStat = RUN_STAT_FAIL;
        return FAILED;
    }
    ret = testPrepareNameSpace();
    if (ret != 0) {
        AW_FUN_Log(LOG_INFO, "[testInit1] testPrepareNameSpace failed, ret = %d.\n", ret);
        g_runStat = RUN_STAT_FAIL;
        return FAILED;
    }
    g_runStat = RUN_STAT_SUCC;
    return 0;
}

int AdapAndStartServerErr(AdaptFuncE func)
{
    int ret = 0;
    if (g_runStat == RUN_STAT_SUCC) {
        return 0;
    }
    if (g_runStat == RUN_STAT_INIT) {
        while (ret < 500 && g_runStat == RUN_STAT_INIT) {
            ++ret;
            usleep(10000);
        }
        return 0;
    }
    g_runStat = RUN_STAT_INIT;
    pthread_mutex_init(&g_logLockTest, NULL);
    pthread_mutex_init(&g_logLockClient, NULL);
    pthread_mutex_init(&g_connLock, NULL);
    pthread_mutex_init(&g_connConcurrent, NULL);
    memset(g_logFilter, 0, sizeof(g_logFilter));
    if (g_isReadConfig == false) {
        getSysConfig();
    }

    system("ipcrm -a");
#ifdef FEATURE_CLT_SERVER_SAME_PROCESS
    GmsAdptFuncsT adpt = { 0 };
    if (func == RSM || func == RSM_AND_COMPRESS) {
        adpt.getReservedMemFunc = (GmsGetRedoBufferReservedSegmentFuncT)GetRedoBufferRsmErr;
    }
    if (func == COMPRESS || func == RSM_AND_COMPRESS) {
        adpt.persistCompressFunc = PstCompress;
        adpt.persistDecompressFunc = PstUnCompress;
    }
    ret = GmsRegAdaptFuncs(&adpt);
    if (ret != 0) {
        AW_FUN_Log(LOG_INFO, "[testInit1] GmsRegAdaptFuncs failed, ret = %d.\n", ret);
    }

    char *cmdString[4] = {(char *)"gmserver", (char *)"-p", (char *)g_sysGMDBCfg};
    ret = GmsServerMain(3, cmdString);
    if (ret != 0) {
        AW_FUN_Log(LOG_INFO, "[testEnvInit] GmsServerMain failed, ret = %d.\n", ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
#endif
    ret = OpenEpollFunFromHPE();
    if (ret != 0) {
        AW_FUN_Log(LOG_INFO, "[testInit1] OpenEpollFunFromHPE failed, ret = %d.\n", ret);
        g_runStat = RUN_STAT_FAIL;
        return FAILED;
    }
    ret = GmcInit();
    if (ret != 0) {
        AW_FUN_Log(LOG_INFO, "[testInit1] GmcInit failed, ret = %d.\n", ret);
        g_runStat = RUN_STAT_FAIL;
        return FAILED;
    }
    ret = testPrepareNameSpace();
    if (ret != 0) {
        AW_FUN_Log(LOG_INFO, "[testInit1] testPrepareNameSpace failed, ret = %d.\n", ret);
        g_runStat = RUN_STAT_FAIL;
        return FAILED;
    }
    g_runStat = RUN_STAT_SUCC;
    return 0;
}

// 新增配置项不存在gmserver.ini里
int Addini(char *para, char *value)
{
    char gCommand[1024];
    memset(gCommand, 0, sizeof(gCommand));
    (void)snprintf(gCommand, 1024, "grep -rn %s %s |wc -l", para, g_sysGMDBCfg);
    int ret = executeCommand(gCommand, "0");
    if (ret == GMERR_OK) {
        memset(gCommand, 0, sizeof(gCommand));
        // echo赋值必须是初始值
        (void)snprintf(gCommand, 1024, "echo '%s = %s' >> %s", para, value, g_sysGMDBCfg);
        system(gCommand);
    }
    return T_OK;
}

int MultiZoneBk()
{
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        printf("get env PWD fail.\n");
    }
    char tempDir[1024] = {0};
    (void)sprintf(g_dbFilePath, "%s/gmdb", pwdDir);
    (void)sprintf(g_newDbFilePath, "%s/new_gmdb", pwdDir);
    int ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"1");
    RETURN_IFERR(ret);
    ret = ChangeGmserverCfg((char *)"multizonePersistNum", (char *)"2");
    RETURN_IFERR(ret);
    (void)sprintf(g_dbFilePath1, "%s1", g_dbFilePath);
    (void)sprintf(g_dbFilePath2, "%s2", g_dbFilePath);
    
    (void)Rmdir(g_dbFilePath1);
    (void)Rmdir(g_dbFilePath2);
    ret = mkdir(g_dbFilePath1, S_IRUSR | S_IWUSR);
    RETURN_IFERR(ret);
    ret = mkdir(g_dbFilePath2, S_IRUSR | S_IWUSR);
    RETURN_IFERR(ret);
    char temp[2048] = {0};
    (void)sprintf(temp, "%s,%s", g_dbFilePath1, g_dbFilePath2);
    ret = ChangeGmserverCfg((char *)"dataFileDirPath", temp);
    RETURN_IFERR(ret);
    return T_OK;
}

#ifdef __cplusplus
}
#endif

#endif /* INCRE_PST_COMMON_H */

