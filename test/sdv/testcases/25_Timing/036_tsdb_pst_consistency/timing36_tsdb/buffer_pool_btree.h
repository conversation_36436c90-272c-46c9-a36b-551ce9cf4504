/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: buffer_pool_btree.h
 * Author: lushiguang
 * Create: 2024-05-23
 */
#ifndef BUFFER_POOL_BTREE_H
#define BUFFER_POOL_BTREE_H

#include "gtest/gtest.h"
#include "t_rd_common.h"
#include "t_rd_sn.h"
#include "t_rd_ts.h"

#define MAX_BLUK_COUNT 50000

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
GmcConnT *g_conn2 = NULL;
GmcStmtT *g_stmt2 = NULL;

char *g_sysGMDBCfgTs = (char *)"$HOME/../usr/local/file/gmserver_ts.ini";
char g_dbFilePath[1024] = {0};
char g_tempFilePath[1024] = {0};
char g_cStoreDir[1024] = {0};
char g_tableName[] = "testdb";
char g_tableName2[] = "testdb2";
char *g_cfeExePath = (char *)"$HOME/cfe/cfe/cfe";
char g_command[1024] = {0};

char g_tableColumnStr[512] =
    "time integer, num1 integer, num2 integer, num3 integer, num4 integer, str1 char(32), str2 char(32)";
char g_otherTableConfig[512] = "";
char g_selectColumnStr[512] = "num1,num2,num3,num4,str2";

typedef struct {
    GmcStmtT *stmt;
    int expectCount;
    int insertCount;
    char *tableName;
    char *queryCond;
} DMLArgsT;

typedef struct {
    GmcStmtT *stmt;
    char *tableName;
    int beginIndex;
    int endIndex;
} DDLArgsT;

typedef enum RecordOrderE { RANDOM = 0, ASC, DESC } RecordOrderE;

int CfeInjectFaults(char *cfeCmd, ...)
{
    int ret = GtExecSystemCmd("%s \"%s\"", g_cfeExePath, cfeCmd);
    return ret;
}

int StartEnvWithConfig(char *configStr)
{
    if (configStr) {
        char tempCmd[1024] = {0};
        (void)sprintf(tempCmd, "sh $TEST_HOME/tools/modifyCfg.sh -ts \"%s\"", configStr);
        system(tempCmd);
    }
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = testEnvInit();
    RETURN_IFERR(ret);
    ret = create_epoll_thread();
    RETURN_IFERR(ret);
    ret = TestTsGmcConnect(&g_conn, &g_stmt);
    return ret;
}

int DropCmTable(GmcStmtT *stmt, char *tableName)
{
    char sqlCmd[100] = {0};
    (void)snprintf(sqlCmd, sizeof(sqlCmd), "drop table %s;", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    int ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    return ret;
}

int CreateCmTable(GmcStmtT *stmt, char *tableName)
{
    char sqlCmd[2048] = {0};
    (void)snprintf(sqlCmd, sizeof(sqlCmd), "create table %s(%s) with (time_col = 'time', interval= '1 hour'%s);",
        tableName, g_tableColumnStr, g_otherTableConfig);
    uint32_t cmdLen = strlen(sqlCmd);
    int ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    return ret;
}

int QueryExpectRecord(GmcStmtT *stmt, char *tableName, char *queryCond, int expectCount, bool printFlag = true)
{
    if (printFlag) {
        AW_FUN_Log(LOG_STEP, "Query tableName: %s queryCond: %s expectCount: %d.", tableName, queryCond, expectCount);
    }
    char sqlCmd[4096] = {0};
    (void)snprintf(sqlCmd, sizeof(sqlCmd), "select %s from %s where %s", g_selectColumnStr, tableName, queryCond);
    int ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
    RETURN_IFERR(ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    RETURN_IFERR(ret);
    if (dataCount != expectCount) {
        AW_FUN_Log(LOG_STEP, "expect query count: %d actually query count:%d.", expectCount, dataCount);
        return FAILED;
    }
    bool eof;
    int64_t num1;
    char text[33] = {0};
    bool isNull = false;
    uint32_t size = sizeof(int64_t);
    for (int i = 0; i < dataCount; i++) {
        ret = GmcFetch(stmt, &eof);
        RETURN_IFERR(ret);
        if (eof) {
            AW_FUN_Log(LOG_STEP, "GmcFetch eof.");
            return FAILED;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &num1, &size, &isNull);
        RETURN_IFERR(ret);
        if (printFlag) {
            printf(" %lld", num1);
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &num1, &size, &isNull);
        RETURN_IFERR(ret);
        if (printFlag) {
            printf(" %lld", num1);
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &num1, &size, &isNull);
        RETURN_IFERR(ret);
        if (printFlag) {
            printf(" %lld", num1);
        }
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &num1, &size, &isNull);
        RETURN_IFERR(ret);
        if (printFlag) {
            printf(" %lld", num1);
        }
        uint32_t strSize = sizeof(text);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, text, &strSize, &isNull);
        RETURN_IFERR(ret);
        if (printFlag) {
            printf(" %s\n", text);
        }
        memset(text, 0, sizeof(text));
    }
    return GMERR_OK;
}

int InsertCommon(GmcStmtT *stmt, char *tableName, int insertCount, RecordOrderE mode)
{
    AW_FUN_Log(LOG_STEP, "Insert tableName: %s count: %d mode: %d.", tableName, insertCount, mode);
    int tempCount = insertCount;
    int textLen = 32;
    int blukTime = 0;
    int ret;
    int maxBlukInsertCount = (insertCount > MAX_BLUK_COUNT) ? MAX_BLUK_COUNT : insertCount;
    int64_t *timeArray = (int64_t *)malloc(sizeof(int64_t) * maxBlukInsertCount);
    if (timeArray == NULL) {
        AW_FUN_Log(LOG_STEP, "malloc fail.");
        return FAILED;
    }
    memset(timeArray, 0, maxBlukInsertCount);
    int64_t *num = (int64_t *)malloc(sizeof(int64_t) * maxBlukInsertCount);
    if (num == NULL) {
        AW_FUN_Log(LOG_STEP, "malloc fail.");
        free(timeArray);
        return FAILED;
    }
    int64_t *num2 = (int64_t *)malloc(sizeof(int64_t) * maxBlukInsertCount);
    if (num2 == NULL) {
        AW_FUN_Log(LOG_STEP, "malloc fail.");
        free(num);
        free(timeArray);
        return FAILED;
    }
    char *text = (char *)malloc(sizeof(char) * maxBlukInsertCount * textLen);
    if (text == NULL) {
        AW_FUN_Log(LOG_STEP, "malloc fail.");
        free(timeArray);
        free(num);
        free(num2);
        return FAILED;
    }
    memset(text, 'A', maxBlukInsertCount * textLen);
    while (tempCount > 0) {
        int onceInsertCount = 0;
        if (tempCount >= maxBlukInsertCount) {
            onceInsertCount = maxBlukInsertCount;
            blukTime++;
            tempCount = tempCount - maxBlukInsertCount;
        } else {
            onceInsertCount = tempCount;
            tempCount = 0;
        }
        if (mode == ASC) {
            for (int i = 0; i < onceInsertCount; i++) {
                timeArray[i] = i + (blukTime - 1) * maxBlukInsertCount;
                num[i] = i + (blukTime - 1) * maxBlukInsertCount;
                num2[i] = i + 10 * i + (blukTime - 1) * maxBlukInsertCount;
            }
        }
        if (mode == DESC) {
            for (int i = 0; i < onceInsertCount; i++) {
                timeArray[i] = 0xffffffff - i + (blukTime - 1) * maxBlukInsertCount;
                num[i] = i + (blukTime - 1) * maxBlukInsertCount;
                num2[i] = i + 10 * i + (blukTime - 1) * maxBlukInsertCount;
            }
        }

        if (mode == RANDOM) {
            for (int i = 0; i < onceInsertCount; i++) {
                timeArray[i] = i + (blukTime - 1) * maxBlukInsertCount;
                num[i] = i + (blukTime - 1) * maxBlukInsertCount;
                num2[i] = i + 10 * i + (blukTime - 1) * maxBlukInsertCount;
            }
            // 小范围随机打乱timeArray
            uint32_t seed = (uint32_t)time(NULL);
            srand(seed);
            int64_t temp;
            for (int i = 0; i < 500; i++) {
                int index1 = rand() % onceInsertCount;
                int index2 = rand() % onceInsertCount;
                temp = timeArray[index1];
                timeArray[index1] = timeArray[index2];
                timeArray[index2] = temp;
            }
        }

        ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &onceInsertCount, sizeof(int));
        GOTO_TAG_IFERR(ret, FREE_TAG);
        ret = GmcBindCol(stmt, 0, (GmcDataTypeE)DB_DATATYPE_INT64, timeArray, 0, NULL);
        GOTO_TAG_IFERR(ret, FREE_TAG);
        ret = GmcBindCol(stmt, 1, (GmcDataTypeE)DB_DATATYPE_INT64, num, 0, NULL);
        GOTO_TAG_IFERR(ret, FREE_TAG);
        ret = GmcBindCol(stmt, 2, (GmcDataTypeE)DB_DATATYPE_INT64, num, 0, NULL);
        GOTO_TAG_IFERR(ret, FREE_TAG);
        ret = GmcBindCol(stmt, 3, (GmcDataTypeE)DB_DATATYPE_INT64, num2, 0, NULL);
        GOTO_TAG_IFERR(ret, FREE_TAG);
        ret = GmcBindCol(stmt, 4, (GmcDataTypeE)DB_DATATYPE_INT64, num2, 0, NULL);
        GOTO_TAG_IFERR(ret, FREE_TAG);
        ret = GmcBindCol(stmt, 5, (GmcDataTypeE)DB_DATATYPE_FIXED, text, textLen, NULL);
        GOTO_TAG_IFERR(ret, FREE_TAG);
        ret = GmcBindCol(stmt, 6, (GmcDataTypeE)DB_DATATYPE_FIXED, text, textLen, NULL);
        GOTO_TAG_IFERR(ret, FREE_TAG);
        ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
        GOTO_TAG_IFERR(ret, FREE_TAG);
        ret = GmcExecute(stmt);
        GOTO_TAG_IFERR(ret, FREE_TAG);
    }
FREE_TAG:
    free(timeArray);
    free(num);
    free(num2);
    free(text);
    return ret;
}

int CommonOperate(GmcStmtT *stmt, char *tableName, int insertCount)
{
    int ret = CreateCmTable(stmt, tableName);
    RETURN_IFERR(ret);
    ret = InsertCommon(stmt, tableName, insertCount, ASC);
    return ret;
}

void *InsertTsdbAsync(void *args)
{
    DMLArgsT *arg = (DMLArgsT *)args;
    int ret = InsertCommon(arg->stmt, arg->tableName, arg->insertCount, ASC);
    AW_FUN_Log(LOG_STEP, "InsertTsdbAsync: %d", ret);
    return nullptr;
}

void *QueryRecordAsync(void *args)
{
    DMLArgsT *arg = (DMLArgsT *)args;
    int ret = QueryExpectRecord(arg->stmt, arg->tableName, arg->queryCond, arg->expectCount, false);
    AW_FUN_Log(LOG_STEP, "QueryRecord: %d", ret);
    return nullptr;
}

void *CreateTableAsync(void *args)
{
    DDLArgsT *arg = (DDLArgsT *)args;
    char tableName[50] = {0};
    for (int i = arg->beginIndex; i < arg->endIndex; i++) {
        (void)snprintf(tableName, sizeof(tableName), "%s%d", arg->tableName, i);
        int ret = CreateCmTable(arg->stmt, tableName);
        if (ret != GMERR_OK && ret != GMERR_DUPLICATE_TABLE) {
            AW_FUN_Log(LOG_STEP, "[CreateTableAsync] error when create table %s ret = %d", tableName, ret);
            break;
        }
    }
    return nullptr;
}

void *DropTableAsync(void *args)
{
    DDLArgsT *arg = (DDLArgsT *)args;
    char tableName[50] = {0};
    for (int i = arg->beginIndex; i < arg->endIndex; i++) {
        (void)snprintf(tableName, sizeof(tableName), "%s%d", arg->tableName, i);
        int ret = DropCmTable(arg->stmt, tableName);
        if (ret != GMERR_OK && ret != GMERR_UNDEFINED_TABLE) {
            AW_FUN_Log(LOG_STEP, "[DropTableAsync] error when drop table %s ret = %d", tableName, ret);
            break;
        }
    }
    return nullptr;
}

int ShutdownByGmadmin(int timeout = 20)
{
    memset(g_command, 0, sizeof(g_command));
    (void)sprintf_s(g_command, sizeof(g_command), "gmadmin -shutdown -s %s", g_connServerTsdb);
    AW_FUN_Log(LOG_STEP, "g_command: %s", g_command);
    system(g_command);
    for (int i = 0; i < timeout; i++) {
        int ret = GtExecSystemCmd("pidof gmserver_ts");
        if (ret != GMERR_OK) {
            return GMERR_OK;
        }
        sleep(1);
    }
    AW_FUN_Log(LOG_STEP, "The process does not exit after %d seconds.", timeout);
    return FAILED;
}

void ReConnEnv()
{
    int ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestTsGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

#endif
