/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 【交付增强】查询结果集直接写表
 * Author: jiangjjincheng
 * Create: 2024-04-16
 */
#include "gtest/gtest.h"
#include "t_rd_ts.h"
#include "../006_tsdb_query/tsdb_query.h"

static GmcConnOptionsT *connOptions = NULL;
static GmcConnT *conn = NULL;
static GmcStmtT *stmt = NULL;
Status ret = 0;
char g_cStoreDir[64] = {0};
char *dir = getenv("GMDB_HOME");
char tableName[] = "testdb";
char tableNameIn[] = "testdbIn";
char tableNameOut[] = "testdbOut";
bool eof = false;
bool isNull = false;

class TSDBInsertIntoConstrains : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        InitTsCiCfg();
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        // 创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        ret = TestTsGmcConnect(&conn, &stmt, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        GmcConnOptionsDestroy(connOptions);
        GmcFreeStmt(stmt);
        GmcDisconnect(conn);
        close_epoll_thread();
        testEnvClean();
        RecoverTsCiCfg();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void TSDBInsertIntoConstrains::SetUp()
{
    AW_CHECK_LOG_BEGIN();
}

void TSDBInsertIntoConstrains::TearDown()
{
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
}

int DropCmTable(char *tableName)
{
    int ret = 0;
    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    if (ret != 0) {
        AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_DATA_EXCEPTION);
    }
    return ret;
}

// 001.查询表不存在  预期：插入失败
TEST_F(TSDBInsertIntoConstrains, Timing_016_TSDBInsertIntoConstrains_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 建目的表
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameIn);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 执行insert into语句
    (void)sprintf(sqlCmd, "insert into %s select * from %s;", tableNameIn, tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    // 建源表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 源表写数据
    constexpr int64_t count = 10;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t age[count] = {0};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704038400 + i;
        age[i] = 18;
    }
    ret = BlukInsert(stmt, tableNameOut, count, 3, id, time, age);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 执行insert into语句
    (void)sprintf(sqlCmd, "insert into %s select * from %s;", tableNameIn, tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 补充删表动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameIn));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameOut));
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNDEFINED_TABLE);
}

// 002.插入表不存在  预期：插入失败
TEST_F(TSDBInsertIntoConstrains, Timing_016_TSDBInsertIntoConstrains_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    // 建源表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameOut);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 源表写数据
    constexpr int64_t count = 10;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t age[count] = {0};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704038400 + i;
        age[i] = 18;
    }
    ret = BlukInsert(stmt, tableNameOut, count, 3, id, time, age);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 执行insert into语句
    (void)sprintf(sqlCmd, "insert into %s select * from %s;", tableNameIn, tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    // 建目的表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameIn);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 执行insert into语句
    (void)sprintf(sqlCmd, "insert into %s select * from %s;", tableNameIn, tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 补充删表动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameIn));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameOut));
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNDEFINED_TABLE);
}

// 003.查询表不为时序表  预期：插入失败
TEST_F(TSDBInsertIntoConstrains, Timing_016_TSDBInsertIntoConstrains_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char g_label_schema[512] =
        "[{\"type\":\"record\", \"name\":\"T39\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
        "{\"name\":\"F1\", \"type\":\"int32\", \"nullable\" : false , \"default\" : 1},{\"name\":\"F2\", "
        "\"type\":\"int\"},{\"name\":\"F3\", \"type\":\"int32\"}],"
        "\"keys\":[{\"node\":\"T39\", \"name\":\"T39_K0\", \"fields\":[\"F0\"], "
        "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]";

    char g_configJson[128] = "{\"max_record_count\" : 10000}";
    char g_labelName[128] = "T39";
    GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, g_label_schema, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);

    GmcFreeStmt(stmt);
    GmcDisconnect(conn);

    conn = NULL;
    stmt = NULL;

    GmcConnOptionsCreate(&connOptions);
    GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    GmcConnOptionsSetCSRead(connOptions);
    GmcConnOptionsSetCSMode(connOptions);
    GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    GmcAllocStmt(conn, &stmt);

    char sqlCmd[512] = {0};
    // 建源表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameOut);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 源表写数据
    constexpr int64_t count = 10;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t age[count] = {0};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704038400 + i;
        age[i] = 18;
    }
    ret = BlukInsert(stmt, tableNameOut, count, 3, id, time, age);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 执行insert into语句
    (void)sprintf(sqlCmd, "insert into %s select * from %s;", tableNameIn, g_labelName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    // 建目的表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameIn);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 执行insert into语句
    (void)sprintf(sqlCmd, "insert into %s select * from %s;", tableNameIn, tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 补充删表动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameIn));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameOut));
    testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestTsGmcConnect(&conn, &stmt, 0));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcDropVertexLabel(stmt, g_labelName));
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNDEFINED_TABLE);
}

// 004.插入表不为时序表  预期：插入失败
TEST_F(TSDBInsertIntoConstrains, Timing_016_TSDBInsertIntoConstrains_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char g_label_schema[512] =
        "[{\"type\":\"record\", \"name\":\"T39\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
        "{\"name\":\"F1\", \"type\":\"int32\", \"nullable\" : false , \"default\" : 1},{\"name\":\"F2\", "
        "\"type\":\"int\"},{\"name\":\"F3\", \"type\":\"int32\"}],"
        "\"keys\":[{\"node\":\"T39\", \"name\":\"T39_K0\", \"fields\":[\"F0\"], "
        "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]";

    char g_configJson[128] = "{\"max_record_count\" : 10000}";
    char g_labelName[128] = "T39";
    GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, g_label_schema, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);

    GmcFreeStmt(stmt);
    GmcDisconnect(conn);

    conn = NULL;
    stmt = NULL;

    GmcConnOptionsCreate(&connOptions);
    GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    GmcConnOptionsSetCSRead(connOptions);
    GmcConnOptionsSetCSMode(connOptions);
    GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
    GmcAllocStmt(conn, &stmt);

    char sqlCmd[512] = {0};
    // 建源表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameOut);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 源表写数据
    constexpr int64_t count = 10;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t age[count] = {0};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704038400 + i;
        age[i] = 18;
    }
    ret = BlukInsert(stmt, tableNameOut, count, 3, id, time, age);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 执行insert into语句
    (void)sprintf(sqlCmd, "insert into %s select * from %s;", g_labelName, tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    // 建目的表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameIn);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 执行insert into语句
    (void)sprintf(sqlCmd, "insert into %s select * from %s;", tableNameIn, tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 补充删表动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameIn));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameOut));
    testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestTsGmcConnect(&conn, &stmt, 0));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcDropVertexLabel(stmt, g_labelName));
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNDEFINED_TABLE);
}

// 005.插入表列类型为integer，查询对应列为定长字符串类型  预期：插入失败
TEST_F(TSDBInsertIntoConstrains, Timing_016_TSDBInsertIntoConstrains_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    // 建目的表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameIn);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建源表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, name char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 源表写数据
    constexpr int64_t count = 10;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t age[count] = {0};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704038400 + i;
        age[i] = 18;
    }
    ret = BlukInsert_char(stmt, tableNameOut, count, 4, id, time, age, name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 执行insert into语句
    (void)sprintf(sqlCmd, "insert into %s (time, age) select time, name from %s;", tableNameIn, tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATATYPE_MISMATCH, ret);
    // 补充删表动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameIn));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameOut));
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DATATYPE_MISMATCH, GMERR_DATA_EXCEPTION);
}

// 006.插入表列类型为定长字符串，查询对应列为integer类型  预期：插入失败
TEST_F(TSDBInsertIntoConstrains, Timing_016_TSDBInsertIntoConstrains_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    // 建目的表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, name char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameIn);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建源表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 源表写数据
    constexpr int64_t count = 10;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t age[count] = {0};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704038400 + i;
        age[i] = 18;
    }
    ret = BlukInsert(stmt, tableNameOut, count, 3, id, time, age);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 执行insert into语句
    (void)sprintf(sqlCmd, "insert into %s (time, name) select time, age from %s;", tableNameIn, tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATATYPE_MISMATCH, ret);
    // 补充删表动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameIn));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameOut));
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DATATYPE_MISMATCH, GMERR_DATA_EXCEPTION);
}

// 007.插入表指定列数比查询表列数少  预期：插入失败
TEST_F(TSDBInsertIntoConstrains, Timing_016_TSDBInsertIntoConstrains_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    // 建目的表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, name char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameIn);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建源表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, name char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 源表写数据
    constexpr int64_t count = 10;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t age[count] = {0};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704038400 + i;
        age[i] = 18;
    }
    ret = BlukInsert_char(stmt, tableNameOut, count, 4, id, time, age, name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 执行insert into语句
    (void)sprintf(sqlCmd, "insert into %s (time, name) select time, age, name from %s;", tableNameIn, tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    // 补充删表动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameIn));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameOut));
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_INVALID_PARAMETER_VALUE, GMERR_DATA_EXCEPTION);
}

// 008.插入表指定列数比查询表列数多  预期：插入失败
TEST_F(TSDBInsertIntoConstrains, Timing_016_TSDBInsertIntoConstrains_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    // 建目的表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, name char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameIn);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建源表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, name char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 源表写数据
    constexpr int64_t count = 10;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t age[count] = {0};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704038400 + i;
        age[i] = 18;
    }
    ret = BlukInsert_char(stmt, tableNameOut, count, 4, id, time, age, name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 执行insert into语句
    (void)sprintf(
        sqlCmd, "insert into %s (id, time, age, name) select id, time, age from %s;", tableNameIn, tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    // 补充删表动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameIn));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameOut));
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_INVALID_PARAMETER_VALUE, GMERR_DATA_EXCEPTION);
}

// 009.插入表指定列中有重复列  预期：插入失败
TEST_F(TSDBInsertIntoConstrains, Timing_016_TSDBInsertIntoConstrains_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    // 建目的表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, name char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameIn);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建源表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, name char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 源表写数据
    constexpr int64_t count = 10;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t age[count] = {0};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704038400 + i;
        age[i] = 18;
    }
    ret = BlukInsert_char(stmt, tableNameOut, count, 4, id, time, age, name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 执行insert into语句
    (void)sprintf(sqlCmd, "insert into %s (id, time, id, name) select * from %s;", tableNameIn, tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DUPLICATE_COLUMN, ret);
    // 补充删表动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameIn));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameOut));
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DUPLICATE_COLUMN, GMERR_DATA_EXCEPTION);
}

// 010.插入表指定列不包含时间列  预期：插入失败
TEST_F(TSDBInsertIntoConstrains, Timing_016_TSDBInsertIntoConstrains_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    // 建目的表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, name char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameIn);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建源表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, name char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 源表写数据
    constexpr int64_t count = 10;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t age[count] = {0};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704038400 + i;
        age[i] = 18;
    }
    ret = BlukInsert_char(stmt, tableNameOut, count, 4, id, time, age, name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 执行insert into语句
    (void)sprintf(sqlCmd, "insert into %s (id, age, name) select id, age, name from %s;",
        // 检验用"insert into %s (time) select time from %s;",
        tableNameIn, tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    // 补充删表动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameIn));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameOut));
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_INVALID_PARAMETER_VALUE, GMERR_DATA_EXCEPTION);
}

// 011.插入表未指定列，查询指定列数大于插入表全部列数  预期：插入失败
TEST_F(TSDBInsertIntoConstrains, Timing_016_TSDBInsertIntoConstrains_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    // 建目的表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameIn);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建源表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, name char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 源表写数据
    constexpr int64_t count = 10;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t age[count] = {0};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704038400 + i;
        age[i] = 18;
    }
    ret = BlukInsert_char(stmt, tableNameOut, count, 4, id, time, age, name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 执行insert into语句
    (void)sprintf(sqlCmd, "insert into %s select id, time, age, name from %s;", tableNameIn, tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    // 补充删表动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameIn));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameOut));
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_INVALID_PARAMETER_VALUE, GMERR_DATA_EXCEPTION);
}

// 012.插入表未指定列，查询指定列数的等于插入表全部列数，查表  预期：插入成功，查询结果正确
TEST_F(TSDBInsertIntoConstrains, Timing_016_TSDBInsertIntoConstrains_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    // 建目的表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, remark char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameIn);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建源表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, remark char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 源表写数据
    constexpr int64_t count = 10;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t age[count] = {0};
    char *remark = (char *)malloc(count * 64);
    char remarkSource[10][64] = {"ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai",
        "ShangHai", "ShangHai", "ShangHai"};
    memcpy(remark, (char *)remarkSource, 640);
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704038400 + i;
        age[i] = 18;
    }
    ret = BlukInsert_char(stmt, tableNameOut, count, 4, id, time, age, remark);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(remark);
    // 执行insert into语句
    (void)sprintf(sqlCmd, "insert into %s select id, time, age, remark from %s;", tableNameIn, tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查表
    (void)sprintf(sqlCmd, "SELECT * FROM %s;", tableNameIn);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 10;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(count, dataCount);
    // 验证查询结果
    int64_t cPersonId = 0;
    int64_t cPersonTime = 0;
    int64_t cPersonAge = 0;
    char cPersonRemark[64] = {0};
    uint32_t size = sizeof(int64_t);
    int fetchTimes = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof) {
            break;
        }
        size = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &cPersonId, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(fetchTimes, cPersonId);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &cPersonTime, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(fetchTimes + 1704038400, cPersonTime);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &cPersonAge, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(18, cPersonAge);
        size = sizeof(cPersonRemark);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &cPersonRemark, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_STR("ShangHai", cPersonRemark);
        fetchTimes++;
    }
    // 补充删表动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameIn));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameOut));
}

// 013.插入表未指定列，查询指定列数的小于插入表全部列数，查表  预期：插入成功，查询结果正确
TEST_F(TSDBInsertIntoConstrains, Timing_016_TSDBInsertIntoConstrains_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    // 建目的表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, remark char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameIn);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建源表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, remark char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 源表写数据
    constexpr int64_t count = 10;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t age[count] = {0};
    char *remark = (char *)malloc(count * 64);
    char remarkSource[10][64] = {"ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai",
        "ShangHai", "ShangHai", "ShangHai"};
    memcpy(remark, (char *)remarkSource, 640);
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704038400 + i;
        age[i] = 18;
    }
    ret = BlukInsert_char(stmt, tableNameOut, count, 4, id, time, age, remark);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(remark);
    // 执行insert into语句
    (void)sprintf(sqlCmd, "insert into %s select id, time, age from %s;", tableNameIn, tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查表
    (void)sprintf(sqlCmd, "SELECT * FROM %s;", tableNameIn);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 10;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(count, dataCount);
    // 验证查询结果
    int64_t cPersonId = 0;
    int64_t cPersonTime = 0;
    int64_t cPersonAge = 0;
    char cPersonRemark[64] = {0};
    uint32_t size = sizeof(int64_t);
    int fetchTimes = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof) {
            break;
        }
        size = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &cPersonId, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(fetchTimes, cPersonId);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &cPersonTime, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(fetchTimes + 1704038400, cPersonTime);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &cPersonAge, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(18, cPersonAge);
        size = sizeof(cPersonRemark);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &cPersonRemark, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_STR("", cPersonRemark);
        fetchTimes++;
    }
    // 补充删表动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameIn));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameOut));
}

// 014.插入表时，查询结果列包含一个常量值，查表  预期：插入成功，查询结果正确
TEST_F(TSDBInsertIntoConstrains, Timing_016_TSDBInsertIntoConstrains_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    // 建目的表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, remark char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameIn);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建源表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, remark char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 源表写数据
    constexpr int64_t count = 10;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t age[count] = {0};
    char *remark = (char *)malloc(count * 64);
    char remarkSource[10][64] = {"ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai",
        "ShangHai", "ShangHai", "ShangHai"};
    memcpy(remark, (char *)remarkSource, 640);
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704038400 + i;
        age[i] = 18;
    }
    ret = BlukInsert_char(stmt, tableNameOut, count, 4, id, time, age, remark);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(remark);
    // 执行insert into语句
    (void)sprintf(sqlCmd, "insert into %s select id, time, 19, remark from %s;", tableNameIn, tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查表
    (void)sprintf(sqlCmd, "SELECT * FROM %s;", tableNameIn);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 10;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(count, dataCount);
    // 验证查询结果
    int64_t cPersonId = 0;
    int64_t cPersonTime = 0;
    int64_t cPersonAge = 0;
    char cPersonRemark[64] = {0};
    uint32_t size = sizeof(int64_t);
    int fetchTimes = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof) {
            break;
        }
        size = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &cPersonId, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(fetchTimes, cPersonId);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &cPersonTime, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(fetchTimes + 1704038400, cPersonTime);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &cPersonAge, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(19, cPersonAge);
        size = sizeof(cPersonRemark);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &cPersonRemark, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_STR("ShangHai", cPersonRemark);
        fetchTimes++;
    }
    // 补充删表动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameIn));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameOut));
}

// 015.插入表时，查询结果列包含多个常量值，查表  预期：插入成功，查询结果正确
TEST_F(TSDBInsertIntoConstrains, Timing_016_TSDBInsertIntoConstrains_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    // 建目的表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, remark char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameIn);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建源表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, remark char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 源表写数据
    constexpr int64_t count = 10;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t age[count] = {0};
    char *remark = (char *)malloc(count * 64);
    char remarkSource[10][64] = {"ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai",
        "ShangHai", "ShangHai", "ShangHai"};
    memcpy(remark, (char *)remarkSource, 640);
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704038400 + i;
        age[i] = 18;
    }
    ret = BlukInsert_char(stmt, tableNameOut, count, 4, id, time, age, remark);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(remark);
    // 执行insert into语句
    (void)sprintf(sqlCmd, "insert into %s select 10, time, 19, remark from %s;", tableNameIn, tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查表
    (void)sprintf(sqlCmd, "SELECT * FROM %s;", tableNameIn);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 10;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(count, dataCount);
    // 验证查询结果
    int64_t cPersonId = 0;
    int64_t cPersonTime = 0;
    int64_t cPersonAge = 0;
    char cPersonRemark[64] = {0};
    uint32_t size = sizeof(int64_t);
    int fetchTimes = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof) {
            break;
        }
        size = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &cPersonId, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(10, cPersonId);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &cPersonTime, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(fetchTimes + 1704038400, cPersonTime);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &cPersonAge, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(19, cPersonAge);
        size = sizeof(cPersonRemark);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &cPersonRemark, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_STR("ShangHai", cPersonRemark);
        fetchTimes++;
    }
    // 补充删表动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameIn));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameOut));
}

// 016.插入表和查询表为同一张表  预期：插入失败，预期报错
TEST_F(TSDBInsertIntoConstrains, Timing_016_TSDBInsertIntoConstrains_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    // 建目的表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, remark char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameIn);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建源表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, remark char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 源表写数据
    constexpr int64_t count = 10;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t age[count] = {0};
    char *remark = (char *)malloc(count * 64);
    char remarkSource[10][64] = {"ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai",
        "ShangHai", "ShangHai", "ShangHai"};
    memcpy(remark, (char *)remarkSource, 640);
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704038400 + i;
        age[i] = 18;
    }
    ret = BlukInsert_char(stmt, tableNameOut, count, 4, id, time, age, remark);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(remark);
    // 执行insert into语句
    (void)sprintf(sqlCmd, "insert into %s select id, time, age, remark from %s;", tableNameOut, tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONSTRAINT_CHECK_VIOLATION, ret);
    // 查表
    (void)sprintf(sqlCmd, "SELECT * FROM %s;", tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 10;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(count, dataCount);
    // 验证查询结果
    int64_t cPersonId = 0;
    int64_t cPersonTime = 0;
    int64_t cPersonAge = 0;
    char cPersonRemark[64] = {0};
    uint32_t size = sizeof(int64_t);
    int fetchTimes = 0;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof) {
            break;
        }
        size = sizeof(int64_t);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &cPersonId, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(fetchTimes, cPersonId);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &cPersonTime, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(fetchTimes + 1704038400, cPersonTime);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &cPersonAge, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(18, cPersonAge);
        size = sizeof(cPersonRemark);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &cPersonRemark, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_STR("ShangHai", cPersonRemark);
        fetchTimes++;
    }
    // 补充删表动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameIn));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameOut));
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_CONSTRAINT_CHECK_VIOLATION, GMERR_DATA_EXCEPTION);
}

typedef struct {
    GmcStmtT *stmt;
    char *testTableIn;
    char *testTableOut;
} InsretIntoBody;

void *InsertIntoConcurrent(void *arg)
{
    InsretIntoBody insretIntoBody = *(InsretIntoBody *)arg;
    char sqlCmd[256] = {0};
    // 执行insert into语句
    (void)sprintf(sqlCmd, "insert into %s select * from %s;", insretIntoBody.testTableIn, insretIntoBody.testTableOut);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(insretIntoBody.stmt, sqlCmd, cmdLen);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_UNEXPECTED_NULL_VALUE, ret);
    }
    return nullptr;
}

// 017.A、B两边循环调用insert into，查询结果无法保证  预期：插入动作执行成功
TEST_F(TSDBInsertIntoConstrains, Timing_016_TSDBInsertIntoConstrains_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int threadCount = 2;
    pthread_t tid[threadCount];
    char sqlCmd[512] = {0};
    // 建目的表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, remark char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameIn);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建源表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, remark char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 源表和目的表写数据
    constexpr int64_t count = 5000;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t age[count] = {0};
    char *remark = (char *)malloc(count * 64);
    char remarkSource[10][64] = {"ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai",
        "ShangHai", "ShangHai", "ShangHai"};
    memcpy(remark, (char *)remarkSource, 640);
    for (int i = 0; i < count; i++) {
        id[i] = i;
        if ((i % 2) == 0) {
            time[i] = 1704038400;
        } else {
            time[i] = 1704040000;
        }
        age[i] = 18;
    }
    ret = BlukInsert_char(stmt, tableNameOut, count, 4, id, time, age, remark);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BlukInsert_char(stmt, tableNameIn, count, 4, id, time, age, remark);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(remark);

    static GmcConnT *conn_1 = NULL;
    static GmcStmtT *stmt_1 = NULL;
    ret = TestTsGmcConnect(&conn_1, &stmt_1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    static GmcConnT *conn_2 = NULL;
    static GmcStmtT *stmt_2 = NULL;
    ret = TestTsGmcConnect(&conn_2, &stmt_2, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置好源表和目的表
    InsretIntoBody insretIntoBody = {stmt_1, tableNameIn, tableNameOut};
    InsretIntoBody insretIntoBody_1 = {stmt_2, tableNameOut, tableNameIn};
    pthread_create(&tid[0], NULL, InsertIntoConcurrent, &insretIntoBody);
    pthread_create(&tid[1], NULL, InsertIntoConcurrent, &insretIntoBody_1);

    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    ret = testGmcDisconnect(conn_1, stmt_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_2, stmt_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 补充删表动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameIn));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameOut));
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_REQUEST_TIME_OUT, GMERR_DATA_EXCEPTION);
}

void *BulkInsertConcurrent(void *arg)
{
    InsretIntoBody insretIntoBody = *(InsretIntoBody *)arg;
    constexpr int64_t count = 100000;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t age[count] = {0};

    for (int i = 0; i < count; i++) {
        id[i] = 1;
        time[i] = 1704038400;
        age[i] = 18;
    }
    for (int i = 0; i < 1; i++) {
        ret = BlukInsert(insretIntoBody.stmt, insretIntoBody.testTableOut, count, 3, id, time, age);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    }
    return nullptr;
}

// 018.插入表源表和DML并发，查询结果无法保证  预期：插入成功
TEST_F(TSDBInsertIntoConstrains, Timing_016_TSDBInsertIntoConstrains_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int threadCount = 2;
    pthread_t tid[threadCount];
    char sqlCmd[512] = {0};
    // 建目的表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, remark char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameIn);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建源表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, remark char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 源表写数据
    constexpr int64_t count = 100000;
    // 执行插入插入次数，用于验证最终结果
    int insertTimes = 50;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t age[count] = {0};
    char *remark = (char *)malloc(count * 64);
    char remarkSource[10][64] = {"ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai",
        "ShangHai", "ShangHai", "ShangHai"};
    memcpy(remark, (char *)remarkSource, 640);
    for (int i = 0; i < count; i++) {
        id[i] = 1;
        if ((i % 2) == 0) {
            time[i] = 1704038400;
        } else {
            time[i] = 1704040000;
        }
        age[i] = 18;
    }
    for (int i = 0; i < insertTimes; i++) {
        ret = BlukInsert_char(stmt, tableNameOut, count, 4, id, time, age, remark);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    free(remark);
    static GmcConnT *conn_1 = NULL;
    static GmcStmtT *stmt_1 = NULL;
    ret = TestTsGmcConnect(&conn_1, &stmt_1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    static GmcConnT *conn_2 = NULL;
    static GmcStmtT *stmt_2 = NULL;
    ret = TestTsGmcConnect(&conn_2, &stmt_2, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置好源表和目的表
    InsretIntoBody insretIntoBody = {stmt_1, tableNameIn, tableNameOut};
    InsretIntoBody insretIntoBody_1 = {stmt_2, tableNameIn, tableNameOut};
    pthread_create(&tid[0], NULL, InsertIntoConcurrent, &insretIntoBody);
    pthread_create(&tid[1], NULL, BulkInsertConcurrent, &insretIntoBody_1);

    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    ret = testGmcDisconnect(conn_1, stmt_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_2, stmt_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查表
    (void)sprintf(sqlCmd, "SELECT time FROM %s;", tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入次数*单次插入数量与最终目的表内数据数量不一致
    AW_MACRO_ASSERT_NE_INT((insertTimes * count), dataCount);
    // 补充删表动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameIn));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameOut));
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_PROGRAM_LIMIT_EXCEEDED);
}

void *QueryConcurrent(void *arg)
{
    InsretIntoBody insretIntoBody = *(InsretIntoBody *)arg;
    char sqlCmd[512] = {0};
    // 查表
    (void)sprintf(sqlCmd, "SELECT time FROM %s;", insretIntoBody.testTableOut);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(insretIntoBody.stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(insretIntoBody.stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入次数*单次插入数量与最终目的表内数据数量不一致
    if (dataCount != 0) {
        AW_MACRO_EXPECT_EQ_INT(5000000, dataCount);
    }
    return nullptr;
}

// 019.插入表源表和DQL并发  预期：插入成功，查询结果正确
TEST_F(TSDBInsertIntoConstrains, Timing_016_TSDBInsertIntoConstrains_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int threadCount = 2;
    pthread_t tid[threadCount];
    char sqlCmd[512] = {0};
    // 建目的表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, remark char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameIn);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建源表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, remark char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 源表写数据
    constexpr int64_t count = 100000;
    // 执行插入插入次数，用于验证最终结果
    int insertTimes = 50;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t age[count] = {0};
    char *remark = (char *)malloc(count * 64);
    char remarkSource[10][64] = {"ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai",
        "ShangHai", "ShangHai", "ShangHai"};
    memcpy(remark, (char *)remarkSource, 640);
    for (int i = 0; i < count; i++) {
        id[i] = 1;
        if ((i % 2) == 0) {
            time[i] = 1704038400;
        } else {
            time[i] = 1704040000;
        }
        age[i] = 18;
    }
    for (int i = 0; i < insertTimes; i++) {
        ret = BlukInsert_char(stmt, tableNameOut, count, 4, id, time, age, remark);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    free(remark);
    static GmcConnT *conn_1 = NULL;
    static GmcStmtT *stmt_1 = NULL;
    ret = TestTsGmcConnect(&conn_1, &stmt_1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    static GmcConnT *conn_2 = NULL;
    static GmcStmtT *stmt_2 = NULL;
    ret = TestTsGmcConnect(&conn_2, &stmt_2, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置好源表和目的表
    InsretIntoBody insretIntoBody = {stmt_1, tableNameIn, tableNameOut};
    InsretIntoBody insretIntoBody_1 = {stmt_2, tableNameOut, tableNameOut};
    pthread_create(&tid[0], NULL, InsertIntoConcurrent, &insretIntoBody);
    pthread_create(&tid[1], NULL, QueryConcurrent, &insretIntoBody_1);

    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    ret = testGmcDisconnect(conn_1, stmt_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_2, stmt_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查表
    (void)sprintf(sqlCmd, "SELECT time FROM %s;", tableNameIn);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入次数*单次插入数量与最终目的表内数据数量不一致
    AW_MACRO_EXPECT_EQ_INT(insertTimes * count, dataCount);
    // 补充删表动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameIn));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameOut));
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_PROGRAM_LIMIT_EXCEEDED);
}

void *DropTableConcurrent(void *arg)
{
    InsretIntoBody insretIntoBody = *(InsretIntoBody *)arg;
    char sqlCmd[512] = {0};
    // 查表
    (void)sprintf(sqlCmd, "drop table %s;", insretIntoBody.testTableOut);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(insretIntoBody.stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return nullptr;
}

// 020.插入表源表和DDL并发  预期：insert into执行成功，ddl执行成功
TEST_F(TSDBInsertIntoConstrains, Timing_016_TSDBInsertIntoConstrains_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int threadCount = 2;
    pthread_t tid[threadCount];
    char sqlCmd[512] = {0};
    // 建目的表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, remark char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameIn);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建源表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, remark char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 源表写数据
    constexpr int64_t count = 150;
    // 执行插入插入次数，用于验证最终结果
    int insertTimes = 3;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t age[count] = {0};
    char *remark = (char *)malloc(count * 64);
    char remarkSource[10][64] = {"ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai",
        "ShangHai", "ShangHai", "ShangHai"};
    memcpy(remark, (char *)remarkSource, 640);
    for (int i = 0; i < count; i++) {
        id[i] = 1;
        time[i] = 1704038400 + i * 3600;
        age[i] = 18;
    }
    for (int i = 0; i < insertTimes; i++) {
        ret = BlukInsert_char(stmt, tableNameOut, count, 4, id, time, age, remark);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    free(remark);
    static GmcConnT *conn_1 = NULL;
    static GmcStmtT *stmt_1 = NULL;
    ret = TestTsGmcConnect(&conn_1, &stmt_1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    static GmcConnT *conn_2 = NULL;
    static GmcStmtT *stmt_2 = NULL;
    ret = TestTsGmcConnect(&conn_2, &stmt_2, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置好源表和目的表
    InsretIntoBody insretIntoBody = {stmt_1, tableNameIn, tableNameOut};
    InsretIntoBody insretIntoBody_1 = {stmt_2, tableNameOut, tableNameOut};
    pthread_create(&tid[0], NULL, InsertIntoConcurrent, &insretIntoBody);
    // 确保删表动作在insert之后拉起
    usleep(50 * 1000);
    pthread_create(&tid[1], NULL, DropTableConcurrent, &insretIntoBody_1);

    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    ret = testGmcDisconnect(conn_1, stmt_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_2, stmt_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查表
    (void)sprintf(sqlCmd, "SELECT time FROM %s;", tableNameIn);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //  insert into必定执行成功，目的表有源表全量数据
    AW_MACRO_EXPECT_EQ_INT(insertTimes * count, dataCount);
    DropCmTable(tableNameIn);
    DropCmTable(tableNameOut);
}

int getDataCsvCount()
{
    int flag = 0;
    int count = 0;
    char tail;
    if (FILE *file = fopen("data.csv", "r")) {
        while ((flag = fgetc(file)) != EOF) {
            if (flag == '\n')
                count++;
            tail = flag;
        }
        if (tail != '\n')
            count++;
        return count;
    }
    return -1;
}

void *CopyToConcurrent(void *arg)
{
    InsretIntoBody insretIntoBody = *(InsretIntoBody *)arg;
    system("rm -rf data.csv");
    char sqlCmd[256] = {0};
    sprintf(sqlCmd,
        "COPY (SELECT * FROM %s) TO"
        "'%s/test/sdv/testcases/25_Timing/016_tsdb_insert_into/data.csv';",
        insretIntoBody.testTableOut, dir);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, 256);
    int count = getDataCsvCount();
    if (ret != GMERR_OK) {
        // 执行失败时判断为csv文件不存在
        AW_MACRO_EXPECT_EQ_INT(-1, count);
    } else {
        // 执行成功时判断为csv文件存在
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (count != 1) {
            AW_MACRO_EXPECT_EQ_INT(5000000, count);
        }
        AW_FUN_Log(LOG_INFO, "data.csv is existed? answer is %d\n", count);
    }
    return nullptr;
}

// 021.插入表源表和copy to并发  预期：插入成功，查询结果正确
TEST_F(TSDBInsertIntoConstrains, Timing_016_TSDBInsertIntoConstrains_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int threadCount = 2;
    pthread_t tid[threadCount];
    char sqlCmd[512] = {0};
    // 建目的表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, remark char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameIn);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建源表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, remark char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 源表写数据
    constexpr int64_t count = 100000;
    // 执行插入插入次数，用于验证最终结果
    int insertTimes = 50;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t age[count] = {0};
    char *remark = (char *)malloc(count * 64);
    char remarkSource[10][64] = {"ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai",
        "ShangHai", "ShangHai", "ShangHai"};
    memcpy(remark, (char *)remarkSource, 640);
    for (int i = 0; i < count; i++) {
        id[i] = i;
        if ((i % 2) == 0) {
            time[i] = 1704038400;
        } else {
            time[i] = 1704040000;
        }
        age[i] = 18;
    }
    for (int i = 0; i < insertTimes; i++) {
        ret = BlukInsert_char(stmt, tableNameOut, count, 4, id, time, age, remark);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    free(remark);
    static GmcConnT *conn_1 = NULL;
    static GmcStmtT *stmt_1 = NULL;
    ret = TestTsGmcConnect(&conn_1, &stmt_1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    static GmcConnT *conn_2 = NULL;
    static GmcStmtT *stmt_2 = NULL;
    ret = TestTsGmcConnect(&conn_2, &stmt_2, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置好源表和目的表
    InsretIntoBody insretIntoBody = {stmt_1, tableNameIn, tableNameOut};
    InsretIntoBody insretIntoBody_1 = {stmt_2, tableNameOut, tableNameOut};
    pthread_create(&tid[0], NULL, InsertIntoConcurrent, &insretIntoBody);
    pthread_create(&tid[1], NULL, CopyToConcurrent, &insretIntoBody_1);

    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    ret = testGmcDisconnect(conn_1, stmt_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_2, stmt_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查表
    (void)sprintf(sqlCmd, "SELECT time FROM %s;", tableNameIn);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "selected data num is %ld\n", dataCount);
    AW_MACRO_EXPECT_EQ_INT(insertTimes * count, dataCount);
    // 补充删表动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameIn));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameOut));
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_PROGRAM_LIMIT_EXCEEDED);
}

// 022.insert into并发两个被插入表的源表为同一张源表  预期：插入成功，查询结果正确
TEST_F(TSDBInsertIntoConstrains, Timing_016_TSDBInsertIntoConstrains_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int threadCount = 2;
    pthread_t tid[threadCount];
    char sqlCmd[512] = {0};
    // 建目的表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, remark char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameIn);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, remark char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建源表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, remark char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 源表写数据
    constexpr int64_t count = 100000;
    // 执行插入插入次数，用于验证最终结果
    int insertTimes = 50;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t age[count] = {0};
    char *remark = (char *)malloc(count * 64);
    char remarkSource[10][64] = {"ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai",
        "ShangHai", "ShangHai", "ShangHai"};
    memcpy(remark, (char *)remarkSource, 640);
    for (int i = 0; i < count; i++) {
        id[i] = i;
        if ((i % 2) == 0) {
            time[i] = 1704038400;
        } else {
            time[i] = 1704040000;
        }
        age[i] = 18;
    }
    for (int i = 0; i < insertTimes; i++) {
        ret = BlukInsert_char(stmt, tableNameOut, count, 4, id, time, age, remark);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    free(remark);
    static GmcConnT *conn_1 = NULL;
    static GmcStmtT *stmt_1 = NULL;
    ret = TestTsGmcConnect(&conn_1, &stmt_1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    static GmcConnT *conn_2 = NULL;
    static GmcStmtT *stmt_2 = NULL;
    ret = TestTsGmcConnect(&conn_2, &stmt_2, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置好源表和目的表
    InsretIntoBody insretIntoBody = {stmt_1, tableNameIn, tableNameOut};
    InsretIntoBody insretIntoBody_1 = {stmt_2, tableName, tableNameOut};
    pthread_create(&tid[0], NULL, InsertIntoConcurrent, &insretIntoBody);
    pthread_create(&tid[1], NULL, InsertIntoConcurrent, &insretIntoBody_1);

    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    ret = testGmcDisconnect(conn_1, stmt_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_2, stmt_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查表
    (void)sprintf(sqlCmd, "SELECT time FROM %s;", tableNameIn);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(insertTimes * count, dataCount);

    (void)sprintf(sqlCmd, "SELECT time FROM %s;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(insertTimes * count, dataCount);
    // 补充删表动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameIn));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameOut));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableName));
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_PROGRAM_LIMIT_EXCEEDED);
}

// 023.insert into并发，插入表的源表作为另一个插入动作的被插入表，查询结果无法保证  预期：插入成功
TEST_F(TSDBInsertIntoConstrains, Timing_016_TSDBInsertIntoConstrains_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int threadCount = 2;
    pthread_t tid[threadCount];
    char sqlCmd[512] = {0};
    // 建目的表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, remark char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameIn);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, remark char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建源表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, remark char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 源表写数据
    constexpr int64_t count = 100000;
    // 执行插入插入次数，用于验证最终结果
    int insertTimes = 50;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t age[count] = {0};
    char *remark = (char *)malloc(count * 64);
    char remarkSource[10][64] = {"ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai",
        "ShangHai", "ShangHai", "ShangHai"};
    memcpy(remark, (char *)remarkSource, 640);
    for (int i = 0; i < count; i++) {
        id[i] = i;
        if ((i % 2) == 0) {
            time[i] = 1704038400;
        } else {
            time[i] = 1704040000;
        }
        age[i] = 18;
    }
    for (int i = 0; i < insertTimes; i++) {
        ret = BlukInsert_char(stmt, tableNameOut, count, 4, id, time, age, remark);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = BlukInsert_char(stmt, tableNameIn, count, 4, id, time, age, remark);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    free(remark);

    static GmcConnT *conn_1 = NULL;
    static GmcStmtT *stmt_1 = NULL;
    ret = TestTsGmcConnect(&conn_1, &stmt_1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    static GmcConnT *conn_2 = NULL;
    static GmcStmtT *stmt_2 = NULL;
    ret = TestTsGmcConnect(&conn_2, &stmt_2, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置好源表和目的表
    InsretIntoBody insretIntoBody = {stmt_1, tableNameIn, tableNameOut};
    InsretIntoBody insretIntoBody_1 = {stmt_2, tableName, tableNameIn};
    pthread_create(&tid[0], NULL, InsertIntoConcurrent, &insretIntoBody);
    pthread_create(&tid[1], NULL, InsertIntoConcurrent, &insretIntoBody_1);

    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    ret = testGmcDisconnect(conn_1, stmt_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_2, stmt_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查表
    (void)sprintf(sqlCmd, "SELECT time FROM %s;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_LE(insertTimes * count, dataCount);
    // 补充删表动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameIn));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameOut));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableName));
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_PROGRAM_LIMIT_EXCEEDED);
}

// 024.插入表目的表和DML并发，查询目的表  预期：插入成功，查询结果正确
TEST_F(TSDBInsertIntoConstrains, Timing_016_TSDBInsertIntoConstrains_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int threadCount = 2;
    pthread_t tid[threadCount];
    char sqlCmd[512] = {0};
    // 建目的表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, remark char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameIn);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建源表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, remark char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 源表写数据
    constexpr int64_t count = 100000;
    // 执行插入插入次数，用于验证最终结果
    int insertTimes = 50;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t age[count] = {0};
    char *remark = (char *)malloc(count * 64);
    char remarkSource[10][64] = {"ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai",
        "ShangHai", "ShangHai", "ShangHai"};
    memcpy(remark, (char *)remarkSource, 640);
    for (int i = 0; i < count; i++) {
        id[i] = 1;
        if ((i % 2) == 0) {
            time[i] = 1704038400;
        } else {
            time[i] = 1704040000;
        }
        age[i] = 18;
    }
    for (int i = 0; i < insertTimes; i++) {
        ret = BlukInsert_char(stmt, tableNameOut, count, 4, id, time, age, remark);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    free(remark);
    static GmcConnT *conn_1 = NULL;
    static GmcStmtT *stmt_1 = NULL;
    ret = TestTsGmcConnect(&conn_1, &stmt_1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    static GmcConnT *conn_2 = NULL;
    static GmcStmtT *stmt_2 = NULL;
    ret = TestTsGmcConnect(&conn_2, &stmt_2, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置好源表和目的表
    InsretIntoBody insretIntoBody = {stmt_1, tableNameIn, tableNameOut};
    InsretIntoBody insretIntoBody_1 = {stmt_2, tableNameIn, tableNameIn};
    pthread_create(&tid[0], NULL, InsertIntoConcurrent, &insretIntoBody);
    pthread_create(&tid[1], NULL, BulkInsertConcurrent, &insretIntoBody_1);

    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    ret = testGmcDisconnect(conn_1, stmt_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_2, stmt_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查表
    (void)sprintf(sqlCmd, "SELECT time FROM %s;", tableNameIn);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入次数*单次插入数量与最终目的表内数据数量不一致
    AW_MACRO_EXPECT_EQ_INT((insertTimes * count) + 100000, dataCount);
    // 补充删表动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameIn));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameOut));
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_PROGRAM_LIMIT_EXCEEDED);
}

// 025.插入表目的表和DQL并发，稳定后查询目的表  预期：插入成功，查询结果正确
TEST_F(TSDBInsertIntoConstrains, Timing_016_TSDBInsertIntoConstrains_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int threadCount = 2;
    pthread_t tid[threadCount];
    char sqlCmd[512] = {0};
    // 建目的表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, remark char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameIn);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建源表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, remark char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 源表写数据
    constexpr int64_t count = 100000;
    // 执行插入插入次数，用于验证最终结果
    int insertTimes = 50;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t age[count] = {0};
    char *remark = (char *)malloc(count * 64);
    char remarkSource[10][64] = {"ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai",
        "ShangHai", "ShangHai", "ShangHai"};
    memcpy(remark, (char *)remarkSource, 640);
    for (int i = 0; i < count; i++) {
        id[i] = 1;
        if ((i % 2) == 0) {
            time[i] = 1704038400;
        } else {
            time[i] = 1704040000;
        }
        age[i] = 18;
    }
    for (int i = 0; i < insertTimes; i++) {
        ret = BlukInsert_char(stmt, tableNameOut, count, 4, id, time, age, remark);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    free(remark);
    static GmcConnT *conn_1 = NULL;
    static GmcStmtT *stmt_1 = NULL;
    ret = TestTsGmcConnect(&conn_1, &stmt_1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    static GmcConnT *conn_2 = NULL;
    static GmcStmtT *stmt_2 = NULL;
    ret = TestTsGmcConnect(&conn_2, &stmt_2, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置好源表和目的表
    InsretIntoBody insretIntoBody = {stmt_1, tableNameIn, tableNameOut};
    InsretIntoBody insretIntoBody_1 = {stmt_2, tableNameIn, tableNameIn};
    pthread_create(&tid[0], NULL, InsertIntoConcurrent, &insretIntoBody);
    pthread_create(&tid[1], NULL, QueryConcurrent, &insretIntoBody_1);

    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    ret = testGmcDisconnect(conn_1, stmt_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_2, stmt_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查表
    (void)sprintf(sqlCmd, "SELECT time FROM %s;", tableNameIn);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入次数*单次插入数量与最终目的表内数据数量不一致
    AW_MACRO_EXPECT_EQ_INT(insertTimes * count, dataCount);
    // 补充删表动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameIn));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameOut));
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_PROGRAM_LIMIT_EXCEEDED);
}

// 026.插入表目的表和DDL并发  预期：insert into执行成功，ddl执行成功
TEST_F(TSDBInsertIntoConstrains, Timing_016_TSDBInsertIntoConstrains_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int threadCount = 2;
    pthread_t tid[threadCount];
    char sqlCmd[512] = {0};
    // 建目的表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, remark char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameIn);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建源表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, remark char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 源表写数据
    constexpr int64_t count = 150;
    // 执行插入插入次数，用于验证最终结果
    int insertTimes = 3;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t age[count] = {0};
    char *remark = (char *)malloc(count * 64);
    char remarkSource[10][64] = {"ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai",
        "ShangHai", "ShangHai", "ShangHai"};
    memcpy(remark, (char *)remarkSource, 640);
    for (int i = 0; i < count; i++) {
        id[i] = 1;
        time[i] = 1704038400 + 3600 * i;
        age[i] = 18;
    }
    for (int i = 0; i < insertTimes; i++) {
        ret = BlukInsert_char(stmt, tableNameOut, count, 4, id, time, age, remark);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    free(remark);
    static GmcConnT *conn_1 = NULL;
    static GmcStmtT *stmt_1 = NULL;
    ret = TestTsGmcConnect(&conn_1, &stmt_1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    static GmcConnT *conn_2 = NULL;
    static GmcStmtT *stmt_2 = NULL;
    ret = TestTsGmcConnect(&conn_2, &stmt_2, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置好源表和目的表
    InsretIntoBody insretIntoBody = {stmt_1, tableNameIn, tableNameOut};
    InsretIntoBody insretIntoBody_1 = {stmt_2, tableNameIn, tableNameIn};
    pthread_create(&tid[0], NULL, InsertIntoConcurrent, &insretIntoBody);
    // 确保insert into先被拉起
    usleep(50 * 1000);
    pthread_create(&tid[1], NULL, DropTableConcurrent, &insretIntoBody_1);

    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    ret = testGmcDisconnect(conn_1, stmt_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_2, stmt_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查表
    (void)sprintf(sqlCmd, "SELECT time FROM %s;", tableNameIn);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    DropCmTable(tableNameOut);
    DropCmTable(tableNameIn);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNDEFINED_TABLE);
}

// 027.插入表目的表和copy to并发，copy to结果无法保证  预期：插入成功，查询结果正确
TEST_F(TSDBInsertIntoConstrains, Timing_016_TSDBInsertIntoConstrains_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int threadCount = 2;
    pthread_t tid[threadCount];
    char sqlCmd[512] = {0};
    // 建目的表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, remark char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameIn);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建源表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, remark char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 源表写数据
    constexpr int64_t count = 100000;
    // 执行插入插入次数，用于验证最终结果
    int insertTimes = 50;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t age[count] = {0};
    char *remark = (char *)malloc(count * 64);
    char remarkSource[10][64] = {"ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai",
        "ShangHai", "ShangHai", "ShangHai"};
    memcpy(remark, (char *)remarkSource, 640);
    for (int i = 0; i < count; i++) {
        id[i] = i;
        if ((i % 2) == 0) {
            time[i] = 1704038400;
        } else {
            time[i] = 1704040000;
        }
        age[i] = 18;
    }
    for (int i = 0; i < insertTimes; i++) {
        ret = BlukInsert_char(stmt, tableNameOut, count, 4, id, time, age, remark);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    free(remark);
    static GmcConnT *conn_1 = NULL;
    static GmcStmtT *stmt_1 = NULL;
    ret = TestTsGmcConnect(&conn_1, &stmt_1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    static GmcConnT *conn_2 = NULL;
    static GmcStmtT *stmt_2 = NULL;
    ret = TestTsGmcConnect(&conn_2, &stmt_2, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置好源表和目的表
    InsretIntoBody insretIntoBody = {stmt_1, tableNameIn, tableNameOut};
    InsretIntoBody insretIntoBody_1 = {stmt_2, tableNameIn, tableNameIn};
    pthread_create(&tid[0], NULL, InsertIntoConcurrent, &insretIntoBody);
    pthread_create(&tid[1], NULL, CopyToConcurrent, &insretIntoBody_1);

    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    ret = testGmcDisconnect(conn_1, stmt_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_2, stmt_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查表
    (void)sprintf(sqlCmd, "SELECT time FROM %s;", tableNameIn);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(insertTimes * count, dataCount);
    // 补充删表动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameIn));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameOut));
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_PROGRAM_LIMIT_EXCEEDED);
}

// 028.insert into并发两个被插入表的目的表为同一张目的表  预期：插入成功，查询结果正确
TEST_F(TSDBInsertIntoConstrains, Timing_016_TSDBInsertIntoConstrains_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int threadCount = 2;
    pthread_t tid[threadCount];
    char sqlCmd[512] = {0};
    // 建目的表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, remark char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameIn);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, remark char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建源表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, remark char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 源表写数据
    constexpr int64_t count = 100000;
    // 执行插入插入次数，用于验证最终结果
    int insertTimes = 50;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t age[count] = {0};
    char *remark = (char *)malloc(count * 64);
    char remarkSource[10][64] = {"ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai",
        "ShangHai", "ShangHai", "ShangHai"};
    memcpy(remark, (char *)remarkSource, 640);
    for (int i = 0; i < count; i++) {
        id[i] = i;
        if ((i % 2) == 0) {
            time[i] = 1704038400;
        } else {
            time[i] = 1704040000;
        }
        age[i] = 18;
    }
    for (int i = 0; i < insertTimes; i++) {
        ret = BlukInsert_char(stmt, tableNameOut, count, 4, id, time, age, remark);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = BlukInsert_char(stmt, tableName, count, 4, id, time, age, remark);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    free(remark);
    static GmcConnT *conn_1 = NULL;
    static GmcStmtT *stmt_1 = NULL;
    ret = TestTsGmcConnect(&conn_1, &stmt_1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    static GmcConnT *conn_2 = NULL;
    static GmcStmtT *stmt_2 = NULL;
    ret = TestTsGmcConnect(&conn_2, &stmt_2, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置好源表和目的表
    InsretIntoBody insretIntoBody = {stmt_1, tableNameIn, tableNameOut};
    InsretIntoBody insretIntoBody_1 = {stmt_2, tableNameIn, tableName};
    pthread_create(&tid[0], NULL, InsertIntoConcurrent, &insretIntoBody);
    pthread_create(&tid[1], NULL, InsertIntoConcurrent, &insretIntoBody_1);

    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    ret = testGmcDisconnect(conn_1, stmt_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_2, stmt_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查表
    (void)sprintf(sqlCmd, "SELECT time FROM %s;", tableNameIn);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((insertTimes * count) * 2, dataCount);
    // 补充删表动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameIn));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameOut));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableName));
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_PROGRAM_LIMIT_EXCEEDED);
}

// 029.insert into并发，插入表的目的表作为另一个插入动作的源表，第二个插入结果无法保证
// 预期：插入成功，第一个insert动作目的表查询结果正确
TEST_F(TSDBInsertIntoConstrains, Timing_016_TSDBInsertIntoConstrains_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int threadCount = 2;
    pthread_t tid[threadCount];
    char sqlCmd[512] = {0};
    // 建目的表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, remark char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameIn);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, remark char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建源表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, remark char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 源表写数据
    constexpr int64_t count = 100000;
    // 执行插入插入次数，用于验证最终结果
    int insertTimes = 50;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t age[count] = {0};
    char *remark = (char *)malloc(count * 64);
    char remarkSource[10][64] = {"ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai", "ShangHai",
        "ShangHai", "ShangHai", "ShangHai"};
    memcpy(remark, (char *)remarkSource, 640);
    for (int i = 0; i < count; i++) {
        id[i] = i;
        if ((i % 2) == 0) {
            time[i] = 1704038400;
        } else {
            time[i] = 1704040000;
        }
        age[i] = 18;
    }
    for (int i = 0; i < insertTimes; i++) {
        ret = BlukInsert_char(stmt, tableNameOut, count, 4, id, time, age, remark);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = BlukInsert_char(stmt, tableNameIn, count, 4, id, time, age, remark);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    free(remark);

    static GmcConnT *conn_1 = NULL;
    static GmcStmtT *stmt_1 = NULL;
    ret = TestTsGmcConnect(&conn_1, &stmt_1, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    static GmcConnT *conn_2 = NULL;
    static GmcStmtT *stmt_2 = NULL;
    ret = TestTsGmcConnect(&conn_2, &stmt_2, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置好源表和目的表
    InsretIntoBody insretIntoBody = {stmt_1, tableNameIn, tableNameOut};
    InsretIntoBody insretIntoBody_1 = {stmt_2, tableName, tableNameIn};
    pthread_create(&tid[0], NULL, InsertIntoConcurrent, &insretIntoBody);
    pthread_create(&tid[1], NULL, InsertIntoConcurrent, &insretIntoBody_1);

    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    ret = testGmcDisconnect(conn_1, stmt_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_2, stmt_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查表
    (void)sprintf(sqlCmd, "SELECT time FROM %s;", tableNameIn);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((insertTimes * count) * 2, dataCount);

    (void)sprintf(sqlCmd, "SELECT time FROM %s;", tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dataCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_LE((insertTimes * count), dataCount);
    // 补充删表动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameIn));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameOut));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableName));
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_PROGRAM_LIMIT_EXCEEDED);
}

// 030.insert into关键字间无空格  预期：执行失败
TEST_F(TSDBInsertIntoConstrains, Timing_016_TSDBInsertIntoConstrains_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    // 建目的表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameIn);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建源表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, name char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 源表写数据
    constexpr int64_t count = 10;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t age[count] = {0};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704038400 + i;
        age[i] = 18;
    }
    ret = BlukInsert_char(stmt, tableNameOut, count, 4, id, time, age, name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 执行insert into语句
    (void)sprintf(sqlCmd, "insertinto %s (time, age) select time, age from %s;", tableNameIn, tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    // 补充删表动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameIn));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameOut));
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_SYNTAX_ERROR, GMERR_DATA_EXCEPTION);
}

// 031.insert into未指定目标表  预期：执行失败
TEST_F(TSDBInsertIntoConstrains, Timing_016_TSDBInsertIntoConstrains_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    // 建目的表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameIn);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建源表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, name char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 源表写数据
    constexpr int64_t count = 10;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t age[count] = {0};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704038400 + i;
        age[i] = 18;
    }
    ret = BlukInsert_char(stmt, tableNameOut, count, 4, id, time, age, name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 执行insert into语句
    (void)sprintf(sqlCmd, "insert into (time, age) select time, age from %s;", tableNameIn, tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    // 补充删表动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameIn));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameOut));
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_SYNTAX_ERROR, GMERR_DATA_EXCEPTION);
}

// 032.insert into中DQL语句错误  预期：执行失败
TEST_F(TSDBInsertIntoConstrains, Timing_016_TSDBInsertIntoConstrains_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    // 建目的表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameIn);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建源表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, name char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 源表写数据
    constexpr int64_t count = 10;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t age[count] = {0};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704038400 + i;
        age[i] = 18;
    }
    ret = BlukInsert_char(stmt, tableNameOut, count, 4, id, time, age, name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 执行insert into语句
    (void)sprintf(sqlCmd, "insert into %s (time, age) select time, age from %s where;", tableNameIn, tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    // 补充删表动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameIn));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameOut));
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_SYNTAX_ERROR, GMERR_DATA_EXCEPTION);
}

// 033.insert into指定多张表  预期：执行失败
TEST_F(TSDBInsertIntoConstrains, Timing_016_TSDBInsertIntoConstrains_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    // 建目的表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameIn);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer) with (time_col = "
        "'time', interval = '1 hour');",
        tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建源表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, name char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 源表写数据
    constexpr int64_t count = 10;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t age[count] = {0};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704038400 + i;
        age[i] = 18;
    }
    ret = BlukInsert_char(stmt, tableNameOut, count, 4, id, time, age, name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 执行insert into语句
    (void)sprintf(sqlCmd, "insert into %s (time, age), %s (time, age) select time, age from %s;", tableNameIn,
        tableName, tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    // 补充删表动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameIn));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameOut));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableName));
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_SYNTAX_ERROR, GMERR_DATA_EXCEPTION);
}

// 034.插入表只指定时间列  预期：插入成功
TEST_F(TSDBInsertIntoConstrains, Timing_016_TSDBInsertIntoConstrains_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[512] = {0};
    // 建目的表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, name char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameIn);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建源表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, age integer, name char(64)) with (time_col = "
        "'time', interval = '1 hour');",
        tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 源表写数据
    constexpr int64_t count = 10;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    int64_t age[count] = {0};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1704038400 + i;
        age[i] = 18;
    }
    ret = BlukInsert_char(stmt, tableNameOut, count, 4, id, time, age, name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 执行insert into语句
    (void)sprintf(sqlCmd, "insert into %s (time) select time from %s;", tableNameIn, tableNameOut);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 补充删表动作
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameIn));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, DropTsTable(stmt, tableNameOut));
}
