// sub test
extern "C" {
}
#include <stdlib.h>
#include <stdio.h>
// #include <string>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
//#include "DefaultSize.h"
#include <time.h>
#include <sys/time.h>
#include "t_datacom_lite.h"
#define MAX_NAME_LENGTH 128
#define THREAD_NUM 10
#define MAX_NUM_WAIT 1
#if defined(ENV_RTOSV2X)
    int MAX_NUM = 24;
#else
    int MAX_NUM = 64;
#endif

GmcConnT *conn = NULL;
GmcStmtT *stmt = NULL;
GmcTxConfigT MSTrxConfig;
//订阅句柄
//订阅连接名称
const char *g_subName = "subVertexLabel";
SnUserDataT *user_data;
// int user_data->subIndex=0;//user_data的数组下标

int start_num = 0;
int end_num = 100;
char *test_schema1 = NULL;
char *Normal_subinfo = NULL;
GmcConnT *g_subChan[1024];
GmcStmtT *g_stmt_sub[1024];
GmcConnT *g_conn1[MAX_CONN_SIZE];
GmcStmtT *g_stmt1[MAX_CONN_SIZE];
void set_VertexProperty_PK(GmcStmtT *stmt, int i)
{
    int ret = 0;
    int64_t f0_value = i;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
}

void set_VertexProperty(GmcStmtT *stmt, int i, bool bool_value, char *f14_value)
{
    int ret = 0;

    uint64_t f1_value = i;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    int32_t f2_value = 2 * i;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f3_value = 3 * i;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int16_t f4_value = 4 * i;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f5_value = 5 * i;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t f6_value = 6 * i;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f7_value = 7 * i;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    char f12_value = 12 * i;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f13_value = 13 * i;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);
}

void sn_callback1(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int index, i;
    char labelName[MAX_NAME_LENGTH] = {0};        //表名数组,128个
    unsigned int labelNamelen = MAX_NAME_LENGTH;  //名字长度为128
    void *keyValue = 0, *conn_sync = 0, *stmt_sync = 0;
    char keyName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;  //用户数据结构体
    //扫描句柄中数据
    bool eof = false;
    while (!eof) {
        int ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }
        for (int i = 0; i < info->labelCount; i++)  //信息结构体中表数量？
        {
            memset(labelName, 0, sizeof(labelName));
            labelNamelen = MAX_NAME_LENGTH;
            switch (info->eventType)  //当前订阅支持的事件类型只包括DML，不包括DDL？
            {
                case GMC_SUB_EVENT_INSERT: {
                    ret = GmcSubSetFetchMode(
                        subStmt, GMC_SUB_FETCH_NEW);  //当前接口为增量订阅，获取旧顶点和新顶点区别是什么？
                    EXPECT_EQ(GMERR_OK, ret);
                    if (ret != 0) {
                        ret = testGmcGetLastError(NULL);
                        EXPECT_EQ(GMERR_OK, ret);
                    }
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    // printf("thread %d [NEW/OLD OBJECT] GMC_SUB_EVENT_INSERT new_value is %d\r\n",user_data->threadId,
                    // index);//如果生产出新数据，读结构体中的数据？
                    ret = GmcSubSetFetchMode(
                        subStmt, GMC_SUB_FETCH_OLD);  //为什么这里扫旧数据会出错，旧数据是指老化数据还是？
                    EXPECT_EQ(GMERR_NO_DATA, ret);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    //读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_NO_DATA, ret);
                    //读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_DELETE old_value is %d\r\n", index);
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    //读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_UPDATE new_value is %d\r\n", index);
                    //读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_UPDATE old_value is %d\r\n", index);
                    break;
                }
                case GMC_SUB_EVENT_REPLACE: {
                    //读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_REPLACE new_value is %d\r\n", index);

                    //读old
                    if (((bool *)user_data->isReplace_insert)[user_data->subIndex]) {
                        printf("[NEW OBJECT] GMC_SUB_EVENT_REPLACE insert\r\n");
                        ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                        EXPECT_EQ(GMERR_OK, ret);
                        char *pValue = (char *)malloc(sizeof(int32_t));
                        bool isNull = 0;
                        ret = GmcGetVertexPropertyByName(subStmt, "F6", pValue, sizeof(int32_t), &isNull);
                        EXPECT_EQ(GMERR_OK, ret);
                        EXPECT_EQ(1, isNull);
                        free(pValue);
                    } else {
                        printf("[NEW OBJECT] GMC_SUB_EVENT_REPLACE update\r\n");
                        ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                        EXPECT_EQ(GMERR_OK, ret);
                        index = ((int *)user_data->old_value)[user_data->subIndex];
                    }
                    break;
                }
                default: {
                    printf("default: invalid eventType\r\n");
                    break;
                }
            }
            break;
        }
        user_data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}

void sn_callback2(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int index, i;
    char labelName[MAX_NAME_LENGTH] = {0};        //表名数组,128个
    unsigned int labelNamelen = MAX_NAME_LENGTH;  //名字长度为128
    void *keyValue = 0, *conn_sync = 0, *stmt_sync = 0;
    char keyName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;  //用户数据结构体
    usleep(10000);
    //扫描句柄中数据
    bool eof = false;
    while (!eof) {
        int ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }
        for (int i = 0; i < info->labelCount; i++)  //信息结构体中表数量？
        {
            memset(labelName, 0, sizeof(labelName));
            labelNamelen = MAX_NAME_LENGTH;
            switch (info->eventType)  //当前订阅支持的事件类型只包括DML，不包括DDL？
            {
                case GMC_SUB_EVENT_INSERT: {
                    ret = GmcSubSetFetchMode(
                        subStmt, GMC_SUB_FETCH_NEW);  //当前接口为增量订阅，获取旧顶点和新顶点区别是什么？
                    EXPECT_EQ(GMERR_OK, ret);
                    if (ret != 0) {
                        ret = testGmcGetLastError(NULL);
                        EXPECT_EQ(GMERR_OK, ret);
                    }
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    // printf("thread %d [NEW/OLD OBJECT] GMC_SUB_EVENT_INSERT new_value is %d\r\n",user_data->threadId,
                    // index);//如果生产出新数据，读结构体中的数据？
                    ret = GmcSubSetFetchMode(
                        subStmt, GMC_SUB_FETCH_OLD);  //为什么这里扫旧数据会出错，旧数据是指老化数据还是？
                    EXPECT_EQ(GMERR_NO_DATA, ret);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    //读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_NO_DATA, ret);
                    //读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_DELETE old_value is %d\r\n", index);
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    //读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_UPDATE new_value is %d\r\n", index);
                    //读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_UPDATE old_value is %d\r\n", index);
                    break;
                }
                case GMC_SUB_EVENT_REPLACE: {
                    //读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_REPLACE new_value is %d\r\n", index);

                    //读old
                    if (((bool *)user_data->isReplace_insert)[user_data->subIndex]) {
                        printf("[NEW OBJECT] GMC_SUB_EVENT_REPLACE insert\r\n");
                        ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                        EXPECT_EQ(GMERR_OK, ret);
                        char *pValue = (char *)malloc(sizeof(int32_t));
                        bool isNull = 0;
                        ret = GmcGetVertexPropertyByName(subStmt, "F6", pValue, sizeof(int32_t), &isNull);
                        EXPECT_EQ(GMERR_OK, ret);
                        EXPECT_EQ(1, isNull);
                        free(pValue);
                    } else {
                        printf("[NEW OBJECT] GMC_SUB_EVENT_REPLACE update\r\n");
                        ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                        EXPECT_EQ(GMERR_OK, ret);
                        index = ((int *)user_data->old_value)[user_data->subIndex];
                    }
                    break;
                }
                default: {
                    printf("default: invalid eventType\r\n");
                    break;
                }
            }
            break;
        }
        user_data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}
void sn_callback3(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int index, i;
    char labelName[MAX_NAME_LENGTH] = {0};        //表名数组,128个
    unsigned int labelNamelen = MAX_NAME_LENGTH;  //名字长度为128
    void *keyValue = 0, *conn_sync = 0, *stmt_sync = 0;
    char keyName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;  //用户数据结构体
    usleep(100000);
    //扫描句柄中数据
    bool eof = false;
    while (!eof) {
        int ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }
        for (int i = 0; i < info->labelCount; i++)  //信息结构体中表数量？
        {
            memset(labelName, 0, sizeof(labelName));
            labelNamelen = MAX_NAME_LENGTH;
            switch (info->eventType)  //当前订阅支持的事件类型只包括DML，不包括DDL？
            {
                case GMC_SUB_EVENT_INSERT: {
                    ret = GmcSubSetFetchMode(
                        subStmt, GMC_SUB_FETCH_NEW);  //当前接口为增量订阅，获取旧顶点和新顶点区别是什么？
                    EXPECT_EQ(GMERR_OK, ret);
                    if (ret != 0) {
                        ret = testGmcGetLastError(NULL);
                        EXPECT_EQ(GMERR_OK, ret);
                    }
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    // printf("thread %d [NEW/OLD OBJECT] GMC_SUB_EVENT_INSERT new_value is %d\r\n",user_data->threadId,
                    // index);//如果生产出新数据，读结构体中的数据？
                    ret = GmcSubSetFetchMode(
                        subStmt, GMC_SUB_FETCH_OLD);  //为什么这里扫旧数据会出错，旧数据是指老化数据还是？
                    EXPECT_EQ(GMERR_NO_DATA, ret);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    //读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_NO_DATA, ret);
                    //读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_DELETE old_value is %d\r\n", index);
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    //读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_UPDATE new_value is %d\r\n", index);
                    //读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_UPDATE old_value is %d\r\n", index);
                    break;
                }
                case GMC_SUB_EVENT_REPLACE: {
                    //读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_REPLACE new_value is %d\r\n", index);

                    //读old
                    if (((bool *)user_data->isReplace_insert)[user_data->subIndex]) {
                        printf("[NEW OBJECT] GMC_SUB_EVENT_REPLACE insert\r\n");
                        ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                        EXPECT_EQ(GMERR_OK, ret);
                        char *pValue = (char *)malloc(sizeof(int32_t));
                        bool isNull = 0;
                        ret = GmcGetVertexPropertyByName(subStmt, "F6", pValue, sizeof(int32_t), &isNull);
                        EXPECT_EQ(GMERR_OK, ret);
                        EXPECT_EQ(1, isNull);
                        free(pValue);
                    } else {
                        printf("[NEW OBJECT] GMC_SUB_EVENT_REPLACE update\r\n");
                        ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                        EXPECT_EQ(GMERR_OK, ret);
                        index = ((int *)user_data->old_value)[user_data->subIndex];
                    }
                    break;
                }
                default: {
                    printf("default: invalid eventType\r\n");
                    break;
                }
            }
            break;
        }
        user_data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}
void sn_callback4(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int index, i;
    char labelName[MAX_NAME_LENGTH] = {0};        //表名数组,128个
    unsigned int labelNamelen = MAX_NAME_LENGTH;  //名字长度为128
    void *keyValue = 0, *conn_sync = 0, *stmt_sync = 0;
    char keyName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;  //用户数据结构体
    usleep(500000);
    //扫描句柄中数据
    bool eof = false;
    while (!eof) {
        int ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }
        for (int i = 0; i < info->labelCount; i++)  //信息结构体中表数量？
        {
            memset(labelName, 0, sizeof(labelName));
            labelNamelen = MAX_NAME_LENGTH;
            switch (info->eventType)  //当前订阅支持的事件类型只包括DML，不包括DDL？
            {
                case GMC_SUB_EVENT_INSERT: {
                    ret = GmcSubSetFetchMode(
                        subStmt, GMC_SUB_FETCH_NEW);  //当前接口为增量订阅，获取旧顶点和新顶点区别是什么？
                    EXPECT_EQ(GMERR_OK, ret);
                    if (ret != 0) {
                        ret = testGmcGetLastError(NULL);
                        EXPECT_EQ(GMERR_OK, ret);
                    }
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    // printf("thread %d [NEW/OLD OBJECT] GMC_SUB_EVENT_INSERT new_value is %d\r\n",user_data->threadId,
                    // index);//如果生产出新数据，读结构体中的数据？
                    ret = GmcSubSetFetchMode(
                        subStmt, GMC_SUB_FETCH_OLD);  //为什么这里扫旧数据会出错，旧数据是指老化数据还是？
                    EXPECT_EQ(GMERR_NO_DATA, ret);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    //读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_NO_DATA, ret);
                    //读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_DELETE old_value is %d\r\n", index);
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    //读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_UPDATE new_value is %d\r\n", index);
                    //读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_UPDATE old_value is %d\r\n", index);
                    break;
                }
                case GMC_SUB_EVENT_REPLACE: {
                    //读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_REPLACE new_value is %d\r\n", index);

                    //读old
                    if (((bool *)user_data->isReplace_insert)[user_data->subIndex]) {
                        printf("[NEW OBJECT] GMC_SUB_EVENT_REPLACE insert\r\n");
                        ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                        EXPECT_EQ(GMERR_OK, ret);
                        char *pValue = (char *)malloc(sizeof(int32_t));
                        bool isNull = 0;
                        ret = GmcGetVertexPropertyByName(subStmt, "F6", pValue, sizeof(int32_t), &isNull);
                        EXPECT_EQ(GMERR_OK, ret);
                        EXPECT_EQ(1, isNull);
                        free(pValue);
                    } else {
                        printf("[NEW OBJECT] GMC_SUB_EVENT_REPLACE update\r\n");
                        ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                        EXPECT_EQ(GMERR_OK, ret);
                        index = ((int *)user_data->old_value)[user_data->subIndex];
                    }
                    break;
                }
                default: {
                    printf("default: invalid eventType\r\n");
                    break;
                }
            }
            break;
        }
        user_data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}
class schedule_sub : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        // system("sh $TEST_HOME/tools/modifyCfg.sh \"agePushSubsBatch=10\"");
        system("sh $TEST_HOME/tools/start.sh");
    }
    static void TearDownTestCase()
    {
        int ret = 0;
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void schedule_sub::SetUp()
{
    int ret = 0;
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void schedule_sub::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}
#define CMD_LEN 1024

int ret;
const char *DFXCmd =
    g_runMode ?
        "gmsysview -q V\\$DRT_CONN_STAT -u user123 -p password.123 -e DAP -s channel:" :
        "gmsysview -q V\\$DRT_CONN_STAT -u user123 -p password.123 -e RTOS -s usocket:/run/verona/unix_emserver";

typedef struct {
    uint32_t SEND_BUFF_SIZE;
    uint32_t RECV_BUFF_SIZE;
    uint32_t SEND_BUFF_USED_SIZE;
    uint32_t RECV_BUFF_USED_SIZE;
    uint32_t CONN_SHAREMEM_ALLOC_NUM;
    uint32_t CONN_SHAREMEM_FREE_NUM;
} connInfo;

#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];
bool isNull;
void getDRT_CONN_STAT_PURE()
{
    char const *view_name = "V\\$DRT_CONN_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -u %s -s %s -p %s  -q %s", g_toolPath, g_userName, g_connServer,
        g_passwd, view_name);
    printf("%s\n", g_command);
    system(g_command);
    memset(g_command, 0, sizeof(g_command));
}
void DFX(const char *connName = NULL)
{
    char cmd[CMD_LEN] = {0};
    if (connName == NULL) {
        system(DFXCmd);
    } else {
        sprintf(cmd, "%s -f \"NODE_NAME='%s'\"", DFXCmd, connName);
        system(DFXCmd);
    }
}
void execCmd(const char *cmd, char result[CMD_LEN])
{
    result[0] = '\0';
    extern int errno;

    FILE *fp = popen(cmd, "r");
    if (fp == NULL) {
        printf("[ERROR] popen create fp failed file=%s, line=%d, func=%s\n", __FILE__, __LINE__, __FUNCTION__);
        return;
    }

    char *p = fgets(result, CMD_LEN, fp);
    // int len = strlen(result);
    // result[(len > 0) ? (len - 1) : 0] = 0;
    pclose(fp);

    if (NULL == p)
        return;
    int len = strlen(result);
    if (len > 0 && '\r' == result[len - 1]) {
        result[len - 1] = 0;
    }
    return;
}
void getConnInfo(connInfo *info, const char *filter, const char *key = NULL, bool Exist = true)
{
    char cmd[CMD_LEN];
    char result[CMD_LEN] = {0};
    if (Exist == false) {  // 连接不存在
        ASSERT_NE((void *)NULL, key);
        sprintf(cmd, "%s -f %s | grep %s", DFXCmd, filter, key);
        result[0] = 0;
        execCmd(cmd, result);
        EXPECT_EQ(0, strlen(result));
    } else {  // 获取连接信息
        ASSERT_NE((void *)NULL, info);
        sprintf(cmd, "%s -f %s > tempFile.txt", DFXCmd, filter);
        system(cmd);
        sprintf(cmd, "cat tempFile.txt | grep SEND_BUFF_SIZE |awk '{printf $2}'");
        execCmd(cmd, result);
        info->SEND_BUFF_SIZE = atoi(result);
        sprintf(cmd, "cat tempFile.txt | grep RECV_BUFF_SIZE |awk '{printf $2}'");
        execCmd(cmd, result);
        info->RECV_BUFF_SIZE = atoi(result);
        sprintf(cmd, "cat tempFile.txt | grep SEND_BUFF_USED_SIZE |awk '{printf $2}'");
        execCmd(cmd, result);
        info->SEND_BUFF_USED_SIZE = atoi(result);
        sprintf(cmd, "cat tempFile.txt | grep RECV_BUFF_USED_SIZE |awk '{printf $2}'");
        execCmd(cmd, result);
        info->RECV_BUFF_USED_SIZE = atoi(result);
        sprintf(cmd, "cat tempFile.txt | grep CONN_SHAREMEM_ALLOC_NUM |awk '{printf $2}'");
        execCmd(cmd, result);
        info->CONN_SHAREMEM_ALLOC_NUM = atoi(result);
        sprintf(cmd, "cat tempFile.txt | grep CONN_SHAREMEM_FREE_NUM |awk '{printf $2}'");
        execCmd(cmd, result);
        info->CONN_SHAREMEM_FREE_NUM = atoi(result);
        //  printf("SEND_BUFF_SIZE:%d\nRECV_BUFF_SIZE:%d\nSEND_BUFF_USED_SIZE:%d\nRECV_BUFF_USED_SIZE:%d\nCONN_SHAREMEM_"
        //        "ALLOC_NUM:%d\nCONN_SHAREMEM_FREE_NUM:%d\n",
        //     info->SEND_BUFF_SIZE,
        //     info->RECV_BUFF_SIZE,
        //     info->SEND_BUFF_USED_SIZE,
        //     info->RECV_BUFF_USED_SIZE,
        //     info->CONN_SHAREMEM_ALLOC_NUM,
        //     info->CONN_SHAREMEM_FREE_NUM);
        system("rm -rf tempFile.txt");
    }
}
void *read_table_thread_func(void *args)
{
    int num = *((int *)args);
    ret = testGmcConnect(&g_conn1[num], &g_stmt1[num]);
    EXPECT_EQ(GMERR_OK, ret);
    bool isEof = false;
    char Normal_thread_names[1023];
    snprintf(Normal_thread_names, 15, "Normal%d", num);
    printf("read func:%s\n", Normal_thread_names);
    ret = testGmcPrepareStmtByLabelName(g_stmt1[num], Normal_thread_names, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt1[num], NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt1[num]);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t prop_size = 0;
    uint32_t prop_value = 0;
    while (!isEof) {
        ret = GmcFetch(g_stmt1[num], &isEof);
        EXPECT_EQ(GMERR_OK, ret);
        if (isEof == true) {
            break;
        }
        ret = GmcGetVertexPropertySizeByName(g_stmt1[num], "F0", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt1[num], "F0", &prop_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(g_conn1[num], g_stmt1[num]);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}
void *write_table_thread_func(void *args)
{
    int num = *((int *)args);
    int value = *((int *)args);
    GmcConnT *g_conn1[MAX_CONN_SIZE];
    GmcStmtT *g_stmt1[MAX_CONN_SIZE];
    GmcConnT *g_subChan[1024];
    GmcStmtT *g_stmt_sub[1024];
    char Sub_conn_names[1023];
    int userDataIdx = 0;
    int ret = testGmcConnect(&g_conn1[num], &g_stmt1[num]);
    EXPECT_EQ(GMERR_OK, ret);
    char Normal_subinfo[1023];
    char Normal_schema_names[1023];
    char sub_schema_names[1023];  // new

    SnUserDataT *user_data;
    testSnMallocUserData(&user_data, end_num * 3);
    user_data->threadId = num;
    char *sub_info = NULL;
    // for(int k=0;k<MAX_NUM;k++)
    //{
    snprintf(Normal_subinfo, 1023,
        "{\"name\":\"subVertexLabel%d\",\"label_name\":\"OP_T%d\",\"comment\":\"VertexLabel "
        "subscription\",\"type\":\"before_commit\",\"events\":[{\"type\":\"insert\",\"msgTypes\":[\"new object\", "
        "\"old object\"]},"
        "{\"type\":\"update\",\"msgTypes\":[\"new object\", \"old object\"]},{\"type\":\"delete\",\"msgTypes\":[\"new "
        "object\", \"old "
        "object\"]}],\"is_reliable\":true,\"is_path\":false,\"retry\":true,\"constraint\":{\"operator_type\":\"and\","
        "\"conditions\":[{\"property\":\"F0\"}]}}",
        num, num);
    //}
    // const char *subname="subVertexLabel";
    snprintf(sub_schema_names, 50, "subVertexLabel%d", num);  // new
    snprintf(Normal_schema_names, 15, "OP_T%d", num);
    //创建订阅连接
    snprintf(Sub_conn_names, 15, "subConnName%d", num);
    int chanRingLen = 256;
    ret = testSubConnect(&g_subChan[num], &g_stmt_sub[num], 1, g_epoll_reg_info, Sub_conn_names, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);
    GmcSubConfigT tmp_subInfo;
    tmp_subInfo.subsName = sub_schema_names;  // new
    tmp_subInfo.configJson = Normal_subinfo;
    ret = GmcSubscribe(g_stmt1[num], &tmp_subInfo, g_subChan[num], sn_callback1, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret != 0) {
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt1[num], Normal_schema_names, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t pk_value[1000];
    pk_value[0] = 100;
    for (int i = 1; i < 1000; i++) {
        pk_value[i] = 100 + i * 100;
    }
    for (int i = pk_value[value]; i < pk_value[value + 1]; i++) {
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;
    }
    uint32_t cnt = 0;
    for (uint32_t j = pk_value[value]; j < pk_value[value + 1]; j++) {
        // j=j+5;
        ret = GmcSetVertexProperty(g_stmt1[num], "F0", GMC_DATATYPE_UINT32, &j, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt1[num], "F1", GMC_DATATYPE_UINT32, &j, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // printf("xxxxx :%d\n",j);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(g_stmt1[num]);
        cnt++;
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    EXPECT_EQ(100, cnt);
    // //等待insert事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 100);
    EXPECT_EQ(GMERR_OK, ret);
    // 资源释放
    GmcFreeIndexKey(g_stmt1[num]);
    testSnFreeUserData(user_data);
    ret = GmcUnSubscribe(g_stmt1[num], sub_schema_names);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_subChan[num], g_stmt_sub[num]);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn1[num], g_stmt1[num]);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}
void *write_table_thread_func2(void *args)
{
    int num = *((int *)args);
    int value = *((int *)args);
    GmcConnT *g_conn1[MAX_CONN_SIZE];
    GmcStmtT *g_stmt1[MAX_CONN_SIZE];
    GmcConnT *g_subChan[1024];
    GmcStmtT *g_stmt_sub[1024];
    char Sub_conn_names[1023];
    int userDataIdx = 0;
    int ret = testGmcConnect(&g_conn1[num], &g_stmt1[num]);
    EXPECT_EQ(GMERR_OK, ret);
    char Normal_subinfo[1023];
    char Normal_schema_names[1023];
    char sub_schema_names[1023];  // new

    SnUserDataT *user_data;
    testSnMallocUserData(&user_data, end_num * 3);
    user_data->threadId = num;
    char *sub_info = NULL;
    // for(int k=0;k<MAX_NUM;k++)
    //{
    snprintf(Normal_subinfo, 1023,
        "{\"name\":\"subVertexLabel%d\",\"label_name\":\"OP_T%d\",\"comment\":\"VertexLabel "
        "subscription\",\"type\":\"before_commit\",\"events\":[{\"type\":\"insert\",\"msgTypes\":[\"new object\", "
        "\"old object\"]},"
        "{\"type\":\"update\",\"msgTypes\":[\"new object\", \"old object\"]},{\"type\":\"delete\",\"msgTypes\":[\"new "
        "object\", \"old "
        "object\"]}],\"is_reliable\":true,\"is_path\":false,\"retry\":true,\"constraint\":{\"operator_type\":\"and\","
        "\"conditions\":[{\"property\":\"F0\"}]}}",
        num, num);
    //}
    // const char *subname="subVertexLabel";
    snprintf(sub_schema_names, 50, "subVertexLabel%d", num);  // new
    snprintf(Normal_schema_names, 15, "OP_T%d", num);
    //创建订阅连接
    snprintf(Sub_conn_names, 15, "subConnName%d", num);
    int chanRingLen = 256;
    ret = testSubConnect(&g_subChan[num], &g_stmt_sub[num], 1, g_epoll_reg_info, Sub_conn_names, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);
    GmcSubConfigT tmp_subInfo;
    tmp_subInfo.subsName = sub_schema_names;  // new
    tmp_subInfo.configJson = Normal_subinfo;
    ret = GmcSubscribe(g_stmt1[num], &tmp_subInfo, g_subChan[num], sn_callback2, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret != 0) {
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt1[num], Normal_schema_names, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t pk_value[1000];
    pk_value[0] = 100;
    for (int i = 1; i < 1000; i++) {
        pk_value[i] = 100 + i * 100;
    }
    for (int i = pk_value[value]; i < pk_value[value + 1]; i++) {
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;
    }
    uint32_t cnt = 0;
    for (uint32_t j = pk_value[value]; j < pk_value[value + 1]; j++) {
        // j=j+5;
        ret = GmcSetVertexProperty(g_stmt1[num], "F0", GMC_DATATYPE_UINT32, &j, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt1[num], "F1", GMC_DATATYPE_UINT32, &j, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // printf("xxxxx :%d\n",j);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(g_stmt1[num]);
        cnt++;
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    EXPECT_EQ(100, cnt);
    // //等待insert事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 100);
    EXPECT_EQ(GMERR_OK, ret);
    // 资源释放
    GmcFreeIndexKey(g_stmt1[num]);
    testSnFreeUserData(user_data);
    ret = GmcUnSubscribe(g_stmt1[num], sub_schema_names);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_subChan[num], g_stmt_sub[num]);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn1[num], g_stmt1[num]);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}
void *write_table_thread_func3(void *args)
{
    int num = *((int *)args);
    int value = *((int *)args);
    GmcConnT *g_conn1[MAX_CONN_SIZE];
    GmcStmtT *g_stmt1[MAX_CONN_SIZE];
    GmcConnT *g_subChan[1024];
    GmcStmtT *g_stmt_sub[1024];
    char Sub_conn_names[1023];
    int userDataIdx = 0;
    int ret = testGmcConnect(&g_conn1[num], &g_stmt1[num]);
    EXPECT_EQ(GMERR_OK, ret);
    char Normal_subinfo[1023];
    char Normal_schema_names[1023];
    char sub_schema_names[1023];  // new

    SnUserDataT *user_data;
    testSnMallocUserData(&user_data, end_num * 3);
    user_data->threadId = num;
    char *sub_info = NULL;
    // for(int k=0;k<MAX_NUM;k++)
    //{
    snprintf(Normal_subinfo, 1023,
        "{\"name\":\"subVertexLabel%d\",\"label_name\":\"OP_T%d\",\"comment\":\"VertexLabel "
        "subscription\",\"type\":\"before_commit\",\"events\":[{\"type\":\"insert\",\"msgTypes\":[\"new object\", "
        "\"old object\"]},"
        "{\"type\":\"update\",\"msgTypes\":[\"new object\", \"old object\"]},{\"type\":\"delete\",\"msgTypes\":[\"new "
        "object\", \"old "
        "object\"]}],\"is_reliable\":true,\"is_path\":false,\"retry\":true,\"constraint\":{\"operator_type\":\"and\","
        "\"conditions\":[{\"property\":\"F0\"}]}}",
        num, num);
    //}
    // const char *subname="subVertexLabel";
    snprintf(sub_schema_names, 50, "subVertexLabel%d", num);  // new
    snprintf(Normal_schema_names, 15, "OP_T%d", num);
    //创建订阅连接
    snprintf(Sub_conn_names, 15, "subConnName%d", num);
    int chanRingLen = 256;
    ret = testSubConnect(&g_subChan[num], &g_stmt_sub[num], 1, g_epoll_reg_info, Sub_conn_names, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);
    GmcSubConfigT tmp_subInfo;
    tmp_subInfo.subsName = sub_schema_names;  // new
    tmp_subInfo.configJson = Normal_subinfo;
    ret = GmcSubscribe(g_stmt1[num], &tmp_subInfo, g_subChan[num], sn_callback3, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret != 0) {
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt1[num], Normal_schema_names, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t pk_value[1000];
    pk_value[0] = 100;
    for (int i = 1; i < 1000; i++) {
        pk_value[i] = 100 + i * 100;
    }
    for (int i = pk_value[value]; i < pk_value[value + 1]; i++) {
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;
    }
    uint32_t cnt = 0;
    for (uint32_t j = pk_value[value]; j < pk_value[value + 1]; j++) {
        // j=j+5;
        ret = GmcSetVertexProperty(g_stmt1[num], "F0", GMC_DATATYPE_UINT32, &j, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt1[num], "F1", GMC_DATATYPE_UINT32, &j, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // printf("xxxxx :%d\n",j);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(g_stmt1[num]);
        cnt++;
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    EXPECT_EQ(100, cnt);
    // //等待insert事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 100);
    EXPECT_EQ(GMERR_OK, ret);
    // 资源释放
    GmcFreeIndexKey(g_stmt1[num]);
    testSnFreeUserData(user_data);
    ret = GmcUnSubscribe(g_stmt1[num], sub_schema_names);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_subChan[num], g_stmt_sub[num]);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn1[num], g_stmt1[num]);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}
void *write_table_thread_func4(void *args)
{
    int num = *((int *)args);
    int value = *((int *)args);
    GmcConnT *g_conn1[MAX_CONN_SIZE];
    GmcStmtT *g_stmt1[MAX_CONN_SIZE];
    GmcConnT *g_subChan[1024];
    GmcStmtT *g_stmt_sub[1024];
    char Sub_conn_names[1023];
    int userDataIdx = 0;
    int ret = testGmcConnect(&g_conn1[num], &g_stmt1[num]);
    EXPECT_EQ(GMERR_OK, ret);
    char Normal_subinfo[1023];
    char Normal_schema_names[1023];
    char sub_schema_names[1023];  // new

    SnUserDataT *user_data;
    testSnMallocUserData(&user_data, end_num * 3);
    user_data->threadId = num;
    char *sub_info = NULL;
    // for(int k=0;k<MAX_NUM;k++)
    //{
    snprintf(Normal_subinfo, 1023,
        "{\"name\":\"subVertexLabel%d\",\"label_name\":\"OP_T%d\",\"comment\":\"VertexLabel "
        "subscription\",\"type\":\"before_commit\",\"events\":[{\"type\":\"insert\",\"msgTypes\":[\"new object\", "
        "\"old object\"]},"
        "{\"type\":\"update\",\"msgTypes\":[\"new object\", \"old object\"]},{\"type\":\"delete\",\"msgTypes\":[\"new "
        "object\", \"old "
        "object\"]}],\"is_reliable\":true,\"is_path\":false,\"retry\":true,\"constraint\":{\"operator_type\":\"and\","
        "\"conditions\":[{\"property\":\"F0\"}]}}",
        num, num);
    //}
    // const char *subname="subVertexLabel";
    snprintf(sub_schema_names, 50, "subVertexLabel%d", num);  // new
    snprintf(Normal_schema_names, 15, "OP_T%d", num);
    //创建订阅连接
    snprintf(Sub_conn_names, 15, "subConnName%d", num);
    int chanRingLen = 256;
    ret = testSubConnect(&g_subChan[num], &g_stmt_sub[num], 1, g_epoll_reg_info, Sub_conn_names, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);
    GmcSubConfigT tmp_subInfo;
    tmp_subInfo.subsName = sub_schema_names;  // new
    tmp_subInfo.configJson = Normal_subinfo;
    ret = GmcSubscribe(g_stmt1[num], &tmp_subInfo, g_subChan[num], sn_callback4, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret != 0) {
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt1[num], Normal_schema_names, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t pk_value[1000];
    pk_value[0] = 100;
    for (int i = 1; i < 1000; i++) {
        pk_value[i] = 100 + i * 100;
    }
    for (int i = pk_value[value]; i < pk_value[value + 1]; i++) {
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;
    }
    uint32_t cnt = 0;
    for (uint32_t j = pk_value[value]; j < pk_value[value + 1]; j++) {
        // j=j+5;
        ret = GmcSetVertexProperty(g_stmt1[num], "F0", GMC_DATATYPE_UINT32, &j, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt1[num], "F1", GMC_DATATYPE_UINT32, &j, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // printf("xxxxx :%d\n",j);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(g_stmt1[num]);
        cnt++;
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    EXPECT_EQ(100, cnt);
    // //等待insert事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 100);
    EXPECT_EQ(GMERR_OK, ret);
    // 资源释放
    GmcFreeIndexKey(g_stmt1[num]);
    testSnFreeUserData(user_data);
    ret = GmcUnSubscribe(g_stmt1[num], sub_schema_names);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_subChan[num], g_stmt_sub[num]);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn1[num], g_stmt1[num]);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}
void *write_thread_func(void *args)
{
    int num = *((int *)args);
    int value = *((int *)args);
    GmcConnT *g_conn1[MAX_CONN_SIZE];
    GmcStmtT *g_stmt1[MAX_CONN_SIZE];
    int userDataIdx = 0;
    int ret = testGmcConnect(&g_conn1[num], &g_stmt1[num]);
    EXPECT_EQ(GMERR_OK, ret);
    char Normal_schema_names[1023];
    snprintf(Normal_schema_names, 15, "OP_T%d", num);
    ret = testGmcPrepareStmtByLabelName(g_stmt1[num], Normal_schema_names, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t pk_value[1000];
    pk_value[0] = 100;
    for (int i = 1; i < 1000; i++) {
        pk_value[i] = 100 + i * 100;
    }

    uint32_t cnt = 0;
    for (uint32_t j = pk_value[value]; j < pk_value[value + 1]; j++) {
        ret = GmcSetVertexProperty(g_stmt1[num], "F0", GMC_DATATYPE_UINT32, &j, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt1[num], "F1", GMC_DATATYPE_UINT32, &j, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(g_stmt1[num]);
        cnt++;
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    EXPECT_EQ(100, cnt);
    ret = testGmcDisconnect(g_conn1[num], g_stmt1[num]);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}
void GetViewFieldResultFilter(const char *viewName, const char *fieldName, const char *filter, char *result, int resLen)
{
    char command[MAX_CMD_SIZE];
    snprintf(command, MAX_CMD_SIZE, "gmsysview -s %s -q %s -f %s |grep -E '%s' |awk -F '[:,]' '{print $2}'",
        g_connServer, viewName, filter, fieldName);
    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        printf("popen(%s) error./n", command);
    }
    EXPECT_NE((void *)NULL, pf);
    while (fgets(result, resLen, pf) != NULL) {
    }
    (void)pclose(pf);
}
int var_AVG_MSG_PROC_TIME;
int var_AVG_MSG_CPU_PROC_TIME;
int var_SEND_BUFF_USED_SIZE;
int var_RECV_BUFF_USED_SIZE;
int var_TOTAL_WAIT_TIME;

void *view_table_thread_func(void *args)
{
    var_SEND_BUFF_USED_SIZE = 0;
    var_RECV_BUFF_USED_SIZE = 0;
    GmcConnT *g_conn = NULL;
    GmcStmtT *g_stmt = NULL;
    char cmd[CMD_LEN];
    char result[CMD_LEN] = {0};
    int time = *((int *)args);
    int ret = 0;
    char tmp[1024];
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int sum2 = 0;
    int sum1 = 0;
    for (int i = 0; i < 9; i++) {
        char cmdOutput1[64] = {0};
        char cmdOutput2[64] = {0};
        char Conn_id_filter[64] = {0};
        (void)memset_s(Conn_id_filter, sizeof(Conn_id_filter), 0, sizeof(Conn_id_filter));
        (void)memset_s(cmdOutput2, sizeof(cmdOutput2), 0, sizeof(cmdOutput2));
        (void)memset_s(cmdOutput1, sizeof(cmdOutput1), 0, sizeof(cmdOutput1));
        int res = snprintf(Conn_id_filter, sizeof(Conn_id_filter), "CONN_ID=%d", i);
        if (res < 0) {
            printf("snprintf error!");
        }
        GetViewFieldResultFilter("V\\$DRT_CONN_STAT", "SEND_BUFF_USED_SIZE", Conn_id_filter, cmdOutput2, 64);
        GetViewFieldResultFilter("V\\$DRT_CONN_STAT", "RECV_BUFF_USED_SIZE", Conn_id_filter, cmdOutput1, 64);
        uint32_t max_time2 = atoi(cmdOutput2);
        uint32_t max_time1 = atoi(cmdOutput1);
        sum2 = sum2 + max_time2;
        sum1 = sum1 + max_time1;
        printf("SEND_BUFF_USED_SIZE %d\n", max_time2);
        printf("RECV_BUFF_USED_SIZE %d\n", max_time1);
        usleep(500000);
    }
    int avg2 = sum2 / 9;
    printf("SEND_BUFF_USED_SIZE avg is:%d\n", avg2);
    int avg1 = sum1 / 9;
    printf("RECV_BUFF_USED_SIZE avg is:%d\n", avg1);
    var_SEND_BUFF_USED_SIZE = avg2;
    var_RECV_BUFF_USED_SIZE = avg1;
    return NULL;
}
void *view_table_thread_func2(void *args)
{
    var_TOTAL_WAIT_TIME = 0;
    GmcConnT *g_conn = NULL;
    GmcStmtT *g_stmt = NULL;
    char cmd[CMD_LEN];
    char result[CMD_LEN] = {0};
    int time = *((int *)args);
    int ret = 0;
    char tmp[1024];
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int sum = 0;
    for (int i = 0; i < 9; i++) {
        char cmdOutput2[64] = {0};
        char Conn_id_filter[64] = {0};
        (void)memset_s(Conn_id_filter, sizeof(Conn_id_filter), 0, sizeof(Conn_id_filter));
        (void)memset_s(cmdOutput2, sizeof(cmdOutput2), 0, sizeof(cmdOutput2));
        int res = snprintf(Conn_id_filter, sizeof(Conn_id_filter), "CONN_ID=%d", i);
        if (res < 0) {
            printf("snprintf error!");
        }
        GetViewFieldResultFilter("V\\$DRT_SCHEDULE_STAT", "TOTAL_WAIT_TIME", Conn_id_filter, cmdOutput2, 64);
        uint32_t max_time = atoi(cmdOutput2);
        sum = sum + max_time;
        printf("%d\n", max_time);
        usleep(500000);
    }
    int avg = sum / 9;
    printf("avg is:%d\n", avg);
    var_TOTAL_WAIT_TIME = avg;
    return NULL;
}
void *view_table_thread_func3(void *args)
{
    var_AVG_MSG_PROC_TIME = 0;
    var_AVG_MSG_CPU_PROC_TIME = 0;
    GmcConnT *g_conn = NULL;
    GmcStmtT *g_stmt = NULL;
    char cmd[CMD_LEN];
    char result[CMD_LEN] = {0};
    int time = *((int *)args);
    int ret = 0;
    char tmp[1024];
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int sum2 = 0;
    int sum1 = 0;
    for (int i = 0; i < 9; i++) {
        char cmdOutput1[64] = {0};
        char cmdOutput2[64] = {0};
        char Conn_id_filter[64] = {0};
        (void)memset_s(Conn_id_filter, sizeof(Conn_id_filter), 0, sizeof(Conn_id_filter));
        (void)memset_s(cmdOutput2, sizeof(cmdOutput2), 0, sizeof(cmdOutput2));
        (void)memset_s(cmdOutput1, sizeof(cmdOutput1), 0, sizeof(cmdOutput1));
        int res = snprintf(Conn_id_filter, sizeof(Conn_id_filter), "CONN_ID=%d", i);
        if (res < 0) {
            printf("snprintf error!");
        }
        GetViewFieldResultFilter("V\\$DRT_CONN_STAT", "AVG_MSG_CPU_PROC_TIME", Conn_id_filter, cmdOutput2, 64);
        GetViewFieldResultFilter("V\\$DRT_CONN_STAT", "AVG_MSG_PROC_TIME", Conn_id_filter, cmdOutput1, 64);
        uint32_t max_time2 = atoi(cmdOutput2);
        uint32_t max_time1 = atoi(cmdOutput1);
        sum2 = sum2 + max_time2;
        sum1 = sum1 + max_time1;
        printf("AVG_MSG_CPU_PROC_TIME %d\n", max_time2);
        printf("AVG_MSG_PROC_TIME %d\n", max_time1);
        usleep(500000);
    }
    int avg2 = sum2 / 9;
    printf("AVG_MSG_CPU_PROC_TIME avg is:%d\n", avg2);
    int avg1 = sum1 / 9;
    printf("AVG_MSG_PROC_TIME avg is:%d\n", avg1);
    var_AVG_MSG_PROC_TIME = avg1;
    var_AVG_MSG_CPU_PROC_TIME = avg2;
    return NULL;
}
void *view_table_thread_func4(void *args)
{
    GmcConnT *g_conn = NULL;
    GmcStmtT *g_stmt = NULL;
    char cmd[CMD_LEN];
    char result[CMD_LEN] = {0};
    int ret = 0;
    char tmp[1024];
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    char const *view_name = "V\\$DRT_CONN_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
    printf("%s\n", g_command);
    uint32_t n = 1;
    // uint32_t arr_value[10]={};
    // uint32_t *arr_new;
    // int cursors=0;
    // uint32_t *arr_change=(uint32_t *)malloc((n)*sizeof(uint32_t));
    // memset(arr_change,0,1);
    for (int i = 0; i < 10; i++) {
        printf("No.%d scan start\n", i);
        uint32_t ACT_CNT = 0;
        sprintf(cmd, "%s  > tempFile3.txt", g_command);
        system(cmd);
        // system(g_command);
        FILE *pf = fopen("./tempFile3.txt", "r");
        int length;
        char DEFRAGMENTATION_CNT[512];

        int size = 0;
        char tmpBuff[512];
        char str[3][512];
        while (fgets(tmpBuff, sizeof(tmpBuff), pf) != NULL) {
            if (tmpBuff[0] == '#') {
                continue;
            }
            length = strlen(tmpBuff);
            while (length > 0 && (tmpBuff[length - 1] == '\n' || tmpBuff[length - 1] == '\r')) {
                tmpBuff[length - 1] = '\0';
                --length;
            }
            sscanf(tmpBuff, "%s %s", str[0], str[2]);
            if (str[0][0] == ' ' || str[2][0] == '\0') {
                continue;
            }
            if (0 == strcmp(str[0], "AVG_MSG_PROC_TIME:")) {
                ACT_CNT += atoi(str[2]);
                printf("in loop :%d\n", ACT_CNT);

                // cursors++;
                // arr_change[n-1]= atoi(str[2]);
                // if(cursors>=n)
                // {
                //     arr_change=(uint32_t *)realloc(arr_change,(n+1)*sizeof(uint32_t));
                // }
                // n++;
            }
            // system("cat /dev/null > tempFile3.txt");
        }
        printf("the sum is:%d\n", ACT_CNT);
        // for(int i=0;i<cursors;i++)//打印动态数组
        // {
        //     printf("loop %d:%d\n",i,arr_change[i]);
        // }
        // cursors=0;
        usleep(500000);
        printf("No.%d scan end\n", i);
    }
    return NULL;
}
void *sub_table_thread_func(void *args)
{
    int num = *((int *)args);
    int value = *((int *)args);
    GmcConnT *g_conn1[MAX_CONN_SIZE];
    GmcStmtT *g_stmt1[MAX_CONN_SIZE];
    GmcConnT *g_subChan[1024];
    GmcStmtT *g_stmt_sub[1024];
    char Sub_conn_names[1023];
    int userDataIdx = 0;
    int ret = testGmcConnect(&g_conn1[num], &g_stmt1[num]);
    EXPECT_EQ(GMERR_OK, ret);
    char Normal_subinfo[1023];
    char Normal_schema_names[1023];
    char sub_schema_names[1023];  // new

    SnUserDataT *user_data;
    testSnMallocUserData(&user_data, end_num * 3);
    user_data->threadId = num;
    char *sub_info = NULL;
    // for(int k=0;k<MAX_NUM;k++)
    // {
    //     snprintf(Normal_subinfo, 1023,
    //     "{\"name\":\"subVertexLabel%d\",\"label_name\":\"OP_T%d\",\"comment\":\"VertexLabel
    //     subscription\",\"type\":\"before_commit\",\"events\":[{\"type\":\"insert\"},"
    //         "{\"type\":\"update\"},{\"type\":\"delete\"}],\"is_reliable\":true,\"is_path\":false,\"retry\":true,\"constraint\":{\"operator_type\":\"and\",\"conditions\":[{\"property\":\"F0\"}]}}",k,k);
    // }
    for (int k = 0; k < MAX_NUM; k++) {
        snprintf(Normal_subinfo, 1023,
            "{\"name\":\"subVertexLabel%d\",\"label_name\":\"OP_T%d\",\"comment\":\"VertexLabel "
            "subscription\",\"type\":\"before_commit\",\"events\":[{\"type\":\"insert\",\"msgTypes\":[\"new object\", "
            "\"old object\"]},"
            "{\"type\":\"update\",\"msgTypes\":[\"new object\", \"old "
            "object\"]},{\"type\":\"delete\",\"msgTypes\":[\"new object\", \"old "
            "object\"]}],\"is_reliable\":true,\"is_path\":false,\"retry\":true,\"constraint\":{\"operator_type\":"
            "\"and\",\"conditions\":[{\"property\":\"F0\"}]}}",
            k, k);
    }
    // const char *subname="subVertexLabel";
    snprintf(sub_schema_names, 50, "subVertexLabel%d", num);  // new
    snprintf(Normal_schema_names, 15, "OP_T%d", num);
    //创建订阅连接
    snprintf(Sub_conn_names, 15, "subConnName%d", num);
    int chanRingLen = 256;
    ret = testSubConnect(&g_subChan[num], &g_stmt_sub[num], 1, g_epoll_reg_info, Sub_conn_names, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);
    GmcSubConfigT tmp_subInfo;
    tmp_subInfo.subsName = sub_schema_names;  // new
    tmp_subInfo.configJson = Normal_subinfo;
    ret = GmcSubscribe(g_stmt1[num], &tmp_subInfo, g_subChan[num], sn_callback1, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t pk_value[1000];
    pk_value[0] = 100;
    for (int i = 1; i < 1000; i++) {
        pk_value[i] = 100 + i * 100;
    }
    for (int i = pk_value[value]; i < pk_value[value + 1]; i++) {
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;
    }
    if (ret != 0) {
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // //等待insert事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 100);
    if (ret == -1 || ret == 0) {
        EXPECT_EQ(GMERR_OK, 0);
    }

    // 资源释放
    GmcFreeIndexKey(g_stmt1[num]);
    testSnFreeUserData(user_data);
    ret = GmcUnSubscribe(g_stmt1[num], sub_schema_names);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_subChan[num], g_stmt_sub[num]);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn1[num], g_stmt1[num]);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

void *write_func(void *args)
{
    int num = *((int *)args);
    int value = *((int *)args);
    int ret = testGmcConnect(&g_conn1[num], &g_stmt1[num]);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t pk_value[1000];
    pk_value[0] = 100;
    for (int i = 1; i < 1000; i++) {
        pk_value[i] = 100 + i * 100;
    }
    char Normal_thread_names[1023];
    snprintf(Normal_thread_names, 15, "Normal%d", num);
    printf("write func:%s\n", Normal_thread_names);
    ret = testGmcPrepareStmtByLabelName(g_stmt1[num], Normal_thread_names, GMC_OPERATION_INSERT);
    if (ret != 0) {
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        printf(" write func success:%s\n", Normal_thread_names);
    } else {
        printf(" write func success:%s\n", Normal_thread_names);
    }
    // assert(GMERR_OK==ret);
    for (uint32_t j = pk_value[value]; j < pk_value[value + 1]; j++) {
        // j=j+5;
        ret = GmcSetVertexProperty(g_stmt1[num], "F0", GMC_DATATYPE_UINT32, &j, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt1[num], "F1", GMC_DATATYPE_UINT32, &j, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(g_stmt1[num]);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }

    ret = testGmcDisconnect(g_conn1[num], g_stmt1[num]);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}
void *delete_func(void *args)
{
    int num = *((int *)args);
    // int num=0;
    int value = *((int *)args);
    int ret = testGmcConnect(&g_conn1[num], &g_stmt1[num]);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t pk_value[1000];
    pk_value[0] = 100;
    for (int i = 1; i < 1000; i++) {
        pk_value[i] = 100 + i * 100;
    }
    char Normal_thread_names[1023];
    snprintf(Normal_thread_names, 15, "Normal%d", num);
    printf("delete func:%s\n", Normal_thread_names);
    // ret=GmcPrepareStmtByLabelName(g_stmt1[num], Normal_thread_names, GMC_OPERATION_DELETE);
    // if(ret!=0)
    // {
    //         ret=testGmcGetLastError(g_conn1[num],NULL);
    //         EXPECT_EQ(GMERR_OK, ret);
    //         printf(" delete func success:%s\n",Normal_thread_names);
    // }
    for (uint32_t j = pk_value[value]; j < pk_value[value + 1]; j++) {
        // j=j+5;
        ret = testGmcPrepareStmtByLabelName(g_stmt1[num], Normal_thread_names, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt1[num], "K1");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt1[num], 0, GMC_DATATYPE_UINT32, &j, sizeof(j));
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(g_stmt1[num]);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }

    ret = testGmcDisconnect(g_conn1[num], g_stmt1[num]);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}
/*******************************************************************************
  函 数 名		:  DFX_040_001
  功能描述		:  生成多个表，创建多个订阅连接,插入数据，回调函数不休眠，观察消息堆积情况
  输入参数		:
  输出参数		:  None
  返 回 值		:  None
*******************************************************************************/
char Normal_schemas[1023];

const char *V_config = "{\"max_record_num\":1000000,\"defragmentation\":false}";

TEST_F(schedule_sub, DFX_040_001)
{
    int ret = 0, index[MAX_NUM], i;
    pthread_t write_table[MAX_NUM], view_table[MAX_NUM];
    for (int i = 0; i < MAX_NUM; i++) {
        ret = testGmcConnect(&g_conn1[i], &g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
        snprintf(Normal_schemas, 1023,
            "[{\"type\":\"record\", \"name\":\"OP_T%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"uint32\"},"
            "{\"name\":\"F1\", \"type\":\"uint32\"},{\"name\":\"F2\", \"type\":\"int32\"},{\"name\":\"F3\", "
            "\"type\":\"uint32\"}],"
            "\"keys\":[{\"node\":\"OP_T0\", \"name\":\"OP_PK\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
            i);
        ret = GmcCreateVertexLabel(g_stmt1[i], Normal_schemas, V_config);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            printf("%d\n", i);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = testGmcDisconnect(g_conn1[i], g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (i = 0; i < 1; i++) {
        index[i] = i;
        ret = pthread_create(&view_table[i], NULL, view_table_thread_func, (void *)&index[i]);
        EXPECT_EQ(ret, GMERR_OK);
    }
    for (i = 0; i < MAX_NUM; i++) {
        index[i] = i;
        ret = pthread_create(&write_table[i], NULL, write_table_thread_func, (void *)&index[i]);
        EXPECT_EQ(ret, GMERR_OK);
    }

    for (int i = 0; i < MAX_NUM; i++) {
        ret = pthread_join(write_table[i], NULL);
        EXPECT_EQ(ret, GMERR_OK);
    }
    for (int i = 0; i < 1; i++) {
        ret = pthread_join(view_table[i], NULL);
        EXPECT_EQ(ret, GMERR_OK);
    }
    EXPECT_GE(var_SEND_BUFF_USED_SIZE, 0);
    for (int i = 0; i < MAX_NUM; i++) {
        ret = testGmcConnect(&g_conn1[i], &g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
        char Normal_schema_names[1023];
        snprintf(Normal_schema_names, 15, "OP_T%d", i);
        ret = GmcDropVertexLabel(g_stmt1[i], Normal_schema_names);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = testGmcDisconnect(g_conn1[i], g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

/*******************************************************************************
  函 数 名		:  DFX_040_002
  功能描述		:  生成多个表，创建多个订阅连接,插入数据，回调函数休眠10ms，观察消息堆积情况
  输入参数		:
  输出参数		:  None
  返 回 值		:  None
*******************************************************************************/
TEST_F(schedule_sub, DFX_040_002)
{
    int ret = 0, index[MAX_NUM], i;
    pthread_t write_table[MAX_NUM], view_table[MAX_NUM];
    for (int i = 0; i < MAX_NUM; i++) {
        ret = testGmcConnect(&g_conn1[i], &g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
        snprintf(Normal_schemas, 1023,
            "[{\"type\":\"record\", \"name\":\"OP_T%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"uint32\"},"
            "{\"name\":\"F1\", \"type\":\"uint32\"},{\"name\":\"F2\", \"type\":\"int32\"},{\"name\":\"F3\", "
            "\"type\":\"uint32\"}],"
            "\"keys\":[{\"node\":\"OP_T0\", \"name\":\"OP_PK\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
            i);
        ret = GmcCreateVertexLabel(g_stmt1[i], Normal_schemas, V_config);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            printf("%d\n", i);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = testGmcDisconnect(g_conn1[i], g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (i = 0; i < 1; i++) {
        index[i] = i;
        ret = pthread_create(&view_table[i], NULL, view_table_thread_func, (void *)&index[i]);
        EXPECT_EQ(ret, GMERR_OK);
    }
    for (i = 0; i < MAX_NUM; i++) {
        index[i] = i;
        ret = pthread_create(&write_table[i], NULL, write_table_thread_func2, (void *)&index[i]);
        EXPECT_EQ(ret, GMERR_OK);
    }

    for (int i = 0; i < MAX_NUM; i++) {
        ret = pthread_join(write_table[i], NULL);
        EXPECT_EQ(ret, GMERR_OK);
    }
    for (int i = 0; i < 1; i++) {
        ret = pthread_join(view_table[i], NULL);
        EXPECT_EQ(ret, GMERR_OK);
    }
    EXPECT_GE(var_SEND_BUFF_USED_SIZE, 0);
    for (int i = 0; i < MAX_NUM; i++) {
        ret = testGmcConnect(&g_conn1[i], &g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
        char Normal_schema_names[1023];
        snprintf(Normal_schema_names, 15, "OP_T%d", i);
        ret = GmcDropVertexLabel(g_stmt1[i], Normal_schema_names);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = testGmcDisconnect(g_conn1[i], g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

/*******************************************************************************
  函 数 名		:  DFX_040_003
  功能描述		:  生成多个表，创建多个订阅连接,插入数据，回调函数休眠100ms，观察消息堆积情况
  输入参数		:
  输出参数		:  None
  返 回 值		:  None
*******************************************************************************/
TEST_F(schedule_sub, DFX_040_003)
{
    int ret = 0, index[MAX_NUM_WAIT], i;
    pthread_t write_table[MAX_NUM_WAIT], view_table[MAX_NUM_WAIT];
    for (int i = 0; i < MAX_NUM_WAIT; i++) {
        ret = testGmcConnect(&g_conn1[i], &g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
        snprintf(Normal_schemas, 1023,
            "[{\"type\":\"record\", \"name\":\"OP_T%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"uint32\"},"
            "{\"name\":\"F1\", \"type\":\"uint32\"},{\"name\":\"F2\", \"type\":\"int32\"},{\"name\":\"F3\", "
            "\"type\":\"uint32\"}],"
            "\"keys\":[{\"node\":\"OP_T0\", \"name\":\"OP_PK\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
            i);
        ret = GmcCreateVertexLabel(g_stmt1[i], Normal_schemas, V_config);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            printf("%d\n", i);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = testGmcDisconnect(g_conn1[i], g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (i = 0; i < 1; i++) {
        index[i] = i;
        ret = pthread_create(&view_table[i], NULL, view_table_thread_func, (void *)&index[i]);
        EXPECT_EQ(ret, GMERR_OK);
    }
    for (i = 0; i < MAX_NUM_WAIT; i++) {
        index[i] = i;
        ret = pthread_create(&write_table[i], NULL, write_table_thread_func3, (void *)&index[i]);
        EXPECT_EQ(ret, GMERR_OK);
    }

    for (int i = 0; i < MAX_NUM_WAIT; i++) {
        ret = pthread_join(write_table[i], NULL);
        EXPECT_EQ(ret, GMERR_OK);
    }
    for (int i = 0; i < 1; i++) {
        ret = pthread_join(view_table[i], NULL);
        EXPECT_EQ(ret, GMERR_OK);
    }
    EXPECT_GE(var_SEND_BUFF_USED_SIZE, 0);
    for (int i = 0; i < MAX_NUM_WAIT; i++) {
        ret = testGmcConnect(&g_conn1[i], &g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
        char Normal_schema_names[1023];
        snprintf(Normal_schema_names, 15, "OP_T%d", i);
        ret = GmcDropVertexLabel(g_stmt1[i], Normal_schema_names);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = testGmcDisconnect(g_conn1[i], g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

/*******************************************************************************
  函 数 名		:  DFX_040_004
  功能描述		:  生成多个表，创建多个订阅连接,插入数据，回调函数不休眠，观察等待调度时间
  输入参数		:
  输出参数		:  None
  返 回 值		:  None
*******************************************************************************/
TEST_F(schedule_sub, DFX_040_004)
{
    int ret = 0, index[MAX_NUM], i;
    pthread_t write_table[MAX_NUM], view_table[MAX_NUM];
    for (int i = 0; i < MAX_NUM; i++) {
        ret = testGmcConnect(&g_conn1[i], &g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
        snprintf(Normal_schemas, 1023,
            "[{\"type\":\"record\", \"name\":\"OP_T%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"uint32\"},"
            "{\"name\":\"F1\", \"type\":\"uint32\"},{\"name\":\"F2\", \"type\":\"int32\"},{\"name\":\"F3\", "
            "\"type\":\"uint32\"}],"
            "\"keys\":[{\"node\":\"OP_T0\", \"name\":\"OP_PK\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
            i);
        ret = GmcCreateVertexLabel(g_stmt1[i], Normal_schemas, V_config);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            printf("%d\n", i);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = testGmcDisconnect(g_conn1[i], g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (i = 0; i < 1; i++) {
        index[i] = i;
        ret = pthread_create(&view_table[i], NULL, view_table_thread_func2, (void *)&index[i]);
        EXPECT_EQ(ret, GMERR_OK);
    }
    for (i = 0; i < MAX_NUM; i++) {
        index[i] = i;
        ret = pthread_create(&write_table[i], NULL, write_table_thread_func, (void *)&index[i]);
        EXPECT_EQ(ret, GMERR_OK);
    }

    for (int i = 0; i < MAX_NUM; i++) {
        ret = pthread_join(write_table[i], NULL);
        EXPECT_EQ(ret, GMERR_OK);
    }
    for (int i = 0; i < 1; i++) {
        ret = pthread_join(view_table[i], NULL);
        EXPECT_EQ(ret, GMERR_OK);
    }
    EXPECT_GE(var_TOTAL_WAIT_TIME, 0);
    for (int i = 0; i < MAX_NUM; i++) {
        ret = testGmcConnect(&g_conn1[i], &g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
        char Normal_schema_names[1023];
        snprintf(Normal_schema_names, 15, "OP_T%d", i);
        ret = GmcDropVertexLabel(g_stmt1[i], Normal_schema_names);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = testGmcDisconnect(g_conn1[i], g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
}
/*******************************************************************************
  函 数 名		:  DFX_040_005
  功能描述		:  生成多个表，创建多个订阅连接,插入数据，回调函数休眠10ms，观察等待调度时间
  输入参数		:
  输出参数		:  None
  返 回 值		:  None
*******************************************************************************/
TEST_F(schedule_sub, DFX_040_005)
{
    int ret = 0, index[MAX_NUM], i;
    pthread_t write_table[MAX_NUM], view_table[MAX_NUM];
    for (int i = 0; i < MAX_NUM; i++) {
        ret = testGmcConnect(&g_conn1[i], &g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
        snprintf(Normal_schemas, 1023,
            "[{\"type\":\"record\", \"name\":\"OP_T%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"uint32\"},"
            "{\"name\":\"F1\", \"type\":\"uint32\"},{\"name\":\"F2\", \"type\":\"int32\"},{\"name\":\"F3\", "
            "\"type\":\"uint32\"}],"
            "\"keys\":[{\"node\":\"OP_T0\", \"name\":\"OP_PK\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
            i);
        ret = GmcCreateVertexLabel(g_stmt1[i], Normal_schemas, V_config);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            printf("%d\n", i);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = testGmcDisconnect(g_conn1[i], g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (i = 0; i < 1; i++) {
        index[i] = i;
        ret = pthread_create(&view_table[i], NULL, view_table_thread_func2, (void *)&index[i]);
        EXPECT_EQ(ret, GMERR_OK);
    }
    for (i = 0; i < MAX_NUM; i++) {
        index[i] = i;
        ret = pthread_create(&write_table[i], NULL, write_table_thread_func2, (void *)&index[i]);
        EXPECT_EQ(ret, GMERR_OK);
    }

    for (int i = 0; i < MAX_NUM; i++) {
        ret = pthread_join(write_table[i], NULL);
        EXPECT_EQ(ret, GMERR_OK);
    }
    for (int i = 0; i < 1; i++) {
        ret = pthread_join(view_table[i], NULL);
        EXPECT_EQ(ret, GMERR_OK);
    }
    EXPECT_GE(var_TOTAL_WAIT_TIME, 0);
    for (int i = 0; i < MAX_NUM; i++) {
        ret = testGmcConnect(&g_conn1[i], &g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
        char Normal_schema_names[1023];
        snprintf(Normal_schema_names, 15, "OP_T%d", i);
        ret = GmcDropVertexLabel(g_stmt1[i], Normal_schema_names);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = testGmcDisconnect(g_conn1[i], g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

/*******************************************************************************
  函 数 名		:  DFX_040_006
  功能描述		:  生成多个表，创建多个订阅连接,插入数据，回调函数休眠0.5s，观察等待调度时间
  输入参数		:
  输出参数		:  None
  返 回 值		:  None
*******************************************************************************/
TEST_F(schedule_sub, DFX_040_006)
{
    int ret = 0, index[MAX_NUM_WAIT], i;
    pthread_t write_table[MAX_NUM_WAIT], view_table[MAX_NUM_WAIT];
    for (int i = 0; i < MAX_NUM_WAIT; i++) {
        ret = testGmcConnect(&g_conn1[i], &g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
        snprintf(Normal_schemas, 1023,
            "[{\"type\":\"record\", \"name\":\"OP_T%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"uint32\"},"
            "{\"name\":\"F1\", \"type\":\"uint32\"},{\"name\":\"F2\", \"type\":\"int32\"},{\"name\":\"F3\", "
            "\"type\":\"uint32\"}],"
            "\"keys\":[{\"node\":\"OP_T0\", \"name\":\"OP_PK\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
            i);
        ret = GmcCreateVertexLabel(g_stmt1[i], Normal_schemas, V_config);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            printf("%d\n", i);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = testGmcDisconnect(g_conn1[i], g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (i = 0; i < 1; i++) {
        index[i] = i;
        ret = pthread_create(&view_table[i], NULL, view_table_thread_func2, (void *)&index[i]);
        EXPECT_EQ(ret, GMERR_OK);
    }
    for (i = 0; i < MAX_NUM_WAIT; i++) {
        index[i] = i;
        ret = pthread_create(&write_table[i], NULL, write_table_thread_func4, (void *)&index[i]);
        EXPECT_EQ(ret, GMERR_OK);
    }
    for (int i = 0; i < MAX_NUM_WAIT; i++) {
        ret = pthread_join(write_table[i], NULL);
        EXPECT_EQ(ret, GMERR_OK);
    }
    for (int i = 0; i < 1; i++) {
        ret = pthread_join(view_table[i], NULL);
        EXPECT_EQ(ret, GMERR_OK);
    }
    EXPECT_GE(var_TOTAL_WAIT_TIME, 0);
    for (int i = 0; i < MAX_NUM_WAIT; i++) {
        ret = testGmcConnect(&g_conn1[i], &g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
        char Normal_schema_names[1023];
        snprintf(Normal_schema_names, 15, "OP_T%d", i);
        ret = GmcDropVertexLabel(g_stmt1[i], Normal_schema_names);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = testGmcDisconnect(g_conn1[i], g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

/*******************************************************************************
  函 数 名		:  DFX_040_007
  功能描述		:  生成多个表，创建多个订阅连接,插入数据，回调函数不休眠，观察任务执行时间
  输入参数		:
  输出参数		:  None
  返 回 值		:  None
*******************************************************************************/
TEST_F(schedule_sub, DFX_040_007)
{
    int ret = 0, index[MAX_NUM], i;
    pthread_t write_table[MAX_NUM], view_table[MAX_NUM];
    for (int i = 0; i < MAX_NUM; i++) {
        ret = testGmcConnect(&g_conn1[i], &g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
        snprintf(Normal_schemas, 1023,
            "[{\"type\":\"record\", \"name\":\"OP_T%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"uint32\"},"
            "{\"name\":\"F1\", \"type\":\"uint32\"},{\"name\":\"F2\", \"type\":\"int32\"},{\"name\":\"F3\", "
            "\"type\":\"uint32\"}],"
            "\"keys\":[{\"node\":\"OP_T0\", \"name\":\"OP_PK\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
            i);
        ret = GmcCreateVertexLabel(g_stmt1[i], Normal_schemas, V_config);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            printf("%d\n", i);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = testGmcDisconnect(g_conn1[i], g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (i = 0; i < 1; i++) {
        index[i] = i;
        ret = pthread_create(&view_table[i], NULL, view_table_thread_func3, (void *)&index[i]);
        EXPECT_EQ(ret, GMERR_OK);
    }
    for (i = 0; i < MAX_NUM; i++) {
        index[i] = i;
        ret = pthread_create(&write_table[i], NULL, write_table_thread_func, (void *)&index[i]);
        EXPECT_EQ(ret, GMERR_OK);
    }

    for (int i = 0; i < MAX_NUM; i++) {
        ret = pthread_join(write_table[i], NULL);
        EXPECT_EQ(ret, GMERR_OK);
    }
    for (int i = 0; i < 1; i++) {
        ret = pthread_join(view_table[i], NULL);
        EXPECT_EQ(ret, GMERR_OK);
    }
    EXPECT_GT(var_AVG_MSG_PROC_TIME, 10);
    for (int i = 0; i < MAX_NUM; i++) {
        ret = testGmcConnect(&g_conn1[i], &g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
        char Normal_schema_names[1023];
        snprintf(Normal_schema_names, 15, "OP_T%d", i);
        ret = GmcDropVertexLabel(g_stmt1[i], Normal_schema_names);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = testGmcDisconnect(g_conn1[i], g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
}
/*******************************************************************************
  函 数 名		:  DFX_040_008
  功能描述		:  生成多个表，创建多个订阅连接,插入数据，回调函数休眠10ms，观察任务执行时间
  输入参数		:
  输出参数		:  None
  返 回 值		:  None
*******************************************************************************/
TEST_F(schedule_sub, DFX_040_008)
{
    int ret = 0, index[MAX_NUM], i;
    pthread_t write_table[MAX_NUM], view_table[MAX_NUM];
    for (int i = 0; i < MAX_NUM; i++) {
        ret = testGmcConnect(&g_conn1[i], &g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
        snprintf(Normal_schemas, 1023,
            "[{\"type\":\"record\", \"name\":\"OP_T%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"uint32\"},"
            "{\"name\":\"F1\", \"type\":\"uint32\"},{\"name\":\"F2\", \"type\":\"int32\"},{\"name\":\"F3\", "
            "\"type\":\"uint32\"}],"
            "\"keys\":[{\"node\":\"OP_T0\", \"name\":\"OP_PK\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
            i);
        ret = GmcCreateVertexLabel(g_stmt1[i], Normal_schemas, V_config);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            printf("%d\n", i);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = testGmcDisconnect(g_conn1[i], g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (i = 0; i < 1; i++) {
        index[i] = i;
        ret = pthread_create(&view_table[i], NULL, view_table_thread_func3, (void *)&index[i]);
        EXPECT_EQ(ret, GMERR_OK);
    }
    for (i = 0; i < MAX_NUM; i++) {
        index[i] = i;
        ret = pthread_create(&write_table[i], NULL, write_table_thread_func2, (void *)&index[i]);
        EXPECT_EQ(ret, GMERR_OK);
    }

    for (int i = 0; i < MAX_NUM; i++) {
        ret = pthread_join(write_table[i], NULL);
        EXPECT_EQ(ret, GMERR_OK);
    }
    for (int i = 0; i < 1; i++) {
        ret = pthread_join(view_table[i], NULL);
        EXPECT_EQ(ret, GMERR_OK);
    }
    EXPECT_GT(var_AVG_MSG_PROC_TIME, 50);
    for (int i = 0; i < MAX_NUM; i++) {
        ret = testGmcConnect(&g_conn1[i], &g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
        char Normal_schema_names[1023];
        snprintf(Normal_schema_names, 15, "OP_T%d", i);
        ret = GmcDropVertexLabel(g_stmt1[i], Normal_schema_names);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = testGmcDisconnect(g_conn1[i], g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

/*******************************************************************************
  函 数 名		:  DFX_040_009
  功能描述		:  生成多个表，创建多个订阅连接,插入数据，回调函数休眠100ms，观察任务执行时间
  输入参数		:
  输出参数		:  None
  返 回 值		:  None
*******************************************************************************/

TEST_F(schedule_sub, DFX_040_009)
{
    int ret = 0, index[MAX_NUM_WAIT], i;
    pthread_t write_table[MAX_NUM_WAIT], view_table[MAX_NUM_WAIT];
    for (int i = 0; i < MAX_NUM_WAIT; i++) {
        ret = testGmcConnect(&g_conn1[i], &g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
        snprintf(Normal_schemas, 1023,
            "[{\"type\":\"record\", \"name\":\"OP_T%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"uint32\"},"
            "{\"name\":\"F1\", \"type\":\"uint32\"},{\"name\":\"F2\", \"type\":\"int32\"},{\"name\":\"F3\", "
            "\"type\":\"uint32\"}],"
            "\"keys\":[{\"node\":\"OP_T0\", \"name\":\"OP_PK\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
            i);
        ret = GmcCreateVertexLabel(g_stmt1[i], Normal_schemas, V_config);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            printf("%d\n", i);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = testGmcDisconnect(g_conn1[i], g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    uint32_t result = 0;
    for (i = 0; i < 1; i++) {
        index[i] = i;
        ret = pthread_create(&view_table[i], NULL, view_table_thread_func3, (void *)&result);
        EXPECT_EQ(ret, GMERR_OK);
    }

    for (i = 0; i < MAX_NUM_WAIT; i++) {
        index[i] = i;
        ret = pthread_create(&write_table[i], NULL, write_table_thread_func3, (void *)&index[i]);
        EXPECT_EQ(ret, GMERR_OK);
    }

    for (int i = 0; i < MAX_NUM_WAIT; i++) {
        ret = pthread_join(write_table[i], NULL);
        EXPECT_EQ(ret, GMERR_OK);
    }
    for (int i = 0; i < 1; i++) {
        ret = pthread_join(view_table[i], NULL);
        EXPECT_EQ(ret, GMERR_OK);
    }
    EXPECT_GT(var_AVG_MSG_PROC_TIME, 10);
    for (int i = 0; i < MAX_NUM_WAIT; i++) {
        ret = testGmcConnect(&g_conn1[i], &g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
        char Normal_schema_names[1023];
        snprintf(Normal_schema_names, 15, "OP_T%d", i);
        // printf("the finally result is:%d\n",result);
        ret = GmcDropVertexLabel(g_stmt1[i], Normal_schema_names);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = testGmcDisconnect(g_conn1[i], g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

/*******************************************************************************
  函 数 名		:  DFX_040_010
  功能描述		:  单线程打开1024个表进行简单的DML操作，查看消息堆积情况
  输入参数		:
  输出参数		:  None
  返 回 值		:  None
*******************************************************************************/
char Normal_schema_names2[1023];
GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
TEST_F(schedule_sub, DFX_040_010)
{
    int ret = 0, index[MAX_NUM], i;
    pthread_t write_table[MAX_NUM], view_table[MAX_NUM];
    char *Normal_schema = NULL;
    bool isEof;
    bool isNull;
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 1024; i++) {
        snprintf(Normal_schema_names2, 15, "Normal%d", i);
        ret = GmcDropVertexLabel(g_stmt, Normal_schema_names2);
        // EXPECT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < 1024; i++) {
        snprintf(Normal_schemas, 1023,
            "[{\"type\":\"record\", \"name\":\"Normal%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"uint32\"},"
            "{\"name\":\"F1\", \"type\":\"uint32\"},{\"name\":\"F2\", \"type\":\"uint32\"},{\"name\":\"F3\", "
            "\"type\":\"uint32\"}],"
            "\"keys\":[{\"node\":\"Normal%d\", \"name\":\"Normal_K0\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
            i, i);

        ret = GmcCreateVertexLabel(g_stmt, Normal_schemas, V_config);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    for (int i = 0; i < 1; i++) {
        index[i] = i;
        ret = pthread_create(&view_table[i], NULL, view_table_thread_func, (void *)&index[i]);
        EXPECT_EQ(ret, GMERR_OK);
    }
    for (int i = 0; i < 1024; i++) {
        snprintf(Normal_schema_names2, 15, "Normal%d", i);
        ret = testGmcPrepareStmtByLabelName(g_stmt, Normal_schema_names2, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < 100; j++) {
            ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &j, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(g_stmt);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != 0) {
                ret = testGmcGetLastError(NULL);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        ret = testGmcPrepareStmtByLabelName(g_stmt, Normal_schema_names2, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < 50; j++) {
            uint32_t up_value = j + 201;
            ret = GmcSetIndexKeyName(g_stmt, "Normal_K0");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &j, sizeof(j));
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &up_value, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(g_stmt);
            ASSERT_EQ(GMERR_OK, ret);
            if (ret != 0) {
                ret = testGmcGetLastError(NULL);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        ret = testGmcPrepareStmtByLabelName(g_stmt, Normal_schema_names2, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 50; j < 100; j++) {
            ret = GmcSetIndexKeyName(g_stmt, "Normal_K0");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &j, sizeof(j));
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcExecute(g_stmt);
            ASSERT_EQ(GMERR_OK, ret);
            if (ret != 0) {
                ret = testGmcGetLastError(NULL);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        isEof = false;
        ret = testGmcPrepareStmtByLabelName(g_stmt, Normal_schema_names2, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t prop_size = 0;
        uint32_t prop_value = 0;
        while (!isEof) {
            ret = GmcFetch(g_stmt, &isEof);
            EXPECT_EQ(GMERR_OK, ret);
            if (isEof == true) {
                break;
            }
            ret = GmcGetVertexPropertySizeByName(g_stmt, "F0", &prop_size);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(g_stmt, "F0", &prop_value, prop_size, &isNull);  // isNull传出
            EXPECT_EQ(GMERR_OK, ret);
            // printf("table %d:the F0 value is:%d  ",i,prop_value);
        }
        // printf("\n");
    }
    for (int i = 0; i < 1; i++) {
        ret = pthread_join(view_table[i], NULL);
        EXPECT_EQ(ret, GMERR_OK);
    }
    EXPECT_GE(var_SEND_BUFF_USED_SIZE, 0);
    EXPECT_GE(var_RECV_BUFF_USED_SIZE, 0);
    for (int i = 0; i < 1024; i++) {
        snprintf(Normal_schema_names2, 15, "Normal%d", i);
        ret = GmcDropVertexLabel(g_stmt, Normal_schema_names2);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}
/*******************************************************************************
  函 数 名		:  DFX_040_011
  功能描述		:  单线程执行简单的DML操作，观察等待调度时间
  输入参数		:
  输出参数		:  None
  返 回 值		:  None
*******************************************************************************/
TEST_F(schedule_sub, DFX_040_011)
{
    int ret = 0, index[MAX_NUM], i;
    pthread_t write_table[MAX_NUM], view_table[MAX_NUM];
    char *Normal_schema = NULL;
    bool isEof;
    bool isNull;
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 1024; i++) {
        snprintf(Normal_schema_names2, 15, "Normal%d", i);
        ret = GmcDropVertexLabel(g_stmt, Normal_schema_names2);
        // EXPECT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < 1024; i++) {
        snprintf(Normal_schemas, 1023,
            "[{\"type\":\"record\", \"name\":\"Normal%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"uint32\"},"
            "{\"name\":\"F1\", \"type\":\"uint32\"},{\"name\":\"F2\", \"type\":\"uint32\"},{\"name\":\"F3\", "
            "\"type\":\"uint32\"}],"
            "\"keys\":[{\"node\":\"Normal\", \"name\":\"Normal_K0\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
            i);

        ret = GmcCreateVertexLabel(g_stmt, Normal_schemas, V_config);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    for (int i = 0; i < 1; i++) {
        index[i] = i;
        ret = pthread_create(&view_table[i], NULL, view_table_thread_func2, (void *)&index[i]);
        EXPECT_EQ(ret, GMERR_OK);
    }
    for (int i = 0; i < 1024; i++) {
        snprintf(Normal_schema_names2, 15, "Normal%d", i);
        ret = testGmcPrepareStmtByLabelName(g_stmt, Normal_schema_names2, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < 100; j++) {
            ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &j, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(g_stmt);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != 0) {
                ret = testGmcGetLastError(NULL);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        ret = testGmcPrepareStmtByLabelName(g_stmt, Normal_schema_names2, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < 50; j++) {
            uint32_t up_value = j + 201;
            ret = GmcSetIndexKeyName(g_stmt, "Normal_K0");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &j, sizeof(j));
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &up_value, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(g_stmt);
            ASSERT_EQ(GMERR_OK, ret);
            if (ret != 0) {
                ret = testGmcGetLastError(NULL);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        ret = testGmcPrepareStmtByLabelName(g_stmt, Normal_schema_names2, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 50; j < 100; j++) {
            ret = GmcSetIndexKeyName(g_stmt, "Normal_K0");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &j, sizeof(j));
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcExecute(g_stmt);
            ASSERT_EQ(GMERR_OK, ret);
            if (ret != 0) {
                ret = testGmcGetLastError(NULL);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        isEof = false;
        ret = testGmcPrepareStmtByLabelName(g_stmt, Normal_schema_names2, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t prop_size = 0;
        uint32_t prop_value = 0;
        while (!isEof) {
            ret = GmcFetch(g_stmt, &isEof);
            EXPECT_EQ(GMERR_OK, ret);
            if (isEof == true) {
                break;
            }
            ret = GmcGetVertexPropertySizeByName(g_stmt, "F0", &prop_size);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(g_stmt, "F0", &prop_value, prop_size, &isNull);  // isNull传出
            EXPECT_EQ(GMERR_OK, ret);
            // printf("table %d:the F0 value is:%d  ",i,prop_value);
        }
        // printf("\n");
    }
    for (int i = 0; i < 1; i++) {
        ret = pthread_join(view_table[i], NULL);
        EXPECT_EQ(ret, GMERR_OK);
    }
    EXPECT_GE(var_TOTAL_WAIT_TIME, 0);
    for (int i = 0; i < 1024; i++) {
        snprintf(Normal_schema_names2, 15, "Normal%d", i);
        ret = GmcDropVertexLabel(g_stmt, Normal_schema_names2);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}
/*******************************************************************************
  函 数 名		:  DFX_040_012
  功能描述		:  单线程执行简单的DML操作，观察任务执行时间
  输入参数		:
  输出参数		:  None
  返 回 值		:  None
*******************************************************************************/

TEST_F(schedule_sub, DFX_040_012)
{
    int ret = 0, index[MAX_NUM], i;
    pthread_t write_table[MAX_NUM], view_table[MAX_NUM];
    char *Normal_schema = NULL;
    bool isEof;
    bool isNull;
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 1024; i++) {
        snprintf(Normal_schema_names2, 15, "Normal%d", i);
        ret = GmcDropVertexLabel(g_stmt, Normal_schema_names2);
        // EXPECT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < 1024; i++) {
        snprintf(Normal_schemas, 1023,
            "[{\"type\":\"record\", \"name\":\"Normal%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"uint32\"},"
            "{\"name\":\"F1\", \"type\":\"uint32\"},{\"name\":\"F2\", \"type\":\"uint32\"},{\"name\":\"F3\", "
            "\"type\":\"uint32\"}],"
            "\"keys\":[{\"node\":\"Normal\", \"name\":\"Normal_K0\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
            i);

        ret = GmcCreateVertexLabel(g_stmt, Normal_schemas, V_config);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    for (int i = 0; i < 1; i++) {
        index[i] = i;
        ret = pthread_create(&view_table[i], NULL, view_table_thread_func3, (void *)&index[i]);
        EXPECT_EQ(ret, GMERR_OK);
    }

    for (int i = 0; i < 1024; i++) {
        snprintf(Normal_schema_names2, 15, "Normal%d", i);
        ret = testGmcPrepareStmtByLabelName(g_stmt, Normal_schema_names2, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < 100; j++) {
            ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &j, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(g_stmt);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != 0) {
                ret = testGmcGetLastError(NULL);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        ret = testGmcPrepareStmtByLabelName(g_stmt, Normal_schema_names2, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < 50; j++) {
            uint32_t up_value = j + 201;
            ret = GmcSetIndexKeyName(g_stmt, "Normal_K0");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &j, sizeof(j));
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &up_value, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(g_stmt);
            ASSERT_EQ(GMERR_OK, ret);
            if (ret != 0) {
                ret = testGmcGetLastError(NULL);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        ret = testGmcPrepareStmtByLabelName(g_stmt, Normal_schema_names2, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 50; j < 100; j++) {
            ret = GmcSetIndexKeyName(g_stmt, "Normal_K0");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &j, sizeof(j));
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcExecute(g_stmt);
            ASSERT_EQ(GMERR_OK, ret);
            if (ret != 0) {
                ret = testGmcGetLastError(NULL);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        isEof = false;
        ret = testGmcPrepareStmtByLabelName(g_stmt, Normal_schema_names2, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t prop_size = 0;
        uint32_t prop_value = 0;
        while (!isEof) {
            ret = GmcFetch(g_stmt, &isEof);
            EXPECT_EQ(GMERR_OK, ret);
            if (isEof == true) {
                break;
            }
            ret = GmcGetVertexPropertySizeByName(g_stmt, "F0", &prop_size);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(g_stmt, "F0", &prop_value, prop_size, &isNull);  // isNull传出
            EXPECT_EQ(GMERR_OK, ret);
            // printf("table %d:the F0 value is:%d  ",i,prop_value);
        }
        // printf("\n");
    }
    for (int i = 0; i < 1; i++) {
        ret = pthread_join(view_table[i], NULL);
        EXPECT_EQ(ret, GMERR_OK);
    }
    EXPECT_GT(var_AVG_MSG_PROC_TIME, 10);
    for (int i = 0; i < 1024; i++) {
        snprintf(Normal_schema_names2, 15, "Normal%d", i);
        ret = GmcDropVertexLabel(g_stmt, Normal_schema_names2);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}
/*******************************************************************************
  函 数 名		:  DFX_040_013
  功能描述		:  64线程并发读写，观察消息堆积情况
  输入参数		:
  输出参数		:  None
  返 回 值		:  None
*******************************************************************************/
TEST_F(schedule_sub, DFX_040_013)
{
    pthread_t create_table[MAX_NUM];
    pthread_t drop_muti_table[MAX_NUM];
    pthread_t write_table[MAX_NUM];
    pthread_t read_table[MAX_NUM];
    pthread_t del_table[MAX_NUM];
    pthread_t view_table[MAX_NUM];
    int i, index[MAX_NUM];
    int table_num = 10;
    bool isEof;
    bool isNull;
    int ret = 0;
    for (int i = 0; i < 1024; i++) {
        ret = testGmcConnect(&g_conn1[i], &g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
        snprintf(Normal_schemas, 1023,
            "[{\"type\":\"record\", \"name\":\"Normal%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"uint32\"},"
            "{\"name\":\"F1\", \"type\":\"uint32\"},{\"name\":\"F2\", \"type\":\"uint32\"},{\"name\":\"F3\", "
            "\"type\":\"uint32\"}],"
            "\"keys\":[{\"node\":\"Normal\", \"name\":\"Normal_K0\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
            i);
        ret = GmcCreateVertexLabel(g_stmt1[i], Normal_schemas, V_config);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcDisconnect(g_conn1[i], g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < 1; i++) {
        index[i] = i;
        ret = pthread_create(&view_table[i], NULL, view_table_thread_func, (void *)&index[i]);
        EXPECT_EQ(ret, GMERR_OK);
    }
    for (i = 0; i < MAX_NUM; i++) {
        index[i] = i;
        ret = pthread_create(&write_table[i], NULL, write_func, (void *)&index[i]);
        EXPECT_EQ(ret, GMERR_OK);
    }
    for (int i = 0; i < MAX_NUM; i++) {
        ret = pthread_join(write_table[i], NULL);
        EXPECT_EQ(ret, GMERR_OK);
    }
    for (i = 0; i < MAX_NUM; i++) {
        index[i] = i;
        ret = pthread_create(&read_table[i], NULL, read_table_thread_func, (void *)&index[i]);
        EXPECT_EQ(ret, GMERR_OK);
    }
    for (int i = 0; i < MAX_NUM; i++) {
        ret = pthread_join(read_table[i], NULL);
        EXPECT_EQ(ret, GMERR_OK);
    }
    for (int i = 0; i < 1; i++) {
        ret = pthread_join(view_table[i], NULL);
        EXPECT_EQ(ret, GMERR_OK);
    }
    EXPECT_GE(var_SEND_BUFF_USED_SIZE, 0);
    EXPECT_GE(var_RECV_BUFF_USED_SIZE, 0);
    for (int i = 0; i < 1024; i++) {
        ret = testGmcConnect(&g_conn1[i], &g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
        snprintf(Normal_schema_names2, 15, "Normal%d", i);
        ret = GmcDropVertexLabel(g_stmt1[i], Normal_schema_names2);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = testGmcDisconnect(g_conn1[i], g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
}
/*******************************************************************************
  函 数 名		:  DFX_040_014
  功能描述		:  多线程并发执行DML操作，观察等待调度时间
  输入参数		:
  输出参数		:  None
  返 回 值		:  None
*******************************************************************************/
TEST_F(schedule_sub, DFX_040_014)
{
    pthread_t create_table[MAX_NUM];
    pthread_t drop_muti_table[MAX_NUM];
    pthread_t write_table[MAX_NUM];
    pthread_t read_table[MAX_NUM];
    pthread_t del_table[MAX_NUM];
    pthread_t view_table[MAX_NUM];
    int i, index[MAX_NUM];
    int table_num = 10;
    bool isEof;
    bool isNull;
    int ret = 0;
    for (int i = 0; i < 1024; i++) {
        ret = testGmcConnect(&g_conn1[i], &g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
        snprintf(Normal_schemas, 1023,
            "[{\"type\":\"record\", \"name\":\"Normal%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"uint32\"},"
            "{\"name\":\"F1\", \"type\":\"uint32\"},{\"name\":\"F2\", \"type\":\"uint32\"},{\"name\":\"F3\", "
            "\"type\":\"uint32\"}],"
            "\"keys\":[{\"node\":\"Normal\", \"name\":\"Normal_K0\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
            i);
        ret = GmcCreateVertexLabel(g_stmt1[i], Normal_schemas, V_config);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcDisconnect(g_conn1[i], g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < 1; i++) {
        index[i] = i;
        ret = pthread_create(&view_table[i], NULL, view_table_thread_func2, (void *)&index[i]);
        EXPECT_EQ(ret, GMERR_OK);
    }
    for (i = 0; i < MAX_NUM; i++) {
        index[i] = i;
        ret = pthread_create(&write_table[i], NULL, write_func, (void *)&index[i]);
        EXPECT_EQ(ret, GMERR_OK);
    }
    for (int i = 0; i < MAX_NUM; i++) {
        ret = pthread_join(write_table[i], NULL);
        EXPECT_EQ(ret, GMERR_OK);
    }
    for (i = 0; i < MAX_NUM; i++) {
        index[i] = i;
        ret = pthread_create(&read_table[i], NULL, read_table_thread_func, (void *)&index[i]);
        EXPECT_EQ(ret, GMERR_OK);
    }
    for (int i = 0; i < MAX_NUM; i++) {
        ret = pthread_join(read_table[i], NULL);
        EXPECT_EQ(ret, GMERR_OK);
    }
    for (int i = 0; i < 1; i++) {
        ret = pthread_join(view_table[i], NULL);
        EXPECT_EQ(ret, GMERR_OK);
    }
    EXPECT_GE(var_TOTAL_WAIT_TIME, 0);
    for (int i = 0; i < 1024; i++) {
        ret = testGmcConnect(&g_conn1[i], &g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
        snprintf(Normal_schema_names2, 15, "Normal%d", i);
        ret = GmcDropVertexLabel(g_stmt1[i], Normal_schema_names2);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = testGmcDisconnect(g_conn1[i], g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

/*******************************************************************************
  函 数 名		:  DFX_040_015
  功能描述		:  多线程并发执行DML操作，观察任务执行时间
  输入参数		:
  输出参数		:  None
  返 回 值		:  None
*******************************************************************************/
TEST_F(schedule_sub, DFX_040_015)
{
    pthread_t create_table[MAX_NUM];
    pthread_t drop_muti_table[MAX_NUM];
    pthread_t write_table[MAX_NUM];
    pthread_t read_table[MAX_NUM];
    pthread_t del_table[MAX_NUM];
    pthread_t view_table[MAX_NUM];
    int i, index[MAX_NUM];
    int table_num = 10;
    bool isEof;
    bool isNull;
    int ret = 0;
    for (int i = 0; i < 1024; i++) {
        ret = testGmcConnect(&g_conn1[i], &g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
        snprintf(Normal_schemas, 1023,
            "[{\"type\":\"record\", \"name\":\"Normal%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"uint32\"},"
            "{\"name\":\"F1\", \"type\":\"uint32\"},{\"name\":\"F2\", \"type\":\"uint32\"},{\"name\":\"F3\", "
            "\"type\":\"uint32\"}],"
            "\"keys\":[{\"node\":\"Normal\", \"name\":\"Normal_K0\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
            i);
        ret = GmcCreateVertexLabel(g_stmt1[i], Normal_schemas, V_config);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcDisconnect(g_conn1[i], g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < 1; i++) {
        index[i] = i;
        ret = pthread_create(&view_table[i], NULL, view_table_thread_func3, (void *)&index[i]);
        EXPECT_EQ(ret, GMERR_OK);
    }
    for (i = 0; i < MAX_NUM; i++) {
        index[i] = i;
        ret = pthread_create(&write_table[i], NULL, write_func, (void *)&index[i]);
        EXPECT_EQ(ret, GMERR_OK);
    }
    for (int i = 0; i < MAX_NUM; i++) {
        ret = pthread_join(write_table[i], NULL);
        EXPECT_EQ(ret, GMERR_OK);
    }
    for (i = 0; i < MAX_NUM; i++) {
        index[i] = i;
        ret = pthread_create(&read_table[i], NULL, read_table_thread_func, (void *)&index[i]);
        EXPECT_EQ(ret, GMERR_OK);
    }
    for (int i = 0; i < MAX_NUM; i++) {
        ret = pthread_join(read_table[i], NULL);
        EXPECT_EQ(ret, GMERR_OK);
    }
    for (int i = 0; i < 1; i++) {
        ret = pthread_join(view_table[i], NULL);
        EXPECT_EQ(ret, GMERR_OK);
    }
    EXPECT_GE(var_AVG_MSG_PROC_TIME, 0);
    for (int i = 0; i < 1024; i++) {
        ret = testGmcConnect(&g_conn1[i], &g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
        snprintf(Normal_schema_names2, 15, "Normal%d", i);
        ret = GmcDropVertexLabel(g_stmt1[i], Normal_schema_names2);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = testGmcDisconnect(g_conn1[i], g_stmt1[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
}
