/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2017-2027. All rights reserved.
 * File Name:cltProcessFlowCtrl.cpp
 * Author: yaosiyuan
 * Date: 2021-12-16
 * Describle:
 */

#include "cltProcessFlowCtrl.h"

class cltProcessFlowCtrl : public testing::Test {
public:
    virtual void SetUp()
    {
        mallocSubData(&g_userData, g_sizeMalloc);
        AW_CHECK_LOG_BEGIN();
    };
    virtual void TearDown()
    {
        AW_CHECK_LOG_END();
        freeMallocSqace(g_userData);
    };
    static void SetUpTestCase(){};
    static void TearDownTestCase()
    {
        int ret = 0;
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        ret = close_epoll_thread();
        ASSERT_EQ(GMERR_OK, ret);
        testEnvClean();
    };
};

// 查询流控视图，写数据，再查询流控视图
TEST_F(cltProcessFlowCtrl, DFX_049_001)
{
    int ret = 0;
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalShmSize=256\" \"maxTotalDynSize=512\""
           " \"clientServerFlowControl=1\" \"overloadThreshold=cpu:8,10,12,15,17,20\" "
           "\"dsFlowCtrlThreshold=8,10,12,15,17,20\"");
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    sleep(10);
    ret = system("gmsysview -q V\\$CLT_PROCESS_FLOWCTRL_INFO_LABEL");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, schemaAllTypes, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"schema_datatype";
    char stringValue[529];
    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[528] = '\0';
    ret = writeVertex(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, GMC_OPERATION_INSERT, conn);
    EXPECT_EQ(GMERR_OK, ret);
    ret = system("gmsysview -q V\\$CLT_PROCESS_FLOWCTRL_INFO_LABEL");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写入大量数据，触发流控，查询流控视图
TEST_F(cltProcessFlowCtrl, DFX_049_002)
{
    int ret = 0;
    if (g_envType == MODE_DAP) {
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalShmSize=256\" \"maxTotalDynSize=512\""
               " \"clientServerFlowControl=1\" \"overloadThreshold=cpu:2,3,3,4,4,6\" "
               "\"dsFlowCtrlThreshold=8,10,12,15,17,20\" "
               "\"maxSeMem=128\" \"maxHprShmSize=64\" \"maxSysShmSize=32\" \"maxSysDynSize=170\"");
    } else {
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalShmSize=256\" \"maxTotalDynSize=512\""
               " \"clientServerFlowControl=1\" \"overloadThreshold=cpu:8,10,12,15,17,20\" "
               "\"dsFlowCtrlThreshold=8,10,12,15,17,20\" "
               "\"maxSeMem=128\" \"maxHprShmSize=64\" \"maxSysShmSize=32\" \"maxSysDynSize=170\"");
    }
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, schemaAllTypes, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"schema_datatype";
    ret = writeVertex(RECORDCOUNTSTART, CLTFLOWTROLNUM, stmt, labelName, GMC_OPERATION_INSERT, conn);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 订阅，写大量数据，触发解反压，查询流控视图
TEST_F(cltProcessFlowCtrl, DFX_049_003)
{
    int ret = 0;
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalShmSize=256\" \"maxTotalDynSize=512\""
           " \"clientServerFlowControl=1\" \"overloadThreshold=cpu:28,30,32,35,40,45\" "
           "\"dsFlowCtrlThreshold=8,10,12,15,17,20\"");
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, schemaAllTypes, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &g_chanRingLen);
    ASSERT_EQ(GMERR_OK, ret);
    const char *subName = (char *)"subAllType";
    GmcSubConfigT tmp_schema;
    tmp_schema.subsName = subName;
    tmp_schema.configJson = subAllType;
    ret = GmcSubscribe(stmt, &tmp_schema, g_conn_sub, vertexSnCallback, g_userData);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"schema_datatype";
    ret = writeVertex(RECORDCOUNTSTART, CLTFLOWTROLNUM, stmt, labelName, GMC_OPERATION_INSERT, conn);
    EXPECT_EQ(GMERR_OK, ret);
    sleep(5);
    ret = GmcUnSubscribe(stmt, subName);
    EXPECT_EQ(GMERR_OK, ret);
    // 没有获取到统计
    ret = system("gmsysview -q V\\$CLT_PROCESS_FLOWCTRL_INFO_LABEL");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 开启事务，插入大量数据触发流控，结束事务，查询流控视图
TEST_F(cltProcessFlowCtrl, DFX_049_004)
{
    int ret = 0;
    if (g_envType == MODE_DAP) {
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalShmSize=256\" \"maxTotalDynSize=512\""
               " \"clientServerFlowControl=1\" \"overloadThreshold=cpu:2,3,3,4,4,6\" "
               "\"dsFlowCtrlThreshold=8,10,12,15,17,20\"");
    } else {
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalShmSize=256\" \"maxTotalDynSize=512\""
               " \"clientServerFlowControl=1\" \"overloadThreshold=cpu:8,10,12,15,17,20\" "
               "\"dsFlowCtrlThreshold=8,10,12,15,17,20\"");
    }
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelConfig = (char *)"{\"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(stmt, schemaAllTypes, labelConfig);
    EXPECT_EQ(GMERR_OK, ret);
    ret = syTransStart(conn, GMC_TRANS_USED_IN_CS);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"schema_datatype";
    ret = writeVertex(RECORDCOUNTSTART, CLTFLOWTROLNUM, stmt, labelName, GMC_OPERATION_INSERT, conn);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTransRollBack(conn);
    EXPECT_EQ(GMERR_OK, ret);
    ret = system("gmsysview -q V\\$CLT_PROCESS_FLOWCTRL_INFO_LABEL");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 创建两个线程，一个线程不断写数据，另外一个线程一直使用流控视图查询
TEST_F(cltProcessFlowCtrl, DFX_049_005)
{
    int ret = 0;
    if (g_envType == MODE_DAP) {
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalShmSize=256\" \"maxTotalDynSize=512\""
               " \"clientServerFlowControl=1\" \"overloadThreshold=cpu:2,3,3,4,4,6\" "
               "\"dsFlowCtrlThreshold=8,10,12,15,17,20\"");
    } else {
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalShmSize=256\" \"maxTotalDynSize=512\""
               " \"clientServerFlowControl=1\" \"overloadThreshold=cpu:8,10,12,15,17,20\" "
               "\"dsFlowCtrlThreshold=8,10,12,15,17,20\"");
    }
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);
    int thr_num = 2;
    GmcStmtT *stmt = NULL;
    GmcConnT *conn = NULL;
    pthread_t thr_arr[thr_num];
    void *thr_ret[thr_num];
    thread_func thr_func[2] = {threadVertexWrite, threadFlowCtrlDFX};
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, schemaAllTypes, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < thr_num; i++) {
        ret = pthread_create(&thr_arr[i], NULL, thr_func[i % 2], NULL);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < thr_num; i++) {
        ret = pthread_join(thr_arr[i], &thr_ret[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    sleep(10);
    ret = system("gmsysview -q V\\$CLT_PROCESS_FLOWCTRL_INFO_LABEL");
    EXPECT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"schema_datatype";
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 创建两个线程，一个线程订阅不断写数据，另外一个线程一直使用流控视图查询
TEST_F(cltProcessFlowCtrl, DFX_049_006)
{
    int ret = 0;
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalShmSize=256\" \"maxTotalDynSize=512\""
           " \"clientServerFlowControl=1\" \"overloadThreshold=cpu:28,30,33,36,40,45\" "
           "\"dsFlowCtrlThreshold=8,10,12,15,17,20\"");
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);
    int thr_num = 2;
    GmcStmtT *stmt = NULL;
    GmcConnT *conn = NULL;
    pthread_t thr_arr[thr_num];
    void *thr_ret[thr_num];
    thread_func thr_func[2] = {threadVertexWrite, threadFlowCtrlDFX};
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, schemaAllTypes, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &g_chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);
    const char *subName = (char *)"subAllType";
    GmcSubConfigT tmp_schema;
    tmp_schema.subsName = subName;
    tmp_schema.configJson = subAllType;
    ret = GmcSubscribe(stmt, &tmp_schema, g_conn_sub, vertexSnCallback, g_userData);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < thr_num; i++) {
        ret = pthread_create(&thr_arr[i], NULL, thr_func[i % 2], NULL);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < thr_num; i++) {
        ret = pthread_join(thr_arr[i], &thr_ret[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcUnSubscribe(stmt, subName);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"schema_datatype";
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 创建多个线程，一个线程一直使用视图查询，其他线程一直执行增删改查的操作
TEST_F(cltProcessFlowCtrl, DFX_049_007)
{
    int ret = 0;
    if (g_envType == MODE_DAP) {
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalShmSize=256\" \"maxTotalDynSize=512\""
               " \"clientServerFlowControl=1\" \"overloadThreshold=cpu:2,3,3,4,4,6\" "
               "\"dsFlowCtrlThreshold=8,10,12,15,17,20\"");
    } else {
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalShmSize=256\" \"maxTotalDynSize=512\""
               " \"clientServerFlowControl=1\" \"overloadThreshold=cpu:8,10,12,15,17,20\" "
               "\"dsFlowCtrlThreshold=8,10,12,15,17,20\"");
    }
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);
    int thr_num = 3;
    GmcStmtT *stmt = NULL;
    GmcConnT *conn = NULL;
    pthread_t thr_arr[thr_num];
    void *thr_ret[thr_num];
    thread_func thr_func[2] = {threadVertexWrite, threadFlowCtrlDFX};
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, schemaAllTypes, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < thr_num - 1; i++) {
        ret = pthread_create(&thr_arr[i], NULL, thr_func[0], NULL);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = pthread_create(&thr_arr[thr_num - 1], NULL, thr_func[1], NULL);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 0; i < thr_num; i++) {
        ret = pthread_join(thr_arr[i], &thr_ret[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    char *labelName = (char *)"schema_datatype";
    ret = system("gmsysview -q V\\$CLT_PROCESS_FLOWCTRL_INFO_LABEL");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 创建多个线程，一个线程一直使用视图查询，其他线程订阅后一直执行增删改查的操作
TEST_F(cltProcessFlowCtrl, DFX_049_008)
{
    int ret = 0;
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalShmSize=256\" \"maxTotalDynSize=512\""
           " \"clientServerFlowControl=1\" \"overloadThreshold=cpu:28,30,33,36,40,45\" "
           "\"dsFlowCtrlThreshold=8,10,12,15,17,20\"");
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);
    int thr_num = 3;
    GmcStmtT *stmt = NULL;
    GmcConnT *conn = NULL;
    pthread_t thr_arr[thr_num];
    void *thr_ret[thr_num];
    thread_func thr_func[2] = {threadVertexWrite, threadFlowCtrlDFX};
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, schemaAllTypes, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &g_chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);
    const char *subName = (char *)"subAllType";
    GmcSubConfigT tmp_schema;
    tmp_schema.subsName = subName;
    tmp_schema.configJson = subAllType;
    ret = GmcSubscribe(stmt, &tmp_schema, g_conn_sub, vertexSnCallback, g_userData);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < thr_num - 1; i++) {
        ret = pthread_create(&thr_arr[i], NULL, thr_func[0], NULL);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = pthread_create(&thr_arr[thr_num - 1], NULL, thr_func[1], NULL);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 0; i < thr_num; i++) {
        ret = pthread_join(thr_arr[i], &thr_ret[i]);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcUnSubscribe(stmt, subName);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"schema_datatype";
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 写大量数据触发流控，每10分钟使用流控视图查询一次。查看周期刷新的情况
TEST_F(cltProcessFlowCtrl, DFX_049_009)
{
    int ret = 0;
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalShmSize=256\" \"maxTotalDynSize=512\""
           " \"clientServerFlowControl=1\" \"overloadThreshold=cpu:8,10,12,15,17,20\" "
           "\"dsFlowCtrlThreshold=8,10,12,15,17,20\"");
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, schemaAllTypes, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    char *labelName = (char *)"schema_datatype";
    char stringValue[529];
    memset(stringValue, 'a', sizeof(stringValue) - 1);
    stringValue[528] = '\0';
    ret = writeVertex(RECORDCOUNTSTART, CLTFLOWTROLNUM, stmt, labelName, GMC_OPERATION_INSERT, conn);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        sleep(600);
        ret = system("gmsysview -q V\\$CLT_PROCESS_FLOWCTRL_INFO_LABEL");
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}
