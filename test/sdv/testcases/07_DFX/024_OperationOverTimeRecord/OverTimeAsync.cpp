/* ****************************************************************************
 Description  : 长事务视图 OPCODE字段测试
 Node      :
 01.基本功能（异步场景测试）
    DFX_024_Async_001 使用异步方式循环创建和删除顶点表100次，查看视图信息
    DFX_024_Async_002 使用异步方式truncate顶点表100次，查看视图信息
    DFX_024_Async_003 使用异步方式open 顶点表100次，查看视图信息
    DFX_024_Async_004 使用异步方式循环创建，切换，删除namespace100次，查看视图信息
    DFX_024_Async_005 使用异步方式循环insert和delete顶点表100次，查看视图信息
    DFX_024_Async_006 使用异步方式循环update顶点表100次，查看视图信息
    DFX_024_Async_007 使用异步方式循环merge和replace顶点表100次，查看视图信息
    DFX_024_Async_008 使用异步方式循环insert,过滤update和过滤delete顶点表100次，查看视图信息
    DFX_024_Async_009 使用异步方式循环创建和删除kv表100次，查看视图信息
    DFX_024_Async_010 使用异步方式truncate kv表100次，查看视图信息
    DFX_024_Async_011 使用异步方式open kv表100次，查看视图信息
    DFX_024_Async_012 使用异步方式循环set和delete kv表100次，查看视图信息
 Author       : 黄楚灿 hwx1007418
 Modification :
 Date         : 2021/07/12
**************************************************************************** */
extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "../../../../../src/common/include/protocol/db_rpc_msg_op.h"  //包含op code的枚举值

//#define DEBUG_PRINT 1


#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];
char cond_OpCode[128] = {0};
char const *view_name = "V\\$DRT_LONG_OPERATION_STAT";

GmcConnT *g_conn_async = NULL;
GmcStmtT *g_stmt_async = NULL;

AsyncUserDataT asyncUserData = {0};

class OperationOverTimeAsync_test : public testing::Test {
protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}

public:
    virtual void SetUp();
    virtual void TearDown();
};

void OperationOverTimeAsync_test::SetUp()
{
    AW_CHECK_LOG_BEGIN();
}

void OperationOverTimeAsync_test::TearDown()
{
    AW_CHECK_LOG_END();
}

int GetPrintBycmd(char *cmd, int *vaule)
{
    FILE *pf = popen(cmd, "r");
    if (pf == NULL) {
        printf("popen(%s) error./n", cmd);
        return -1;
    }
    char cmdOutput[64] = {0};
    while (NULL != fgets(cmdOutput, 64, pf))
        ;
    *vaule = atoi(cmdOutput);
    pclose(pf);
    return 0;
}
void set_VertexProperty_sourceVertex(
    GmcStmtT *stmt, uint32_t pk_value, uint8_t edge_vdata1, uint8_t edge_vdata2, uint8_t edge_vdata3)
{
    int ret = 0;
    uint8_t temp = 1;
    uint32_t V1_f5_value = pk_value;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT32, &V1_f5_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t V1_f0_value = edge_vdata1;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_BITFIELD8, &V1_f0_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t V1_f1_value = edge_vdata2;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_BITFIELD8, &V1_f1_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t V1_f2_value = temp;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_BITFIELD8, &V1_f2_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t V1_f3_value = edge_vdata3;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_BITFIELD8, &V1_f3_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t V1_f4_value = temp;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_BITFIELD8, &V1_f4_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
}
void set_VertexProperty_dstVertex(
    GmcStmtT *stmt, uint32_t pk_value, uint8_t edge_vdata1, uint8_t edge_vdata2, uint8_t edge_vdata3)
{
    int ret = 0;
    uint8_t temp = 2;
    uint32_t V2_f5_value = pk_value;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT32, &V2_f5_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t V2_f0_value = temp;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_BITFIELD8, &V2_f0_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t V2_f1_value = edge_vdata2;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_BITFIELD8, &V2_f1_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t V2_f2_value = edge_vdata1;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_BITFIELD8, &V2_f2_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t V2_f3_value = temp;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_BITFIELD8, &V2_f3_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t V2_f4_value = edge_vdata3;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_BITFIELD8, &V2_f4_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t V2_f6_value = temp;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_BITFIELD8, &V2_f6_value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
}
void set_VertexProperty_PK(GmcStmtT *stmt, uint64_t i)
{
    int ret = 0;
    uint64_t f7_value = i;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT64, &f7_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
}
void set_VertexProperty_localhash(GmcStmtT *stmt, int i)
{
    int ret = 0;
    int8_t f2_value = i;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &f2_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);
    int16_t f4_value = i + 2;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);
}
void set_VertexProperty_hashcluster(GmcStmtT *stmt, int i)
{
    int ret = 0;
    uint8_t f3_value = i;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &f3_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint16_t f5_value = i + 2;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);
}
void set_VertexProperty(GmcStmtT *stmt, int i, bool bool_value, char *f14_value, bool isupdate)
{
    int ret = 0;
    char f0_value = i;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &f0_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);
    unsigned char f1_vaule = i + 1;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &f0_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);
    int32_t f6_value = i + 2;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &f6_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    bool f8_value = bool_value;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);
    int64_t f9_value = i + 3;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &f9_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t f10_value = i + 4;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &f10_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    float f11_value = i + 5;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &f11_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);
    double f12_value = i + 6;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &f12_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t f13_value = i + 7;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &f13_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);
    uint8_t f17_value = (i + 8) % 255;  // 2^8
    ret = GmcSetVertexProperty(stmt, "F17", GMC_DATATYPE_BITFIELD8, &f17_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint16_t f18_value = (i + 9) % 4095;  // 2^12
    ret = GmcSetVertexProperty(stmt, "F18", GMC_DATATYPE_BITFIELD16, &f18_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t f19_value = (i + 10) % 268435455;  // 2^28
    ret = GmcSetVertexProperty(stmt, "F19", GMC_DATATYPE_BITFIELD32, &f19_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t f20_value = i + 11;  // 2^58
    ret = GmcSetVertexProperty(stmt, "F20", GMC_DATATYPE_BITFIELD64, &f20_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    // partition字段不允许修改
    if (isupdate == 0) {
        uint8_t f21_value = (12 + i) % 15;
        ret = GmcSetVertexProperty(stmt, (char *)"F21", GMC_DATATYPE_PARTITION, &f21_value, sizeof(uint8_t));
        ASSERT_EQ(GMERR_OK, ret);
    }
}
// 001 使用异步方式循环创建和删除顶点表100次，查看视图信息
TEST_F(OperationOverTimeAsync_test, DFX_024_001_031)
{
    int ret = 0;

    system("sh $TEST_HOME/tools/stop.sh");  //修改配置，先停服务
    system("ipcs");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"longProcTimeThreshold=0\"");  //修改时间阈值
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ipcs");

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);

    memset(&asyncUserData, 0, sizeof(asyncUserData));

    char *schema = NULL;
    char labelName[] = "T20";

    readJanssonFile("schema_file/byte_bitfield_type_continuous_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    for (int i = 0; i < 100; i++) {
        // creat vertexlabel
        ret = GmcCreateVertexLabelAsync(g_stmt_async, schema, NULL, create_vertex_label_callback, &asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncUserData.status);
        // drop vertexlabel
        ret = GmcDropVertexLabelAsync(g_stmt_async, labelName, drop_vertex_label_callback, &asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncUserData.status);
    }
    free(schema);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT,
        MSG_OP_RPC_CREATE_VERTEX_LABEL, MSG_OP_RPC_DROP_VERTEX_LABEL};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("*************test op code display end************\n");

    //比对视图信息,count预期
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
#ifdef DEBUG_PRINT
    printf("%s\n", g_command);
    system(g_command);
#else
    ret = executeCommand(g_command, "OPCODE", "COUNT", "TIME_AVERAGE", "TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
#endif
    /*COUNT校验*/
    int sum_count;
    int time_average;
    // creat vertexlabel
    sprintf(cond_OpCode, "OPCODE='%d'", MSG_OP_RPC_CREATE_VERTEX_LABEL);
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -s %s -q %s -f %s |grep -E 'COUNT' |awk -F '[:,]' 'NR==1{print $2}'", g_toolPath,
        g_connServer, view_name, cond_OpCode);
    ret = GetPrintBycmd(g_command, &sum_count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_LE(100, sum_count);
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -s %s -q %s -f %s |grep -E 'TIME_AVERAGE' |awk -F '[:,]' 'NR==1{print $2}'",
        g_toolPath, g_connServer, view_name, cond_OpCode);
    ret = GetPrintBycmd(g_command, &time_average);
    EXPECT_EQ(GMERR_OK, ret);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL %s count:%d averageTime:%6.3f ms\n", cond_OpCode, sum_count,
        (float)time_average / 1000);
    // drop vertexlabel
    sprintf(cond_OpCode, "OPCODE='%d'", MSG_OP_RPC_DROP_VERTEX_LABEL);
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -s %s -q %s -f %s |grep -E 'COUNT' |awk -F '[:,]' 'NR==1{print $2}'", g_toolPath,
        g_connServer, view_name, cond_OpCode);
    ret = GetPrintBycmd(g_command, &sum_count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_LE(100, sum_count);
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -s %s -q %s -f %s |grep -E 'TIME_AVERAGE' |awk -F '[:,]' 'NR==1{print $2}'",
        g_toolPath, g_connServer, view_name, cond_OpCode);
    ret = GetPrintBycmd(g_command, &time_average);
    EXPECT_EQ(GMERR_OK, ret);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL %s count:%d averageTime:%6.3f ms\n", cond_OpCode, sum_count,
        (float)time_average / 1000);
    /*OPCODE校验*/
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s >test_log", g_toolPath,
        g_connServer, view_name);
    system(g_command);
    //获取操作总数
    int operator_count = 0;
    snprintf(g_command, MAX_CMD_SIZE, "cat test_log | grep \"OPCODE\" -c");
    ret = GetPrintBycmd(g_command, &operator_count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_LE(count, operator_count);
    //比对实际的opcode预期
    for (int i = 0; i < operator_count; i++) {
        int line = i + 1;
        int GetOpCode;
        snprintf(g_command, MAX_CMD_SIZE,
            "cat test_log | grep \"OPCODE\"| awk -F \":\" '{print $2}'| awk 'NR==%d{print}'", line);
        ret = GetPrintBycmd(g_command, &GetOpCode);
        EXPECT_EQ(GMERR_OK, ret);
        for (int j = 0; j < count; j++) {
            if (GetOpCode == Check_OpCode[j])
                break;
            if (j == count - 1)
                printf("[error] the OpCode:%d no find \n", GetOpCode);
        }
    }

    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
}

// 002 使用异步方式truncate顶点表100次，查看视图信息
TEST_F(OperationOverTimeAsync_test, DFX_024_001_032)
{
    int ret = 0;

    system("sh $TEST_HOME/tools/stop.sh");  //修改配置，先停服务
    system("ipcs");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"longProcTimeThreshold=0\"");  //修改时间阈值
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ipcs");

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);

    memset(&asyncUserData, 0, sizeof(asyncUserData));

    char *schema = NULL;
    char labelName[] = "T20";

    readJanssonFile("schema_file/byte_bitfield_type_continuous_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    // creat vertexlabel edgelabel
    ret = GmcCreateVertexLabelAsync(g_stmt_async, schema, NULL, create_vertex_label_callback, &asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncUserData.status);

    //异步OPEN缓存表
    void *label = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < 100; i++) {
        // truncate vertex label
        ret = GmcTruncateVertexLabelAsync(g_stmt_async, labelName, truncate_vertex_label_callback, &asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncUserData);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(GMERR_OK, asyncUserData.status);
    }

    // drop vertexlabel edgelabel
    ret = GmcDropVertexLabelAsync(g_stmt_async, labelName, drop_vertex_label_callback, &asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncUserData.status);

    free(schema);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT,
        MSG_OP_RPC_CREATE_VERTEX_LABEL, MSG_OP_RPC_DROP_VERTEX_LABEL, MSG_OP_RPC_TRUNCATE_VERTEX_LABEL};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("MSG_OP_RPC_TRUNCATE_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_TRUNCATE_VERTEX_LABEL);
    printf("*************test op code display end************\n");

    //比对视图信息,count预期
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
#ifdef DEBUG_PRINT
    printf("%s\n", g_command);
    system(g_command);
#else
    ret = executeCommand(g_command, "OPCODE", "COUNT", "TIME_AVERAGE", "TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
#endif
    /*COUNT校验*/
    int sum_count;
    int time_average;
    // truncate vertexlabel
    sprintf(cond_OpCode, "OPCODE='%d'", MSG_OP_RPC_TRUNCATE_VERTEX_LABEL);
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -s %s -q %s -f %s |grep -E 'COUNT' |awk -F '[:,]' 'NR==1{print $2}'", g_toolPath,
        g_connServer, view_name, cond_OpCode);
    ret = GetPrintBycmd(g_command, &sum_count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_LE(100, sum_count);
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -s %s -q %s -f %s |grep -E 'TIME_AVERAGE' |awk -F '[:,]' 'NR==1{print $2}'",
        g_toolPath, g_connServer, view_name, cond_OpCode);
    ret = GetPrintBycmd(g_command, &time_average);
    EXPECT_EQ(GMERR_OK, ret);
    printf("MSG_OP_RPC_TRUNCATE_VERTEX_LABEL %s count:%d averageTime:%6.3f ms\n", cond_OpCode, sum_count,
        (float)time_average / 1000);
    /*OPCODE校验*/
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s >test_log", g_toolPath,
        g_connServer, view_name);
    system(g_command);
    //获取操作总数
    int operator_count = 0;
    snprintf(g_command, MAX_CMD_SIZE, "cat test_log | grep \"OPCODE\" -c");
    ret = GetPrintBycmd(g_command, &operator_count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_LE(count, operator_count);
    //比对实际的opcode预期
    for (int i = 0; i < operator_count; i++) {
        int line = i + 1;
        int GetOpCode;
        snprintf(g_command, MAX_CMD_SIZE,
            "cat test_log | grep \"OPCODE\"| awk -F \":\" '{print $2}'| awk 'NR==%d{print}'", line);
        ret = GetPrintBycmd(g_command, &GetOpCode);
        EXPECT_EQ(GMERR_OK, ret);
        for (int j = 0; j < count; j++) {
            if (GetOpCode == Check_OpCode[j])
                break;
            if (j == count - 1)
                printf("[error] the OpCode:%d no find \n", GetOpCode);
        }
    }

    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
}
#define TIMES_SYSTEM_003 (2 + 1 + 2 + 2)
// 003 使用异步方式open 顶点表100次，查看视图信息
TEST_F(OperationOverTimeAsync_test, DFX_024_001_033)
{
    int ret = 0;

    system("sh $TEST_HOME/tools/stop.sh");  //修改配置，先停服务
    system("ipcs");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"longProcTimeThreshold=0\"");  //修改时间阈值
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ipcs");

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);

    memset(&asyncUserData, 0, sizeof(asyncUserData));

    char vertexLabelJson[1024];
    char vertexLabelName[32] = "testT1";
    void *label = NULL;
    for (int j = 0; j < 100; j++) {
        sprintf(vertexLabelJson, "[{\
        \"type\":\"record\",\
        \"name\":\"testT%d\",\
        \"fields\":\
            [{\"name\":\"F0\", \"type\":\"uint8\", \"nullable\":false},\
            {\"name\":\"F1\", \"type\":\"float\",  \"nullable\":false},\
            {\"name\":\"F2\", \"type\":\"double\", \"nullable\":false},\
            {\"name\":\"F3\", \"type\":\"string\", \"nullable\":false, \"size\":100}],\
        \"keys\":\
            [{\"node\":\"testT%d\",\
            \"name\":\"T10_PK\",\
            \"fields\":[\"F0\"],\
            \"index\":{\"type\":\"primary\"},\
            \"constraints\":{\"unique\":true}}]\
        }]",
            j, j);
        sprintf(vertexLabelName, "testT%d", j);
        // create vertexlabel
        ret = GmcCreateVertexLabelAsync(g_stmt_async, vertexLabelJson, NULL, create_vertex_label_callback, &asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncUserData.status);
        // open vertexlabel
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, vertexLabelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        // drop vertexlabel
        ret = GmcDropVertexLabelAsync(g_stmt_async, vertexLabelName, drop_vertex_label_callback, &asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncUserData.status);
    }

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT,
        MSG_OP_RPC_CREATE_VERTEX_LABEL, MSG_OP_RPC_DROP_VERTEX_LABEL};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("*************test op code display end************\n");

    //比对视图信息,count预期
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
#ifdef DEBUG_PRINT
    printf("%s\n", g_command);
    system(g_command);
#else
    ret = executeCommand(g_command, "OPCODE", "COUNT", "TIME_AVERAGE", "TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
#endif
    /*COUNT校验*/
    int sum_count;
    int time_average;
    // open vertexlabel
    sprintf(cond_OpCode, "OPCODE='%d'", MSG_OP_RPC_GET_VERTEX_LABEL);
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -s %s -q %s -f %s |grep -E 'COUNT' |awk -F '[:,]' 'NR==1{print $2}'", g_toolPath,
        g_connServer, view_name, cond_OpCode);
    ret = GetPrintBycmd(g_command, &sum_count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(100 + TIMES_SYSTEM_003, sum_count);
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -s %s -q %s -f %s |grep -E 'TIME_AVERAGE' |awk -F '[:,]' 'NR==1{print $2}'",
        g_toolPath, g_connServer, view_name, cond_OpCode);
    ret = GetPrintBycmd(g_command, &time_average);
    EXPECT_EQ(GMERR_OK, ret);
    printf("MSG_OP_RPC_GET_VERTEX_LABEL %s count:%d averageTime:%6.3f ms\n", cond_OpCode, sum_count,
        (float)time_average / 1000);
    /*OPCODE校验*/
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s >test_log", g_toolPath,
        g_connServer, view_name);
    system(g_command);
    //获取操作总数
    int operator_count = 0;
    snprintf(g_command, MAX_CMD_SIZE, "cat test_log | grep \"OPCODE\" -c");
    ret = GetPrintBycmd(g_command, &operator_count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_LE(count, operator_count);
    //比对实际的opcode预期
    for (int i = 0; i < operator_count; i++) {
        int line = i + 1;
        int GetOpCode;
        snprintf(g_command, MAX_CMD_SIZE,
            "cat test_log | grep \"OPCODE\"| awk -F \":\" '{print $2}'| awk 'NR==%d{print}'", line);
        ret = GetPrintBycmd(g_command, &GetOpCode);
        EXPECT_EQ(GMERR_OK, ret);
        for (int j = 0; j < count; j++) {
            if (GetOpCode == Check_OpCode[j])
                break;
            if (j == count - 1)
                printf("[error] the OpCode:%d no find \n", GetOpCode);
        }
    }

    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
}
// 004 使用异步方式循环创建，切换，删除namespace100次，查看视图信息
TEST_F(OperationOverTimeAsync_test, DFX_024_001_034)
{
    int ret = 0;

    system("sh $TEST_HOME/tools/stop.sh");  //修改配置，先停服务
    system("ipcs");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"longProcTimeThreshold=0\"");  //修改时间阈值
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ipcs");

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);

    memset(&asyncUserData, 0, sizeof(asyncUserData));

    char const *usr_name = "userby025";
    char const *name_space = "CLASS1";
    for (int i = 0; i < 100; i++) {
        // create namespace
        ret = GmcCreateNamespaceAsync(g_stmt_async, name_space, usr_name, create_namespace_callback, &asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncUserData.status);
        // usr namespace
        ret = GmcUseNamespaceAsync(g_stmt_async, name_space, use_namespace_callback, &asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncUserData.status);
        // drop namespace
        ret = GmcDropNamespaceAsync(g_stmt_async, name_space, drop_namespace_callback, &asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncUserData.status);
    }

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT,
        MSG_OP_RPC_CREATE_NAMESPACE, MSG_OP_RPC_DROP_NAMESPACE, MSG_OP_RPC_USE_NAMESPACE};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_NAMESPACE : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_NAMESPACE);
    printf("MSG_OP_RPC_DROP_NAMESPACE   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_NAMESPACE);
    printf("MSG_OP_RPC_USE_NAMESPACE   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_USE_NAMESPACE);
    printf("*************test op code display end************\n");

    //比对视图信息,count预期
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
#ifdef DEBUG_PRINT
    printf("%s\n", g_command);
    system(g_command);
#else
    ret = executeCommand(g_command, "OPCODE", "COUNT", "TIME_AVERAGE", "TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
#endif
    /*COUNT校验*/
    int sum_count;
    int time_average;
    // create namespace
    sprintf(cond_OpCode, "OPCODE='%d'", MSG_OP_RPC_CREATE_NAMESPACE);
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -s %s -q %s -f %s |grep -E 'COUNT' |awk -F '[:,]' 'NR==1{print $2}'", g_toolPath,
        g_connServer, view_name, cond_OpCode);
    ret = GetPrintBycmd(g_command, &sum_count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_LE(100, sum_count);
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -s %s -q %s -f %s |grep -E 'TIME_AVERAGE' |awk -F '[:,]' 'NR==1{print $2}'",
        g_toolPath, g_connServer, view_name, cond_OpCode);
    ret = GetPrintBycmd(g_command, &time_average);
    EXPECT_EQ(GMERR_OK, ret);
    printf("MSG_OP_RPC_CREATE_NAMESPACE %s count:%d averageTime:%6.3f ms\n", cond_OpCode, sum_count,
        (float)time_average / 1000);
    // drop namespace
    sprintf(cond_OpCode, "OPCODE='%d'", MSG_OP_RPC_DROP_NAMESPACE);
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -s %s -q %s -f %s |grep -E 'COUNT' |awk -F '[:,]' 'NR==1{print $2}'", g_toolPath,
        g_connServer, view_name, cond_OpCode);
    ret = GetPrintBycmd(g_command, &sum_count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_LE(100, sum_count);
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -s %s -q %s -f %s |grep -E 'TIME_AVERAGE' |awk -F '[:,]' 'NR==1{print $2}'",
        g_toolPath, g_connServer, view_name, cond_OpCode);
    ret = GetPrintBycmd(g_command, &time_average);
    EXPECT_EQ(GMERR_OK, ret);
    printf("MSG_OP_RPC_DROP_NAMESPACE %s count:%d averageTime:%6.3f ms\n", cond_OpCode, sum_count,
        (float)time_average / 1000);
    // usr namespace
    sprintf(cond_OpCode, "OPCODE='%d'", MSG_OP_RPC_USE_NAMESPACE);
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -s %s -q %s -f %s |grep -E 'COUNT' |awk -F '[:,]' 'NR==1{print $2}'", g_toolPath,
        g_connServer, view_name, cond_OpCode);
    ret = GetPrintBycmd(g_command, &sum_count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_LE(100, sum_count);
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -s %s -q %s -f %s |grep -E 'TIME_AVERAGE' |awk -F '[:,]' 'NR==1{print $2}'",
        g_toolPath, g_connServer, view_name, cond_OpCode);
    ret = GetPrintBycmd(g_command, &time_average);
    EXPECT_EQ(GMERR_OK, ret);
    printf("MSG_OP_RPC_USE_NAMESPACE %s count:%d averageTime:%6.3f ms\n", cond_OpCode, sum_count,
        (float)time_average / 1000);
    /*OPCODE校验*/
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s >test_log", g_toolPath,
        g_connServer, view_name);
    system(g_command);
    //获取操作总数
    int operator_count = 0;
    snprintf(g_command, MAX_CMD_SIZE, "cat test_log | grep \"OPCODE\" -c");
    ret = GetPrintBycmd(g_command, &operator_count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_LE(count, operator_count);
    //比对实际的opcode预期
    for (int i = 0; i < operator_count; i++) {
        int line = i + 1;
        int GetOpCode;
        snprintf(g_command, MAX_CMD_SIZE,
            "cat test_log | grep \"OPCODE\"| awk -F \":\" '{print $2}'| awk 'NR==%d{print}'", line);
        ret = GetPrintBycmd(g_command, &GetOpCode);
        EXPECT_EQ(GMERR_OK, ret);
        for (int j = 0; j < count; j++) {
            if (GetOpCode == Check_OpCode[j])
                break;
            if (j == count - 1)
                printf("[error] the OpCode:%d no find \n", GetOpCode);
        }
    }

    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
}
// 005 使用异步方式循环insert和delete顶点表100次，查看视图信息
TEST_F(OperationOverTimeAsync_test, DFX_024_001_035)
{
    int ret = 0;

    system("sh $TEST_HOME/tools/stop.sh");  //修改配置，先停服务
    system("ipcs");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"longProcTimeThreshold=0\"");  //修改时间阈值
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ipcs");

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);

    memset(&asyncUserData, 0, sizeof(asyncUserData));

    char *schema = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    void *label = NULL;
    uint64_t key_value, locahash_value, hashcluster_value, value;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    // creat vertexlabel
    ret = GmcCreateVertexLabelAsync(g_stmt_async, schema, NULL, create_vertex_label_callback, &asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncUserData.status);
    printf("------------create vertexlabel------------\r\n");

    free(schema);

    // open vertexlabel
    printf("------------get and open vertexlabel------------\r\n");

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    for (int i = 0; i < 100; i++) {
        // insert record
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt_async, key_value);
        set_VertexProperty_localhash(g_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &asyncUserData;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncUserData.status);
        EXPECT_EQ(1, asyncUserData.affectRows);
        // delete record
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT64, &key_value, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, PKName);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT deleteRequestCtx;
        deleteRequestCtx.deleteCb = delete_vertex_callback;
        deleteRequestCtx.userData = &asyncUserData;
        ret = GmcExecuteAsync(g_stmt_async, &deleteRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncUserData.status);
        EXPECT_EQ(1, asyncUserData.affectRows);
    }

    // close vertexlabel
    EXPECT_EQ(GMERR_OK, ret);
    printf("------------close vertexlabel------------\r\n");

    // drop vertexlabel
    ret = GmcDropVertexLabelAsync(g_stmt_async, labelName, drop_vertex_label_callback, &asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncUserData.status);
    printf("------------drop vertexlabel------------\r\n");

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT,
        MSG_OP_RPC_CREATE_VERTEX_LABEL, MSG_OP_RPC_DROP_VERTEX_LABEL, MSG_OP_RPC_INSERT_VERTEX,
        MSG_OP_RPC_DELETE_VERTEX};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("MSG_OP_RPC_INSERT_VERTEX   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_INSERT_VERTEX);
    printf("MSG_OP_RPC_DELETE_VERTEX     : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DELETE_VERTEX);
    printf("*************test op code display end************\n");

    //比对视图信息,count预期
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
#ifdef DEBUG_PRINT
    printf("%s\n", g_command);
    system(g_command);
#else
    ret = executeCommand(g_command, "OPCODE", "COUNT", "TIME_AVERAGE", "TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
#endif
    /*COUNT校验*/
    int sum_count;
    int time_average;
    // insert record
    sprintf(cond_OpCode, "OPCODE='%d'", MSG_OP_RPC_INSERT_VERTEX);
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -s %s -q %s -f %s |grep -E 'COUNT' |awk -F '[:,]' 'NR==1{print $2}'", g_toolPath,
        g_connServer, view_name, cond_OpCode);
    ret = GetPrintBycmd(g_command, &sum_count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_LE(100, sum_count);
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -s %s -q %s -f %s |grep -E 'TIME_AVERAGE' |awk -F '[:,]' 'NR==1{print $2}'",
        g_toolPath, g_connServer, view_name, cond_OpCode);
    ret = GetPrintBycmd(g_command, &time_average);
    EXPECT_EQ(GMERR_OK, ret);
    printf("MSG_OP_RPC_INSERT_VERTEX %s count:%d averageTime:%6.3f ms\n", cond_OpCode, sum_count,
        (float)time_average / 1000);
    // delete record
    sprintf(cond_OpCode, "OPCODE='%d'", MSG_OP_RPC_DELETE_VERTEX);
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -s %s -q %s -f %s |grep -E 'COUNT' |awk -F '[:,]' 'NR==1{print $2}'", g_toolPath,
        g_connServer, view_name, cond_OpCode);
    ret = GetPrintBycmd(g_command, &sum_count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_LE(100, sum_count);
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -s %s -q %s -f %s |grep -E 'TIME_AVERAGE' |awk -F '[:,]' 'NR==1{print $2}'",
        g_toolPath, g_connServer, view_name, cond_OpCode);
    ret = GetPrintBycmd(g_command, &time_average);
    EXPECT_EQ(GMERR_OK, ret);
    printf("MSG_OP_RPC_DELETE_VERTEX %s count:%d averageTime:%6.3f ms\n", cond_OpCode, sum_count,
        (float)time_average / 1000);
    /*OPCODE校验*/
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s >test_log", g_toolPath,
        g_connServer, view_name);
    system(g_command);
    //获取操作总数
    int operator_count = 0;
    snprintf(g_command, MAX_CMD_SIZE, "cat test_log | grep \"OPCODE\" -c");
    ret = GetPrintBycmd(g_command, &operator_count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_LE(count, operator_count);
    //比对实际的opcode预期
    for (int i = 0; i < operator_count; i++) {
        int line = i + 1;
        int GetOpCode;
        snprintf(g_command, MAX_CMD_SIZE,
            "cat test_log | grep \"OPCODE\"| awk -F \":\" '{print $2}'| awk 'NR==%d{print}'", line);
        ret = GetPrintBycmd(g_command, &GetOpCode);
        EXPECT_EQ(GMERR_OK, ret);
        for (int j = 0; j < count; j++) {
            if (GetOpCode == Check_OpCode[j])
                break;
            if (j == count - 1)
                printf("[error] the OpCode:%d no find \n", GetOpCode);
        }
    }

    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
}
// 006 使用异步方式循环update顶点表100次，查看视图信息
TEST_F(OperationOverTimeAsync_test, DFX_024_001_036)
{
    int ret = 0;

    system("sh $TEST_HOME/tools/stop.sh");  //修改配置，先停服务
    system("ipcs");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"longProcTimeThreshold=0\"");  //修改时间阈值
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ipcs");

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);

    memset(&asyncUserData, 0, sizeof(asyncUserData));

    char *schema = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    void *label = NULL;
    uint64_t key_value, locahash_value, hashcluster_value, value;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    // creat vertexlabel
    ret = GmcCreateVertexLabelAsync(g_stmt_async, schema, NULL, create_vertex_label_callback, &asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncUserData.status);
    printf("------------create vertexlabel------------\r\n");

    free(schema);

    // open vertexlabel
    printf("------------get and open vertexlabel------------\r\n");

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    // insert one record
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    set_VertexProperty_PK(g_stmt_async, key_value);
    set_VertexProperty_localhash(g_stmt_async, locahash_value);
    set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
    set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = insert_vertex_callback;
    insertRequestCtx.userData = &asyncUserData;
    ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncUserData.status);
    EXPECT_EQ(1, asyncUserData.affectRows);

    for (int i = 0; i < 100; i++) {
        locahash_value++;
        // update record
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT64, &key_value, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 1);
        ret = GmcSetIndexKeyName(g_stmt_async, PKName);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT updateRequestCtx;
        updateRequestCtx.updateCb = update_vertex_callback;
        updateRequestCtx.userData = &asyncUserData;
        ret = GmcExecuteAsync(g_stmt_async, &updateRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncUserData.status);
        EXPECT_EQ(1, asyncUserData.affectRows);
    }

    // drop vertexlabel
    ret = GmcDropVertexLabelAsync(g_stmt_async, labelName, drop_vertex_label_callback, &asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncUserData.status);
    printf("------------drop vertexlabel------------\r\n");

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT,
        MSG_OP_RPC_CREATE_VERTEX_LABEL, MSG_OP_RPC_DROP_VERTEX_LABEL, MSG_OP_RPC_INSERT_VERTEX,
        MSG_OP_RPC_UPDATE_VERTEX};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("MSG_OP_RPC_INSERT_VERTEX   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_INSERT_VERTEX);
    printf("MSG_OP_RPC_UPDATE_VERTEX     : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_UPDATE_VERTEX);
    printf("*************test op code display end************\n");

    //比对视图信息,count预期
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
#ifdef DEBUG_PRINT
    printf("%s\n", g_command);
    system(g_command);
#else
    ret = executeCommand(g_command, "OPCODE", "COUNT", "TIME_AVERAGE", "TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
#endif
    /*COUNT校验*/
    int sum_count;
    int time_average;
    // update record
    sprintf(cond_OpCode, "OPCODE='%d'", MSG_OP_RPC_UPDATE_VERTEX);
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -s %s -q %s -f %s |grep -E 'COUNT' |awk -F '[:,]' 'NR==1{print $2}'", g_toolPath,
        g_connServer, view_name, cond_OpCode);
    ret = GetPrintBycmd(g_command, &sum_count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_LE(100, sum_count);
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -s %s -q %s -f %s |grep -E 'TIME_AVERAGE' |awk -F '[:,]' 'NR==1{print $2}'",
        g_toolPath, g_connServer, view_name, cond_OpCode);
    ret = GetPrintBycmd(g_command, &time_average);
    EXPECT_EQ(GMERR_OK, ret);
    printf("MSG_OP_RPC_UPDATE_VERTEX %s count:%d averageTime:%6.3f ms\n", cond_OpCode, sum_count,
        (float)time_average / 1000);
    /*OPCODE校验*/
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s >test_log", g_toolPath,
        g_connServer, view_name);
    system(g_command);
    //获取操作总数
    int operator_count = 0;
    snprintf(g_command, MAX_CMD_SIZE, "cat test_log | grep \"OPCODE\" -c");
    ret = GetPrintBycmd(g_command, &operator_count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_LE(count, operator_count);
    //比对实际的opcode预期
    for (int i = 0; i < operator_count; i++) {
        int line = i + 1;
        int GetOpCode;
        snprintf(g_command, MAX_CMD_SIZE,
            "cat test_log | grep \"OPCODE\"| awk -F \":\" '{print $2}'| awk 'NR==%d{print}'", line);
        ret = GetPrintBycmd(g_command, &GetOpCode);
        EXPECT_EQ(GMERR_OK, ret);
        for (int j = 0; j < count; j++) {
            if (GetOpCode == Check_OpCode[j])
                break;
            if (j == count - 1)
                printf("[error] the OpCode:%d no find \n", GetOpCode);
        }
    }

    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
}
// 007 使用异步方式循环merge和replace顶点表100次，查看视图信息
TEST_F(OperationOverTimeAsync_test, DFX_024_001_037)
{
    int ret = 0;

    system("sh $TEST_HOME/tools/stop.sh");  //修改配置，先停服务
    system("ipcs");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"longProcTimeThreshold=0\"");  //修改时间阈值
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ipcs");

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);

    memset(&asyncUserData, 0, sizeof(asyncUserData));

    char *schema = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    void *label = NULL;
    uint64_t key_value, locahash_value, hashcluster_value, value;

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    // creat vertexlabel
    ret = GmcCreateVertexLabelAsync(g_stmt_async, schema, NULL, create_vertex_label_callback, &asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncUserData.status);
    printf("------------create vertexlabel------------\r\n");

    free(schema);

    // open vertexlabel
    printf("------------get and open vertexlabel------------\r\n");

    key_value = 1;
    locahash_value = 2;
    hashcluster_value = 3;
    value = 4;
    for (int i = 0; i < 100; i++) {
        // replace record
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt_async, key_value);
        set_VertexProperty_localhash(g_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
        GmcAsyncRequestDoneContextT replaceRequestCtx;
        replaceRequestCtx.replaceCb = replace_vertex_callback;
        replaceRequestCtx.userData = &asyncUserData;
        ret = GmcExecuteAsync(g_stmt_async, &replaceRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncUserData.status);
        EXPECT_EQ(1, asyncUserData.affectRows);
        value++;
        // merge record
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_MERGE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT64, &key_value, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, PKName);
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_localhash(g_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
        set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 1);
        GmcAsyncRequestDoneContextT mergeRequestCtx;
        mergeRequestCtx.mergeCb = merge_vertex_callback;
        mergeRequestCtx.userData = &asyncUserData;
        ret = GmcExecuteAsync(g_stmt_async, &mergeRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncUserData.status);
        EXPECT_EQ(2, asyncUserData.affectRows);
        key_value++;
    }

    // drop vertexlabel
    ret = GmcDropVertexLabelAsync(g_stmt_async, labelName, drop_vertex_label_callback, &asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncUserData.status);
    printf("------------drop vertexlabel------------\r\n");

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT,
        MSG_OP_RPC_CREATE_VERTEX_LABEL, MSG_OP_RPC_DROP_VERTEX_LABEL, MSG_OP_RPC_REPLACE_VERTEX,
        MSG_OP_RPC_MERGE_VERTEX};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("MSG_OP_RPC_REPLACE_VERTEX   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_REPLACE_VERTEX);
    printf("MSG_OP_RPC_MERGE_VERTEX     : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_MERGE_VERTEX);
    printf("*************test op code display end************\n");

    //比对视图信息,count预期
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
#ifdef DEBUG_PRINT
    printf("%s\n", g_command);
    system(g_command);
#else
    ret = executeCommand(g_command, "OPCODE", "COUNT", "TIME_AVERAGE", "TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
#endif
    /*COUNT校验*/
    int sum_count;
    int time_average;
    // replace record
    sprintf(cond_OpCode, "OPCODE='%d'", MSG_OP_RPC_REPLACE_VERTEX);
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -s %s -q %s -f %s |grep -E 'COUNT' |awk -F '[:,]' 'NR==1{print $2}'", g_toolPath,
        g_connServer, view_name, cond_OpCode);
    ret = GetPrintBycmd(g_command, &sum_count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_LE(100, sum_count);
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -s %s -q %s -f %s |grep -E 'TIME_AVERAGE' |awk -F '[:,]' 'NR==1{print $2}'",
        g_toolPath, g_connServer, view_name, cond_OpCode);
    ret = GetPrintBycmd(g_command, &time_average);
    EXPECT_EQ(GMERR_OK, ret);
    printf("MSG_OP_RPC_REPLACE_VERTEX %s count:%d averageTime:%6.3f ms\n", cond_OpCode, sum_count,
        (float)time_average / 1000);
    // merge record
    sprintf(cond_OpCode, "OPCODE='%d'", MSG_OP_RPC_MERGE_VERTEX);
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -s %s -q %s -f %s |grep -E 'COUNT' |awk -F '[:,]' 'NR==1{print $2}'", g_toolPath,
        g_connServer, view_name, cond_OpCode);
    ret = GetPrintBycmd(g_command, &sum_count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_LE(100, sum_count);
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -s %s -q %s -f %s |grep -E 'TIME_AVERAGE' |awk -F '[:,]' 'NR==1{print $2}'",
        g_toolPath, g_connServer, view_name, cond_OpCode);
    ret = GetPrintBycmd(g_command, &time_average);
    EXPECT_EQ(GMERR_OK, ret);
    printf("MSG_OP_RPC_MERGE_VERTEX %s count:%d averageTime:%6.3f ms\n", cond_OpCode, sum_count,
        (float)time_average / 1000);

    /*OPCODE校验*/
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s >test_log", g_toolPath,
        g_connServer, view_name);
    system(g_command);
    //获取操作总数
    int operator_count = 0;
    snprintf(g_command, MAX_CMD_SIZE, "cat test_log | grep \"OPCODE\" -c");
    ret = GetPrintBycmd(g_command, &operator_count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_LE(count, operator_count);
    //比对实际的opcode预期
    for (int i = 0; i < operator_count; i++) {
        int line = i + 1;
        int GetOpCode;
        snprintf(g_command, MAX_CMD_SIZE,
            "cat test_log | grep \"OPCODE\"| awk -F \":\" '{print $2}'| awk 'NR==%d{print}'", line);
        ret = GetPrintBycmd(g_command, &GetOpCode);
        EXPECT_EQ(GMERR_OK, ret);
        for (int j = 0; j < count; j++) {
            if (GetOpCode == Check_OpCode[j])
                break;
            if (j == count - 1)
                printf("[error] the OpCode:%d no find \n", GetOpCode);
        }
    }

    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
}
// 008 使用异步方式循环insert,过滤update和过滤delete顶点表100次，查看视图信息
TEST_F(OperationOverTimeAsync_test, DFX_024_001_038)
{
    int ret = 0;

    system("sh $TEST_HOME/tools/stop.sh");  //修改配置，先停服务
    system("ipcs");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"longProcTimeThreshold=0\"");  //修改时间阈值
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ipcs");

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);

    memset(&asyncUserData, 0, sizeof(asyncUserData));

    char *schema = NULL;
    char labelName[] = "T25";
    char PKName[] = "T25_PK";
    void *label = NULL;
    uint64_t key_value, locahash_value, hashcluster_value, value;
    int end_num = 100;
    char cond[] = "F7>0";

    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    // creat vertexlabel
    ret = GmcCreateVertexLabelAsync(g_stmt_async, schema, NULL, create_vertex_label_callback, &asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncUserData.status);
    printf("------------create vertexlabel------------\r\n");

    free(schema);

    for (int i = 0; i < 100; i++) {
        // insert record
        key_value = 1;
        locahash_value = 2;
        hashcluster_value = 3;
        value = 4;
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        for (int j = 0; j < end_num; j++) {
            set_VertexProperty_PK(g_stmt_async, key_value);
            set_VertexProperty_localhash(g_stmt_async, locahash_value);
            set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
            set_VertexProperty(g_stmt_async, value, 0, (char *)"string", 0);
            GmcAsyncRequestDoneContextT insertRequestCtx;
            insertRequestCtx.insertCb = insert_vertex_callback;
            insertRequestCtx.userData = &asyncUserData;
            ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
            EXPECT_EQ(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&asyncUserData);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(GMERR_OK, asyncUserData.status);
            EXPECT_EQ(1, asyncUserData.affectRows);
            key_value++;
            value++;
        }
        // cond update
        locahash_value = 3;
        hashcluster_value = 4;
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_localhash(g_stmt_async, locahash_value);
        set_VertexProperty_hashcluster(g_stmt_async, hashcluster_value);
        ret = GmcSetFilter(g_stmt_async, cond);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT updateByCondRequestCtx;
        updateByCondRequestCtx.updateCb = update_vertex_by_cond_callback;
        updateByCondRequestCtx.userData = &asyncUserData;
        ret = GmcExecuteAsync(g_stmt_async, &updateByCondRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncUserData.status);
        EXPECT_EQ(end_num, asyncUserData.affectRows);
        // cond delete
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetFilter(g_stmt_async, cond);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT deleteByCondRequestCtx;
        deleteByCondRequestCtx.deleteCb = delete_vertex_by_cond_callback;
        deleteByCondRequestCtx.userData = &asyncUserData;
        ret = GmcExecuteAsync(g_stmt_async, &deleteByCondRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncUserData.status);
        EXPECT_EQ(end_num, asyncUserData.affectRows);
    }
    // drop vertexlabel
    ret = GmcDropVertexLabelAsync(g_stmt_async, labelName, drop_vertex_label_callback, &asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncUserData.status);
    printf("------------drop vertexlabel------------\r\n");

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT,
        MSG_OP_RPC_CREATE_VERTEX_LABEL, MSG_OP_RPC_DROP_VERTEX_LABEL, MSG_OP_RPC_INSERT_VERTEX,
        MSG_OP_RPC_DELETE_VERTEX, MSG_OP_RPC_UPDATE_VERTEX};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_VERTEX_LABEL : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_VERTEX_LABEL);
    printf("MSG_OP_RPC_DROP_VERTEX_LABEL   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_VERTEX_LABEL);
    printf("MSG_OP_RPC_INSERT_VERTEX   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_INSERT_VERTEX);
    printf("MSG_OP_RPC_DELETE_VERTEX     : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DELETE_VERTEX);
    printf("MSG_OP_RPC_UPDATE_VERTEX     : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_UPDATE_VERTEX);
    printf("*************test op code display end************\n");

    //比对视图信息,count预期
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
#ifdef DEBUG_PRINT
    printf("%s\n", g_command);
    system(g_command);
#else
    ret = executeCommand(g_command, "OPCODE", "COUNT", "TIME_AVERAGE", "TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
#endif
    /*COUNT校验*/
    int sum_count;
    int time_average;
    // cond update record
    sprintf(cond_OpCode, "OPCODE='%d'", MSG_OP_RPC_UPDATE_VERTEX);
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -s %s -q %s -f %s |grep -E 'COUNT' |awk -F '[:,]' 'NR==1{print $2}'", g_toolPath,
        g_connServer, view_name, cond_OpCode);
    ret = GetPrintBycmd(g_command, &sum_count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_LE(100, sum_count);
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -s %s -q %s -f %s |grep -E 'TIME_AVERAGE' |awk -F '[:,]' 'NR==1{print $2}'",
        g_toolPath, g_connServer, view_name, cond_OpCode);
    ret = GetPrintBycmd(g_command, &time_average);
    EXPECT_EQ(GMERR_OK, ret);
    printf("MSG_OP_RPC_UPDATE_VERTEX %s count:%d averageTime:%6.3f ms\n", cond_OpCode, sum_count,
        (float)time_average / 1000);
    // cond delete record
    sprintf(cond_OpCode, "OPCODE='%d'", MSG_OP_RPC_DELETE_VERTEX);
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -s %s -q %s -f %s |grep -E 'COUNT' |awk -F '[:,]' 'NR==1{print $2}'", g_toolPath,
        g_connServer, view_name, cond_OpCode);
    ret = GetPrintBycmd(g_command, &sum_count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_LE(100, sum_count);
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -s %s -q %s -f %s |grep -E 'TIME_AVERAGE' |awk -F '[:,]' 'NR==1{print $2}'",
        g_toolPath, g_connServer, view_name, cond_OpCode);
    ret = GetPrintBycmd(g_command, &time_average);
    EXPECT_EQ(GMERR_OK, ret);
    printf("MSG_OP_RPC_DELETE_VERTEX %s count:%d averageTime:%6.3f ms\n", cond_OpCode, sum_count,
        (float)time_average / 1000);
    /*OPCODE校验*/
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s >test_log", g_toolPath,
        g_connServer, view_name);
    system(g_command);
    //获取操作总数
    int operator_count = 0;
    snprintf(g_command, MAX_CMD_SIZE, "cat test_log | grep \"OPCODE\" -c");
    ret = GetPrintBycmd(g_command, &operator_count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_LE(count, operator_count);
    //比对实际的opcode预期
    for (int i = 0; i < operator_count; i++) {
        int line = i + 1;
        int GetOpCode;
        snprintf(g_command, MAX_CMD_SIZE,
            "cat test_log | grep \"OPCODE\"| awk -F \":\" '{print $2}'| awk 'NR==%d{print}'", line);
        ret = GetPrintBycmd(g_command, &GetOpCode);
        EXPECT_EQ(GMERR_OK, ret);
        for (int j = 0; j < count; j++) {
            if (GetOpCode == Check_OpCode[j])
                break;
            if (j == count - 1)
                printf("[error] the OpCode:%d no find \n", GetOpCode);
        }
    }

    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
}
// 009 使用异步方式循环创建和删除kv表100次，查看视图信息
TEST_F(OperationOverTimeAsync_test, DFX_024_001_039)
{
    int ret = 0;

    system("sh $TEST_HOME/tools/stop.sh");  //修改配置，先停服务
    system("ipcs");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"longProcTimeThreshold=0\"");  //修改时间阈值
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ipcs");

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);

    memset(&asyncUserData, 0, sizeof(asyncUserData));

    char kv_name[128] = "KV0";
    char configJson[128] = "{\"max_record_count\" : 1000}";
    for (int i = 0; i < 100; i++) {
        // create kv table
        ret = GmcKvCreateTableAsync(g_stmt_async, kv_name, configJson, create_kv_table_callback, &asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncUserData.status);
        // drop kv table
        ret = GmcKvDropTableAsync(g_stmt_async, kv_name, drop_kv_table_callback, &asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncUserData.status);
    }

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT,
        MSG_OP_RPC_CREATE_KV_TABLE, MSG_OP_RPC_DROP_KV_TABLE};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_KV_TABLE : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_KV_TABLE);
    printf("MSG_OP_RPC_DROP_KV_TABLE   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_KV_TABLE);
    printf("*************test op code display end************\n");

    //比对视图信息,count预期
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
#ifdef DEBUG_PRINT
    printf("%s\n", g_command);
    system(g_command);
#else
    ret = executeCommand(g_command, "OPCODE", "COUNT", "TIME_AVERAGE", "TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
#endif
    /*COUNT校验*/
    int sum_count;
    int time_average;
    // create kv vertex
    sprintf(cond_OpCode, "OPCODE='%d'", MSG_OP_RPC_DROP_KV_TABLE);
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -s %s -q %s -f %s |grep -E 'COUNT' |awk -F '[:,]' 'NR==1{print $2}'", g_toolPath,
        g_connServer, view_name, cond_OpCode);
    ret = GetPrintBycmd(g_command, &sum_count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(100, sum_count);
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -s %s -q %s -f %s |grep -E 'TIME_AVERAGE' |awk -F '[:,]' 'NR==1{print $2}'",
        g_toolPath, g_connServer, view_name, cond_OpCode);
    ret = GetPrintBycmd(g_command, &time_average);
    EXPECT_EQ(GMERR_OK, ret);
    printf("MSG_OP_RPC_DROP_KV_TABLE %s count:%d averageTime:%6.3f ms\n", cond_OpCode, sum_count,
        (float)time_average / 1000);
    // drop kv vertex
    sprintf(cond_OpCode, "OPCODE='%d'", MSG_OP_RPC_DROP_KV_TABLE);
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -s %s -q %s -f %s |grep -E 'COUNT' |awk -F '[:,]' 'NR==1{print $2}'", g_toolPath,
        g_connServer, view_name, cond_OpCode);
    ret = GetPrintBycmd(g_command, &sum_count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(100, sum_count);
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -s %s -q %s -f %s |grep -E 'TIME_AVERAGE' |awk -F '[:,]' 'NR==1{print $2}'",
        g_toolPath, g_connServer, view_name, cond_OpCode);
    ret = GetPrintBycmd(g_command, &time_average);
    EXPECT_EQ(GMERR_OK, ret);
    printf("MSG_OP_RPC_DROP_KV_TABLE %s count:%d averageTime:%6.3f ms\n", cond_OpCode, sum_count,
        (float)time_average / 1000);
    /*OPCODE校验*/
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s >test_log", g_toolPath,
        g_connServer, view_name);
    system(g_command);
    //获取操作总数
    int operator_count = 0;
    snprintf(g_command, MAX_CMD_SIZE, "cat test_log | grep \"OPCODE\" -c");
    ret = GetPrintBycmd(g_command, &operator_count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_LE(count, operator_count);
    //比对实际的opcode预期
    for (int i = 0; i < operator_count; i++) {
        int line = i + 1;
        int GetOpCode;
        snprintf(g_command, MAX_CMD_SIZE,
            "cat test_log | grep \"OPCODE\"| awk -F \":\" '{print $2}'| awk 'NR==%d{print}'", line);
        ret = GetPrintBycmd(g_command, &GetOpCode);
        EXPECT_EQ(GMERR_OK, ret);
        for (int j = 0; j < count; j++) {
            if (GetOpCode == Check_OpCode[j])
                break;
            if (j == count - 1)
                printf("[error] the OpCode:%d no find \n", GetOpCode);
        }
    }

    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
}
// 010 使用异步方式truncate kv表100次，查看视图信息
//异步创建KV表后，需要执行GmcStoreKvTableToCacheAsync，才能够执行truncate，直接执行有概率报15000
TEST_F(OperationOverTimeAsync_test, DFX_024_001_040)
{
    int ret = 0;

    system("sh $TEST_HOME/tools/stop.sh");  //修改配置，先停服务
    system("ipcs");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"longProcTimeThreshold=0\"");  //修改时间阈值
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ipcs");

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);

    memset(&asyncUserData, 0, sizeof(asyncUserData));

    char kv_name[128] = "KV0";
    char configJson[128] = "{\"max_record_count\" : 1000}";

    // create kv table
    ret = GmcKvCreateTableAsync(g_stmt_async, kv_name, configJson, create_kv_table_callback, &asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncUserData.status);

    //异步OPEN缓存表
    void *kvlable = NULL;
    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, kv_name);
    EXPECT_EQ(GMERR_OK, ret);
    // ret = GmcCloseKvTable(g_stmt_async);
    // EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < 100; i++) {
        // truncate kv table
        ret = GmcKvTruncateTableAsync(g_stmt_async, kv_name, truncate_kv_table_callback, &asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncUserData.status);
    }

    // drop kv table
    ret = GmcKvDropTableAsync(g_stmt_async, kv_name, drop_kv_table_callback, &asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncUserData.status);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT,
        MSG_OP_RPC_CREATE_KV_TABLE, MSG_OP_RPC_DROP_KV_TABLE, MSG_OP_RPC_GET_KV_TABLE, MSG_OP_RPC_TRUNCATE_KV_TABLE};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_KV_TABLE : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_KV_TABLE);
    printf("MSG_OP_RPC_DROP_KV_TABLE   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_KV_TABLE);
    printf("MSG_OP_RPC_GET_KV_TABLE   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_KV_TABLE);
    printf("MSG_OP_RPC_TRUNCATE_KV_TABLE   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_TRUNCATE_KV_TABLE);
    printf("*************test op code display end************\n");

    //比对视图信息,count预期
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
#ifdef DEBUG_PRINT
    printf("%s\n", g_command);
    system(g_command);
#else
    ret = executeCommand(g_command, "OPCODE", "COUNT", "TIME_AVERAGE", "TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
#endif
    /*COUNT校验*/
    int sum_count;
    int time_average;
    // truncate kv vertex
    sprintf(cond_OpCode, "OPCODE='%d'", MSG_OP_RPC_TRUNCATE_KV_TABLE);
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -s %s -q %s -f %s |grep -E 'COUNT' |awk -F '[:,]' 'NR==1{print $2}'", g_toolPath,
        g_connServer, view_name, cond_OpCode);
    ret = GetPrintBycmd(g_command, &sum_count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(100, sum_count);
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -s %s -q %s -f %s |grep -E 'TIME_AVERAGE' |awk -F '[:,]' 'NR==1{print $2}'",
        g_toolPath, g_connServer, view_name, cond_OpCode);
    ret = GetPrintBycmd(g_command, &time_average);
    EXPECT_EQ(GMERR_OK, ret);
    printf("MSG_OP_RPC_TRUNCATE_KV_TABLE %s count:%d averageTime:%6.3f ms\n", cond_OpCode, sum_count,
        (float)time_average / 1000);
    /*OPCODE校验*/
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s >test_log", g_toolPath,
        g_connServer, view_name);
    system(g_command);
    //获取操作总数
    int operator_count = 0;
    snprintf(g_command, MAX_CMD_SIZE, "cat test_log | grep \"OPCODE\" -c");
    ret = GetPrintBycmd(g_command, &operator_count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_LE(count, operator_count);
    //比对实际的opcode预期
    for (int i = 0; i < operator_count; i++) {
        int line = i + 1;
        int GetOpCode;
        snprintf(g_command, MAX_CMD_SIZE,
            "cat test_log | grep \"OPCODE\"| awk -F \":\" '{print $2}'| awk 'NR==%d{print}'", line);
        ret = GetPrintBycmd(g_command, &GetOpCode);
        EXPECT_EQ(GMERR_OK, ret);
        for (int j = 0; j < count; j++) {
            if (GetOpCode == Check_OpCode[j])
                break;
            if (j == count - 1)
                printf("[error] the OpCode:%d no find \n", GetOpCode);
        }
    }

    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
}
// 011 使用异步方式open kv表100次，查看视图信息
TEST_F(OperationOverTimeAsync_test, DFX_024_001_041)
{
    int ret = 0;

    system("sh $TEST_HOME/tools/stop.sh");  //修改配置，先停服务
    system("ipcs");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"longProcTimeThreshold=0\"");  //修改时间阈值
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ipcs");

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);

    memset(&asyncUserData, 0, sizeof(asyncUserData));

    char kv_name[128] = "KV0";
    char configJson[128] = "{\"max_record_count\" : 1000}";
    void *kvlable = NULL;
    for (int i = 0; i < 100; i++) {
        sprintf(kv_name, "KV%d", i);
        // create kv table
        ret = GmcKvCreateTableAsync(g_stmt_async, kv_name, configJson, create_kv_table_callback, &asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncUserData.status);
        // open kv table
        ret = GmcKvPrepareStmtByLabelName(g_stmt_async, kv_name);
        EXPECT_EQ(GMERR_OK, ret);
        // close kv table
        // ret = GmcCloseKvTable(g_stmt_async);
        // EXPECT_EQ(GMERR_OK, ret);
        // drop kv
        ret = GmcKvDropTableAsync(g_stmt_async, kv_name, drop_kv_table_callback, &asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncUserData.status);
    }

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT,
        MSG_OP_RPC_CREATE_KV_TABLE, MSG_OP_RPC_DROP_KV_TABLE, MSG_OP_RPC_GET_KV_TABLE};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_KV_TABLE : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_KV_TABLE);
    printf("MSG_OP_RPC_DROP_KV_TABLE   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_KV_TABLE);
    printf("MSG_OP_RPC_GET_KV_TABLE   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_KV_TABLE);
    printf("*************test op code display end************\n");

    //比对视图信息,count预期
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
#ifdef DEBUG_PRINT
    printf("%s\n", g_command);
    system(g_command);
#else
    ret = executeCommand(g_command, "OPCODE", "COUNT", "TIME_AVERAGE", "TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
#endif
    /*COUNT校验*/
    int sum_count;
    int time_average;
    // open kv vertex
    sprintf(cond_OpCode, "OPCODE='%d'", MSG_OP_RPC_GET_KV_TABLE);
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -s %s -q %s -f %s |grep -E 'COUNT' |awk -F '[:,]' 'NR==1{print $2}'", g_toolPath,
        g_connServer, view_name, cond_OpCode);
    ret = GetPrintBycmd(g_command, &sum_count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(100, sum_count);
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -s %s -q %s -f %s |grep -E 'TIME_AVERAGE' |awk -F '[:,]' 'NR==1{print $2}'",
        g_toolPath, g_connServer, view_name, cond_OpCode);
    ret = GetPrintBycmd(g_command, &time_average);
    EXPECT_EQ(GMERR_OK, ret);
    printf("MSG_OP_RPC_GET_KV_TABLE %s count:%d averageTime:%6.3f ms\n", cond_OpCode, sum_count,
        (float)time_average / 1000);
    /*OPCODE校验*/
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s >test_log", g_toolPath,
        g_connServer, view_name);
    system(g_command);
    //获取操作总数
    int operator_count = 0;
    snprintf(g_command, MAX_CMD_SIZE, "cat test_log | grep \"OPCODE\" -c");
    ret = GetPrintBycmd(g_command, &operator_count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_LE(count, operator_count);
    //比对实际的opcode预期
    for (int i = 0; i < operator_count; i++) {
        int line = i + 1;
        int GetOpCode;
        snprintf(g_command, MAX_CMD_SIZE,
            "cat test_log | grep \"OPCODE\"| awk -F \":\" '{print $2}'| awk 'NR==%d{print}'", line);
        ret = GetPrintBycmd(g_command, &GetOpCode);
        EXPECT_EQ(GMERR_OK, ret);
        for (int j = 0; j < count; j++) {
            if (GetOpCode == Check_OpCode[j])
                break;
            if (j == count - 1)
                printf("[error] the OpCode:%d no find \n", GetOpCode);
        }
    }

    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
}
// 012 使用异步方式循环set和delete kv表100次，查看视图信息
TEST_F(OperationOverTimeAsync_test, DFX_024_001_042)
{
    int ret = 0;

    system("sh $TEST_HOME/tools/stop.sh");  //修改配置，先停服务
    system("ipcs");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"longProcTimeThreshold=0\"");  //修改时间阈值
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ipcs");

    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);

    memset(&asyncUserData, 0, sizeof(asyncUserData));

    char kv_name[128] = "KV0";
    char configJson[128] = "{\"max_record_count\" : 1000}";
    void *kvlable = NULL;
    char key[32] = "zhangsan";
    int32_t value = 1;
    GmcKvTupleT kvInfo = {0};
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);

    // create kv table
    ret = GmcKvCreateTableAsync(g_stmt_async, kv_name, configJson, create_kv_table_callback, &asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncUserData.status);
    // open kv table
    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, kv_name);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < 100; i++) {
        // set kv
        ret = GmcKvSetAsync(g_stmt_async, &kvInfo, set_kv_callback, &asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncUserData.status);
        // delete kv
        ret = GmcKvRemoveAsync(g_stmt_async, key, strlen(key), delete_kv_callback, &asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncUserData.status);
    }

    // close kv table
    // ret = GmcCloseKvTable(g_stmt_async);
    // EXPECT_EQ(GMERR_OK, ret);
    // drop kv
    ret = GmcKvDropTableAsync(g_stmt_async, kv_name, drop_kv_table_callback, &asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncUserData.status);

    MsgOpcodeRpcE Check_OpCode[] = {MSG_OP_RPC_GET_VERTEX_LABEL, MSG_OP_RPC_SCAN_VERTEX_BEGIN, MSG_OP_RPC_CONNECT,
        MSG_OP_RPC_DISCONNECT, MSG_OP_RPC_RELEASE_STMT,
        MSG_OP_RPC_CREATE_KV_TABLE, MSG_OP_RPC_DROP_KV_TABLE, MSG_OP_RPC_SET_KV, MSG_OP_RPC_DELETE_KV,
        MSG_OP_RPC_GET_KV_TABLE};
    unsigned int count = sizeof(Check_OpCode) / sizeof(MsgOpcodeRpcE);
    printf("************test op code name and num************\n");
    printf("MSG_OP_RPC_GET_VERTEX_LABEL  : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_VERTEX_LABEL);
    printf("MSG_OP_RPC_SCAN_VERTEX_BEGIN : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SCAN_VERTEX_BEGIN);
    printf("MSG_OP_RPC_CONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CONNECT);
    printf("MSG_OP_RPC_DISCONNECT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DISCONNECT);
    printf("MSG_OP_RPC_RELEASE_STMT : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_RELEASE_STMT);
    printf("MSG_OP_RPC_CREATE_KV_TABLE : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_CREATE_KV_TABLE);
    printf("MSG_OP_RPC_DROP_KV_TABLE   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DROP_KV_TABLE);
    printf("MSG_OP_RPC_GET_KV_TABLE   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_GET_KV_TABLE);
    printf("MSG_OP_RPC_SET_KV   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_SET_KV);
    printf("MSG_OP_RPC_DELETE_KV   : %d\n", (MsgOpcodeRpcE)MSG_OP_RPC_DELETE_KV);
    printf("*************test op code display end************\n");

    //比对视图信息,count预期
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
        view_name);
#ifdef DEBUG_PRINT
    printf("%s\n", g_command);
    system(g_command);
#else
    ret = executeCommand(g_command, "OPCODE", "COUNT", "TIME_AVERAGE", "TIME_MAX");
    EXPECT_EQ(GMERR_OK, ret);
#endif
    /*COUNT校验*/
    int sum_count;
    int time_average;
    // set kv vertex
    sprintf(cond_OpCode, "OPCODE='%d'", MSG_OP_RPC_SET_KV);
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -s %s -q %s -f %s |grep -E 'COUNT' |awk -F '[:,]' 'NR==1{print $2}'", g_toolPath,
        g_connServer, view_name, cond_OpCode);
    ret = GetPrintBycmd(g_command, &sum_count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(100, sum_count);
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -s %s -q %s -f %s |grep -E 'TIME_AVERAGE' |awk -F '[:,]' 'NR==1{print $2}'",
        g_toolPath, g_connServer, view_name, cond_OpCode);
    ret = GetPrintBycmd(g_command, &time_average);
    EXPECT_EQ(GMERR_OK, ret);
    printf("MSG_OP_RPC_SET_KV %s count:%d averageTime:%6.3f ms\n", cond_OpCode, sum_count, (float)time_average / 1000);
    // delete kv
    sprintf(cond_OpCode, "OPCODE='%d'", MSG_OP_RPC_DELETE_KV);
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -s %s -q %s -f %s |grep -E 'COUNT' |awk -F '[:,]' 'NR==1{print $2}'", g_toolPath,
        g_connServer, view_name, cond_OpCode);
    ret = GetPrintBycmd(g_command, &sum_count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(100, sum_count);
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -s %s -q %s -f %s |grep -E 'TIME_AVERAGE' |awk -F '[:,]' 'NR==1{print $2}'",
        g_toolPath, g_connServer, view_name, cond_OpCode);
    ret = GetPrintBycmd(g_command, &time_average);
    EXPECT_EQ(GMERR_OK, ret);
    printf(
        "MSG_OP_RPC_DELETE_KV %s count:%d averageTime:%6.3f ms\n", cond_OpCode, sum_count, (float)time_average / 1000);
    /*OPCODE校验*/
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s >test_log", g_toolPath,
        g_connServer, view_name);
    system(g_command);
    //获取操作总数
    int operator_count = 0;
    snprintf(g_command, MAX_CMD_SIZE, "cat test_log | grep \"OPCODE\" -c");
    ret = GetPrintBycmd(g_command, &operator_count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_LE(count, operator_count);
    //比对实际的opcode预期
    for (int i = 0; i < operator_count; i++) {
        int line = i + 1;
        int GetOpCode;
        snprintf(g_command, MAX_CMD_SIZE,
            "cat test_log | grep \"OPCODE\"| awk -F \":\" '{print $2}'| awk 'NR==%d{print}'", line);
        ret = GetPrintBycmd(g_command, &GetOpCode);
        EXPECT_EQ(GMERR_OK, ret);
        for (int j = 0; j < count; j++) {
            if (GetOpCode == Check_OpCode[j])
                break;
            if (j == count - 1)
                printf("[error] the OpCode:%d no find \n", GetOpCode);
        }
    }

    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
}
