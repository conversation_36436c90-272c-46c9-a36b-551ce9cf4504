/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: included by is_config_filter_async_test.cpp & is_config_filter_test.cpp
 * Author: liwenhai
 * Create: 2022-8-11
 */
#ifndef TOOL_H
#define TOOL_H
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/epoll.h>
#include <assert.h>
#include <errno.h>
#include <stdint.h>
#include <typeinfo>
#include <limits.h>
#include <iostream>
#include <fstream>
#include "gtest/gtest.h"
#include "jansson.h"
#include "t_datacom_lite.h"
using namespace std;

#define MAX_CMD_SIZE 1024
#define MAX_NAME_LENGTH 128
char g_command[MAX_CMD_SIZE];
GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
GmcConnT *g_connAsync = NULL;
GmcStmtT *g_stmtAsync = NULL;
GmcTxConfigT g_trxConfig;

// vertexLabel config
const char *g_vertexLabelConfig = "{\"max_record_num\":100000000}";

// 格式化断言，规避codeClean
#define AW_MACRO_EXPECT_LE_INT(a, b) EXPECT_LE(a, b)

int MergeVdata(GmcStmtT *stmt, const char *labelName, uint32_t keyValue1, uint32_t keyValue2,
    uint32_t f2Value, uint32_t f3Value)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &keyValue1, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &keyValue2, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2Value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &f3Value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

int GetValue(const char *command, const char *keyWord)
{
    string valueStr;
    int value = 0;
    char cmdOutPut[1024] = {0};
    FILE *pf = popen(command, "r");
    while (fgets(cmdOutPut, sizeof(cmdOutPut), pf) != NULL) {
        valueStr.assign(cmdOutPut);
        string::size_type idx = valueStr.find(keyWord);
        if (idx != string::npos) {
            valueStr = valueStr.substr(valueStr.find(keyWord) + strlen(keyWord));
            value = stoi(valueStr);
            pclose(pf);
            return value;
        }
    }
    pclose(pf);
    return -1;
}

int GetValueBetween(const char *command, const char *keyWord, const char *startKeyWord, const char *endKeyWord)
{
    string valueStr;
    int value = 0;
    char cmdOutPut[1024] = {0};
    FILE *pf = popen(command, "r");
    while (fgets(cmdOutPut, sizeof(cmdOutPut), pf) != NULL) {
        valueStr.assign(cmdOutPut);
        string::size_type idx = valueStr.find(keyWord);
        if (idx != string::npos) {
            valueStr = valueStr.substr(valueStr.find(startKeyWord),
                valueStr.find(endKeyWord) - valueStr.find(startKeyWord));
            valueStr = valueStr.substr(valueStr.find(keyWord) + strlen(keyWord));
            value = stoi(valueStr);
            pclose(pf);
            return value;
        }
    }
    pclose(pf);
    return -1;
}

int CalculateByte(string groupName)
{
    string cmdStr = "gmsysview -q V\\$COM_SHMEM_GROUP -f GROUP_NAME=\"" + groupName + "\"|grep GROUP_TOTAL_PHYSIZE";
    const char *cmd = cmdStr.c_str();
    int getM = GetValue(cmd, "GROUP_TOTAL_PHYSIZE: [");
    int getK = GetValue(cmd, "] MB [");
    int getB = GetValue(cmd, "] KB [");
    return getM * 1024 * 1024 + getK * 1024 + getB;
}

// 建一张超大的表
string WriteBigVertexLabel(int fNum)
{
    string str = R"([{
    "version":"2.0",
    "type":"record",
    "name":"big_vertex_label",
    "comment":"主键索引",
    "fields":
    [
        {"name":"id", "type":"uint32"},)";
    for (int i = 0; i < fNum; i++) {
        str += R"(
        {"name":"F)";
        str += string('a', 100);
        str += to_string(i);
        str += R"(", "type":"string"})";
        if (i != fNum - 1) {
            str += ",";
        }
    }
    str += R"(
    ],
    "keys": [
    { "name": "primary_key", "index": { "type": "primary" },
      "node": "big_vertex_label",
      "fields": ["id"],
      "constraints": { "unique": true},
      "comment": "主键索引"
    }]
}])";
    return str;
}

void *ThrCreateVertexLabel(void *args)
{
    int thrIndex = *((int *)args);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = 0;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string labelJsonStr = WriteBigVertexLabel(1000);
    const char *labelJson = labelJsonStr.c_str();

    int res = 0;
    int times = 0;
    system("gmsysview -q V\\$COM_SHMEM_GROUP");
    system("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=\"catalog share memory context\"");
    while (res == GMERR_OK) {
        string labelNameStr = "thr" + to_string(thrIndex) + "label" + to_string(times);
        const char *labelName = labelNameStr.c_str();
        res = GmcCreateVertexLabelWithName(stmt, labelJson, g_vertexLabelConfig, labelName);
        if (res == GMERR_OK) {
            times++;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, res);
        }
    }
    system("gmsysview -q V\\$COM_SHMEM_GROUP");
    system("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=\"catalog share memory context\"");

    int fullM = GetValue("gmsysview -q V\\$COM_SHMEM_GROUP -f GROUP_NAME=\"GMDB SysShm Group\""
                            "|grep GROUP_TOTAL_PHYSIZE", "GROUP_TOTAL_PHYSIZE: [");
    int fullK = GetValue("gmsysview -q V\\$COM_SHMEM_GROUP -f GROUP_NAME=\"GMDB SysShm Group\""
                            "|grep GROUP_TOTAL_PHYSIZE", "] MB [");
    int fullB = GetValue("gmsysview -q V\\$COM_SHMEM_GROUP -f GROUP_NAME=\"GMDB SysShm Group\""
                            "|grep GROUP_TOTAL_PHYSIZE", "] KB [");
    int full = fullM * 1024 * 1024 + fullK * 1024 + fullB;

    int sysMMax = GetValue("gmsysview -q V\\$COM_SHMEM_GROUP -f GROUP_NAME=\"GMDB SysShm Group\""
                            "|grep GROUP_MAX_SIZE", "GROUP_MAX_SIZE: [");
    int sysKMax = GetValue("gmsysview -q V\\$COM_SHMEM_GROUP -f GROUP_NAME=\"GMDB SysShm Group\""
                            "|grep GROUP_MAX_SIZE", "] MB [");
    int sysBMax = GetValue("gmsysview -q V\\$COM_SHMEM_GROUP -f GROUP_NAME=\"GMDB SysShm Group\""
                            "|grep GROUP_MAX_SIZE", "] KB [");
    int sysMax = sysMMax * 1024 * 1024 + sysKMax * 1024 + sysBMax;

    int stepM = GetValueBetween("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=\"catalog share memory context\""
                                "|grep \"step size\"", ":[", "step size", "max size");
    int stepK = GetValueBetween("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=\"catalog share memory context\""
                                "|grep \"step size\"", "] MB [", "step size", "max size");
    int stepB = GetValueBetween("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=\"catalog share memory context\""
                                "|grep \"step size\"", "] KB [", "step size", "max size");
    int step = stepM * 1024 * 1024 + stepK * 1024 + stepB;

    int sysRest = sysMax - full;
    EXPECT_LT(sysRest, step);

    for (int i = 0; i < times; i++) {
        string labelNameStr = "thr" + to_string(thrIndex) + "label" + to_string(i);
        const char *labelName = labelNameStr.c_str();
        ret = GmcDropVertexLabel(stmt, labelName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

int InsertVdata(GmcStmtT *stmt, const char *labelName, uint32_t valueF1, uint32_t valueF2)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return 0;
}

#endif
