#include "tools.h"

class memory_divide_group_view_scene_test : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void memory_divide_group_view_scene_test::SetUpTestCase(){}
void memory_divide_group_view_scene_test::TearDownTestCase(){}
void memory_divide_group_view_scene_test::SetUp(){}
void memory_divide_group_view_scene_test::TearDown(){}

// 010.开启纵向隔离，建满订阅连接，使用COM_SHMEM_GROUP视图查询
TEST_F(memory_divide_group_view_scene_test, DFX_086_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableVerticalIsolation=1\"");
    system("start.sh -f");

    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *connSub[MAX_CONN_SIZE - 1] = {NULL};
    GmcStmtT *stmtSub[MAX_CONN_SIZE - 1] = {NULL};
    int chanRingLen = 256;
    int appBefore = CalculateByte("GMDB App Shmem Ctx Group");
    int sysBefore = CalculateByte("GMDB SysShm Group");
    for (int i = 0; i < MAX_CONN_SIZE - 1; i++) {
        string subConnNameStr = "subConn" + to_string(i);
        const char *subConnName = subConnNameStr.c_str();
        ret = testSubConnect(&connSub[i], &stmtSub[i], 1, g_epoll_reg_info, subConnName, &chanRingLen);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    int appAfter = CalculateByte("GMDB App Shmem Ctx Group");
    int sysAfter = CalculateByte("GMDB SysShm Group");
    EXPECT_GE(appAfter, appBefore);
    EXPECT_GE(sysAfter, sysBefore);

    for (int i = 0; i < MAX_CONN_SIZE - 1; i++) {
        ret = testSubDisConnect(connSub[i], stmtSub[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcDetachAllShmSeg();
	testEnvClean();

    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

// 011.开启纵向隔离，建100张表，每次建表后使用COM_SHMEM_GROUP视图查询
TEST_F(memory_divide_group_view_scene_test, DFX_086_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 开启纵向隔离
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableVerticalIsolation=1\"");
    system("start.sh -f");

    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    string labelJsonStr = WriteBigVertexLabel(1000);
    const char *labelJson = labelJsonStr.c_str();
    int labelNum = 100;
    
    int sysBefore = CalculateByte("GMDB SysShm Group");
    for (int i = 0; i < labelNum; i++) {
        string labelNameStr = "label" + to_string(i);
        const char *labelName = labelNameStr.c_str();
        ret = GmcCreateVertexLabelWithName(g_stmt, labelJson, g_vertexLabelConfig, labelName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    int sysAfter = CalculateByte("GMDB SysShm Group");
    EXPECT_GT(sysAfter, sysBefore);

    for (int i = 0; i < labelNum; i++) {
        string labelNameStr = "label" + to_string(i);
        const char *labelName = labelNameStr.c_str();
        ret = GmcDropVertexLabel(g_stmt, labelName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcDetachAllShmSeg();
	testEnvClean();

    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

// 012.开启纵向隔离，建表、写10000条数据，每次写数据后使用COM_SHMEM_GROUP视图查询
TEST_F(memory_divide_group_view_scene_test, DFX_086_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 开启纵向隔离
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableVerticalIsolation=1\"");
    system("start.sh -f");

    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *labelJson = NULL;
    readJanssonFile("schemaFile/simple_label.gmjson", &labelJson);
    AW_MACRO_ASSERT_NOTNULL(labelJson);
    ret = GmcCreateVertexLabel(g_stmt, labelJson, g_vertexLabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);
    int insertNum = 100000;
    int memBefore = CalculateByte("GMDB storage shmem group");
    for (int i = 0; i < insertNum; i++) {
        ret = InsertVdata(g_stmt, "label", i, i);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    int memAfter = CalculateByte("GMDB storage shmem group");
    EXPECT_GT(memAfter, memBefore);

    ret = GmcDropVertexLabel(g_stmt, "label");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcDetachAllShmSeg();
	testEnvClean();

    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}
