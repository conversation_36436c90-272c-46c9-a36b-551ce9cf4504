#include "tools.h"

class memory_divide_group_view_reliable_test : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void memory_divide_group_view_reliable_test::SetUpTestCase(){}
void memory_divide_group_view_reliable_test::TearDownTestCase(){}
void memory_divide_group_view_reliable_test::SetUp(){}
void memory_divide_group_view_reliable_test::TearDown(){}

// 013.使用COM_SHMEM_GROUP视图查询1000次(CIDA上的文本用例有误，以此为准)
TEST_F(memory_divide_group_view_reliable_test, DFX_086_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 开启纵向隔离
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableVerticalIsolation=1\"");
    system("start.sh -f");

    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int app = CalculateByte("GMDB App Shmem Ctx Group");
    int sys = CalculateByte("GMDB SysShm Group");
    int mem = CalculateByte("GMDB storage shmem group");
    for (int i = 0; i < 1000; i++) {
        int reApp = CalculateByte("GMDB App Shmem Ctx Group");
        AW_MACRO_EXPECT_LE_INT(abs(app - reApp), 1024 * 1024);
        int reSys = CalculateByte("GMDB SysShm Group");
        AW_MACRO_EXPECT_LE_INT(abs(sys - reSys), 1024 * 1024);
        int reMem = CalculateByte("GMDB storage shmem group");
        AW_MACRO_EXPECT_LE_INT(abs(mem - reMem), 1024 * 1024);
    }

    GmcDetachAllShmSeg();
	testEnvClean();

    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}
