#include "tools.h"

class memory_divide_group_view_test : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void memory_divide_group_view_test::SetUpTestCase(){}
void memory_divide_group_view_test::TearDownTestCase(){}
void memory_divide_group_view_test::SetUp(){}
void memory_divide_group_view_test::TearDown(){}

// 001.关闭纵向隔离，使用COM_SHMEM_GROUP视图查询
TEST_F(memory_divide_group_view_test, DFX_086_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableVerticalIsolation=0\"");
    system("start.sh -f");

    int ret = 0;
    int viewLineNum = GetValue("gmsysview -q V\\$COM_SHMEM_GROUP|wc -l", "");
    AW_MACRO_EXPECT_EQ_INT(1, viewLineNum);
    ret = executeCommand((char *)"gmsysview -q V\\$COM_SHMEM_GROUP", "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcDetachAllShmSeg();
	testEnvClean();

    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

// 002.关闭纵向隔离，使用COM_SHMEM_CTX视图查询
TEST_F(memory_divide_group_view_test, DFX_086_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableVerticalIsolation=0\"");
    system("start.sh -f");

    int ret = 0;
    int viewIndexNum = GetValue("gmsysview -q V\\$COM_SHMEM_CTX|grep index|wc -l", "");
    int viewGroupIDNum = GetValue("gmsysview -q V\\$COM_SHMEM_CTX|grep GROUP_ID|wc -l", "");
    AW_MACRO_EXPECT_EQ_INT(viewIndexNum, viewGroupIDNum);
    int groupIDNotNullNum = GetValue("gmsysview -q V\\$COM_SHMEM_CTX|grep GROUP_ID|grep -v \"GROUP_ID: null\"|wc -l", "");
    AW_MACRO_EXPECT_EQ_INT(groupIDNotNullNum, 0);
    ret = executeCommand((char *)"gmsysview -q V\\$COM_SHMEM_CTX", "GROUP_ID: null");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcDetachAllShmSeg();
	testEnvClean();

    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

// 003.开启纵向隔离，使用COM_SHMEM_GROUP视图查询
TEST_F(memory_divide_group_view_test, DFX_086_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableVerticalIsolation=1\"");
    system("start.sh -f");

    int ret = 0;
    ret = executeCommand((char *)"gmsysview -q V\\$COM_SHMEM_GROUP", "GROUP_ID", "GROUP_NAME", "GROUP_TOTAL_PHYSIZE", "GROUP_MAX_SIZE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcDetachAllShmSeg();
	testEnvClean();

    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

// 004.开启纵向隔离，使用COM_SHMEM_CTX视图查询
TEST_F(memory_divide_group_view_test, DFX_086_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableVerticalIsolation=1\"");
    system("start.sh -f");

    int ret = 0;
    int viewIndexNum = GetValue("gmsysview -q V\\$COM_SHMEM_CTX|grep index|wc -l", "");
    int viewGroupIDNum = GetValue("gmsysview -q V\\$COM_SHMEM_CTX|grep GROUP_ID|wc -l", "");
    AW_MACRO_EXPECT_EQ_INT(viewIndexNum, viewGroupIDNum);
    int groupIDNullNum = GetValue("gmsysview -q V\\$COM_SHMEM_CTX|grep GROUP_ID|grep \"GROUP_ID: null\"|wc -l", "");
    AW_MACRO_EXPECT_EQ_INT(groupIDNullNum, 0);

    GmcDetachAllShmSeg();
	testEnvClean();

    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

// 005.启服务，使用COM_SHMEM_GROUP视图查询
TEST_F(memory_divide_group_view_test, DFX_086_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("start.sh -f");

    int ret = 0;
    int viewLineNum = GetValue("gmsysview -q V\\$COM_SHMEM_GROUP|wc -l", "");
    AW_MACRO_EXPECT_EQ_INT(1, viewLineNum);
    ret = executeCommand((char *)"gmsysview -q V\\$COM_SHMEM_GROUP", "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcDetachAllShmSeg();
	testEnvClean();
}

// 006.启服务，使用COM_SHMEM_CTX视图查询
TEST_F(memory_divide_group_view_test, DFX_086_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("start.sh -f");

    int ret = 0;
    int viewIndexNum = GetValue("gmsysview -q V\\$COM_SHMEM_CTX|grep index|wc -l", "");
    int viewGroupIDNum = GetValue("gmsysview -q V\\$COM_SHMEM_CTX|grep GROUP_ID|wc -l", "");
    AW_MACRO_EXPECT_EQ_INT(viewIndexNum, viewGroupIDNum);
    int groupIDNotNullNum = GetValue("gmsysview -q V\\$COM_SHMEM_CTX|grep GROUP_ID|grep -v \"GROUP_ID: null\"|wc -l", "");
    AW_MACRO_EXPECT_EQ_INT(groupIDNotNullNum, 0);
    ret = executeCommand((char *)"gmsysview -q V\\$COM_SHMEM_CTX", "GROUP_ID: null");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcDetachAllShmSeg();
	testEnvClean();
}

// 007.开启纵向隔离，使用COM_SHMEM_GROUP -f 筛选一个存在的字段和字段值，视图查询
TEST_F(memory_divide_group_view_test, DFX_086_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableVerticalIsolation=1\"");
    system("start.sh -f");

    int ret = 0;

    ret = executeCommand((char *)"gmsysview -q V\\$COM_SHMEM_GROUP -f GROUP_ID=1", "GROUP_NAME", "GROUP_TOTAL_PHYSIZE", "GROUP_MAX_SIZE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcDetachAllShmSeg();
	testEnvClean();

    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

// 008.开启纵向隔离，使用COM_SHMEM_GROUP -f 筛选一个不存在的字段和字段值，视图查询
TEST_F(memory_divide_group_view_test, DFX_086_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableVerticalIsolation=1\"");
    system("start.sh -f");

    int ret = 0;
    int viewLineNum = GetValue("gmsysview -q V\\$COM_SHMEM_GROUP -f GROUP_NAME=unexit_group_name|wc -l", "");
    AW_MACRO_EXPECT_EQ_INT(viewLineNum, 1);
    ret = executeCommand((char *)"gmsysview -q V\\$COM_SHMEM_GROUP -f GROUP_NAME=unexit_group_name", "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcDetachAllShmSeg();
	testEnvClean();

    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

// 009.开启纵向隔离，使用COM_SHMEM_GROUP 加不存在的参数，视图查询
TEST_F(memory_divide_group_view_test, DFX_086_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableVerticalIsolation=1\"");
    system("start.sh -f");

    int ret = 0;
    ret = executeCommand((char *)"gmsysview -q V\\$COM_SHMEM_GROUP -unexistParam 1",
                            "sysview initial option param unsucc, ret = 1004004.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcDetachAllShmSeg();
	testEnvClean();

    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}
