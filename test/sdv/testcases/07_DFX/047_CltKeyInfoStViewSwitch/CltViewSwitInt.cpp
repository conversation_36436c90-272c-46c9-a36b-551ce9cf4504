/*****************************************************************************
 Description  : 客户端关键信息统计视图开关
 Notes        :
 History      :
 Author       : yangfuwen ywx1060383
 Modification :
*****************************************************************************/

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
using namespace std;

#define MAX_CMD_SIZE 1024
#define LABELNAME_MAX_LENGTH 128
#define DELAYTIME 30 // 视图上报周期
GmcStmtT *stmt = NULL;
GmcConnT *conn = NULL;

int ret;
int oper_nums = 100;
unsigned int uint32_tmp = 111111;
char g_command[MAX_CMD_SIZE];
char g_labelName[LABELNAME_MAX_LENGTH] = "T39";
char g_labelName_T40[LABELNAME_MAX_LENGTH] = "T40";
char g_configJson[128] = "{\"max_record_num\" : 999999}";

class CltKeyInfoStViewSwitch : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        uint32_t queryStatis = 1;
        EXPECT_EQ(GMERR_OK,
            GmcSetCltCfg("clientQryStatisticEnable", GMC_DATATYPE_INT32, &queryStatis, (uint32_t)sizeof(int32_t)));
        system("gmadmin -cfgName isCltStatisEnable -cfgVal 1");
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void CltKeyInfoStViewSwitch::SetUp()
{
    printf("\n======================TEST:BEGIN======================\n");
    AW_ADD_ERR_WHITE_LIST(2, "GMERR-1009009", "GMERR-1009001");
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}
void CltKeyInfoStViewSwitch::TearDown()
{
    AW_CHECK_LOG_END();
    system("gmadmin -cfgName isCltStatisEnable -cfgVal 1");
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    printf("\n======================TEST:END========================\n");
}

int testInsertVertex(GmcStmtT *stmt, const char *labelName, int oper_begin, int oper_end)
{
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int loop = oper_begin; loop <= oper_end; loop++) {
        // pk
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        // hashcluster key
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

// 036.验证统计时间间隔可以被修改延长
TEST_F(CltKeyInfoStViewSwitch, DFX_047_036)
{
    uint32_t Interval = 12;
    GmcConnOptionsT *connOptionsInnner;
    ret = GmcConnOptionsCreate(&connOptionsInnner);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcConnOptionsSetServerLocator(connOptionsInnner, g_connServer);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcConnOptionsSetTimeInterval(connOptionsInnner, Interval);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcConnOptionsSetEpollRegFuncWithUserData(connOptionsInnner, g_epoll_reg_info, &g_epollData.userEpollFd);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptionsInnner, &conn);
    EXPECT_EQ(GMERR_OK, ret);
    GmcConnOptionsDestroy(connOptionsInnner);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    //创建 vertexLabel
    char *schema_json = NULL;
    readJanssonFile("./schema_file/CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema_json);

    //写入数据
    ret = testInsertVertex(stmt, g_labelName, 1, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);

    //全表扫
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    sleep(DELAYTIME);
    // 查询视图
    char const *view_name = "V\\$CLT_PROCESS_LABEL";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer, view_name);
    printf("%s\n", g_command);
    system(g_command);

    // 校验视图返回字段(查不到被延长了)
#if defined(RUN_DATACOM_DAP)
    ret = executeCommand(g_command, "PROCESS_PID", "NAMESPACE_NAME", "LABEL_NAME: T39", "LABEL_TYPE", "QUERY_CNT: 1");
    EXPECT_EQ(-1, ret);
#else
    printf("no support test\n");
#endif

    // 校验视图返回字段
#if defined(RUN_DATACOM_DAP)
    do {
        sleep(3);  //每3秒去查视图匹配
        system(g_command);
        ret = executeCommand(g_command, "PROCESS_PID", "NAMESPACE_NAME", "LABEL_NAME", "LABEL_TYPE", "QUERY_CNT: 1");
    } while (ret);
#else
    printf("no support test\n");
#endif

    ret = GmcDropVertexLabel(stmt, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);

    GmcFreeStmt(stmt);
    ret = GmcDisconnect(conn);
    EXPECT_EQ(GMERR_OK, ret);
}

// 037.验证统计时间间隔可以被修改缩短
TEST_F(CltKeyInfoStViewSwitch, DFX_047_037)
{
    uint32_t Interval = 1;  //心跳30s/次
    GmcConnOptionsT *connOptionsInnner;
    ret = GmcConnOptionsCreate(&connOptionsInnner);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcConnOptionsSetServerLocator(connOptionsInnner, g_connServer);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcConnOptionsSetTimeInterval(connOptionsInnner, Interval);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcConnOptionsSetEpollRegFuncWithUserData(connOptionsInnner, g_epoll_reg_info, &g_epollData.userEpollFd);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptionsInnner, &conn);
    EXPECT_EQ(GMERR_OK, ret);
    GmcConnOptionsDestroy(connOptionsInnner);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    //创建 vertexLabel
    char *schema_json = NULL;
    readJanssonFile("./schema_file/CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema_json);

    //写入数据
    ret = testInsertVertex(stmt, g_labelName, 1, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);

    //全表扫
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    sleep(DELAYTIME);
    // 查询视图
    char const *view_name = "V\\$CLT_PROCESS_LABEL";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer, view_name);
    printf("%s\n", g_command);
    system(g_command);

    // 校验视图返回字段
#if defined(RUN_DATACOM_DAP)
    ret = executeCommand(g_command, "PROCESS_PID", "NAMESPACE_NAME", "LABEL_NAME", "LABEL_TYPE", "QUERY_CNT: 1");
    EXPECT_EQ(GMERR_OK, ret);
#else
    printf("no support test\n");
#endif

    ret = GmcDropVertexLabel(stmt, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);

    GmcFreeStmt(stmt);
    ret = GmcDisconnect(conn);
    EXPECT_EQ(GMERR_OK, ret);
}

// 039.传入GmcConnOptionsSetTimeInterval接口传入边界值，设置成功（编译器自动强转）
TEST_F(CltKeyInfoStViewSwitch, DFX_047_039)
{
    uint32_t Interval = 4294967297;  // 4294967295是边界值,这里被编译器强制转换
    printf("%u\n", Interval);
    GmcConnOptionsT *connOptionsInnner;
    ret = GmcConnOptionsCreate(&connOptionsInnner);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcConnOptionsSetServerLocator(connOptionsInnner, g_connServer);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcConnOptionsSetTimeInterval(connOptionsInnner, Interval);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcConnOptionsSetEpollRegFuncWithUserData(connOptionsInnner, g_epoll_reg_info, &g_epollData.userEpollFd);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptionsInnner, &conn);
    EXPECT_EQ(GMERR_OK, ret);
    GmcConnOptionsDestroy(connOptionsInnner);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    GmcFreeStmt(stmt);
    ret = GmcDisconnect(conn);
    EXPECT_EQ(GMERR_OK, ret);
}

// 041.多连接，多表，进行读操作，查看视图
TEST_F(CltKeyInfoStViewSwitch, DFX_047_041)
{
    //创建3个连接
    uint32_t Interval = 1;
    GmcStmtT *stmt_2 = NULL;
    GmcConnT *conn_2 = NULL;
    GmcStmtT *stmt_3 = NULL;
    GmcConnT *conn_3 = NULL;
    GmcConnOptionsT *connOptionsInnner;
    ret = GmcConnOptionsCreate(&connOptionsInnner);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcConnOptionsSetServerLocator(connOptionsInnner, g_connServer);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcConnOptionsSetTimeInterval(connOptionsInnner, Interval);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcConnOptionsSetEpollRegFuncWithUserData(connOptionsInnner, g_epoll_reg_info, &g_epollData.userEpollFd);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptionsInnner, &conn);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptionsInnner, &conn_2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptionsInnner, &conn_3);
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnOptionsDestroy(connOptionsInnner);
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(conn_2, &stmt_2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(conn_3, &stmt_3);
    EXPECT_EQ(GMERR_OK, ret);

    //打开开关
    int32_t setValue_1 = 1;
    ret = GmcSetCfg(stmt, "isCltStatisEnable", GMC_DATATYPE_INT32, &setValue_1, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    //创建 vertexLabel
    char *schema_json = NULL;
    readJanssonFile("./schema_file/CreateVertexLabel_test.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema_json);

    readJanssonFile("./schema_file/CreateVertexLabel_test_T40.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    GmcDropVertexLabel(stmt_2, g_labelName_T40);
    ret = GmcCreateVertexLabel(stmt_2, schema_json, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema_json);

    //创建kv表
    char kvName[128] = "KV3";
    ret = GmcKvCreateTable(stmt_3, kvName, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    //写入数据
    ret = testInsertVertex(stmt, g_labelName, 1, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testInsertVertex(stmt_2, g_labelName_T40, 1, oper_nums);
    EXPECT_EQ(GMERR_OK, ret);
    GmcKvTupleT kvInfo = {0};
    int32_t value = 100;
    char key[32] = "zhangsan";
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);
    ret = GmcKvPrepareStmtByLabelName(stmt_3, kvName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvSet(stmt_3, key, strlen(key), &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    //全表扫
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt_2, g_labelName_T40, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt_2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(stmt_3, kvName);
    EXPECT_EQ(GMERR_OK, ret);
    int64_t limitCount = 0;
    ret = GmcKvScan(stmt_3, limitCount);
    EXPECT_EQ(GMERR_OK, ret);
    sleep(DELAYTIME);

    // 查询视图
    char const *view_name = "V\\$CLT_PROCESS_LABEL";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer, view_name);
    printf("%s\n", g_command);
    system(g_command);

#if defined(RUN_DATACOM_DAP)
    // 校验视图返回字段
    ret = executeCommand(g_command, "index = 0", "index = 1", "index = 2", "QUERY_CNT: 1");
    EXPECT_EQ(GMERR_OK, ret);
#else
    printf("no support test\n");
#endif

    ret = GmcDropVertexLabel(stmt, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt_2, g_labelName_T40);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvDropTable(stmt_3, kvName);
    EXPECT_EQ(GMERR_OK, ret);

    //断连
    GmcFreeStmt(stmt);
    GmcFreeStmt(stmt_2);
    GmcFreeStmt(stmt_3);
    ret = GmcDisconnect(conn);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDisconnect(conn_2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDisconnect(conn_3);
    EXPECT_EQ(GMERR_OK, ret);
}
