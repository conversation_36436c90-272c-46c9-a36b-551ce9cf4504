/*  版权所有 (c) 华为技术有限公司 2024-2024 */
#include "gm_udf.h"
#include "stdio.h"
#include "unistd.h"
#pragma pack(1)

typedef struct Inp1 {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    uint8_t a1[1024];
    uint8_t a2[1024];
    uint8_t a3[1024];
    uint8_t a4[1024];
    uint8_t a5[1024];
    uint8_t a6[1024];
    uint8_t a7[1024];
    uint8_t a8[1024];
    uint8_t a9[1024];
    uint8_t a10[1024];
    uint8_t a11[1024];
    uint8_t a12[1024];
    uint8_t a13[1024];
    uint8_t a14[1024];
    uint8_t a15[1024];
    uint8_t a16[1024];
    uint8_t a17[1024];
    uint8_t a18[1024];
    uint8_t a19[1024];
    uint8_t a20[1024];
    uint8_t a21[1024];
    uint8_t a22[1024];
    uint8_t a23[1024];
    uint8_t a24[1024];
    uint8_t a25[1024];
    uint8_t a26[1024];
    uint8_t a27[1024];
    uint8_t a28[1024];
    uint8_t a29[1024];
    uint8_t a30[1024];
    uint8_t a31[1024];
    uint8_t a32[1016];
} Inp1;

typedef struct Func {
    int32_t dtlReservedCount;
    int64_t a;
    int64_t b;
    int64_t c;
    int64_t d;
} Func;

#pragma pack(0)

const char *g_logName = "/root/_datalog_/TbmRunLog.txt";

int32_t dtl_ext_func_funcmemalloc(void *tuple, GmUdfCtxT *ctx)
{
    Inp1 *outStruct = GmUdfMemAlloc(ctx, sizeof(Inp1));
    Func *f = (Func *)tuple;
    f->d = f->a + f->b;
    if (f->c % 2 == 0) {
        GmUdfMemFree(ctx, outStruct);
    }
    usleep(500000);
    return GMERR_OK;
}
