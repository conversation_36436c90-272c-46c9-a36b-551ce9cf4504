[{"version": "2.0", "config": {"check_validity": false}, "type": "record", "name": "normalvertex", "fields": [{"name": "F1", "type": "int32"}, {"name": "F2", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F3", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F4", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F5", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F6", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F7", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F8", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F9", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F10", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F11", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F12", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F13", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F14", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F15", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F16", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F17", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F18", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F19", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F20", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F21", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F22", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F23", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F24", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F25", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F26", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F27", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F28", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F29", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F30", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F31", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F32", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F33", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F34", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F35", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F36", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F37", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F38", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F39", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F40", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F41", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F42", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F43", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F44", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F45", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F46", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F47", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F48", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F49", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F50", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F51", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F52", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F53", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F54", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F55", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F56", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F57", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F58", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F59", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F60", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F61", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F62", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F63", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}, {"name": "F64", "type": "bytes", "size": 1024, "nullable": false, "default": "1"}], "keys": [{"name": "pk", "node": "normalvertex", "fields": ["F1"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]