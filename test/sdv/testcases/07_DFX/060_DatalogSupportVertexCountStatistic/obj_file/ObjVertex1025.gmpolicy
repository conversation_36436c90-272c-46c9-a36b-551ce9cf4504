{"object_privilege_config": [{"obj_name": ["y1"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y2"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y2"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y3"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y4"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y5"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y6"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y7"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y8"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y9"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y10"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y11"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y12"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y13"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y14"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y15"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y16"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y17"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y18"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y19"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y20"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y21"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y22"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y23"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y24"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y25"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y26"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y27"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y28"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y29"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y30"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y31"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y32"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y33"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y34"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y35"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y36"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y37"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y38"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y39"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y40"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y41"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y42"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y43"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y44"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y45"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y46"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y47"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y48"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y49"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y50"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y51"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y52"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y53"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y54"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y55"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y56"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y57"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y58"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y59"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y60"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y61"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y62"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y63"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y64"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y65"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y66"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y67"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y68"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y69"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y70"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y71"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y72"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y73"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y74"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y75"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y76"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y77"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y78"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y79"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y80"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y81"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y82"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y83"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y84"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y85"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y86"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y87"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y88"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y89"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y90"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y91"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y92"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y93"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y94"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y95"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y96"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y97"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y98"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y99"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y100"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y101"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y102"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y103"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y104"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y105"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y106"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y107"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y108"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y109"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y110"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y111"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y112"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y113"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y114"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y115"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y116"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y117"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y118"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y119"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y120"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y121"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y122"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y123"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y124"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y125"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y126"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y127"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y128"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y129"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y130"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y131"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y132"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y133"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y134"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y135"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y136"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y137"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y138"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y139"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y140"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y141"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y142"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y143"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y144"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y145"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y146"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y147"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y148"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y149"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y150"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y151"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y152"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y153"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y154"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y155"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y156"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y157"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y158"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y159"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y160"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y161"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y162"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y163"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y164"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y165"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y166"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y167"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y168"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y169"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y170"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y171"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y172"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y173"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y174"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y175"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y176"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y177"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y178"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y179"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y180"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y181"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y182"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y183"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y184"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y185"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y186"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y187"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y188"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y189"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y190"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y191"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y192"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y193"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y194"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y195"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y196"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y197"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y198"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y199"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y200"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y201"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y202"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y203"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y204"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y205"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y206"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y207"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y208"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y209"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y210"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y211"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y212"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y213"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y214"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y215"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y216"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y217"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y218"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y219"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y220"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y221"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y222"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y223"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y224"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y225"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y226"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y227"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y228"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y229"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y230"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y231"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y232"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y233"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y234"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y235"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y236"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y237"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y238"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y239"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y240"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y241"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y242"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y243"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y244"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y245"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y246"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y247"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y248"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y249"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y250"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y251"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y252"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y253"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y254"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y255"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y256"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y257"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y258"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y259"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y260"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y261"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y262"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y263"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y264"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y265"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y266"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y267"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y268"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y269"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y270"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y271"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y272"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y273"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y274"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y275"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y276"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y277"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y278"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y279"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y280"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y281"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y282"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y283"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y284"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y285"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y286"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y287"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y288"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y289"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y290"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y291"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y292"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y293"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y294"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y295"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y296"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y297"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y298"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y299"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y300"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y301"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y302"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y303"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y304"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y305"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y306"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y307"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y308"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y309"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y310"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y311"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y312"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y313"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y314"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y315"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y316"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y317"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y318"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y319"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y320"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y321"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y322"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y323"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y324"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y325"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y326"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y327"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y328"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y329"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y330"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y331"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y332"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y333"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y334"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y335"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y336"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y337"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y338"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y339"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y340"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y341"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y342"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y343"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y344"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y345"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y346"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y347"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y348"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y349"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y350"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y351"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y352"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y353"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y354"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y355"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y356"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y357"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y358"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y359"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y360"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y361"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y362"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y363"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y364"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y365"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y366"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y367"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y368"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y369"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y370"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y371"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y372"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y373"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y374"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y375"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y376"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y377"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y378"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y379"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y380"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y381"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y382"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y383"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y384"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y385"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y386"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y387"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y388"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y389"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y390"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y391"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y392"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y393"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y394"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y395"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y396"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y397"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y398"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y399"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y400"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y401"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y402"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y403"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y404"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y405"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y406"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y407"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y408"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y409"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y410"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y411"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y412"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y413"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y414"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y415"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y416"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y417"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y418"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y419"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y420"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y421"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y422"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y423"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y424"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y425"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y426"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y427"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y428"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y429"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y430"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y431"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y432"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y433"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y434"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y435"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y436"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y437"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y438"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y439"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y440"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y441"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y442"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y443"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y444"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y445"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y446"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y447"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y448"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y449"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y450"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y451"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y452"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y453"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y454"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y455"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y456"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y457"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y458"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y459"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y460"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y461"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y462"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y463"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y464"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y465"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y466"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y467"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y468"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y469"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y470"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y471"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y472"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y473"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y474"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y475"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y476"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y477"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y478"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y479"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y480"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y481"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y482"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y483"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y484"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y485"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y486"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y487"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y488"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y489"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y490"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y491"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y492"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y493"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y494"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y495"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y496"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y497"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y498"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y499"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y500"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y501"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y502"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y503"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y504"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y505"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y506"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y507"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y508"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y509"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y510"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y511"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y512"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y513"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y514"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y515"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y516"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y517"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y518"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y519"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y520"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y521"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y522"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y523"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y524"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y525"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y526"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y527"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y528"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y529"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y530"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y531"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y532"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y533"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y534"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y535"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y536"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y537"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y538"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y539"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y540"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y541"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y542"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y543"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y544"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y545"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y546"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y547"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y548"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y549"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y550"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y551"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y552"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y553"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y554"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y555"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y556"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y557"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y558"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y559"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y560"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y561"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y562"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y563"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y564"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y565"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y566"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y567"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y568"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y569"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y570"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y571"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y572"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y573"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y574"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y575"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y576"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y577"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y578"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y579"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y580"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y581"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y582"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y583"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y584"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y585"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y586"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y587"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y588"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y589"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y590"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y591"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y592"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y593"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y594"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y595"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y596"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y597"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y598"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y599"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y600"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y601"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y602"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y603"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y604"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y605"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y606"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y607"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y608"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y609"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y610"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y611"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y612"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y613"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y614"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y615"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y616"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y617"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y618"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y619"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y620"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y621"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y622"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y623"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y624"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y625"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y626"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y627"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y628"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y629"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y630"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y631"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y632"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y633"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y634"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y635"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y636"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y637"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y638"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y639"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y640"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y641"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y642"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y643"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y644"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y645"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y646"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y647"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y648"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y649"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y650"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y651"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y652"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y653"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y654"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y655"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y656"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y657"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y658"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y659"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y660"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y661"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y662"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y663"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y664"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y665"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y666"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y667"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y668"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y669"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y670"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y671"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y672"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y673"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y674"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y675"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y676"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y677"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y678"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y679"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y680"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y681"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y682"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y683"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y684"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y685"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y686"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y687"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y688"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y689"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y690"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y691"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y692"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y693"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y694"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y695"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y696"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y697"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y698"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y699"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y700"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y701"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y702"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y703"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y704"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y705"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y706"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y707"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y708"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y709"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y710"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y711"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y712"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y713"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y714"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y715"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y716"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y717"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y718"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y719"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y720"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y721"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y722"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y723"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y724"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y725"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y726"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y727"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y728"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y729"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y730"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y731"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y732"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y733"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y734"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y735"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y736"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y737"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y738"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y739"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y740"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y741"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y742"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y743"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y744"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y745"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y746"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y747"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y748"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y749"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y750"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y751"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y752"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y753"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y754"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y755"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y756"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y757"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y758"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y759"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y760"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y761"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y762"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y763"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y764"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y765"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y766"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y767"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y768"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y769"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y770"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y771"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y772"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y773"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y774"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y775"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y776"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y777"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y778"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y779"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y780"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y781"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y782"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y783"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y784"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y785"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y786"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y787"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y788"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y789"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y790"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y791"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y792"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y793"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y794"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y795"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y796"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y797"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y798"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y799"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y800"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y801"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y802"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y803"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y804"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y805"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y806"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y807"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y808"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y809"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y810"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y811"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y812"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y813"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y814"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y815"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y816"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y817"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y818"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y819"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y820"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y821"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y822"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y823"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y824"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y825"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y826"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y827"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y828"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y829"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y830"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y831"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y832"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y833"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y834"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y835"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y836"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y837"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y838"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y839"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y840"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y841"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y842"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y843"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y844"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y845"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y846"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y847"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y848"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y849"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y850"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y851"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y852"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y853"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y854"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y855"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y856"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y857"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y858"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y859"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y860"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y861"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y862"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y863"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y864"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y865"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y866"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y867"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y868"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y869"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y870"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y871"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y872"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y873"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y874"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y875"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y876"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y877"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y878"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y879"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y880"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y881"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y882"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y883"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y884"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y885"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y886"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y887"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y888"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y889"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y890"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y891"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y892"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y893"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y894"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y895"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y896"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y897"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y898"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y899"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y900"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y901"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y902"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y903"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y904"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y905"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y906"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y907"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y908"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y909"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y910"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y911"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y912"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y913"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y914"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y915"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y916"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y917"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y918"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y919"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y920"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y921"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y922"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y923"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y924"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y925"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y926"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y927"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y928"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y929"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y930"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y931"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y932"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y933"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y934"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y935"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y936"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y937"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y938"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y939"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y940"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y941"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y942"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y943"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y944"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y945"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y946"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y947"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y948"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y949"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y950"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y951"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y952"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y953"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y954"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y955"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y956"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y957"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y958"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y959"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y960"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y961"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y962"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y963"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y964"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y965"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y966"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y967"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y968"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y969"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y970"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y971"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y972"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y973"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y974"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y975"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y976"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y977"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y978"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y979"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y980"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y981"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y982"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y983"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y984"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y985"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y986"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y987"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y988"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y989"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y990"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y991"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y992"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y993"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y994"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y995"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y996"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y997"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y998"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y999"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y1000"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y1001"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y1002"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y1003"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y1004"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y1005"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y1006"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y1007"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y1008"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y1009"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y1010"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y1011"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y1012"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y1013"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y1014"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y1015"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y1016"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y1017"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y1018"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y1019"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y1020"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y1021"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y1022"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y1023"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y1024"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}, {"obj_name": ["y1025"], "obj_type": "VERTEX_LABEL", "namespace": "public", "privs": [{"user": "root", "process": "DatalogDfx", "privs_type": ["INSERT", "SELECT"]}]}]}