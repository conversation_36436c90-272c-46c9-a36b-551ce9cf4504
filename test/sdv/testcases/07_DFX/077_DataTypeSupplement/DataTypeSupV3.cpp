/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: DataTypeSupV3.cpp
 * Description: V3用例
 * Author: ya<PERSON><PERSON><PERSON> ywx1060383
 * Create: 2023-08-03
 */

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "DataTypeSup.h"
#include "t_datacom_lite.h"

using namespace std;
GmcConnT *g_conn;
GmcStmtT *g_stmt;

class DataTypeSupV3 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh ");
        int ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcConnect(&g_conn, &g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcDisconnect(g_conn, g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void DataTypeSupV3::SetUp()
{
    AW_CHECK_LOG_BEGIN();
}

void DataTypeSupV3::TearDown()
{
    AW_CHECK_LOG_END();
}

// 020.Vertex表gmsysview record查询FIXED字段过滤0x0g超过十六进制范围的数据
TEST_F(DataTypeSupV3, DFX_077_020)
{
    char g_errorCode1[1024] = {0};
    (void)snprintf(g_errorCode1, 1024, "GMERR-%d", GMERR_SYNTAX_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode1);

    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char *schemaFile = NULL;
    char labelName[] = "vertex";
    readJanssonFile("schemaFile/vertex.gmjson", &schemaFile);
    ASSERT_NE((void *)NULL, schemaFile);
    ret = GmcCreateVertexLabel(g_stmt, schemaFile, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schemaFile);

    for (int64_t i = 0; i < 2; i++) {
        int64_t pkValue = i;
        int64_t fixed[1] = {i};
        int32_t fixedSize = 1;
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT64, &pkValue, sizeof(int64_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_FIXED, fixed, fixedSize);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_FIXED, fixed, fixedSize);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // -eq
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -eq 0x0g -s %s -ns %s", g_toolPath, labelName,
        g_connServer, g_testNameSpace);
        system(g_command);
    ret = executeCommand(g_command, "Exec gmsysview record unsuccessful, ret = 1009000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 021.Vertex表gmsysview record查询FIXED字段过滤长度小于fixed字段的长度
TEST_F(DataTypeSupV3, DFX_077_021)
{
    char g_errorCode1[1024] = {0};
    (void)snprintf(g_errorCode1, 1024, "GMERR-%d", GMERR_SYNTAX_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode1);

    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char *schemaFile = NULL;
    char labelName[] = "vertex2";
    readJanssonFile("schemaFile/vertex2.gmjson", &schemaFile);
    ASSERT_NE((void *)NULL, schemaFile);
    ret = GmcCreateVertexLabel(g_stmt, schemaFile, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schemaFile);

    for (int64_t i = 0; i < 2; i++) {
        int64_t pkValue = i;
        int64_t fixed[2] = {i};
        int32_t fixedSize = 2;
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT64, &pkValue, sizeof(int64_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_FIXED, fixed, fixedSize);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_FIXED, fixed, fixedSize);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // -eq
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -eq 0x00 -s %s -ns %s", g_toolPath, labelName,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "Scan vertex label for record unsuccessful, ret = 1009000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 022.Vertex表gmsysview record查询FIXED字段过滤长度大于fixed字段的长度
TEST_F(DataTypeSupV3, DFX_077_022)
{
    char g_errorCode1[1024] = {0};
    (void)snprintf(g_errorCode1, 1024, "GMERR-%d", GMERR_SYNTAX_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode1);

    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char *schemaFile = NULL;
    char labelName[] = "vertex2";
    readJanssonFile("schemaFile/vertex2.gmjson", &schemaFile);
    ASSERT_NE((void *)NULL, schemaFile);
    ret = GmcCreateVertexLabel(g_stmt, schemaFile, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schemaFile);

    for (int64_t i = 0; i < 2; i++) {
        int64_t pkValue = i;
        int64_t fixed[2] = {i};
        int32_t fixedSize = 2;
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT64, &pkValue, sizeof(int64_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_FIXED, fixed, fixedSize);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_FIXED, fixed, fixedSize);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // -eq
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -eq 0x000000 -s %s -ns %s", g_toolPath,
        labelName, g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "Scan vertex label for record unsuccessful, ret = 1009000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 023.eq条件
TEST_F(DataTypeSupV3, DFX_077_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char *schemaFile = NULL;
    char labelName[] = "vertex2";
    readJanssonFile("schemaFile/vertex2.gmjson", &schemaFile);
    ASSERT_NE((void *)NULL, schemaFile);
    ret = GmcCreateVertexLabel(g_stmt, schemaFile, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schemaFile);

    for (int64_t i = 10; i < 13; i++) {
        int64_t pkValue = i;
        int64_t fixed[2] = {0};
        memset(fixed, i, sizeof(fixed));
        int32_t fixedSize = 2;
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT64, &pkValue, sizeof(int64_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_FIXED, fixed, fixedSize);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_FIXED, fixed, fixedSize);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // -eq
    AW_FUN_Log(LOG_STEP, "格式正常但不存在的值，过滤不到");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -eq 0x0000 -s %s -ns %s >b.txt", g_toolPath,
        labelName, g_connServer, g_testNameSpace);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "wc -l b.txt");
    ret = executeCommand(g_command, "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "正常的0x，16进制的值为小写");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -eq 0x0a0a -s %s -ns %s ", g_toolPath,
        labelName, g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F1\": \"0x0a0a\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "正常的0x，16进制的值为大写");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -eq 0x0A0A -s %s -ns %s ", g_toolPath,
        labelName, g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F1\": \"0x0a0a\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "正常的0x，16进制的值为大小写");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -eq 0x0A0a -s %s -ns %s ", g_toolPath,
        labelName, g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F1\": \"0x0a0a\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "大写的0X，16进制的值为小写");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -eq 0X0a0a -s %s -ns %s ", g_toolPath,
        labelName, g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F1\": \"0x0a0a\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "大写的0X，16进制的值为大写");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -eq 0X0A0A -s %s -ns %s ", g_toolPath,
        labelName, g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F1\": \"0x0a0a\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "大写的0X，16进制的值为大小写");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -eq 0X0A0a -s %s -ns %s ", g_toolPath,
        labelName, g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F1\": \"0x0a0a\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 024.ge条件
TEST_F(DataTypeSupV3, DFX_077_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char *schemaFile = NULL;
    char labelName[] = "vertex2";
    readJanssonFile("schemaFile/vertex2.gmjson", &schemaFile);
    ASSERT_NE((void *)NULL, schemaFile);
    ret = GmcCreateVertexLabel(g_stmt, schemaFile, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schemaFile);

    for (int64_t i = 10; i < 13; i++) {
        int64_t pkValue = i;
        int64_t fixed[2] = {0};
        memset(fixed, i, sizeof(fixed));
        int32_t fixedSize = 2;
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT64, &pkValue, sizeof(int64_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_FIXED, fixed, fixedSize);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_FIXED, fixed, fixedSize);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // -ge
    AW_FUN_Log(LOG_STEP, "格式正常但不存在的值，过滤不到");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -ge 0xffff -s %s -ns %s >b.txt", g_toolPath,
        labelName, g_connServer, g_testNameSpace);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "wc -l b.txt");
    ret = executeCommand(g_command, "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "正常的0x，16进制的值为小写");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -ge 0x0b0b -s %s -ns %s ", g_toolPath,
        labelName, g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F1\": \"0x0b0b\"", "\"F1\": \"0x0c0c\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "正常的0x，16进制的值为大写");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -ge 0x0B0B -s %s -ns %s ", g_toolPath,
        labelName, g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F1\": \"0x0b0b\"", "\"F1\": \"0x0c0c\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "正常的0x，16进制的值为大小写");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -ge 0x0B0b -s %s -ns %s ", g_toolPath,
        labelName, g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F1\": \"0x0b0b\"", "\"F1\": \"0x0c0c\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "大写的0X，16进制的值为小写");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -ge 0X0b0b -s %s -ns %s ", g_toolPath,
        labelName, g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F1\": \"0x0b0b\"", "\"F1\": \"0x0c0c\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "大写的0X，16进制的值为大写");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -ge 0X0B0B -s %s -ns %s ", g_toolPath,
        labelName, g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F1\": \"0x0b0b\"", "\"F1\": \"0x0c0c\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "大写的0X，16进制的值为大小写");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -ge 0X0B0b -s %s -ns %s ", g_toolPath,
        labelName, g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F1\": \"0x0b0b\"", "\"F1\": \"0x0c0c\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 025.gt条件
TEST_F(DataTypeSupV3, DFX_077_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char *schemaFile = NULL;
    char labelName[] = "vertex2";
    readJanssonFile("schemaFile/vertex2.gmjson", &schemaFile);
    ASSERT_NE((void *)NULL, schemaFile);
    ret = GmcCreateVertexLabel(g_stmt, schemaFile, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schemaFile);

    for (int64_t i = 10; i < 13; i++) {
        int64_t pkValue = i;
        int64_t fixed[2] = {0};
        memset(fixed, i, sizeof(fixed));
        int32_t fixedSize = 2;
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT64, &pkValue, sizeof(int64_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_FIXED, fixed, fixedSize);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_FIXED, fixed, fixedSize);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // -gt
    AW_FUN_Log(LOG_STEP, "格式正常但不存在的值，过滤不到");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -gt 0xffff -s %s -ns %s >b.txt", g_toolPath,
        labelName, g_connServer, g_testNameSpace);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "wc -l b.txt");
    ret = executeCommand(g_command, "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "正常的0x，16进制的值为小写");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -gt 0x0b0b -s %s -ns %s ", g_toolPath,
        labelName, g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F1\": \"0x0c0c\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "正常的0x，16进制的值为大写");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -gt 0x0B0B -s %s -ns %s ", g_toolPath,
        labelName, g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F1\": \"0x0c0c\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "正常的0x，16进制的值为大小写");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -gt 0x0B0b -s %s -ns %s ", g_toolPath,
        labelName, g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F1\": \"0x0c0c\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "大写的0X，16进制的值为小写");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -gt 0X0b0b -s %s -ns %s ", g_toolPath,
        labelName, g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F1\": \"0x0c0c\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "大写的0X，16进制的值为大写");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -gt 0X0B0B -s %s -ns %s ", g_toolPath,
        labelName, g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F1\": \"0x0c0c\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "大写的0X，16进制的值为大小写");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -gt 0X0B0b -s %s -ns %s ", g_toolPath,
        labelName, g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F1\": \"0x0c0c\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 026.le条件
TEST_F(DataTypeSupV3, DFX_077_026)
{
    char g_errorCode1[1024] = {0};
    (void)snprintf(g_errorCode1, 1024, "GMERR-%d", GMERR_SYNTAX_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode1);

    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char *schemaFile = NULL;
    char labelName[] = "vertex2";
    readJanssonFile("schemaFile/vertex2.gmjson", &schemaFile);
    ASSERT_NE((void *)NULL, schemaFile);
    ret = GmcCreateVertexLabel(g_stmt, schemaFile, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schemaFile);

    for (int64_t i = 10; i < 13; i++) {
        int64_t pkValue = i;
        int64_t fixed[2] = {0};
        memset(fixed, i, sizeof(fixed));
        int32_t fixedSize = 2;
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT64, &pkValue, sizeof(int64_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_FIXED, fixed, fixedSize);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_FIXED, fixed, fixedSize);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // -le
    AW_FUN_Log(LOG_STEP, "size小于2，报错");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -le 0x00 -s %s -ns %s", g_toolPath, labelName,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "Scan vertex label for record unsuccessful, ret = 1009000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "正常的0x，16进制的值为小写");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -le 0x0b0b -s %s -ns %s ", g_toolPath,
        labelName, g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F1\": \"0x0a0a\"", "\"F1\": \"0x0b0b\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "正常的0x，16进制的值为大写");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -le 0x0B0B -s %s -ns %s ", g_toolPath,
        labelName, g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F1\": \"0x0a0a\"", "\"F1\": \"0x0b0b\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "正常的0x，16进制的值为大小写");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -le 0x0B0b -s %s -ns %s ", g_toolPath,
        labelName, g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F1\": \"0x0a0a\"", "\"F1\": \"0x0b0b\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "大写的0X，16进制的值为小写");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -le 0X0b0b -s %s -ns %s ", g_toolPath,
        labelName, g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F1\": \"0x0a0a\"", "\"F1\": \"0x0b0b\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "大写的0X，16进制的值为大写");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -le 0X0B0B -s %s -ns %s ", g_toolPath,
        labelName, g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F1\": \"0x0a0a\"", "\"F1\": \"0x0b0b\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "大写的0X，16进制的值为大小写");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -le 0X0B0b -s %s -ns %s ", g_toolPath,
        labelName, g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F1\": \"0x0a0a\"", "\"F1\": \"0x0b0b\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 027.lt条件
TEST_F(DataTypeSupV3, DFX_077_027)
{
    char g_errorCode1[1024] = {0};
    (void)snprintf(g_errorCode1, 1024, "GMERR-%d", GMERR_SYNTAX_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode1);

    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char *schemaFile = NULL;
    char labelName[] = "vertex2";
    readJanssonFile("schemaFile/vertex2.gmjson", &schemaFile);
    ASSERT_NE((void *)NULL, schemaFile);
    ret = GmcCreateVertexLabel(g_stmt, schemaFile, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schemaFile);

    for (int64_t i = 10; i < 13; i++) {
        int64_t pkValue = i;
        int64_t fixed[2] = {0};
        memset(fixed, i, sizeof(fixed));
        int32_t fixedSize = 2;
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT64, &pkValue, sizeof(int64_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_FIXED, fixed, fixedSize);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_FIXED, fixed, fixedSize);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // -lt
    AW_FUN_Log(LOG_STEP, "格式正常但不存在的值，过滤不到");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -lt 0x00 -s %s -ns %s", g_toolPath, labelName,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "Scan vertex label for record unsuccessful, ret = 1009000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "正常的0x，16进制的值为小写");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -lt 0x0b0b -s %s -ns %s ", g_toolPath,
        labelName, g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F1\": \"0x0a0a\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "正常的0x，16进制的值为大写");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -lt 0x0B0B -s %s -ns %s ", g_toolPath,
        labelName, g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F1\": \"0x0a0a\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "正常的0x，16进制的值为大小写");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -lt 0x0B0b -s %s -ns %s ", g_toolPath,
        labelName, g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F1\": \"0x0a0a\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "大写的0X，16进制的值为小写");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -lt 0X0b0b -s %s -ns %s ", g_toolPath,
        labelName, g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F1\": \"0x0a0a\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "大写的0X，16进制的值为大写");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -lt 0X0B0B -s %s -ns %s ", g_toolPath,
        labelName, g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F1\": \"0x0a0a\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "大写的0X，16进制的值为大小写");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -lt 0X0B0b -s %s -ns %s ", g_toolPath,
        labelName, g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F1\": \"0x0a0a\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 028.f转换格式
TEST_F(DataTypeSupV3, DFX_077_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char *schemaFile = NULL;
    char labelName[] = "vertex4";
    readJanssonFile("schemaFile/vertex4.gmjson", &schemaFile);
    ASSERT_NE((void *)NULL, schemaFile);
    ret = GmcCreateVertexLabel(g_stmt, schemaFile, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schemaFile);

    char labelName2[] = "vertex6";
    readJanssonFile("schemaFile/vertex6.gmjson", &schemaFile);
    ASSERT_NE((void *)NULL, schemaFile);
    ret = GmcCreateVertexLabel(g_stmt, schemaFile, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schemaFile);

    for (int64_t i = 0; i < 3; i++) {
        int64_t pkValue = i;
        int64_t fixed[4] = {0};
        memset(fixed, i, sizeof(fixed));
        int32_t fixedSize = 4;
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT64, &pkValue, sizeof(int64_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_FIXED, fixed, fixedSize);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_FIXED, fixed, fixedSize);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (int64_t i = 0; i < 3; i++) {
        int64_t pkValue = i;
        int64_t fixed[6] = {0};
        memset(fixed, i, sizeof(fixed));
        int32_t fixedSize = 6;
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName2, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT64, &pkValue, sizeof(int64_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_FIXED, fixed, fixedSize);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_FIXED, fixed, fixedSize);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // -eq
    AW_FUN_Log(LOG_STEP, "转ipv4");
    int32_t value = 0;
    char ipv4[100] = {0};
    sprintf(ipv4, "\"F1\": \"0.0.0.%d\"", value);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -eq 0x00000000 -f F1 to ipv4 -s %s -ns %s ",
        g_toolPath, labelName, g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, ipv4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "转ipv6");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -eq 0x01010101 -f F1 to ipv6 -s %s -ns %s ",
        g_toolPath, labelName, g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F1\": \"101:101::\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "转mac");
    (void)snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview record %s -c F1 -eq 0x010101010101 -f F1 to mac -s %s -ns %s ", g_toolPath, labelName2,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F1\": \"01-01-01-01-01-01\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "转hex");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -eq 0x01010101 -f F1 to hex -s %s -ns %s ",
        g_toolPath, labelName, g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F1\": \"0x01010101\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "转time");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -eq 0x01010101 -f F1 to time -s %s -ns %s ",
        g_toolPath, labelName, g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F1\": \"\\\\001\\\\001\\\\001\\\\001\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "转string");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -eq 0x01010101 -f F1 to string -s %s -ns %s ",
        g_toolPath, labelName, g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F1\": \"\\\\001\\\\001\\\\001\\\\001\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 029.多字段组合
TEST_F(DataTypeSupV3, DFX_077_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char *schemaFile = NULL;
    char labelName[] = "vertex8";
    readJanssonFile("schemaFile/vertex8.gmjson", &schemaFile);
    ASSERT_NE((void *)NULL, schemaFile);
    ret = GmcCreateVertexLabel(g_stmt, schemaFile, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schemaFile);

    for (int64_t i = 0; i < 8; i++) {
        int64_t pkValue = i;
        int64_t fixed[1] = {0};
        memset(fixed, 0, sizeof(fixed));
        int32_t fixedSize = 1;
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT64, &pkValue, sizeof(int64_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_FIXED, fixed, fixedSize);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        memset(fixed, 1, sizeof(fixed));
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_FIXED, fixed, fixedSize);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        memset(fixed, 2, sizeof(fixed));
        ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_FIXED, fixed, fixedSize);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        memset(fixed, 3, sizeof(fixed));
        ret = GmcSetVertexProperty(g_stmt, "F4", GMC_DATATYPE_FIXED, fixed, fixedSize);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        memset(fixed, 4, sizeof(fixed));
        ret = GmcSetVertexProperty(g_stmt, "F5", GMC_DATATYPE_FIXED, fixed, fixedSize);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        memset(fixed, 5, sizeof(fixed));
        ret = GmcSetVertexProperty(g_stmt, "F6", GMC_DATATYPE_FIXED, fixed, fixedSize);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        memset(fixed, 6, sizeof(fixed));
        ret = GmcSetVertexProperty(g_stmt, "F7", GMC_DATATYPE_FIXED, fixed, fixedSize);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        memset(fixed, i, sizeof(fixed));
        ret = GmcSetVertexProperty(g_stmt, "F8", GMC_DATATYPE_FIXED, fixed, fixedSize);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "多字段组合，8个字段，全是fixed字段");
    (void)snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview record %s -c F1 -eq 0x00 F2 -eq 0x01 F3 -eq 0x02 F4 -eq 0x03 F5 -eq 0x04 F6 -eq 0x05 F7 -eq 0x06 "
        "F8 -eq 0x07 -s %s -ns %s ",
        g_toolPath, labelName, g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F8\": \"0x07\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "多字段组合，8个字段，fixed字段跟其他字段");
    (void)snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview record %s -c F0 -eq 7 F2 -eq 0x01 F3 -eq 0x02 F4 -eq 0x03 F5 -eq 0x04 F6 -eq 0x05 F7 -eq 0x06 "
        "F8 -eq 0x07 -s %s -ns %s ",
        g_toolPath, labelName, g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F8\": \"0x07\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "多字段组合，9个字段");
    (void)snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview record %s -c F0 -eq 7 F1 -eq 0x00 F2 -eq 0x01 F3 -eq 0x02 F4 -eq 0x03 F5 -eq 0x04 F6 -eq 0x05"
        "F7 -eq 0x06 F8 -eq 0x07 -s %s -ns %s ",
        g_toolPath, labelName, g_connServer, g_testNameSpace);
    ret = executeCommand(g_command,
        "The number of parameters for the option(\"-c\") is in the range((1, 24)). please check your input.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
