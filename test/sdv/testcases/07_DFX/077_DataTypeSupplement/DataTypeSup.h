/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: DataTypeSup.h
 * Description:
 * Author: yang<PERSON>wen ywx1060383
 * Create: 2023-08-03
 */

#ifndef DATA_TYPE_SUP_H
#define DATA_TYPE_SUP_H
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "t_datacom_lite.h"

#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];
char g_configJson[128] = "{\"max_record_count\" : 1000}";

int recordInsert(GmcStmtT *stmt, char *labelName, int64_t insertValue, int64_t fixedValue[], int32_t fixedSize)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *root, *T1, *T2;
    ret = GmcGetRootNode(stmt, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t pkValue = insertValue;
    ret = GmcNodeSetPropertyByName(root, "F0", GMC_DATATYPE_INT64, &pkValue, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(root, "F1", GMC_DATATYPE_FIXED, fixedValue, fixedSize);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(root, "F2", GMC_DATATYPE_FIXED, fixedValue, fixedSize);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeGetChild(root, "T1", &T1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeAppendElement(T1, &T1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T1, "V1", GMC_DATATYPE_FIXED, fixedValue, fixedSize);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T1, "V2", GMC_DATATYPE_FIXED, fixedValue, fixedSize);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeGetChild(root, "T2", &T2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeAppendElement(T2, &T2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T2, "A1", GMC_DATATYPE_FIXED, fixedValue, fixedSize);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T2, "A2", GMC_DATATYPE_FIXED, fixedValue, fixedSize);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *A3;
    ret = GmcNodeGetChild(T2, "A3", &A3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeAppendElement(A3, &A3);
    ret = GmcNodeSetPropertyByName(A3, "B1", GMC_DATATYPE_FIXED, fixedValue, fixedSize);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(A3, "B2", GMC_DATATYPE_FIXED, fixedValue, fixedSize);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    return ret;
}

int recordInsertPrimaryKey(GmcStmtT *stmt, char *labelName, int64_t insertValue)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *root;
    ret = GmcGetRootNode(stmt, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t pkValue = insertValue;
    ret = GmcNodeSetPropertyByName(root, "F0", GMC_DATATYPE_INT64, &pkValue, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    return ret;
}

int singleRecordInsertInt8(GmcStmtT *stmt, char *labelName, int8_t value)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT8, &value, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT8, &value, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &value, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    return ret;
}

int singleRecordInsertInt16(GmcStmtT *stmt, char *labelName, int16_t value)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT16, &value, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT16, &value, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT16, &value, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    return ret;
}

int singleRecordInsertInt32(GmcStmtT *stmt, char *labelName, int32_t value)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    return ret;
}

int singleRecordInsertInt64(GmcStmtT *stmt, char *labelName, int64_t value)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    return ret;
}

int singleRecordInsertFloat(GmcStmtT *stmt, char *labelName, int64_t value, float value2)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_FLOAT, &value2, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_FLOAT, &value2, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    return ret;
}

int singleRecordInsertDouble(GmcStmtT *stmt, char *labelName, int64_t value, double value2)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1",  GMC_DATATYPE_DOUBLE, &value2, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2",  GMC_DATATYPE_DOUBLE, &value2, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    return ret;
}
#endif
