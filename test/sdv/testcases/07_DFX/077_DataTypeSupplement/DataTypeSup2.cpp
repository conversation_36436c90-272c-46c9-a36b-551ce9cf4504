/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * File Name: DataTypeSup2.cpp
 * Description: DTS2025012418174问题单用例加固
 * Author: yang<PERSON><PERSON> ywx1060383
 * Create: 2025-02-14
 */

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "DataTypeSup.h"
#include "t_datacom_lite.h"

using namespace std;
GmcConnT *g_conn;
GmcStmtT *g_stmt;

class DataTypeSup : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh ");
        int ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcConnect(&g_conn, &g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcDisconnect(g_conn, g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void DataTypeSup::SetUp()
{
    AW_CHECK_LOG_BEGIN();
}

void DataTypeSup::TearDown()
{
    AW_CHECK_LOG_END();
}

// 100.int8类型数据, 插入负数, gmsysview record -eq | -ge | -gt | -le | -lt过滤查询
TEST_F(DataTypeSup, DFX_077_100)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char *schemaFile = NULL;
    char labelName[] = "vertex";
    readJanssonFile("schemaFile/vertexint8.gmjson", &schemaFile);
    ASSERT_NE((void *)NULL, schemaFile);
    ret = GmcCreateVertexLabel(g_stmt, schemaFile, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schemaFile);

    for (int8_t i = 1; i < 4; i++) {
        int8_t value = -i;
        ret = singleRecordInsertInt8(g_stmt, labelName, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // -eq
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -eq -1 -s %s -ns %s", g_toolPath, labelName,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F0\": -1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // -ge
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -ge -2 -s %s -ns %s", g_toolPath, labelName,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F0\": -1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "\"F0\": -2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // -gt
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -gt -2 -s %s -ns %s", g_toolPath, labelName,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F0\": -1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "\"F0\": -2");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE

    // -le
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -le -2 -s %s -ns %s", g_toolPath, labelName,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F0\": -2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "\"F0\": -3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // -lt
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -lt -2 -s %s -ns %s", g_toolPath, labelName,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F0\": -3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "\"F0\": -2");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 101.int16类型数据, 插入负数, gmsysview record -eq | -ge | -gt | -le | -lt过滤查询
TEST_F(DataTypeSup, DFX_077_101)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char *schemaFile = NULL;
    char labelName[] = "vertex";
    readJanssonFile("schemaFile/vertexint16.gmjson", &schemaFile);
    ASSERT_NE((void *)NULL, schemaFile);
    ret = GmcCreateVertexLabel(g_stmt, schemaFile, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schemaFile);

    for (int8_t i = 1; i < 4; i++) {
        int8_t value = -i;
        ret = singleRecordInsertInt16(g_stmt, labelName, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // -eq
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -eq -1 -s %s -ns %s", g_toolPath, labelName,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F0\": -1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // -ge
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -ge -2 -s %s -ns %s", g_toolPath, labelName,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F0\": -1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "\"F0\": -2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // -gt
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -gt -2 -s %s -ns %s", g_toolPath, labelName,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F0\": -1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "\"F0\": -2");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE

    // -le
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -le -2 -s %s -ns %s", g_toolPath, labelName,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F0\": -2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "\"F0\": -3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // -lt
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -lt -2 -s %s -ns %s", g_toolPath, labelName,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F0\": -3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "\"F0\": -2");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 102.int32类型数据, 插入负数, gmsysview record -eq | -ge | -gt | -le | -lt过滤查询
TEST_F(DataTypeSup, DFX_077_102)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char *schemaFile = NULL;
    char labelName[] = "vertex";
    readJanssonFile("schemaFile/vertexint32.gmjson", &schemaFile);
    ASSERT_NE((void *)NULL, schemaFile);
    ret = GmcCreateVertexLabel(g_stmt, schemaFile, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schemaFile);

    for (int8_t i = 1; i < 4; i++) {
        int8_t value = -i;
        ret = singleRecordInsertInt32(g_stmt, labelName, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // -eq
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -eq -1 -s %s -ns %s", g_toolPath, labelName,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F0\": -1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // -ge
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -ge -2 -s %s -ns %s", g_toolPath, labelName,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F0\": -1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "\"F0\": -2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // -gt
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -gt -2 -s %s -ns %s", g_toolPath, labelName,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F0\": -1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "\"F0\": -2");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE

    // -le
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -le -2 -s %s -ns %s", g_toolPath, labelName,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F0\": -2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "\"F0\": -3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // -lt
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -lt -2 -s %s -ns %s", g_toolPath, labelName,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F0\": -3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "\"F0\": -2");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 103.int64类型数据, 插入负数, gmsysview record -eq | -ge | -gt | -le | -lt过滤查询
TEST_F(DataTypeSup, DFX_077_103)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char *schemaFile = NULL;
    char labelName[] = "vertex";
    readJanssonFile("schemaFile/vertexint64.gmjson", &schemaFile);
    ASSERT_NE((void *)NULL, schemaFile);
    ret = GmcCreateVertexLabel(g_stmt, schemaFile, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schemaFile);

    for (int8_t i = 1; i < 4; i++) {
        int8_t value = -i;
        ret = singleRecordInsertInt64(g_stmt, labelName, value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // -eq
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -eq -1 -s %s -ns %s", g_toolPath, labelName,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F0\": -1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // -ge
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -ge -2 -s %s -ns %s", g_toolPath, labelName,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F0\": -1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "\"F0\": -2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // -gt
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -gt -2 -s %s -ns %s", g_toolPath, labelName,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F0\": -1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "\"F0\": -2");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE

    // -le
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -le -2 -s %s -ns %s", g_toolPath, labelName,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F0\": -2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "\"F0\": -3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // -lt
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -lt -2 -s %s -ns %s", g_toolPath, labelName,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F0\": -3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "\"F0\": -2");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 104.float类型数据, 插入负数, gmsysview record -eq | -ge | -gt | -le | -lt过滤查询
TEST_F(DataTypeSup, DFX_077_104)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char *schemaFile = NULL;
    char labelName[] = "vertex";
    readJanssonFile("schemaFile/vertexfloat.gmjson", &schemaFile);
    ASSERT_NE((void *)NULL, schemaFile);
    ret = GmcCreateVertexLabel(g_stmt, schemaFile, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schemaFile);

    for (int64_t i = 1; i < 4; i++) {
        int64_t value = -i;
        float value2 = -i;
        ret = singleRecordInsertFloat(g_stmt, labelName, value, value2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // -eq
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -eq -1.0 -s %s -ns %s", g_toolPath, labelName,
        g_connServer, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"F1\": -1.0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // -ge
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -ge -2.0 -s %s -ns %s", g_toolPath, labelName,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F1\": -1.0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "\"F1\": -2.0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // -gt
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -gt -2.0 -s %s -ns %s", g_toolPath, labelName,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F1\": -1.0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "\"F1\": -2.0");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE

    // -le
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -le -2.0 -s %s -ns %s", g_toolPath, labelName,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F1\": -2.0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "\"F1\": -3.0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // -lt
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -lt -2.0 -s %s -ns %s", g_toolPath, labelName,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F1\": -3.0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "\"F1\": -2.0");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 105.double类型数据, 插入负数, gmsysview record -eq | -ge | -gt | -le | -lt过滤查询
TEST_F(DataTypeSup, DFX_077_105)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char *schemaFile = NULL;
    char labelName[] = "vertex";
    readJanssonFile("schemaFile/vertexdouble.gmjson", &schemaFile);
    ASSERT_NE((void *)NULL, schemaFile);
    ret = GmcCreateVertexLabel(g_stmt, schemaFile, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schemaFile);

    for (int64_t i = 1; i < 4; i++) {
        int64_t value = -i;
        float value2 = -i;
        ret = singleRecordInsertDouble(g_stmt, labelName, value, value2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // -eq
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -eq -1.0 -s %s -ns %s", g_toolPath, labelName,
        g_connServer, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"F1\": -1.0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // -ge
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -ge -2.0 -s %s -ns %s", g_toolPath, labelName,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F1\": -1.0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "\"F1\": -2.0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // -gt
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -gt -2.0 -s %s -ns %s", g_toolPath, labelName,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F1\": -1.0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "\"F1\": -2.0");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE

    // -le
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -le -2.0 -s %s -ns %s", g_toolPath, labelName,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F1\": -2.0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "\"F1\": -3.0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // -lt
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -c F1 -lt -2.0 -s %s -ns %s", g_toolPath, labelName,
        g_connServer, g_testNameSpace);
    ret = executeCommand(g_command, "\"F1\": -3.0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "\"F1\": -2.0");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);  // NE

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
