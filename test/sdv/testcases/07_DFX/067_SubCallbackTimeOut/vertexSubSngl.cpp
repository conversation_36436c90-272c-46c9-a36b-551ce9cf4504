extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include "gtest/gtest.h"
#include "./subTools.h"

unsigned int connNotifyCnt = 0;
using namespace std;

class vertexSubSngl : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");

        int ret;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);

        //创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }

    static void TearDownTestCase()
    {
        int ret;
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    }

public:
    SnUserDataT *user_data;

    virtual void SetUp();
    virtual void TearDown();
};

void vertexSubSngl::SetUp()
{
    printf("vertexSubSngl Start.\n");
    ClearSubTmoutLog();

    user_data = NULL;
    g_schema = NULL;
    g_conn_sync = NULL;
    g_stmt_sync = NULL;
    g_subInfo = NULL;
    g_conn_sub = NULL;
    g_stmt_sub = NULL;


    g_subTriggerCnt = 0;
    g_subCallTmOutCt = 0;
    g_subCallTmOutRows = 0;
    g_subCallSleepTm = 1;

    user_data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data, 0, sizeof(SnUserDataT));
    user_data->new_value = (int *)malloc(sizeof(int) * 50000);
    memset(user_data->new_value, 0, sizeof(int) * 50000);
    user_data->old_value = (int *)malloc(sizeof(int) * 50000);
    memset(user_data->old_value, 0, sizeof(int) * 50000);

    int ret = 0;

    //创建同步连接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file/all_type_schema.gmjson", &g_schema);
    ASSERT_NE((void *)NULL, g_schema);

    test_close_and_drop_label(g_stmt_sync, g_labelName);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema, g_labelConfig);
    EXPECT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}
void vertexSubSngl::TearDown()
{
    int ret;

    // 释放订阅连接
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);

    //删表断连
    test_close_and_drop_label(g_stmt_sync, g_labelName);

    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_subInfo);
    free(g_schema);
    free(user_data->new_value);
    free(user_data->old_value);
    free(user_data);
    memset(g_command, 0, sizeof(g_command));
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    char errorMsg3[errCodeLen] = {0};
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_CONFIGURATION_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(3, errorMsg1, errorMsg2, errorMsg3);
    AddWhiteList(GMERR_INSUFFICIENT_RESOURCES);
    AW_CHECK_LOG_END();
    printf("vertexSubSngl End.\n");
}


//001.图顶点订阅，不设置阈值，订阅回调小于200ms   单次订阅回调超时阈值默认为200ms，设置范围1-1000ms
TEST_F(vertexSubSngl, DFX_067_002_001) 
{
    int ret, i;
    int times = 2;

    // 默认值
    setSubCfgAndConn(0, 0);

    // 设置 call_back sleep time
    g_subCallSleepTm = 100;
    g_subCallTmOutSet = 2;

    readJanssonFile("schema_file/all_type_schema_subinfo.gmjson", &g_subInfo);
    ASSERT_NE((void *)NULL, g_subInfo);


    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = (char *)"subVertexLabel";
    tmp_g_sub_info.configJson = g_subInfo;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback_wait, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    // 写数据
    for (i = 0; i < g_dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_INFO, ">>> insert records: %d\n", g_dataNum);

    // 更新数据
    for (i = 0; i < g_dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        test_setVertexProperty_updt(g_stmt_sync, i + g_dataNum);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lablePK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_INFO, ">>> update records: %d\n", g_dataNum);

    // replace
    for (i = 0; i < g_dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_REPLACE);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_INFO, ">>> replace records: %d\n", g_dataNum);

    // 删除数据
    for (i = 0; i < g_dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_DELETE);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lablePK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_INFO, ">>> delete records: %d\n", g_dataNum);

    // replace
    for (i = 0; i < g_dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_REPLACE);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_INFO, ">>> replace records: %d\n", g_dataNum);

    int64_t num = GetSubNum();

    //用于弱校验，校验新增推送次数
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_dataNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_UPDATE, g_dataNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_REPLACE_UPDATE, g_dataNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_dataNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_REPLACE_INSERT, g_dataNum);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, ">>> sub_push_num: %d \n", g_dataNum);
    AW_FUN_Log(LOG_INFO, ">>> sub_trigger_cnt: %u subCallTmOutCt : %u  subCallTmOutRows: %u\n",
        g_subTriggerCnt, g_subCallTmOutCt, g_subCallTmOutRows);

    // 小于默认值200
    ret = checkSubTmoutLog(200, (char *)"subVertexLabel", 0);
    EXPECT_EQ(ret, 0);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}


//002.图顶点订阅，不设置阈值，订阅回调大于200ms，一条超时
TEST_F(vertexSubSngl, DFX_067_002_002) 
{
    int ret, i;
    int times = 2;

    // 默认值
    setSubCfgAndConn(0, 0);

    // 设置 call_back sleep time
    g_subCallSleepTm = 205;
    g_subCallTmOutSet = 1;

    readJanssonFile("schema_file/all_type_schema_subinfo.gmjson", &g_subInfo);
    ASSERT_NE((void *)NULL, g_subInfo);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = (char *)"subVertexLabel";
    tmp_g_sub_info.configJson = g_subInfo;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback_wait, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    // 写数据
    for (i = 0; i < g_dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_INFO, ">>> insert records: %d\n", g_dataNum);
    sleep(1);

    // 更新数据
    for (i = 0; i < g_dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        test_setVertexProperty_updt(g_stmt_sync, i + g_dataNum);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lablePK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_INFO, ">>> update records: %d\n", g_dataNum);
    sleep(1);

    // replace
    for (i = 0; i < g_dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_REPLACE);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_INFO, ">>> replace records: %d\n", g_dataNum);
    sleep(1);

    // 删除数据
    for (i = 0; i < g_dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_DELETE);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lablePK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_INFO, ">>> delete records: %d\n", g_dataNum);
    sleep(1);

    // replace
    for (i = 0; i < g_dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_REPLACE);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_INFO, ">>> replace records: %d\n", g_dataNum);
    sleep(1);

    int64_t num = GetSubNum();

    //用于弱校验，校验新增推送次数
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_dataNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_UPDATE, g_dataNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_REPLACE_UPDATE, g_dataNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_dataNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_REPLACE_INSERT, g_dataNum);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, ">>> sub_push_num: %d \n", g_dataNum);
    AW_FUN_Log(LOG_INFO, ">>> sub_trigger_cnt: %u subCallTmOutCt : %u  subCallTmOutRows: %u\n",
        g_subTriggerCnt, g_subCallTmOutCt, g_subCallTmOutRows);

    // 
    ret = checkSubTmoutLog(200, (char *)"subVertexLabel", g_subCallTmOutCt);
    EXPECT_EQ(ret, 0);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}



//003.图顶点订阅，不设置阈值，订阅回调大于200ms，多条超时
TEST_F(vertexSubSngl, DFX_067_002_003) 
{
    int ret, i;
    int times = 2;

    // 默认值
    setSubCfgAndConn(0, 0);

    // 设置 call_back sleep time
    g_subCallSleepTm = 201;
    g_subCallTmOutSet = 2;

    readJanssonFile("schema_file/all_type_schema_subinfo.gmjson", &g_subInfo);
    ASSERT_NE((void *)NULL, g_subInfo);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = (char *)"subVertexLabel";
    tmp_g_sub_info.configJson = g_subInfo;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback_wait, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    // 写数据
    for (i = 0; i < g_dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_INFO, ">>> insert records: %d\n", g_dataNum);
    sleep(1);

    // 更新数据
    for (i = 0; i < g_dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        test_setVertexProperty_updt(g_stmt_sync, i + g_dataNum);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lablePK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_INFO, ">>> update records: %d\n", g_dataNum);
    sleep(1);

    // replace
    for (i = 0; i < g_dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_REPLACE);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_INFO, ">>> replace records: %d\n", g_dataNum);
    sleep(1);

    // 删除数据
    for (i = 0; i < g_dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_DELETE);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lablePK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_INFO, ">>> delete records: %d\n", g_dataNum);
    sleep(1);

    // replace
    for (i = 0; i < g_dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_REPLACE);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_INFO, ">>> replace records: %d\n", g_dataNum);
    sleep(1);

    int64_t num = GetSubNum();

    //用于弱校验，校验新增推送次数
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_dataNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_UPDATE, g_dataNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_REPLACE_UPDATE, g_dataNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_dataNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_REPLACE_INSERT, g_dataNum);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, ">>> sub_push_num: %d \n", g_dataNum);
    AW_FUN_Log(LOG_INFO, ">>> sub_trigger_cnt: %u subCallTmOutCt : %u  subCallTmOutRows: %u\n",
        g_subTriggerCnt, g_subCallTmOutCt, g_subCallTmOutRows);

    // 日志折叠成5条了
    ret = checkSubTmoutLog(200, (char *)"subVertexLabel", 5);
    EXPECT_EQ(ret, 0);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}


//004.图顶点订阅，不设置阈值，订阅回调大于200ms，批量操作
TEST_F(vertexSubSngl, DFX_067_002_004) 
{
    int ret, i;
    int times = 2;

    // 默认值
    setSubCfgAndConn(0, 0);

    // 设置 call_back sleep time
    g_subCallSleepTm = 201;
    g_subCallTmOutSet = 2;

    readJanssonFile("schema_file/all_type_schema_subinfo.gmjson", &g_subInfo);
    ASSERT_NE((void *)NULL, g_subInfo);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = (char *)"subVertexLabel";
    tmp_g_sub_info.configJson = g_subInfo;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback_wait, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    // 批量写数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_batchNum; i++) {
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(g_batchNum, totalNum);
    ASSERT_EQ(g_batchNum, successNum);
    GmcBatchReset(batch);
    sleep(1);

    // 批量更新数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_batchNum; i++) {
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        test_setVertexProperty_updt(g_stmt_sync, i + g_batchNum);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lablePK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(g_batchNum, totalNum);
    ASSERT_EQ(g_batchNum, successNum);
    GmcBatchReset(batch);
    sleep(1);

    // 批量删除数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_batchNum; i++) {
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lablePK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(g_batchNum, totalNum);
    ASSERT_EQ(g_batchNum, successNum);
    GmcBatchDestroy(batch);

    int64_t num = GetSubNum();

    //用于弱校验，校验新增推送次数
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_batchNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_UPDATE, g_batchNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_batchNum);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, ">>> sub_trigger_cnt: %u subCallTmOutCt : %u  subCallTmOutRows: %u\n",
        g_subTriggerCnt, g_subCallTmOutCt, g_subCallTmOutRows);

    ret = checkSubTmoutLog(200, (char *)"subVertexLabel", 3);
    EXPECT_EQ(ret, 0);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}


//005.图顶点订阅，设置单次订阅阈值20，全表订阅，订阅回调小于20ms
TEST_F(vertexSubSngl, DFX_067_002_005) 
{
    int ret, i;
    int times = 2;

    // 单次默认值
    setSubCfgAndConn(20, 0);

    // 设置 call_back sleep time
    g_subCallSleepTm = 10;
    g_subCallTmOutSet = 2;

    readJanssonFile("schema_file/all_type_schema_subinfo.gmjson", &g_subInfo);
    ASSERT_NE((void *)NULL, g_subInfo);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = (char *)"subVertexLabel";
    tmp_g_sub_info.configJson = g_subInfo;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback_wait, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    // 批量写数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_batchNum; i++) {
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(g_batchNum, totalNum);
    ASSERT_EQ(g_batchNum, successNum);
    GmcBatchReset(batch);

    // 批量更新数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_batchNum; i++) {
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        test_setVertexProperty_updt(g_stmt_sync, i + g_batchNum);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lablePK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(g_batchNum, totalNum);
    ASSERT_EQ(g_batchNum, successNum);
    GmcBatchReset(batch);

    // 批量删除数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_batchNum; i++) {
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lablePK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(g_batchNum, totalNum);
    ASSERT_EQ(g_batchNum, successNum);
    GmcBatchDestroy(batch);

    int64_t num = GetSubNum();

    //用于弱校验，校验新增推送次数
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_batchNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_UPDATE, g_batchNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_batchNum);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, ">>> sub_trigger_cnt: %u subCallTmOutCt : %u  subCallTmOutRows: %u\n",
        g_subTriggerCnt, g_subCallTmOutCt, g_subCallTmOutRows);

    // 性能较差时，可能有超时日志打印；
    int tNum = checkSubTmoutLog(20, (char *)"subVertexLabel", 1, 2);
    if (tNum != 0 && tNum != 1) {
        EXPECT_NE(GMERR_OK, GMERR_OK);
    }

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}


// 006.图顶点订阅，设置单次订阅阈值20，条件订阅，订阅回调小于20ms
TEST_F(vertexSubSngl, DFX_067_002_006)
{
    int ret, i, j;
    int times = 2;

    // 设置20
    setSubCfgAndConn(20, 0);

    // 设置 call_back sleep time
    g_subCallSleepTm = 10;
    g_subCallTmOutSet = 2;

    readJanssonFile("schema_file/all_type_schema_cons_subinfo.gmjson", &g_subInfo);
    ASSERT_NE((void *)NULL, g_subInfo);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = (char *)"subVertexLabel";
    tmp_g_sub_info.configJson = g_subInfo;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback_wait, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    for (j = 1; j <= 4; j++) {
        //批量写数据
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        GmcBatchRetT batchRet;
        GmcBatchOptionT batchOption;
        ret = GmcBatchOptionInit(&batchOption);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
        EXPECT_EQ(GMERR_OK, ret);
        GmcBatchT *batch = NULL;
        ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
        EXPECT_EQ(GMERR_OK, ret);
        for (i = g_batchNum * (j - 1) + 1; i <= g_batchNum * j; i++) {
            test_setVertexProperty(g_stmt_sync, i);
            ret = GmcBatchAddDML(batch, g_stmt_sync);
            EXPECT_EQ(GMERR_OK, ret);
        }
        unsigned int totalNum = 0;
        unsigned int successNum = 0;
        ret = GmcBatchExecute(batch, &batchRet);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(g_batchNum, totalNum);
        EXPECT_EQ(g_batchNum, successNum);
        GmcBatchDestroy(batch);
    }

    int64_t num = GetSubNum();

    //用于弱校验，校验新增推送次数
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT * times);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, ">>> sub_trigger_cnt: %u subCallTmOutCt : %u  subCallTmOutRows: %u\n",
        g_subTriggerCnt, g_subCallTmOutCt, g_subCallTmOutRows);

    ret = checkSubTmoutLog(20, (char *)"subVertexLabel", 0);
    EXPECT_EQ(ret, 0);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}


//007.图顶点订阅，设置单次订阅阈值20，全量订阅，订阅回调小于20ms
TEST_F(vertexSubSngl, DFX_067_002_007)
{
    int ret, i, j, count = 100;
    int times = 2;

    // 设置20
    setSubCfgAndConn(20, 0);

    // 设置 call_back sleep time
    g_subCallSleepTm = 10;  // 构建机性能差，增强稳定性
    g_subCallTmOutSet = 1;

#if defined ENV_RTOSV2X
    count = 50;
#endif

    for (j = 1; j <= count; j++) {
        //批量写数据
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        GmcBatchRetT batchRet;
        GmcBatchOptionT batchOption;
        ret = GmcBatchOptionInit(&batchOption);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
        EXPECT_EQ(GMERR_OK, ret);
        GmcBatchT *batch = NULL;
        ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
        EXPECT_EQ(GMERR_OK, ret);
        for (i = g_batchNum * (j - 1) + 1; i <= g_batchNum * j; i++) {
            test_setVertexProperty(g_stmt_sync, i);
            ret = GmcBatchAddDML(batch, g_stmt_sync);
            EXPECT_EQ(GMERR_OK, ret);
        }
        unsigned int totalNum = 0;
        unsigned int successNum = 0;
        ret = GmcBatchExecute(batch, &batchRet);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(g_batchNum, totalNum);
        EXPECT_EQ(g_batchNum, successNum);
        GmcBatchDestroy(batch);
    }
    readJanssonFile("schema_file/all_type_schema_fullsubs_subinfo.gmjson", &g_subInfo);
    ASSERT_NE((void *)NULL, g_subInfo);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = (char *)"subVertexLabel";
    tmp_g_sub_info.configJson = g_subInfo;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback_wait, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    int64_t num = GetSubNum();
    //用于弱校验，校验新增推送次数
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INITIAL_LOAD, count * g_batchNum, RECV_TIMEOUT * times);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, ">>> sub_trigger_cnt: %u subCallTmOutCt : %u  subCallTmOutRows: %u\n",
        g_subTriggerCnt, g_subCallTmOutCt, g_subCallTmOutRows);

    ret = checkSubTmoutLog(20, (char *)"subVertexLabel", 0);
    EXPECT_EQ(ret, 0);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}




// 008.图顶点订阅，设置单次订阅阈值20，全表订阅，回调大于20ms
TEST_F(vertexSubSngl, DFX_067_002_008) 
{
    int ret, i;
    int times = 2;

    // 单次设置阈值
    setSubCfgAndConn(20, 0);

    // 设置 call_back sleep time
    g_subCallSleepTm = 25;
    g_subCallTmOutSet = 3;

    readJanssonFile("schema_file/all_type_schema_subinfo.gmjson", &g_subInfo);
    ASSERT_NE((void *)NULL, g_subInfo);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = (char *)"subVertexLabel";
    tmp_g_sub_info.configJson = g_subInfo;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback_wait, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    // 批量写数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_batchNum; i++) {
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(g_batchNum, totalNum);
    ASSERT_EQ(g_batchNum, successNum);
    GmcBatchReset(batch);
    sleep(1);

    // 批量更新数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_batchNum; i++) {
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        test_setVertexProperty_updt(g_stmt_sync, i + g_batchNum);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lablePK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(g_batchNum, totalNum);
    ASSERT_EQ(g_batchNum, successNum);
    GmcBatchReset(batch);
    sleep(1);

    // 批量删除数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_batchNum; i++) {
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lablePK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(g_batchNum, totalNum);
    ASSERT_EQ(g_batchNum, successNum);
    GmcBatchDestroy(batch);
    sleep(1);

    int64_t num = GetSubNum();

    //用于弱校验，校验新增推送次数
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_batchNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_UPDATE, g_batchNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_batchNum);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, ">>> sub_trigger_cnt: %u subCallTmOutCt : %u  subCallTmOutRows: %u\n",
        g_subTriggerCnt, g_subCallTmOutCt, g_subCallTmOutRows);

    ret = checkSubTmoutLog(20, (char *)"subVertexLabel", 3);
    EXPECT_EQ(ret, 0);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}


// 009.图顶点订阅，设置单次订阅阈值20，条件订阅，回调大于20ms
TEST_F(vertexSubSngl, DFX_067_002_009)
{
    int ret, i, j;
    int times = 2;

    // 设置20
    setSubCfgAndConn(20, 0);

    // 设置 call_back sleep time
    g_subCallSleepTm = 21;
    g_subCallTmOutSet = 2;

    readJanssonFile("schema_file/all_type_schema_cons_subinfo.gmjson", &g_subInfo);
    ASSERT_NE((void *)NULL, g_subInfo);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = (char *)"subVertexLabel";
    tmp_g_sub_info.configJson = g_subInfo;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback_wait, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    for (j = 1; j <= 4; j++) {
        //批量写数据
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        GmcBatchRetT batchRet;
        GmcBatchOptionT batchOption;
        ret = GmcBatchOptionInit(&batchOption);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
        EXPECT_EQ(GMERR_OK, ret);
        GmcBatchT *batch = NULL;
        ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
        EXPECT_EQ(GMERR_OK, ret);
        for (i = g_batchNum * (j - 1) + 1; i <= g_batchNum * j; i++) {
            test_setVertexProperty_2(g_stmt_sync, i, 11);
            ret = GmcBatchAddDML(batch, g_stmt_sync);
            EXPECT_EQ(GMERR_OK, ret);
        }
        unsigned int totalNum = 0;
        unsigned int successNum = 0;
        ret = GmcBatchExecute(batch, &batchRet);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(g_batchNum, totalNum);
        EXPECT_EQ(g_batchNum, successNum);
        GmcBatchDestroy(batch);
        sleep(1);
    }

    int64_t num = GetSubNum();

    //用于弱校验，校验新增推送次数
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 11, RECV_TIMEOUT * times);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, ">>> sub_trigger_cnt: %u subCallTmOutCt : %u  subCallTmOutRows: %u\n",
        g_subTriggerCnt, g_subCallTmOutCt, g_subCallTmOutRows);

    ret = checkSubTmoutLog(20, (char *)"subVertexLabel", 1);
    EXPECT_EQ(ret, 0);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}


//010.图顶点订阅，设置单次订阅阈值20，全量订阅，订阅回调大于20ms
TEST_F(vertexSubSngl, DFX_067_002_010)
{
    int ret, i, j, count = 100;
    int times = 2;

    // 设置20
    setSubCfgAndConn(20, 0);

    // 设置 call_back sleep time
    g_subCallSleepTm = 25;
    g_subCallTmOutSet = 2;

    for (j = 1; j <= 4; j++) {
        //批量写数据
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        GmcBatchRetT batchRet;
        GmcBatchOptionT batchOption;
        ret = GmcBatchOptionInit(&batchOption);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
        EXPECT_EQ(GMERR_OK, ret);
        GmcBatchT *batch = NULL;
        ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
        EXPECT_EQ(GMERR_OK, ret);
        for (i = g_batchNum * (j - 1) + 1; i <= g_batchNum * j; i++) {
            test_setVertexProperty(g_stmt_sync, i);
            ret = GmcBatchAddDML(batch, g_stmt_sync);
            EXPECT_EQ(GMERR_OK, ret);
        }
        unsigned int totalNum = 0;
        unsigned int successNum = 0;
        ret = GmcBatchExecute(batch, &batchRet);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(g_batchNum, totalNum);
        EXPECT_EQ(g_batchNum, successNum);
        GmcBatchDestroy(batch);
    }
    readJanssonFile("schema_file/all_type_schema_fullsubs_subinfo.gmjson", &g_subInfo);
    ASSERT_NE((void *)NULL, g_subInfo);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = (char *)"subVertexLabel";
    tmp_g_sub_info.configJson = g_subInfo;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback_wait, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    int64_t num = GetSubNum();
    //用于弱校验，校验新增推送次数
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INITIAL_LOAD, 4 * g_batchNum, RECV_TIMEOUT * times);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, ">>> sub_trigger_cnt: %u subCallTmOutCt : %u  subCallTmOutRows: %u\n",
        g_subTriggerCnt, g_subCallTmOutCt, g_subCallTmOutRows);

    ret = checkSubTmoutLog(20, (char *)"subVertexLabel", 1);
    EXPECT_EQ(ret, 0);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}


// 011.图顶点订阅，设置单次订阅阈值20，一个连接上多个订阅关系，回调小于20ms
TEST_F(vertexSubSngl, DFX_067_002_011) 
{
    int ret, i;
    int times = 2;

    // 默认值
    setSubCfgAndConn(120, 0);

    // 设置 call_back sleep time
    g_subCallSleepTm = 100;
    g_subCallTmOutSet = 2;

    // 订阅关系1
    readJanssonFile("schema_file/all_type_schema_subinfo.gmjson", &g_subInfo);
    ASSERT_NE((void *)NULL, g_subInfo);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = (char *)"subVertexLabel";
    tmp_g_sub_info.configJson = g_subInfo;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback_wait, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_subInfo);

    // 订阅关系2
    readJanssonFile("schema_file/all_type_schema_subinfo_2.gmjson", &g_subInfo);
    ASSERT_NE((void *)NULL, g_subInfo);

    tmp_g_sub_info.subsName = (char *)"subVertexLabel_2";
    tmp_g_sub_info.configJson = g_subInfo;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback_wait, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    // 写数据
    for (i = 0; i < g_dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_INFO, ">>> insert records: %d\n", g_dataNum);

    // 更新数据
    for (i = 0; i < g_dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        test_setVertexProperty_updt(g_stmt_sync, i + g_dataNum);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lablePK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_INFO, ">>> update records: %d\n", g_dataNum);

    // replace
    for (i = 0; i < g_dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_REPLACE);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_INFO, ">>> replace records: %d\n", g_dataNum);

    // 删除数据
    for (i = 0; i < g_dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_DELETE);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lablePK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_INFO, ">>> delete records: %d\n", g_dataNum);

    // replace
    for (i = 0; i < g_dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_REPLACE);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_INFO, ">>> replace records: %d\n", g_dataNum);

    int64_t num = GetSubNum();

    //用于弱校验，校验新增推送次数
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_dataNum*2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_UPDATE, g_dataNum*2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_REPLACE_UPDATE, g_dataNum*2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_dataNum*2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_REPLACE_INSERT, g_dataNum*2);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, ">>> sub_push_num: %d \n", g_dataNum);
    AW_FUN_Log(LOG_INFO, ">>> sub_trigger_cnt: %u subCallTmOutCt : %u  subCallTmOutRows: %u\n",
        g_subTriggerCnt, g_subCallTmOutCt, g_subCallTmOutRows);

    // 小于默认值200
    ret = checkSubTmoutLog(120, (char *)"subVertexLabel", 0);
    EXPECT_EQ(ret, 0);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt_sync, "subVertexLabel_2");
    EXPECT_EQ(GMERR_OK, ret);
}


// 012.图顶点订阅，设置单次订阅阈值20，一个连接上多个订阅关系，回调大于20ms
TEST_F(vertexSubSngl, DFX_067_002_012) 
{
    int ret, i;
    int times = 2;

    // 120
    setSubCfgAndConn(120, 0);

    // 设置 call_back sleep time
    g_subCallSleepTm = 130;
    g_subCallTmOutSet = 2;

    // 订阅关系1
    readJanssonFile("schema_file/all_type_schema_subinfo.gmjson", &g_subInfo);
    ASSERT_NE((void *)NULL, g_subInfo);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = (char *)"subVertexLabel";
    tmp_g_sub_info.configJson = g_subInfo;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback_wait, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_subInfo);

    // 订阅关系2
    readJanssonFile("schema_file/all_type_schema_subinfo_2.gmjson", &g_subInfo);
    ASSERT_NE((void *)NULL, g_subInfo);

    tmp_g_sub_info.subsName = (char *)"subVertexLabel_2";
    tmp_g_sub_info.configJson = g_subInfo;
    SnUserDataT * user_data2= (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data2, 0, sizeof(SnUserDataT));
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback_wait1, user_data2);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_subInfo);

    // 订阅关系3
    readJanssonFile("schema_file/all_type_schema_subinfo_3.gmjson", &g_subInfo);
    ASSERT_NE((void *)NULL, g_subInfo);

    tmp_g_sub_info.subsName = (char *)"subVertexLabel_3";
    tmp_g_sub_info.configJson = g_subInfo;
    SnUserDataT * user_data3 = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data3, 0, sizeof(SnUserDataT));
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback_wait2, user_data3);
    EXPECT_EQ(GMERR_OK, ret);

    // 写数据
    for (i = 0; i < g_dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_INFO, ">>> insert records: %d\n", g_dataNum);
    sleep(1);

    // 更新数据
    for (i = 0; i < g_dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        test_setVertexProperty_updt(g_stmt_sync, i + g_dataNum);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lablePK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_INFO, ">>> update records: %d\n", g_dataNum);
    sleep(1);

    // replace
    for (i = 0; i < g_dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_REPLACE);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_INFO, ">>> replace records: %d\n", g_dataNum);
    sleep(1);

    // 删除数据
    for (i = 0; i < g_dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_DELETE);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lablePK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_INFO, ">>> delete records: %d\n", g_dataNum);
    sleep(1);

    // replace
    for (i = 0; i < g_dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_REPLACE);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_INFO, ">>> replace records: %d\n", g_dataNum);
    sleep(1);

    int64_t num = GetSubNum();

    //用于弱校验，校验新增推送次数
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_dataNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_UPDATE, g_dataNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_REPLACE_UPDATE, g_dataNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_dataNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_REPLACE_INSERT, g_dataNum);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, ">>> sub_trigger_cnt: %u subCallTmOutCt : %u  subCallTmOutRows: %u\n",
        g_subTriggerCnt, g_subCallTmOutCt, g_subCallTmOutRows);

    // 多订阅关系，可能折叠，日志触发，大于0即可
    ret = checkSubTmoutLog(120, (char *)"subVertexLabel", 5, 0);
    EXPECT_EQ(ret, 0);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt_sync, "subVertexLabel_2");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt_sync, "subVertexLabel_3");
    EXPECT_EQ(GMERR_OK, ret);

    free(user_data2);
    free(user_data3);
}


// 013.图顶点订阅，订阅连接1设置阈值200，订阅连接2设置阈值20，连接1上多个订阅关系，回调大于20小于200
TEST_F(vertexSubSngl, DFX_067_002_013) 
{
    int ret, i;
    int times = 2;

    //创建订阅连接1，超时200 
    connNotifyCnt = 0;
    int chanRingLen = 256;
    ConnOptionT *connOption;
    uint32_t timeOut = 25;

    ret = testMallocConnOptions(&connOption, NULL, NULL, NULL, 0, 0, 0, SubNotifyCallback, (void *)&connNotifyCnt,
        NULL, NULL, &timeOut, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *subConn[2] = {NULL};
    const char *subChan[2] = {"subChan1", "subChan2"};
    ret = testSubConnect(&subConn[0], NULL, 1, g_epoll_reg_info, subChan[0], &chanRingLen, connOption);
    EXPECT_EQ(GMERR_OK, ret);

    //创建订阅连接2，超时20 
    connOption->subCallBackTimeout = 200;
    ret = testSubConnect(&subConn[1], NULL, 1, g_epoll_reg_info, subChan[1], &chanRingLen, connOption);
    EXPECT_EQ(GMERR_OK, ret);

    free(connOption);
    connOption = NULL;

    // 设置 call_back sleep time
    g_subCallSleepTm = 205;
    g_subCallTmOutSet = 2;

    // 订阅关系1
    readJanssonFile("schema_file/all_type_schema_subinfo.gmjson", &g_subInfo);
    ASSERT_NE((void *)NULL, g_subInfo);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = (char *)"subVertexLabel";
    tmp_g_sub_info.configJson = g_subInfo;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, subConn[0], sn_callback_wait2, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_subInfo);

    // 订阅关系2
    readJanssonFile("schema_file/all_type_schema_subinfo_2.gmjson", &g_subInfo);
    ASSERT_NE((void *)NULL, g_subInfo);

    tmp_g_sub_info.subsName = (char *)"subVertexLabel_2";
    tmp_g_sub_info.configJson = g_subInfo;
    SnUserDataT * user_data2= (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data2, 0, sizeof(SnUserDataT));
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, subConn[1], sn_callback_wait, user_data2);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_subInfo);

    // 订阅关系3
    readJanssonFile("schema_file/all_type_schema_subinfo_3.gmjson", &g_subInfo);
    ASSERT_NE((void *)NULL, g_subInfo);

    tmp_g_sub_info.subsName = (char *)"subVertexLabel_3";
    tmp_g_sub_info.configJson = g_subInfo;
    SnUserDataT * user_data3 = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data3, 0, sizeof(SnUserDataT));
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, subConn[1], sn_callback_wait2, user_data3);
    EXPECT_EQ(GMERR_OK, ret);

    // 写数据
    for (i = 0; i < g_dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_INFO, ">>> insert records: %d\n", g_dataNum);
    sleep(1);

    // 更新数据
    for (i = 0; i < g_dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        test_setVertexProperty_updt(g_stmt_sync, i + g_dataNum);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lablePK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_INFO, ">>> update records: %d\n", g_dataNum);
    sleep(1);

    // replace
    for (i = 0; i < g_dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_REPLACE);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_INFO, ">>> replace records: %d\n", g_dataNum);
    sleep(1);

    // 删除数据
    for (i = 0; i < g_dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_DELETE);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lablePK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_INFO, ">>> delete records: %d\n", g_dataNum);
    sleep(1);

    // replace
    for (i = 0; i < g_dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_REPLACE);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_INFO, ">>> replace records: %d\n", g_dataNum);
    sleep(1);

    //用于弱校验，校验新增推送次数
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_dataNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_UPDATE, g_dataNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_REPLACE_UPDATE, g_dataNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_dataNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_REPLACE_INSERT, g_dataNum);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, ">>> sub_trigger_cnt: %u subCallTmOutCt : %u  subCallTmOutRows: %u\n",
        g_subTriggerCnt, g_subCallTmOutCt, g_subCallTmOutRows);

    ret = checkSubTmoutLog(25, (char *)"subVertexLabel", 0);
    EXPECT_EQ(ret, 0);

    ret = checkSubTmoutLog(200, (char *)"subVertexLabel_2", 5);
    EXPECT_EQ(ret, 0);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt_sync, "subVertexLabel_2");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt_sync, "subVertexLabel_3");
    EXPECT_EQ(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(subConn[0]);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(subConn[1]);
    EXPECT_EQ(GMERR_OK, ret);

    free(user_data2);
    free(user_data3);
}


// 014.图顶点订阅，订阅连接1设置阈值20，订阅连接2设置阈值200，连接1上多个订阅关系，回调大于20
TEST_F(vertexSubSngl, DFX_067_002_014) 
{
    int ret, i;
    int times = 2;

    //创建订阅连接1，超时200
    connNotifyCnt = 0;
    int chanRingLen = 256;
    ConnOptionT *connOption;
    uint32_t timeOut = 200;

    ret = testMallocConnOptions(&connOption, NULL, NULL, NULL, 0, 0, 0, SubNotifyCallback, (void *)&connNotifyCnt,
        NULL, NULL, &timeOut, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *subConn[2] = {NULL};
    const char *subChan[2] = {"subChan1", "subChan2"};
    ret = testSubConnect(&subConn[0], NULL, 1, g_epoll_reg_info, subChan[0], &chanRingLen, connOption);
    EXPECT_EQ(GMERR_OK, ret);

    //创建订阅连接2，超时25 
    connOption->subCallBackTimeout = 25;
    ret = testSubConnect(&subConn[1], NULL, 1, g_epoll_reg_info, subChan[1], &chanRingLen, connOption);
    EXPECT_EQ(GMERR_OK, ret);

    free(connOption);
    connOption = NULL;

    // 设置 call_back sleep time
    g_subCallSleepTm = 30;
    g_subCallTmOutSet = 2;

    // 订阅关系1
    readJanssonFile("schema_file/all_type_schema_subinfo.gmjson", &g_subInfo);
    ASSERT_NE((void *)NULL, g_subInfo);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = (char *)"subVertexLabel";
    tmp_g_sub_info.configJson = g_subInfo;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, subConn[0], sn_callback_wait2, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_subInfo);

    // 订阅关系2
    readJanssonFile("schema_file/all_type_schema_subinfo_2.gmjson", &g_subInfo);
    ASSERT_NE((void *)NULL, g_subInfo);

    tmp_g_sub_info.subsName = (char *)"subVertexLabel_2";
    tmp_g_sub_info.configJson = g_subInfo;
    SnUserDataT * user_data2= (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data2, 0, sizeof(SnUserDataT));
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, subConn[1], sn_callback_wait, user_data2);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_subInfo);

    // 订阅关系3
    readJanssonFile("schema_file/all_type_schema_subinfo_3.gmjson", &g_subInfo);
    ASSERT_NE((void *)NULL, g_subInfo);

    tmp_g_sub_info.subsName = (char *)"subVertexLabel_3";
    tmp_g_sub_info.configJson = g_subInfo;
    SnUserDataT * user_data3 = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data3, 0, sizeof(SnUserDataT));
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, subConn[1], sn_callback_wait2, user_data3);
    EXPECT_EQ(GMERR_OK, ret);

    // 写数据
    for (i = 0; i < g_dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_INFO, ">>> insert records: %d\n", g_dataNum);
    sleep(1);

    // 更新数据
    for (i = 0; i < g_dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        test_setVertexProperty_updt(g_stmt_sync, i + g_dataNum);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lablePK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_INFO, ">>> update records: %d\n", g_dataNum);
    sleep(1);

    // replace
    for (i = 0; i < g_dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_REPLACE);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_INFO, ">>> replace records: %d\n", g_dataNum);
    sleep(1);

    // 删除数据
    for (i = 0; i < g_dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_DELETE);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lablePK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_INFO, ">>> delete records: %d\n", g_dataNum);
    sleep(1);

    // replace
    for (i = 0; i < g_dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_labelName, GMC_OPERATION_REPLACE);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_INFO, ">>> replace records: %d\n", g_dataNum);
    sleep(1);

    //用于弱校验，校验新增推送次数
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_dataNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_UPDATE, g_dataNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_REPLACE_UPDATE, g_dataNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_dataNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_REPLACE_INSERT, g_dataNum);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, ">>> sub_trigger_cnt: %u subCallTmOutCt : %u  subCallTmOutRows: %u\n",
        g_subTriggerCnt, g_subCallTmOutCt, g_subCallTmOutRows);

    ret = checkSubTmoutLog(200, (char *)"subVertexLabel", 0);
    EXPECT_EQ(ret, 0);

    ret = checkSubTmoutLog(25, (char *)"subVertexLabel_2", 5);
    EXPECT_EQ(ret, 0);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt_sync, "subVertexLabel_2");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt_sync, "subVertexLabel_3");
    EXPECT_EQ(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(subConn[0]);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(subConn[1]);
    EXPECT_EQ(GMERR_OK, ret);

    free(user_data2);
    free(user_data3);
}
