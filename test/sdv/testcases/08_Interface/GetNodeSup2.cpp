/*****************************************************************************
 Description  : GmcGetNodeSuperFieldByName接口健壮性测试
 Notes        : 01.参数正常，预期返回GMERR_OK
                02.stmt=NULL,预期返回错误码
                03.async stmt,预期返回错误码
                04.superFieldName=NULLL,预期返回错误码
                05.invalid superFieldName,预期返回错误码
                06.invalid length,预期返回错误码
                07.output parameter=NULL,预期返回错误码
 History      :
 Author       : linjian lwx734521
 Modification :
 Date         : 2021/1/28
*****************************************************************************/
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
using namespace std;
#define LABELNAME_MAX_LENGTH 128

int ret = 0;
GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
GmcStmtT *g_stmt_async = NULL;
GmcConnT *g_conn_async = NULL;

char *g_schema = NULL;
char g_labelName[LABELNAME_MAX_LENGTH] = "DML_011_scanVertexTest_001";
char g_configJson[512] = "{\"max_record_count\" : 999999}";

class GmcGetNodeSuperFieldByName_test : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        readJanssonFile("schema_file/scan_vertex_label_tree.gmjson", &g_schema);
        EXPECT_NE((void *)NULL, g_schema);
    }
    static void TearDownTestCase()
    {
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
        free(g_schema);
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void GmcGetNodeSuperFieldByName_test::SetUp()
{
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    GmcDropVertexLabel(g_stmt, g_labelName);
    AW_CHECK_LOG_BEGIN(0);
}

void GmcGetNodeSuperFieldByName_test::TearDown()
{
    AW_CHECK_LOG_END();
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
}

// 01.参数正常，预期返回GMERR_OK
TEST_F(GmcGetNodeSuperFieldByName_test, GmcSetNodeSuperFieldByName_test_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    ret = GmcCreateVertexLabel(g_stmt, g_schema, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);

    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // tree接口适配  定义节点结构体
    GmcNodeT *root, *H1;  // root为根节点  H1为普通子节点
    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex
    int32_t num = 10;
    int32_t H0Vale = num;
    ret = GmcNodeSetPropertyByName(root, (char *)"H0", GMC_DATATYPE_INT32, &H0Vale,
        sizeof(int32_t));  // ret = GmcSetNodePropertyByName(g_stmt, (char *)"H0", GMC_DATATYPE_INT32, &H0Vale,
                           // sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "H1", &H1);  // ret = GmcSetNodeRecordIndex(g_stmt, (char *) "H1", 0);
    EXPECT_EQ(GMERR_OK, ret);
    char *sp_1 = (char *)malloc(16);
    *(int32_t *)sp_1 = num;
    *(int32_t *)(sp_1 + 4) = num;
    *(int32_t *)(sp_1 + 8) = num;
    *(int32_t *)(sp_1 + 12) = num;
    ret = GmcNodeSetSuperfieldByName(H1, (char *)"superfield0", sp_1,
        16);  // ret = GmcSetNodeSuperFieldByName(g_stmt, (char *)"H1.superfield0", sp_1, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    free(sp_1);

    // query
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    char *sp_1_get = (char *)malloc(16);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &H0Vale, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "DML_011_scanVertexTest_K0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    ret = GmcFetch(g_stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "H1", &H1);  // ret = GmcSetNodeRecordIndex(g_stmt, (char *) "H1", 0);
    EXPECT_EQ(GMERR_OK, ret);
    // test point:参数正常
    ret = GmcNodeGetSuperfieldByName(H1, (char *)"superfield0", sp_1_get,
        16);  // ret = GmcGetNodeSuperFieldByName(g_stmt, (char *)"H1.superfield0", 16, sp_1_get);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(num, *(int32_t *)sp_1_get);
    EXPECT_EQ(num, *(int32_t *)(sp_1_get + 4));
    EXPECT_EQ(num, *(int32_t *)(sp_1_get + 8));
    EXPECT_EQ(num, *(int32_t *)(sp_1_get + 12));
    free(sp_1_get);

    // dorp vertexLabel
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 02.stmt=NULL,预期返回错误码
TEST_F(GmcGetNodeSuperFieldByName_test, GmcSetNodeSuperFieldByName_test_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    ret = GmcCreateVertexLabel(g_stmt, g_schema, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);

    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // tree接口适配  定义节点结构体
    GmcNodeT *root, *H1;  // root为根节点  H1为普通子节点
    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex
    int32_t num = 10;
    int32_t H0Vale = num;
    ret = GmcNodeSetPropertyByName(root, (char *)"H0", GMC_DATATYPE_INT32, &H0Vale,
        sizeof(int32_t));  // ret = GmcSetNodePropertyByName(g_stmt, (char *)"H0", GMC_DATATYPE_INT32, &H0Vale,
                           // sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "H1", &H1);  // ret = GmcSetNodeRecordIndex(g_stmt, (char *) "H1", 0);
    EXPECT_EQ(GMERR_OK, ret);
    char *sp_1 = (char *)malloc(16);
    *(int32_t *)sp_1 = num;
    *(int32_t *)(sp_1 + 4) = num;
    *(int32_t *)(sp_1 + 8) = num;
    *(int32_t *)(sp_1 + 12) = num;
    ret = GmcNodeSetSuperfieldByName(H1, (char *)"superfield0", sp_1,
        16);  // ret = GmcSetNodeSuperFieldByName(g_stmt, (char *)"H1.superfield0", sp_1, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    free(sp_1);

    // query
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    char *sp_1_get = (char *)malloc(16);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &H0Vale, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "DML_011_scanVertexTest_K0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "H1", &H1);  // ret = GmcSetNodeRecordIndex(g_stmt, (char *) "H1", 0);
    EXPECT_EQ(GMERR_OK, ret);

    free(sp_1_get);
    // close vertexLabel

    // dorp vertexLabel
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 04.superFieldName=NULLL,预期返回错误码
TEST_F(GmcGetNodeSuperFieldByName_test, GmcSetNodeSuperFieldByName_test_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    ret = GmcCreateVertexLabel(g_stmt, g_schema, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);

    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // tree接口适配  定义节点结构体
    GmcNodeT *root, *H1;  // root为根节点  H1为普通子节点
    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex
    int32_t num = 10;
    int32_t H0Vale = num;
    ret = GmcNodeSetPropertyByName(root, (char *)"H0", GMC_DATATYPE_INT32, &H0Vale,
        sizeof(int32_t));  // ret = GmcSetNodePropertyByName(g_stmt, (char *)"H0", GMC_DATATYPE_INT32, &H0Vale,
                           // sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "H1", &H1);  // ret = GmcSetNodeRecordIndex(g_stmt, (char *) "H1", 0);
    EXPECT_EQ(GMERR_OK, ret);
    char *sp_1 = (char *)malloc(16);
    *(int32_t *)sp_1 = num;
    *(int32_t *)(sp_1 + 4) = num;
    *(int32_t *)(sp_1 + 8) = num;
    *(int32_t *)(sp_1 + 12) = num;
    ret = GmcNodeSetSuperfieldByName(H1, (char *)"superfield0", sp_1,
        16);  // ret = GmcSetNodeSuperFieldByName(g_stmt, (char *)"H1.superfield0", sp_1, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    free(sp_1);

    // query
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    char *sp_1_get = (char *)malloc(16);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &H0Vale, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "DML_011_scanVertexTest_K0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "H1", &H1);  // ret = GmcSetNodeRecordIndex(g_stmt, (char *) "H1", 0);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    free(sp_1_get);

    // dorp vertexLabel
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 05.invalid superFieldName,预期返回错误码
TEST_F(GmcGetNodeSuperFieldByName_test, GmcSetNodeSuperFieldByName_test_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    ret = GmcCreateVertexLabel(g_stmt, g_schema, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);

    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // tree接口适配  定义节点结构体
    GmcNodeT *root, *H1;  // root为根节点  H1为普通子节点
    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex
    int32_t num = 10;
    int32_t H0Vale = num;
    ret = GmcNodeSetPropertyByName(root, (char *)"H0", GMC_DATATYPE_INT32, &H0Vale,
        sizeof(int32_t));  // ret = GmcSetNodePropertyByName(g_stmt, (char *)"H0", GMC_DATATYPE_INT32, &H0Vale,
                           // sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "H1", &H1);  // ret = GmcSetNodeRecordIndex(g_stmt, (char *) "H1", 0);
    EXPECT_EQ(GMERR_OK, ret);
    char *sp_1 = (char *)malloc(16);
    *(int32_t *)sp_1 = num;
    *(int32_t *)(sp_1 + 4) = num;
    *(int32_t *)(sp_1 + 8) = num;
    *(int32_t *)(sp_1 + 12) = num;
    ret = GmcNodeSetSuperfieldByName(H1, (char *)"superfield0", sp_1,
        16);  // ret = GmcSetNodeSuperFieldByName(g_stmt, (char *)"H1.superfield0", sp_1, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    free(sp_1);

    // query
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    char *sp_1_get = (char *)malloc(16);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &H0Vale, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "DML_011_scanVertexTest_K0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "H1", &H1);  // ret = GmcSetNodeRecordIndex(g_stmt, (char *) "H1", 0);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    free(sp_1_get);

    // dorp vertexLabel
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 06.invalid length,预期返回错误码
TEST_F(GmcGetNodeSuperFieldByName_test, GmcSetNodeSuperFieldByName_test_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    ret = GmcCreateVertexLabel(g_stmt, g_schema, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);

    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // tree接口适配  定义节点结构体
    GmcNodeT *root, *H1;  // root为根节点  H1为普通子节点
    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex
    int32_t num = 10;
    int32_t H0Vale = num;
    ret = GmcNodeSetPropertyByName(root, (char *)"H0", GMC_DATATYPE_INT32, &H0Vale,
        sizeof(int32_t));  // ret = GmcSetNodePropertyByName(g_stmt, (char *)"H0", GMC_DATATYPE_INT32, &H0Vale,
                           // sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "H1", &H1);  // ret = GmcSetNodeRecordIndex(g_stmt, (char *) "H1", 0);
    EXPECT_EQ(GMERR_OK, ret);
    char *sp_1 = (char *)malloc(16);
    *(int32_t *)sp_1 = num;
    *(int32_t *)(sp_1 + 4) = num;
    *(int32_t *)(sp_1 + 8) = num;
    *(int32_t *)(sp_1 + 12) = num;
    ret = GmcNodeSetSuperfieldByName(H1, (char *)"superfield0", sp_1,
        16);  // ret = GmcSetNodeSuperFieldByName(g_stmt, (char *)"H1.superfield0", sp_1, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    free(sp_1);

    // query
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    char *sp_1_get = (char *)malloc(16);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &H0Vale, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "DML_011_scanVertexTest_K0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "H1", &H1);  // ret = GmcSetNodeRecordIndex(g_stmt, (char *) "H1", 0);
    EXPECT_EQ(GMERR_OK, ret);

    // test point:invalid length
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    free(sp_1_get);

    // dorp vertexLabel
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 07.output parameter=NULL,预期返回错误码
TEST_F(GmcGetNodeSuperFieldByName_test, GmcSetNodeSuperFieldByName_test_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    ret = GmcCreateVertexLabel(g_stmt, g_schema, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);

    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // tree接口适配  定义节点结构体
    GmcNodeT *root, *H1;  // root为根节点  H1为普通子节点
    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex
    int32_t num = 10;
    int32_t H0Vale = num;
    ret = GmcNodeSetPropertyByName(root, (char *)"H0", GMC_DATATYPE_INT32, &H0Vale,
        sizeof(int32_t));  // ret = GmcSetNodePropertyByName(g_stmt, (char *)"H0", GMC_DATATYPE_INT32, &H0Vale,
                           // sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "H1", &H1);  // ret = GmcSetNodeRecordIndex(g_stmt, (char *) "H1", 0);
    EXPECT_EQ(GMERR_OK, ret);
    char *sp_1 = (char *)malloc(16);
    *(int32_t *)sp_1 = num;
    *(int32_t *)(sp_1 + 4) = num;
    *(int32_t *)(sp_1 + 8) = num;
    *(int32_t *)(sp_1 + 12) = num;
    ret = GmcNodeSetSuperfieldByName(H1, (char *)"superfield0", sp_1,
        16);  // ret = GmcSetNodeSuperFieldByName(g_stmt, (char *)"H1.superfield0", sp_1, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    free(sp_1);

    // query
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    char *sp_1_get = (char *)malloc(16);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &H0Vale, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "DML_011_scanVertexTest_K0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "H1", &H1);  // ret = GmcSetNodeRecordIndex(g_stmt, (char *) "H1", 0);
    EXPECT_EQ(GMERR_OK, ret);

    // test point:output parameter=NULL
    ret = GmcNodeGetSuperfieldByName(H1, (char *)"superfield0", NULL,
        16);  // ret = GmcGetNodeSuperFieldByName(g_stmt, (char *)"H1.superfield0", 16, NULL);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(sp_1_get);

    // dorp vertexLabel
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);
}
