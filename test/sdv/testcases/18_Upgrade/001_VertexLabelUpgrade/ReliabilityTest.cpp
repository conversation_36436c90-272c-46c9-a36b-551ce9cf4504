/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2012-2018. All rights reserved.
 Description  :可靠性测试
 Author       : wuxiaochun wx753022
 Modification :
 Date         : 2022/08/08
**************************************************************************** */
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <time.h>

#include "gtest/gtest.h"

#include "VertexLabelUpgradeStruct.h"


class ReliabilityTest : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void ReliabilityTest::SetUpTestCase()
{
    int ret = 0;
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void ReliabilityTest::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}

void ReliabilityTest::SetUp()
{
    // 建连
    int ret = 0;
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void ReliabilityTest::TearDown()
{
    AW_CHECK_LOG_END();
    // 断链
    int ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
}

class ReliabilityTest02 : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void ReliabilityTest02::SetUpTestCase()
{}

void ReliabilityTest02::TearDownTestCase()
{}

void ReliabilityTest02::SetUp()
{}

void ReliabilityTest02::TearDown()
{}

void *Thread01(void *args)
{
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = 0;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 对旧版本insert数据
    uint32_t schemaVersion = 0;
    int64_t startValue = 0;
    int64_t endValue = 100;
    uint64_t resCount = 1;
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, schemaVersion, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i =startValue; i < endValue; i++) {
        TestSimpleT1SetPk(stmt, i);
        TestSimpleT1OldVersionSetProperty(stmt, i, true);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(stmt, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, schemaVersion, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = true;
    for (int i =startValue; i < endValue; i++) {
       TestSimpleT1PkIndexSet(stmt, i);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSimpleT1UpdateGetOldPropertyByName(stmt, i, true);
        TestSimpleT1GetLpmProperty(stmt, i);
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ((void *)0);
}
void *Thread02(void *args)
{
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = 0;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 对中间版本insert数据
    uint32_t schemaVersion = 1;
    int64_t startValue = 0;
    int64_t endValue = 100;
    uint64_t resCount = 1;
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, schemaVersion, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i =startValue; i < endValue; i++) {
        TestSimpleT1SetPk(stmt, i + 200);
        TestSimpleT1OldVersionSetProperty(stmt, i + 200, true);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(stmt, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, schemaVersion, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = true;
    for (int i =startValue; i < endValue; i++) {
       TestSimpleT1PkIndexSet(stmt, i + 200);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSimpleT1UpdateGetOldPropertyByName(stmt, i + 200, true);
        TestSimpleT1GetLpmProperty(stmt, i + 200);
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ((void *)0);
}
void *Thread03(void *args)
{
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = 0;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 对新版本insert数据
    uint32_t schemaVersion = 2;
    int64_t startValue = 0;
    int64_t endValue = 100;
    uint64_t resCount = 1;
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, schemaVersion, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i =startValue; i < endValue; i++) {
        TestSimpleT1SetPk(stmt, i + 600);
        TestSimpleT1OldVersionSetProperty(stmt, i + 600, true);
        TestSimpleT1NewFieldSetOk(stmt, i + 600);
        int64_t f15Value = i + 600;
        int32_t ret = GmcSetVertexProperty(stmt, (char *)"F15", GMC_DATATYPE_INT64, &f15Value, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(stmt, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, schemaVersion, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = true;
    for (int i =startValue; i < endValue; i++) {
       TestSimpleT1PkIndexSet(stmt, i + 600);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSimpleT1UpdateGetOldPropertyByName(stmt, i + 600, true);
        TestSimpleT1GetLpmProperty(stmt, i + 600);
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ((void *)0);
}
/* *********************************************************************************
 Description  : 001.简单表多线程并发读写新老版本数据
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
*********************************************************************************** */
TEST_F(ReliabilityTest, Upgrade_001_005_001)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    int ret ;
    char *schemaParth = (char *)"./schemaFile/simpleLabel1.gmjson";
    // 预制一张表
    GmcDropVertexLabel(g_stmt, g_labelName);
    ret = TestCreateLabel(g_stmt, schemaParth, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 完成表升级
    char *expectValue = (char *)"upgrade successfully";
    char *schemaUpdateParth = (char *)"./schemaFile/SimpleLabelUpgradeUint46.gmjson";
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char *schemaUpdateParth1 = (char *)"./schemaFile/SimpleLabelUpgradeint64.gmjson";
    ret = TestUpdateVertexLabel(schemaUpdateParth1, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    pthread_t thr_01;
    pthread_t thr_02;
    pthread_t thr_03;
    ret = pthread_create(&thr_01, NULL, Thread01, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thr_02, NULL, Thread02, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thr_03, NULL, Thread03, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    pthread_join(thr_01, NULL);
    pthread_join(thr_02, NULL);
    pthread_join(thr_03, NULL);
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

void *OldVersionWriteRead(void *args)
{
    GtSpeciallabelCfgT vertexCfg = *(GtSpeciallabelCfgT *)args;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int32_t ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabelStructWrite(stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(stmt, vertexCfg, 0, bytesValue, stringValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

void *MidVersionWriteRead(void *args)
{
    GtSpeciallabelCfgT vertexCfg = *(GtSpeciallabelCfgT *)args;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int32_t ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel2StructWrite(stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(stmt, vertexCfg, 2, bytesValue, stringValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

void *NewVersionWriteRead(void *args)
{
    GtSpeciallabelCfgT vertexCfg = *(GtSpeciallabelCfgT *)args;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int32_t ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel3StructWrite(stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(stmt, vertexCfg, 2, bytesValue, stringValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

/* ****************************************************************************
 Description  : 003.特殊复杂表多线程并发新老版本并发写与读
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(ReliabilityTest, Upgrade_001_005_003)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test_start");
    // 建连
    int ret = 0;
    char *schemaParth = (char *)"./schemaFile/SpecialTableSchema.gmjson";
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    int32_t startValue = 0;
    uint32_t endValue = 20;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    uint32_t schemaVersion = 1;
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SpecialTableSchemaBigObj.gmjson";
    pthread_t oldVersion;
    pthread_t midVersion;
    pthread_t newVersion;
    // 创表
    ret = TestCreateLabel(g_stmt, schemaParth, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT,
                                    {false, true, true}};
    GtSpeciallabelCfgT vertexCfg2 = {(int32_t)endValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_REPLACE,
                                     {false, false, true}};
    GtSpeciallabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue, 0, 1, 0, 3, 3, 2, GMC_OPERATION_REPLACE,
                                     {false, false, false}};
    int32_t err = pthread_create(&oldVersion, NULL, OldVersionWriteRead, &vertexCfg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, err);
    err = pthread_create(&midVersion, NULL, MidVersionWriteRead, &vertexCfg2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, err);
    err = pthread_create(&newVersion, NULL, NewVersionWriteRead, &vertexCfg3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, err);

    pthread_join(oldVersion, NULL);
    pthread_join(midVersion, NULL);
    pthread_join(newVersion, NULL);
    // 核查
    GtSpeciallabelCfgT pkReadCfg = {startValue, endValue * 3, 0, (int32_t)endValue * 3, 0, 3, 3, 2, GMC_OPERATION_SCAN};
    ret = TestSpecialTWholeRead(g_stmt, pkReadCfg, bytesValue, stringValue, true);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 004.老版本写数据至内存满，新版本更新数据
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(ReliabilityTest02, Upgrade_001_005_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 1000000;
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    char *schemaParth = (char *)"./schemaFile/SpecialTableSchema.gmjson";
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SpecialTableSchemaBigObj.gmjson";
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int count = 0;
    #if defined CPU_BIT_32
    system("sh $TEST_HOME/tools/stop.sh");                        // 修改配置，先停服务
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=100\"");  // 内存大小改小，减少单个用例执行时间
    system("sh $TEST_HOME/tools/start.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\""); // 恢复配置
    #else
    if (g_envType == 2) {
        system("sh $TEST_HOME/tools/start.sh");
    } else {
        system("sh $TEST_HOME/tools/stop.sh");                        // 修改配置，先停服务
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=300\"");  // 内存大小改小，减少单个用例执行时间
        system("sh $TEST_HOME/tools/start.sh -f");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\""); // 恢复配置
    }
    #endif
    sleep(1);
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创表
    ret = TestCreateLabel(stmt, schemaParth, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};

    GtSpeciallabelVertexT *vertex = (GtSpeciallabelVertexT *)malloc(sizeof(GtSpeciallabelVertexT));
    if (vertex == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, 1);
    }
    (void)memset(vertex, 0, sizeof(GtSpeciallabelVertexT));
    GtSpeciallabelT1VVertexT *t1V = (GtSpeciallabelT1VVertexT *)malloc(sizeof(GtSpeciallabelT1VVertexT) * vertexCfg.t1VCount);
    if (t1V == NULL) {
        AW_FUN_Log(LOG_ERROR, "t1V is NULL\n");
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, 1);
    }
    (void)memset(t1V, 0, sizeof(GtSpeciallabelT1VVertexT) * vertexCfg.t1VCount);
    vertex->t1V = t1V;
    GtSpeciallabelT2VVertexT *t2V =
    (GtSpeciallabelT2VVertexT *)malloc(sizeof(GtSpeciallabelT2VVertexT) * vertexCfg.t1VCount * vertexCfg.t2VCount);
    if (t2V == NULL) {
        AW_FUN_Log(LOG_ERROR, "t2V is NULL\n");
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, 1);
    }
    (void)memset(t2V, 0, sizeof(GtSpeciallabelT2VVertexT) * vertexCfg.t1VCount * vertexCfg.t2VCount);
    for (int32_t i = 0; i < vertexCfg.t1VCount; i++) {
        t1V[i].t2V = &t2V[vertexCfg.t2VCount * ((0) + i)];
    }
    TestLabelInfoT labelInfo = {g_labelName2, 0, g_testNameSpace};
    for (count = startValue; count < startValue + endValue; count++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName2, 0, GMC_OPERATION_INSERT);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GtSpeciallabelStructSetPk(vertex, count);
        GtSpeciallabelStructSetHashcluster(vertex, count, 0);
        GtSpeciallabelStructSetLocalhash(vertex, count, 0);
        GtSpeciallabelStructSetLocal(vertex, count);
        GtSpeciallabelStructSetLpm4(vertex, count);
        GtSpeciallabelStructSetOldProperty(vertex, count, vertexCfg.t1VCount, vertexCfg.t2VCount, bytesValue, stringValue,
                                           isDefaultValue);
        ret = testStructSetVertexWithBuf(stmt, vertex, &labelInfo);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret != 0) {
            AW_FUN_Log(LOG_INFO, "GmcExecute is ret = %d count = %d\n", ret, count);
            break;
        }
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (count % 50000 == 0) {
            AW_FUN_Log(LOG_INFO, "----ret = %d count = %d----\n", ret, count);
        }
        ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    GtSpeciallabelStructFree(vertex);
    free(t2V);
    free(t1V);
    free(vertex);

    // 读
    vertexCfg.count = count / 2;
    vertexCfg.schemaVersion = 2;
    vertexCfg.optType = GMC_OPERATION_SCAN;
    vertexCfg.expAffectRows = count;
    vertexCfg.fieldIsNull[1] = true;
    vertexCfg.fieldIsNull[2] = true;
    ret = TestSpecialT3NewOldVersionGeneralRead(stmt, vertexCfg, 0, bytesValue, stringValue, isDefaultValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg.startVal = count / 2;
    vertexCfg.count = count;
    ret = TestSpecialT3NewOldVersionGeneralRead(stmt, vertexCfg, 3, bytesValue, stringValue, isDefaultValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg.startVal = 0;
    vertexCfg.schemaVersion = 0;
    ret = TestSpecialT3NewOldVersionGeneralRead(stmt, vertexCfg, 1, bytesValue, stringValue, isDefaultValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end.");
}
