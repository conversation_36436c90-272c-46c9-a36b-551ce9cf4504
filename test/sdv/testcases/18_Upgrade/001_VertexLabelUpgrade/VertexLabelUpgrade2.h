/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2012-2018. All rights reserved.
 Description  :表结构升级头文件
 Author       : wuxiaochun wx753022
 Modification :
 Date         : 2022/07/22
**************************************************************************** */
#ifndef VERTEXLABEL_UPGRADE2_H
#define VERTEXLABEL_UPGRADE2_H

#include "t_datacom_lite.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
SnUserDataT *user_data;
char *g_labelName = (char *)"generalLabel";
int g_subIndex = 0;
#define MAX_NAME_LENGTH 128

char *g_labelConfig = NULL;

typedef struct TagSimplelabelCfg {
    int32_t startVal;  // 主键或其他非成员索引的起始值
    uint32_t count;    // 主键或其他非成员索引的数量
    int32_t coefficient;  // 字段值生成系数, 通过startVal和coefficient组合生成不同的整形和浮点型字段值
    int32_t expAffectRows;      // 预期的affectRows
    GmcOperationTypeE optType;  // vertex操作类型
} GtSimplelabelCfgT;

int TestGetAffactRows(GmcStmtT *stmt, int32_t expectValue)
{
    int32_t affectRows = 0;
    int ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectValue, affectRows);
    return expectValue == affectRows ? GMERR_OK : 1;
}

void TestDropVertexLabel(const char *vertexLabelName, int32_t expectValue = GMERR_OK)
{
    int ret = 0;
    GmcConnT *connSync = NULL;
    GmcStmtT *stmtSync = NULL;
    ret = testGmcConnect(&connSync, &stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmtSync, vertexLabelName);
    AW_MACRO_EXPECT_EQ_INT(expectValue, ret);
    ret = testGmcDisconnect(connSync, stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

int TestCreateLabel(GmcStmtT *stmt, char *schemaPath, char *labelName, char const *configJson = g_labelConfig)
{
    int ret = 0;
    char *testSchema = NULL;

    if (schemaPath) {
        readJanssonFile(schemaPath, &testSchema);
        EXPECT_NE((void *)NULL, testSchema);
    }
    ret = GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, testSchema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret == GMERR_OK) {
        printf("[INFO]Test create label %s success \n", labelName);
    } else {
        testGmcGetLastError(NULL);
    }
    if (testSchema) {
        free(testSchema);
        testSchema = NULL;
    }
    return ret;
}

int TestUpdateVertexLabel(char *schemaPath, char *expectValue, char *labelName = NULL, char *uWay = (char *)"online",
                          char *nsName = g_testNameSpace)
{
    char *schema = NULL;
    readJanssonFile(schemaPath, &schema);
    EXPECT_NE((void *)NULL, schema);
    free(schema);
    // gmddl工具升级表操作
    char cmd[512] = {0};
    int ret = 0;
    if (labelName) {
        (void)snprintf(cmd, 512, "%s/gmddl -c alter -t %s -f %s -u %s -ns %s", g_toolPath, labelName, schemaPath, uWay,
            nsName);
    } else {
        (void)snprintf(cmd, 512, "%s/gmddl -c alter -f %s -u %s -ns %s", g_toolPath, schemaPath, uWay, nsName);
    }
    AW_FUN_Log(LOG_INFO, "cmd: %s\n", cmd);
    ret = executeCommand(cmd, expectValue);
    if (ret != GMERR_OK) {
        system(cmd);
    }
    return ret;
}

/**-------------------简单表-----------------------**/
#define SIMPLE_LABEL_FIXED_SIZE 9
#define SIMPLE_LABEL2_FIXED_SIZE 8
#define SIMPLE_LABEL2_BITMAP_SIZE 8
#define MAX_MASK_LEN_16 1000
#define MAX_MASK_LEN_24 2501000
#define SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE 13312

// 设置索引start
void TestGeneralT1SetPk(GmcNodeT *node, int64_t i)
{
    int ret = 0;
    int64_t f0Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F0", GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestGeneralT1PkIndexSet(GmcStmtT *stmt, int64_t i)
{
    int ret = 0;
    int64_t f0Value = i;
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestGeneralHashclusterIndexSet(GmcStmtT *stmt, int64_t i)
{
    int ret = 0;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    ret = GmcSetIndexKeyName(stmt, "hashcluster_unique_key");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestGeneralT1LocalhashIndexSet(GmcStmtT *stmt, int64_t i)
{
    int ret = 0;
    int16_t f4Value = i;
    uint16_t f5Value = i;
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT16, &f5Value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestGeneralT1LocalIndexRangeSet(GmcStmtT *stmt, int64_t startValue, int64_t endValue)
{
    int ret = 0;
    unsigned int arrLen = 1;
    uint32_t lValue = startValue;
    uint32_t rValue = endValue;

    GmcPropValueT *leftKeyProps = NULL;
    leftKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    if (leftKeyProps != NULL) {
        leftKeyProps[0].type = GMC_DATATYPE_UINT32;
        leftKeyProps[0].value = &lValue;
        leftKeyProps[0].size = sizeof(uint32_t);
    }

    GmcPropValueT *rightKeyProps = NULL;
    rightKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    if (rightKeyProps != NULL) {
        rightKeyProps[0].type = GMC_DATATYPE_UINT32;
        rightKeyProps[0].value = &rValue;
        rightKeyProps[0].size = sizeof(uint32_t);
    }

    GmcRangeItemT items[arrLen];
    items[0].lValue = &leftKeyProps[0];
    items[0].rValue = &rightKeyProps[0];
    items[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].order = GMC_ORDER_ASC;
    ret = GmcSetKeyRange(stmt, items, arrLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "local_key");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(leftKeyProps);
    free(rightKeyProps);
}

void TestGeneralT1LpmIndexSet(GmcStmtT *stmt, int64_t i)
{
    int ret = 0;
    uint32_t vrid = 0;
    uint32_t vrfIndex = 0;
    uint8_t destIpAddr[16] = {
        0xcd, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20, 0x20};
    uint8_t maskLen = i;
    ret = GmcSetIndexKeyName(stmt, "lpm6_key");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vrid, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, destIpAddr, 16);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 设置索引end

// 设置属性start
void TestGeneralSetCommonProperty_Root(GmcNodeT *node, int64_t i, char *string, uint8_t *wrFixed,
                                       bool isDefaultValue = true)
{
    int ret = 0;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    uint32_t f3Value = i;
    int16_t f4Value = i % 32768;
    uint16_t f5Value = i % 65536;
    uint8_t f6Value = i;
    uint64_t f7Value = i;

    uint32_t vrid = 0;
    uint32_t vrfIndex = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = i;
    uint8_t f13Value = i & 0xf;
    ret = GmcNodeSetPropertyByName(node, (char *)"F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F3", GMC_DATATYPE_UINT32, &f3Value, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F4", GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F5", GMC_DATATYPE_UINT16, &f5Value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F7", GMC_DATATYPE_TIME, &f7Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F9", GMC_DATATYPE_STRING, string, strlen(string));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    if (!isDefaultValue) {
        uint8_t f9Value = i % 31;
        uint16_t f10Value = i % 1023;

        ret = GmcNodeSetPropertyByName(node, (char *)"F10", GMC_DATATYPE_BITFIELD8, &f9Value, sizeof(uint8_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = GmcNodeSetPropertyByName(node, (char *)"F11", GMC_DATATYPE_BITFIELD16, &f10Value, sizeof(uint16_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcNodeSetPropertyByName(node, (char *)"F12", GMC_DATATYPE_UINT32, &vrid, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F13", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F8", GMC_DATATYPE_FIXED, wrFixed, 16);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F6", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestGeneralUpdateSetCommonProperty_Root(GmcNodeT *node, int64_t i, char *string, bool isDefaultValue = true)
{
    int ret = 0;
    int64_t f0Value = i;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    uint32_t f3Value = i;
    int16_t f4Value = i % 32768;
    uint16_t f5Value = i % 65536;
    uint64_t f7Value = i;

    uint8_t f13Value = i & 0xf;
    ret = GmcNodeSetPropertyByName(node, (char *)"F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F3", GMC_DATATYPE_UINT32, &f3Value, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F4", GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F5", GMC_DATATYPE_UINT16, &f5Value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F7", GMC_DATATYPE_TIME, &f7Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F9", GMC_DATATYPE_STRING, string, strlen(string));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    if (!isDefaultValue) {
        uint8_t f9Value = i % 31;
        uint16_t f10Value = i % 1023;

        ret = GmcNodeSetPropertyByName(node, (char *)"F10", GMC_DATATYPE_BITFIELD8, &f9Value, sizeof(uint8_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = GmcNodeSetPropertyByName(node, (char *)"F11", GMC_DATATYPE_BITFIELD16, &f10Value, sizeof(uint16_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestGeneralT1ldVersionSetAddStringPropertyV0_Root(GmcNodeT *node, char *string)
{
    int ret = 0;
    ret = GmcNodeSetPropertyByName(node, (char *)"F14", GMC_DATATYPE_STRING, string, strlen(string));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
}

void TestGeneralT1ldVersionSetAddStringPropertyV1_Root(GmcNodeT *node, char *string)
{
    int ret = 0;
    ret = GmcNodeSetPropertyByName(node, (char *)"F14", GMC_DATATYPE_STRING, string, strlen(string));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestGeneralT1ldVersionSetAddStringPropertyV2_Root(GmcNodeT *node, char *string)
{
    int ret = 0;
    ret = GmcNodeSetPropertyByName(node, (char *)"F15", GMC_DATATYPE_STRING, string, strlen(string));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestGeneralT1ldVersionSetAllFieldsProperty_Root(GmcNodeT *node, int64_t i)
{
    int ret = 0;

    int64_t f14Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F14", GMC_DATATYPE_INT64, &f14Value, sizeof(int64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint64_t f15Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F15", GMC_DATATYPE_UINT64, &f15Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int32_t f16Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F16", GMC_DATATYPE_INT32, &f16Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint32_t f17Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F17", GMC_DATATYPE_UINT32, &f17Value, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int16_t f18Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F18", GMC_DATATYPE_INT16, &f18Value, sizeof(int16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint16_t f19Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F19", GMC_DATATYPE_UINT16, &f19Value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int8_t f20Value = i % 128;
    ret = GmcNodeSetPropertyByName(node, (char *)"F20", GMC_DATATYPE_INT8, &f20Value, sizeof(int8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint8_t f21Value = i % 256;
    ret = GmcNodeSetPropertyByName(node, (char *)"F21", GMC_DATATYPE_UINT8, &f21Value, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint64_t f22Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F22", GMC_DATATYPE_TIME, &f22Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char f23Value[8] = "fixed";
    ret = GmcNodeSetPropertyByName(node, (char *)"F23", GMC_DATATYPE_FIXED, &f23Value, 8);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char f24Value[20] = "string";
    ret = GmcNodeSetPropertyByName(node, (char *)"F24", GMC_DATATYPE_STRING, &f24Value, strlen(f24Value));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f25Value = (i)&0x1f;
    ret = GmcNodeSetPropertyByName(node, (char *)"F25", GMC_DATATYPE_BITFIELD8, &f25Value, sizeof(f25Value));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint16_t f26Value = (i)&0x1ffff;
    ret = GmcNodeSetPropertyByName(node, (char *)"F26", GMC_DATATYPE_BITFIELD16, &f26Value, sizeof(f26Value));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint32_t f27Value = (i)&0x1ffff;
    ret = GmcNodeSetPropertyByName(node, (char *)"F27", GMC_DATATYPE_BITFIELD32, &f27Value, sizeof(f27Value));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint64_t f28Value = (i)&0x1ffffffff;
    ret = GmcNodeSetPropertyByName(node, (char *)"F28", GMC_DATATYPE_BITFIELD64, &f28Value, sizeof(f28Value));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    bool f29Value = true;
    ret = GmcNodeSetPropertyByName(node, (char *)"F29", GMC_DATATYPE_BOOL, &f29Value, sizeof(f29Value));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    float f30Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F30", GMC_DATATYPE_FLOAT, &f30Value, sizeof(f30Value));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    double f31Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F31", GMC_DATATYPE_DOUBLE, &f31Value, sizeof(f31Value));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f32ValueBits[1] = {0xff};
    GmcBitMapT f32Value = {0};
    f32Value.beginPos = 0;
    f32Value.endPos = 8 - 1;
    f32Value.bits = f32ValueBits;
    ret = GmcNodeSetPropertyByName(node, (char *)"F32", GMC_DATATYPE_BITMAP, &f32Value, sizeof(f32Value));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestGeneralT1ldVersionSetAllFieldsPropertyV2_Root(GmcNodeT *node, int64_t i)
{
    int ret = 0;

    int64_t f14Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F14", GMC_DATATYPE_INT64, &f14Value, sizeof(int64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint64_t f15Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F15", GMC_DATATYPE_UINT64, &f15Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int32_t f16Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F16", GMC_DATATYPE_INT32, &f16Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint32_t f17Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F17", GMC_DATATYPE_UINT32, &f17Value, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int16_t f18Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F18", GMC_DATATYPE_INT16, &f18Value, sizeof(int16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint16_t f19Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F19", GMC_DATATYPE_UINT16, &f19Value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int8_t f20Value = i % 128;
    ret = GmcNodeSetPropertyByName(node, (char *)"F20", GMC_DATATYPE_INT8, &f20Value, sizeof(int8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint8_t f21Value = i % 256;
    ret = GmcNodeSetPropertyByName(node, (char *)"F21", GMC_DATATYPE_UINT8, &f21Value, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint64_t f22Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F22", GMC_DATATYPE_TIME, &f22Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char f23Value[8] = "fixed";
    ret = GmcNodeSetPropertyByName(node, (char *)"F23", GMC_DATATYPE_FIXED, &f23Value, 8);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char f24Value[20] = "string";
    ret = GmcNodeSetPropertyByName(node, (char *)"F24", GMC_DATATYPE_STRING, &f24Value, strlen(f24Value));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f25Value = (i)&0x1f;
    ret = GmcNodeSetPropertyByName(node, (char *)"F25", GMC_DATATYPE_BITFIELD8, &f25Value, sizeof(f25Value));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint16_t f26Value = (i)&0x1ffff;
    ret = GmcNodeSetPropertyByName(node, (char *)"F26", GMC_DATATYPE_BITFIELD16, &f26Value, sizeof(f26Value));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint32_t f27Value = (i)&0x1ffff;
    ret = GmcNodeSetPropertyByName(node, (char *)"F27", GMC_DATATYPE_BITFIELD32, &f27Value, sizeof(f27Value));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint64_t f28Value = (i)&0x1ffffffff;
    ret = GmcNodeSetPropertyByName(node, (char *)"F28", GMC_DATATYPE_BITFIELD64, &f28Value, sizeof(f28Value));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    bool f29Value = true;
    ret = GmcNodeSetPropertyByName(node, (char *)"F29", GMC_DATATYPE_BOOL, &f29Value, sizeof(f29Value));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    float f30Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F30", GMC_DATATYPE_FLOAT, &f30Value, sizeof(f30Value));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    double f31Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F31", GMC_DATATYPE_DOUBLE, &f31Value, sizeof(f31Value));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f32ValueBits[1] = {0xff};
    GmcBitMapT f32Value = {0};
    f32Value.beginPos = 0;
    f32Value.endPos = 8 - 1;
    f32Value.bits = f32ValueBits;
    ret = GmcNodeSetPropertyByName(node, (char *)"F32", GMC_DATATYPE_BITMAP, &f32Value, sizeof(f32Value));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int64_t f33Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F33", GMC_DATATYPE_INT64, &f33Value, sizeof(int64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestGeneralT1ldVersionSetAllFieldsPropertyFailed_Root(GmcNodeT *node, int64_t i)
{
    int ret = 0;

    int64_t f14Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F14", GMC_DATATYPE_INT64, &f14Value, sizeof(int64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    uint64_t f15Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F15", GMC_DATATYPE_UINT64, &f15Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    int32_t f16Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F16", GMC_DATATYPE_INT32, &f16Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    uint32_t f17Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F17", GMC_DATATYPE_UINT32, &f17Value, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    int16_t f18Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F18", GMC_DATATYPE_INT16, &f18Value, sizeof(int16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    uint16_t f19Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F19", GMC_DATATYPE_UINT16, &f19Value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    int8_t f20Value = i % 128;
    ret = GmcNodeSetPropertyByName(node, (char *)"F20", GMC_DATATYPE_INT8, &f20Value, sizeof(int8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    uint8_t f21Value = i % 256;
    ret = GmcNodeSetPropertyByName(node, (char *)"F21", GMC_DATATYPE_UINT8, &f21Value, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    uint64_t f22Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F22", GMC_DATATYPE_TIME, &f22Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    char f23Value[8] = "fixed";
    ret = GmcNodeSetPropertyByName(node, (char *)"F23", GMC_DATATYPE_FIXED, &f23Value, 8);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    char f24Value[20] = "string";
    ret = GmcNodeSetPropertyByName(node, (char *)"F24", GMC_DATATYPE_STRING, &f24Value, strlen(f24Value));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint8_t f25Value = (i)&0x1f;
    ret = GmcNodeSetPropertyByName(node, (char *)"F25", GMC_DATATYPE_BITFIELD8, &f25Value, sizeof(f25Value));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    uint16_t f26Value = (i)&0x1ffff;
    ret = GmcNodeSetPropertyByName(node, (char *)"F26", GMC_DATATYPE_BITFIELD16, &f26Value, sizeof(f26Value));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    uint32_t f27Value = (i)&0x1ffff;
    ret = GmcNodeSetPropertyByName(node, (char *)"F27", GMC_DATATYPE_BITFIELD32, &f27Value, sizeof(f27Value));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    uint64_t f28Value = (i)&0x1ffffffff;
    ret = GmcNodeSetPropertyByName(node, (char *)"F28", GMC_DATATYPE_BITFIELD64, &f28Value, sizeof(f28Value));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    bool f29Value = true;
    ret = GmcNodeSetPropertyByName(node, (char *)"F29", GMC_DATATYPE_BOOL, &f29Value, sizeof(f29Value));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    float f30Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F30", GMC_DATATYPE_FLOAT, &f30Value, sizeof(f30Value));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    double f31Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F31", GMC_DATATYPE_DOUBLE, &f31Value, sizeof(f31Value));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint8_t f32ValueBits[1] = {0xff};
    GmcBitMapT f32Value = {0};
    f32Value.beginPos = 0;
    f32Value.endPos = 8 - 1;
    f32Value.bits = f32ValueBits;
    ret = GmcNodeSetPropertyByName(node, (char *)"F32", GMC_DATATYPE_BITMAP, &f32Value, sizeof(f32Value));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    int64_t f33Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F33", GMC_DATATYPE_INT64, &f33Value, sizeof(int64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
}

void TestGeneralT1ldVersionSetAllFieldsPropertyV2Failed_Root(GmcNodeT *node, int64_t i)
{
    int ret = 0;
    int64_t f33Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F33", GMC_DATATYPE_INT64, &f33Value, sizeof(int64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
}

void TestGeneralT1SetAddBigObject_Root(GmcNodeT *node, char bigstring)
{
    int ret = 0;
    char string13k[1024 * 13] = {0};
    memset(string13k, bigstring, sizeof(string13k));
    string13k[1024 * 13 - 1] = '\0';
    ret = GmcNodeSetPropertyByName(node, (char *)"F23", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F24", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F25", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F26", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F27", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F28", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F29", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F30", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F31", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F32", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F33", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F34", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F35", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F36", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F37", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F38", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F39", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F40", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F41", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F42", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F43", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F44", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F45", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F46", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F47", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F48", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F49", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F50", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F51", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F52", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F53", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F54", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F55", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F56", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F57", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F58", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F59", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F60", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F61", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F62", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F63", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F64", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F65", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F66", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F67", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F68", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F69", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F70", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F71", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F72", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F73", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F74", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F75", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F76", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F77", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F78", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F79", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F80", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F81", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F82", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F83", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F84", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F85", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F86", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F87", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F88", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F89", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F90", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F91", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F92", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F93", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F94", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F95", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F96", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F97", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F98", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F99", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F100", GMC_DATATYPE_FIXED, string13k, 13312);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestGeneralT1ldVersionSetCommonProperty_T1_V(GmcNodeT *node, int64_t i)
{
    int ret = 0;
    int64_t f0Value = i;
    uint64_t f1Value = i;

    ret = GmcNodeSetPropertyByName(node, (char *)"F0", GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char f2Value[12] = "string";
    ret = GmcNodeSetPropertyByName(node, (char *)"F2", GMC_DATATYPE_STRING, f2Value, strlen(f2Value));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char f3Value[12] = "fixed";
    ret = GmcNodeSetPropertyByName(node, (char *)"F3", GMC_DATATYPE_FIXED, f3Value, 9);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char f4Value[10] = "bytes";
    ret = GmcNodeSetPropertyByName(node, (char *)"F4", GMC_DATATYPE_BYTES, f4Value, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestGeneralT1ldVersionSetCommonProperty_T2_V(GmcNodeT *node, int64_t i)
{
    int ret = 0;
    int64_t f0Value = i;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    uint32_t f3Value = i;

    ret = GmcNodeSetPropertyByName(node, (char *)"F0", GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F3", GMC_DATATYPE_UINT32, &f3Value, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char f4Value[12] = "string";
    ret = GmcNodeSetPropertyByName(node, (char *)"F4", GMC_DATATYPE_STRING, f4Value, strlen(f4Value));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char f5Value[12] = "fixed";
    ret = GmcNodeSetPropertyByName(node, (char *)"F5", GMC_DATATYPE_FIXED, &f5Value, 7);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestGeneralT1InsertOrReplace(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue, char *string,
    int32_t schemaVersion, GmcOperationTypeE operationType, bool isDefaultValue = true, char bigstring = 'a')
{
    int ret = 0;
    uint8_t wrFixed[16] = {
        0xcd, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20, 0x20};
    for (int i = startValue; i < endValue; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *root, *T1, *T2;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        // 设置主键
        TestGeneralT1SetPk(root, i);
        // 设置根节点公共属性
        TestGeneralSetCommonProperty_Root(root, i, string, wrFixed, isDefaultValue);

        // InsertOrReplace设置新增字段属性
        if (schemaVersion == 1) {
            TestGeneralT1ldVersionSetAddStringPropertyV1_Root(root, string);
        } else if (schemaVersion == 2) {
            TestGeneralT1ldVersionSetAllFieldsProperty_Root(root, i);
        } else if (schemaVersion == 3) {
            TestGeneralT1SetAddBigObject_Root(root, bigstring);
        }
        // 插入vectoryT1
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(T1, &T1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionSetCommonProperty_T1_V(T1, i);
        // 插入vectoryT2
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(T2, &T2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionSetCommonProperty_T2_V(T2, i);

        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(stmt, 1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestGeneralT1ReplaceUpdate(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue, char *string,
    int32_t schemaVersion, GmcOperationTypeE operationType, bool isDefaultValue = true, char bigstring = 'a')
{
    int ret = 0;
    uint8_t wrFixed[16] = {
        0xcd, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20, 0x20};
    for (int i = startValue; i < endValue; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *root, *T1, *T2;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        // 设置主键
        TestGeneralT1SetPk(root, i);
        // 设置根节点公共属性
        TestGeneralSetCommonProperty_Root(root, i, string, wrFixed, isDefaultValue);

        // InsertOrReplace设置新增字段属性
        if (schemaVersion == 1) {
            TestGeneralT1ldVersionSetAddStringPropertyV1_Root(root, string);
        } else if (schemaVersion == 2) {
            TestGeneralT1ldVersionSetAllFieldsProperty_Root(root, i);
        } else if (schemaVersion == 3) {
            TestGeneralT1SetAddBigObject_Root(root, bigstring);
        }

        // 插入vectoryT1
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(T1, &T1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionSetCommonProperty_T1_V(T1, i);
        // 插入vectoryT2
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(T2, &T2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionSetCommonProperty_T2_V(T2, i);

        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(stmt, 2);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestGeneralT1Merge(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue, char *string,
    int32_t schemaVersion, GmcOperationTypeE operationType, bool isDefaultValue = true, char bigstring = 'a')
{
    uint8_t wrFixed[16] = {
        0xcd, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20, 0x20};
    for (int i = startValue; i < endValue; i++) {
        int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        TestGeneralT1PkIndexSet(stmt, i);
        GmcNodeT *root, *T1, *T2;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1SetPk(root, i);
        // Merge设置公共部分属性
        TestGeneralSetCommonProperty_Root(root, i, string, wrFixed, isDefaultValue);

        // Merge设置新增字段属性
        if (schemaVersion == 1) {
            TestGeneralT1ldVersionSetAddStringPropertyV1_Root(root, string);
        } else if (schemaVersion == 2) {
            TestGeneralT1ldVersionSetAllFieldsProperty_Root(root, i);
        } else if (schemaVersion == 3) {
            TestGeneralT1SetAddBigObject_Root(root, bigstring);
        }

        // 插入vectoryT1
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(T1, &T1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionSetCommonProperty_T1_V(T1, i);
        // 插入vectoryT2
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(T2, &T2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionSetCommonProperty_T2_V(T2, i);

        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(stmt, 1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestGeneralT1MergeUpdate(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue, char *string,
    int32_t schemaVersion, GmcOperationTypeE operationType, bool isDefaultValue = true, int64_t updateValue = 0,
    char bigstring = 'a')
{
    for (int i = startValue; i < endValue; i++) {
        int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        TestGeneralT1PkIndexSet(stmt, i);
        GmcNodeT *root, *T1, *T2;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);

        // MergeUpdate设置公共部分属性
        TestGeneralUpdateSetCommonProperty_Root(root, i + updateValue, string, isDefaultValue);

        // Merge设置新增字段属性
        if (schemaVersion == 1) {
            TestGeneralT1ldVersionSetAddStringPropertyV1_Root(root, string);
        } else if (schemaVersion == 2) {
            TestGeneralT1ldVersionSetAllFieldsProperty_Root(root, i + updateValue);
        } else if (schemaVersion == 3) {
            TestGeneralT1SetAddBigObject_Root(root, bigstring);
        }

        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(stmt, 2);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestGeneralT1GetCommonProperty_Root(GmcNodeT *node, int64_t i, char *string, bool isDefaultValue = true)
{
    int ret = 0;
    bool isNull;
    int64_t f0Value = i;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    uint32_t f3Value = i;
    int16_t f4Value = i % 32768;
    uint16_t f5Value = i % 65536;
    uint8_t f6Value = i;
    uint64_t f7Value = i;

    uint32_t vrid = 0;
    uint32_t vrfIndex = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = i;
    uint8_t f13Value = i & 0xf;

    uint64_t f1Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F1", &f1Value2, sizeof(uint64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f1Value, f1Value2);

    int32_t f2Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F2", &f2Value2, sizeof(int32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f2Value, f2Value2);

    uint32_t f3Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F3", &f3Value2, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f3Value, f3Value2);

    int16_t f4Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F4", &f4Value2, sizeof(int16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f4Value, f4Value2);

    uint16_t f5Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F5", &f5Value2, sizeof(uint16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f5Value, f5Value2);

    uint64_t f7Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F7", &f7Value2, sizeof(uint64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f7Value, f7Value2);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"F9", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(string) + 1);
    char f9Value2[20] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F9", &f9Value2, propSize, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string, f9Value2), 0);

    if (!isDefaultValue) {
        uint8_t f9Value = i % 31;
        uint16_t f10Value = i % 1023;

        uint8_t f9Value2;
        ret = GmcNodeGetPropertyByName(node, (char *)"F10", &f9Value, sizeof(uint8_t), &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ASSERT_EQ((unsigned int)0, isNull);
        AW_MACRO_ASSERT_EQ_INT(f9Value, f9Value2);

        uint16_t f10Value2;
        ret = GmcNodeGetPropertyByName(node, (char *)"F11", &f10Value, sizeof(uint16_t), &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ASSERT_EQ((unsigned int)0, isNull);
        AW_MACRO_ASSERT_EQ_INT(f10Value, f10Value2);
    }
}

void TestGeneralT1GetLpm6Property(GmcNodeT *node, int64_t i)
{
    int ret = 0;
    bool isNull;
    uint32_t vrid = 0;
    uint32_t vrfIndex = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = i;

    uint32_t vrid2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F12", &vrid2, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(vrid, vrid2);

    uint32_t vrfIndex2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F13", &vrfIndex2, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(vrfIndex, vrfIndex2);

    uint8_t wr_fixed[16] = {
        0xcd, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20, 0x20};

    ret = GmcNodeGetPropertyByName(node, (char *)"F8", wr_fixed, 16, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t maskLen2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F6", &maskLen2, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(maskLen, maskLen2);
}

void TestGeneralT1GetAddStringProperty_Root(GmcNodeT *node, char *string)
{
    int ret = 0;
    unsigned int propSize;
    bool isNull;

    ret = GmcNodeGetPropertySizeByName(node, (char *)"F14", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(string) + 1);
    char f14Value2[20] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F14", &f14Value2, propSize, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string, f14Value2), 0);
}

void TestGeneralT1GetAllField_Root(GmcNodeT *node, int64_t i)
{
    int ret = 0;
    bool isNull;
    int64_t Value = i;

    int64_t f14Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F14", &f14Value2, sizeof(int64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(Value, f14Value2);

    uint64_t f15Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F15", &f15Value2, sizeof(uint64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(Value, f15Value2);

    int32_t f16Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F16", &f16Value2, sizeof(int32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(Value, f16Value2);

    uint32_t f17Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F17", &f17Value2, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(Value, f17Value2);

    int16_t f18Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F18", &f18Value2, sizeof(int16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(Value, f18Value2);

    uint16_t f19Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F19", &f19Value2, sizeof(uint16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(Value, f19Value2);

    int8_t f20Value2;
    int8_t f20Value = i % 128;
    ret = GmcNodeGetPropertyByName(node, (char *)"F20", &f20Value2, sizeof(int8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f20Value, f20Value2);

    uint8_t f21Value2;
    uint8_t f21Value = i % 256;
    ret = GmcNodeGetPropertyByName(node, (char *)"F21", &f21Value2, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f21Value, f21Value2);

    uint64_t f22Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F22", &f22Value2, sizeof(uint64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(Value, f22Value2);

    unsigned int propSize;
    char f23Value[8] = "fixed";
    ret = GmcNodeGetPropertySizeByName(node, (char *)"F23", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(8, propSize);
    char f23Value2[20] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F23", &f23Value2, propSize, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(strcmp(f23Value, f23Value2), 0);

    propSize = 0;
    char f24Value[20] = "string";
    ret = GmcNodeGetPropertySizeByName(node, (char *)"F24", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(f24Value) + 1);
    char f24Value2[20] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F24", &f24Value2, propSize, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(strcmp(f24Value, f24Value2), 0);

    uint8_t f25Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F25", &f25Value2, sizeof(f25Value2), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(Value & 0x1f, f25Value2);

    uint16_t f26Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F26", &f26Value2, sizeof(f26Value2), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(Value & 0x1ffff, f26Value2);

    uint32_t f27Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F27", &f27Value2, sizeof(f27Value2), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(Value & 0x1ffff, f27Value2);

    uint64_t f28Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F28", &f28Value2, sizeof(f28Value2), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(Value & 0x1ffffffff, f28Value2);

    bool f29Value = true;
    bool f29Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F29", &f29Value2, sizeof(f29Value2), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f29Value, f29Value2);

    float f30Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F30", &f30Value2, sizeof(f30Value2), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(Value, f30Value2);

    double f31Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F31", &f31Value2, sizeof(f31Value2), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(Value, f31Value2);

    uint8_t f32Bits[1] = {0xff};
    ret = GmcNodeGetPropertyByName(node, (char *)"F32", f32Bits, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
}

void TestGeneralT1GetAllFieldV2_Root(GmcNodeT *node, int64_t i)
{
    int ret = 0;
    bool isNull;
    int64_t Value = i;

    int64_t f14Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F14", &f14Value2, sizeof(int64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(Value, f14Value2);

    uint64_t f15Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F15", &f15Value2, sizeof(uint64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(Value, f15Value2);

    int32_t f16Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F16", &f16Value2, sizeof(int32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(Value, f16Value2);

    uint32_t f17Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F17", &f17Value2, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(Value, f17Value2);

    int16_t f18Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F18", &f18Value2, sizeof(int16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(Value, f18Value2);

    uint16_t f19Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F19", &f19Value2, sizeof(uint16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(Value, f19Value2);

    int8_t f20Value2;
    int8_t f20Value = i % 128;
    ret = GmcNodeGetPropertyByName(node, (char *)"F20", &f20Value2, sizeof(int8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f20Value, f20Value2);

    uint8_t f21Value2;
    uint8_t f21Value = i % 256;
    ret = GmcNodeGetPropertyByName(node, (char *)"F21", &f21Value2, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f21Value, f21Value2);

    uint64_t f22Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F22", &f22Value2, sizeof(uint64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(Value, f22Value2);

    unsigned int propSize;
    char f23Value[8] = "fixed";
    ret = GmcNodeGetPropertySizeByName(node, (char *)"F23", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(8, propSize);
    char f23Value2[20] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F23", &f23Value2, propSize, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(strcmp(f23Value, f23Value2), 0);

    propSize = 0;
    char f24Value[20] = "string";
    ret = GmcNodeGetPropertySizeByName(node, (char *)"F24", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(f24Value) + 1);
    char f24Value2[20] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F24", &f24Value2, propSize, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(strcmp(f24Value, f24Value2), 0);

    uint8_t f25Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F25", &f25Value2, sizeof(f25Value2), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(Value & 0x1f, f25Value2);

    uint16_t f26Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F26", &f26Value2, sizeof(f26Value2), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(Value & 0x1ffff, f26Value2);

    uint32_t f27Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F27", &f27Value2, sizeof(f27Value2), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(Value & 0x1ffff, f27Value2);

    uint64_t f28Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F28", &f28Value2, sizeof(f28Value2), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(Value & 0x1ffffffff, f28Value2);

    bool f29Value = true;
    bool f29Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F29", &f29Value2, sizeof(f29Value2), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f29Value, f29Value2);

    float f30Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F30", &f30Value2, sizeof(f30Value2), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(Value, f30Value2);

    double f31Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F31", &f31Value2, sizeof(f31Value2), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(Value, f31Value2);

    uint8_t f32Bits[1] = {0xff};
    ret = GmcNodeGetPropertyByName(node, (char *)"F32", f32Bits, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);

    int64_t f33Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F33", &f33Value2, sizeof(int64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(Value, f33Value2);
}

void TestGeneralT1GetAllFieldV1NULL_Root(GmcNodeT *node)
{
    int ret = 0;
    bool isNull;
    int64_t Value = 0;

    int64_t f14Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F14", &f14Value2, sizeof(int64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ(true, isNull);

    uint64_t f15Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F15", &f15Value2, sizeof(uint64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ(true, isNull);

    int32_t f16Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F16", &f16Value2, sizeof(int32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ(true, isNull);

    uint32_t f17Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F17", &f17Value2, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ(true, isNull);

    int16_t f18Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F18", &f18Value2, sizeof(int16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ(true, isNull);

    uint16_t f19Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F19", &f19Value2, sizeof(uint16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ(true, isNull);

    int8_t f20Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F20", &f20Value2, sizeof(int8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ(true, isNull);

    uint8_t f21Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F21", &f21Value2, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ(true, isNull);

    uint64_t f22Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F22", &f22Value2, sizeof(uint64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ(true, isNull);

    unsigned int propSize;
    char f23Value[8] = "fixed";
    ret = GmcNodeGetPropertySizeByName(node, (char *)"F23", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(8, propSize);
    char f23Value2[20] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F23", &f23Value2, propSize, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ(true, isNull);

    propSize = 0;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"F24", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(0, propSize);
    char f24Value2[20] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F24", &f24Value2, propSize, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ(true, isNull);

    uint8_t f25Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F25", &f25Value2, sizeof(f25Value2), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ(true, isNull);

    uint16_t f26Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F26", &f26Value2, sizeof(f26Value2), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ(true, isNull);

    uint32_t f27Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F27", &f27Value2, sizeof(f27Value2), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ(true, isNull);

    uint64_t f28Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F28", &f28Value2, sizeof(f28Value2), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ(true, isNull);

    bool f29Value = true;
    bool f29Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F29", &f29Value2, sizeof(f29Value2), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ(true, isNull);

    float f30Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F30", &f30Value2, sizeof(f30Value2), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ(true, isNull);

    double f31Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F31", &f31Value2, sizeof(f31Value2), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ(true, isNull);

    uint8_t f32Bits[1] = {0xff};
    ret = GmcNodeGetPropertyByName(node, (char *)"F32", f32Bits, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ(false, isNull);

    int64_t f33Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F33", &f33Value2, sizeof(int64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ(true, isNull);
}

void TestGeneralT1GetAllFieldF33MultipleVersion_Root(GmcNodeT *node)
{
    int ret = 0;
    bool isNull;
    int64_t f33Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F33", &f33Value2, sizeof(int64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ(true, isNull);
}

void TestGeneralT1ldVersionGetAllFieldsPropertyV2Failed_Root(GmcNodeT *node)
{
    int ret = 0;
    bool isNull;
    int64_t f33Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F33", &f33Value2, sizeof(int64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
}

void TestGeneralT1GetAllFieldFail_Root(GmcNodeT *node, int64_t i)
{
    int ret = 0;
    bool isNull;
    int64_t Value = i;

    int64_t f14Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F14", &f14Value2, sizeof(int64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint64_t f15Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F15", &f15Value2, sizeof(uint64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    int32_t f16Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F16", &f16Value2, sizeof(int32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint32_t f17Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F17", &f17Value2, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    int16_t f18Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F18", &f18Value2, sizeof(int16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint16_t f19Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F19", &f19Value2, sizeof(uint16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    int8_t f20Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F20", &f20Value2, sizeof(int8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint8_t f21Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F21", &f21Value2, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint64_t f22Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F22", &f22Value2, sizeof(uint64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    char f23Value2[20] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F23", &f23Value2, 8, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    char f24Value2[20] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F24", &f24Value2, 20, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint8_t f25Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F25", &f25Value2, sizeof(f25Value2), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint16_t f26Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F26", &f26Value2, sizeof(f26Value2), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint32_t f27Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F27", &f27Value2, sizeof(f27Value2), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint64_t f28Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F28", &f28Value2, sizeof(f28Value2), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    bool f29Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F29", &f29Value2, sizeof(f29Value2), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    float f30Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F30", &f30Value2, sizeof(f30Value2), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    double f31Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F31", &f31Value2, sizeof(f31Value2), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint8_t f32Bits[1] = {0xff};
    ret = GmcNodeGetPropertyByName(node, (char *)"F32", f32Bits, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    int64_t f33Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F33", &f33Value2, sizeof(int64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
}

void TestGeneralT1GetUpdateBigObject_Root(GmcNodeT *node, char bigstring)
{
    int ret = 0;
    bool isNull;
    char string13k[1024 * 13] = {0};
    memset(string13k, bigstring, sizeof(string13k));
    string13k[1024 * 13 - 1] = '\0';

    char f23Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F23", f23Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f23Value2), 0);
    char f24Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F24", f24Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f24Value2), 0);
    char f25Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F25", f25Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f25Value2), 0);
    char f26Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F26", f26Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f26Value2), 0);
    char f27Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F27", f27Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f27Value2), 0);
    char f28Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F28", f28Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f28Value2), 0);
    char f29Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F29", f29Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f29Value2), 0);
    char f30Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F30", f30Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f30Value2), 0);
    char f31Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F31", f31Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f31Value2), 0);
    char f32Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F32", f32Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f32Value2), 0);
    char f33Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F33", f33Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f33Value2), 0);
    char f34Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F34", f34Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f34Value2), 0);
    char f35Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F35", f35Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f35Value2), 0);
    char f36Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F36", f36Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f36Value2), 0);
    char f37Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F37", f37Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f37Value2), 0);
    char f38Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F38", f38Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f38Value2), 0);
    char f39Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F39", f39Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f39Value2), 0);
    char f40Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F40", f40Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f40Value2), 0);
    char f41Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F41", f41Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f40Value2), 0);
    char f42Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F42", f42Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f42Value2), 0);
    char f43Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F43", f43Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f43Value2), 0);
    char f44Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F44", f44Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f44Value2), 0);
    char f45Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F45", f45Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f45Value2), 0);
    char f46Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F46", f46Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f46Value2), 0);
    char f47Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F47", f47Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f47Value2), 0);
    char f48Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F48", f48Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f48Value2), 0);
    char f49Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F49", f49Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f49Value2), 0);
    char f50Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F50", f50Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f50Value2), 0);
    char f51Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F51", f51Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f51Value2), 0);
    char f52Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F52", f52Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f52Value2), 0);
    char f53Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F53", f53Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f53Value2), 0);
    char f54Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F54", f54Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f54Value2), 0);
    char f55Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F55", f55Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f55Value2), 0);
    char f56Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F56", f56Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f56Value2), 0);
    char f57Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F57", f57Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f57Value2), 0);
    char f58Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F58", f58Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f58Value2), 0);
    char f59Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F59", f59Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f59Value2), 0);
    char f60Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F60", f60Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f60Value2), 0);
    char f61Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F61", f61Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f61Value2), 0);
    char f62Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F62", f62Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f62Value2), 0);
    char f63Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F63", f63Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f63Value2), 0);
    char f64Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F64", f64Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f64Value2), 0);
    char f65Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F65", f65Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f65Value2), 0);
    char f66Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F66", f66Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f66Value2), 0);
    char f67Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F67", f67Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f67Value2), 0);
    char f68Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F68", f68Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f68Value2), 0);
    char f69Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F69", f69Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f69Value2), 0);
    char f70Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F70", f70Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f70Value2), 0);
    char f71Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F71", f71Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f71Value2), 0);
    char f72Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F72", f72Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f72Value2), 0);
    char f73Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F73", f73Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f73Value2), 0);
    char f74Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F74", f74Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f74Value2), 0);
    char f75Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F75", f75Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f75Value2), 0);
    char f76Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F76", f76Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f76Value2), 0);
    char f77Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F77", f77Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f77Value2), 0);
    char f78Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F78", f78Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f78Value2), 0);
    char f79Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F79", f79Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f79Value2), 0);
    char f80Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F80", f80Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f80Value2), 0);
    char f81Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F81", f81Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f81Value2), 0);
    char f82Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F82", f82Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f82Value2), 0);
    char f83Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F83", f83Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f83Value2), 0);
    char f84Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F84", f84Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f84Value2), 0);
    char f85Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F85", f85Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f85Value2), 0);
    char f86Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F86", f86Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f86Value2), 0);
    char f87Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F87", f87Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f87Value2), 0);
    char f88Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F88", f88Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f88Value2), 0);
    char f89Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F89", f89Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f89Value2), 0);
    char f90Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F90", f90Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f90Value2), 0);
    char f91Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F91", f91Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f91Value2), 0);
    char f92Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F92", f92Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f92Value2), 0);
    char f93Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F93", f93Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f93Value2), 0);
    char f94Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F94", f94Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f94Value2), 0);
    char f95Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F95", f95Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f95Value2), 0);
    char f96Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F96", f96Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f96Value2), 0);
    char f97Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F97", f97Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f97Value2), 0);
    char f98Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F98", f98Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f98Value2), 0);
    char f99Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F99", f99Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f99Value2), 0);
    char f100Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F100", f100Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f100Value2), 0);
}

void TestGeneralT1ldVersionGetCommonProperty_T1_V(GmcNodeT *node, int64_t i)
{
    int ret = 0;
    bool isNull;
    unsigned int propSize;
    int64_t f0Value = i;
    uint64_t f1Value = i;

    int64_t f0Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F0", &f0Value2, sizeof(int64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f0Value, f0Value2);

    uint64_t f1Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F1", &f1Value2, sizeof(uint64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f1Value, f1Value2);

    char f2Value[100] = "string";
    char f2Value2[100] = {0};
    ret = GmcNodeGetPropertySizeByName(node, (char *)"F2", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(f2Value) + 1);
    ret = GmcNodeGetPropertyByName(node, (char *)"F2", &f2Value2, propSize, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(f2Value, f2Value2), 0);

    char f3Value[9] = "fixed";
    char f3Value2[9] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F3", &f3Value2, 9, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(f3Value, f3Value2), 0);

    char f4Value[10] = "bytes";
    char f4Value2[11] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F4", &f4Value2, 10, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(f4Value, f4Value2), 0);
}

void TestGeneralT1ldVersionGetCommonProperty_T2_V(GmcNodeT *node, int64_t i)
{
    int ret = 0;
    bool isNull;
    unsigned int propSize;
    int64_t f0Value = i;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    uint32_t f3Value = i;

    int64_t f0Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F0", &f0Value2, sizeof(int64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f0Value, f0Value2);

    uint64_t f1Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F1", &f1Value2, sizeof(uint64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f1Value, f1Value2);

    int32_t f2Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F2", &f2Value2, sizeof(int32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f2Value, f2Value2);

    uint32_t f3Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F3", &f3Value2, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f3Value, f3Value2);

    char f4Value[20] = "string";
    char f4Value2[100] = {0};
    ret = GmcNodeGetPropertySizeByName(node, (char *)"F4", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(f4Value) + 1);
    ret = GmcNodeGetPropertyByName(node, (char *)"F4", &f4Value2, propSize, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(f4Value, f4Value2), 0);

    char f5Value[7] = "fixed";
    char f5Value2[7] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F5", &f5Value2, 7, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(f5Value, f5Value2), 0);
}

// delete start
void TestGeneralT1PkDelete(
    GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue, int32_t schemaVersion, bool isSn = false)
{
    int userDataIdx = 0;
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = startValue; i < endValue; i++) {
        if (isSn == true) {
            ((int *)(user_data->old_value))[userDataIdx] = i;
            userDataIdx++;
        }
        TestGeneralT1PkIndexSet(stmt, i);
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestGeneralT1LocalhashDelete(
    GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue, int32_t schemaVersion)
{
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = startValue; i < endValue; i++) {
        TestGeneralT1LocalhashIndexSet(stmt, i);
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}
// delete end

void TestGeneralT1Update(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue, char *string,
    int32_t schemaVersion, GmcOperationTypeE operationType, bool isDefaultValue = true, int64_t updateValue = 0,
    char bigstring = 'a')
{
    int ret = 0;
    for (int i = startValue; i < endValue; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        TestGeneralT1PkIndexSet(stmt, i);
        GmcNodeT *root, *T1, *T2;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        // 设置根节点公共部分属性
        TestGeneralUpdateSetCommonProperty_Root(root, i + updateValue, string, isDefaultValue);
        // Update设置新增字段属性
        if (schemaVersion == 1) {
            TestGeneralT1ldVersionSetAddStringPropertyV1_Root(root, string);
        } else if (schemaVersion == 2) {
            TestGeneralT1ldVersionSetAllFieldsProperty_Root(root, i + updateValue);
        } else if (schemaVersion == 3) {
            TestGeneralT1SetAddBigObject_Root(root, bigstring);
        }

        // 更新vectoryT1
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(T1, &T1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionSetCommonProperty_T1_V(T1, i + updateValue);
        // 更新vectoryT2
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(T2, &T2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionSetCommonProperty_T2_V(T2, i + updateValue);

        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(stmt, 1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

// 批量start
void TestGeneralT1BatchWriteVertex(GmcConnT *conn, GmcStmtT *stmt, GtSimplelabelCfgT vertexCfg, char *string,
    int32_t schemaVersion = 1, bool isDefaultValue = true, int64_t updateValue = 0, char bigstring = 'a')
{
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t coefficient = vertexCfg.coefficient;
    GmcOperationTypeE optType = vertexCfg.optType;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    uint8_t wrFixed[16] = {
        0xcd, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20, 0x20};
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    GmcBatchOptionT batchOption;
    int ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int32_t batchCount = 0;
    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, schemaVersion, optType);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (optType == GMC_OPERATION_MERGE) {
            TestGeneralT1PkIndexSet(stmt, i);
        }
        // 设置所有根节点属性
        GmcNodeT *root, *T1, *T2;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        // 设置主键
        TestGeneralT1SetPk(root, i);
        // 设置公共部分属性
        TestGeneralSetCommonProperty_Root(root, i, string, wrFixed, isDefaultValue);

        // batch设置升级属性
        if (schemaVersion == 1) {
            TestGeneralT1ldVersionSetAddStringPropertyV1_Root(root, string);
        } else if (schemaVersion == 2) {
            TestGeneralT1ldVersionSetAllFieldsProperty_Root(root, i);
        } else if (schemaVersion == 3) {
            TestGeneralT1SetAddBigObject_Root(root, bigstring);
        }

        // 插入vectoryT1
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(T1, &T1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionSetCommonProperty_T1_V(T1, i);
        // 插入vectoryT2
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(T2, &T2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionSetCommonProperty_T2_V(T2, i);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        batchCount++;

        if (batchCount != vertexCount && i != startPkVal + vertexCount - 1) {
            continue;
        }
        ret = GmcBatchExecute(batch, &batchRet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        AW_FUN_Log(LOG_INFO, "successNum: %d\n", successNum);
        AW_FUN_Log(LOG_INFO, "totalNum: %d\n", totalNum);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        batchCount = 0;
    }
    GmcBatchDestroy(batch);
}

int TestGeneralT1BatchUpdateVertex(GmcConnT *conn, GmcStmtT *stmt, GtSimplelabelCfgT vertexCfg, char *string,
    int32_t schemaVersion = 1, bool isDefaultValue = true, int64_t updateValue = 0, char bigstring = 'a')
{
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t coefficient = vertexCfg.coefficient;
    GmcOperationTypeE optType = vertexCfg.optType;

    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    int ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, schemaVersion, optType);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGeneralT1PkIndexSet(stmt, i);
        // 设置所有根节点属性
        GmcNodeT *root, *T1, *T2;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        // 设置公共部分属性
        TestGeneralUpdateSetCommonProperty_Root(root, i + updateValue, string, isDefaultValue);

        // batch_update设置新增属性
        if (schemaVersion == 1) {
            TestGeneralT1ldVersionSetAddStringPropertyV1_Root(root, string);
        } else if (schemaVersion == 2) {
            TestGeneralT1ldVersionSetAllFieldsProperty_Root(root, i + updateValue);
        } else if (schemaVersion == 3) {
            TestGeneralT1SetAddBigObject_Root(root, bigstring);
        }

        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchExecute(batch, &batchRet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        AW_MACRO_EXPECT_EQ_INT(1, successNum);
        AW_MACRO_EXPECT_EQ_INT(1, totalNum);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    GmcBatchDestroy(batch);
    return ret;
}
// 批量end

// 插入查询start
void TestGeneralT1PkScan(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue, char *string,
    int32_t schemaVersion, bool isDefaultValue = true, int64_t updateValue = 0, char bigstring = 'a')
{
    bool isFinish = true;
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = startValue; i < endValue; i++) {
        TestGeneralT1PkIndexSet(stmt, i);
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *root, *T1, *T2;
        // 查询根节点
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        // 查询根节点公共部分
        TestGeneralT1GetCommonProperty_Root(root, i + updateValue, string, isDefaultValue);
        // 查询根节点LMP6索引
        TestGeneralT1GetLpm6Property(root, i);

        // Pk查询升级后新增字段
        if (schemaVersion == 1) {
            TestGeneralT1GetAddStringProperty_Root(root, string);
        } else if (schemaVersion == 2) {
            TestGeneralT1GetAllField_Root(root, i + updateValue);
        } else if (schemaVersion == 3) {
            TestGeneralT1GetUpdateBigObject_Root(root, bigstring);
        }

        // 查询vectoryT1
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionGetCommonProperty_T1_V(T1, i);
        // 查询vectoryT2
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionGetCommonProperty_T2_V(T2, i);
    }
}

void TestGeneralT1LocalhashScan(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue, char *string,
    int32_t schemaVersion, bool isDefaultValue = true, int64_t updateValue = 0, char bigstring = 'a')
{
    bool isFinish = true;
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = startValue; i < endValue; i++) {
        TestGeneralT1LocalhashIndexSet(stmt, i + updateValue);
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *root, *T1, *T2;
        // 查询根节点
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        // 查询根节点公共部分
        TestGeneralT1GetCommonProperty_Root(root, i + updateValue, string, isDefaultValue);
        // 查询根节点LMP6索引
        TestGeneralT1GetLpm6Property(root, i);

        // Localhash查询升级后新增字段
        if (schemaVersion == 1) {
            TestGeneralT1GetAddStringProperty_Root(root, string);
        } else if (schemaVersion == 2) {
            TestGeneralT1GetAllField_Root(root, i + updateValue);
        } else if (schemaVersion == 3) {
            TestGeneralT1GetUpdateBigObject_Root(root, bigstring);
        }

        // 查询vectoryT1
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionGetCommonProperty_T1_V(T1, i);
        // 查询vectoryT2
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionGetCommonProperty_T2_V(T2, i);

        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(true, isFinish);
    }
}

void TestGeneralT1HashclusterScan(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue, char *string,
    int32_t schemaVersion, bool isDefaultValue = true, int64_t updateValue = 0, char bigstring = 'a')
{
    bool isFinish = true;
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = startValue; i < endValue; i++) {
        TestGeneralHashclusterIndexSet(stmt, i + updateValue);
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *root, *T1, *T2;
        // 查询根节点
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        // 查询根节点公共部分
        TestGeneralT1GetCommonProperty_Root(root, i + updateValue, string, isDefaultValue);
        // 查询根节点LMP6索引
        TestGeneralT1GetLpm6Property(root, i);

        // Hashcluster查询升级后新增字段
        if (schemaVersion == 1) {
            TestGeneralT1GetAddStringProperty_Root(root, string);
        } else if (schemaVersion == 2) {
            TestGeneralT1GetAllField_Root(root, i + updateValue);
        } else if (schemaVersion == 3) {
            TestGeneralT1GetUpdateBigObject_Root(root, bigstring);
        }

        // 查询vectoryT1
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionGetCommonProperty_T1_V(T1, i);
        // 查询vectoryT2
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionGetCommonProperty_T2_V(T2, i);

        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(true, isFinish);
    }
}

void TestGeneralT1LocalScan(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue, char *string,
    int32_t schemaVersion, int32_t *fetchNum, bool isDefaultValue = true, int64_t updateValue = 0, char bigstring = 'a')
{
    bool isFinish = true;
    int64_t f0Value;
    bool isNull = 0;
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    TestGeneralT1LocalIndexRangeSet(stmt, startValue + updateValue, endValue + updateValue);
    ret = GmcExecute(stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isFinish);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    while (!isFinish) {
        (*fetchNum)++;
        GmcNodeT *root, *T1, *T2;
        // 查询根节点
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        // 查询根节点公共部分
        TestGeneralT1GetCommonProperty_Root(root, f0Value + updateValue, string, isDefaultValue);
        // 查询根节点LMP6索引
        TestGeneralT1GetLpm6Property(root, f0Value);

        // Local查询升级后新增字段
        if (schemaVersion == 1) {
            TestGeneralT1GetAddStringProperty_Root(root, string);
        } else if (schemaVersion == 2) {
            TestGeneralT1GetAllField_Root(root, f0Value + updateValue);
        } else if (schemaVersion == 3) {
            TestGeneralT1GetUpdateBigObject_Root(root, bigstring);
        }

        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestGeneralT1LpmScan(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue, char *string,
    int32_t schemaVersion, bool isDefaultValue = true, int64_t updateValue = 0, char bigstring = 'a')
{
    bool isFinish = true;
    int64_t f0Value;
    bool isNull = 0;
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = startValue; i < endValue; i++) {
        uint32_t vrid = 0;
        uint32_t vrfIndex = 0;
        uint8_t destIpAddr[16] = {
            0xcd, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20, 0x20};
        uint8_t maskLen = i;
        ret = GmcSetIndexKeyName(stmt, "lpm6_key");
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vrid, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, destIpAddr, 16);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *root, *T1, *T2;
        // 查询根节点
        ret = GmcGetRootNode(stmt, &root);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        // 查询根节点公共部分
        TestGeneralT1GetCommonProperty_Root(root, f0Value + updateValue, string, isDefaultValue);
        // 查询根节点LMP6索引
        TestGeneralT1GetLpm6Property(root, f0Value);

        // 查询升级后新增字段
        if (schemaVersion == 1) {
            TestGeneralT1GetAddStringProperty_Root(root, string);
        } else if (schemaVersion == 2) {
            TestGeneralT1GetAllField_Root(root, f0Value + updateValue);
        } else if (schemaVersion == 3) {
            TestGeneralT1GetUpdateBigObject_Root(root, bigstring);
        }

        // 查询vectoryT1
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionGetCommonProperty_T1_V(T1, f0Value);
        // 查询vectoryT2
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionGetCommonProperty_T2_V(T2, f0Value);

        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(true, isFinish);
    }
}
// 插入查询end

// 多版本
void TestGeneralT1InsertOrReplaceMultipleVersion(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue,
    char *string, int32_t schemaVersion, GmcOperationTypeE operationType, bool isDefaultValue = true)
{
    int ret = 0;
    uint8_t wrFixed[16] = {
        0xcd, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20, 0x20};
    for (int i = startValue; i < endValue; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *root, *T1, *T2;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        // 设置主键
        TestGeneralT1SetPk(root, i);
        // 设置根节点公共属性
        TestGeneralSetCommonProperty_Root(root, i, string, wrFixed, isDefaultValue);

        // InsertOrReplace设置新增字段属性
        if (schemaVersion == 0) {
            TestGeneralT1ldVersionSetAllFieldsPropertyFailed_Root(root, i);
        } else if (schemaVersion == 1) {
            TestGeneralT1ldVersionSetAllFieldsProperty_Root(root, i);
        } else if (schemaVersion == 2) {
            TestGeneralT1ldVersionSetAllFieldsPropertyV2_Root(root, i);
        }

        // 插入vectoryT1
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(T1, &T1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionSetCommonProperty_T1_V(T1, i);
        // 插入vectoryT2
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(T2, &T2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionSetCommonProperty_T2_V(T2, i);

        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(stmt, 1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestGeneralT1ScanMultipleVersion(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue,
    char *string, int32_t schemaVersion, bool isDefaultValue = true, int64_t updateValue = 0, int64_t keyId = 0)
{
    bool isFinish = true;
    int ret = 0;
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = startValue; i < endValue; i++) {
        if (keyId == 0) {
            TestGeneralT1PkIndexSet(stmt, i);
        } else if (keyId == 1) {
            TestGeneralHashclusterIndexSet(stmt, i);
        } else if (keyId == 2) {
            TestGeneralT1LocalhashIndexSet(stmt, i);
        }
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *root, *T1, *T2;
        // 查询根节点
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        // 查询根节点公共部分
        TestGeneralT1GetCommonProperty_Root(root, i + updateValue, string, isDefaultValue);
        // 查询根节点LMP6索引
        TestGeneralT1GetLpm6Property(root, i);

        if (schemaVersion == 0) {
            TestGeneralT1ldVersionSetAllFieldsPropertyFailed_Root(root, i);
        } else if (schemaVersion == 1) {
            TestGeneralT1ldVersionSetAllFieldsProperty_Root(root, i);
        } else if (schemaVersion == 2) {
            TestGeneralT1GetAllFieldV2_Root(root, i + updateValue);
        }

        // 查询vectoryT1
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionGetCommonProperty_T1_V(T1, i);
        // 查询vectoryT2
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionGetCommonProperty_T2_V(T2, i);
    }
}

// update_start
void TestGeneralT1NewUpdateMiddld(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue, char *string,
    int32_t schemaVersion, GmcOperationTypeE operationType, bool isDefaultValue = true, int64_t updateValue = 0)
{
    int ret = 0;
    for (int i = startValue; i < endValue; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        TestGeneralT1PkIndexSet(stmt, i);
        GmcNodeT *root, *T1, *T2;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        // 设置根节点公共部分属性
        TestGeneralUpdateSetCommonProperty_Root(root, i + updateValue, string, isDefaultValue);

        // Update设置最新版本字段属性
        TestGeneralT1ldVersionSetAllFieldsPropertyV2_Root(root, i + updateValue);
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(stmt, 1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestGeneralT1MiddleVersionUpdateOldData(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue,
    char *string, int32_t schemaVersion, GmcOperationTypeE operationType, bool isDefaultValue = true,
    int64_t updateValue = 0)
{
    int ret = 0;
    for (int i = startValue; i < endValue; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        TestGeneralT1PkIndexSet(stmt, i);
        GmcNodeT *root, *T1, *T2;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        // 设置根节点公共部分属性
        TestGeneralUpdateSetCommonProperty_Root(root, i + updateValue, string, isDefaultValue);

        // Update设置中间版本新增字段属性
        TestGeneralT1ldVersionSetAllFieldsProperty_Root(root, i + updateValue);

        // Update设置最新版本字段失败
        TestGeneralT1ldVersionSetAllFieldsPropertyV2Failed_Root(root, i + updateValue);
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(stmt, 1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestGeneralT1OldOrMiddleVersionUpdateNewData(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue,
    char *string, int32_t schemaVersion, GmcOperationTypeE operationType, bool isDefaultValue = true,
    int64_t updateValue = 0)
{
    int ret = 0;
    for (int i = startValue; i < endValue; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        TestGeneralT1PkIndexSet(stmt, i);
        GmcNodeT *root, *T1, *T2;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        // 设置根节点公共部分属性
        TestGeneralUpdateSetCommonProperty_Root(root, i + updateValue, string, isDefaultValue);

        // Update设置新增字段属性
        if (schemaVersion == 0) {
            TestGeneralT1ldVersionSetAllFieldsPropertyFailed_Root(root, i + updateValue);
        } else if (schemaVersion == 1) {
            // 版本1设置版本2字段失败
            TestGeneralT1ldVersionSetAllFieldsPropertyV2Failed_Root(root, i + updateValue);
            // 设置更新版本1字段
            TestGeneralT1ldVersionSetAllFieldsProperty_Root(root, i + updateValue);
        }
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(stmt, 1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}
// update_end

void TestGeneralT1ScanOldVersionReadNewData(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue,
    char *string, int32_t schemaVersion, bool isDefaultValue = true, int64_t updateValue = 0, int64_t keyId = 0)
{
    bool isFinish = true;
    int ret = 0;
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = startValue; i < endValue; i++) {
        if (keyId == 0) {
            TestGeneralT1PkIndexSet(stmt, i);
        } else if (keyId == 1) {
            TestGeneralHashclusterIndexSet(stmt, i);
        } else if (keyId == 2) {
            TestGeneralT1LocalhashIndexSet(stmt, i);
        }
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *root, *T1, *T2;
        // 查询根节点
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        // 查询根节点公共部分
        TestGeneralT1GetCommonProperty_Root(root, i + updateValue, string, isDefaultValue);
        // 查询根节点LMP6索引
        TestGeneralT1GetLpm6Property(root, i);

        // 旧版本读最新字段数据接口报错
        TestGeneralT1GetAllFieldFail_Root(root, i + updateValue);

        // 查询vectoryT1
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionGetCommonProperty_T1_V(T1, i);
        // 查询vectoryT2
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionGetCommonProperty_T2_V(T2, i);
    }
}

void TestGeneralT1ScanMiddleVersionReadNewData(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue,
    char *string, int32_t schemaVersion, bool isDefaultValue = true, int64_t updateValue = 0, int64_t keyId = 0)
{
    bool isFinish = true;
    int ret = 0;
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = startValue; i < endValue; i++) {
        if (keyId == 0) {
            TestGeneralT1PkIndexSet(stmt, i);
        } else if (keyId == 1) {
            TestGeneralHashclusterIndexSet(stmt, i);
        } else if (keyId == 2) {
            TestGeneralT1LocalhashIndexSet(stmt, i);
        }
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *root, *T1, *T2;
        // 查询根节点
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        // 查询根节点公共部分
        TestGeneralT1GetCommonProperty_Root(root, i + updateValue, string, isDefaultValue);
        // 查询根节点LMP6索引
        TestGeneralT1GetLpm6Property(root, i);

        // 读取中间版本升级字段
        TestGeneralT1GetAllField_Root(root, i + updateValue);
        // 中间版本读最新字段数据接口报错
        TestGeneralT1ldVersionGetAllFieldsPropertyV2Failed_Root(root);

        // 查询vectoryT1
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionGetCommonProperty_T1_V(T1, i);
        // 查询vectoryT2
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionGetCommonProperty_T2_V(T2, i);
    }
}

void TestGeneralT1ScanNewVersionReadOldData(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue,
    char *string, int32_t schemaVersion, bool isDefaultValue = true, int64_t updateValue = 0, int64_t keyId = 0)
{
    bool isFinish = true;
    int ret = 0;
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = startValue; i < endValue; i++) {
        if (keyId == 0) {
            TestGeneralT1PkIndexSet(stmt, i);
        } else if (keyId == 1) {
            TestGeneralHashclusterIndexSet(stmt, i);
        } else if (keyId == 2) {
            TestGeneralT1LocalhashIndexSet(stmt, i);
        }
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *root, *T1, *T2;
        // 查询根节点
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        // 查询根节点公共部分
        TestGeneralT1GetCommonProperty_Root(root, i + updateValue, string, isDefaultValue);
        // 查询根节点LMP6索引
        TestGeneralT1GetLpm6Property(root, i);

        // 新版本读老数据新字段为null
        TestGeneralT1GetAllFieldV1NULL_Root(root);

        // 查询vectoryT1
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionGetCommonProperty_T1_V(T1, i);
        // 查询vectoryT2
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionGetCommonProperty_T2_V(T2, i);
    }
}

void TestGeneralT1ScanNewVersionReadMiddleData(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue,
    char *string, int32_t schemaVersion, bool isDefaultValue = true, int64_t updateValue = 0, int64_t keyId = 0)
{
    bool isFinish = true;
    int ret = 0;
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = startValue; i < endValue; i++) {
        if (keyId == 0) {
            TestGeneralT1PkIndexSet(stmt, i);
        } else if (keyId == 1) {
            TestGeneralHashclusterIndexSet(stmt, i);
        } else if (keyId == 2) {
            TestGeneralT1LocalhashIndexSet(stmt, i);
        }
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *root, *T1, *T2;
        // 查询根节点
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        // 查询根节点公共部分
        TestGeneralT1GetCommonProperty_Root(root, i + updateValue, string, isDefaultValue);
        // 查询根节点LMP6索引
        TestGeneralT1GetLpm6Property(root, i);

        // 读取中间版本升级字段
        TestGeneralT1GetAllField_Root(root, i + updateValue);
        // 中间版本读最新版本字段为空
        TestGeneralT1GetAllFieldF33MultipleVersion_Root(root);

        // 查询vectoryT1
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionGetCommonProperty_T1_V(T1, i);
        // 查询vectoryT2
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionGetCommonProperty_T2_V(T2, i);
    }
}

void TestGeneralT1PkScanMiddleReadOld(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue,
    char *string, int32_t schemaVersion, bool isDefaultValue = true, int64_t updateValue = 0)
{
    bool isFinish = true;
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = startValue; i < endValue; i++) {
        TestGeneralT1PkIndexSet(stmt, i);
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *root, *T1, *T2;
        // 查询根节点
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        // 查询根节点公共部分
        TestGeneralT1GetCommonProperty_Root(root, i + updateValue, string, isDefaultValue);
        // 查询根节点LMP6索引
        TestGeneralT1GetLpm6Property(root, i);

        // Pk查询中间版本更新字段
        TestGeneralT1GetAllField_Root(root, i + updateValue);

        // 查询vectoryT1
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionGetCommonProperty_T1_V(T1, i);
        // 查询vectoryT2
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionGetCommonProperty_T2_V(T2, i);
    }
}

void TestGeneralT1PkScanUpdateNewReadMiddleData(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue,
    char *string, int32_t schemaVersion, bool isDefaultValue = true, int64_t updateValue = 0)
{
    bool isFinish = true;
    int ret = 0;
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = startValue; i < endValue; i++) {
        TestGeneralT1PkIndexSet(stmt, i);
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *root, *T1, *T2;
        // 查询根节点
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        // 查询根节点公共部分
        TestGeneralT1GetCommonProperty_Root(root, i + updateValue, string, isDefaultValue);
        // 查询根节点LMP6索引
        TestGeneralT1GetLpm6Property(root, i);

        // 最新版本更新中间版本读取数据
        TestGeneralT1GetAllFieldV2_Root(root, i + updateValue);

        // 查询vectoryT1
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionGetCommonProperty_T1_V(T1, i);
        // 查询vectoryT2
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionGetCommonProperty_T2_V(T2, i);
    }
}

void TestGeneralT1PkScanMultipleVersionAfterTruncate(GmcStmtT *stmt, char *labelName, int64_t startValue,
    int64_t endValue, char *string, int32_t schemaVersion, bool isDefaultValue = true, int64_t updateValue = 0)
{
    bool isFinish = true;
    int ret = 0;
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = startValue; i < endValue; i++) {
        TestGeneralT1PkIndexSet(stmt, i);
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *root, *T1, *T2;
        // 查询根节点报错
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_NO_DATA, ret);
    }
}

// 订阅
void TestGeneralT1InsertOrReplaceOldSn(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue,
    char *string, int32_t schemaVersion, GmcOperationTypeE operationType, bool isDefaultValue = true)
{
    int ret = 0;
    uint8_t wrFixed[16] = {
        0xcd, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20, 0x20};
    for (int i = startValue; i < endValue; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *root, *T1, *T2;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        // 设置主键
        TestGeneralT1SetPk(root, i);
        // 设置根节点公共属性
        TestGeneralSetCommonProperty_Root(root, i, string, wrFixed, isDefaultValue);

        // InsertOrReplace设置新增字段属性
        if (schemaVersion == 0) {
            TestGeneralT1ldVersionSetAllFieldsPropertyFailed_Root(root, i);
        } else if (schemaVersion == 1) {
            TestGeneralT1ldVersionSetAllFieldsProperty_Root(root, i);
        }

        // 插入vectoryT1
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(T1, &T1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionSetCommonProperty_T1_V(T1, i);
        // 插入vectoryT2
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(T2, &T2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionSetCommonProperty_T2_V(T2, i);

        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(stmt, 1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestGeneralT1MergeSn(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue, char *string,
    int32_t schemaVersion, GmcOperationTypeE operationType, bool isDefaultValue = true)
{
    uint8_t wrFixed[16] = {
        0xcd, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20, 0x20};
    for (int i = startValue; i < endValue; i++) {
        int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        TestGeneralT1PkIndexSet(stmt, i);
        GmcNodeT *root, *T1, *T2;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1SetPk(root, i);
        // Merge设置公共部分属性
        TestGeneralSetCommonProperty_Root(root, i, string, wrFixed, isDefaultValue);

        // InsertOrReplace设置新增字段属性
        if (schemaVersion == 0) {
            TestGeneralT1ldVersionSetAllFieldsPropertyFailed_Root(root, i);
        } else if (schemaVersion == 1) {
            TestGeneralT1ldVersionSetAllFieldsProperty_Root(root, i);
        }

        // 插入vectoryT1
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(T1, &T1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionSetCommonProperty_T1_V(T1, i);
        // 插入vectoryT2
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(T2, &T2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionSetCommonProperty_T2_V(T2, i);

        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(stmt, 1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestGeneralT1UpdateOldSn(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue, char *string,
    int32_t schemaVersion, GmcOperationTypeE operationType, bool isDefaultValue = true, int64_t updateValue = 0)
{
    int ret = 0;
    for (int i = startValue; i < endValue; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        TestGeneralT1PkIndexSet(stmt, i);
        GmcNodeT *root, *T1, *T2;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        // 设置根节点公共部分属性
        TestGeneralUpdateSetCommonProperty_Root(root, i + updateValue, string, isDefaultValue);

        // Update设置新增字段属性
        if (schemaVersion == 0) {
            TestGeneralT1ldVersionSetAllFieldsPropertyFailed_Root(root, i + updateValue);
        } else if (schemaVersion == 1) {
            TestGeneralT1ldVersionSetAllFieldsProperty_Root(root, i + updateValue);
        }

        // 更新vectoryT1
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(T1, &T1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionSetCommonProperty_T1_V(T1, i + updateValue);
        // 更新vectoryT2
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(T2, &T2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionSetCommonProperty_T2_V(T2, i + updateValue);

        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(stmt, 1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestGeneralT1ReplaceUpdateOldSn(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue,
    char *string, int32_t schemaVersion, GmcOperationTypeE operationType, bool isDefaultValue = true)
{
    int ret = 0;
    uint8_t wrFixed[16] = {
        0xcd, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20, 0x20};
    for (int i = startValue; i < endValue; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *root, *T1, *T2;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        // 设置主键
        TestGeneralT1SetPk(root, i);
        // 设置根节点公共属性
        TestGeneralSetCommonProperty_Root(root, i, string, wrFixed, isDefaultValue);

        // InsertOrReplace设置新增字段属性
        if (schemaVersion == 0) {
            TestGeneralT1ldVersionSetAllFieldsPropertyFailed_Root(root, i);
        } else if (schemaVersion == 1) {
            TestGeneralT1ldVersionSetAllFieldsProperty_Root(root, i);
        }
        // 插入vectoryT1
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(T1, &T1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionSetCommonProperty_T1_V(T1, i);
        // 插入vectoryT2
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(T2, &T2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionSetCommonProperty_T2_V(T2, i);

        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(stmt, 2);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestGeneralT1MergeUpdateSn(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue, char *string,
    int32_t schemaVersion, GmcOperationTypeE operationType, bool isDefaultValue = true)
{
    for (int i = startValue; i < endValue; i++) {
        int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        TestGeneralT1PkIndexSet(stmt, i);
        GmcNodeT *root, *T1, *T2;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1SetPk(root, i);
        // Merge设置公共部分属性
        TestGeneralUpdateSetCommonProperty_Root(root, i, string, isDefaultValue);

        // InsertOrReplace设置新增字段属性
        if (schemaVersion == 0) {
            TestGeneralT1ldVersionSetAllFieldsPropertyFailed_Root(root, i);
        } else if (schemaVersion == 1) {
            TestGeneralT1ldVersionSetAllFieldsProperty_Root(root, i);
        }

        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(stmt, 2);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

// delete start
void TestGeneralT1PkDeleteSn(
    GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue, int32_t schemaVersion, bool isSn = false)
{
    int userDataIdx = 0;
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = startValue; i < endValue; i++) {
        TestGeneralT1PkIndexSet(stmt, i);
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void SnQuery(GmcStmtT *subStmt, int64_t updateValue = 0)
{
    int ret;
    int64_t f0Value;
    bool isNull;
    GmcNodeT *root, *T1, *T2;
    // 查询根节点
    ret = GmcGetRootNode(subStmt, &root);
    ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    // 查询根节点公共部分
    TestGeneralT1GetCommonProperty_Root(root, f0Value + updateValue, (char *)"string", true);
    // 查询根节点LMP6索引
    TestGeneralT1GetLpm6Property(root, f0Value);
    // 查询升级字段属性
    TestGeneralT1GetAllField_Root(root, f0Value + updateValue);
    // 查询vectoryT1
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    TestGeneralT1ldVersionGetCommonProperty_T1_V(T1, f0Value);
    // 查询vectoryT2
    ret = GmcNodeGetChild(T1, "T2", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    TestGeneralT1ldVersionGetCommonProperty_T2_V(T2, f0Value);
}

void SnQueryOldVersion(GmcStmtT *subStmt, int64_t updateValue = 0)
{
    int ret;
    int64_t f0Value;
    bool isNull;
    GmcNodeT *root, *T1, *T2;
    // 查询根节点
    ret = GmcGetRootNode(subStmt, &root);
    ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    // 查询根节点公共部分
    TestGeneralT1GetCommonProperty_Root(root, f0Value + updateValue, (char *)"string", true);
    // 查询根节点LMP6索引
    TestGeneralT1GetLpm6Property(root, f0Value);

    // 查询升级字段属性失败
    TestGeneralT1GetAllFieldFail_Root(root, f0Value + updateValue);

    // 查询vectoryT1
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    TestGeneralT1ldVersionGetCommonProperty_T1_V(T1, f0Value);
    // 查询vectoryT2
    ret = GmcNodeGetChild(T1, "T2", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    TestGeneralT1ldVersionGetCommonProperty_T2_V(T2, f0Value);
}

void sn_callback_newversion(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret;
    bool isNull;
    int index, i;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    void *label = 0, *keyValue = 0;
    char keyName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);
            // 默认推送new object和old object
            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    SnQuery(subStmt);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    SnQuery(subStmt);
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    SnQuery(subStmt, 50);
                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    SnQuery(subStmt);
                    break;
                }
                case GMC_SUB_EVENT_MERGE_INSERT: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    SnQuery(subStmt);
                    break;
                }
                case GMC_SUB_EVENT_MERGE_UPDATE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    SnQuery(subStmt, 50);
                    break;
                }
                case GMC_SUB_EVENT_REPLACE_INSERT: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    SnQuery(subStmt);
                    break;
                }
                case GMC_SUB_EVENT_REPLACE_UPDATE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    SnQuery(subStmt);
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    SnQuery(subStmt);
                    break;
                }
                case GMC_SUB_EVENT_MERGE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);

                    // 读old
                    if (((bool *)user_data->isReplace_insert)[user_data->subIndex]) {
                        ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                        EXPECT_EQ(GMERR_NO_DATA, ret);
                    } else {
                        ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                        EXPECT_EQ(GMERR_OK, ret);
                        index = ((int *)user_data->old_value)[user_data->subIndex];
                    }
                    break;
                }
                case GMC_SUB_EVENT_REPLACE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);

                    // 读old
                    if (((bool *)user_data->isReplace_insert)[user_data->subIndex]) {
                        ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                        EXPECT_EQ(GMERR_NO_DATA, ret);
                    } else {
                        ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                        EXPECT_EQ(GMERR_OK, ret);
                        index = ((int *)user_data->old_value)[user_data->subIndex];
                    }
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_ERROR, "invalid eventType %d  line:%d\r\n", info->eventType, __LINE__);
                    break;
                }
            }
            break;
        }
        user_data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE_INSERT: {
                user_data->replaceInsertNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE_UPDATE: {
                user_data->replaceUpdateNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE: {
                user_data->mergeNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_INSERT: {
                user_data->mergeInsertNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_UPDATE: {
                user_data->mergeUpdateNum++;
                break;
            }
            default: {
                AW_FUN_Log(LOG_ERROR, "invalid eventType %d  line:%d\r\n", info->eventType, __LINE__);
                break;
            }
        }
    }
}

void sn_callback_oldversion(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret;
    bool isNull;
    int index, i;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    void *label = 0, *keyValue = 0;
    char keyName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);
            // 默认推送new object和old object
            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    SnQueryOldVersion(subStmt);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    SnQueryOldVersion(subStmt);
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    SnQueryOldVersion(subStmt, 100);
                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    SnQueryOldVersion(subStmt);
                    break;
                }
                case GMC_SUB_EVENT_MERGE_INSERT: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    SnQueryOldVersion(subStmt);
                    break;
                }
                case GMC_SUB_EVENT_MERGE_UPDATE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    SnQueryOldVersion(subStmt);
                    break;
                }
                case GMC_SUB_EVENT_REPLACE_INSERT: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    SnQueryOldVersion(subStmt);
                    break;
                }
                case GMC_SUB_EVENT_REPLACE_UPDATE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    SnQueryOldVersion(subStmt);
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    SnQueryOldVersion(subStmt);
                    break;
                }
                case GMC_SUB_EVENT_MERGE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);

                    // 读old
                    if (((bool *)user_data->isReplace_insert)[user_data->subIndex]) {
                        ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                        EXPECT_EQ(GMERR_NO_DATA, ret);
                    } else {
                        ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                        EXPECT_EQ(GMERR_OK, ret);
                        index = ((int *)user_data->old_value)[user_data->subIndex];
                    }
                    break;
                }
                case GMC_SUB_EVENT_REPLACE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    // 读old
                    if (((bool *)user_data->isReplace_insert)[user_data->subIndex]) {
                        ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                        EXPECT_EQ(GMERR_NO_DATA, ret);
                    } else {
                        ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                        EXPECT_EQ(GMERR_OK, ret);
                        index = ((int *)user_data->old_value)[user_data->subIndex];
                    }
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_ERROR, "invalid eventType %d  line:%d\r\n", info->eventType, __LINE__);
                    break;
                }
            }
            break;
        }
        user_data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE_INSERT: {
                user_data->replaceInsertNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE_UPDATE: {
                user_data->replaceUpdateNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE: {
                user_data->mergeNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_INSERT: {
                user_data->mergeInsertNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_UPDATE: {
                user_data->mergeUpdateNum++;
                break;
            }
            default: {
                AW_FUN_Log(LOG_ERROR, "invalid eventType %d  line:%d\r\n", info->eventType, __LINE__);
                break;
            }
        }
    }
}

void TestGeneralT1UpdateMultipleVersion(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue,
    char *string, int32_t schemaVersion, GmcOperationTypeE operationType, bool isDefaultValue = true,
    int64_t updateValue = 0)
{
    int ret = 0;
    for (int i = startValue; i < endValue; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        TestGeneralT1PkIndexSet(stmt, i);
        GmcNodeT *root, *T1, *T2;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);

        // 设置根节点公共部分属性
        TestGeneralUpdateSetCommonProperty_Root(root, i + updateValue, string, isDefaultValue);
        // Update设置新增字段属性
        if (schemaVersion == 1) {
            TestGeneralT1ldVersionSetAllFieldsProperty_Root(root, i + updateValue);
        } else if (schemaVersion == 2) {
            TestGeneralT1ldVersionSetAllFieldsPropertyV2_Root(root, i + updateValue);
        }

        // 更新vectoryT1
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(T1, &T1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionSetCommonProperty_T1_V(T1, i + updateValue);
        // 更新vectoryT2
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(T2, &T2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionSetCommonProperty_T2_V(T2, i + updateValue);

        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(stmt, 1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestGeneralT1PkScanMultipleVersionUpdate(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue,
    char *string, int32_t schemaVersion, bool isDefaultValue = true, int64_t updateValue = 0, int64_t id = 25)
{
    bool isFinish = true;
    int ret = 0;
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = startValue; i < endValue; i++) {
        TestGeneralT1PkIndexSet(stmt, i);
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *root, *T1, *T2;
        // 查询根节点
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        // 查询根节点公共部分
        TestGeneralT1GetCommonProperty_Root(root, i + updateValue, string, isDefaultValue);
        // 查询根节点LMP6索引
        TestGeneralT1GetLpm6Property(root, i);

        // Pk查询升级后新增字段
        if (schemaVersion == 0) {
            TestGeneralT1GetAllFieldFail_Root(root, i);
        } else if (schemaVersion == 1) {
            TestGeneralT1GetAllField_Root(root, i + updateValue);
        } else if (schemaVersion == 2) {
            if (id == 25) {
                TestGeneralT1GetAllFieldV1NULL_Root(root);
            } else if (id == 26) {
                TestGeneralT1GetAllField_Root(root, i);
                TestGeneralT1GetAllFieldF33MultipleVersion_Root(root);
            }
        }

        // 查询vectoryT1
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionGetCommonProperty_T1_V(T1, i);
        // 查询vectoryT2
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionGetCommonProperty_T2_V(T2, i);
    }
}

void TestGeneralT1FullScanMultipleVersion(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue,
    char *string, int32_t schemaVersion, bool isDefaultValue = true)
{
    bool isFinish = true;
    int ret = 0;
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    for (int i = startValue; i < endValue; i++) {
        TestGeneralT1PkIndexSet(stmt, i);
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *root, *T1, *T2;
        // 查询根节点
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        // 查询根节点公共部分
        TestGeneralT1GetCommonProperty_Root(root, i, string, isDefaultValue);

        // 查询升级后新增字段
        bool isNull;
        if ((i >= 0) && (i < 10)) {
            int64_t f14Value;
            ret = GmcNodeGetPropertyByName(root, (char *)"F14", &f14Value, sizeof(int64_t), &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ASSERT_EQ(true, isNull);
        } else if ((i >= 10) && (i < 20)) {
            int64_t f14Value;
            ret = GmcNodeGetPropertyByName(root, (char *)"F14", &f14Value, sizeof(int64_t), &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ASSERT_EQ(false, isNull);
            AW_MACRO_ASSERT_EQ_INT(i, f14Value);

            int64_t f33Value;
            ret = GmcNodeGetPropertyByName(root, (char *)"F33", &f33Value, sizeof(int64_t), &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ASSERT_EQ(true, isNull);
        } else if ((i >= 20) && (i < 30)) {
            int64_t f14Value;
            ret = GmcNodeGetPropertyByName(root, (char *)"F14", &f14Value, sizeof(int64_t), &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ASSERT_EQ(false, isNull);
            AW_MACRO_ASSERT_EQ_INT(i, f14Value);

            int64_t f33Value;
            ret = GmcNodeGetPropertyByName(root, (char *)"F33", &f33Value, sizeof(int64_t), &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ASSERT_EQ(false, isNull);
            AW_MACRO_ASSERT_EQ_INT(i, f33Value);
        }

        // 查询vectoryT1
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionGetCommonProperty_T1_V(T1, i);
        // 查询vectoryT2
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionGetCommonProperty_T2_V(T2, i);
    }
}

void TestGeneralT1FullScanReliable(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue, char *string,
    int32_t schemaVersion, bool isDefaultValue = true)
{
    bool isFinish = true;
    int ret = 0;
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    for (int i = startValue; i < endValue; i++) {
        TestGeneralT1PkIndexSet(stmt, i);
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *root, *T1, *T2;
        // 查询根节点
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        // 查询根节点公共部分
        TestGeneralT1GetCommonProperty_Root(root, i, string, isDefaultValue);

        // 查询升级后新增字段
        bool isNull;
        if ((i >= 0) && (i < 30)) {
            int64_t f14Value;
            ret = GmcNodeGetPropertyByName(root, (char *)"F14", &f14Value, sizeof(int64_t), &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ASSERT_EQ(true, isNull);
            int64_t f33Value;
            ret = GmcNodeGetPropertyByName(root, (char *)"F33", &f33Value, sizeof(int64_t), &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ASSERT_EQ(true, isNull);
        } else if ((i >= 30) && (i < 60)) {
            int64_t f14Value;
            ret = GmcNodeGetPropertyByName(root, (char *)"F14", &f14Value, sizeof(int64_t), &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ASSERT_EQ(false, isNull);
            AW_MACRO_ASSERT_EQ_INT(i, f14Value);

            int64_t f33Value;
            ret = GmcNodeGetPropertyByName(root, (char *)"F33", &f33Value, sizeof(int64_t), &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ASSERT_EQ(true, isNull);
        } else if ((i >= 60) && (i < 90)) {
            int64_t f14Value;
            ret = GmcNodeGetPropertyByName(root, (char *)"F14", &f14Value, sizeof(int64_t), &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ASSERT_EQ(false, isNull);
            AW_MACRO_ASSERT_EQ_INT(i, f14Value);

            int64_t f33Value;
            ret = GmcNodeGetPropertyByName(root, (char *)"F33", &f33Value, sizeof(int64_t), &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ASSERT_EQ(false, isNull);
            AW_MACRO_ASSERT_EQ_INT(i, f33Value);
        }

        // 查询vectoryT1
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionGetCommonProperty_T1_V(T1, i);
        // 查询vectoryT2
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        TestGeneralT1ldVersionGetCommonProperty_T2_V(T2, i);
    }
}

#endif
