/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2012-2018. All rights reserved.
 Description  :表结构升级结构化头文件
 Author       : wuxiaochun wx753022
 Modification :
 Date         : 2022/07/28
**************************************************************************** */
#ifndef VERTEXLABEL_UPGRADE_STRUCT_H
#define VERTEXLABEL_UPGRADE_STRUCT_H

#include "VertexLabelUpgrade.h"

typedef struct TagSimplelabelStructCfg {
    int32_t startVal;       // 主键或其他非成员索引的起始值
    uint32_t count;         // 主键或其他非成员索引的数量
    int32_t coefficient;    // 字段值生成系数, 通过startVal和coefficient组合生成不同的整形和浮点型字段值
    int32_t expAffectRows;  // 预期的affectRows
    int32_t threadId;       // 线程Id
} GtSimplelabelStructCfgT;

#pragma pack(1)
typedef struct TagSimplel0abelVertex {
    int64_t f0;
    uint64_t f1;
    int32_t f2;
    uint32_t f3;
    int16_t f4;
    uint16_t f5;
    uint8_t f6;
    uint64_t f7;
    uint8_t f8[9];
    uint8_t f9 : 5;
    uint8_t res1 : 3;
    uint16_t f10 : 10;
    uint16_t res2 : 6;
    uint32_t f11;
    uint32_t f12;
    uint8_t f13 : 4;
    uint8_t res3 : 4;
} GtSimplelabel0VertexT;
#pragma pack()

#pragma pack(1)
typedef struct TagSimplelabelVertex {
    int64_t f0;
    uint64_t f1;
    int32_t f2;
    uint32_t f3;
    int16_t f4;
    uint16_t f5;
    uint8_t f6;
    uint64_t f7;
    uint8_t f8[9];
    uint8_t f9 : 5;
    uint8_t res1 : 3;
    uint16_t f10 : 10;
    uint16_t res2 : 6;
    uint32_t f11;
    uint32_t f12;
    uint8_t f13 : 4;
    uint8_t res3 : 4;
    uint64_t f14;
} GtSimplelabelVertexT;
#pragma pack()

#pragma pack(1)
typedef struct TagSimplelabel2Vertex {
    int64_t f0;
    uint64_t f1;
    int32_t f2;
    uint32_t f3;
    int16_t f4;
    uint16_t f5;
    uint8_t f6;
    uint64_t f7;
    uint8_t f8[9];
    uint8_t f9 : 5;
    uint8_t res1 : 3;
    uint16_t f10 : 10;
    uint16_t res2 : 6;
    uint32_t f11;
    uint32_t f12;
    uint8_t f13 : 4;
    uint8_t res3 : 4;
    int64_t f14;
    uint64_t f15;
    int32_t f16;
    uint32_t f17;
    int16_t f18;
    uint16_t f19;
    int8_t f20;
    uint8_t f21;
    uint64_t f22;
    uint8_t f23[8];
    uint8_t f24 : 5;
    uint8_t res4 : 3;
    uint16_t f25 : 10;
    uint16_t res5 : 6;
    uint32_t f26 : 17;
    uint32_t res6 : 15;
    uint64_t f27 : 33;
    uint64_t res7 : 31;
    bool f28;
    float f29;
    double f30;
    uint8_t f31[1];
} GtSimplelabel2VertexT;
#pragma pack()

#pragma pack(1)
typedef struct TagSimplelabel3Vertex {
    int64_t f0;
    uint64_t f1;
    int32_t f2;
    uint32_t f3;
    int16_t f4;
    uint16_t f5;
    uint8_t f6;
    uint64_t f7;
    uint8_t f8[9];
    uint8_t f9 : 5;
    uint8_t res1 : 3;
    uint16_t f10 : 10;
    uint16_t res2 : 6;
    uint32_t f11;
    uint32_t f12;
    uint8_t f13 : 4;
    uint8_t res3 : 4;
    uint8_t f23[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f24[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f25[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f26[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f27[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f28[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f29[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f30[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f31[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f32[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f33[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f34[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f35[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f36[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f37[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f38[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f39[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f40[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f41[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f42[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f43[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f44[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f45[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f46[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f47[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f48[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f49[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f50[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f51[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f52[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f53[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f54[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f55[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f56[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f57[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f58[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f59[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f60[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f61[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f62[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f63[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f64[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f65[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f66[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f67[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f68[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f69[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f70[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f71[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f72[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f73[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f74[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f75[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f76[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f77[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f78[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f79[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f80[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f81[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f82[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f83[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f84[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f85[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f86[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f87[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f88[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f89[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f90[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f91[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f92[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f93[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f94[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f95[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f96[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f97[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f98[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f99[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
    uint8_t f100[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE];
} GtSimplelabel3VertexT;
#pragma pack()

#pragma pack(1)
typedef struct TagSimplelabel4Vertex {
    int64_t f0;
    uint64_t f1;
    int32_t f2;
    uint32_t f3;
    int16_t f4;
    uint16_t f5;
    uint8_t f6;
    uint64_t f7;
    uint8_t f8[9];
    uint8_t f9 : 5;
    uint8_t res1 : 3;
    uint16_t f10 : 10;
    uint16_t res2 : 6;
    uint32_t f11;
    uint32_t f12;
    uint8_t f13 : 4;
    uint8_t f14 : 1;
    uint8_t res3 : 3;
    uint32_t f15;
    uint16_t f16;
} GtSimplelabel4VertexT;
#pragma pack()

#pragma pack(1)
typedef struct TagSimplelabel5Vertex {
    int64_t f0;
    uint64_t f1;
    int32_t f2;
    uint32_t f3;
    int16_t f4;
    uint16_t f5;
    uint8_t f6;
    uint64_t f7;
    uint8_t f8[9];
    uint8_t f9 : 5;
    uint8_t res1 : 3;
    uint16_t f10 : 10;
    uint16_t res2 : 6;
    uint32_t f11;
    uint32_t f12;
    uint8_t f13 : 4;
    uint8_t res3 : 4;
    int64_t f14;
    uint64_t f15;
    int32_t f16;
    uint32_t f17;
    int16_t f18;
    uint16_t f19;
    int8_t f20;
    uint8_t f21;
    uint64_t f22;
    uint8_t f23[8];
    uint8_t f24 : 5;
    uint8_t res4 : 3;
    uint16_t f25 : 10;
    uint16_t res5 : 6;
    uint32_t f26 : 17;
    uint32_t res6 : 15;
    uint64_t f27 : 33;
    uint64_t res7 : 31;
    bool f28;
    float f29;
    double f30;
    uint8_t f31[1];
    uint8_t f32[SIMPLE_LABEL_ADD_FIXED_SIZE];
} GtSimplelabel5VertexT;
#pragma pack()

void GtSimplelabel3StructSetNewProperty(GtSimplelabel3VertexT *vertex, int32_t value, int32_t coefficient,
                                        int64_t updateValue = 0)
{
    (void)snprintf((char *)vertex->f26, sizeof(vertex->f26), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f27, sizeof(vertex->f27), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f28, sizeof(vertex->f28), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f29, sizeof(vertex->f29), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f30, sizeof(vertex->f30), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f31, sizeof(vertex->f31), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f32, sizeof(vertex->f32), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f33, sizeof(vertex->f33), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f34, sizeof(vertex->f34), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f35, sizeof(vertex->f35), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f36, sizeof(vertex->f36), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f37, sizeof(vertex->f37), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f38, sizeof(vertex->f38), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f39, sizeof(vertex->f39), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f40, sizeof(vertex->f40), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f41, sizeof(vertex->f41), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f42, sizeof(vertex->f42), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f43, sizeof(vertex->f43), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f44, sizeof(vertex->f44), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f45, sizeof(vertex->f45), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f46, sizeof(vertex->f46), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f47, sizeof(vertex->f47), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f48, sizeof(vertex->f48), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f49, sizeof(vertex->f49), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f50, sizeof(vertex->f50), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f51, sizeof(vertex->f51), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f52, sizeof(vertex->f52), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f53, sizeof(vertex->f53), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f54, sizeof(vertex->f54), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f55, sizeof(vertex->f55), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f56, sizeof(vertex->f56), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f57, sizeof(vertex->f57), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f58, sizeof(vertex->f58), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f59, sizeof(vertex->f59), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f60, sizeof(vertex->f60), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f61, sizeof(vertex->f61), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f62, sizeof(vertex->f62), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f63, sizeof(vertex->f63), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f64, sizeof(vertex->f64), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f65, sizeof(vertex->f65), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f66, sizeof(vertex->f66), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f67, sizeof(vertex->f67), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f68, sizeof(vertex->f68), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f69, sizeof(vertex->f69), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f70, sizeof(vertex->f70), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f71, sizeof(vertex->f71), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f72, sizeof(vertex->f72), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f73, sizeof(vertex->f73), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f74, sizeof(vertex->f74), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f75, sizeof(vertex->f75), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f76, sizeof(vertex->f76), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f77, sizeof(vertex->f77), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f78, sizeof(vertex->f78), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f79, sizeof(vertex->f79), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f80, sizeof(vertex->f80), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f81, sizeof(vertex->f81), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f82, sizeof(vertex->f82), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f83, sizeof(vertex->f83), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f84, sizeof(vertex->f84), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f85, sizeof(vertex->f85), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f86, sizeof(vertex->f86), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f87, sizeof(vertex->f87), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f88, sizeof(vertex->f88), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f89, sizeof(vertex->f89), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f90, sizeof(vertex->f90), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f91, sizeof(vertex->f91), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f92, sizeof(vertex->f92), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f93, sizeof(vertex->f93), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f94, sizeof(vertex->f94), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f95, sizeof(vertex->f95), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f96, sizeof(vertex->f96), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f97, sizeof(vertex->f97), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f98, sizeof(vertex->f98), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f99, sizeof(vertex->f99), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f100, sizeof(vertex->f100), "f%013310d", value + updateValue);
}


void GtSimplelabel1StructSetPk(GtSimplelabelVertexT *vertex, int64_t value)
{
    vertex->f0 = value;
}

void GtSimplelabel1StructSetHashcluster(GtSimplelabelVertexT *vertex, int64_t value, int64_t updateValue = 0)
{
    uint64_t f1Value = value + updateValue;
    int32_t f2Value = value  + updateValue;
    vertex->f1 = f1Value;
    vertex->f2 = f2Value;
}

void GtSimplelabel1StructSetLocalhash(GtSimplelabelVertexT *vertex, int64_t value, int64_t updateValue = 0)
{
    int16_t f4Value = (value + updateValue) % 32768;
    uint16_t f5Value = (value + updateValue) % 65536;
    vertex->f4 = f4Value;
    vertex->f5 = f5Value;
}

void GtSimplelabel1StructSetLpm4(GtSimplelabelVertexT *vertex, int64_t value)
{
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    vertex->f3 = 0;
    vertex->f11 = 0;
    if ((value) <= MAX_MASK_LEN_16) {
        destIpAddr = ((value + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if ((value) > MAX_MASK_LEN_16 && (value) <= MAX_MASK_LEN_24) {
        destIpAddr = ((value + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((value + 2));
        maskLen = ((32) & 0xff);
    }
    vertex->f12 = destIpAddr;
    vertex->f6 = maskLen;
}

void GtSimplelabel1StructSetLocal(GtSimplelabelVertexT *vertex, int64_t value)
{
    uint32_t f3Value = value & 0xffffffff;
    vertex->f3 = f3Value;
}

void GtSimplelabel1StructSetOldProperty(GtSimplelabelVertexT *vertex, int64_t value, int64_t coefficient,
                                        bool isDefaultValue = true, int32_t updateValue = 0)
{
    vertex->f1 = value + updateValue;
    vertex->f2 = value + updateValue;

    vertex->f4 = (value + updateValue) % 32768;
    vertex->f5 = (value + updateValue) % 65536;
    vertex->f7 = value + updateValue;
    if (!isDefaultValue) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            vertex->f8[j] = j;
        }
        vertex->f9 = (value + updateValue) % 31;
        vertex->f10 = (value + updateValue) % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            vertex->f8[j] = 0xff;
        }
        vertex->f9 = 0x1f;
        vertex->f10 = 0x3ff;
    }
    vertex->f13 = (value + updateValue) & 0xf;
}

void GtSimplelabel1StructSetNewProperty(GtSimplelabelVertexT *vertex, int64_t value, int64_t coefficient,
                                        int32_t updateValue = 0)
{
    vertex->f14 = value + updateValue;
}

void GtSimplelabel0StructSetPk(GtSimplelabel0VertexT *vertex, int64_t value)
{
    vertex->f0 = value;
}

void GtSimplelabel0StructSetHashcluster(GtSimplelabel0VertexT *vertex, int64_t value, int64_t updateValue = 0)
{
    uint64_t f1Value = value + updateValue;
    int32_t f2Value = value  + updateValue;
    vertex->f1 = f1Value;
    vertex->f2 = f2Value;
}

void GtSimplelabel0StructSetLocalhash(GtSimplelabel0VertexT *vertex, int64_t value, int64_t updateValue = 0)
{
    int16_t f4Value = (value + updateValue) % 32768;
    uint16_t f5Value = (value + updateValue) % 65536;
    vertex->f4 = f4Value;
    vertex->f5 = f5Value;
}

void GtSimplelabel0StructSetLpm4(GtSimplelabel0VertexT *vertex, int64_t value)
{
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    vertex->f3 = 0;
    vertex->f11 = 0;
    if ((value) <= MAX_MASK_LEN_16) {
        destIpAddr = ((value + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if ((value) > MAX_MASK_LEN_16 && (value) <= MAX_MASK_LEN_24) {
        destIpAddr = ((value + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((value + 2));
        maskLen = ((32) & 0xff);
    }
    vertex->f12 = destIpAddr;
    vertex->f6 = maskLen;
}

void GtSimplelabel0StructSetLocal(GtSimplelabel0VertexT *vertex, int64_t value)
{
    uint32_t f3Value = value & 0xffffffff;
    vertex->f3 = f3Value;
}

void GtSimplelabel0StructSetOldProperty(GtSimplelabel0VertexT *vertex, int64_t value, int64_t coefficient,
                                        bool isDefaultValue = true, int32_t updateValue = 0)
{
    vertex->f1 = value + updateValue;
    vertex->f2 = value + updateValue;

    vertex->f4 = (value + updateValue) % 32768;
    vertex->f5 = (value + updateValue) % 65536;
    vertex->f7 = value + updateValue;
    if (!isDefaultValue) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            vertex->f8[j] = j;
        }
        vertex->f9 = (value + updateValue) % 31;
        vertex->f10 = (value + updateValue) % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            vertex->f8[j] = 0xff;
        }
        vertex->f9 = 0x1f;
        vertex->f10 = 0x3ff;
    }
    vertex->f13 = (value + updateValue) & 0xf;
}

void GtSimplelabel0StructGetLmpProperty(GtSimplelabel0VertexT *vertex, int64_t value)
{
    uint32_t f3Value = 0;
    uint32_t f11Value = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    if (value <= MAX_MASK_LEN_16) {
        destIpAddr = ((value + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if (value > MAX_MASK_LEN_16 && value <= MAX_MASK_LEN_24) {
        destIpAddr = ((value + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((value + 2));
        maskLen = ((32) & 0xff);
    }
    int32_t ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &f3Value, &vertex->f3, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &f11Value, &vertex->f11, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &destIpAddr, &vertex->f12, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &maskLen, &vertex->f6, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void GtSimplelabel0StructGetOldProperty(GtSimplelabel0VertexT *vertex, int64_t index, int64_t coefficient,
                                        bool isDefaultVaule = true, int32_t updateValue = 0)
{
    int ret = 0;
    int64_t f0Value = index;
    uint64_t f1Value = index + updateValue;
    int32_t f2Value = index + updateValue;
    int16_t f4Value = (index + updateValue) % 32768;
    uint16_t f5Value = (index + updateValue) % 65536;
    uint64_t f7Value = index + updateValue;
    uint8_t fixedValue[SIMPLE_LABEL_FIXED_SIZE] = {0};
    uint8_t f9Value = 0;
    uint16_t f10Value = 0;
    uint8_t f13Value = (index + updateValue) & 0xf;
    if (!isDefaultVaule) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = j;
        }
        f9Value = (index + updateValue) % 31;
        f10Value = (index + updateValue) % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = 0xff;
        }
        f9Value = 0x1f;
        f10Value = 0x3ff;
    }
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &f0Value, &vertex->f0, sizeof(f0Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &f1Value, &vertex->f1, sizeof(f1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &f2Value, &vertex->f2, sizeof(f2Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &f4Value, &vertex->f4, sizeof(f4Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &f5Value, &vertex->f5, sizeof(f5Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_TIME, &f7Value, &vertex->f7, sizeof(f7Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, fixedValue, vertex->f8, SIMPLE_LABEL_FIXED_SIZE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(f9Value, vertex->f9);
    AW_MACRO_EXPECT_EQ_INT(f10Value, vertex->f10);
    AW_MACRO_EXPECT_EQ_INT(f13Value, vertex->f13);
}


void GtSimplelabel2StructSetPk(GtSimplelabel2VertexT *vertex, int64_t value)
{
    vertex->f0 = value;
}

void GtSimplelabel2StructSetHashcluster(GtSimplelabel2VertexT *vertex, int64_t value, int64_t updateValue = 0)
{
    uint64_t f1Value = value + updateValue;
    int32_t f2Value = value  + updateValue;
    vertex->f1 = f1Value;
    vertex->f2 = f2Value;
}

void GtSimplelabel2StructSetLocalhash(GtSimplelabel2VertexT *vertex, int64_t value, int64_t updateValue = 0)
{
    int16_t f4Value = (value + updateValue) % 32768;
    uint16_t f5Value = (value + updateValue) % 65536;
    vertex->f4 = f4Value;
    vertex->f5 = f5Value;
}

void GtSimplelabel2StructSetLpm4(GtSimplelabel2VertexT *vertex, int64_t value)
{
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    vertex->f3 = 0;
    vertex->f11 = 0;
    if ((value) <= MAX_MASK_LEN_16) {
        destIpAddr = ((value + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if ((value) > MAX_MASK_LEN_16 && (value) <= MAX_MASK_LEN_24) {
        destIpAddr = ((value + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((value + 2));
        maskLen = ((32) & 0xff);
    }
    vertex->f12 = destIpAddr;
    vertex->f6 = maskLen;
}

void GtSimplelabel2StructSetLocal(GtSimplelabel2VertexT *vertex, int64_t value)
{
    uint32_t f3Value = value & 0xffffffff;
    vertex->f3 = f3Value;
}

void GtSimplelabel3StructSetPk(GtSimplelabel3VertexT *vertex, int64_t value)
{
    vertex->f0 = value;
}

void GtSimplelabel3StructSetHashcluster(GtSimplelabel3VertexT *vertex, int64_t value, int64_t updateValue = 0)
{
    uint64_t f1Value = value + updateValue;
    int32_t f2Value = value  + updateValue;
    vertex->f1 = f1Value;
    vertex->f2 = f2Value;
}

void GtSimplelabel3StructSetLocalhash(GtSimplelabel3VertexT *vertex, int64_t value, int64_t updateValue = 0)
{
    int16_t f4Value = (value + updateValue) % 32768;
    uint16_t f5Value = (value + updateValue) % 65536;
    vertex->f4 = f4Value;
    vertex->f5 = f5Value;
}

void GtSimplelabel3StructSetLpm4(GtSimplelabel3VertexT *vertex, int64_t value)
{
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    vertex->f3 = 0;
    vertex->f11 = 0;
    if ((value) <= MAX_MASK_LEN_16) {
        destIpAddr = ((value + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if ((value) > MAX_MASK_LEN_16 && (value) <= MAX_MASK_LEN_24) {
        destIpAddr = ((value + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((value + 2));
        maskLen = ((32) & 0xff);
    }
    vertex->f12 = destIpAddr;
    vertex->f6 = maskLen;
}

void GtSimplelabel3StructSetLocal(GtSimplelabel3VertexT *vertex, int64_t value)
{
    uint32_t f3Value = value & 0xffffffff;
    vertex->f3 = f3Value;
}

void GtSimplelabel3StructSetOldProperty(GtSimplelabel3VertexT *vertex, int64_t value, int64_t coefficient,
                                        bool isDefaultValue = true, int32_t updateValue = 0)
{
    vertex->f1 = value + updateValue;
    vertex->f2 = value + updateValue;

    vertex->f4 = (value + updateValue) % 32768;
    vertex->f5 = (value + updateValue) % 65536;
    vertex->f7 = value + updateValue;
    if (!isDefaultValue) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            vertex->f8[j] = j;
        }
        vertex->f9 = (value + updateValue) % 31;
        vertex->f10 = (value + updateValue) % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            vertex->f8[j] = 0xff;
        }
        vertex->f9 = 0x1f;
        vertex->f10 = 0x3ff;
    }
    vertex->f13 = (value + updateValue) & 0xf;
    (void)snprintf((char *)vertex->f23, sizeof(vertex->f23), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f24, sizeof(vertex->f24), "f%013310d", value + updateValue);
    (void)snprintf((char *)vertex->f25, sizeof(vertex->f25), "f%013310d", value + updateValue);
}

void GtSimplelabel2StructSetOldProperty(GtSimplelabel2VertexT *vertex, int64_t value, int64_t coefficient,
                                        bool isDefaultValue = true, int32_t updateValue = 0)
{
    vertex->f1 = value + updateValue;
    vertex->f2 = value + updateValue;

    vertex->f4 = (value + updateValue) % 32768;
    vertex->f5 = (value + updateValue) % 65536;
    vertex->f7 = value + updateValue;
    if (!isDefaultValue) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            vertex->f8[j] = j;
        }
        vertex->f9 = (value + updateValue) % 31;
        vertex->f10 = (value + updateValue) % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            vertex->f8[j] = 0xff;
        }
        vertex->f9 = 0x1f;
        vertex->f10 = 0x3ff;
    }
    vertex->f13 = (value + updateValue) & 0xf;
}

void GtSimplelabel2StructSetNewProperty(GtSimplelabel2VertexT *vertex, int64_t value, int64_t coefficient,
                                        int32_t updateValue = 0)
{
    vertex->f14 = value + updateValue;
    vertex->f15 = value + updateValue;
    vertex->f16 = value + updateValue;
    vertex->f17 = value + updateValue;
    vertex->f18 = (value + updateValue) & 0x7fff;
    vertex->f19 = (value + updateValue) & 0xffff;
    vertex->f20 = (value + updateValue) & 0x7f;
    vertex->f21 = (value + updateValue) & 0xff;
    vertex->f22 = value + updateValue;
    for (int j = 0; j < SIMPLE_LABEL2_FIXED_SIZE; j++) {
        vertex->f23[j] = j;
    }
    vertex->f24 = (value + updateValue) & 0x1f;
    vertex->f25 = (value + updateValue) & 0x3ff;
    vertex->f26 = (value + updateValue) & 0x1ffff;
    vertex->f27 = (value + updateValue) & 0x1ffffffff;
    vertex->f28 = value + updateValue;
    vertex->f29 = value + updateValue;
    vertex->f30 = value + updateValue;
    uint8_t bitmap[1] = {0xff};
    memcpy(vertex->f31, bitmap, sizeof(vertex->f31));
}

void GtSimplelabel5StructSetPk(GtSimplelabel5VertexT *vertex, int64_t value)
{
    vertex->f0 = value;
}

void GtSimplelabel5StructSetHashcluster(GtSimplelabel5VertexT *vertex, int64_t value, int64_t updateValue = 0)
{
    uint64_t f1Value = value + updateValue;
    int32_t f2Value = value  + updateValue;
    vertex->f1 = f1Value;
    vertex->f2 = f2Value;
}

void GtSimplelabel5StructSetLocalhash(GtSimplelabel5VertexT *vertex, int64_t value, int64_t updateValue = 0)
{
    int16_t f4Value = (value + updateValue) % 32768;
    uint16_t f5Value = (value + updateValue) % 65536;
    vertex->f4 = f4Value;
    vertex->f5 = f5Value;
}

void GtSimplelabel5StructSetLpm4(GtSimplelabel5VertexT *vertex, int64_t value)
{
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    vertex->f3 = 0;
    vertex->f11 = 0;
    if ((value) <= MAX_MASK_LEN_16) {
        destIpAddr = ((value + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if ((value) > MAX_MASK_LEN_16 && (value) <= MAX_MASK_LEN_24) {
        destIpAddr = ((value + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((value + 2));
        maskLen = ((32) & 0xff);
    }
    vertex->f12 = destIpAddr;
    vertex->f6 = maskLen;
}

void GtSimplelabel5StructSetLocal(GtSimplelabel5VertexT *vertex, int64_t value)
{
    uint32_t f3Value = value & 0xffffffff;
    vertex->f3 = f3Value;
}

void GtSimplelabel5StructSetOldProperty(GtSimplelabel5VertexT *vertex, int64_t value,
                                        bool isDefaultValue = true, int32_t updateValue = 0)
{
    vertex->f1 = value + updateValue;
    vertex->f2 = value + updateValue;

    vertex->f4 = (value + updateValue) % 32768;
    vertex->f5 = (value + updateValue) % 65536;
    vertex->f7 = value + updateValue;
    if (!isDefaultValue) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            vertex->f8[j] = j;
        }
        vertex->f9 = (value + updateValue) % 31;
        vertex->f10 = (value + updateValue) % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            vertex->f8[j] = 0xff;
        }
        vertex->f9 = 0x1f;
        vertex->f10 = 0x3ff;
    }
    vertex->f13 = (value + updateValue) & 0xf;
}

void GtSimplelabel5StructSetMidVersionProperty(GtSimplelabel5VertexT *vertex, int64_t value, int32_t updateValue = 0)
{
    vertex->f14 = value + updateValue;
    vertex->f15 = value + updateValue;
    vertex->f16 = value + updateValue;
    vertex->f17 = value + updateValue;
    vertex->f18 = (value + updateValue) & 0x7fff;
    vertex->f19 = (value + updateValue) & 0xffff;
    vertex->f20 = (value + updateValue) & 0x7f;
    vertex->f21 = (value + updateValue) & 0xff;
    vertex->f22 = value + updateValue;
    for (int j = 0; j < SIMPLE_LABEL2_FIXED_SIZE; j++) {
        vertex->f23[j] = j;
    }
    vertex->f24 = (value + updateValue) & 0x1f;
    vertex->f25 = (value + updateValue) & 0x3ff;
    vertex->f26 = (value + updateValue) & 0x1ffff;
    vertex->f27 = (value + updateValue) & 0x1ffffffff;
    vertex->f28 = value + updateValue;
    vertex->f29 = value + updateValue;
    vertex->f30 = value + updateValue;
    uint8_t bitmap[1] = {0xff};
    memcpy(vertex->f31, bitmap, sizeof(vertex->f31));
}

void GtSimplelabel5StructSetNewVersionProperty(GtSimplelabel5VertexT *vertex, int64_t value, int32_t updateValue = 0)
{
    uint64_t i = value + updateValue;
    (void)snprintf((char *)vertex->f32, SIMPLE_LABEL_ADD_FIXED_SIZE, "f%01329d", i);
}


void GtSimplelabel1StructSetLmpProperty(GtSimplelabelVertexT *vertex, int64_t value)
{
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    vertex->f3 = 0;
    vertex->f11 = 0;
    if ((value) <= MAX_MASK_LEN_16) {
        destIpAddr = ((value + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if ((value) > MAX_MASK_LEN_16 && (value) <= MAX_MASK_LEN_24) {
        destIpAddr = ((value + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((value + 2));
        maskLen = ((32) & 0xff);
    }
    vertex->f12 = destIpAddr;
    vertex->f6 = maskLen;
}

void GtSimplelabel1StructGetLmpProperty(GtSimplelabelVertexT *vertex, int64_t value)
{
    uint32_t f3Value = 0;
    uint32_t f11Value = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    if (value <= MAX_MASK_LEN_16) {
        destIpAddr = ((value + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if (value > MAX_MASK_LEN_16 && value <= MAX_MASK_LEN_24) {
        destIpAddr = ((value + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((value + 2));
        maskLen = ((32) & 0xff);
    }
    int32_t ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &f3Value, &vertex->f3, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &f11Value, &vertex->f11, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &destIpAddr, &vertex->f12, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &maskLen, &vertex->f6, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void GtSimplelabel2StructSetLmpProperty(GtSimplelabel2VertexT *vertex, int64_t value)
{
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    vertex->f3 = 0;
    vertex->f11 = 0;
    if ((value) <= MAX_MASK_LEN_16) {
        destIpAddr = ((value + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if ((value) > MAX_MASK_LEN_16 && (value) <= MAX_MASK_LEN_24) {
        destIpAddr = ((value + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((value + 2));
        maskLen = ((32) & 0xff);
    }
    vertex->f12 = destIpAddr;
    vertex->f6 = maskLen;
}

void GtSimplelabel2StructGetLmpProperty(GtSimplelabel2VertexT *vertex, int64_t value)
{
    uint32_t f3Value = 0;
    uint32_t f11Value = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    if (value <= MAX_MASK_LEN_16) {
        destIpAddr = ((value + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if (value > MAX_MASK_LEN_16 && value <= MAX_MASK_LEN_24) {
        destIpAddr = ((value + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((value + 2));
        maskLen = ((32) & 0xff);
    }
    int32_t ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &f3Value, &vertex->f3, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &f11Value, &vertex->f11, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &destIpAddr, &vertex->f12, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &maskLen, &vertex->f6, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void GtSimplelabel3StructSetLmpProperty(GtSimplelabel3VertexT *vertex, int64_t value)
{
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    vertex->f3 = 0;
    vertex->f11 = 0;
    if ((value) <= MAX_MASK_LEN_16) {
        destIpAddr = ((value + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if ((value) > MAX_MASK_LEN_16 && (value) <= MAX_MASK_LEN_24) {
        destIpAddr = ((value + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((value + 2));
        maskLen = ((32) & 0xff);
    }
    vertex->f12 = destIpAddr;
    vertex->f6 = maskLen;
}

void GtSimplelabel3StructGetLmpProperty(GtSimplelabel3VertexT *vertex, int64_t value)
{
    uint32_t f3Value = 0;
    uint32_t f11Value = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    if (value <= MAX_MASK_LEN_16) {
        destIpAddr = ((value + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if (value > MAX_MASK_LEN_16 && value <= MAX_MASK_LEN_24) {
        destIpAddr = ((value + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((value + 2));
        maskLen = ((32) & 0xff);
    }
    int32_t ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &f3Value, &vertex->f3, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &f11Value, &vertex->f11, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &destIpAddr, &vertex->f12, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &maskLen, &vertex->f6, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void GtSimplelabel5StructGetLmpProperty(GtSimplelabel5VertexT *vertex, int64_t value)
{
    uint32_t f3Value = 0;
    uint32_t f11Value = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    if (value <= MAX_MASK_LEN_16) {
        destIpAddr = ((value + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if (value > MAX_MASK_LEN_16 && value <= MAX_MASK_LEN_24) {
        destIpAddr = ((value + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((value + 2));
        maskLen = ((32) & 0xff);
    }
    int32_t ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &f3Value, &vertex->f3, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &f11Value, &vertex->f11, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &destIpAddr, &vertex->f12, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &maskLen, &vertex->f6, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void GtSimplelabel1StructGetNewProperty(GtSimplelabelVertexT *vertex, int64_t index, int64_t coefficient,
                                        int64_t updateValue = 0)
{
    uint64_t f14Value = index + updateValue;
    int32_t ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &f14Value, &vertex->f14, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void GtSimplelabel2StructGetNewProperty(GtSimplelabel2VertexT *vertex, int64_t value, int64_t coefficient,
                                        int64_t updateValue = 0, bool isNull = false)
{
    if (!isNull) {
        int64_t f14 = value + updateValue;
        int32_t ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &f14, &vertex->f14, sizeof(f14));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint64_t f15 = value + updateValue;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &f15, &vertex->f15, sizeof(f15));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        int32_t f16 = value + updateValue;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &f16, &vertex->f16, sizeof(f16));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint32_t f17 = value + updateValue;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &f17, &vertex->f17, sizeof(f17));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        int16_t f18 = (value + updateValue) & 0x7fff;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &f18, &vertex->f18, sizeof(f18));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint16_t f19 = (value + updateValue) & 0xffff;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &f19, &vertex->f19, sizeof(f19));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        int8_t f20 = (value + updateValue) & 0x7f;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_INT8, &f20, &vertex->f20, sizeof(f20));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint8_t f21 = (value + updateValue) & 0xff;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &f21, &vertex->f21, sizeof(f21));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint64_t f22 = value + updateValue;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_TIME, &f22, &vertex->f22, sizeof(f22));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint8_t f23[SIMPLE_LABEL2_FIXED_SIZE] = {0};
        for (int j = 0; j < SIMPLE_LABEL2_FIXED_SIZE; j++) {
            f23[j] = j;
        }
        ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, f23, &vertex->f23, sizeof(f23));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint8_t f24 = (value + updateValue) & 0x1f;
        uint16_t f25 = (value + updateValue) & 0x3ff;
        uint32_t f26 = (value + updateValue) & 0x1ffff;
        uint64_t f27 = (value + updateValue) & 0x1ffffffff;
        AW_MACRO_EXPECT_EQ_INT(f24, vertex->f24);
        AW_MACRO_EXPECT_EQ_INT(f25, vertex->f25);
        AW_MACRO_EXPECT_EQ_INT(f26, vertex->f26);
        AW_MACRO_EXPECT_EQ_INT(f27, vertex->f27);

        bool f28 = value + updateValue;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_BOOL, &f28, &vertex->f28, sizeof(f28));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        float f29 = value + updateValue;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_FLOAT, &f29, &vertex->f29, sizeof(f29));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        double f30 = value + updateValue;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_DOUBLE, &f30, &vertex->f30, sizeof(f30));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint8_t f31Bits[1] = {0xff};
        GmcBitMapT f31 = {0};
        f31.beginPos = 0;
        f31.endPos = 8 - 1;
        f31.bits = f31Bits;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_BITMAP, f31Bits, &vertex->f31, sizeof(f31Bits));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    } else {
        int64_t f14 = 0;
        int32_t ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &f14, &vertex->f14, sizeof(f14));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint64_t f15 = 0;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &f15, &vertex->f15, sizeof(f15));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        int32_t f16 = 0;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &f16, &vertex->f16, sizeof(f16));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint32_t f17 = 0;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &f17, &vertex->f17, sizeof(f17));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        int16_t f18 = 0;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &f18, &vertex->f18, sizeof(f18));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint16_t f19 = 0;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &f19, &vertex->f19, sizeof(f19));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        int8_t f20 = 0;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_INT8, &f20, &vertex->f20, sizeof(f20));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint8_t f21 = 0;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &f21, &vertex->f21, sizeof(f21));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint64_t f22 = 0;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_TIME, &f22, &vertex->f22, sizeof(f22));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint8_t f23[SIMPLE_LABEL2_FIXED_SIZE] = {0};
        for (int j = 0; j < SIMPLE_LABEL2_FIXED_SIZE; j++) {
            f23[j] = 0;
        }
        ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, f23, &vertex->f23, sizeof(f23));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint8_t f24 = 0;
        uint16_t f25 = 0;
        uint32_t f26 = 0;
        uint64_t f27 = 0;
        AW_MACRO_EXPECT_EQ_INT(f24, vertex->f24);
        AW_MACRO_EXPECT_EQ_INT(f25, vertex->f25);
        AW_MACRO_EXPECT_EQ_INT(f26, vertex->f26);
        AW_MACRO_EXPECT_EQ_INT(f27, vertex->f27);

        bool f28 = 0;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_BOOL, &f28, &vertex->f28, sizeof(f28));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        float f29 = 0;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_FLOAT, &f29, &vertex->f29, sizeof(f29));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        double f30 = 0.00;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_DOUBLE, &f30, &vertex->f30, sizeof(f30));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint8_t f31Bits[1] = {0x0};
        GmcBitMapT f31 = {0};
        f31.beginPos = 0;
        f31.endPos = 8 - 1;
        f31.bits = f31Bits;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_BITMAP, f31Bits, &vertex->f31, sizeof(f31Bits));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void GtSimplelabel5StructGetNewVersionProperty(GtSimplelabel5VertexT *vertex, int64_t index,
                                               int64_t updateValue = 0, bool isNull = false)
{
    uint64_t i = index + updateValue;
    uint8_t f32[SIMPLE_LABEL_ADD_FIXED_SIZE] = {0};
    if (isNull) {
        memset(f32, 0, SIMPLE_LABEL_ADD_FIXED_SIZE);
    } else {
        (void)snprintf((char *)f32, sizeof(f32), "f%01329d", i);
    }
    int32_t ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, f32, vertex->f32, SIMPLE_LABEL_ADD_FIXED_SIZE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void GtSimplelabel5StructGetMidVersionProperty(GtSimplelabel5VertexT *vertex, int64_t value,
                                               int64_t updateValue = 0, bool isNull = false)
{
    if (!isNull) {
        int64_t f14 = value + updateValue;
        int32_t ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &f14, &vertex->f14, sizeof(f14));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint64_t f15 = value + updateValue;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &f15, &vertex->f15, sizeof(f15));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        int32_t f16 = value + updateValue;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &f16, &vertex->f16, sizeof(f16));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint32_t f17 = value + updateValue;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &f17, &vertex->f17, sizeof(f17));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        int16_t f18 = (value + updateValue) & 0x7fff;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &f18, &vertex->f18, sizeof(f18));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint16_t f19 = (value + updateValue) & 0xffff;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &f19, &vertex->f19, sizeof(f19));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        int8_t f20 = (value + updateValue) & 0x7f;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_INT8, &f20, &vertex->f20, sizeof(f20));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint8_t f21 = (value + updateValue) & 0xff;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &f21, &vertex->f21, sizeof(f21));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint64_t f22 = value + updateValue;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_TIME, &f22, &vertex->f22, sizeof(f22));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint8_t f23[SIMPLE_LABEL2_FIXED_SIZE] = {0};
        for (int j = 0; j < SIMPLE_LABEL2_FIXED_SIZE; j++) {
            f23[j] = j;
        }
        ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, f23, &vertex->f23, sizeof(f23));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint8_t f24 = (value + updateValue) & 0x1f;
        uint16_t f25 = (value + updateValue) & 0x3ff;
        uint32_t f26 = (value + updateValue) & 0x1ffff;
        uint64_t f27 = (value + updateValue) & 0x1ffffffff;
        AW_MACRO_EXPECT_EQ_INT(f24, vertex->f24);
        AW_MACRO_EXPECT_EQ_INT(f25, vertex->f25);
        AW_MACRO_EXPECT_EQ_INT(f26, vertex->f26);
        AW_MACRO_EXPECT_EQ_INT(f27, vertex->f27);

        bool f28 = value + updateValue;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_BOOL, &f28, &vertex->f28, sizeof(f28));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        float f29 = value + updateValue;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_FLOAT, &f29, &vertex->f29, sizeof(f29));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        double f30 = value + updateValue;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_DOUBLE, &f30, &vertex->f30, sizeof(f30));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint8_t f31Bits[1] = {0xff};
        GmcBitMapT f31 = {0};
        f31.beginPos = 0;
        f31.endPos = 8 - 1;
        f31.bits = f31Bits;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_BITMAP, f31Bits, &vertex->f31, sizeof(f31Bits));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    } else {
        int64_t f14 = 0;
        int32_t ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &f14, &vertex->f14, sizeof(f14));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint64_t f15 = 0;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &f15, &vertex->f15, sizeof(f15));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        int32_t f16 = 0;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &f16, &vertex->f16, sizeof(f16));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint32_t f17 = 0;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &f17, &vertex->f17, sizeof(f17));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        int16_t f18 = 0;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &f18, &vertex->f18, sizeof(f18));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint16_t f19 = 0;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &f19, &vertex->f19, sizeof(f19));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        int8_t f20 = 0;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_INT8, &f20, &vertex->f20, sizeof(f20));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint8_t f21 = 0;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &f21, &vertex->f21, sizeof(f21));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint64_t f22 = 0;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_TIME, &f22, &vertex->f22, sizeof(f22));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint8_t f23[SIMPLE_LABEL2_FIXED_SIZE] = {0};
        for (int j = 0; j < SIMPLE_LABEL2_FIXED_SIZE; j++) {
            f23[j] = 0;
        }
        ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, f23, &vertex->f23, sizeof(f23));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint8_t f24 = 0;
        uint16_t f25 = 0;
        uint32_t f26 = 0;
        uint64_t f27 = 0;
        AW_MACRO_EXPECT_EQ_INT(f24, vertex->f24);
        AW_MACRO_EXPECT_EQ_INT(f25, vertex->f25);
        AW_MACRO_EXPECT_EQ_INT(f26, vertex->f26);
        AW_MACRO_EXPECT_EQ_INT(f27, vertex->f27);

        bool f28 = 0;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_BOOL, &f28, &vertex->f28, sizeof(f28));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        float f29 = 0;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_FLOAT, &f29, &vertex->f29, sizeof(f29));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        double f30 = 0.00;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_DOUBLE, &f30, &vertex->f30, sizeof(f30));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint8_t f31Bits[1] = {0x0};
        GmcBitMapT f31 = {0};
        f31.beginPos = 0;
        f31.endPos = 8 - 1;
        f31.bits = f31Bits;
        ret = CompareVertexPropertyValue(GMC_DATATYPE_BITMAP, f31Bits, &vertex->f31, sizeof(f31Bits));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void GtSimplelabel3StructGetNewProperty(GtSimplelabel3VertexT *vertex, int64_t value, int64_t coefficient,
                                        int64_t updateValue = 0)
{
    uint8_t fixed26[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)fixed26, sizeof(fixed26), "f%013310d", value + updateValue);
    int32_t ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, fixed26, vertex->f26, SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, fixed26, vertex->f27, SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, fixed26, vertex->f28, SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, fixed26, vertex->f29, SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, fixed26, vertex->f30, SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, fixed26, vertex->f31, SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, fixed26, vertex->f41, SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, fixed26, vertex->f51, SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, fixed26, vertex->f61, SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, fixed26, vertex->f71, SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, fixed26, vertex->f81, SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, fixed26, vertex->f91, SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, fixed26, vertex->f100, SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void GtSimplelabel1StructGetOldProperty(GtSimplelabelVertexT *vertex, int64_t index, int64_t coefficient,
                                        bool isDefaultVaule = true, int32_t updateValue = 0)
{
    int ret = 0;
    int64_t f0Value = index;
    uint64_t f1Value = index + updateValue;
    int32_t f2Value = index + updateValue;
    int16_t f4Value = (index + updateValue) % 32768;
    uint16_t f5Value = (index + updateValue) % 65536;
    uint64_t f7Value = index + updateValue;
    uint8_t fixedValue[SIMPLE_LABEL_FIXED_SIZE] = {0};
    uint8_t f9Value = 0;
    uint16_t f10Value = 0;
    uint8_t f13Value = (index + updateValue) & 0xf;
    if (!isDefaultVaule) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = j;
        }
        f9Value = (index + updateValue) % 31;
        f10Value = (index + updateValue) % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = 0xff;
        }
        f9Value = 0x1f;
        f10Value = 0x3ff;
    }
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &f0Value, &vertex->f0, sizeof(f0Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &f1Value, &vertex->f1, sizeof(f1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &f2Value, &vertex->f2, sizeof(f2Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &f4Value, &vertex->f4, sizeof(f4Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &f5Value, &vertex->f5, sizeof(f5Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_TIME, &f7Value, &vertex->f7, sizeof(f7Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, fixedValue, vertex->f8, SIMPLE_LABEL_FIXED_SIZE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(f9Value, vertex->f9);
    AW_MACRO_EXPECT_EQ_INT(f10Value, vertex->f10);
    AW_MACRO_EXPECT_EQ_INT(f13Value, vertex->f13);
}

void GtSimplelabel2StructGetOldProperty(GtSimplelabel2VertexT *vertex, int64_t index, int64_t coefficient,
                                        bool isDefaultVaule = true, int32_t updateValue = 0)
{
    int ret = 0;
    int64_t f0Value = index;
    uint64_t f1Value = index + updateValue;
    int32_t f2Value = index + updateValue;
    int16_t f4Value = (index + updateValue) % 32768;
    uint16_t f5Value = (index + updateValue) % 65536;
    uint64_t f7Value = index + updateValue;
    uint8_t fixedValue[SIMPLE_LABEL_FIXED_SIZE] = {0};
    uint8_t f9Value = 0;
    uint16_t f10Value = 0;
    uint8_t f13Value = (index + updateValue) & 0xf;
    if (!isDefaultVaule) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = j;
        }
        f9Value = (index + updateValue) % 31;
        f10Value = (index + updateValue) % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = 0xff;
        }
        f9Value = 0x1f;
        f10Value = 0x3ff;
    }
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &f0Value, &vertex->f0, sizeof(f0Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &f1Value, &vertex->f1, sizeof(f1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &f2Value, &vertex->f2, sizeof(f2Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &f4Value, &vertex->f4, sizeof(f4Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &f5Value, &vertex->f5, sizeof(f5Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_TIME, &f7Value, &vertex->f7, sizeof(f7Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, fixedValue, vertex->f8, SIMPLE_LABEL_FIXED_SIZE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(f9Value, vertex->f9);
    AW_MACRO_EXPECT_EQ_INT(f10Value, vertex->f10);
    AW_MACRO_EXPECT_EQ_INT(f13Value, vertex->f13);
}

void GtSimplelabel3StructGetOldProperty(GtSimplelabel3VertexT *vertex, int64_t index, int64_t coefficient,
                                        bool isDefaultVaule = true, int32_t updateValue = 0)
{
    int ret = 0;
    int64_t f0Value = index;
    uint64_t f1Value = index + updateValue;
    int32_t f2Value = index + updateValue;
    int16_t f4Value = (index + updateValue) % 32768;
    uint16_t f5Value = (index + updateValue) % 65536;
    uint64_t f7Value = index + updateValue;
    uint8_t fixedValue[SIMPLE_LABEL_FIXED_SIZE] = {0};
    uint8_t f9Value = 0;
    uint16_t f10Value = 0;
    uint8_t f13Value = (index + updateValue) & 0xf;
    if (!isDefaultVaule) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = j;
        }
        f9Value = (index + updateValue) % 31;
        f10Value = (index + updateValue) % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = 0xff;
        }
        f9Value = 0x1f;
        f10Value = 0x3ff;
    }
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &f0Value, &vertex->f0, sizeof(f0Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &f1Value, &vertex->f1, sizeof(f1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &f2Value, &vertex->f2, sizeof(f2Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &f4Value, &vertex->f4, sizeof(f4Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &f5Value, &vertex->f5, sizeof(f5Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_TIME, &f7Value, &vertex->f7, sizeof(f7Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, fixedValue, vertex->f8, SIMPLE_LABEL_FIXED_SIZE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(f9Value, vertex->f9);
    AW_MACRO_EXPECT_EQ_INT(f10Value, vertex->f10);
    AW_MACRO_EXPECT_EQ_INT(f13Value, vertex->f13);
    uint8_t fixed23[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)fixed23, sizeof(fixed23), "f%013310d", index + updateValue);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, fixed23, vertex->f23, SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, fixed23, vertex->f24, SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, fixed23, vertex->f25, SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void GtSimplelabel1StructVertexBuf(GmcStructBufferT *inputBufInfo, int64_t coefficient,
                                   bool isDefaultVaule = true, int32_t updateValue = 0)
{
    int ret = 0;
    GtSimplelabelVertexT *vertex = (GtSimplelabelVertexT *)inputBufInfo->buf;
    int64_t index = vertex->f0;
    GtSimplelabel1StructGetOldProperty(vertex, index, coefficient, isDefaultVaule, updateValue);
    uint64_t f14Value = index + updateValue;
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &f14Value, &vertex->f14, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void GtSimplelabel2StructVertexBuf(GmcStructBufferT *inputBufInfo, int64_t coefficient,
                                   bool isDefaultVaule = true, int32_t updateValue = 0)
{
    int ret = 0;
    GtSimplelabel2VertexT *vertex = (GtSimplelabel2VertexT *)inputBufInfo->buf;
    int64_t index = vertex->f0;
    GtSimplelabel2StructGetOldProperty(vertex, index, coefficient, isDefaultVaule, updateValue);
    GtSimplelabel2StructGetNewProperty(vertex, index, coefficient, updateValue);
}

void GtSimplelabel3StructVertexBuf(GmcStructBufferT *inputBufInfo, int64_t coefficient,
                                   bool isDefaultVaule = true, int32_t updateValue = 0)
{
    int ret = 0;
    GtSimplelabel3VertexT *vertex = (GtSimplelabel3VertexT *)inputBufInfo->buf;
    int64_t index = vertex->f0;
    GtSimplelabel3StructGetOldProperty(vertex, index, coefficient, isDefaultVaule, updateValue);
    GtSimplelabel3StructGetNewProperty(vertex, index, coefficient, updateValue);
}

// 以结构化的方式 replace Simplelabel 表的数据
int GtSimplelabel1StructInsertOrReplace(GmcStmtT *stmt, const char *labelName, GtSimplelabelStructCfgT vertexCfg,
                                        uint32_t schemaVersion, GmcOperationTypeE operationType,
                                        bool isDefaultValue = true)
{
    int ret = GMERR_OK;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;

    GtSimplelabelVertexT *vertex = (GtSimplelabelVertexT *)malloc(sizeof(GtSimplelabelVertexT));
    if (vertex == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
        return 1;
    }
    (void)memset(vertex, 0, sizeof(GtSimplelabelVertexT));
    TestLabelInfoT labelInfo = {(char *)labelName, schemaVersion, g_testNameSpace};

    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
        RETURN_IFERR(ret);
        GtSimplelabel1StructSetPk(vertex, i);
        GtSimplelabel1StructSetLmpProperty(vertex, i);
        GtSimplelabel1StructSetOldProperty(vertex, i, coefficient, isDefaultValue);
        GtSimplelabel1StructSetNewProperty(vertex, i, coefficient);
        ret = testStructSetVertexWithBuf(stmt, vertex, &labelInfo);
        RETURN_IFERR(ret);

        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = TestGetAffactRows(stmt, expAffectRows);
        RETURN_IFERR(ret);
    }
    free(vertex);
    return GMERR_OK;
}

// 以结构化的方式 merge or update表的数据
int GtSimplelabel1StructMergeOrUpdate(GmcStmtT *stmt, const char *labelName, GtSimplelabelStructCfgT vertexCfg,
                                      uint32_t schemaVersion, GmcOperationTypeE operationType,
                                      bool isDefaultValue = true, int32_t updateValue = 0)
{
    int ret = GMERR_OK;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;

    GtSimplelabelVertexT *vertex = (GtSimplelabelVertexT *)malloc(sizeof(GtSimplelabelVertexT));
    if (vertex == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
        return 1;
    }
    (void)memset(vertex, 0, sizeof(GtSimplelabelVertexT));
    TestLabelInfoT labelInfo = {(char *)labelName, schemaVersion, g_testNameSpace};

    for (int64_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
        RETURN_IFERR(ret);
        GtSimplelabel1StructSetPk(vertex, i);
        ret = testStructSetIndexKeyWithBuf(stmt, vertex, 0, NULL, &labelInfo);
        RETURN_IFERR(ret);
        if (operationType == GMC_OPERATION_MERGE && expAffectRows == 1) {
            TestSimpleT1SetLpmProperty(stmt, i);
        }
        TestSimpleT1UpdateSetProperty(stmt, i + updateValue, isDefaultValue);
        TestSimpleT1NewFieldSetOk(stmt, i + updateValue);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = TestGetAffactRows(stmt, expAffectRows);
        RETURN_IFERR(ret);
    }
    free(vertex);
    return GMERR_OK;
}

int GtSimplelabel1StructIndexScan(GmcStmtT *stmt, const char *labelName, GtSimplelabelStructCfgT vertexCfg,
                                  uint32_t keyId, uint32_t schemaVersion,
                                  bool isDefaultValue = true, int64_t updateValue = 0)
{
    int ret, i;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;
    GtSimplelabelVertexT vertex = (GtSimplelabelVertexT){0};
    TestLabelInfoT labelInfo = {(char *)labelName, schemaVersion, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &vertex, &deseri, &deseriCtx, false, &labelInfo);
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_SCAN);
    RETURN_IFERR(ret);
    for (i = startPkVal; i < startPkVal + vertexCount; i++) {
        if (keyId == 0) {
            GtSimplelabel1StructSetPk(&vertex, i);
        } else if (keyId == 1) {
            GtSimplelabel1StructSetHashcluster(&vertex, i, updateValue);
        } else if (keyId == 2) {
            GtSimplelabel1StructSetLocalhash(&vertex, i, updateValue);
        } else if (keyId == 3) {
            GtSimplelabel1StructSetLocal(&vertex, 0);
        } else if (keyId == 4) {
            GtSimplelabel1StructSetLpm4(&vertex, i);
        }
        ret = testStructSetIndexKeyWithBuf(stmt, &vertex, keyId, NULL, &labelInfo);
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        bool isFinish = false;
        uint32_t cnt = 0;
        if (keyId == 3) {
            if (i == startPkVal) {
                while (!isFinish) {
                    ret = GmcFetch(stmt, &isFinish);
                    RETURN_IFERR(ret);
                    if (isFinish) {
                        break;
                    }
                    cnt++;
                    ret = testStructGetVertexDeseri(stmt, &deseri);
                    RETURN_IFERR(ret);
                    int64_t f0Value = vertex.f0;
                    GtSimplelabel1StructGetLmpProperty(&vertex, f0Value);
                    GtSimplelabel1StructGetNewProperty(&vertex, f0Value, coefficient, updateValue);
                    GtSimplelabel1StructGetOldProperty(&vertex, f0Value, coefficient, isDefaultValue, updateValue);
                }
                AW_MACRO_EXPECT_EQ_INT(vertexCfg.count, cnt);
                return GMERR_OK;
            }
        } else {
            ret = GmcFetch(stmt, &isFinish);
            RETURN_IFERR(ret);
            ret = testStructGetVertexDeseri(stmt, &deseri);
            RETURN_IFERR(ret);
            GtSimplelabel1StructGetLmpProperty(&vertex, i);
            if (schemaVersion == 0) {
            } else if (schemaVersion == 1) {
                GtSimplelabel1StructGetNewProperty(&vertex, i, coefficient, updateValue);
            }
            GtSimplelabel1StructGetOldProperty(&vertex, i, coefficient, isDefaultValue, updateValue);
            ret = GmcFetch(stmt, &isFinish);
            RETURN_IFERR(ret);
            AW_MACRO_EXPECT_EQ_INT(true, isFinish);
        }
    }
    deSeriFreeDynMem(&deseriCtx, true);
    return GMERR_OK;
}

int GtSimplelabel1StructLocalRangScan(GmcStmtT *stmt, const char *labelName, GtSimplelabelStructCfgT vertexCfg,
    uint32_t schemaVersion, int32_t *fetchNum, bool isDefaultValue = true, int64_t updateValue = 0)
{
    int ret = 0;
    uint32_t keyId = 3;
    GtSimplelabelVertexT lKey = (GtSimplelabelVertexT){0};
    GtSimplelabelVertexT rKey = (GtSimplelabelVertexT){0};
    GtSimplelabelVertexT *leftKey = NULL;
    GtSimplelabelVertexT *rightKey = NULL;
    GtSimplelabelVertexT vertex = (GtSimplelabelVertexT){0};
    GmcStructBufferT inputBufInfo = (GmcStructBufferT){0};
    GmcSeriT keySeri = (GmcSeriT){0};
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;
    TestLabelInfoT labelInfo = {(char *)labelName, schemaVersion, g_testNameSpace};

    unsigned int arrLen = 1;
    leftKey = &lKey;
    rightKey = &rKey;
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_SCAN);
    RETURN_IFERR(ret);

    GtSimplelabel1StructSetLocal(leftKey, startPkVal);
    GtSimplelabel1StructSetLocal(rightKey, startPkVal + vertexCount);
    GmcRangeItemFlagT items[arrLen];
    items[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].order = GMC_ORDER_ASC;
    ret = testStructSetKeyRangeStructure(stmt, leftKey, rightKey, items, arrLen, keyId, NULL, &labelInfo);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = testStructGetVertexBuf(stmt, &vertex, keyId, &keySeri, &inputBufInfo, &labelInfo);
        RETURN_IFERR(ret);
        GtSimplelabel1StructVertexBuf(&inputBufInfo, coefficient, isDefaultValue, updateValue);
        (*fetchNum)++;
    }
    return ret;
}

int GtSimplelabel0StructInsertOrReplace(GmcStmtT *stmt, const char *labelName, GtSimplelabelStructCfgT vertexCfg,
                                        uint32_t schemaVersion, GmcOperationTypeE operationType,
                                        bool isDefaultValue = true)
{
    int ret = GMERR_OK;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;

    GtSimplelabel0VertexT *vertex = (GtSimplelabel0VertexT *)malloc(sizeof(GtSimplelabel0VertexT));
    if (vertex == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
        return 1;
    }
    (void)memset(vertex, 0, sizeof(GtSimplelabel0VertexT));
    TestLabelInfoT labelInfo = {(char *)labelName, schemaVersion, g_testNameSpace};

    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
        RETURN_IFERR(ret);
        GtSimplelabel0StructSetPk(vertex, i);
        GtSimplelabel0StructSetLpm4(vertex, i);
        GtSimplelabel0StructSetOldProperty(vertex, i, coefficient, isDefaultValue);
        ret = testStructSetVertexWithBuf(stmt, vertex, &labelInfo);
        RETURN_IFERR(ret);

        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = TestGetAffactRows(stmt, expAffectRows);
        RETURN_IFERR(ret);
    }
    free(vertex);
    return GMERR_OK;
}

int GtSimplelabel2StructInsertOrReplace(GmcStmtT *stmt, const char *labelName, GtSimplelabelStructCfgT vertexCfg,
                                        uint32_t schemaVersion, GmcOperationTypeE operationType,
                                        bool isDefaultValue = true)
{
    int ret = GMERR_OK;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;

    GtSimplelabel2VertexT *vertex = (GtSimplelabel2VertexT *)malloc(sizeof(GtSimplelabel2VertexT));
    if (vertex == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
        return 1;
    }
    (void)memset(vertex, 0, sizeof(GtSimplelabel2VertexT));
    TestLabelInfoT labelInfo = {(char *)labelName, schemaVersion, g_testNameSpace};

    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
        RETURN_IFERR(ret);
        GtSimplelabel2StructSetPk(vertex, i);
        GtSimplelabel2StructSetLmpProperty(vertex, i);
        GtSimplelabel2StructSetOldProperty(vertex, i, coefficient, isDefaultValue);
        GtSimplelabel2StructSetNewProperty(vertex, i, coefficient);
        ret = testStructSetVertexWithBuf(stmt, vertex, &labelInfo);
        RETURN_IFERR(ret);

        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = TestGetAffactRows(stmt, expAffectRows);
        RETURN_IFERR(ret);
    }
    free(vertex);
    return GMERR_OK;
}

int GtSimplelabel5StructInsertOrReplace(GmcStmtT *stmt, const char *labelName, GtSimplelabelStructCfgT vertexCfg,
                                        uint32_t schemaVersion, GmcOperationTypeE operationType,
                                        bool isDefaultValue = true)
{
    int ret = GMERR_OK;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;

    GtSimplelabel5VertexT *vertex = (GtSimplelabel5VertexT *)malloc(sizeof(GtSimplelabel5VertexT));
    if (vertex == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
        return 1;
    }
    (void)memset(vertex, 0, sizeof(GtSimplelabel5VertexT));
    TestLabelInfoT labelInfo = {(char *)labelName, schemaVersion, g_testNameSpace};

    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
        RETURN_IFERR(ret);
        GtSimplelabel5StructSetPk(vertex, i);
        GtSimplelabel5StructSetLpm4(vertex, i);
        GtSimplelabel5StructSetOldProperty(vertex, i, isDefaultValue);
        GtSimplelabel5StructSetMidVersionProperty(vertex, i);
        GtSimplelabel5StructSetNewVersionProperty(vertex, i);
        ret = testStructSetVertexWithBuf(stmt, vertex, &labelInfo);
        RETURN_IFERR(ret);

        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = TestGetAffactRows(stmt, expAffectRows);
        RETURN_IFERR(ret);
    }
    free(vertex);
    return GMERR_OK;
}

// 以结构化的方式 merge or update表的数据
int GtSimplelabel2StructMergeOrUpdate(GmcStmtT *stmt, const char *labelName, GtSimplelabelStructCfgT vertexCfg,
                                      uint32_t schemaVersion, GmcOperationTypeE operationType,
                                      bool isDefaultValue = true, int32_t updateValue = 0)
{
    int ret = GMERR_OK;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;

    GtSimplelabel2VertexT *vertex = (GtSimplelabel2VertexT *)malloc(sizeof(GtSimplelabel2VertexT));
    if (vertex == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
        return 1;
    }
    (void)memset(vertex, 0, sizeof(GtSimplelabel2VertexT));
    TestLabelInfoT labelInfo = {(char *)labelName, schemaVersion, g_testNameSpace};

    for (int64_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
        RETURN_IFERR(ret);
        GtSimplelabel2StructSetPk(vertex, i);
        ret = testStructSetIndexKeyWithBuf(stmt, vertex, 0, NULL, &labelInfo);
        RETURN_IFERR(ret);
        if (operationType == GMC_OPERATION_MERGE && expAffectRows == 1) {
            TestSimpleT1SetLpmProperty(stmt, i);
        }
        TestSimpleT1UpdateSetProperty(stmt, i + updateValue, isDefaultValue);
        TestSimpleT2NewFieldSetOk(stmt, i + updateValue);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = TestGetAffactRows(stmt, expAffectRows);
        RETURN_IFERR(ret);
    }
    free(vertex);
    return GMERR_OK;
}

int GtSimplelabel0StructIndexScan(GmcStmtT *stmt, const char *labelName, GtSimplelabelStructCfgT vertexCfg,
                                  uint32_t keyId, uint32_t schemaVersion,
                                  bool isDefaultValue = true, int64_t updateValue = 0)
{
    int ret, i;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;
    GtSimplelabel0VertexT vertex = (GtSimplelabel0VertexT){0};
    TestLabelInfoT labelInfo = {(char *)labelName, schemaVersion, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &vertex, &deseri, &deseriCtx, false, &labelInfo);

    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_SCAN);
    RETURN_IFERR(ret);
    for (i = startPkVal; i < startPkVal + vertexCount; i++) {
        if (keyId == 0) {
            GtSimplelabel0StructSetPk(&vertex, i);
        } else if (keyId == 1) {
            GtSimplelabel0StructSetHashcluster(&vertex, i, updateValue);
        } else if (keyId == 2) {
            GtSimplelabel0StructSetLocalhash(&vertex, i, updateValue);
        } else if (keyId == 3) {
            GtSimplelabel0StructSetLocal(&vertex, 0);
        } else if (keyId == 4) {
            GtSimplelabel0StructSetLpm4(&vertex, i);
        }
        ret = testStructSetIndexKeyWithBuf(stmt, &vertex, keyId, NULL, &labelInfo);
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        bool isFinish = false;
        uint32_t cnt = 0;
        if (keyId == 3) {
            if (i == startPkVal) {
                while (!isFinish) {
                    ret = GmcFetch(stmt, &isFinish);
                    RETURN_IFERR(ret);
                    if (isFinish) {
                        break;
                    }
                    cnt++;
                    ret = testStructGetVertexDeseri(stmt, &deseri);
                    RETURN_IFERR(ret);
                    int64_t f0Value = vertex.f0;
                    GtSimplelabel0StructGetLmpProperty(&vertex, f0Value);
                    GtSimplelabel0StructGetOldProperty(&vertex, f0Value, coefficient, isDefaultValue, updateValue);
                }
                AW_MACRO_EXPECT_EQ_INT(expAffectRows, cnt);
                return GMERR_OK;
            }
        } else {
            ret = GmcFetch(stmt, &isFinish);
            RETURN_IFERR(ret);
            ret = testStructGetVertexDeseri(stmt, &deseri);
            RETURN_IFERR(ret);
            GtSimplelabel0StructGetLmpProperty(&vertex, i);
            GtSimplelabel0StructGetOldProperty(&vertex, i, coefficient, isDefaultValue, updateValue);
            ret = GmcFetch(stmt, &isFinish);
            RETURN_IFERR(ret);
            AW_MACRO_EXPECT_EQ_INT(true, isFinish);
        }
    }
    deSeriFreeDynMem(&deseriCtx, true);
    return GMERR_OK;
}

int GtSimplelabel2StructIndexScan(GmcStmtT *stmt, const char *labelName, GtSimplelabelStructCfgT vertexCfg,
                                  uint32_t keyId, uint32_t schemaVersion, bool isDefaultValue = true,
                                  int64_t updateValue = 0, bool isNull = false)
{
    int ret, i;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;
    GtSimplelabel2VertexT vertex = (GtSimplelabel2VertexT){0};
    TestLabelInfoT labelInfo = {(char *)labelName, schemaVersion, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &vertex, &deseri, &deseriCtx, false, &labelInfo);

    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_SCAN);
    RETURN_IFERR(ret);
    for (i = startPkVal; i < startPkVal + vertexCount; i++) {
        if (keyId == 0) {
            GtSimplelabel2StructSetPk(&vertex, i);
        } else if (keyId == 1) {
            GtSimplelabel2StructSetHashcluster(&vertex, i, updateValue);
        } else if (keyId == 2) {
            GtSimplelabel2StructSetLocalhash(&vertex, i, updateValue);
        } else if (keyId == 3) {
            GtSimplelabel2StructSetLocal(&vertex, 0);
        } else if (keyId == 4) {
            GtSimplelabel2StructSetLpm4(&vertex, i);
        }
        ret = testStructSetIndexKeyWithBuf(stmt, &vertex, keyId, NULL, &labelInfo);
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        bool isFinish = false;
        uint32_t cnt = 0;
        if (keyId == 3) {
            if (i == startPkVal) {
                while (!isFinish) {
                    ret = GmcFetch(stmt, &isFinish);
                    RETURN_IFERR(ret);
                    if (isFinish) {
                        break;
                    }
                    cnt++;
                    ret = testStructGetVertexDeseri(stmt, &deseri);
                    RETURN_IFERR(ret);
                    int64_t f0Value = vertex.f0;
                    if (f0Value >= startPkVal &&  f0Value < startPkVal + vertexCount) {
                        GtSimplelabel2StructGetLmpProperty(&vertex, f0Value);
                        GtSimplelabel2StructGetNewProperty(&vertex, f0Value, coefficient, updateValue, isNull);
                        GtSimplelabel2StructGetOldProperty(&vertex, f0Value, coefficient, isDefaultValue, updateValue);
                    }
                }
                if (cnt != vertexCount && cnt != expAffectRows) {
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                }
                return GMERR_OK;
            }
        } else {
            ret = GmcFetch(stmt, &isFinish);
            RETURN_IFERR(ret);
            ret = testStructGetVertexDeseri(stmt, &deseri);
            RETURN_IFERR(ret);
            GtSimplelabel2StructGetLmpProperty(&vertex, i);
            GtSimplelabel2StructGetNewProperty(&vertex, i, coefficient, updateValue, isNull);
            GtSimplelabel2StructGetOldProperty(&vertex, i, coefficient, isDefaultValue, updateValue);
            ret = GmcFetch(stmt, &isFinish);
            RETURN_IFERR(ret);
            AW_MACRO_EXPECT_EQ_INT(true, isFinish);
        }
    }
    deSeriFreeDynMem(&deseriCtx, true);
    return GMERR_OK;
}

int GtSimplelabel2StructLocalRangScan(GmcStmtT *stmt, const char *labelName, GtSimplelabelStructCfgT vertexCfg,
                                      uint32_t schemaVersion, int32_t *fetchNum,
                                      bool isDefaultValue = true, int64_t updateValue = 0)
{
    int ret = 0;
    uint32_t keyId = 3;
    GtSimplelabel2VertexT lKey = (GtSimplelabel2VertexT){0};
    GtSimplelabel2VertexT rKey = (GtSimplelabel2VertexT){0};
    GtSimplelabel2VertexT *leftKey = NULL;
    GtSimplelabel2VertexT *rightKey = NULL;
    GtSimplelabel2VertexT vertex = (GtSimplelabel2VertexT){0};
    GmcStructBufferT inputBufInfo = (GmcStructBufferT){0};
    GmcSeriT keySeri = (GmcSeriT){0};
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;
    TestLabelInfoT labelInfo = {(char *)labelName, schemaVersion, g_testNameSpace};

    unsigned int arrLen = 1;
    leftKey = &lKey;
    rightKey = &rKey;
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_SCAN);
    RETURN_IFERR(ret);

    GtSimplelabel2StructSetLocal(leftKey, startPkVal);
    GtSimplelabel2StructSetLocal(rightKey, startPkVal + vertexCount);
    GmcRangeItemFlagT items[arrLen];
    items[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].order = GMC_ORDER_ASC;
    ret = testStructSetKeyRangeStructure(stmt, leftKey, rightKey, items, arrLen, keyId, NULL, &labelInfo);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = testStructGetVertexBuf(stmt, &vertex, keyId, &keySeri, &inputBufInfo, &labelInfo);
        RETURN_IFERR(ret);
        GtSimplelabel2StructVertexBuf(&inputBufInfo, coefficient, isDefaultValue, updateValue);
        (*fetchNum)++;
    }
    return ret;
}

void GtSimplelabel5StructGetOldProperty(GtSimplelabel5VertexT *vertex, int64_t index,
                                        bool isDefaultVaule = true, int32_t updateValue = 0)
{
    int ret = 0;
    int64_t f0Value = index;
    uint64_t f1Value = index + updateValue;
    int32_t f2Value = index + updateValue;
    int16_t f4Value = (index + updateValue) % 32768;
    uint16_t f5Value = (index + updateValue) % 65536;
    uint64_t f7Value = index + updateValue;
    uint8_t fixedValue[SIMPLE_LABEL_FIXED_SIZE] = {0};
    uint8_t f9Value = 0;
    uint16_t f10Value = 0;
    uint8_t f13Value = (index + updateValue) & 0xf;
    if (!isDefaultVaule) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = j;
        }
        f9Value = (index + updateValue) % 31;
        f10Value = (index + updateValue) % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = 0xff;
        }
        f9Value = 0x1f;
        f10Value = 0x3ff;
    }
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &f0Value, &vertex->f0, sizeof(f0Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &f1Value, &vertex->f1, sizeof(f1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &f2Value, &vertex->f2, sizeof(f2Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &f4Value, &vertex->f4, sizeof(f4Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &f5Value, &vertex->f5, sizeof(f5Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_TIME, &f7Value, &vertex->f7, sizeof(f7Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, fixedValue, vertex->f8, SIMPLE_LABEL_FIXED_SIZE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(f9Value, vertex->f9);
    AW_MACRO_EXPECT_EQ_INT(f10Value, vertex->f10);
    AW_MACRO_EXPECT_EQ_INT(f13Value, vertex->f13);
}

int GtSimplelabel5StructIndexScan(GmcStmtT *stmt, const char *labelName, GtSimplelabelStructCfgT vertexCfg,
                                  uint32_t keyId, uint32_t schemaVersion, bool isNull[2], bool isDefaultValue = true,
                                  int64_t updateValue = 0)
{
    int ret, i;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;
    GtSimplelabel5VertexT vertex = (GtSimplelabel5VertexT){0};
    TestLabelInfoT labelInfo = {(char *)labelName, schemaVersion, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &vertex, &deseri, &deseriCtx, false, &labelInfo);

    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_SCAN);
    RETURN_IFERR(ret);
    for (i = startPkVal; i < startPkVal + vertexCount; i++) {
        if (keyId == 0) {
            GtSimplelabel5StructSetPk(&vertex, i);
        } else if (keyId == 1) {
            GtSimplelabel5StructSetHashcluster(&vertex, i, updateValue);
        } else if (keyId == 2) {
            GtSimplelabel5StructSetLocalhash(&vertex, i, updateValue);
        } else if (keyId == 3) {
            GtSimplelabel5StructSetLocal(&vertex, 0);
        } else if (keyId == 4) {
            GtSimplelabel5StructSetLpm4(&vertex, i);
        }
        ret = testStructSetIndexKeyWithBuf(stmt, &vertex, keyId, NULL, &labelInfo);
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        bool isFinish = false;
        uint32_t cnt = 0;
        if (keyId == 3) {
            if (i == startPkVal) {
                while (!isFinish) {
                    ret = GmcFetch(stmt, &isFinish);
                    RETURN_IFERR(ret);
                    if (isFinish) {
                        break;
                    }
                    cnt++;
                    ret = testStructGetVertexDeseri(stmt, &deseri);
                    RETURN_IFERR(ret);
                    int64_t f0Value = vertex.f0;
                    if (f0Value >= startPkVal &&  f0Value < startPkVal + vertexCount) {
                        GtSimplelabel5StructGetLmpProperty(&vertex, f0Value);
                        GtSimplelabel5StructGetNewVersionProperty(&vertex, f0Value, updateValue, isNull[1]);
                        GtSimplelabel5StructGetMidVersionProperty(&vertex, f0Value, updateValue, isNull[0]);
                        GtSimplelabel5StructGetOldProperty(&vertex, f0Value, isDefaultValue, updateValue);
                    }
                }
                if (cnt != vertexCount && cnt != expAffectRows) {
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                }
                return GMERR_OK;
            }
        } else {
            ret = GmcFetch(stmt, &isFinish);
            RETURN_IFERR(ret);
            ret = testStructGetVertexDeseri(stmt, &deseri);
            RETURN_IFERR(ret);
            GtSimplelabel5StructGetLmpProperty(&vertex, i);
            GtSimplelabel5StructGetNewVersionProperty(&vertex, i, updateValue, isNull[1]);
            GtSimplelabel5StructGetMidVersionProperty(&vertex, i, updateValue, isNull[0]);
            GtSimplelabel5StructGetOldProperty(&vertex, i, isDefaultValue, updateValue);
            ret = GmcFetch(stmt, &isFinish);
            RETURN_IFERR(ret);
            AW_MACRO_EXPECT_EQ_INT(true, isFinish);
        }
    }
    deSeriFreeDynMem(&deseriCtx, true);
    return GMERR_OK;
}

int GtSimplelabel3StructInsertOrReplace(GmcStmtT *stmt, const char *labelName, GtSimplelabelStructCfgT vertexCfg,
                                        uint32_t schemaVersion, GmcOperationTypeE operationType,
                                        bool isDefaultValue = true)
{
    int ret = GMERR_OK;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;

    GtSimplelabel3VertexT *vertex = (GtSimplelabel3VertexT *)malloc(sizeof(GtSimplelabel3VertexT));
    if (vertex == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
        return 1;
    }
    (void)memset(vertex, 0, sizeof(GtSimplelabel3VertexT));
    TestLabelInfoT labelInfo = {(char *)labelName, schemaVersion, g_testNameSpace};

    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
        RETURN_IFERR(ret);
        GtSimplelabel3StructSetPk(vertex, i);
        GtSimplelabel3StructSetLmpProperty(vertex, i);
        GtSimplelabel3StructSetOldProperty(vertex, i, coefficient, isDefaultValue);
        GtSimplelabel3StructSetNewProperty(vertex, i, coefficient);
        ret = testStructSetVertexWithBuf(stmt, vertex, &labelInfo);
        RETURN_IFERR(ret);

        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = TestGetAffactRows(stmt, expAffectRows);
        RETURN_IFERR(ret);
    }
    free(vertex);
    return GMERR_OK;
}

// 以结构化的方式 merge or update表的数据
int GtSimplelabel3StructMergeOrUpdate(GmcStmtT *stmt, const char *labelName, GtSimplelabelStructCfgT vertexCfg,
                                      uint32_t schemaVersion, GmcOperationTypeE operationType,
                                      bool isDefaultValue = true, int32_t updateValue = 0)
{
    int ret = GMERR_OK;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;

    GtSimplelabel3VertexT *vertex = (GtSimplelabel3VertexT *)malloc(sizeof(GtSimplelabel3VertexT));
    if (vertex == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
        return 1;
    }
    (void)memset(vertex, 0, sizeof(GtSimplelabel3VertexT));
    TestLabelInfoT labelInfo = {(char *)labelName, schemaVersion, g_testNameSpace};

    for (int64_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
        RETURN_IFERR(ret);
        GtSimplelabel3StructSetPk(vertex, i);
        ret = testStructSetIndexKeyWithBuf(stmt, vertex, 0, NULL, &labelInfo);
        RETURN_IFERR(ret);
        if (operationType == GMC_OPERATION_MERGE && expAffectRows == 1) {
            TestSimpleT1SetLpmProperty(stmt, i);
        }
        TestSimpleT1UpdateSetProperty(stmt, i + updateValue, isDefaultValue);
        TestSimpleT3NewFieldSetOk(stmt, i + updateValue);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = TestGetAffactRows(stmt, expAffectRows);
        RETURN_IFERR(ret);
    }
    free(vertex);
    return GMERR_OK;
}

int GtSimplelabel3StructIndexScan(GmcStmtT *stmt, const char *labelName, GtSimplelabelStructCfgT vertexCfg,
                                  uint32_t keyId, uint32_t schemaVersion,
                                  bool isDefaultValue = true, int64_t updateValue = 0)
{
    int ret, i;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;
    GtSimplelabel3VertexT vertex = (GtSimplelabel3VertexT){0};
    TestLabelInfoT labelInfo = {(char *)labelName, schemaVersion, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &vertex, &deseri, &deseriCtx, false, &labelInfo);

    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_SCAN);
    RETURN_IFERR(ret);
    for (i = startPkVal; i < startPkVal + vertexCount; i++) {
        if (keyId == 0) {
            GtSimplelabel3StructSetPk(&vertex, i);
        } else if (keyId == 1) {
            GtSimplelabel3StructSetHashcluster(&vertex, i, updateValue);
        } else if (keyId == 2) {
            GtSimplelabel3StructSetLocalhash(&vertex, i, updateValue);
        } else if (keyId == 3) {
            GtSimplelabel3StructSetLocal(&vertex, 0);
        } else if (keyId == 4) {
            GtSimplelabel3StructSetLpm4(&vertex, i);
        }
        ret = testStructSetIndexKeyWithBuf(stmt, &vertex, keyId, NULL, &labelInfo);
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        bool isFinish = false;
        uint32_t cnt = 0;
        if (keyId == 3) {
            if (i == startPkVal) {
                while (!isFinish) {
                    ret = GmcFetch(stmt, &isFinish);
                    RETURN_IFERR(ret);
                    if (isFinish) {
                        break;
                    }
                    cnt++;
                    ret = testStructGetVertexDeseri(stmt, &deseri);
                    RETURN_IFERR(ret);
                    int64_t f0Value = vertex.f0;
                    GtSimplelabel3StructGetLmpProperty(&vertex, f0Value);
                    GtSimplelabel3StructGetNewProperty(&vertex, f0Value, coefficient, updateValue);
                    GtSimplelabel3StructGetOldProperty(&vertex, f0Value, coefficient, isDefaultValue, updateValue);
                }
                AW_MACRO_EXPECT_EQ_INT(vertexCfg.count, cnt);
                return 0;
            }
        } else {
            ret = GmcFetch(stmt, &isFinish);
            RETURN_IFERR(ret);
            ret = testStructGetVertexDeseri(stmt, &deseri);
            RETURN_IFERR(ret);
            GtSimplelabel3StructGetLmpProperty(&vertex, i);
            GtSimplelabel3StructGetNewProperty(&vertex, i, coefficient, updateValue);
            GtSimplelabel3StructGetOldProperty(&vertex, i, coefficient, isDefaultValue, updateValue);
            ret = GmcFetch(stmt, &isFinish);
            RETURN_IFERR(ret);
            AW_MACRO_EXPECT_EQ_INT(true, isFinish);
        }
    }
    deSeriFreeDynMem(&deseriCtx, true);
    return GMERR_OK;
}

int GtSimplelabel3StructLocalRangScan(GmcStmtT *stmt, const char *labelName, GtSimplelabelStructCfgT vertexCfg,
                                      uint32_t schemaVersion, int32_t *fetchNum,
                                      bool isDefaultValue = true, int64_t updateValue = 0)
{
    int ret = 0;
    uint32_t keyId = 3;
    GtSimplelabel3VertexT lKey = (GtSimplelabel3VertexT){0};
    GtSimplelabel3VertexT rKey = (GtSimplelabel3VertexT){0};
    GtSimplelabel3VertexT *leftKey = NULL;
    GtSimplelabel3VertexT *rightKey = NULL;
    GtSimplelabel3VertexT vertex = (GtSimplelabel3VertexT){0};
    GmcStructBufferT inputBufInfo = (GmcStructBufferT){0};
    GmcSeriT keySeri = (GmcSeriT){0};
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;
    TestLabelInfoT labelInfo = {(char *)labelName, schemaVersion, g_testNameSpace};

    unsigned int arrLen = 1;
    leftKey = &lKey;
    rightKey = &rKey;
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_SCAN);
    RETURN_IFERR(ret);

    GtSimplelabel3StructSetLocal(leftKey, startPkVal);
    GtSimplelabel3StructSetLocal(rightKey, startPkVal + vertexCount);
    GmcRangeItemFlagT items[arrLen];
    items[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].order = GMC_ORDER_ASC;
    ret = testStructSetKeyRangeStructure(stmt, leftKey, rightKey, items, arrLen, keyId, NULL, &labelInfo);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = testStructGetVertexBuf(stmt, &vertex, keyId, &keySeri, &inputBufInfo, &labelInfo);
        RETURN_IFERR(ret);
        GtSimplelabel3StructVertexBuf(&inputBufInfo, coefficient, isDefaultValue, updateValue);
        (*fetchNum)++;
    }
    return ret;
}

void GtSimplelabel3StructBatchWrite(GmcConnT *conn, GmcStmtT *stmt, GtSimplelabelCfgT vertexCfg, uint32_t schemaVersion,
                                    bool isDefaultValue = true)
{
    int ret;
    GtSimplelabel3VertexT vertex = (GtSimplelabel3VertexT){0};
    TestLabelInfoT labelInfo = {g_labelName, schemaVersion, g_testNameSpace};
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t coefficient = vertexCfg.coefficient;
    GmcOperationTypeE optType = vertexCfg.optType;

    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, schemaVersion, optType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI); // semi会合并成一个事务操作，所以total = 1
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int64_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        GtSimplelabel3StructSetPk(&vertex, i);
        if (optType == GMC_OPERATION_MERGE) {
            ret = testStructSetIndexKeyWithBuf(stmt, &vertex, 0, NULL, &labelInfo);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (expAffectRows == 1) {
            TestSimpleT1SetLpmProperty(stmt, i);
            }
            TestSimpleT1UpdateSetProperty(stmt, i, isDefaultValue);
            TestSimpleT3NewFieldSetOk(stmt, i);
        } else {
            GtSimplelabel3StructSetLmpProperty(&vertex, i);
            GtSimplelabel3StructSetOldProperty(&vertex, i, coefficient, isDefaultValue);
            GtSimplelabel3StructSetNewProperty(&vertex, i, coefficient);
            ret = testStructSetVertexWithBuf(stmt, &vertex, &labelInfo);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchExecute(batch, &batchRet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t totalNum, successNum;
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, totalNum);
        AW_MACRO_EXPECT_EQ_INT(1, successNum);
    }

    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);

    uint64_t count = 0;
    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(2, count);
}

void GtSimplelabel3StructBatchUpdate(GmcConnT *conn, GmcStmtT *stmt, GtSimplelabelCfgT vertexCfg,
                                     uint32_t schemaVersion, bool isDefaultValue = true, int32_t updateValue = 0)
{
    int ret;
    GtSimplelabel3VertexT vertex = (GtSimplelabel3VertexT){0};
    TestLabelInfoT labelInfo = {g_labelName, schemaVersion, g_testNameSpace};
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t coefficient = vertexCfg.coefficient;
    GmcOperationTypeE optType = vertexCfg.optType;

    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, schemaVersion, optType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI); // semi会合并成一个事务操作，所以total = 1
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int64_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        GtSimplelabel3StructSetPk(&vertex, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &vertex, 0, NULL, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSimpleT1UpdateSetProperty(stmt, i + updateValue, isDefaultValue);
        TestSimpleT3NewFieldSetOk(stmt, i + updateValue);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchExecute(batch, &batchRet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t totalNum, successNum;
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, totalNum);
        AW_MACRO_EXPECT_EQ_INT(1, successNum);
    }
    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
}

void GtSimplelabel4StructSetPk(GtSimplelabel4VertexT *vertex, int64_t value)
{
    vertex->f0 = value;
}

void GtSimplelabel4StructSetHashcluster(GtSimplelabel4VertexT *vertex, int64_t value, int64_t updateValue = 0)
{
    uint64_t f1Value = value + updateValue;
    int32_t f2Value = value  + updateValue;
    vertex->f1 = f1Value;
    vertex->f2 = f2Value;
}

void GtSimplelabel4StructSetLocalhash(GtSimplelabel4VertexT *vertex, int64_t value, int64_t updateValue = 0)
{
    int16_t f4Value = (value + updateValue) % 32768;
    uint16_t f5Value = (value + updateValue) % 65536;
    vertex->f4 = f4Value;
    vertex->f5 = f5Value;
}

void GtSimplelabel4StructSetLpm4(GtSimplelabel4VertexT *vertex, int64_t value)
{
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    vertex->f3 = 0;
    vertex->f11 = 0;
    if ((value) <= MAX_MASK_LEN_16) {
        destIpAddr = ((value + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if ((value) > MAX_MASK_LEN_16 && (value) <= MAX_MASK_LEN_24) {
        destIpAddr = ((value + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((value + 2));
        maskLen = ((32) & 0xff);
    }
    vertex->f12 = destIpAddr;
    vertex->f6 = maskLen;
}

void GtSimplelabel4StructSetLocal(GtSimplelabel4VertexT *vertex, int64_t value)
{
    uint32_t f3Value = value & 0xffffffff;
    vertex->f3 = f3Value;
}

void GtSimplelabel4StructSetOldProperty(GtSimplelabel4VertexT *vertex, int64_t value, int64_t coefficient,
                                        bool isDefaultValue = true, int32_t updateValue = 0)
{
    vertex->f1 = value + updateValue;
    vertex->f2 = value + updateValue;

    vertex->f4 = (value + updateValue) % 32768;
    vertex->f5 = (value + updateValue) % 65536;
    vertex->f7 = value + updateValue;
    if (!isDefaultValue) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            vertex->f8[j] = j;
        }
        vertex->f9 = (value + updateValue) % 31;
        vertex->f10 = (value + updateValue) % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            vertex->f8[j] = 0xff;
        }
        vertex->f9 = 0x1f;
        vertex->f10 = 0x3ff;
    }
    vertex->f13 = (value + updateValue) & 0xf;
}

void GtSimplelabel4StructSetNewProperty(GtSimplelabel4VertexT *vertex, int64_t value, int64_t coefficient,
                                        int32_t updateValue = 0)
{
    vertex->f14 = (value + updateValue) & 0x1;
    vertex->f15 = value + updateValue;
    vertex->f16 = (value + updateValue) % 65536;
}

int GtSimplelabel4StructInsertOrReplace(GmcStmtT *stmt, const char *labelName, GtSimplelabelStructCfgT vertexCfg,
                                        uint32_t schemaVersion, GmcOperationTypeE operationType,
                                        bool isDefaultValue = true)
{
    int ret = GMERR_OK;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;

    GtSimplelabel4VertexT *vertex = (GtSimplelabel4VertexT *)malloc(sizeof(GtSimplelabel4VertexT));
    if (vertex == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
        return 1;
    }
    (void)memset(vertex, 0, sizeof(GtSimplelabel4VertexT));
    TestLabelInfoT labelInfo = {(char *)labelName, schemaVersion, g_testNameSpace};

    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
        RETURN_IFERR(ret);
        GtSimplelabel4StructSetPk(vertex, i);
        GtSimplelabel4StructSetLpm4(vertex, i);
        GtSimplelabel4StructSetOldProperty(vertex, i, coefficient, isDefaultValue);
        GtSimplelabel4StructSetNewProperty(vertex, i, coefficient);
        ret = testStructSetVertexWithBuf(stmt, vertex, &labelInfo);
        RETURN_IFERR(ret);

        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = TestGetAffactRows(stmt, expAffectRows);
        RETURN_IFERR(ret);
    }
    free(vertex);
    return GMERR_OK;
}

// 以结构化的方式 merge or update表的数据
int GtSimplelabel4StructMergeOrUpdate(GmcStmtT *stmt, const char *labelName, GtSimplelabelStructCfgT vertexCfg,
                                      uint32_t schemaVersion, GmcOperationTypeE operationType,
                                      bool isDefaultValue = true, int32_t updateValue = 0)
{
    int ret = GMERR_OK;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;

    GtSimplelabel4VertexT *vertex = (GtSimplelabel4VertexT *)malloc(sizeof(GtSimplelabel4VertexT));
    if (vertex == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
        return 1;
    }
    (void)memset(vertex, 0, sizeof(GtSimplelabel4VertexT));
    TestLabelInfoT labelInfo = {(char *)labelName, schemaVersion, g_testNameSpace};

    for (int64_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
        RETURN_IFERR(ret);
        GtSimplelabel4StructSetPk(vertex, i);
        ret = testStructSetIndexKeyWithBuf(stmt, vertex, 0, NULL, &labelInfo);
        RETURN_IFERR(ret);
        if (operationType == GMC_OPERATION_MERGE && expAffectRows == 1) {
            TestSimpleT1SetLpmProperty(stmt, i);
        }
        TestSimpleT1UpdateSetProperty(stmt, i + updateValue, isDefaultValue);
        if (schemaVersion == 0) {
            TestSimpleT4NewFieldSetFailed(stmt, i + updateValue);
        } else if (schemaVersion == 1) {
            TestSimpleT4NewFieldSetOk(stmt, i + updateValue);
        }
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = TestGetAffactRows(stmt, expAffectRows);
        RETURN_IFERR(ret);
    }
    free(vertex);
    return GMERR_OK;
}

void GtSimplelabel4StructGetOldProperty(GtSimplelabel4VertexT *vertex, int64_t index, int64_t coefficient,
                                        bool isDefaultVaule = true, int32_t updateValue = 0)
{
    int ret = 0;
    int64_t f0Value = index;
    uint64_t f1Value = index + updateValue;
    int32_t f2Value = index + updateValue;
    int16_t f4Value = (index + updateValue) % 32768;
    uint16_t f5Value = (index + updateValue) % 65536;
    uint64_t f7Value = index + updateValue;
    uint8_t fixedValue[SIMPLE_LABEL_FIXED_SIZE] = {0};
    uint8_t f9Value = 0;
    uint16_t f10Value = 0;
    uint8_t f13Value = (index + updateValue) & 0xf;
    if (!isDefaultVaule) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = j;
        }
        f9Value = (index + updateValue) % 31;
        f10Value = (index + updateValue) % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = 0xff;
        }
        f9Value = 0x1f;
        f10Value = 0x3ff;
    }
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &f0Value, &vertex->f0, sizeof(f0Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &f1Value, &vertex->f1, sizeof(f1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &f2Value, &vertex->f2, sizeof(f2Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &f4Value, &vertex->f4, sizeof(f4Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &f5Value, &vertex->f5, sizeof(f5Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_TIME, &f7Value, &vertex->f7, sizeof(f7Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, fixedValue, vertex->f8, SIMPLE_LABEL_FIXED_SIZE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(f9Value, vertex->f9);
    AW_MACRO_EXPECT_EQ_INT(f10Value, vertex->f10);
    AW_MACRO_EXPECT_EQ_INT(f13Value, vertex->f13);
}


void GtSimplelabel4StructGetLmpProperty(GtSimplelabel4VertexT *vertex, int64_t value)
{
    uint32_t f3Value = 0;
    uint32_t f11Value = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    if (value <= MAX_MASK_LEN_16) {
        destIpAddr = ((value + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if (value > MAX_MASK_LEN_16 && value <= MAX_MASK_LEN_24) {
        destIpAddr = ((value + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((value + 2));
        maskLen = ((32) & 0xff);
    }
    int32_t ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &f3Value, &vertex->f3, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &f11Value, &vertex->f11, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &destIpAddr, &vertex->f12, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &maskLen, &vertex->f6, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


void GtSimplelabel4StructGetNewProperty(GtSimplelabel4VertexT *vertex, int64_t index, int64_t coefficient,
                                        int64_t updateValue = 0)
{
    uint8_t f14Value = (index + updateValue) & 0x1;
    AW_MACRO_EXPECT_EQ_INT(f14Value, vertex->f14);
    uint32_t f15Value = index + updateValue;
    int32_t ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &f15Value, &vertex->f15, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint16_t f16Value = (index + updateValue) % 65536;
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &f16Value, &vertex->f16, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

int GtSimplelabel4StructIndexScan(GmcStmtT *stmt, const char *labelName, GtSimplelabelStructCfgT vertexCfg,
                                  uint32_t keyId, uint32_t schemaVersion,
                                  bool isDefaultValue = true, int64_t updateValue = 0)
{
    int ret, i;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;
    GtSimplelabel4VertexT vertex = (GtSimplelabel4VertexT){0};
    TestLabelInfoT labelInfo = {(char *)labelName, schemaVersion, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &vertex, &deseri, &deseriCtx, false, &labelInfo);

    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_SCAN);
    RETURN_IFERR(ret);
    for (i = startPkVal; i < startPkVal + vertexCount; i++) {
        if (keyId == 0) {
            GtSimplelabel4StructSetPk(&vertex, i);
        } else if (keyId == 1) {
            GtSimplelabel4StructSetHashcluster(&vertex, i, updateValue);
        } else if (keyId == 2) {
            GtSimplelabel4StructSetLocalhash(&vertex, i, updateValue);
        } else if (keyId == 3) {
            GtSimplelabel4StructSetLocal(&vertex, 0);
        } else if (keyId == 4) {
            GtSimplelabel4StructSetLpm4(&vertex, i);
        }
        ret = testStructSetIndexKeyWithBuf(stmt, &vertex, keyId, NULL, &labelInfo);
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        bool isFinish = false;
        uint32_t cnt = 0;
        if (keyId == 3) {
            if (i == startPkVal) {
                while (!isFinish) {
                    ret = GmcFetch(stmt, &isFinish);
                    RETURN_IFERR(ret);
                    if (isFinish) {
                        break;
                    }
                    cnt++;
                    ret = testStructGetVertexDeseri(stmt, &deseri);
                    RETURN_IFERR(ret);
                    int64_t f0Value = vertex.f0;
                    GtSimplelabel4StructGetLmpProperty(&vertex, f0Value);
                    GtSimplelabel4StructGetNewProperty(&vertex, f0Value, coefficient, updateValue);
                    GtSimplelabel4StructGetOldProperty(&vertex, f0Value, coefficient, isDefaultValue, updateValue);
                }
                AW_MACRO_EXPECT_EQ_INT(vertexCfg.count, cnt);
                return 0;
            }
        } else {
            ret = GmcFetch(stmt, &isFinish);
            RETURN_IFERR(ret);
            ret = testStructGetVertexDeseri(stmt, &deseri);
            RETURN_IFERR(ret);
            GtSimplelabel4StructGetLmpProperty(&vertex, i);
            GtSimplelabel4StructGetNewProperty(&vertex, i, coefficient, updateValue);
            GtSimplelabel4StructGetOldProperty(&vertex, i, coefficient, isDefaultValue, updateValue);
            ret = GmcFetch(stmt, &isFinish);
            RETURN_IFERR(ret);
            AW_MACRO_EXPECT_EQ_INT(true, isFinish);
        }
    }
    deSeriFreeDynMem(&deseriCtx, true);
    return 0;
}

void GtSimplelabel4StructVertexBuf(GmcStructBufferT *inputBufInfo, int64_t coefficient,
                                   bool isDefaultVaule = true, int32_t updateValue = 0)
{
    int ret = 0;
    GtSimplelabel4VertexT *vertex = (GtSimplelabel4VertexT *)inputBufInfo->buf;
    int64_t index = vertex->f0;
    GtSimplelabel4StructGetOldProperty(vertex, index, coefficient, isDefaultVaule, updateValue);
    GtSimplelabel4StructGetNewProperty(vertex, index, coefficient, updateValue);
}

int GtSimplelabel4StructLocalRangScan(GmcStmtT *stmt, const char *labelName, GtSimplelabelStructCfgT vertexCfg,
                                      uint32_t schemaVersion, int32_t *fetchNum,
                                      bool isDefaultValue = true, int64_t updateValue = 0)
{
    int ret = 0;
    uint32_t keyId = 3;
    GtSimplelabel4VertexT lKey = (GtSimplelabel4VertexT){0};
    GtSimplelabel4VertexT rKey = (GtSimplelabel4VertexT){0};
    GtSimplelabel4VertexT *leftKey = NULL;
    GtSimplelabel4VertexT *rightKey = NULL;
    GtSimplelabel4VertexT vertex = (GtSimplelabel4VertexT){0};
    GmcStructBufferT inputBufInfo = (GmcStructBufferT){0};
    GmcSeriT keySeri = (GmcSeriT){0};
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;
    TestLabelInfoT labelInfo = {(char *)labelName, schemaVersion, g_testNameSpace};

    unsigned int arrLen = 1;
    leftKey = &lKey;
    rightKey = &rKey;
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_SCAN);
    RETURN_IFERR(ret);

    GtSimplelabel4StructSetLocal(leftKey, startPkVal);
    GtSimplelabel4StructSetLocal(rightKey, startPkVal + vertexCount);
    GmcRangeItemFlagT items[arrLen];
    items[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].order = GMC_ORDER_ASC;
    ret = testStructSetKeyRangeStructure(stmt, leftKey, rightKey, items, arrLen, keyId, NULL, &labelInfo);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = testStructGetVertexBuf(stmt, &vertex, keyId, &keySeri, &inputBufInfo, &labelInfo);
        RETURN_IFERR(ret);
        GtSimplelabel4StructVertexBuf(&inputBufInfo, coefficient, isDefaultValue, updateValue);
        (*fetchNum)++;
    }
    return GMERR_OK;
}

int GtSimplelabel2StructInsertOrReplaceBatch(GmcConnT *conn, GmcStmtT *stmt, const char *labelName,
                                             GtSimplelabelStructCfgT vertexCfg, uint32_t schemaVersion,
                                             GmcOperationTypeE operationType, bool isDefaultValue = true)
{
    int ret = GMERR_OK;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;

    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    unsigned int opNum = startPkVal - vertexCount;

    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
    RETURN_IFERR(ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    RETURN_IFERR(ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    RETURN_IFERR(ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    RETURN_IFERR(ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    RETURN_IFERR(ret);
    ret = GmcBatchBindStmt(batch, stmt);
    RETURN_IFERR(ret);

    GtSimplelabel2VertexT *vertex = (GtSimplelabel2VertexT *)malloc(sizeof(GtSimplelabel2VertexT));
    if (vertex == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
        return 1;
    }
    (void)memset(vertex, 0, sizeof(GtSimplelabel2VertexT));
    TestLabelInfoT labelInfo = {(char *)labelName, schemaVersion, g_testNameSpace};

    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        GtSimplelabel2StructSetPk(vertex, i);
        GtSimplelabel2StructSetLmpProperty(vertex, i);
        GtSimplelabel2StructSetOldProperty(vertex, i, coefficient, isDefaultValue);
        GtSimplelabel2StructSetNewProperty(vertex, i, coefficient);
        ret = testStructSetVertexWithBuf(stmt, vertex, &labelInfo);
        RETURN_IFERR(ret);

        ret = GmcBatchAddDML(batch, stmt);
        RETURN_IFERR(ret);
    }

    // 批处理提交
    ret = GmcBatchExecute(batch, &batchRet);
    RETURN_IFERR(ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    RETURN_IFERR(ret);
    if (totalNum != successNum) {
        AW_FUN_Log(LOG_INFO, "successNum: %d\n", successNum);
        AW_FUN_Log(LOG_INFO, "totalNum: %d\n", totalNum);
        return -1;
    }
    AW_FUN_Log(LOG_INFO, "successNum: %d\n", successNum);
    AW_FUN_Log(LOG_INFO, "totalNum: %d\n", totalNum);
    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);

    free(vertex);
    return GMERR_OK;
}

int GtSimplelabel2StructMergeOrUpdateBatch(GmcConnT *conn, GmcStmtT *stmt, const char *labelName,
    GtSimplelabelStructCfgT vertexCfg, uint32_t schemaVersion, GmcOperationTypeE operationType,
    bool isDefaultValue = true, int32_t updateValue = 0)
{
    int ret = GMERR_OK;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;

    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    unsigned int opNum = startPkVal - vertexCount;

    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
    RETURN_IFERR(ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    RETURN_IFERR(ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    RETURN_IFERR(ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    RETURN_IFERR(ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    RETURN_IFERR(ret);
    ret = GmcBatchBindStmt(batch, stmt);
    RETURN_IFERR(ret);

    GtSimplelabel2VertexT *vertex = (GtSimplelabel2VertexT *)malloc(sizeof(GtSimplelabel2VertexT));
    if (vertex == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
        return 1;
    }
    (void)memset(vertex, 0, sizeof(GtSimplelabel2VertexT));
    TestLabelInfoT labelInfo = {(char *)labelName, schemaVersion, g_testNameSpace};

    for (int64_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        GtSimplelabel2StructSetPk(vertex, i);
        ret = testStructSetIndexKeyWithBuf(stmt, vertex, 0, NULL, &labelInfo);
        RETURN_IFERR(ret);
        if (operationType == GMC_OPERATION_MERGE && expAffectRows == 1) {
            TestSimpleT1SetLpmProperty(stmt, i);
        }
        TestSimpleT1UpdateSetProperty(stmt, i + updateValue, isDefaultValue);
        TestSimpleT2NewFieldSetOk(stmt, i + updateValue);
        ret = GmcBatchAddDML(batch, stmt);
        RETURN_IFERR(ret);
    }

    // 批处理提交
    ret = GmcBatchExecute(batch, &batchRet);
    RETURN_IFERR(ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    RETURN_IFERR(ret);
    if (totalNum != successNum) {
        AW_FUN_Log(LOG_INFO, "successNum: %d\n", successNum);
        AW_FUN_Log(LOG_INFO, "totalNum: %d\n", totalNum);
        return -1;
    }
    AW_FUN_Log(LOG_INFO, "successNum: %d\n", successNum);
    AW_FUN_Log(LOG_INFO, "totalNum: %d\n", totalNum);
    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);

    free(vertex);
    return GMERR_OK;
}

/*--------------------------------------特殊复杂表---------------------------------*/

char *g_labelName1 = (char *)"specialLabel";

#pragma pack(1)
typedef struct TagSpeciallabelT2VVertex {
    uint32_t v1;
    uint32_t v2;
    uint8_t v3[2];
    uint16_t v4Len;
    uint8_t *v4;
} GtSpeciallabelT2VVertexT;
#pragma pack()

#pragma pack(1)
typedef struct TagSpeciallabelT1VVertex {
    uint32_t v1;
    uint32_t v2;
    uint8_t v3[2];
    uint16_t v4Len;
    uint8_t *v4;
    uint16_t t2VCount;
    GtSpeciallabelT2VVertexT *t2V;
} GtSpeciallabelT1VVertexT;
#pragma pack()

// 升级前
#pragma pack(1)
typedef struct TagSpeciallabelVertex {
    int64_t f0;
    uint64_t f1;
    int32_t f2;
    uint32_t f3;
    int16_t f4;
    uint16_t f5;
    uint8_t f6;
    uint64_t f7;
    uint8_t f8[9];
    uint8_t f9 : 5;
    uint8_t res1 : 3;
    uint16_t f10 : 10;
    uint16_t res2 : 6;
    uint32_t f11;
    uint32_t f12;
    uint8_t f13 : 4;
    uint8_t res3 : 4;
    uint16_t f14Len;
    uint8_t *f14;
    uint16_t t1VCount;
    GtSpeciallabelT1VVertexT *t1V;
} GtSpeciallabelVertexT;
#pragma pack()

#pragma pack(1)
typedef struct TagSpeciallabel2Vertex {
    int64_t f0;
    uint64_t f1;
    int32_t f2;
    uint32_t f3;
    int16_t f4;
    uint16_t f5;
    uint8_t f6;
    uint64_t f7;
    uint8_t f8[9];
    uint8_t f9 : 5;
    uint8_t res1 : 3;
    uint16_t f10 : 10;
    uint16_t res2 : 6;
    uint32_t f11;
    uint32_t f12;
    uint8_t f13 : 4;
    uint8_t res3 : 4;
    uint16_t f14Len;
    uint8_t *f14;
    uint16_t t1VCount;
    GtSpeciallabelT1VVertexT *t1V;
    uint16_t f15Len;
    uint8_t *f15;
    uint16_t f16Len;
    uint8_t *f16;
} GtSpeciallabel2VertexT;
#pragma pack()

#pragma pack(1)
typedef struct TagSpeciallabel3Vertex {
    int64_t f0;
    uint64_t f1;
    int32_t f2;
    uint32_t f3;
    int16_t f4;
    uint16_t f5;
    uint8_t f6;
    uint64_t f7;
    uint8_t f8[9];
    uint8_t f9 : 5;
    uint8_t res1 : 3;
    uint16_t f10 : 10;
    uint16_t res2 : 6;
    uint32_t f11;
    uint32_t f12;
    uint8_t f13 : 4;
    uint8_t res3 : 4;
    uint16_t f14Len;
    uint8_t *f14;
    uint16_t t1VCount;
    GtSpeciallabelT1VVertexT *t1V;
    uint16_t f15Len;
    uint8_t *f15;
    uint16_t f16Len;
    uint8_t *f16;
    uint16_t f17Len;
    uint8_t *f17;
    uint16_t f18Len;
    uint8_t *f18;
    uint16_t f19Len;
    uint8_t *f19;
    uint16_t f20Len;
    uint8_t *f20;
    uint16_t f21Len;
    uint8_t *f21;
} GtSpeciallabel3VertexT;
#pragma pack()


void GtSpeciallabelStructFreeT2V(GtSpeciallabelT2VVertexT *vertex)
{
    if (vertex->v4) {
        free(vertex->v4);
    }
}

void GtSpeciallabelStructFreeT1V(GtSpeciallabelT1VVertexT *vertex)
{
    if (vertex->v4) {
        free(vertex->v4);
    }
    for (int32_t i = 0; i < vertex->t2VCount; i++) {
        GtSpeciallabelStructFreeT2V(&(vertex->t2V[i]));
    }
}

void GtSpeciallabelStructFree(GtSpeciallabelVertexT *vertex)
{
    if (vertex->f14) {
        free(vertex->f14);
    }
    for (int32_t i = 0; i < vertex->t1VCount; i++) {
        GtSpeciallabelStructFreeT1V(&(vertex->t1V[i]));
    }
}

void GtSpeciallabel2StructFree(GtSpeciallabel2VertexT *vertex)
{
    if (vertex->f14) {
        free(vertex->f14);
    }
    for (int32_t i = 0; i < vertex->t1VCount; i++) {
        GtSpeciallabelStructFreeT1V(&(vertex->t1V[i]));
    }
    if (vertex->f15) {
        free(vertex->f15);
    }
    if (vertex->f16) {
        free(vertex->f16);
    }
}

void GtSpeciallabel3StructFree(GtSpeciallabel3VertexT *vertex)
{
    if (vertex->f14) {
        free(vertex->f14);
    }
    for (int32_t i = 0; i < vertex->t1VCount; i++) {
        GtSpeciallabelStructFreeT1V(&(vertex->t1V[i]));
    }
    if (vertex->f15) {
        free(vertex->f15);
    }
    if (vertex->f16) {
        free(vertex->f16);
    }
    if (vertex->f17) {
        free(vertex->f17);
    }
    if (vertex->f18) {
        free(vertex->f18);
    }
    if (vertex->f19) {
        free(vertex->f19);
    }
    if (vertex->f20) {
        free(vertex->f20);
    }
    if (vertex->f21) {
        free(vertex->f21);
    }
}

void GtSpeciallabelStructSetT2VProperty(GtSpeciallabelT2VVertexT *vertex, int32_t value, char *stringValue)
{
    vertex->v1 = value;
    vertex->v2 = value;
    uint8_t bitmap[2] = {0x55, 0x55};
    (void)memcpy(vertex->v3, bitmap, sizeof(vertex->v3));

    vertex->v4Len = strlen(stringValue) + 1;
    if (!vertex->v4) {
        vertex->v4 = (uint8_t *)malloc(vertex->v4Len);
    }
    if (vertex->v4 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->v4 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->v4, vertex->v4Len, "%s", stringValue);
}

void GtSpeciallabelStructSetT1VProperty(GtSpeciallabelT1VVertexT *vertex, int32_t value, char *stringValue,
                                        uint16_t t2VCount)
{
    vertex->v1 = value;
    vertex->v2 = value;
    uint8_t bitmap[2] = {0x55, 0x55};
    memcpy(vertex->v3, bitmap, sizeof(vertex->v3));

    vertex->v4Len = strlen(stringValue) + 1;
    if (!vertex->v4) {
        vertex->v4 = (uint8_t *)malloc(vertex->v4Len);
    }
    if (vertex->v4 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->v4 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->v4, vertex->v4Len, "%s", stringValue);
    vertex->t2VCount = t2VCount;
    for (int32_t i = 0; i < vertex->t2VCount; i++) {
        GtSpeciallabelStructSetT2VProperty(&vertex->t2V[i], value, stringValue);
    }
}

void GtSpeciallabelStructSetPk(GtSpeciallabelVertexT *vertex, int64_t value)
{
    vertex->f0 = value;
}

void GtSpeciallabelStructSetHashcluster(GtSpeciallabelVertexT *vertex, int64_t value, int64_t updateValue = 0)
{
    uint64_t f1Value = value + updateValue;
    int32_t f2Value = value  + updateValue;
    vertex->f1 = f1Value;
    vertex->f2 = f2Value;
}

void GtSpeciallabelStructSetLocalhash(GtSpeciallabelVertexT *vertex, int64_t value, int64_t updateValue = 0)
{
    int16_t f4Value = (value + updateValue) % 32768;
    uint16_t f5Value = (value + updateValue) % 65536;
    vertex->f4 = f4Value;
    vertex->f5 = f5Value;
}

void GtSpeciallabelStructSetLpm4(GtSpeciallabelVertexT *vertex, int64_t value)
{
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    vertex->f3 = 0;
    vertex->f11 = 0;
    if ((value) <= MAX_MASK_LEN_16) {
        destIpAddr = ((value + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if ((value) > MAX_MASK_LEN_16 && (value) <= MAX_MASK_LEN_24) {
        destIpAddr = ((value + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((value + 2));
        maskLen = ((32) & 0xff);
    }
    vertex->f12 = destIpAddr;
    vertex->f6 = maskLen;
}

void GtSpeciallabelStructSetLocal(GtSpeciallabelVertexT *vertex, int64_t value)
{
    uint32_t f3Value = 0;
    vertex->f3 = f3Value;
}

void GtSpeciallabelStructSetOldProperty(GtSpeciallabelVertexT *vertex, int64_t value, uint16_t t1VCount,
                                        uint16_t t2VCount, char *bytesValue, char *stringValue,
                                        bool isDefaultValue = true, int32_t updateValue = 0)
{
    vertex->f1 = value + updateValue;
    vertex->f2 = value + updateValue;

    vertex->f4 = (value + updateValue) % 32768;
    vertex->f5 = (value + updateValue) % 65536;
    vertex->f7 = value + updateValue;
    if (!isDefaultValue) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            vertex->f8[j] = j;
        }
        vertex->f9 = (value + updateValue) % 31;
        vertex->f10 = (value + updateValue) % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            vertex->f8[j] = 0xff;
        }
        vertex->f9 = 0x1f;
        vertex->f10 = 0x3ff;
    }
    vertex->f13 = (value + updateValue) & 0xf;
    vertex->f14Len = strlen(bytesValue);
    if (!vertex->f14) {
        vertex->f14 = (uint8_t *)malloc(vertex->f14Len + 1);
    }
    if (vertex->f14 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->f14 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->f14, vertex->f14Len + 1, "%s", bytesValue);
    vertex->t1VCount = t1VCount;
    for (int32_t i = 0; i < vertex->t1VCount; i++) {
        GtSpeciallabelStructSetT1VProperty(&vertex->t1V[i], value, stringValue, t2VCount);
    }
}

void GtSpeciallabelStructSetProperty(GtSpeciallabelVertexT *vertex, int32_t value, uint16_t t1VCount,
                                     uint16_t t2VCount, char *bytesValue, char *stringValue,
                                     bool isDefaultValue = true, int32_t coefficient = 0)
{
    GtSpeciallabelStructSetPk(vertex, value);
    GtSpeciallabelStructSetHashcluster(vertex, value, coefficient);
    GtSpeciallabelStructSetLocalhash(vertex, value, coefficient);
    GtSpeciallabelStructSetLocal(vertex, value);
    GtSpeciallabelStructSetLpm4(vertex, value);
    GtSpeciallabelStructSetOldProperty(vertex, value, t1VCount, t2VCount, bytesValue, stringValue, isDefaultValue,
                                       coefficient);
}

void GtSpeciallabelStructGetLmpProperty(GtSpeciallabelVertexT *vertex, int64_t value)
{
    uint32_t f3Value = 0;
    uint32_t f11Value = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    if (value <= MAX_MASK_LEN_16) {
        destIpAddr = ((value + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if (value > MAX_MASK_LEN_16 && value <= MAX_MASK_LEN_24) {
        destIpAddr = ((value + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((value + 2));
        maskLen = ((32) & 0xff);
    }
    int32_t ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &f3Value, &vertex->f3, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &f11Value, &vertex->f11, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &destIpAddr, &vertex->f12, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &maskLen, &vertex->f6, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void GtSpeciallabelCompareVertexPropertyVector(
    GtSpeciallabelVertexT *d, int32_t index, int64_t value, char *stringValue, uint16_t t1Count, uint16_t t2Count)
{
    uint32_t v1Value = value;
    int32_t ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &v1Value, &d->t1V[index].v1, sizeof(v1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &v1Value, &d->t1V[index].v2, sizeof(v1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_STRING, stringValue, d->t1V[index].v4, strlen(stringValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t v3Bits[2] = {0x55, 0x55};
    GmcBitMapT v3 = {0};
    v3.beginPos = 0;
    v3.endPos = 8 - 1;
    v3.bits = v3Bits;
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BITMAP, &v3Bits, &d->t1V[index].v3, sizeof(v3Bits));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < t2Count; i++) {
        ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &v1Value, &d->t1V[index].t2V[i].v1, sizeof(v1Value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &v1Value, &d->t1V[index].t2V[i].v2, sizeof(v1Value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = CompareVertexPropertyValue(GMC_DATATYPE_STRING, stringValue, d->t1V[index].t2V[i].v4,
                                         strlen(stringValue));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = CompareVertexPropertyValue(GMC_DATATYPE_BITMAP, &v3Bits, &d->t1V[index].t2V[i].v3, sizeof(v3Bits));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

void GtSpeciallabelStructGetVector(
    GtSpeciallabelVertexT *d, int64_t index, char *stringValue, uint16_t t1Count, uint16_t t2Count)
{
    int ret = 0, i;
    for (i = 0; i < t1Count; ++i) {
        GtSpeciallabelCompareVertexPropertyVector(d, i, index, stringValue, t1Count, t2Count);
    }
}

void GtSpeciallabelStructGetOldProperty(GtSpeciallabelVertexT *vertex, int64_t index, char *bytesValue,
                                        char *stringValue, bool isDefaultVaule = true, int32_t updateValue = 0)
{
    int ret = 0;
    int64_t f0Value = index;
    uint64_t f1Value = index + updateValue;
    int32_t f2Value = index + updateValue;
    int16_t f4Value = (index + updateValue) % 32768;
    uint16_t f5Value = (index + updateValue) % 65536;
    uint64_t f7Value = index + updateValue;
    uint8_t fixedValue[SIMPLE_LABEL_FIXED_SIZE] = {0};
    uint8_t f9Value = 0;
    uint16_t f10Value = 0;
    uint8_t f13Value = (index + updateValue) & 0xf;
    if (!isDefaultVaule) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = j;
        }
        f9Value = (index + updateValue) % 31;
        f10Value = (index + updateValue) % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = 0xff;
        }
        f9Value = 0x1f;
        f10Value = 0x3ff;
    }
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &f0Value, &vertex->f0, sizeof(f0Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &f1Value, &vertex->f1, sizeof(f1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &f2Value, &vertex->f2, sizeof(f2Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &f4Value, &vertex->f4, sizeof(f4Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &f5Value, &vertex->f5, sizeof(f5Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_TIME, &f7Value, &vertex->f7, sizeof(f7Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, fixedValue, vertex->f8, SIMPLE_LABEL_FIXED_SIZE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(f9Value, vertex->f9);
    AW_MACRO_EXPECT_EQ_INT(f10Value, vertex->f10);
    AW_MACRO_EXPECT_EQ_INT(f13Value, vertex->f13);
    uint8_t f14[BYTES_LEN] = {0};
    (void)snprintf((char *)f14, BYTES_LEN, "%s", bytesValue);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BYTES, f14, vertex->f14, strlen(bytesValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GtSpeciallabelStructGetVector(vertex, index, stringValue, 3, 3);
}

void GtSpeciallabel2StructSetPk(GtSpeciallabel2VertexT *vertex, int64_t value)
{
    vertex->f0 = value;
}

void GtSpeciallabel2StructSetHashcluster(GtSpeciallabel2VertexT *vertex, int64_t value, int64_t updateValue = 0)
{
    uint64_t f1Value = value + updateValue;
    int32_t f2Value = value  + updateValue;
    vertex->f1 = f1Value;
    vertex->f2 = f2Value;
}

void GtSpeciallabel2StructSetLocalhash(GtSpeciallabel2VertexT *vertex, int64_t value, int64_t updateValue = 0)
{
    int16_t f4Value = (value + updateValue) % 32768;
    uint16_t f5Value = (value + updateValue) % 65536;
    vertex->f4 = f4Value;
    vertex->f5 = f5Value;
}

void GtSpeciallabel2StructSetLpm4(GtSpeciallabel2VertexT *vertex, int64_t value)
{
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    vertex->f3 = 0;
    vertex->f11 = 0;
    if ((value) <= MAX_MASK_LEN_16) {
        destIpAddr = ((value + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if ((value) > MAX_MASK_LEN_16 && (value) <= MAX_MASK_LEN_24) {
        destIpAddr = ((value + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((value + 2));
        maskLen = ((32) & 0xff);
    }
    vertex->f12 = destIpAddr;
    vertex->f6 = maskLen;
}

void GtSpeciallabel2StructSetLocal(GtSpeciallabel2VertexT *vertex, int64_t value)
{
    uint32_t f3Value = 0;
    vertex->f3 = f3Value;
}

void GtSpeciallabel2StructSetOldProperty(GtSpeciallabel2VertexT *vertex, int64_t value, uint16_t t1VCount,
                                         uint16_t t2VCount, char * bytesValue, char *stringValue,
                                         bool isDefaultValue = true, int32_t updateValue = 0)
{
    vertex->f1 = value + updateValue;
    vertex->f2 = value + updateValue;

    vertex->f4 = (value + updateValue) % 32768;
    vertex->f5 = (value + updateValue) % 65536;
    vertex->f7 = value + updateValue;
    if (!isDefaultValue) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            vertex->f8[j] = j;
        }
        vertex->f9 = (value + updateValue) % 31;
        vertex->f10 = (value + updateValue) % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            vertex->f8[j] = 0xff;
        }
        vertex->f9 = 0x1f;
        vertex->f10 = 0x3ff;
    }
    vertex->f13 = (value + updateValue) & 0xf;
    vertex->f14Len = strlen(bytesValue);
    if (!vertex->f14) {
        vertex->f14 = (uint8_t *)malloc(vertex->f14Len + 1);
    }
    if (vertex->f14 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->f14 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->f14, vertex->f14Len + 1, "%s", bytesValue);
    vertex->t1VCount = t1VCount;
    for (int32_t i = 0; i < vertex->t1VCount; i++) {
        GtSpeciallabelStructSetT1VProperty(&vertex->t1V[i], value, stringValue, t2VCount);
    }
}

void GtSpeciallabel2StructSetNewProperty(GtSpeciallabel2VertexT *vertex, char *bytesValue, char *stringValue)
{
    vertex->f15Len = strlen(bytesValue);
    if (!vertex->f15) {
        vertex->f15 = (uint8_t *)malloc(vertex->f15Len + 1);
    }
    if (vertex->f15 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->f15 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->f15, vertex->f15Len + 1, "%s", bytesValue);
    vertex->f16Len = strlen(stringValue) + 1;
    if (!vertex->f16) {
        vertex->f16 = (uint8_t *)malloc(vertex->f16Len);
    }
    if (vertex->f16 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->f16 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->f16, vertex->f16Len, "%s", stringValue);
}

void GtSpeciallabel2StructSetProperty(GtSpeciallabel2VertexT *vertex, int32_t value, uint16_t t1VCount,
                                      uint16_t t2VCount, char *bytesValue, char *stringValue,
                                      bool isDefaultValue = true, int32_t coefficient = 0)
{
    GtSpeciallabel2StructSetPk(vertex, value);
    GtSpeciallabel2StructSetHashcluster(vertex, value, coefficient);
    GtSpeciallabel2StructSetLocalhash(vertex, value, coefficient);
    GtSpeciallabel2StructSetLocal(vertex, value);
    GtSpeciallabel2StructSetLpm4(vertex, value);
    GtSpeciallabel2StructSetOldProperty(vertex, value, t1VCount, t2VCount, bytesValue, stringValue, isDefaultValue,
                                        coefficient);
    GtSpeciallabel2StructSetNewProperty(vertex, bytesValue, stringValue);
}

void GtSpeciallabel3StructSetPk(GtSpeciallabel3VertexT *vertex, int64_t value)
{
    vertex->f0 = value;
}

void GtSpeciallabel3StructSetHashcluster(GtSpeciallabel3VertexT *vertex, int64_t value, int64_t updateValue = 0)
{
    uint64_t f1Value = value + updateValue;
    int32_t f2Value = value  + updateValue;
    vertex->f1 = f1Value;
    vertex->f2 = f2Value;
}

void GtSpeciallabel3StructSetLocalhash(GtSpeciallabel3VertexT *vertex, int64_t value, int64_t updateValue = 0)
{
    int16_t f4Value = (value + updateValue) % 32768;
    uint16_t f5Value = (value + updateValue) % 65536;
    vertex->f4 = f4Value;
    vertex->f5 = f5Value;
}

void GtSpeciallabel3StructSetLpm4(GtSpeciallabel3VertexT *vertex, int64_t value)
{
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    vertex->f3 = 0;
    vertex->f11 = 0;
    if ((value) <= MAX_MASK_LEN_16) {
        destIpAddr = ((value + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if ((value) > MAX_MASK_LEN_16 && (value) <= MAX_MASK_LEN_24) {
        destIpAddr = ((value + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((value + 2));
        maskLen = ((32) & 0xff);
    }
    vertex->f12 = destIpAddr;
    vertex->f6 = maskLen;
}

void GtSpeciallabel3StructSetLocal(GtSpeciallabel3VertexT *vertex, int64_t value)
{
    uint32_t f3Value = 0;
    vertex->f3 = f3Value;
}

void GtSpeciallabel3StructSetOldProperty(GtSpeciallabel3VertexT *vertex, int64_t value, uint16_t t1VCount,
                                         uint16_t t2VCount, char * bytesValue, char *stringValue,
                                         bool isDefaultValue = true, int32_t updateValue = 0)
{
    vertex->f1 = value + updateValue;
    vertex->f2 = value + updateValue;

    vertex->f4 = (value + updateValue) % 32768;
    vertex->f5 = (value + updateValue) % 65536;
    vertex->f7 = value + updateValue;
    if (!isDefaultValue) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            vertex->f8[j] = j;
        }
        vertex->f9 = (value + updateValue) % 31;
        vertex->f10 = (value + updateValue) % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            vertex->f8[j] = 0xff;
        }
        vertex->f9 = 0x1f;
        vertex->f10 = 0x3ff;
    }
    vertex->f13 = (value + updateValue) & 0xf;
    vertex->f14Len = strlen(bytesValue);
    if (!vertex->f14) {
        vertex->f14 = (uint8_t *)malloc(vertex->f14Len + 1);
    }
    if (vertex->f14 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->f14 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->f14, vertex->f14Len + 1, "%s", bytesValue);
    vertex->t1VCount = t1VCount;
    for (int32_t i = 0; i < vertex->t1VCount; i++) {
        GtSpeciallabelStructSetT1VProperty(&vertex->t1V[i], value, stringValue, t2VCount);
    }
}

void GtSpeciallabel3StructSetNewProperty(GtSpeciallabel3VertexT *vertex, int64_t value, char *bytesValue,
                                         char *stringValue)
{
    vertex->f15Len = strlen(bytesValue);
    if (!vertex->f15) {
        vertex->f15 = (uint8_t *)malloc(vertex->f15Len + 1);
    }
    if (vertex->f15 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->f15 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->f15, vertex->f15Len + 1, "%s", bytesValue);

    vertex->f16Len = strlen(stringValue) + 1;
    if (!vertex->f16) {
        vertex->f16 = (uint8_t *)malloc(vertex->f16Len);
    }
    if (vertex->f16 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->f16 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->f16, vertex->f16Len, "%s", stringValue);

    vertex->f17Len = STRING3_LEN;
    if (!vertex->f17) {
        vertex->f17 = (uint8_t *)malloc(vertex->f17Len + 1);
    }
    if (vertex->f17 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->f17 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->f17, vertex->f17Len + 1, "s%032766d", value);

    vertex->f18Len = STRING3_LEN;
    if (!vertex->f18) {
        vertex->f18 = (uint8_t *)malloc(vertex->f18Len + 1);
    }
    if (vertex->f18 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->f18 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->f18, vertex->f18Len + 1, "s%032766d", value);

    vertex->f19Len = STRING3_LEN;
    if (!vertex->f19) {
        vertex->f19 = (uint8_t *)malloc(vertex->f19Len + 1);
    }
    if (vertex->f19 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->f19 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->f19, vertex->f19Len + 1, "s%032766d", value);

    vertex->f20Len = STRING3_LEN;
    if (!vertex->f20) {
        vertex->f20 = (uint8_t *)malloc(vertex->f20Len + 1);
    }
    if (vertex->f20 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->f20 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->f20, vertex->f20Len + 1, "s%032766d", value);

    vertex->f21Len = STRING3_LEN;
    if (!vertex->f21) {
        vertex->f21 = (uint8_t *)malloc(vertex->f21Len + 1);
    }
    if (vertex->f21 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->f21 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->f21, vertex->f21Len + 1, "s%032766d", value);
}


void GtSpeciallabel3StructSetMidProperty(GtSpeciallabel3VertexT *vertex, char *bytesValue, char *stringValue)
{
    vertex->f15Len = strlen(bytesValue);
    if (!vertex->f15) {
        vertex->f15 = (uint8_t *)malloc(vertex->f15Len + 1);
    }
    if (vertex->f15 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->f15 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->f15, vertex->f15Len + 1, "%s", bytesValue);

    vertex->f16Len = strlen(stringValue) + 1;
    if (!vertex->f16) {
        vertex->f16 = (uint8_t *)malloc(vertex->f16Len);
    }
    if (vertex->f16 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->f16 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->f16, vertex->f16Len, "%s", stringValue);
}

void GtSpeciallabel3StructSetMostNewProperty(GtSpeciallabel3VertexT *vertex, int64_t value)
{
    vertex->f17Len = STRING3_LEN;
    if (!vertex->f17) {
        vertex->f17 = (uint8_t *)malloc(vertex->f17Len + 1);
    }
    if (vertex->f17 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->f17 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->f17, vertex->f17Len + 1, "s%032766d", value);

    vertex->f18Len = STRING3_LEN;
    if (!vertex->f18) {
        vertex->f18 = (uint8_t *)malloc(vertex->f18Len + 1);
    }
    if (vertex->f18 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->f18 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->f18, vertex->f18Len + 1, "s%032766d", value);

    vertex->f19Len = STRING3_LEN;
    if (!vertex->f19) {
        vertex->f19 = (uint8_t *)malloc(vertex->f19Len + 1);
    }
    if (vertex->f19 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->f19 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->f19, vertex->f19Len + 1, "s%032766d", value);

    vertex->f20Len = STRING3_LEN;
    if (!vertex->f20) {
        vertex->f20 = (uint8_t *)malloc(vertex->f20Len + 1);
    }
    if (vertex->f20 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->f20 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->f20, vertex->f20Len + 1, "s%032766d", value);

    vertex->f21Len = STRING3_LEN;
    if (!vertex->f21) {
        vertex->f21 = (uint8_t *)malloc(vertex->f21Len + 1);
    }
    if (vertex->f21 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->f21 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->f21, vertex->f21Len + 1, "s%032766d", value);
}

void GtSpeciallabel3StructSetProperty(GtSpeciallabel3VertexT *vertex, int32_t value, uint16_t t1VCount,
                                      uint16_t t2VCount, char *bytesValue, char *stringValue,
                                      bool isDefaultValue = true, int32_t coefficient = 0)
{
    GtSpeciallabel3StructSetPk(vertex, value);
    GtSpeciallabel3StructSetHashcluster(vertex, value, coefficient);
    GtSpeciallabel3StructSetLocalhash(vertex, value, coefficient);
    GtSpeciallabel3StructSetLocal(vertex, value);
    GtSpeciallabel3StructSetLpm4(vertex, value);
    GtSpeciallabel3StructSetOldProperty(vertex, value, t1VCount, t2VCount, bytesValue, stringValue, isDefaultValue,
                                        coefficient);
    GtSpeciallabel3StructSetNewProperty(vertex, value + coefficient, bytesValue, stringValue);
}

int GtSpeciallabelSetT2VProperty(GmcNodeT *node, uint32_t value, char *stringValue)
{
    int ret = GMERR_OK;

    uint32_t v1 = value;
    ret = GmcNodeSetPropertyByName(node, "V1", GMC_DATATYPE_UINT32, &v1, sizeof(v1));
    RETURN_IFERR(ret);

    uint32_t v2 = value;
    ret = GmcNodeSetPropertyByName(node, "V2", GMC_DATATYPE_UINT32, &v2, sizeof(v2));
    RETURN_IFERR(ret);

    uint8_t v3Bits[2] = {0x55, 0x55};
    GmcBitMapT v3 = {0};
    v3.beginPos = 0;
    v3.endPos = 16 - 1;
    v3.bits = v3Bits;
    ret = GmcNodeSetPropertyByName(node, "V3", GMC_DATATYPE_BITMAP, &v3, sizeof(v3));
    RETURN_IFERR(ret);

    uint8_t v4[STRING_LEN] = {0};
    (void)snprintf((char *)v4, STRING_LEN, "%s", stringValue);
    ret = GmcNodeSetPropertyByName(node, "V4", GMC_DATATYPE_STRING, v4, strlen(stringValue));
    RETURN_IFERR(ret);

    return GMERR_OK;
}

int GtSpeciallabelSetT1VProperty(GmcNodeT *node, uint32_t value, char *stringValue)
{
    int ret = GMERR_OK;

    uint32_t v1 = value;
    ret = GmcNodeSetPropertyByName(node, "V1", GMC_DATATYPE_UINT32, &v1, sizeof(v1));
    RETURN_IFERR(ret);

    uint32_t v2 = value;
    ret = GmcNodeSetPropertyByName(node, "V2", GMC_DATATYPE_UINT32, &v2, sizeof(v2));
    RETURN_IFERR(ret);

    uint8_t v3Bits[2] = {0x55, 0x55};
    GmcBitMapT v3 = {0};
    v3.beginPos = 0;
    v3.endPos = 16 - 1;
    v3.bits = v3Bits;
    ret = GmcNodeSetPropertyByName(node, "V3", GMC_DATATYPE_BITMAP, &v3, sizeof(v3));
    RETURN_IFERR(ret);

    uint8_t v4[STRING_LEN] = {0};
    (void)snprintf((char *)v4, STRING_LEN, "%s", stringValue);
    ret = GmcNodeSetPropertyByName(node, "V4", GMC_DATATYPE_STRING, v4, strlen(stringValue));
    RETURN_IFERR(ret);
    return GMERR_OK;
}


// 以结构化的方式 replace Speciallabel 表的数据
int GtSpeciallabel2StructWrite(GmcStmtT *stmt, GtSpeciallabelCfgT vertexCfg, char *bytesValue, char *stringValue,
                               bool isDefaultValue = true)
{
    int ret = GMERR_OK;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;
    uint16_t t1VCount = vertexCfg.t1VCount;
    uint16_t t2VCount = vertexCfg.t2VCount;
    uint32_t schemaVersion = vertexCfg.schemaVersion;
    GmcOperationTypeE optType = vertexCfg.optType;

    GtSpeciallabel2VertexT *vertex = (GtSpeciallabel2VertexT *)malloc(sizeof(GtSpeciallabel2VertexT));
    if (vertex == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
        return 1;
    }
    (void)memset(vertex, 0, sizeof(GtSpeciallabel2VertexT));
    TestLabelInfoT labelInfo = {g_labelName2, schemaVersion, g_testNameSpace};
    GtSpeciallabelT1VVertexT *t1V = (GtSpeciallabelT1VVertexT *)malloc(sizeof(GtSpeciallabelT1VVertexT) * t1VCount);
    if (t1V == NULL) {
        AW_FUN_Log(LOG_ERROR, "t1V is NULL\n");
        return 1;
    }
    (void)memset(t1V, 0, sizeof(GtSpeciallabelT1VVertexT) * t1VCount);
    vertex->t1V = t1V;
    GtSpeciallabelT2VVertexT *t2V =
    (GtSpeciallabelT2VVertexT *)malloc(sizeof(GtSpeciallabelT2VVertexT) * t1VCount * t2VCount);

    if (t2V == NULL) {
        AW_FUN_Log(LOG_ERROR, "t2V is NULL\n");
        return 1;
    }
    (void)memset(t2V, 0, sizeof(GtSpeciallabelT2VVertexT) * t1VCount * t2VCount);
    for (int32_t i = 0; i < t1VCount; i++) {
        t1V[i].t2V = &t2V[t2VCount * ((0) + i)];
    }
    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName2, schemaVersion, optType);
        RETURN_IFERR(ret);

        GtSpeciallabel2StructSetProperty(vertex, i, t1VCount, t2VCount, bytesValue, stringValue,
                                         isDefaultValue, coefficient);
        ret = testStructSetVertexWithBuf(stmt, vertex, &labelInfo);
        RETURN_IFERR(ret);

        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expAffectRows);
        RETURN_IFERR(ret);
    }
    GtSpeciallabel2StructFree(vertex);
    free(t2V);
    free(t1V);
    free(vertex);
    return GMERR_OK;
}

void GtSpeciallabel2SetLpmProperty(GmcNodeT *node, int64_t i)
{
    int32_t ret = 0;
    uint32_t vrid = 0;
    uint32_t vrfIndex = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    ret = GmcNodeSetPropertyByName(node, (char *)"F3", GMC_DATATYPE_UINT32, &vrid, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F11", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    if (i <= MAX_MASK_LEN_16) {
        destIpAddr = ((i + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if (i > MAX_MASK_LEN_16 && i <= MAX_MASK_LEN_24) {
        destIpAddr = ((i + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((i + 2));
        maskLen = ((32) & 0xff);
    }
    ret = GmcNodeSetPropertyByName(node, (char *)"F12", GMC_DATATYPE_UINT32, &destIpAddr, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F6", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 以结构化的方式 merge or update表的数据
int GtSpeciallabel2StructUpdate(GmcStmtT *stmt, GtSpeciallabelCfgT vertexCfg, char *bytesValue, char *stringValue,
                                bool isDefaultValue = true)
{
    int ret = GMERR_OK;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;
    uint16_t t1VCount = vertexCfg.t1VCount;
    uint16_t t2VCount = vertexCfg.t2VCount;
    uint32_t schemaVersion = vertexCfg.schemaVersion;
    GmcOperationTypeE optType = vertexCfg.optType;
    GmcNodeT *root, *t1V;
    GtSpeciallabel2VertexT *vertex = (GtSpeciallabel2VertexT *)malloc(sizeof(GtSpeciallabel2VertexT));
    if (vertex == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
        return 1;
    }
    (void)memset(vertex, 0, sizeof(GtSpeciallabel2VertexT));
    TestLabelInfoT labelInfo = {g_labelName2, schemaVersion, g_testNameSpace};

    for (int64_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName2, schemaVersion, optType);
        RETURN_IFERR(ret);
        GtSpeciallabel2StructSetPk(vertex, i);
        ret = testStructSetIndexKeyWithBuf(stmt, vertex, 0, NULL, &labelInfo);
        RETURN_IFERR(ret);
        GtSpeciallabel2GetNode(stmt, &root, &t1V);
        if (optType == GMC_OPERATION_MERGE && expAffectRows == 1) {
            GtSpeciallabel2SetLpmProperty(root, i);
        }
        TestSpecialT2UpdateSetOldProperty(root, i + coefficient, bytesValue, isDefaultValue);
        TestSpecialT2UpdateSetNewProperty(root, bytesValue, stringValue);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = TestGetAffactRows(stmt, expAffectRows);
        RETURN_IFERR(ret);
    }
    free(vertex);
    return GMERR_OK;
}

void GtSpeciallabel2StructGetLmpProperty(GtSpeciallabel2VertexT *vertex, int64_t value)
{
    uint32_t f3Value = 0;
    uint32_t f11Value = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    if (value <= MAX_MASK_LEN_16) {
        destIpAddr = ((value + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if (value > MAX_MASK_LEN_16 && value <= MAX_MASK_LEN_24) {
        destIpAddr = ((value + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((value + 2));
        maskLen = ((32) & 0xff);
    }
    int32_t ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &f3Value, &vertex->f3, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &f11Value, &vertex->f11, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &destIpAddr, &vertex->f12, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &maskLen, &vertex->f6, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void CompareVertexPropertyValueVector(
    GtSpeciallabel2VertexT *d, int32_t index, int64_t value, char *stringValue, uint16_t t1Count, uint16_t t2Count)
{
    uint32_t v1Value = value;
    int32_t ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &v1Value, &d->t1V[index].v1, sizeof(v1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &v1Value, &d->t1V[index].v2, sizeof(v1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_STRING, stringValue, d->t1V[index].v4, strlen(stringValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t v3Bits[2] = {0x55, 0x55};
    GmcBitMapT v3 = {0};
    v3.beginPos = 0;
    v3.endPos = 8 - 1;
    v3.bits = v3Bits;
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BITMAP, &v3Bits, &d->t1V[index].v3, sizeof(v3Bits));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void GtSpeciallabel2StructGetVector(
    GtSpeciallabel2VertexT *d, int64_t index, char *stringValue, uint16_t t1Count, uint16_t t2Count)
{
    int ret = 0, i;
    for (i = 0; i < t1Count; ++i) {
        CompareVertexPropertyValueVector(d, i, index, stringValue, t1Count, t2Count);
    }
}

void GtSpeciallabel2StructGetOldProperty(GtSpeciallabel2VertexT *vertex, int64_t index, char *bytesValue,
                                         char *stringValue, bool isDefaultVaule = true, int32_t updateValue = 0)
{
    int ret = 0;
    int64_t f0Value = index;
    uint64_t f1Value = index + updateValue;
    int32_t f2Value = index + updateValue;
    int16_t f4Value = (index + updateValue) % 32768;
    uint16_t f5Value = (index + updateValue) % 65536;
    uint64_t f7Value = index + updateValue;
    uint8_t fixedValue[SIMPLE_LABEL_FIXED_SIZE] = {0};
    uint8_t f9Value = 0;
    uint16_t f10Value = 0;
    uint8_t f13Value = (index + updateValue) & 0xf;
    if (!isDefaultVaule) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = j;
        }
        f9Value = (index + updateValue) % 31;
        f10Value = (index + updateValue) % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = 0xff;
        }
        f9Value = 0x1f;
        f10Value = 0x3ff;
    }
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &f0Value, &vertex->f0, sizeof(f0Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &f1Value, &vertex->f1, sizeof(f1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &f2Value, &vertex->f2, sizeof(f2Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &f4Value, &vertex->f4, sizeof(f4Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &f5Value, &vertex->f5, sizeof(f5Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_TIME, &f7Value, &vertex->f7, sizeof(f7Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, fixedValue, vertex->f8, SIMPLE_LABEL_FIXED_SIZE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(f9Value, vertex->f9);
    AW_MACRO_EXPECT_EQ_INT(f10Value, vertex->f10);
    AW_MACRO_EXPECT_EQ_INT(f13Value, vertex->f13);
    uint8_t f14[BYTES_LEN] = {0};
    (void)snprintf((char *)f14, BYTES_LEN, "%s", bytesValue);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BYTES, f14, vertex->f14, strlen(bytesValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GtSpeciallabel2StructGetVector(vertex, index, stringValue, 3, 3);
}


void GtSpeciallabel2StructGetNewProperty(GtSpeciallabel2VertexT *vertex, char *bytesValue, char *stringValue,
                                         bool isNull = false)
{
    if (!isNull) {
        uint8_t f15[BYTES_LEN] = {0};
        uint8_t f16[STRING2_LEN] = {0};
        (void)snprintf((char *)f15, BYTES_LEN, "%s", bytesValue);
        int32_t ret = CompareVertexPropertyValue(GMC_DATATYPE_BYTES, f15, vertex->f15, strlen(bytesValue));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        (void)snprintf((char *)f16, STRING2_LEN, "%s", stringValue);
        ret = CompareVertexPropertyValue(GMC_DATATYPE_STRING, f16, vertex->f16, strlen(stringValue));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        AW_MACRO_EXPECT_EQ_INT(0, vertex->f15Len);
        AW_MACRO_EXPECT_EQ_INT(0, vertex->f16Len);
    }
}

int GtSpeciallabel2StructIndexScan(GmcStmtT *stmt, GtSpeciallabelCfgT vertexCfg, uint32_t keyId,
                                   char *bytesValue, char *stringValue, bool isDefaultValue = true)
{
    int ret;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    uint32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;
    uint16_t t1VCount = vertexCfg.t1VCount;
    uint16_t t2VCount = vertexCfg.t2VCount;
    uint32_t schemaVersion = vertexCfg.schemaVersion;
    GmcOperationTypeE optType = vertexCfg.optType;
    bool fieldIsNull[8] = {0};
    for (int i = 0; i < 8; i++) {
        fieldIsNull[i] = vertexCfg.fieldIsNull[i];
    }
    GtSpeciallabel2VertexT vertex = (GtSpeciallabel2VertexT){0};
    TestLabelInfoT labelInfo = {g_labelName2, schemaVersion, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &vertex, &deseri, &deseriCtx, false, &labelInfo);

    for (int i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName2, schemaVersion, GMC_OPERATION_SCAN);
        RETURN_IFERR(ret);
        if (keyId == 0) {
            GtSpeciallabel2StructSetPk(&vertex, i);
        } else if (keyId == 1) {
            GtSpeciallabel2StructSetHashcluster(&vertex, i, coefficient);
        } else if (keyId == 2) {
            GtSpeciallabel2StructSetLocalhash(&vertex, i, coefficient);
        } else if (keyId == 3) {
            GtSpeciallabel2StructSetLocal(&vertex, i);
        } else if (keyId == 4) {
            GtSpeciallabel2StructSetLpm4(&vertex, i);
        }
        ret = testStructSetIndexKeyWithBuf(stmt, &vertex, keyId, NULL, &labelInfo);
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        bool isFinish = false;
        uint32_t cnt = 0;
        if (keyId == 3) {
            while (!isFinish) {
                ret = GmcFetch(stmt, &isFinish);
                RETURN_IFERR(ret);
                if (isFinish) {
                    break;
                }
                cnt++;
                ret = testStructGetVertexDeseri(stmt, &deseri);
                RETURN_IFERR(ret);
                int64_t f0Value = vertex.f0;
                if (f0Value >= startPkVal && f0Value < startPkVal + vertexCount) {
                    GtSpeciallabel2StructGetLmpProperty(&vertex, f0Value);
                    GtSpeciallabel2StructGetOldProperty(&vertex, f0Value, bytesValue, stringValue,
                                                        isDefaultValue, coefficient);
                    GtSpeciallabel2StructGetNewProperty(&vertex, bytesValue, stringValue, fieldIsNull[1]);
                }
            }
            AW_MACRO_EXPECT_EQ_INT(vertexCfg.expAffectRows, cnt);
            return GMERR_OK;
        } else {
            ret = GmcFetch(stmt, &isFinish);
            RETURN_IFERR(ret);
            ret = testStructGetVertexDeseri(stmt, &deseri);
            RETURN_IFERR(ret);
            GtSpeciallabel2StructGetLmpProperty(&vertex, i);
            GtSpeciallabel2StructGetOldProperty(&vertex, i, bytesValue, stringValue,
                                                isDefaultValue, coefficient);
            GtSpeciallabel2StructGetNewProperty(&vertex, bytesValue, stringValue, fieldIsNull[1]);
            ret = GmcFetch(stmt, &isFinish);
            RETURN_IFERR(ret);
            AW_MACRO_EXPECT_EQ_INT(true, isFinish);
        }
    }
    deSeriFreeDynMem(&deseriCtx, true);
    return GMERR_OK;
}

void GtSpeciallabel2NodeSet(GmcNodeT *node, uint32_t value, char *stringValue, uint16_t t1Count, uint16_t t2Count)
{
    GmcNodeT *t2Node = NULL;
    // 插入vector节点
    for (uint16_t j = 0; j < t1Count; j++) {
        int32_t ret = GmcNodeAppendElement(node, &node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GtSpeciallabelSetT1VProperty(node, value, stringValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 获取T2V节点
        ret = GmcNodeGetChild(node, "T2V", &t2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (uint16_t k = 0; k < t2Count; k++) {
            ret = GmcNodeAppendElement(t2Node, &t2Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GtSpeciallabelSetT1VProperty(t2Node, value, stringValue);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
}

int GtSpeciallabel2StructBatchWrite(GmcConnT *conn, GmcStmtT *stmt, GtSpeciallabelCfgT vertexCfg, char *bytesValue,
                                    char *stringValue, bool isDefaultValue = true)
{
    int ret;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;
    uint16_t t1VCount = vertexCfg.t1VCount;
    uint16_t t2VCount = vertexCfg.t2VCount;
    uint32_t schemaVersion = vertexCfg.schemaVersion;
    GmcOperationTypeE optType = vertexCfg.optType;
    GmcNodeT *root = NULL, *t1Node = NULL;
    GtSpeciallabel2VertexT *vertex = (GtSpeciallabel2VertexT *)malloc(sizeof(GtSpeciallabel2VertexT));
    if (vertex == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
        return 1;
    }
    (void)memset(vertex, 0, sizeof(GtSpeciallabel2VertexT));
    GtSpeciallabelT1VVertexT *t1V = (GtSpeciallabelT1VVertexT *)malloc(sizeof(GtSpeciallabelT1VVertexT) * t1VCount);
    if (t1V == NULL) {
        AW_FUN_Log(LOG_ERROR, "t1V is NULL\n");
        return 1;
    }
    (void)memset(t1V, 0, sizeof(GtSpeciallabelT1VVertexT) * t1VCount);
    vertex->t1V = t1V;
    GtSpeciallabelT2VVertexT *t2V =
    (GtSpeciallabelT2VVertexT *)malloc(sizeof(GtSpeciallabelT2VVertexT) * t1VCount * t2VCount);
    if (t2V == NULL) {
        AW_FUN_Log(LOG_ERROR, "t2V is NULL\n");
        return 1;
    }
    (void)memset(t2V, 0, sizeof(GtSpeciallabelT2VVertexT) * t1VCount * t2VCount);
    for (int32_t i = 0; i < t1VCount; i++) {
        t1V[i].t2V = &t2V[t2VCount * ((0) + i)];
    }
    TestLabelInfoT labelInfo = {g_labelName2, schemaVersion, g_testNameSpace};
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName2, schemaVersion, optType);
    RETURN_IFERR(ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int64_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName2, schemaVersion, optType);
        RETURN_IFERR(ret);
        if (optType == GMC_OPERATION_MERGE) {
            GtSpeciallabel2StructSetPk(vertex, i);
            ret = testStructSetIndexKeyWithBuf(stmt, vertex, 0, NULL, &labelInfo);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            GtSpeciallabel2GetNode(stmt, &root, &t1Node);
            if (expAffectRows == 1) {
                GtSpeciallabel2SetLpmProperty(root, i);
                int64_t f0Value = i;
                ret = GmcNodeSetPropertyByName(root, (char *)"F0", GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            }
            TestSpecialT2UpdateSetOldProperty(root, i + coefficient, bytesValue, isDefaultValue);
            TestSpecialT2UpdateSetNewProperty(root, bytesValue, stringValue);
            GtSpeciallabel2NodeSet(t1Node, i, stringValue, t1VCount, t2VCount);
        } else {
            GtSpeciallabel2StructSetProperty(vertex, i, t1VCount, t2VCount, bytesValue, stringValue,
                                             isDefaultValue, coefficient);
            ret = testStructSetVertexWithBuf(stmt, vertex, &labelInfo);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t totalNum, successNum;
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(vertexCount, totalNum);
    AW_MACRO_EXPECT_EQ_INT(vertexCount, successNum);
    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    GtSpeciallabel2StructFree(vertex);
    free(t2V);
    free(t1V);
    free(vertex);
    uint64_t count = 0;
    ret = GmcGetVertexCount(stmt, g_labelName2, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(vertexCount, count);
    return GMERR_OK;
}

int GtSpeciallabel2StructBatchUpdate(GmcConnT *conn, GmcStmtT *stmt, GtSpeciallabelCfgT vertexCfg, uint32_t keyId,
                                     char *bytesValue, char *stringValue, bool isDefaultValue = true)
{
    int ret;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;
    uint16_t t1VCount = vertexCfg.t1VCount;
    uint16_t t2VCount = vertexCfg.t2VCount;
    uint32_t schemaVersion = vertexCfg.schemaVersion;
    GmcOperationTypeE optType = vertexCfg.optType;
    GmcNodeT *root = NULL, *t1Node = NULL;
    GtSpeciallabel2VertexT *vertex = (GtSpeciallabel2VertexT *)malloc(sizeof(GtSpeciallabel2VertexT));
    if (vertex == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
        return 1;
    }
    (void)memset(vertex, 0, sizeof(GtSpeciallabel2VertexT));
    TestLabelInfoT labelInfo = {g_labelName2, schemaVersion, g_testNameSpace};
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName2, schemaVersion, optType);
    RETURN_IFERR(ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int64_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName2, schemaVersion, optType);
        RETURN_IFERR(ret);
        if (keyId == 0) {
            GtSpeciallabel2StructSetPk(vertex, i);
        } else if (keyId == 1) {
            GtSpeciallabel2StructSetHashcluster(vertex, i, coefficient);
        } else if (keyId == 2) {
            GtSpeciallabel2StructSetLocalhash(vertex, i, coefficient);
        } else if (keyId == 3) {
            GtSpeciallabel2StructSetLocal(vertex, i);
        } else if (keyId == 4) {
            GtSpeciallabel2StructSetLpm4(vertex, i);
        }
        ret = testStructSetIndexKeyWithBuf(stmt, vertex, keyId, NULL, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GtSpeciallabel2GetNode(stmt, &root, &t1Node);
        TestSpecialT2UpdateSetOldProperty(root, i + coefficient, bytesValue, isDefaultValue);
        TestSpecialT2UpdateSetNewProperty(root, bytesValue, stringValue);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 3) {
            break;
        }
    }
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t totalNum, successNum;
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(vertexCount, totalNum);
    AW_MACRO_EXPECT_EQ_INT(vertexCount, successNum);
    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    GtSpeciallabel2StructFree(vertex);
    free(vertex);
    uint64_t count = 0;
    ret = GmcGetVertexCount(stmt, g_labelName2, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(vertexCount, count);
    return GMERR_OK;
}

int GtSpeciallabel2StructBatchDelete(GmcConnT *conn, GmcStmtT *stmt, GtSpeciallabelCfgT vertexCfg, uint32_t keyId)
{
    int ret;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;
    uint16_t t1VCount = vertexCfg.t1VCount;
    uint16_t t2VCount = vertexCfg.t2VCount;
    uint32_t schemaVersion = vertexCfg.schemaVersion;
    GmcOperationTypeE optType = vertexCfg.optType;
    GmcNodeT *root = NULL, *t1Node = NULL;
    GtSpeciallabel2VertexT *vertex = (GtSpeciallabel2VertexT *)malloc(sizeof(GtSpeciallabel2VertexT));
    if (vertex == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
        return 1;
    }
    (void)memset(vertex, 0, sizeof(GtSpeciallabel2VertexT));
    TestLabelInfoT labelInfo = {g_labelName2, schemaVersion, g_testNameSpace};
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName2, schemaVersion, optType);
    RETURN_IFERR(ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int64_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName2, schemaVersion, optType);
        RETURN_IFERR(ret);
        if (keyId == 0) {
            GtSpeciallabel2StructSetPk(vertex, i);
        } else if (keyId == 1) {
            GtSpeciallabel2StructSetHashcluster(vertex, i, coefficient);
        } else if (keyId == 2) {
            GtSpeciallabel2StructSetLocalhash(vertex, i, coefficient);
        } else if (keyId == 3) {
            GtSpeciallabel2StructSetLocal(vertex, i);
        } else if (keyId == 4) {
            GtSpeciallabel2StructSetLpm4(vertex, i);
        }
        ret = testStructSetIndexKeyWithBuf(stmt, vertex, keyId, NULL, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 3) {
            break;
        }
    }
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t totalNum, successNum;
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(vertexCount, totalNum);
    AW_MACRO_EXPECT_EQ_INT(vertexCount, successNum);
    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    GtSpeciallabel2StructFree(vertex);
    free(vertex);
    return GMERR_OK;
}

// 以结构化的方式 replace Speciallabel 表的数据
int GtSpeciallabel3StructWrite(GmcStmtT *stmt, GtSpeciallabelCfgT vertexCfg, char *bytesValue, char *stringValue,
                               bool isDefaultValue = true)
{
    int ret = GMERR_OK;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;
    uint16_t t1VCount = vertexCfg.t1VCount;
    uint16_t t2VCount = vertexCfg.t2VCount;
    uint32_t schemaVersion = vertexCfg.schemaVersion;
    GmcOperationTypeE optType = vertexCfg.optType;

    GtSpeciallabel3VertexT *vertex = (GtSpeciallabel3VertexT *)malloc(sizeof(GtSpeciallabel3VertexT));
    if (vertex == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
        return 1;
    }
    (void)memset(vertex, 0, sizeof(GtSpeciallabel3VertexT));
    GtSpeciallabelT1VVertexT *t1V = (GtSpeciallabelT1VVertexT *)malloc(sizeof(GtSpeciallabelT1VVertexT) * t1VCount);
    if (t1V == NULL) {
        AW_FUN_Log(LOG_ERROR, "t1V is NULL\n");
        return 1;
    }
    (void)memset(t1V, 0, sizeof(GtSpeciallabelT1VVertexT) * t1VCount);
    vertex->t1V = t1V;
    GtSpeciallabelT2VVertexT *t2V =
    (GtSpeciallabelT2VVertexT *)malloc(sizeof(GtSpeciallabelT2VVertexT) * t1VCount * t2VCount);
    if (t2V == NULL) {
        AW_FUN_Log(LOG_ERROR, "t2V is NULL\n");
        return 1;
    }
    (void)memset(t2V, 0, sizeof(GtSpeciallabelT2VVertexT) * t1VCount * t2VCount);
    for (int32_t i = 0; i < t1VCount; i++) {
        t1V[i].t2V = &t2V[t2VCount * ((0) + i)];
    }
    TestLabelInfoT labelInfo = {g_labelName2, schemaVersion, g_testNameSpace};
    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName2, schemaVersion, optType);
        RETURN_IFERR(ret);

        GtSpeciallabel3StructSetProperty(vertex, i, t1VCount, t2VCount, bytesValue, stringValue,
                                         isDefaultValue, coefficient);
        ret = testStructSetVertexWithBuf(stmt, vertex, &labelInfo);
        RETURN_IFERR(ret);

        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expAffectRows);
        RETURN_IFERR(ret);
    }
    GtSpeciallabel3StructFree(vertex);
    free(t2V);
    free(t1V);
    free(vertex);
    return GMERR_OK;
}

// 以结构化的方式 merge or update表的数据
int GtSpeciallabel3StructUpdate(GmcStmtT *stmt, GtSpeciallabelCfgT vertexCfg, char *bytesValue, char *stringValue,
                                bool isDefaultValue = true)
{
    int ret = GMERR_OK;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;
    uint16_t t1VCount = vertexCfg.t1VCount;
    uint16_t t2VCount = vertexCfg.t2VCount;
    uint32_t schemaVersion = vertexCfg.schemaVersion;
    GmcOperationTypeE optType = vertexCfg.optType;
    GmcNodeT *root, *t1V;
    GtSpeciallabel3VertexT *vertex = (GtSpeciallabel3VertexT *)malloc(sizeof(GtSpeciallabel3VertexT));
    if (vertex == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
        return 1;
    }
    (void)memset(vertex, 0, sizeof(GtSpeciallabel3VertexT));
    TestLabelInfoT labelInfo = {g_labelName2, schemaVersion, g_testNameSpace};

    for (int64_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName2, schemaVersion, optType);
        RETURN_IFERR(ret);
        GtSpeciallabel3StructSetPk(vertex, i);
        ret = testStructSetIndexKeyWithBuf(stmt, vertex, 0, NULL, &labelInfo);
        RETURN_IFERR(ret);
        GtSpeciallabel2GetNode(stmt, &root, &t1V);
        if (optType == GMC_OPERATION_MERGE && expAffectRows == 1) {
            GtSpeciallabel2SetLpmProperty(root, i);
            int64_t f0Value = i;
            ret = GmcNodeSetPropertyByName(root, (char *)"F0", GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        TestSpecialT2UpdateSetOldProperty(root, i + coefficient, bytesValue, isDefaultValue);
        TestSpecialT3UpdateSetNewProperty(root, i + coefficient, bytesValue, stringValue);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = TestGetAffactRows(stmt, expAffectRows);
        RETURN_IFERR(ret);
    }
    free(vertex);
    return GMERR_OK;
}

void GtSpeciallabel3StructGetLmpProperty(GtSpeciallabel3VertexT *vertex, int64_t value)
{
    uint32_t f3Value = 0;
    uint32_t f11Value = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    if (value <= MAX_MASK_LEN_16) {
        destIpAddr = ((value + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if (value > MAX_MASK_LEN_16 && value <= MAX_MASK_LEN_24) {
        destIpAddr = ((value + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((value + 2));
        maskLen = ((32) & 0xff);
    }
    int32_t ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &f3Value, &vertex->f3, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &f11Value, &vertex->f11, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &destIpAddr, &vertex->f12, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &maskLen, &vertex->f6, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void GtSpeciallabel3CompareVertexPropertyVector(
    GtSpeciallabel3VertexT *d, int32_t index, int64_t value, char *stringValue, uint16_t t1Count, uint16_t t2Count)
{
    uint32_t v1Value = value;
    int32_t ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &v1Value, &d->t1V[index].v1, sizeof(v1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &v1Value, &d->t1V[index].v2, sizeof(v1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_STRING, stringValue, d->t1V[index].v4, strlen(stringValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t v3Bits[2] = {0x55, 0x55};
    GmcBitMapT v3 = {0};
    v3.beginPos = 0;
    v3.endPos = 8 - 1;
    v3.bits = v3Bits;
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BITMAP, &v3Bits, &d->t1V[index].v3, sizeof(v3Bits));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < t2Count; i++) {
        ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &v1Value, &d->t1V[index].t2V[i].v1, sizeof(v1Value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &v1Value, &d->t1V[index].t2V[i].v2, sizeof(v1Value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = CompareVertexPropertyValue(GMC_DATATYPE_STRING, stringValue, d->t1V[index].t2V[i].v4,
                                         strlen(stringValue));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = CompareVertexPropertyValue(GMC_DATATYPE_BITMAP, &v3Bits, &d->t1V[index].t2V[i].v3, sizeof(v3Bits));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

void GtSpeciallabel3StructGetVector(
    GtSpeciallabel3VertexT *d, int64_t index, char *stringValue, uint16_t t1Count, uint16_t t2Count)
{
    int ret = 0, i;
    for (i = 0; i < t1Count; ++i) {
        GtSpeciallabel3CompareVertexPropertyVector(d, i, index, stringValue, t1Count, t2Count);
    }
}

void GtSpeciallabel3StructGetOldProperty(GtSpeciallabel3VertexT *vertex, int64_t index, char *bytesValue,
                                         char *stringValue, bool isDefaultVaule = true, int32_t updateValue = 0)
{
    int ret = 0;
    int64_t f0Value = index;
    uint64_t f1Value = index + updateValue;
    int32_t f2Value = index + updateValue;
    int16_t f4Value = (index + updateValue) % 32768;
    uint16_t f5Value = (index + updateValue) % 65536;
    uint64_t f7Value = index + updateValue;
    uint8_t fixedValue[SIMPLE_LABEL_FIXED_SIZE] = {0};
    uint8_t f9Value = 0;
    uint16_t f10Value = 0;
    uint8_t f13Value = (index + updateValue) & 0xf;
    if (!isDefaultVaule) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = j;
        }
        f9Value = (index + updateValue) % 31;
        f10Value = (index + updateValue) % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = 0xff;
        }
        f9Value = 0x1f;
        f10Value = 0x3ff;
    }
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &f0Value, &vertex->f0, sizeof(f0Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &f1Value, &vertex->f1, sizeof(f1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &f2Value, &vertex->f2, sizeof(f2Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &f4Value, &vertex->f4, sizeof(f4Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &f5Value, &vertex->f5, sizeof(f5Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_TIME, &f7Value, &vertex->f7, sizeof(f7Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, fixedValue, vertex->f8, SIMPLE_LABEL_FIXED_SIZE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(f9Value, vertex->f9);
    AW_MACRO_EXPECT_EQ_INT(f10Value, vertex->f10);
    AW_MACRO_EXPECT_EQ_INT(f13Value, vertex->f13);
    uint8_t f14[BYTES_LEN] = {0};
    (void)snprintf((char *)f14, BYTES_LEN, "%s", bytesValue);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BYTES, f14, vertex->f14, strlen(bytesValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GtSpeciallabel3StructGetVector(vertex, index, stringValue, 3, 3);
}

void GtSpeciallabel3StructGetMidProperty(GtSpeciallabel3VertexT *vertex, int64_t value, char *bytesValue,
                                         char *stringValue, bool isNull = false)
{
    if (!isNull) {
        uint8_t f15[BYTES_LEN] = {0};
        uint8_t f16[STRING2_LEN] = {0};
        (void)snprintf((char *)f15, BYTES_LEN, "%s", bytesValue);
        int32_t ret = CompareVertexPropertyValue(GMC_DATATYPE_BYTES, f15, vertex->f15, strlen(bytesValue));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        (void)snprintf((char *)f16, STRING2_LEN, "%s", stringValue);
        ret = CompareVertexPropertyValue(GMC_DATATYPE_STRING, f16, vertex->f16, strlen(stringValue));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        AW_MACRO_EXPECT_EQ_INT(0, vertex->f15Len);
        AW_MACRO_EXPECT_EQ_INT(0, vertex->f16Len);
    }
}

void GtSpeciallabel3StructGetNewProperty(GtSpeciallabel3VertexT *vertex, int64_t value, char *bytesValue,
                                         char *stringValue, bool isNull = false)
{
    if (!isNull) {
        char *stringTest = (char *)malloc(STRING3_LEN);
        if (stringTest == NULL) {
            AW_FUN_Log(LOG_ERROR, "stringTest is NULL\n");
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
        }
        (void)snprintf((char *)stringTest, STRING3_LEN, "s%032766d", value);
        int32_t ret = CompareVertexPropertyValue(GMC_DATATYPE_STRING, stringTest, vertex->f17, strlen(stringTest));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = CompareVertexPropertyValue(GMC_DATATYPE_STRING, stringTest, vertex->f18, strlen(stringTest));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = CompareVertexPropertyValue(GMC_DATATYPE_STRING, stringTest, vertex->f19, strlen(stringTest));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = CompareVertexPropertyValue(GMC_DATATYPE_STRING, stringTest, vertex->f20, strlen(stringTest));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = CompareVertexPropertyValue(GMC_DATATYPE_BYTES, stringTest, vertex->f21, strlen(stringTest));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free(stringTest);
    } else {
        AW_MACRO_EXPECT_EQ_INT(0, vertex->f17Len);
        AW_MACRO_EXPECT_EQ_INT(0, vertex->f18Len);
        AW_MACRO_EXPECT_EQ_INT(0, vertex->f19Len);
        AW_MACRO_EXPECT_EQ_INT(0, vertex->f20Len);
        AW_MACRO_EXPECT_EQ_INT(0, vertex->f21Len);
    }
}

int GtSpeciallabel3StructIndexScan(GmcStmtT *stmt, GtSpeciallabelCfgT vertexCfg, uint32_t keyId,
                                   char *bytesValue, char *stringValue, bool isDefaultValue = true)
{
    int ret;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    uint32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;
    uint16_t t1VCount = vertexCfg.t1VCount;
    uint16_t t2VCount = vertexCfg.t2VCount;
    uint32_t schemaVersion = vertexCfg.schemaVersion;
    GmcOperationTypeE optType = vertexCfg.optType;
    bool fieldIsNull[8] = {0};
    for (int i = 0; i < 8; i++) {
        fieldIsNull[i] = vertexCfg.fieldIsNull[i];
    }
    GtSpeciallabel3VertexT vertex = (GtSpeciallabel3VertexT){0};
    TestLabelInfoT labelInfo = {g_labelName2, schemaVersion, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &vertex, &deseri, &deseriCtx, false, &labelInfo);

    for (int i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName2, schemaVersion, GMC_OPERATION_SCAN);
        RETURN_IFERR(ret);
        if (keyId == 0) {
            GtSpeciallabel3StructSetPk(&vertex, i);
        } else if (keyId == 1) {
            GtSpeciallabel3StructSetHashcluster(&vertex, i, coefficient);
        } else if (keyId == 2) {
            GtSpeciallabel3StructSetLocalhash(&vertex, i, coefficient);
        } else if (keyId == 3) {
            GtSpeciallabel3StructSetLocal(&vertex, i);
        } else if (keyId == 4) {
            GtSpeciallabel3StructSetLpm4(&vertex, i);
        }
        ret = testStructSetIndexKeyWithBuf(stmt, &vertex, keyId, NULL, &labelInfo);
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        bool isFinish = false;
        uint32_t cnt = 0;
        if (keyId == 3) {
            if (i == startPkVal) {
                while (!isFinish) {
                    ret = GmcFetch(stmt, &isFinish);
                    RETURN_IFERR(ret);
                    if (isFinish) {
                        break;
                    }
                    cnt++;
                    ret = testStructGetVertexDeseri(stmt, &deseri);
                    RETURN_IFERR(ret);
                    int64_t f0Value = vertex.f0;
                    if (f0Value >= startPkVal && f0Value < startPkVal + vertexCount) {
                        GtSpeciallabel3StructGetLmpProperty(&vertex, f0Value);
                        GtSpeciallabel3StructGetOldProperty(&vertex, f0Value, bytesValue, stringValue,
                                                            isDefaultValue, coefficient);
                        GtSpeciallabel3StructGetMidProperty(&vertex, f0Value + coefficient, bytesValue, stringValue,
                                                            fieldIsNull[1]);
                        GtSpeciallabel3StructGetNewProperty(&vertex, f0Value + coefficient, bytesValue, stringValue,
                                                            fieldIsNull[2]);
                    }
                }
            }
            AW_MACRO_EXPECT_EQ_INT(vertexCfg.expAffectRows, cnt);
            return GMERR_OK;
        } else {
            ret = GmcFetch(stmt, &isFinish);
            RETURN_IFERR(ret);
            ret = testStructGetVertexDeseri(stmt, &deseri);
            RETURN_IFERR(ret);
            GtSpeciallabel3StructGetLmpProperty(&vertex, i);
            GtSpeciallabel3StructGetOldProperty(&vertex, i, bytesValue, stringValue,
                                                isDefaultValue, coefficient);
            GtSpeciallabel3StructGetMidProperty(&vertex, i + coefficient, bytesValue, stringValue, fieldIsNull[1]);
            GtSpeciallabel3StructGetNewProperty(&vertex, i + coefficient, bytesValue, stringValue, fieldIsNull[2]);
            ret = GmcFetch(stmt, &isFinish);
            RETURN_IFERR(ret);
            AW_MACRO_EXPECT_EQ_INT(true, isFinish);
        }
    }
    deSeriFreeDynMem(&deseriCtx, true);
    return GMERR_OK;
}

int GtSpeciallabel3StructBatchWrite(GmcConnT *conn, GmcStmtT *stmt, GtSpeciallabelCfgT vertexCfg, char *bytesValue,
                                    char *stringValue, bool isDefaultValue = true)
{
    int ret;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;
    uint16_t t1VCount = vertexCfg.t1VCount;
    uint16_t t2VCount = vertexCfg.t2VCount;
    uint32_t schemaVersion = vertexCfg.schemaVersion;
    GmcOperationTypeE optType = vertexCfg.optType;
    GmcNodeT *root = NULL, *t1Node = NULL;
    GtSpeciallabel3VertexT *vertex = (GtSpeciallabel3VertexT *)malloc(sizeof(GtSpeciallabel3VertexT));
    if (vertex == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
        return 1;
    }
    (void)memset(vertex, 0, sizeof(GtSpeciallabel3VertexT));
    GtSpeciallabelT1VVertexT *t1V = (GtSpeciallabelT1VVertexT *)malloc(sizeof(GtSpeciallabelT1VVertexT) * t1VCount);
    if (t1V == NULL) {
        AW_FUN_Log(LOG_ERROR, "t1V is NULL\n");
        return 1;
    }
    (void)memset(t1V, 0, sizeof(GtSpeciallabelT1VVertexT) * t1VCount);
    vertex->t1V = t1V;
    GtSpeciallabelT2VVertexT *t2V =
    (GtSpeciallabelT2VVertexT *)malloc(sizeof(GtSpeciallabelT2VVertexT) * t1VCount * t2VCount);
    if (t2V == NULL) {
        AW_FUN_Log(LOG_ERROR, "t2V is NULL\n");
        return 1;
    }
    (void)memset(t2V, 0, sizeof(GtSpeciallabelT2VVertexT) * t1VCount * t2VCount);
    for (int32_t i = 0; i < t1VCount; i++) {
        t1V[i].t2V = &t2V[t2VCount * ((0) + i)];
    }
    TestLabelInfoT labelInfo = {g_labelName2, schemaVersion, g_testNameSpace};
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName2, schemaVersion, optType);
    RETURN_IFERR(ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int64_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName2, schemaVersion, optType);
        RETURN_IFERR(ret);
        if (optType == GMC_OPERATION_MERGE) {
            GtSpeciallabel3StructSetPk(vertex, i);
            ret = testStructSetIndexKeyWithBuf(stmt, vertex, 0, NULL, &labelInfo);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            GtSpeciallabel2GetNode(stmt, &root, &t1Node);
            if (expAffectRows == 1) {
                GtSpeciallabel2SetLpmProperty(root, i);
                int64_t f0Value = i;
                ret = GmcNodeSetPropertyByName(root, (char *)"F0", GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            }
            TestSpecialT2UpdateSetOldProperty(root, i + coefficient, bytesValue, isDefaultValue);
            TestSpecialT3UpdateSetNewProperty(root, i + coefficient, bytesValue, stringValue);
            GtSpeciallabel2NodeSet(t1Node, i, stringValue, t1VCount, t2VCount);
        } else {
            GtSpeciallabel3StructSetProperty(vertex, i, t1VCount, t2VCount, bytesValue, stringValue,
                                             isDefaultValue, coefficient);
            ret = testStructSetVertexWithBuf(stmt, vertex, &labelInfo);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t totalNum, successNum;
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(vertexCount, totalNum);
    AW_MACRO_EXPECT_EQ_INT(vertexCount, successNum);
    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    GtSpeciallabel3StructFree(vertex);
    free(t2V);
    free(t1V);
    free(vertex);
    uint64_t count = 0;
    ret = GmcGetVertexCount(stmt, g_labelName2, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(vertexCount, count);
    return GMERR_OK;
}

int GtSpeciallabel3StructBatchUpdate(GmcConnT *conn, GmcStmtT *stmt, GtSpeciallabelCfgT vertexCfg, uint32_t keyId,
                                     char *bytesValue, char *stringValue, bool isDefaultValue = true)
{
    int ret;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;
    uint16_t t1VCount = vertexCfg.t1VCount;
    uint16_t t2VCount = vertexCfg.t2VCount;
    uint32_t schemaVersion = vertexCfg.schemaVersion;
    GmcOperationTypeE optType = vertexCfg.optType;
    GmcNodeT *root = NULL, *t1Node = NULL;
    GtSpeciallabel3VertexT *vertex = (GtSpeciallabel3VertexT *)malloc(sizeof(GtSpeciallabel3VertexT));
    if (vertex == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
        return 1;
    }
    (void)memset(vertex, 0, sizeof(GtSpeciallabel3VertexT));
    TestLabelInfoT labelInfo = {g_labelName2, schemaVersion, g_testNameSpace};
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName2, schemaVersion, optType);
    RETURN_IFERR(ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int64_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName2, schemaVersion, optType);
        RETURN_IFERR(ret);
        if (keyId == 0) {
            GtSpeciallabel3StructSetPk(vertex, i);
        } else if (keyId == 1) {
            GtSpeciallabel3StructSetHashcluster(vertex, i, coefficient);
        } else if (keyId == 2) {
            GtSpeciallabel3StructSetLocalhash(vertex, i, coefficient);
        } else if (keyId == 3) {
            GtSpeciallabel3StructSetLocal(vertex, i);
        } else if (keyId == 4) {
            GtSpeciallabel3StructSetLpm4(vertex, i);
        }
        ret = testStructSetIndexKeyWithBuf(stmt, vertex, keyId, NULL, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GtSpeciallabel2GetNode(stmt, &root, &t1Node);
        TestSpecialT2UpdateSetOldProperty(root, i + coefficient, bytesValue, isDefaultValue);
        TestSpecialT3UpdateSetNewProperty(root, i + coefficient, bytesValue, stringValue);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 3) {
            break;
        }
    }
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t totalNum, successNum;
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(vertexCount, totalNum);
    AW_MACRO_EXPECT_EQ_INT(vertexCount, successNum);
    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    GtSpeciallabel3StructFree(vertex);
    free(vertex);
    uint64_t count = 0;
    ret = GmcGetVertexCount(stmt, g_labelName2, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(vertexCount, count);
    return GMERR_OK;
}

int GtSpeciallabel3StructBatchDelete(GmcConnT *conn, GmcStmtT *stmt, GtSpeciallabelCfgT vertexCfg, uint32_t keyId)
{
    int ret;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;
    uint16_t t1VCount = vertexCfg.t1VCount;
    uint16_t t2VCount = vertexCfg.t2VCount;
    uint32_t schemaVersion = vertexCfg.schemaVersion;
    GmcOperationTypeE optType = vertexCfg.optType;
    GmcNodeT *root = NULL, *t1Node = NULL;
    GtSpeciallabel3VertexT *vertex = (GtSpeciallabel3VertexT *)malloc(sizeof(GtSpeciallabel3VertexT));
    if (vertex == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
        return 1;
    }
    (void)memset(vertex, 0, sizeof(GtSpeciallabel3VertexT));
    TestLabelInfoT labelInfo = {g_labelName2, schemaVersion, g_testNameSpace};
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName2, schemaVersion, optType);
    RETURN_IFERR(ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int64_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName2, schemaVersion, optType);
        RETURN_IFERR(ret);
        if (keyId == 0) {
            GtSpeciallabel3StructSetPk(vertex, i);
        } else if (keyId == 1) {
            GtSpeciallabel3StructSetHashcluster(vertex, i, coefficient);
        } else if (keyId == 2) {
            GtSpeciallabel3StructSetLocalhash(vertex, i, coefficient);
        } else if (keyId == 3) {
            GtSpeciallabel3StructSetLocal(vertex, i);
        } else if (keyId == 4) {
            GtSpeciallabel3StructSetLpm4(vertex, i);
        }
        ret = testStructSetIndexKeyWithBuf(stmt, vertex, keyId, NULL, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId == 3) {
            break;
        }
    }
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t totalNum, successNum;
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(vertexCount, totalNum);
    AW_MACRO_EXPECT_EQ_INT(vertexCount, successNum);
    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    GtSpeciallabel3StructFree(vertex);
    free(vertex);
    return GMERR_OK;
}

// 新老版本之间结构化写
int GtSpeciallabelOldNewVersionStructWrite(GmcStmtT *stmt, GtSpeciallabelCfgT vertexCfg, char *bytesValue,
                                           char *stringValue, bool isDefaultValue = true)
{
    int ret = GMERR_OK;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;
    uint16_t t1VCount = vertexCfg.t1VCount;
    uint16_t t2VCount = vertexCfg.t2VCount;
    uint32_t schemaVersion = vertexCfg.schemaVersion;
    GmcOperationTypeE optType = vertexCfg.optType;

    GtSpeciallabel3VertexT *vertex = (GtSpeciallabel3VertexT *)malloc(sizeof(GtSpeciallabel3VertexT));
    if (vertex == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
        return 1;
    }
    (void)memset(vertex, 0, sizeof(GtSpeciallabel3VertexT));
    GtSpeciallabelT1VVertexT *t1V = (GtSpeciallabelT1VVertexT *)malloc(sizeof(GtSpeciallabelT1VVertexT) * t1VCount);
    if (t1V == NULL) {
        AW_FUN_Log(LOG_ERROR, "t1V is NULL\n");
        return 1;
    }
    (void)memset(t1V, 0, sizeof(GtSpeciallabelT1VVertexT) * t1VCount);
    vertex->t1V = t1V;
    GtSpeciallabelT2VVertexT *t2V =
    (GtSpeciallabelT2VVertexT *)malloc(sizeof(GtSpeciallabelT2VVertexT) * t1VCount * t2VCount);
    if (t2V == NULL) {
        AW_FUN_Log(LOG_ERROR, "t2V is NULL\n");
        return 1;
    }
    (void)memset(t2V, 0, sizeof(GtSpeciallabelT2VVertexT) * t1VCount * t2VCount);
    for (int32_t i = 0; i < t1VCount; i++) {
        t1V[i].t2V = &t2V[t2VCount * ((0) + i)];
    }
    TestLabelInfoT labelInfo = {g_labelName2, schemaVersion, g_testNameSpace};
    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName2, schemaVersion, optType);
        RETURN_IFERR(ret);
        GtSpeciallabel3StructSetPk(vertex, i);
        GtSpeciallabel3StructSetHashcluster(vertex, i, coefficient);
        GtSpeciallabel3StructSetLocalhash(vertex, i, coefficient);
        GtSpeciallabel3StructSetLocal(vertex, i);
        GtSpeciallabel3StructSetLpm4(vertex, i);
        GtSpeciallabel3StructSetOldProperty(vertex, i, t1VCount, t2VCount, bytesValue, stringValue, isDefaultValue,
                                            coefficient);
        if (schemaVersion == 0) {
        } else if (schemaVersion == 1) {
            GtSpeciallabel3StructSetMidProperty(vertex, bytesValue, stringValue);
        } else if (schemaVersion == 2) {
            GtSpeciallabel3StructSetNewProperty(vertex, i + coefficient, bytesValue, stringValue);
        }
        ret = testStructSetVertexWithBuf(stmt, vertex, &labelInfo);
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expAffectRows);
        RETURN_IFERR(ret);
    }
    GtSpeciallabel3StructFree(vertex);
    free(t2V);
    free(t1V);
    free(vertex);
    return GMERR_OK;
}

int GtSpeciallabelStructWrite(GmcStmtT *stmt, GtSpeciallabelCfgT vertexCfg, char *bytesValue, char *stringValue,
                              bool isDefaultValue = true)
{
    int ret = GMERR_OK;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;
    uint16_t t1VCount = vertexCfg.t1VCount;
    uint16_t t2VCount = vertexCfg.t2VCount;
    uint32_t schemaVersion = vertexCfg.schemaVersion;
    GmcOperationTypeE optType = vertexCfg.optType;

    GtSpeciallabelVertexT *vertex = (GtSpeciallabelVertexT *)malloc(sizeof(GtSpeciallabelVertexT));
    if (vertex == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
        return 1;
    }
    (void)memset(vertex, 0, sizeof(GtSpeciallabelVertexT));
    GtSpeciallabelT1VVertexT *t1V = (GtSpeciallabelT1VVertexT *)malloc(sizeof(GtSpeciallabelT1VVertexT) * t1VCount);
    if (t1V == NULL) {
        AW_FUN_Log(LOG_ERROR, "t1V is NULL\n");
        return 1;
    }
    (void)memset(t1V, 0, sizeof(GtSpeciallabelT1VVertexT) * t1VCount);
    vertex->t1V = t1V;
    GtSpeciallabelT2VVertexT *t2V =
    (GtSpeciallabelT2VVertexT *)malloc(sizeof(GtSpeciallabelT2VVertexT) * t1VCount * t2VCount);
    if (t2V == NULL) {
        AW_FUN_Log(LOG_ERROR, "t2V is NULL\n");
        return 1;
    }
    (void)memset(t2V, 0, sizeof(GtSpeciallabelT2VVertexT) * t1VCount * t2VCount);
    for (int32_t i = 0; i < t1VCount; i++) {
        t1V[i].t2V = &t2V[t2VCount * ((0) + i)];
    }
    TestLabelInfoT labelInfo = {g_labelName2, schemaVersion, g_testNameSpace};
    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName2, schemaVersion, optType);
        RETURN_IFERR(ret);
        GtSpeciallabelStructSetPk(vertex, i);
        GtSpeciallabelStructSetHashcluster(vertex, i, coefficient);
        GtSpeciallabelStructSetLocalhash(vertex, i, coefficient);
        GtSpeciallabelStructSetLocal(vertex, i);
        GtSpeciallabelStructSetLpm4(vertex, i);
        GtSpeciallabelStructSetOldProperty(vertex, i, t1VCount, t2VCount, bytesValue, stringValue, isDefaultValue,
                                           coefficient);
        ret = testStructSetVertexWithBuf(stmt, vertex, &labelInfo);
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expAffectRows);
        RETURN_IFERR(ret);
    }
    GtSpeciallabelStructFree(vertex);
    free(t2V);
    free(t1V);
    free(vertex);
    return GMERR_OK;
}

int GtSpeciallabelOldVersionStructWriteFailed(GmcStmtT *stmt, GtSpeciallabelCfgT vertexCfg, char *bytesValue,
                                              char *stringValue, bool isDefaultValue = true)
{
    int ret = GMERR_OK;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;
    uint16_t t1VCount = vertexCfg.t1VCount;
    uint16_t t2VCount = vertexCfg.t2VCount;
    uint32_t schemaVersion = vertexCfg.schemaVersion;
    GmcOperationTypeE optType = vertexCfg.optType;

    GtSpeciallabel3VertexT *vertex = (GtSpeciallabel3VertexT *)malloc(sizeof(GtSpeciallabel3VertexT));
    if (vertex == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
        return 1;
    }
    (void)memset(vertex, 0, sizeof(GtSpeciallabel3VertexT));
    GtSpeciallabelT1VVertexT *t1V = (GtSpeciallabelT1VVertexT *)malloc(sizeof(GtSpeciallabelT1VVertexT) * t1VCount);
    if (t1V == NULL) {
        AW_FUN_Log(LOG_ERROR, "t1V is NULL\n");
        return 1;
    }
    (void)memset(t1V, 0, sizeof(GtSpeciallabelT1VVertexT) * t1VCount);
    vertex->t1V = t1V;
    GtSpeciallabelT2VVertexT *t2V =
    (GtSpeciallabelT2VVertexT *)malloc(sizeof(GtSpeciallabelT2VVertexT) * t1VCount * t2VCount);
    if (t2V == NULL) {
        AW_FUN_Log(LOG_ERROR, "t2V is NULL\n");
        return 1;
    }
    (void)memset(t2V, 0, sizeof(GtSpeciallabelT2VVertexT) * t1VCount * t2VCount);
    for (int32_t i = 0; i < t1VCount; i++) {
        t1V[i].t2V = &t2V[t2VCount * ((0) + i)];
    }
    TestLabelInfoT labelInfo = {g_labelName2, schemaVersion, g_testNameSpace};
    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName2, schemaVersion, optType);
        RETURN_IFERR(ret);
        GtSpeciallabel3StructSetPk(vertex, i);
        GtSpeciallabel3StructSetHashcluster(vertex, i, coefficient);
        GtSpeciallabel3StructSetLocalhash(vertex, i, coefficient);
        GtSpeciallabel3StructSetLocal(vertex, i);
        GtSpeciallabel3StructSetLpm4(vertex, i);
        GtSpeciallabel3StructSetOldProperty(vertex, i, t1VCount, t2VCount, bytesValue, stringValue, isDefaultValue,
                                            coefficient);
        if (schemaVersion == 0) {
            GtSpeciallabel3StructSetMidProperty(vertex, bytesValue, stringValue);
        } else if (schemaVersion == 1) {
            GtSpeciallabel3StructSetNewProperty(vertex, i + coefficient, bytesValue, stringValue);
        }
        ret = testStructSetVertexWithBuf(stmt, vertex, &labelInfo);
        RETURN_IFERR(ret);

        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 0);
        RETURN_IFERR(ret);
    }
    GtSpeciallabel3StructFree(vertex);
    free(t2V);
    free(t1V);
    free(vertex);
    return GMERR_OK;
}

int GtSpeciallabel3StructIndexOldNewScan(GmcStmtT *stmt, GtSpeciallabelCfgT vertexCfg, uint32_t keyId,
                                         char *bytesValue, char *stringValue, bool isDefaultValue = true)
{
    int ret;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;
    uint16_t t1VCount = vertexCfg.t1VCount;
    uint16_t t2VCount = vertexCfg.t2VCount;
    uint32_t schemaVersion = vertexCfg.schemaVersion;
    GmcOperationTypeE optType = vertexCfg.optType;
    GtSpeciallabel3VertexT vertex = (GtSpeciallabel3VertexT){0};
    TestLabelInfoT labelInfo = {g_labelName2, schemaVersion, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &vertex, &deseri, &deseriCtx, false, &labelInfo);

    for (int i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName2, schemaVersion, GMC_OPERATION_SCAN);
        RETURN_IFERR(ret);
        if (keyId == 0) {
            GtSpeciallabel3StructSetPk(&vertex, i);
        } else if (keyId == 1) {
            GtSpeciallabel3StructSetHashcluster(&vertex, i, coefficient);
        } else if (keyId == 2) {
            GtSpeciallabel3StructSetLocalhash(&vertex, i, coefficient);
        } else if (keyId == 3) {
            GtSpeciallabel3StructSetLocal(&vertex, i);
        } else if (keyId == 4) {
            GtSpeciallabel3StructSetLpm4(&vertex, i);
        }
        ret = testStructSetIndexKeyWithBuf(stmt, &vertex, keyId, NULL, &labelInfo);
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        bool isFinish = false;
        uint32_t cnt = 0;
        if (keyId == 3) {
            if (i == startPkVal) {
                while (!isFinish) {
                    ret = GmcFetch(stmt, &isFinish);
                    RETURN_IFERR(ret);
                    if (isFinish) {
                        break;
                    }
                    cnt++;
                    ret = testStructGetVertexDeseri(stmt, &deseri);
                    RETURN_IFERR(ret);
                    int64_t f0Value = vertex.f0;
                    GtSpeciallabel3StructGetLmpProperty(&vertex, f0Value);
                    GtSpeciallabel3StructGetOldProperty(&vertex, f0Value, bytesValue, stringValue,
                                                        isDefaultValue, coefficient);
                    if (schemaVersion == 0) {
                    } else if (schemaVersion == 1) {
                        GtSpeciallabel3StructGetMidProperty(&vertex, f0Value + coefficient, bytesValue, stringValue);
                    } else if (schemaVersion == 2) {
                        GtSpeciallabel3StructGetNewProperty(&vertex, f0Value + coefficient, bytesValue, stringValue);
                    }
                }
                AW_MACRO_EXPECT_EQ_INT(expAffectRows, cnt);
                return GMERR_OK;
            }
        } else {
            ret = GmcFetch(stmt, &isFinish);
            RETURN_IFERR(ret);
            ret = testStructGetVertexDeseri(stmt, &deseri);
            RETURN_IFERR(ret);
            GtSpeciallabel3StructGetLmpProperty(&vertex, i);
            GtSpeciallabel3StructGetOldProperty(&vertex, i, bytesValue, stringValue,
                                                isDefaultValue, coefficient);
            if (schemaVersion == 0) {
            } else if (schemaVersion == 1) {
                GtSpeciallabel3StructGetMidProperty(&vertex, i + coefficient, bytesValue, stringValue);
            } else if (schemaVersion == 2) {
                GtSpeciallabel3StructGetNewProperty(&vertex, i + coefficient, bytesValue, stringValue);
            }
            ret = GmcFetch(stmt, &isFinish);
            RETURN_IFERR(ret);
            AW_MACRO_EXPECT_EQ_INT(true, isFinish);
        }
    }
    deSeriFreeDynMem(&deseriCtx, true);
    return GMERR_OK;
}

int GtSpeciallabelStructIndexScan(GmcStmtT *stmt, GtSpeciallabelCfgT vertexCfg, uint32_t keyId,
                                  char *bytesValue, char *stringValue, bool isDefaultValue = true)
{
    int ret;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;
    uint16_t t1VCount = vertexCfg.t1VCount;
    uint16_t t2VCount = vertexCfg.t2VCount;
    uint32_t schemaVersion = vertexCfg.schemaVersion;
    GmcOperationTypeE optType = vertexCfg.optType;
    GtSpeciallabelVertexT vertex = (GtSpeciallabelVertexT){0};
    TestLabelInfoT labelInfo = {g_labelName2, schemaVersion, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &vertex, &deseri, &deseriCtx, false, &labelInfo);

    for (int i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName2, schemaVersion, GMC_OPERATION_SCAN);
        RETURN_IFERR(ret);
        if (keyId == 0) {
            GtSpeciallabelStructSetPk(&vertex, i);
        } else if (keyId == 1) {
            GtSpeciallabelStructSetHashcluster(&vertex, i, coefficient);
        } else if (keyId == 2) {
            GtSpeciallabelStructSetLocalhash(&vertex, i, coefficient);
        } else if (keyId == 3) {
            GtSpeciallabelStructSetLocal(&vertex, i);
        } else if (keyId == 4) {
            GtSpeciallabelStructSetLpm4(&vertex, i);
        }
        ret = testStructSetIndexKeyWithBuf(stmt, &vertex, keyId, NULL, &labelInfo);
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        bool isFinish = false;
        uint32_t cnt = 0;
        if (keyId == 3) {
            if (i == startPkVal) {
                while (!isFinish) {
                    ret = GmcFetch(stmt, &isFinish);
                    RETURN_IFERR(ret);
                    if (isFinish) {
                        break;
                    }
                    cnt++;
                    ret = testStructGetVertexDeseri(stmt, &deseri);
                    RETURN_IFERR(ret);
                    int64_t f0Value = vertex.f0;
                    GtSpeciallabelStructGetLmpProperty(&vertex, f0Value);
                    GtSpeciallabelStructGetOldProperty(&vertex, f0Value, bytesValue, stringValue,
                                                       isDefaultValue, coefficient);
                }
            }
            AW_MACRO_EXPECT_EQ_INT(vertexCfg.expAffectRows, cnt);
            return GMERR_OK;
        } else {
            ret = GmcFetch(stmt, &isFinish);
            RETURN_IFERR(ret);
            ret = testStructGetVertexDeseri(stmt, &deseri);
            RETURN_IFERR(ret);
            GtSpeciallabelStructGetLmpProperty(&vertex, i);
            GtSpeciallabelStructGetOldProperty(&vertex, i, bytesValue, stringValue,
                                               isDefaultValue, coefficient);
            ret = GmcFetch(stmt, &isFinish);
            RETURN_IFERR(ret);
            AW_MACRO_EXPECT_EQ_INT(true, isFinish);
        }
    }
    deSeriFreeDynMem(&deseriCtx, true);
    return GMERR_OK;
}

int TestSpecialT3NewOldVersionGeneralRead(GmcStmtT *stmt, GtSpeciallabelCfgT vertexCfg, uint32_t keyId,
                                          char *bytesValue, char *stringValue, bool isDefaultValue = true)
{
    int ret;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;
    uint16_t t1VCount = vertexCfg.t1VCount;
    uint16_t t2VCount = vertexCfg.t2VCount;
    uint32_t schemaVersion = vertexCfg.schemaVersion;
    GmcOperationTypeE optType = vertexCfg.optType;
    bool fieldIsNull[8] = {0};
    int32_t localFlag = 0;
    uint32_t fetchNum = 0;
    GmcNodeT *root = NULL, *t1Node = NULL;
    for (int i = 0; i < 8; i++) {
        fieldIsNull[i] = vertexCfg.fieldIsNull[i];
    }
    bool isFinish = false;
    AW_FUN_Log(LOG_INFO, "labelName = %s schemaVersion = %d keyId = %d", g_labelName2, schemaVersion, keyId);
    for (int i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName2, schemaVersion, GMC_OPERATION_SCAN);
        RETURN_IFERR(ret);
        if (keyId == 0) {
        TestSimpleT1PkIndexSet(stmt, i);
        } else if (keyId == 1) {
            TestSimpleT1HashclusterIndexSet(stmt, i + coefficient);
        } else if (keyId == 2) {
            TestSimpleT1LocalhashIndexSet(stmt, i + coefficient);
        } else if (keyId == 3) {
            TestSimpleT1LocalIndexSet(stmt, i);
        } else if (keyId == 4) {
            TestSimpleT1LpmIndexSet(stmt, i);
        } else {
            AW_FUN_Log(LOG_ERROR, "keyId error!! keyId = %d", keyId);
        }
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = GmcFetch(stmt, &isFinish);
        RETURN_IFERR(ret);
        if (keyId == 3 && localFlag == 0) {
            localFlag = 1;
            int64_t f0Value = 0;
            bool isNull = false;
            bool newFieldIsNull[2] = {true};
            while (!isFinish) {
                fetchNum++;
                GtSpeciallabel2GetNode(stmt, &root, &t1Node);
                ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                RETURN_IFERR(ret);
                if (f0Value >= startPkVal && f0Value < startPkVal + vertexCount) {
                    TestSpecialT3UpdateGetOldPropertyByName(root, f0Value + coefficient, bytesValue, isDefaultValue);
                    TestSpecialT3GetLpmProperty(root, f0Value);
                    GtSpeciallabel3GeneralGetVector(t1Node, f0Value, stringValue, t1VCount, t2VCount);
                    if (schemaVersion == 0) {
                        TestSpecialT3MidVersionGetNewFieldFailed(root, bytesValue, stringValue);
                        TestSpecialT3MostNewVersionGetNewFieldFailed(root, f0Value + coefficient);
                    } else if (schemaVersion == 1) {
                        TestSpecialT3MidVersionGetNewField(root, bytesValue, stringValue, fieldIsNull[1]);
                        TestSpecialT3MostNewVersionGetNewFieldFailed(root, f0Value + coefficient);
                    } else if (schemaVersion == 2) {
                        TestSpecialT3MidVersionGetNewField(root, bytesValue, stringValue, fieldIsNull[1]);
                        TestSpecialT3MostNewVersionGetNewField(root, f0Value + coefficient, fieldIsNull[2]);
                    }
                }
                ret = GmcFetch(stmt, &isFinish);
                RETURN_IFERR(ret);
                ret = GmcFreeNode(root);
                RETURN_IFERR(ret);
                root = NULL;
            }
            AW_MACRO_EXPECT_EQ_INT(expAffectRows, fetchNum);
            fetchNum = 0;
            return 0;
        } else if (keyId != 3) {
            GtSpeciallabel2GetNode(stmt, &root, &t1Node);
            TestSpecialT3UpdateGetOldPropertyByName(root, i + coefficient, bytesValue, isDefaultValue);
            TestSpecialT3GetLpmProperty(root, i);
            GtSpeciallabel3GeneralGetVector(t1Node, i, stringValue, t1VCount, t2VCount);
            if (schemaVersion == 0) {
                TestSpecialT3MidVersionGetNewFieldFailed(root, bytesValue, stringValue);
                TestSpecialT3MostNewVersionGetNewFieldFailed(root, i + coefficient);
            } else if (schemaVersion == 1) {
                TestSpecialT3MidVersionGetNewField(root, bytesValue, stringValue, fieldIsNull[1]);
                TestSpecialT3MostNewVersionGetNewFieldFailed(root, i + coefficient);
            } else if (schemaVersion == 2) {
                TestSpecialT3MidVersionGetNewField(root, bytesValue, stringValue, fieldIsNull[1]);
                TestSpecialT3MostNewVersionGetNewField(root, i + coefficient, fieldIsNull[2]);
            }
            ret = GmcFetch(stmt, &isFinish);
            RETURN_IFERR(ret);
            AW_MACRO_EXPECT_EQ_INT(true, isFinish);
            ret = GmcFreeNode(root);
            RETURN_IFERR(ret);
            root = NULL;
        }
    }
    return 0;
}

// 以结构化的方式 merge or update表的数据
int GtSpeciallabel3StructUpdateOrMerge(GmcStmtT *stmt, GtSpeciallabelCfgT vertexCfg, uint32_t keyId, char *bytesValue,
                                       char *stringValue, bool isDefaultValue = true)
{
    int ret = GMERR_OK;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;
    uint16_t t1VCount = vertexCfg.t1VCount;
    uint16_t t2VCount = vertexCfg.t2VCount;
    uint32_t schemaVersion = vertexCfg.schemaVersion;
    GmcOperationTypeE optType = vertexCfg.optType;
    GmcNodeT *root, *t1V;
    GtSpeciallabel3VertexT *vertex = (GtSpeciallabel3VertexT *)malloc(sizeof(GtSpeciallabel3VertexT));
    if (vertex == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
        return 1;
    }
    (void)memset(vertex, 0, sizeof(GtSpeciallabel3VertexT));
    TestLabelInfoT labelInfo = {g_labelName2, schemaVersion, g_testNameSpace};

    for (int64_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName2, schemaVersion, optType);
        RETURN_IFERR(ret);
        if (optType == GMC_OPERATION_MERGE) {
            GtSpeciallabel3StructSetPk(vertex, i);
            ret = testStructSetIndexKeyWithBuf(stmt, vertex, 0, NULL, &labelInfo);
            RETURN_IFERR(ret);
        } else {
            if (keyId == 0) {
                GtSpeciallabel3StructSetPk(vertex, i);
            } else if (keyId == 1) {
                GtSpeciallabel3StructSetHashcluster(vertex, i);
            } else if (keyId == 2) {
                GtSpeciallabel3StructSetLocalhash(vertex, i);
            } else if (keyId == 3) {
                GtSpeciallabel3StructSetLocal(vertex, i);
            } else if (keyId == 4) {
                GtSpeciallabel3StructSetLpm4(vertex, i);
            } else {
            AW_FUN_Log(LOG_ERROR, "keyId error!! keyId = %d", keyId);
            }
            ret = testStructSetIndexKeyWithBuf(stmt, vertex, keyId, NULL, &labelInfo);
            RETURN_IFERR(ret);
        }
        GtSpeciallabel2GetNode(stmt, &root, &t1V);
        if (optType == GMC_OPERATION_MERGE && expAffectRows == 1) {
            GtSpeciallabel2SetLpmProperty(root, i);
            int64_t f0Value = i;
            ret = GmcNodeSetPropertyByName(root, (char *)"F0", GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            GtSpeciallabel2NodeSet(t1V, i, stringValue, t1VCount, t2VCount);
        }
        TestSpecialT2UpdateSetOldProperty(root, i + coefficient, bytesValue, isDefaultValue);
        if (schemaVersion == 0) {
            TestSpecialT3UpdateSetMidPropertyFailed(root, bytesValue, stringValue);
            TestSpecialT3UpdateSetNewestPropertyFailed(root);
        } else if (schemaVersion == 1) {
            TestSpecialT3UpdateSetMidProperty(root, bytesValue, stringValue);
            TestSpecialT3UpdateSetNewestPropertyFailed(root);
        } else if (schemaVersion == 2) {
            TestSpecialT3UpdateSetNewProperty(root, i + coefficient, bytesValue, stringValue);
        }
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = TestGetAffactRows(stmt, expAffectRows);
        RETURN_IFERR(ret);
    }
    free(vertex);
    return GMERR_OK;
}


// 以结构化的方式 delete表的数据
int GtSpeciallabel3StructDelete(GmcStmtT *stmt, GtSpeciallabelCfgT vertexCfg, uint32_t keyId)
{
    int ret = GMERR_OK;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;
    uint16_t t1VCount = vertexCfg.t1VCount;
    uint16_t t2VCount = vertexCfg.t2VCount;
    uint32_t schemaVersion = vertexCfg.schemaVersion;
    GmcOperationTypeE optType = vertexCfg.optType;
    GmcNodeT *root, *t1V;
    GtSpeciallabel3VertexT *vertex = (GtSpeciallabel3VertexT *)malloc(sizeof(GtSpeciallabel3VertexT));
    if (vertex == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
        return 1;
    }
    (void)memset(vertex, 0, sizeof(GtSpeciallabel3VertexT));
    TestLabelInfoT labelInfo = {g_labelName2, schemaVersion, g_testNameSpace};

    for (int64_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName2, schemaVersion, optType);
        RETURN_IFERR(ret);
        if (keyId == 0) {
            GtSpeciallabel3StructSetPk(vertex, i);
        } else if (keyId == 1) {
            GtSpeciallabel3StructSetHashcluster(vertex, i, coefficient);
        } else if (keyId == 2) {
            GtSpeciallabel3StructSetLocalhash(vertex, i, coefficient);
        } else if (keyId == 3) {
            GtSpeciallabel3StructSetLocal(vertex, i);
        } else if (keyId == 4) {
            GtSpeciallabel3StructSetLpm4(vertex, i);
        } else {
        AW_FUN_Log(LOG_ERROR, "keyId error!! keyId = %d", keyId);
        }
        ret = testStructSetIndexKeyWithBuf(stmt, vertex, keyId, NULL, &labelInfo);
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = TestGetAffactRows(stmt, expAffectRows);
        RETURN_IFERR(ret);
        if (keyId == 3) {
            break;
        }
    }
    free(vertex);
    return GMERR_OK;
}

#endif
