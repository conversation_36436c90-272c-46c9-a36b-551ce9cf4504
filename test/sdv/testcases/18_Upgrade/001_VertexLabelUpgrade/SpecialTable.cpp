/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2012-2018. All rights reserved.
 Description  :表结构升级特殊复杂表测试
 Author       : wuxiaochun wx753022
 Modification :
 Date         : 2022/07/22
**************************************************************************** */
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <time.h>

#include "gtest/gtest.h"

#include "VertexLabelUpgradeStruct.h"


class SpecialTable : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void SpecialTable::SetUpTestCase()
{
    int ret = 0;
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void SpecialTable::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}

void SpecialTable::SetUp()
{
    // 建连
    int ret = 0;
    char *schemaParth = (char *)"./schemaFile/SpecialTableSchema.gmjson";
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创表
    ret = TestCreateLabel(g_stmt, schemaParth, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
    char errorMsg1[128] = {0}, errorMsg2[128] = {0};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INVALID_PROPERTY);
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
}

void SpecialTable::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = 0;
    ret = GmcDropVertexLabel(g_stmt, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
}

/* ****************************************************************************
 Description  : 001.根节点末尾添加符合的所有字段类型数据进行表升级后结构化insert新数据
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(SpecialTable, Upgrade_001_003_003_001)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int fetchNum = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    uint32_t schemaVersion = 1;
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_INSERT};
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 读
    vertexCfg.optType = GMC_OPERATION_SCAN;
    vertexCfg.expAffectRows = endValue;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 3, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 002.根节点末尾添加符合的所有字段类型数据进行表升级后结构化replace新数据
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(SpecialTable, Upgrade_001_003_003_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int fetchNum = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    uint32_t schemaVersion = 1;
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_INSERT};
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg.optType = GMC_OPERATION_REPLACE;
    vertexCfg.expAffectRows = 2;
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 读
    vertexCfg.optType = GMC_OPERATION_SCAN;
    vertexCfg.expAffectRows = endValue;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 1, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 003.根节点末尾添加符合的所有字段类型数据进行表升级后结构化merge新数据
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(SpecialTable, Upgrade_001_003_003_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int fetchNum = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    int32_t updateValue = 100;
    uint32_t schemaVersion = 1;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEFG";
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_INSERT};
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg.optType = GMC_OPERATION_MERGE;
    vertexCfg.expAffectRows = 2;
    vertexCfg.coefficient = updateValue;
    ret = GtSpeciallabel2StructUpdate(g_stmt, vertexCfg, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg.optType = GMC_OPERATION_SCAN;
    vertexCfg.expAffectRows = endValue;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 0, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 2, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 004.根节点末尾添加符合的所有字段类型数据进行表升级后结构化update新数据
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(SpecialTable, Upgrade_001_003_003_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int fetchNum = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    int32_t updateValue = 100;
    uint32_t schemaVersion = 1;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEFG";
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_INSERT};
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg.optType = GMC_OPERATION_UPDATE;
    vertexCfg.expAffectRows = 1;
    vertexCfg.coefficient = updateValue;
    ret = GtSpeciallabel2StructUpdate(g_stmt, vertexCfg, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg.optType = GMC_OPERATION_SCAN;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 0, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 4, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 005.根节点末尾添加符合的所有字段类型数据进行表升级后结构化批量insert/delete新数据
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(SpecialTable, Upgrade_001_003_003_005)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int fetchNum = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    int32_t updateValue = 100;
    uint32_t schemaVersion = 1;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEFG";
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_INSERT};
    ret = GtSpeciallabel2StructBatchWrite(g_conn, g_stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 读
    vertexCfg.optType = GMC_OPERATION_SCAN;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 4, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 批量delete
    vertexCfg.optType = GMC_OPERATION_DELETE;
    ret = GtSpeciallabel2StructBatchDelete(g_conn, g_stmt, vertexCfg, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg.expAffectRows = 0;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 3, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 006.根节点末尾添加符合的所有字段类型数据进行表升级后结构化批量replace/delete新数据
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(SpecialTable, Upgrade_001_003_003_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int fetchNum = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    int32_t updateValue = 100;
    uint32_t schemaVersion = 1;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEFG";
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_REPLACE};
    ret = GtSpeciallabel2StructBatchWrite(g_conn, g_stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 读
    vertexCfg.optType = GMC_OPERATION_SCAN;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 4, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 批量delete
    vertexCfg.optType = GMC_OPERATION_DELETE;
    ret = GtSpeciallabel2StructBatchDelete(g_conn, g_stmt, vertexCfg, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg.expAffectRows = 0;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 3, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 007.根节点末尾添加符合的所有字段类型数据进行表升级后结构化批量merge新数据
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(SpecialTable, Upgrade_001_003_003_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int fetchNum = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    int32_t updateValue = 100;
    uint32_t schemaVersion = 1;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEFG";
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_MERGE};
    ret = GtSpeciallabel2StructBatchWrite(g_conn, g_stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 读
    vertexCfg.optType = GMC_OPERATION_SCAN;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* *********************************************************************************
 Description  : 008.根节点末尾添加符合的所有字段类型数据进行表升级后结构化批量update新数据
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
*********************************************************************************** */
TEST_F(SpecialTable, Upgrade_001_003_003_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int fetchNum = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    int32_t updateValue = 100;
    uint32_t schemaVersion = 1;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEFG";
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_INSERT};
    ret = GtSpeciallabel2StructBatchWrite(g_conn, g_stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg.optType = GMC_OPERATION_UPDATE;
    vertexCfg.coefficient = updateValue;
    ret = GtSpeciallabel2StructBatchUpdate(g_conn, g_stmt, vertexCfg, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 读
    vertexCfg.optType = GMC_OPERATION_SCAN;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 009.小对象升级成大对象表后的结构化insert新数据
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(SpecialTable, Upgrade_001_003_003_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int fetchNum = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
#ifdef ENV_RTOSV2X
    endValue = 20;
#endif
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEFG";
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaBigObj.gmjson";
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 2, GMC_OPERATION_INSERT};
    ret = GtSpeciallabel3StructWrite(g_stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg.optType = GMC_OPERATION_SCAN;
    vertexCfg.expAffectRows = endValue;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 010.小对象升级成大对象的表后的结构化replace新数据
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(SpecialTable, Upgrade_001_003_003_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int fetchNum = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
#ifdef ENV_RTOSV2X
    endValue = 20;
#endif
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    uint32_t schemaVersion = 2;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEFG";
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaBigObj.gmjson";
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 2, GMC_OPERATION_REPLACE};
    ret = GtSpeciallabel3StructWrite(g_stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg.optType = GMC_OPERATION_SCAN;
    vertexCfg.expAffectRows = endValue;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 1, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 011.小对象升级成大对象的表后的结构化merge新数据
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(SpecialTable, Upgrade_001_003_003_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int fetchNum = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
#ifdef ENV_RTOSV2X
    endValue = 20;
#endif
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    uint32_t schemaVersion = 2;
    int32_t updateValue = 100;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEFG";
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaBigObj.gmjson";
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 2, GMC_OPERATION_INSERT};
    ret = GtSpeciallabel3StructWrite(g_stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg.optType = GMC_OPERATION_MERGE;
    vertexCfg.expAffectRows = 2;
    vertexCfg.coefficient = updateValue;
    ret = GtSpeciallabel3StructUpdate(g_stmt, vertexCfg, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg.optType = GMC_OPERATION_SCAN;
    vertexCfg.expAffectRows = endValue;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 0, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 3, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 012.小对象升级成大对象的表后的结构化update新数据
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(SpecialTable, Upgrade_001_003_003_012)
{
   int ret = 0;
    int fetchNum = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
#ifdef ENV_RTOSV2X
    endValue = 20;
#endif
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    int32_t updateValue = 100;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEFG";
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaBigObj.gmjson";
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 2, GMC_OPERATION_INSERT};
    ret = GtSpeciallabel3StructWrite(g_stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg.optType = GMC_OPERATION_UPDATE;
    vertexCfg.expAffectRows = 1;
    vertexCfg.coefficient = updateValue;
    ret = GtSpeciallabel3StructUpdate(g_stmt, vertexCfg, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg.optType = GMC_OPERATION_SCAN;
    vertexCfg.expAffectRows = endValue;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 0, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 4, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 013.小对象升级成大对象的表后的批量结构化insert/delete新数据
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(SpecialTable, Upgrade_001_003_003_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int fetchNum = 0;
    int32_t startValue = 0;
    uint32_t endValue = 2;
    char *expectValue = (char *)"upgrade successfully";
    uint32_t schemaVersion = 2;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEFG";
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaBigObj.gmjson";
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 2, GMC_OPERATION_INSERT};
    ret = GtSpeciallabel3StructBatchWrite(g_conn, g_stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg.optType = GMC_OPERATION_SCAN;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg.optType = GMC_OPERATION_DELETE;
    vertexCfg.expAffectRows = endValue;
    ret = GtSpeciallabel3StructBatchDelete(g_conn, g_stmt, vertexCfg, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg.optType = GMC_OPERATION_SCAN;
    vertexCfg.expAffectRows = 0;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 3, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 014.小对象升级成大对象的表后的批量结构化replace新数据
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(SpecialTable, Upgrade_001_003_003_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int fetchNum = 0;
    int32_t startValue = 0;
    uint32_t endValue = 2;
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEFG";
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaBigObj.gmjson";
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 2, GMC_OPERATION_REPLACE};
    ret = GtSpeciallabel3StructWrite(g_stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg.optType = GMC_OPERATION_SCAN;
    vertexCfg.expAffectRows = endValue;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 3, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 015.小对象升级大对象的表后的批量结构化merge新数据
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(SpecialTable, Upgrade_001_003_003_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int fetchNum = 0;
    int32_t startValue = 0;
    uint32_t endValue = 2;
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    int32_t updateValue = 100;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEFG";
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaBigObj.gmjson";
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 2, GMC_OPERATION_INSERT};
    ret = GtSpeciallabel3StructBatchWrite(g_conn, g_stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg.optType = GMC_OPERATION_MERGE;
    vertexCfg.coefficient = updateValue;
    vertexCfg.expAffectRows = 2;
    ret = GtSpeciallabel3StructBatchUpdate(g_conn, g_stmt, vertexCfg, 0, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg.optType = GMC_OPERATION_SCAN;
    vertexCfg.expAffectRows = endValue;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 0, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 4, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 016.小对象升级成大对象的表后的批量结构化update新数据
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(SpecialTable, Upgrade_001_003_003_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int fetchNum = 0;
    int32_t startValue = 0;
    uint32_t endValue = 2;
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    int32_t updateValue = 100;
    uint32_t schemaVersion = 2;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEFG";
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaBigObj.gmjson";
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 2, GMC_OPERATION_INSERT};
    ret = GtSpeciallabel3StructBatchWrite(g_conn, g_stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg.optType = GMC_OPERATION_UPDATE;
    vertexCfg.coefficient = updateValue;
    ret = GtSpeciallabel3StructBatchUpdate(g_conn, g_stmt, vertexCfg, 0, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg.optType = GMC_OPERATION_SCAN;
    vertexCfg.expAffectRows = endValue;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 0, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 4, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 017.最老版本读最新版本的数据
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(SpecialTable, Upgrade_001_003_003_017)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int fetchNum = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
#ifdef ENV_RTOSV2X
    endValue = 10;
#endif
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    uint32_t schemaVersion = 1;
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SpecialTableSchemaBigObj.gmjson";
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    GtSpeciallabelCfgT vertexCfg2 = {(int32_t)endValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_REPLACE};
    GtSpeciallabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue, 0, 1, 0, 3, 3, 2, GMC_OPERATION_REPLACE};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel3StructWrite(g_stmt, vertexCfg3, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg3.optType = GMC_OPERATION_SCAN;
    vertexCfg3.schemaVersion = 0;
    vertexCfg3.expAffectRows = endValue * 3;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg3, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg3, 1, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 018.中间版本读最新版本的数据
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(SpecialTable, Upgrade_001_003_003_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
#ifdef ENV_RTOSV2X
    endValue = 10;
#endif
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    uint32_t schemaVersion = 1;
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SpecialTableSchemaBigObj.gmjson";
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    GtSpeciallabelCfgT vertexCfg2 = {(int32_t)endValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_REPLACE};
    GtSpeciallabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue, 0, 1, 0, 3, 3, 2, GMC_OPERATION_REPLACE};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel3StructWrite(g_stmt, vertexCfg3, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg3.optType = GMC_OPERATION_SCAN;
    vertexCfg3.schemaVersion = 1;
    vertexCfg3.expAffectRows = endValue * 3;
    vertexCfg3.fieldIsNull[1] = false;
    vertexCfg3.fieldIsNull[2] = false;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg3, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg3, 3, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 019.最老版本更新最新版本的数据
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(SpecialTable, Upgrade_001_003_003_019)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
#ifdef ENV_RTOSV2X
    endValue = 10;
#endif
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    uint32_t schemaVersion = 1;
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SpecialTableSchemaBigObj.gmjson";
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    GtSpeciallabelCfgT vertexCfg2 = {(int32_t)endValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_REPLACE};
    GtSpeciallabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue, 0, 1, 0, 3, 3, 2, GMC_OPERATION_REPLACE};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel3StructWrite(g_stmt, vertexCfg3, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    vertexCfg3.schemaVersion = 0;
    vertexCfg3.optType = GMC_OPERATION_UPDATE;
    vertexCfg3.coefficient = 300;
    ret = GtSpeciallabel3StructUpdateOrMerge(g_stmt, vertexCfg3, 0, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 读
    vertexCfg3.optType = GMC_OPERATION_SCAN;
    vertexCfg3.expAffectRows = endValue * 3;
    vertexCfg3.fieldIsNull[1] = false;
    vertexCfg3.fieldIsNull[2] = false;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg3, 0, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg3, 3, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg2.schemaVersion = 0;
    vertexCfg2.optType = GMC_OPERATION_SCAN;
    vertexCfg2.expAffectRows = endValue * 3;
    vertexCfg2.fieldIsNull[1] = false;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg2, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg2, 1, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg.optType = GMC_OPERATION_SCAN;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 020.最老版本更新中间版本的数据
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(SpecialTable, Upgrade_001_003_003_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
#ifdef ENV_RTOSV2X
    endValue = 10;
#endif
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    uint32_t schemaVersion = 1;
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SpecialTableSchemaBigObj.gmjson";
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    GtSpeciallabelCfgT vertexCfg2 = {(int32_t)endValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_REPLACE};
    GtSpeciallabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue, 0, 1, 0, 3, 3, 2, GMC_OPERATION_REPLACE};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel3StructWrite(g_stmt, vertexCfg3, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    vertexCfg2.schemaVersion = 0;
    vertexCfg2.expAffectRows = 2;
    vertexCfg2.coefficient = 300;
    vertexCfg2.optType = GMC_OPERATION_MERGE;
    ret = GtSpeciallabel3StructUpdateOrMerge(g_stmt, vertexCfg2, 0, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 读
    vertexCfg2.optType = GMC_OPERATION_SCAN;
    vertexCfg2.expAffectRows = endValue * 3;
    vertexCfg2.fieldIsNull[1] = false;
    vertexCfg2.fieldIsNull[2] = true;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg2, 0, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg2, 1, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg3.schemaVersion = 0;
    vertexCfg3.optType = GMC_OPERATION_SCAN;
    vertexCfg3.expAffectRows = endValue * 3;
    vertexCfg3.fieldIsNull[1] = false;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg3, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg3, 1, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg.optType = GMC_OPERATION_SCAN;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 021.中间版本更新最新版本的数据
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(SpecialTable, Upgrade_001_003_003_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
#ifdef ENV_RTOSV2X
    endValue = 10;
#endif
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    uint32_t schemaVersion = 1;
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SpecialTableSchemaBigObj.gmjson";
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    GtSpeciallabelCfgT vertexCfg2 = {(int32_t)endValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_REPLACE};
    GtSpeciallabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue, 0, 1, 0, 3, 3, 2, GMC_OPERATION_REPLACE};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel3StructWrite(g_stmt, vertexCfg3, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    vertexCfg3.schemaVersion = 1;
    vertexCfg3.expAffectRows = 2;
    vertexCfg3.coefficient = 300;
    vertexCfg3.optType = GMC_OPERATION_MERGE;
    ret = GtSpeciallabel3StructUpdateOrMerge(g_stmt, vertexCfg3, 0, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 读
    vertexCfg3.optType = GMC_OPERATION_SCAN;
    vertexCfg3.expAffectRows = endValue * 3;
    vertexCfg3.fieldIsNull[1] = false;
    vertexCfg3.fieldIsNull[2] = true;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg3, 0, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg3, 1, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg2.schemaVersion = 0;
    vertexCfg2.optType = GMC_OPERATION_SCAN;
    vertexCfg2.expAffectRows = endValue * 3;
    vertexCfg2.fieldIsNull[1] = false;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg2, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg2, 1, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg.optType = GMC_OPERATION_SCAN;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 022.中间版本更新最老版本的数据
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(SpecialTable, Upgrade_001_003_003_022)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
#ifdef ENV_RTOSV2X
    endValue = 10;
#endif
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    uint32_t schemaVersion = 1;
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SpecialTableSchemaBigObj.gmjson";
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    GtSpeciallabelCfgT vertexCfg2 = {(int32_t)endValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_REPLACE};
    GtSpeciallabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue, 0, 1, 0, 3, 3, 2, GMC_OPERATION_REPLACE};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel3StructWrite(g_stmt, vertexCfg3, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    vertexCfg.schemaVersion = 1;
    vertexCfg.expAffectRows = 2;
    vertexCfg.coefficient = 300;
    vertexCfg.optType = GMC_OPERATION_MERGE;
    ret = GtSpeciallabel3StructUpdateOrMerge(g_stmt, vertexCfg, 0, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 读
    vertexCfg.schemaVersion = 2;
    vertexCfg.optType = GMC_OPERATION_SCAN;
    vertexCfg.expAffectRows = endValue * 3;
    vertexCfg.fieldIsNull[1] = false;
    vertexCfg.fieldIsNull[2] = true;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 0, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 1, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg2.schemaVersion = 0;
    vertexCfg2.optType = GMC_OPERATION_SCAN;
    vertexCfg2.expAffectRows = endValue * 3;
    vertexCfg2.fieldIsNull[1] = false;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg2, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg2, 3, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg3.schemaVersion = 0;
    vertexCfg3.optType = GMC_OPERATION_SCAN;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg3, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg3, 2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 023.最新版本读最老版本的数据
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(SpecialTable, Upgrade_001_003_003_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
#ifdef ENV_RTOSV2X
    endValue = 10;
#endif
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    uint32_t schemaVersion = 1;
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SpecialTableSchemaBigObj.gmjson";
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_REPLACE};
    GtSpeciallabelCfgT vertexCfg2 = {(int32_t)endValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_REPLACE};
    GtSpeciallabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue, 0, 1, 0, 3, 3, 2, GMC_OPERATION_REPLACE};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel3StructWrite(g_stmt, vertexCfg3, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg.optType = GMC_OPERATION_SCAN;
    vertexCfg.schemaVersion = 2;
    vertexCfg.expAffectRows = endValue * 3;
    vertexCfg.fieldIsNull[1] = true;
    vertexCfg.fieldIsNull[2] = true;

    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 4, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 024.最新版本读中间版本的数据
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(SpecialTable, Upgrade_001_003_003_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
#ifdef ENV_RTOSV2X
    endValue = 10;
#endif
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    uint32_t schemaVersion = 1;
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SpecialTableSchemaBigObj.gmjson";
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    GtSpeciallabelCfgT vertexCfg2 = {(int32_t)endValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_REPLACE};
    GtSpeciallabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue, 0, 1, 0, 3, 3, 2, GMC_OPERATION_REPLACE};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel3StructWrite(g_stmt, vertexCfg3, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg2.optType = GMC_OPERATION_SCAN;
    vertexCfg2.schemaVersion = 2;
    vertexCfg2.expAffectRows = endValue * 3;
    vertexCfg2.fieldIsNull[0] = false;
    vertexCfg2.fieldIsNull[1] = false;
    vertexCfg2.fieldIsNull[2] = true;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg2, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg2, 2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 025.最新版本更新中间版本的数据
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(SpecialTable, Upgrade_001_003_003_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
#ifdef ENV_RTOSV2X
    endValue = 10;
#endif
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    uint32_t schemaVersion = 1;
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SpecialTableSchemaBigObj.gmjson";
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    GtSpeciallabelCfgT vertexCfg2 = {(int32_t)endValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_REPLACE};
    GtSpeciallabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue, 0, 1, 0, 3, 3, 2, GMC_OPERATION_REPLACE};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel3StructWrite(g_stmt, vertexCfg3, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    vertexCfg2.schemaVersion = 2;
    vertexCfg2.expAffectRows = 1;
    vertexCfg2.coefficient = 300;
    vertexCfg2.optType = GMC_OPERATION_UPDATE;
    ret = GtSpeciallabel3StructUpdateOrMerge(g_stmt, vertexCfg2, 0, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 读
    vertexCfg2.schemaVersion = 2;
    vertexCfg2.optType = GMC_OPERATION_SCAN;
    vertexCfg2.expAffectRows = endValue * 3;
    vertexCfg2.fieldIsNull[1] = false;
    vertexCfg2.fieldIsNull[2] = false;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg2, 0, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg2, 1, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg.schemaVersion = 0;
    vertexCfg.optType = GMC_OPERATION_SCAN;
    vertexCfg.expAffectRows = endValue * 3;
    vertexCfg.fieldIsNull[1] = true;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 3, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg3.schemaVersion = 0;
    vertexCfg3.optType = GMC_OPERATION_SCAN;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg3, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg3, 2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 026.最新版本更新最老版本的数据
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(SpecialTable, Upgrade_001_003_003_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
#ifdef ENV_RTOSV2X
    endValue = 10;
#endif
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    uint32_t schemaVersion = 1;
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SpecialTableSchemaBigObj.gmjson";
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    GtSpeciallabelCfgT vertexCfg2 = {(int32_t)endValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_REPLACE};
    GtSpeciallabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue, 0, 1, 0, 3, 3, 2, GMC_OPERATION_REPLACE};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel3StructWrite(g_stmt, vertexCfg3, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    vertexCfg.schemaVersion = 2;
    vertexCfg.expAffectRows = 1;
    vertexCfg.coefficient = 300;
    vertexCfg.optType = GMC_OPERATION_UPDATE;
    ret = GtSpeciallabel3StructUpdateOrMerge(g_stmt, vertexCfg, 0, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 读
    vertexCfg.schemaVersion = 2;
    vertexCfg.optType = GMC_OPERATION_SCAN;
    vertexCfg.expAffectRows = endValue * 3;
    vertexCfg.fieldIsNull[1] = false;
    vertexCfg.fieldIsNull[2] = false;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 0, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 1, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg.schemaVersion = 0;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 1, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg2.schemaVersion = 0;
    vertexCfg2.optType = GMC_OPERATION_SCAN;
    vertexCfg2.expAffectRows = endValue * 3;
    vertexCfg2.fieldIsNull[1] = true;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg2, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg2, 3, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg3.schemaVersion = 0;
    vertexCfg3.optType = GMC_OPERATION_SCAN;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg3, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg3, 2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 027.新版本insert数据的订阅推送
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(SpecialTable, Upgrade_001_003_003_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
#ifdef ENV_RTOSV2X
    endValue = 10;
#endif
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SpecialTableSchemaBigObj.gmjson";
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 订阅
    char *subInfo = NULL;
    int chanRingLen = 64;
    char subConnName[128] = "subTabel";
    GmcStmtT *stmtSub = NULL;
    GmcConnT *testSubConn = NULL;
    char subName[128] = "subSpecialLabel";
    SnUserDataT *userData = NULL;
    ret = testSubConnect(&testSubConn, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    readJanssonFile("schemaFile/subSchema/specialTSub.gmjson", &subInfo);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, subInfo);
    userData = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData, 0, sizeof(SnUserDataT));
    GmcSubConfigT tmpSubInfo;
    tmpSubInfo.subsName = subName;
    tmpSubInfo.configJson = subInfo;
    ret = GmcSubscribe(g_stmt, &tmpSubInfo, testSubConn, SubSpecialTCallBackWithNewVersion, userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(subInfo);
    // 写数据
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT, {false, true, true}};
    GtSpeciallabelCfgT vertexCfg2 = {(int32_t)endValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_INSERT,
                                     {false, false, true}};
    GtSpeciallabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue, 0, 1, 0, 3, 3, 2, GMC_OPERATION_INSERT,
                                      {false, false, false}};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel3StructWrite(g_stmt, vertexCfg3, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INSERT, (int32_t)endValue * 3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 取消订阅
    ret = GmcUnSubscribe(g_stmt, subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 释放订阅连接
    ret = testSubDisConnect(testSubConn, stmtSub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(userData);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 028.新版本replace数据的订阅推送
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(SpecialTable, Upgrade_001_003_003_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
#ifdef ENV_RTOSV2X
    endValue = 10;
#endif
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SpecialTableSchemaBigObj.gmjson";
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 订阅
    char *subInfo = NULL;
    int chanRingLen = 64;
    char subConnName[128] = "subTabel";
    GmcStmtT *stmtSub = NULL;
    GmcConnT *testSubConn = NULL;
    char subName[128] = "subSpecialLabel";
    SnUserDataT *userData = NULL;
    ret = testSubConnect(&testSubConn, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    readJanssonFile("schemaFile/subSchema/specialTSub.gmjson", &subInfo);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, subInfo);
    userData = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData, 0, sizeof(SnUserDataT));
    GmcSubConfigT tmpSubInfo;
    tmpSubInfo.subsName = subName;
    tmpSubInfo.configJson = subInfo;
    ret = GmcSubscribe(g_stmt, &tmpSubInfo, testSubConn, SubSpecialTCallBackWithNewVersion, userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(subInfo);
    // 写数据
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_REPLACE, {false, true, true}};
    GtSpeciallabelCfgT vertexCfg2 = {(int32_t)endValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_REPLACE,
                                     {false, false, true}};
    GtSpeciallabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue, 0, 1, 0, 3, 3, 2, GMC_OPERATION_REPLACE,
                                      {false, false, false}};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel3StructWrite(g_stmt, vertexCfg3, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_REPLACE, (int32_t)endValue * 3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 取消订阅
    ret = GmcUnSubscribe(g_stmt, subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 释放订阅连接
    ret = testSubDisConnect(testSubConn, stmtSub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(userData);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 029.新版本update数据的订阅推送
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(SpecialTable, Upgrade_001_003_003_029)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
#ifdef ENV_RTOSV2X
    endValue = 10;
#endif
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SpecialTableSchemaBigObj.gmjson";
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 写数据
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_MERGE, {false, true, true}};
    GtSpeciallabelCfgT vertexCfg2 = {(int32_t)endValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_MERGE,
                                     {false, false, true}};
    GtSpeciallabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue, 0, 1, 0, 3, 3, 2, GMC_OPERATION_MERGE,
                                      {false, false, false}};
    ret = GtSpeciallabel3StructUpdateOrMerge(g_stmt, vertexCfg, 0, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel3StructUpdateOrMerge(g_stmt, vertexCfg2, 0, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel3StructUpdateOrMerge(g_stmt, vertexCfg3, 0, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 订阅
    char *subInfo = NULL;
    int chanRingLen = 64;
    char subConnName[128] = "subTabel";
    GmcStmtT *stmtSub = NULL;
    GmcConnT *testSubConn = NULL;
    char subName[128] = "subSpecialLabel";
    SnUserDataT *userData = NULL;
    ret = testSubConnect(&testSubConn, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    readJanssonFile("schemaFile/subSchema/specialTSub.gmjson", &subInfo);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, subInfo);
    userData = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData, 0, sizeof(SnUserDataT));
    GmcSubConfigT tmpSubInfo;
    tmpSubInfo.subsName = subName;
    tmpSubInfo.configJson = subInfo;
    ret = GmcSubscribe(g_stmt, &tmpSubInfo, testSubConn, SubSpecialTCallBackWithNewVersion, userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(subInfo);
    // update
    vertexCfg.optType = GMC_OPERATION_UPDATE;
    vertexCfg2.optType = GMC_OPERATION_UPDATE;
    vertexCfg3.optType = GMC_OPERATION_UPDATE;
    vertexCfg.schemaVersion = 2;
    vertexCfg2.schemaVersion = 2;
    vertexCfg3.schemaVersion = 2;
    vertexCfg.coefficient = 300;
    vertexCfg2.coefficient = 300;
    vertexCfg3.coefficient = 300;
    ret = GtSpeciallabel3StructUpdateOrMerge(g_stmt, vertexCfg, 0, bytesValue, stringValue, true);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel3StructUpdateOrMerge(g_stmt, vertexCfg2, 1, bytesValue, stringValue, true);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel3StructUpdateOrMerge(g_stmt, vertexCfg3, 2, bytesValue, stringValue, true);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_UPDATE, (int32_t)endValue * 3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 取消订阅
    ret = GmcUnSubscribe(g_stmt, subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 释放订阅连接
    ret = testSubDisConnect(testSubConn, stmtSub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(userData);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 030.新版本merge数据的订阅推送
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(SpecialTable, Upgrade_001_003_003_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
#ifdef ENV_RTOSV2X
    endValue = 10;
#endif
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SpecialTableSchemaBigObj.gmjson";
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 订阅
    char *subInfo = NULL;
    int chanRingLen = 64;
    char subConnName[128] = "subTabel";
    GmcStmtT *stmtSub = NULL;
    GmcConnT *testSubConn = NULL;
    char subName[128] = "subSpecialLabel";
    SnUserDataT *userData = NULL;
    ret = testSubConnect(&testSubConn, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    readJanssonFile("schemaFile/subSchema/specialTSub.gmjson", &subInfo);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, subInfo);
    userData = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData, 0, sizeof(SnUserDataT));
    GmcSubConfigT tmpSubInfo;
    tmpSubInfo.subsName = subName;
    tmpSubInfo.configJson = subInfo;
    ret = GmcSubscribe(g_stmt, &tmpSubInfo, testSubConn, SubSpecialTCallBackWithNewVersion, userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(subInfo);
    // 写数据
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_MERGE, {false, true, true}};
    GtSpeciallabelCfgT vertexCfg2 = {(int32_t)endValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_MERGE,
                                     {false, false, true}};
    GtSpeciallabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue, 0, 1, 0, 3, 3, 2, GMC_OPERATION_MERGE,
                                      {false, false, false}};
    ret = GtSpeciallabel3StructUpdateOrMerge(g_stmt, vertexCfg, 0, bytesValue, stringValue, true);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel3StructUpdateOrMerge(g_stmt, vertexCfg2, 0, bytesValue, stringValue, true);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel3StructUpdateOrMerge(g_stmt, vertexCfg3, 0, bytesValue, stringValue, true);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_MERGE_INSERT, (int32_t)endValue * 3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 取消订阅
    ret = GmcUnSubscribe(g_stmt, subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 释放订阅连接
    ret = testSubDisConnect(testSubConn, stmtSub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(userData);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 031.新版本delete新老数据的订阅推送
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(SpecialTable, Upgrade_001_003_003_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
#ifdef ENV_RTOSV2X
    endValue = 10;
#endif
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SpecialTableSchemaBigObj.gmjson";
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 写数据
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_MERGE, {false, true, true}};
    GtSpeciallabelCfgT vertexCfg2 = {(int32_t)endValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_MERGE,
                                     {false, false, true}};
    GtSpeciallabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue, 0, 1, 0, 3, 3, 2, GMC_OPERATION_MERGE,
                                      {false, false, false}};
    ret = GtSpeciallabel3StructUpdateOrMerge(g_stmt, vertexCfg, 0, bytesValue, stringValue, true);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel3StructUpdateOrMerge(g_stmt, vertexCfg2, 0, bytesValue, stringValue, true);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel3StructUpdateOrMerge(g_stmt, vertexCfg3, 0, bytesValue, stringValue, true);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 订阅
    char *subInfo = NULL;
    int chanRingLen = 64;
    char subConnName[128] = "subTabel";
    GmcStmtT *stmtSub = NULL;
    GmcConnT *testSubConn = NULL;
    char subName[128] = "subSpecialLabel";
    SnUserDataT *userData = NULL;
    ret = testSubConnect(&testSubConn, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    readJanssonFile("schemaFile/subSchema/specialTSub.gmjson", &subInfo);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, subInfo);
    userData = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData, 0, sizeof(SnUserDataT));
    GmcSubConfigT tmpSubInfo;
    tmpSubInfo.subsName = subName;
    tmpSubInfo.configJson = subInfo;
    ret = GmcSubscribe(g_stmt, &tmpSubInfo, testSubConn, SubSpecialTCallBackWithNewVersion, userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(subInfo);
    // delete数据
    vertexCfg.schemaVersion = 2;
    vertexCfg.optType = GMC_OPERATION_DELETE;
    ret = GtSpeciallabel3StructDelete(g_stmt, vertexCfg, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg2.schemaVersion = 0;
    vertexCfg2.optType = GMC_OPERATION_DELETE;
    ret = GtSpeciallabel3StructDelete(g_stmt, vertexCfg2, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg3.schemaVersion = 1;
    vertexCfg3.optType = GMC_OPERATION_DELETE;
    ret = GtSpeciallabel3StructDelete(g_stmt, vertexCfg3, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_DELETE, (int32_t)endValue * 3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 取消订阅
    ret = GmcUnSubscribe(g_stmt, subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 释放订阅连接
    ret = testSubDisConnect(testSubConn, stmtSub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(userData);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 032.旧版本insert数据的订阅推送
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(SpecialTable, Upgrade_001_003_003_032)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
#ifdef ENV_RTOSV2X
    endValue = 10;
#endif
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SpecialTableSchemaBigObj.gmjson";

    // 订阅
    char *subInfo = NULL;
    int chanRingLen = 64;
    char subConnName[128] = "subTabel";
    GmcStmtT *stmtSub = NULL;
    GmcConnT *testSubConn = NULL;
    char subName[128] = "subSpecialLabel";
    SnUserDataT *userData = NULL;
    ret = testSubConnect(&testSubConn, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    readJanssonFile("schemaFile/subSchema/specialTSub2.gmjson", &subInfo);
    AW_MACRO_EXPECT_NE_INT((void *)NULL, subInfo);
    userData = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData, 0, sizeof(SnUserDataT));
    GmcSubConfigT tmpSubInfo;
    tmpSubInfo.subsName = subName;
    tmpSubInfo.configJson = subInfo;
    ret = GmcSubscribe(g_stmt, &tmpSubInfo, testSubConn, SubSpecialTCallBackWithOldVersion, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(subInfo);

    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT, {false, true, true}};
    GtSpeciallabelCfgT vertexCfg2 = {(int32_t)endValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT,
                                     {false, false, true}};
    GtSpeciallabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT,
                                      {false, false, false}};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfg, bytesValue, stringValue, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfg2, bytesValue, stringValue, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfg3, bytesValue, stringValue, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INSERT, (int32_t)endValue * 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 取消订阅
    ret = GmcUnSubscribe(g_stmt, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放订阅连接
    ret = testSubDisConnect(testSubConn, stmtSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(userData);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 033.旧版本replace数据的订阅推送
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(SpecialTable, Upgrade_001_003_003_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
#ifdef ENV_RTOSV2X
    endValue = 10;
#endif
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SpecialTableSchemaBigObj.gmjson";
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 订阅
    char *subInfo = NULL;
    int chanRingLen = 64;
    char subConnName[128] = "subTabel";
    GmcStmtT *stmtSub = NULL;
    GmcConnT *testSubConn = NULL;
    char subName[128] = "subSpecialLabel";
    SnUserDataT *userData = NULL;
    ret = testSubConnect(&testSubConn, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    readJanssonFile("schemaFile/subSchema/specialTSub3.gmjson", &subInfo);
    AW_MACRO_EXPECT_NE_INT((void *)NULL, subInfo);
    userData = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData, 0, sizeof(SnUserDataT));
    GmcSubConfigT tmpSubInfo;
    tmpSubInfo.subsName = subName;
    tmpSubInfo.configJson = subInfo;
    ret = GmcSubscribe(g_stmt, &tmpSubInfo, testSubConn, SubSpecialTCallBackWithOldVersion, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(subInfo);

    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_REPLACE};
    GtSpeciallabelCfgT vertexCfg2 = {(int32_t)endValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_REPLACE,
                                     {false, false, true}};
    GtSpeciallabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_REPLACE,
                                      {false, false, false}};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfg, bytesValue, stringValue, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg2, bytesValue, stringValue, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg3, bytesValue, stringValue, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_REPLACE, (int32_t)endValue * 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 取消订阅
    ret = GmcUnSubscribe(g_stmt, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放订阅连接
    ret = testSubDisConnect(testSubConn, stmtSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(userData);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 034.旧版本update数据的订阅推送
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(SpecialTable, Upgrade_001_003_003_034)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
#ifdef ENV_RTOSV2X
    endValue = 10;
#endif
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    int32_t updateValue = 300;
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SpecialTableSchemaBigObj.gmjson";
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 订阅
    char *subInfo = NULL;
    int chanRingLen = 64;
    char subConnName[128] = "subTabel";
    GmcStmtT *stmtSub = NULL;
    GmcConnT *testSubConn = NULL;
    char subName[128] = "subSpecialLabel";
    SnUserDataT *userData = NULL;
    ret = testSubConnect(&testSubConn, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    readJanssonFile("schemaFile/subSchema/specialTSub3.gmjson", &subInfo);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, subInfo);
    userData = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData, 0, sizeof(SnUserDataT));
    GmcSubConfigT tmpSubInfo;
    tmpSubInfo.subsName = subName;
    tmpSubInfo.configJson = subInfo;
    ret = GmcSubscribe(g_stmt, &tmpSubInfo, testSubConn, SubSpecialTCallBackWithOldVersion, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(subInfo);

    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_MERGE, {false, true, true}};
    GtSpeciallabelCfgT vertexCfg2 = {(int32_t)endValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_MERGE,
                                     {false, false, true}};
    GtSpeciallabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_MERGE,
                                      {false, false, false}};
    ret = GtSpeciallabel3StructUpdateOrMerge(g_stmt, vertexCfg, 0, bytesValue, stringValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel3StructUpdateOrMerge(g_stmt, vertexCfg2, 0, bytesValue, stringValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel3StructUpdateOrMerge(g_stmt, vertexCfg3, 0, bytesValue, stringValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_MERGE_INSERT, (int32_t)endValue * 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // merge update数据
    vertexCfg.schemaVersion = 1;
    vertexCfg.optType = GMC_OPERATION_UPDATE;
    vertexCfg.coefficient = updateValue;
    ret = GtSpeciallabel3StructUpdateOrMerge(g_stmt, vertexCfg, 0, bytesValue, stringValue, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    vertexCfg2.schemaVersion = 1;
    vertexCfg2.optType = GMC_OPERATION_UPDATE;
    vertexCfg2.coefficient = updateValue;
    ret = GtSpeciallabel3StructUpdateOrMerge(g_stmt, vertexCfg2, 0, bytesValue, stringValue, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    vertexCfg3.schemaVersion = 1;
    vertexCfg3.optType = GMC_OPERATION_UPDATE;
    vertexCfg3.coefficient = updateValue;
    ret = GtSpeciallabel3StructUpdateOrMerge(g_stmt, vertexCfg3, 0, bytesValue, stringValue, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_UPDATE, (int32_t)endValue * 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 取消订阅
    ret = GmcUnSubscribe(g_stmt, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放订阅连接
    ret = testSubDisConnect(testSubConn, stmtSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(userData);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 035.旧版本merge数据的订阅推送
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(SpecialTable, Upgrade_001_003_003_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
#ifdef ENV_RTOSV2X
    endValue = 10;
#endif
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    int32_t updateValue = 300;
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SpecialTableSchemaBigObj.gmjson";
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 订阅
    char *subInfo = NULL;
    int chanRingLen = 64;
    char subConnName[128] = "subTabel";
    GmcStmtT *stmtSub = NULL;
    GmcConnT *testSubConn = NULL;
    char subName[128] = "subSpecialLabel";
    SnUserDataT *userData = NULL;
    ret = testSubConnect(&testSubConn, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    readJanssonFile("schemaFile/subSchema/specialTSub3.gmjson", &subInfo);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, subInfo);
    userData = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData, 0, sizeof(SnUserDataT));
    GmcSubConfigT tmpSubInfo;
    tmpSubInfo.subsName = subName;
    tmpSubInfo.configJson = subInfo;
    ret = GmcSubscribe(g_stmt, &tmpSubInfo, testSubConn, SubSpecialTCallBackWithOldVersion, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(subInfo);

    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_MERGE, {false, true, true}};
    GtSpeciallabelCfgT vertexCfg2 = {(int32_t)endValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_MERGE,
                                     {false, false, true}};
    GtSpeciallabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_MERGE,
                                      {false, false, false}};
    ret = GtSpeciallabel3StructUpdateOrMerge(g_stmt, vertexCfg, 0, bytesValue, stringValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel3StructUpdateOrMerge(g_stmt, vertexCfg2, 0, bytesValue, stringValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel3StructUpdateOrMerge(g_stmt, vertexCfg3, 0, bytesValue, stringValue, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_MERGE_INSERT, (int32_t)endValue * 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // merge update数据
    vertexCfg.schemaVersion = 1;
    vertexCfg.expAffectRows = 2;
    vertexCfg.coefficient = updateValue;
    ret = GtSpeciallabel3StructUpdateOrMerge(g_stmt, vertexCfg, 0, bytesValue, stringValue, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    vertexCfg2.schemaVersion = 1;
    vertexCfg2.expAffectRows = 2;
    vertexCfg2.coefficient = updateValue;
    ret = GtSpeciallabel3StructUpdateOrMerge(g_stmt, vertexCfg2, 0, bytesValue, stringValue, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    vertexCfg3.schemaVersion = 1;
    vertexCfg3.expAffectRows = 2;
    vertexCfg3.coefficient = updateValue;
    ret = GtSpeciallabel3StructUpdateOrMerge(g_stmt, vertexCfg3, 0, bytesValue, stringValue, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_MERGE_UPDATE, (int32_t)endValue * 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 取消订阅
    ret = GmcUnSubscribe(g_stmt, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放订阅连接
    ret = testSubDisConnect(testSubConn, stmtSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(userData);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 036.旧版本delete新老数据的订阅推送
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(SpecialTable, Upgrade_001_003_003_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
#ifdef ENV_RTOSV2X
    endValue = 10;
#endif
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SpecialTableSchemaBigObj.gmjson";

    // 订阅
    char *subInfo = NULL;
    int chanRingLen = 64;
    char subConnName[128] = "subTabel";
    GmcStmtT *stmtSub = NULL;
    GmcConnT *testSubConn = NULL;
    char subName[128] = "subSpecialLabel";
    SnUserDataT *userData = NULL;
    ret = testSubConnect(&testSubConn, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    readJanssonFile("schemaFile/subSchema/specialTSub2.gmjson", &subInfo);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, subInfo);
    userData = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData, 0, sizeof(SnUserDataT));
    GmcSubConfigT tmpSubInfo;
    tmpSubInfo.subsName = subName;
    tmpSubInfo.configJson = subInfo;
    ret = GmcSubscribe(g_stmt, &tmpSubInfo, testSubConn, SubSpecialTCallBackWithOldVersion, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(subInfo);

    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    GtSpeciallabelCfgT vertexCfg2 = {(int32_t)endValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    GtSpeciallabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfg, bytesValue, stringValue, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg2, bytesValue, stringValue, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel3StructWrite(g_stmt, vertexCfg3, bytesValue, stringValue, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INSERT, (int32_t)endValue * 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // delete
    vertexCfg.schemaVersion = 0;
    vertexCfg.optType = GMC_OPERATION_DELETE;
    vertexCfg.expAffectRows = endValue * 3;
    ret = GtSpeciallabel3StructDelete(g_stmt, vertexCfg, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 取消订阅
    ret = GmcUnSubscribe(g_stmt, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放订阅连接
    ret = testSubDisConnect(testSubConn, stmtSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(userData);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 037.开启对账,更新部分新老版本数据,结束对账,新老版本分别读数据
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(SpecialTable, Upgrade_001_003_003_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
#ifdef ENV_RTOSV2X
    endValue = 10;
#endif
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    int32_t updateValue = 300;
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SpecialTableSchemaBigObj.gmjson";

    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    GtSpeciallabelCfgT vertexCfg2 = {(int32_t)endValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_INSERT};
    GtSpeciallabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue, 0, 1, 0, 3, 3, 2, GMC_OPERATION_INSERT};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfg, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg2, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel3StructWrite(g_stmt, vertexCfg3, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 开启对账
    bool isAbnormal = false;
    ret = GmcBeginCheck(g_stmt, g_labelName2, 0xff);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg.schemaVersion = 2;
    vertexCfg.expAffectRows = 1;
    vertexCfg.coefficient = updateValue;
    vertexCfg.optType = GMC_OPERATION_UPDATE;
    ret = GtSpeciallabel3StructUpdateOrMerge(g_stmt, vertexCfg, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg2.schemaVersion = 2;
    vertexCfg2.expAffectRows = 1;
    vertexCfg2.coefficient = updateValue;
    vertexCfg2.optType = GMC_OPERATION_UPDATE;
    ret = GtSpeciallabel3StructUpdateOrMerge(g_stmt, vertexCfg2, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg3.schemaVersion = 2;
    vertexCfg3.expAffectRows = 1;
    vertexCfg3.coefficient = updateValue;
    vertexCfg3.optType = GMC_OPERATION_UPDATE;
    ret = GtSpeciallabel3StructUpdateOrMerge(g_stmt, vertexCfg3, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret=GmcEndCheck(g_stmt, g_labelName2, 0xff, isAbnormal);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CheckAccountStatus(g_stmt, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg3.schemaVersion = 2;
    vertexCfg3.expAffectRows = endValue * 3;
    vertexCfg3.startVal = 0;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg3, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg3, 3, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 038.存在新版本中间版本最老版本数据进行全表扫描
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(SpecialTable, Upgrade_001_003_003_038)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
#ifdef ENV_RTOSV2X
    endValue = 10;
#endif
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SpecialTableSchemaBigObj.gmjson";

    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    GtSpeciallabelCfgT vertexCfg2 = {(int32_t)endValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_INSERT};
    GtSpeciallabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue, 0, 1, 0, 3, 3, 2, GMC_OPERATION_INSERT};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfg, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg2, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel3StructWrite(g_stmt, vertexCfg3, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
 
    // 核查
    GtSpeciallabelCfgT pkReadCfg = {startValue, endValue * 3, 0, (int32_t)endValue * 3, 0, 3, 3, 0, GMC_OPERATION_SCAN};
    ret = TestSpecialTWholeRead(g_stmt, pkReadCfg, bytesValue, stringValue, isDefaultValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    pkReadCfg.schemaVersion = 1;
    ret = TestSpecialTWholeRead(g_stmt, pkReadCfg, bytesValue, stringValue, isDefaultValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    pkReadCfg.schemaVersion = 2;
    ret = TestSpecialTWholeRead(g_stmt, pkReadCfg, bytesValue, stringValue, isDefaultValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 039.存在新版本中间版本最老版本数据进行truncate
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(SpecialTable, Upgrade_001_003_003_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
#ifdef ENV_RTOSV2X
    endValue = 10;
#endif
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    int32_t updateValue = 300;
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SpecialTableSchemaBigObj.gmjson";

    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    GtSpeciallabelCfgT vertexCfg2 = {(int32_t)endValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_INSERT};
    GtSpeciallabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue, 0, 1, 0, 3, 3, 2, GMC_OPERATION_INSERT};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfg, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg2, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel3StructWrite(g_stmt, vertexCfg3, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
     // truncate
    ret = GmcTruncateVertexLabel(g_stmt, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    vertexCfg3.schemaVersion = 0;
    vertexCfg3.expAffectRows = 0;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg3, 3, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg3.schemaVersion = 1;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg3, 3, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg3.schemaVersion = 2;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg3, 3, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 040.最老版本结构化读最新版本的数据
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(SpecialTable, Upgrade_001_003_003_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int fetchNum = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
#ifdef ENV_RTOSV2X
    endValue = 10;
#endif
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    uint32_t schemaVersion = 1;
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SpecialTableSchemaBigObj.gmjson";
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    GtSpeciallabelCfgT vertexCfg2 = {(int32_t)endValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_REPLACE};
    GtSpeciallabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue, 0, 1, 0, 3, 3, 2, GMC_OPERATION_REPLACE};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel3StructWrite(g_stmt, vertexCfg3, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg3.optType = GMC_OPERATION_SCAN;
    vertexCfg3.schemaVersion = 0;
    vertexCfg3.expAffectRows = endValue * 3;
    for (uint32_t keyId = 0; keyId < 5; keyId++) {
        ret = GtSpeciallabelStructIndexScan(g_stmt, vertexCfg3, keyId, bytesValue, stringValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 041.中间版本结构化读最新版本的数据
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(SpecialTable, Upgrade_001_003_003_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
#ifdef ENV_RTOSV2X
    endValue = 10;
#endif
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    uint32_t schemaVersion = 1;
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SpecialTableSchemaBigObj.gmjson";
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    GtSpeciallabelCfgT vertexCfg2 = {(int32_t)endValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_REPLACE};
    GtSpeciallabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue, 0, 1, 0, 3, 3, 2, GMC_OPERATION_REPLACE};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel3StructWrite(g_stmt, vertexCfg3, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg3.optType = GMC_OPERATION_SCAN;
    vertexCfg3.schemaVersion = 1;
    vertexCfg3.expAffectRows = endValue * 3;
    vertexCfg3.fieldIsNull[1] = false;
    vertexCfg3.fieldIsNull[2] = false;
    for (uint32_t keyId = 0; keyId < 5; keyId++) {
        ret = GtSpeciallabel2StructIndexScan(g_stmt, vertexCfg3, keyId, bytesValue, stringValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 042.最新版本结构化读最老版本的数据
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(SpecialTable, Upgrade_001_003_003_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
#ifdef ENV_RTOSV2X
    endValue = 10;
#endif
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    uint32_t schemaVersion = 1;
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SpecialTableSchemaBigObj.gmjson";
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_REPLACE};
    GtSpeciallabelCfgT vertexCfg2 = {(int32_t)endValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_REPLACE};
    GtSpeciallabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue, 0, 1, 0, 3, 3, 2, GMC_OPERATION_REPLACE};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel3StructWrite(g_stmt, vertexCfg3, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg.optType = GMC_OPERATION_SCAN;
    vertexCfg.schemaVersion = 2;
    vertexCfg.expAffectRows = endValue * 3;
    vertexCfg.fieldIsNull[1] = true;
    vertexCfg.fieldIsNull[2] = true;
    for (uint32_t keyId = 0; keyId < 5; keyId++) {
        ret = GtSpeciallabel3StructIndexScan(g_stmt, vertexCfg, keyId, bytesValue, stringValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 043.最新版本结构化读中间版本的数据
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(SpecialTable, Upgrade_001_003_003_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
#ifdef ENV_RTOSV2X
    endValue = 10;
#endif
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    uint32_t schemaVersion = 1;
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SpecialTableSchemaBigObj.gmjson";
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    GtSpeciallabelCfgT vertexCfg2 = {(int32_t)endValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_REPLACE};
    GtSpeciallabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue, 0, 1, 0, 3, 3, 2, GMC_OPERATION_REPLACE};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel3StructWrite(g_stmt, vertexCfg3, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg2.optType = GMC_OPERATION_SCAN;
    vertexCfg2.schemaVersion = 2;
    vertexCfg2.expAffectRows = endValue * 3;
    vertexCfg2.fieldIsNull[0] = false;
    vertexCfg2.fieldIsNull[1] = false;
    vertexCfg2.fieldIsNull[2] = true;
    for (uint32_t keyId = 0; keyId < 5; keyId++) {
        ret = GtSpeciallabel3StructIndexScan(g_stmt, vertexCfg2, keyId, bytesValue, stringValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 044.中间版本结构化读最老版本的数据
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(SpecialTable, Upgrade_001_003_003_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
#ifdef ENV_RTOSV2X
    endValue = 10;
#endif
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    uint32_t schemaVersion = 1;
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SpecialTableSchemaBigObj.gmjson";
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    GtSpeciallabelCfgT vertexCfg2 = {(int32_t)endValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_REPLACE};
    GtSpeciallabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue, 0, 1, 0, 3, 3, 2, GMC_OPERATION_REPLACE};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel3StructWrite(g_stmt, vertexCfg3, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg.optType = GMC_OPERATION_SCAN;
    vertexCfg.schemaVersion = 1;
    vertexCfg.expAffectRows = endValue * 3;
    vertexCfg.fieldIsNull[1] = true;
    vertexCfg.fieldIsNull[2] = true;
    for (uint32_t keyId = 0; keyId < 5; keyId++) {
        ret = GtSpeciallabel2StructIndexScan(g_stmt, vertexCfg, keyId, bytesValue, stringValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 045.最老版本结构化读中间版本的数据
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(SpecialTable, Upgrade_001_003_003_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 100;
#ifdef ENV_RTOSV2X
    endValue = 10;
#endif
    char *expectValue = (char *)"upgrade successfully";
    bool isDefaultValue = false;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    uint32_t schemaVersion = 1;
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SpecialTableSchemaBigObj.gmjson";
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    GtSpeciallabelCfgT vertexCfg2 = {(int32_t)endValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_REPLACE};
    GtSpeciallabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue, 0, 1, 0, 3, 3, 2, GMC_OPERATION_REPLACE};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel3StructWrite(g_stmt, vertexCfg3, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg2.optType = GMC_OPERATION_SCAN;
    vertexCfg2.schemaVersion = 0;
    vertexCfg2.expAffectRows = endValue * 3;
    for (uint32_t keyId = 0; keyId < 5; keyId++) {
        ret = GtSpeciallabelStructIndexScan(g_stmt, vertexCfg2, keyId, bytesValue, stringValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
