/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2012-2018. All rights reserved.
 Description  :表结构升级头文件
 Author       : wuxiaochun wx753022
 Modification :
 Date         : 2022/07/22
**************************************************************************** */
#ifndef VERTEXLABEL_UPGRADE_H
#define VERTEXLABEL_UPGRADE_H

#include "t_datacom_lite.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
char *g_labelName = (char *)"simpleLabel";
char *g_labelConfig = NULL;
typedef struct TagSimplelabelCfg {
    int32_t startVal;       // 主键或其他非成员索引的起始值
    uint32_t count;         // 主键或其他非成员索引的数量
    int32_t coefficient;    // 字段值生成系数, 通过startVal和coefficient组合生成不同的整形和浮点型字段值
    int32_t expAffectRows;  // 预期的affectRows
    GmcOperationTypeE optType;      // vertex操作类型
} GtSimplelabelCfgT;

typedef struct TaglabelCfg {
    int32_t startVal;       // 主键或其他非成员索引的起始值
    uint32_t count;         // 主键或其他非成员索引的数量
    int32_t coefficient;    // 字段值生成系数, 通过startVal和coefficient组合生成不同的整形和浮点型字段值
    int32_t expAffectRows;  // 预期的affectRows
    bool fieldIsNull[8];    // 新增版本的字段是否空值
} GtSimplelabelCfgRead;

int TestGetAffactRows(GmcStmtT *stmt, int32_t expectValue)
{
    int32_t affectRows = 0;
    int ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectValue, affectRows);
    return expectValue == affectRows ? GMERR_OK : 1;
}

void TestDropVertexLabel(const char *vertexLabelName, int32_t expectValue = GMERR_OK)
{
    int ret = 0;
    GmcConnT *connSync = NULL;
    GmcStmtT *stmtSync = NULL;
    ret = testGmcConnect(&connSync, &stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmtSync, vertexLabelName);
    AW_MACRO_EXPECT_EQ_INT(expectValue, ret);
    ret = testGmcDisconnect(connSync, stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

int TestCreateLabel(GmcStmtT *stmt, char *schemaPath, char *labelName, char const *configJson = g_labelConfig)
{
    int ret = 0;
    char *testSchema = NULL;

    if (schemaPath) {
        readJanssonFile(schemaPath, &testSchema);
        EXPECT_NE((void *)NULL, testSchema);
    }
    ret = GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, testSchema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret == GMERR_OK) {
        AW_FUN_Log(LOG_INFO, "[INFO]Test create label %s success \n", labelName);
    } else {
        testGmcGetLastError(NULL);
    }
    if (testSchema) {
        free(testSchema);
        testSchema = NULL;
    }
    return ret;
}

int TestUpdateVertexLabel(char *schemaPath, char *expectValue, char *labelName = NULL, char *uWay = (char *)"online",
                          char *nsName = g_testNameSpace)
{
    char *schema = NULL;
    readJanssonFile(schemaPath, &schema);
    EXPECT_NE((void *)NULL, schema);
    free(schema);
    // gmddl工具升级表操作
    char cmd[512] = {0};
    int ret = 0;
    if (labelName) {
        (void)snprintf(cmd, 512, "%s/gmddl -c alter -t %s -f %s -u %s -ns %s", g_toolPath, labelName, schemaPath, uWay,
            nsName);
    } else {
        (void)snprintf(cmd, 512, "%s/gmddl -c alter -f %s -u %s -ns %s", g_toolPath, schemaPath, uWay, nsName);
    }
    AW_FUN_Log(LOG_INFO, "cmd: %s\n", cmd);
    ret = executeCommand(cmd, expectValue);
    if (ret != GMERR_OK) {
        system(cmd);
    }
    return ret;
}

/**-------------------简单表-----------------------**/
#define SIMPLE_LABEL_FIXED_SIZE   9
#define SIMPLE_LABEL2_FIXED_SIZE  8
#define SIMPLE_LABEL2_BITMAP_SIZE 8
#define MAX_MASK_LEN_16 1000
#define MAX_MASK_LEN_24 2501000
#define SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE   13312
#define SIMPLE_LABEL_ADD_FIXED_SIZE   1331

void TestSimpleT1SetPk(GmcStmtT *stmt, int64_t i)
{
    int ret = 0;
    int64_t f0Value = i;
    ret = GmcSetVertexProperty(stmt, (char *)"F0", GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSimpleT1OldVersionSetProperty(GmcStmtT *stmt, int64_t i, bool isDefaultValue = true)
{
    int ret = 0;
    int64_t f0Value = i;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    uint32_t f3Value = i;
    int16_t f4Value = i % 32768;
    uint16_t f5Value = i % 65536;
    uint64_t f7Value = i;
    uint32_t vrid = 0;
    uint32_t vrfIndex = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    uint8_t f13Value = i & 0xf;
    ret = GmcSetVertexProperty(stmt, (char *)"F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F4", GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F5", GMC_DATATYPE_UINT16, &f5Value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F7", GMC_DATATYPE_TIME, &f7Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    if (!isDefaultValue) {
        uint8_t fixedValue[SIMPLE_LABEL_FIXED_SIZE] = {0};
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = j;
        }
        uint8_t f9Value = i % 31;
        uint16_t f10Value = i % 1023;
        ret = GmcSetVertexProperty(stmt, (char *)"F8", GMC_DATATYPE_FIXED, fixedValue, SIMPLE_LABEL_FIXED_SIZE);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, (char *)"F9", GMC_DATATYPE_BITFIELD8, &f9Value, sizeof(uint8_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, (char *)"F10", GMC_DATATYPE_BITFIELD16, &f10Value, sizeof(uint16_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcSetVertexProperty(stmt, (char *)"F3", GMC_DATATYPE_UINT32, &vrid, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F11", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    if (i <= MAX_MASK_LEN_16) {
        destIpAddr = ((i + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if (i > MAX_MASK_LEN_16 && i <= MAX_MASK_LEN_24) {
        destIpAddr = ((i + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((i + 2));
        maskLen = ((32) & 0xff);
    }
    ret = GmcSetVertexProperty(stmt, (char *)"F12", GMC_DATATYPE_UINT32, &destIpAddr, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F6", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F13", GMC_DATATYPE_BITFIELD8, &f13Value, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}


void TestSimpleT1UpdateSetProperty(GmcStmtT *stmt, int64_t i, bool isDefaultValue = true)
{
    int ret = 0;
    int64_t f0Value = i;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    uint32_t f3Value = i;
    int16_t f4Value = i % 32768;
    uint16_t f5Value = i% 65536;
    uint64_t f7Value = i;
    uint8_t f13Value = i & 0xf;
    ret = GmcSetVertexProperty(stmt, (char *)"F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F4", GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F5", GMC_DATATYPE_UINT16, &f5Value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F7", GMC_DATATYPE_TIME, &f7Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint8_t fixedValue[SIMPLE_LABEL_FIXED_SIZE] = {0};
    uint8_t f9Value;
    uint16_t f10Value;
    if (!isDefaultValue) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = j;
        }
        f9Value = i % 31;
        f10Value = i % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = 0xff;
        }
        f9Value = 0x1f;
        f10Value = 0x3ff;
    }
    ret = GmcSetVertexProperty(stmt, (char *)"F8", GMC_DATATYPE_FIXED, fixedValue, SIMPLE_LABEL_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F9", GMC_DATATYPE_BITFIELD8, &f9Value, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F10", GMC_DATATYPE_BITFIELD16, &f10Value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F13", GMC_DATATYPE_BITFIELD8, &f13Value, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSimpleT1NewFieldSetOk(GmcStmtT *stmt, int64_t i)
{
    uint64_t f14Value = i;
    int32_t ret = GmcSetVertexProperty(stmt, (char *)"F14", GMC_DATATYPE_UINT64, &f14Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSimpleT2NewFieldSetOk(GmcStmtT *stmt, int64_t value)
{
    int ret = 0;
    int64_t f14 = value;
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_INT64, &f14, sizeof(f14));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint64_t f15 = value;
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_UINT64, &f15, sizeof(f15));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int32_t f16 = value;
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_INT32, &f16, sizeof(f16));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint32_t f17 = value;
    ret = GmcSetVertexProperty(stmt, "F17", GMC_DATATYPE_UINT32, &f17, sizeof(f17));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int16_t f18 = (value) & 0x7fff;
    ret = GmcSetVertexProperty(stmt, "F18", GMC_DATATYPE_INT16, &f18, sizeof(f18));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint16_t f19 = (value) & 0xffff;
    ret = GmcSetVertexProperty(stmt, "F19", GMC_DATATYPE_UINT16, &f19, sizeof(f19));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int8_t f20 = (value) & 0x7f;
    ret = GmcSetVertexProperty(stmt, "F20", GMC_DATATYPE_INT8, &f20, sizeof(f20));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f21 = (value) & 0xff;
    ret = GmcSetVertexProperty(stmt, "F21", GMC_DATATYPE_UINT8, &f21, sizeof(f21));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint64_t f22 = value;
    ret = GmcSetVertexProperty(stmt, "F22", GMC_DATATYPE_TIME, &f22, sizeof(f22));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f23[SIMPLE_LABEL2_FIXED_SIZE] = {0};
    for (int j = 0; j < SIMPLE_LABEL2_FIXED_SIZE; j++) {
        f23[j] = j;
    }
    ret = GmcSetVertexProperty(stmt, "F23", GMC_DATATYPE_FIXED, f23, sizeof(f23));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f24 = (value) & 0x1f;
    ret = GmcSetVertexProperty(stmt, "F24", GMC_DATATYPE_BITFIELD8, &f24, sizeof(f24));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint16_t f25 = (value) & 0x3ff;
    ret = GmcSetVertexProperty(stmt, "F25", GMC_DATATYPE_BITFIELD16, &f25, sizeof(f25));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint32_t f26 = (value) & 0x1ffff;
    ret = GmcSetVertexProperty(stmt, "F26", GMC_DATATYPE_BITFIELD32, &f26, sizeof(f26));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint64_t f27 = (value) & 0x1ffffffff;
    ret = GmcSetVertexProperty(stmt, "F27", GMC_DATATYPE_BITFIELD64, &f27, sizeof(f27));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    bool f28 = value;
    ret = GmcSetVertexProperty(stmt, "F28", GMC_DATATYPE_BOOL, &f28, sizeof(f28));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    float f29 = value;
    ret = GmcSetVertexProperty(stmt, "F29", GMC_DATATYPE_FLOAT, &f29, sizeof(f29));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    double f30 = value;
    ret = GmcSetVertexProperty(stmt, "F30", GMC_DATATYPE_DOUBLE, &f30, sizeof(f30));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f31Bits[1] = {0xff};
    GmcBitMapT f31 = {0};
    f31.beginPos = 0;
    f31.endPos = 8 - 1;
    f31.bits = f31Bits;
    ret = GmcSetVertexProperty(stmt, "F31", GMC_DATATYPE_BITMAP, &f31, sizeof(f31));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSimpleT3FullFieldUpdateSetOk(GmcStmtT *stmt, int64_t value)
{
    int ret = GMERR_OK;
    uint64_t i = value;
    uint8_t f32[SIMPLE_LABEL_ADD_FIXED_SIZE] = {0};
    memset(f32, 0, SIMPLE_LABEL_ADD_FIXED_SIZE);
    f32[0] = 65;
    f32[SIMPLE_LABEL_ADD_FIXED_SIZE - 1] = i % 256;
    ret = GmcSetVertexProperty(stmt, "F32", GMC_DATATYPE_FIXED, f32, sizeof(f32));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSimpleT3NewFieldSetOk(GmcStmtT *stmt, int64_t value)
{
    int ret = GMERR_OK;
    uint64_t i = value;
    uint8_t f23[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f23, sizeof(f23), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F23", GMC_DATATYPE_FIXED, f23, sizeof(f23));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f24[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f24, sizeof(f24), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F24", GMC_DATATYPE_FIXED, f24, sizeof(f24));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f25[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f25, sizeof(f25), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F25", GMC_DATATYPE_FIXED, f25, sizeof(f25));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f26[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f26, sizeof(f26), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F26", GMC_DATATYPE_FIXED, f26, sizeof(f26));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f27[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f27, sizeof(f27), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F27", GMC_DATATYPE_FIXED, f27, sizeof(f27));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f28[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f28, sizeof(f28), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F28", GMC_DATATYPE_FIXED, f28, sizeof(f28));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f29[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f29, sizeof(f29), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F29", GMC_DATATYPE_FIXED, f29, sizeof(f29));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f30[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f30, sizeof(f30), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F30", GMC_DATATYPE_FIXED, f30, sizeof(f30));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f31[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f31, sizeof(f31), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F31", GMC_DATATYPE_FIXED, f31, sizeof(f31));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f32[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f32, sizeof(f32), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F32", GMC_DATATYPE_FIXED, f32, sizeof(f32));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f33[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f33, sizeof(f33), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F33", GMC_DATATYPE_FIXED, f33, sizeof(f33));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f34[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f34, sizeof(f34), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F34", GMC_DATATYPE_FIXED, f34, sizeof(f34));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f35[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f35, sizeof(f35), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F35", GMC_DATATYPE_FIXED, f35, sizeof(f35));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f36[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f36, sizeof(f36), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F36", GMC_DATATYPE_FIXED, f36, sizeof(f36));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f37[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f37, sizeof(f37), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F37", GMC_DATATYPE_FIXED, f37, sizeof(f37));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f38[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f38, sizeof(f38), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F38", GMC_DATATYPE_FIXED, f38, sizeof(f38));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f39[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f39, sizeof(f39), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F39", GMC_DATATYPE_FIXED, f39, sizeof(f39));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f40[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f40, sizeof(f40), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F40", GMC_DATATYPE_FIXED, f40, sizeof(f40));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f41[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f41, sizeof(f41), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F41", GMC_DATATYPE_FIXED, f41, sizeof(f41));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f42[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f42, sizeof(f42), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F42", GMC_DATATYPE_FIXED, f42, sizeof(f42));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f43[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f43, sizeof(f43), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F43", GMC_DATATYPE_FIXED, f43, sizeof(f43));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f44[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f44, sizeof(f44), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F44", GMC_DATATYPE_FIXED, f44, sizeof(f44));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f45[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f45, sizeof(f45), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F45", GMC_DATATYPE_FIXED, f45, sizeof(f45));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f46[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f46, sizeof(f46), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F46", GMC_DATATYPE_FIXED, f46, sizeof(f46));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f47[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f47, sizeof(f47), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F47", GMC_DATATYPE_FIXED, f47, sizeof(f47));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f48[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f48, sizeof(f48), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F48", GMC_DATATYPE_FIXED, f48, sizeof(f48));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f49[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f49, sizeof(f49), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F49", GMC_DATATYPE_FIXED, f49, sizeof(f49));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f50[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f50, sizeof(f50), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F50", GMC_DATATYPE_FIXED, f50, sizeof(f50));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f51[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f51, sizeof(f51), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F51", GMC_DATATYPE_FIXED, f51, sizeof(f51));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f52[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f52, sizeof(f52), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F52", GMC_DATATYPE_FIXED, f52, sizeof(f52));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f53[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f53, sizeof(f53), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F53", GMC_DATATYPE_FIXED, f53, sizeof(f53));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f54[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f54, sizeof(f54), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F54", GMC_DATATYPE_FIXED, f54, sizeof(f54));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f55[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f55, sizeof(f55), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F55", GMC_DATATYPE_FIXED, f55, sizeof(f55));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f56[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f56, sizeof(f56), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F56", GMC_DATATYPE_FIXED, f56, sizeof(f56));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f57[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f57, sizeof(f57), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F57", GMC_DATATYPE_FIXED, f57, sizeof(f57));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f58[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f58, sizeof(f58), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F58", GMC_DATATYPE_FIXED, f58, sizeof(f58));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f59[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f59, sizeof(f59), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F59", GMC_DATATYPE_FIXED, f59, sizeof(f59));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f60[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f60, sizeof(f60), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F60", GMC_DATATYPE_FIXED, f60, sizeof(f60));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f61[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f61, sizeof(f61), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F61", GMC_DATATYPE_FIXED, f61, sizeof(f61));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f62[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f62, sizeof(f62), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F62", GMC_DATATYPE_FIXED, f62, sizeof(f62));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f63[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f63, sizeof(f63), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F63", GMC_DATATYPE_FIXED, f63, sizeof(f63));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f64[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f64, sizeof(f64), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F64", GMC_DATATYPE_FIXED, f64, sizeof(f64));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f65[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f65, sizeof(f65), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F65", GMC_DATATYPE_FIXED, f65, sizeof(f65));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f66[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f66, sizeof(f66), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F66", GMC_DATATYPE_FIXED, f66, sizeof(f66));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f67[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f67, sizeof(f67), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F67", GMC_DATATYPE_FIXED, f67, sizeof(f67));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f68[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f68, sizeof(f68), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F68", GMC_DATATYPE_FIXED, f68, sizeof(f68));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f69[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f69, sizeof(f69), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F69", GMC_DATATYPE_FIXED, f69, sizeof(f69));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f70[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f70, sizeof(f70), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F70", GMC_DATATYPE_FIXED, f70, sizeof(f70));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f71[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f71, sizeof(f71), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F71", GMC_DATATYPE_FIXED, f71, sizeof(f71));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f72[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f72, sizeof(f72), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F72", GMC_DATATYPE_FIXED, f72, sizeof(f72));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f73[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f73, sizeof(f73), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F73", GMC_DATATYPE_FIXED, f73, sizeof(f73));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f74[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f74, sizeof(f74), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F74", GMC_DATATYPE_FIXED, f74, sizeof(f74));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f75[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f75, sizeof(f75), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F75", GMC_DATATYPE_FIXED, f75, sizeof(f75));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f76[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f76, sizeof(f76), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F76", GMC_DATATYPE_FIXED, f76, sizeof(f76));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f77[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f77, sizeof(f77), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F77", GMC_DATATYPE_FIXED, f77, sizeof(f77));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f78[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f78, sizeof(f78), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F78", GMC_DATATYPE_FIXED, f78, sizeof(f78));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f79[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f79, sizeof(f79), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F79", GMC_DATATYPE_FIXED, f79, sizeof(f79));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f80[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f80, sizeof(f80), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F80", GMC_DATATYPE_FIXED, f80, sizeof(f80));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f81[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f81, sizeof(f81), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F81", GMC_DATATYPE_FIXED, f81, sizeof(f81));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f82[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f82, sizeof(f82), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F82", GMC_DATATYPE_FIXED, f82, sizeof(f82));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f83[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f83, sizeof(f83), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F83", GMC_DATATYPE_FIXED, f83, sizeof(f83));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f84[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f84, sizeof(f84), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F84", GMC_DATATYPE_FIXED, f84, sizeof(f84));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f85[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f85, sizeof(f85), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F85", GMC_DATATYPE_FIXED, f85, sizeof(f85));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f86[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f86, sizeof(f86), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F86", GMC_DATATYPE_FIXED, f86, sizeof(f86));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f87[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f87, sizeof(f87), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F87", GMC_DATATYPE_FIXED, f87, sizeof(f87));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f88[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f88, sizeof(f88), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F88", GMC_DATATYPE_FIXED, f88, sizeof(f88));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f89[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f89, sizeof(f89), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F89", GMC_DATATYPE_FIXED, f89, sizeof(f89));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f90[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f90, sizeof(f90), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F90", GMC_DATATYPE_FIXED, f90, sizeof(f90));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f91[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f91, sizeof(f91), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F91", GMC_DATATYPE_FIXED, f91, sizeof(f91));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f92[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f92, sizeof(f92), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F92", GMC_DATATYPE_FIXED, f92, sizeof(f92));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f93[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f93, sizeof(f93), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F93", GMC_DATATYPE_FIXED, f93, sizeof(f93));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f94[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f94, sizeof(f94), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F94", GMC_DATATYPE_FIXED, f94, sizeof(f94));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f95[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f95, sizeof(f95), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F95", GMC_DATATYPE_FIXED, f95, sizeof(f95));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f96[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f96, sizeof(f96), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F96", GMC_DATATYPE_FIXED, f96, sizeof(f96));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f97[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f97, sizeof(f97), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F97", GMC_DATATYPE_FIXED, f97, sizeof(f97));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f98[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f98, sizeof(f98), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F98", GMC_DATATYPE_FIXED, f98, sizeof(f98));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f99[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f99, sizeof(f99), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F99", GMC_DATATYPE_FIXED, f99, sizeof(f99));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f100[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f100, sizeof(f100), "f%013310d", i);
    ret = GmcSetVertexProperty(stmt, "F100", GMC_DATATYPE_FIXED, f100, sizeof(f100));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSimpleT2NewFieldSetFailed(GmcStmtT *stmt, int64_t value)
{
    int ret = 0;
    int64_t f14 = value;
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_INT64, &f14, sizeof(f14));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint64_t f15 = value;
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_UINT64, &f15, sizeof(f15));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    int32_t f16 = value;
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_INT32, &f16, sizeof(f16));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint32_t f17 = value;
    ret = GmcSetVertexProperty(stmt, "F17", GMC_DATATYPE_UINT32, &f17, sizeof(f17));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    int16_t f18 = (value) & 0x7fff;
    ret = GmcSetVertexProperty(stmt, "F18", GMC_DATATYPE_INT16, &f18, sizeof(f18));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint16_t f19 = (value) & 0xffff;
    ret = GmcSetVertexProperty(stmt, "F19", GMC_DATATYPE_UINT16, &f19, sizeof(f19));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    int8_t f20 = (value) & 0x7f;
    ret = GmcSetVertexProperty(stmt, "F20", GMC_DATATYPE_INT8, &f20, sizeof(f20));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint8_t f21 = (value) & 0xff;
    ret = GmcSetVertexProperty(stmt, "F21", GMC_DATATYPE_UINT8, &f21, sizeof(f21));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint64_t f22 = value;
    ret = GmcSetVertexProperty(stmt, "F22", GMC_DATATYPE_TIME, &f22, sizeof(f22));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint8_t f23[SIMPLE_LABEL2_FIXED_SIZE] = {0};
    for (int j = 0; j < SIMPLE_LABEL2_FIXED_SIZE; j++) {
        f23[j] = j;
    }
    ret = GmcSetVertexProperty(stmt, "F23", GMC_DATATYPE_FIXED, f23, sizeof(f23));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint8_t f24 = (value) & 0x1f;
    ret = GmcSetVertexProperty(stmt, "F24", GMC_DATATYPE_BITFIELD8, &f24, sizeof(f24));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint16_t f25 = (value) & 0x3ff;
    ret = GmcSetVertexProperty(stmt, "F25", GMC_DATATYPE_BITFIELD16, &f25, sizeof(f25));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint32_t f26 = (value) & 0x1ffff;
    ret = GmcSetVertexProperty(stmt, "F26", GMC_DATATYPE_BITFIELD32, &f26, sizeof(f26));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint64_t f27 = (value) & 0x1ffffffff;
    ret = GmcSetVertexProperty(stmt, "F27", GMC_DATATYPE_BITFIELD64, &f27, sizeof(f27));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    bool f28 = value;
    ret = GmcSetVertexProperty(stmt, "F28", GMC_DATATYPE_BOOL, &f28, sizeof(f28));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    float f29 = value;
    ret = GmcSetVertexProperty(stmt, "F29", GMC_DATATYPE_FLOAT, &f29, sizeof(f29));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    double f30 = value;
    ret = GmcSetVertexProperty(stmt, "F30", GMC_DATATYPE_DOUBLE, &f30, sizeof(f30));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint8_t f31Bits[1] = {0xff};
    GmcBitMapT f31 = {0};
    f31.beginPos = 0;
    f31.endPos = 8 - 1;
    f31.bits = f31Bits;
    ret = GmcSetVertexProperty(stmt, "F31", GMC_DATATYPE_BITMAP, &f31, sizeof(f31));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
}

void TestSimpleT4NewFieldSetOk(GmcStmtT *stmt, int64_t value)
{
    int ret = 0;
    uint8_t f14 = value & 0x1;
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_BITFIELD8, &f14, sizeof(f14));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint32_t f15 = value;
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_UINT32, &f15, sizeof(f15));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint16_t f16 = value % 65536;
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_UINT16, &f16, sizeof(f16));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSimpleT4NewFieldSetFailed(GmcStmtT *stmt, int64_t value)
{
    int ret = 0;
    uint8_t f14 = value & 0x1;
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_BITFIELD8, &f14, sizeof(f14));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    uint32_t f15 = value;
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_UINT32, &f15, sizeof(f15));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    uint16_t f16 = value % 65536;
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_UINT16, &f16, sizeof(f16));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
}

void TestSimpleT3FullFieldUpdateSetFailed(GmcStmtT *stmt, int64_t i)
{
    uint8_t f32Value[SIMPLE_LABEL_ADD_FIXED_SIZE] = {0};
    int32_t ret = GmcSetVertexProperty(stmt, (char *)"F32", GMC_DATATYPE_FIXED, f32Value, sizeof(f32Value));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
}

void TestSimpleT1NewFieldSetFailed(GmcStmtT *stmt, int64_t i)
{
    uint64_t f14Value = i;
    int32_t ret = GmcSetVertexProperty(stmt, (char *)"F14", GMC_DATATYPE_UINT64, &f14Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
}

void TestSimpleT1LpmIndexSet(GmcStmtT *stmt, int64_t value)
{
    int ret = 0;
    uint32_t vrid = 0;
    uint32_t vrfIndex = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    int64_t uiVrIndex = value;
    ret = GmcSetIndexKeyName(stmt, "lpm4_key");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vrid, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    if (uiVrIndex <= MAX_MASK_LEN_16) {
        destIpAddr = ((uiVrIndex + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if (uiVrIndex > MAX_MASK_LEN_16 && uiVrIndex <= MAX_MASK_LEN_24) {
        destIpAddr = ((uiVrIndex + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((uiVrIndex + 2));
        maskLen = ((32) & 0xff);
    }
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &destIpAddr, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSimpleT1SetLpmProperty(GmcStmtT *stmt, int64_t i)
{
    int32_t ret = 0;
    uint32_t vrid = 0;
    uint32_t vrfIndex = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    ret = GmcSetVertexProperty(stmt, (char *)"F3", GMC_DATATYPE_UINT32, &vrid, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F11", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    if (i <= MAX_MASK_LEN_16) {
        destIpAddr = ((i + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if (i > MAX_MASK_LEN_16 && i <= MAX_MASK_LEN_24) {
        destIpAddr = ((i + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((i + 2));
        maskLen = ((32) & 0xff);
    }
    ret = GmcSetVertexProperty(stmt, (char *)"F12", GMC_DATATYPE_UINT32, &destIpAddr, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F6", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSimpleT1PkIndexSet(GmcStmtT *stmt, int64_t i)
{
    int ret = 0;
    int64_t f0Value = i;
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSimpleT1LocalhashIndexSet(GmcStmtT *stmt, int64_t i)
{
    int ret = 0;
    int16_t f4Value = i;
    uint16_t f5Value = i;
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT16, &f5Value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSimpleT1HashclusterIndexSet(GmcStmtT *stmt, int64_t i)
{
    int ret = 0;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    ret = GmcSetIndexKeyName(stmt, "hashcluster_unique_key");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSimpleT1LocalIndexSet(GmcStmtT *stmt, int64_t i)
{
    int ret = 0;
    uint32_t f3Value = 0;
    ret = GmcSetIndexKeyName(stmt, "local_key");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f3Value, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSimpleT1LocalIndexRangeSet(GmcStmtT *stmt, int64_t startValue, int64_t endValue)
{
    int ret = 0;
    unsigned int arrLen = 1;
    uint32_t lValue = startValue;
    uint32_t rValue = endValue;

    GmcPropValueT *leftKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    if (leftKeyProps == NULL) {
        AW_FUN_Log(LOG_ERROR, "leftKeyProps is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    leftKeyProps[0].type = GMC_DATATYPE_UINT32;
    leftKeyProps[0].value = &lValue;
    leftKeyProps[0].size = sizeof(uint32_t);

    GmcPropValueT *rightKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    if (rightKeyProps == NULL) {
        AW_FUN_Log(LOG_ERROR, "rightKeyProps is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    rightKeyProps[0].type = GMC_DATATYPE_UINT32;
    rightKeyProps[0].value = &rValue;
    rightKeyProps[0].size = sizeof(uint32_t);

    GmcRangeItemT items[arrLen];
    items[0].lValue = &leftKeyProps[0];
    items[0].rValue = &rightKeyProps[0];
    items[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].order = GMC_ORDER_ASC;
    ret = GmcSetKeyRange(stmt, items, arrLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "local_key");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(leftKeyProps);
    free(rightKeyProps);
}

void TestSimpleT1GetOldPropertyByName(GmcStmtT *stmt, int64_t i, bool isDefaultValue = true)
{
    int ret = 0;
    bool isNull = 0;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    int16_t f4Value = i % 32768;
    uint16_t f5Value = i % 65536;
    uint64_t f7Value = i;
    uint32_t vrid = 0;
    uint32_t vrfIndex = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;

    uint64_t f1Value2 = i;
    int32_t f2Value2 = i;
    int16_t f4Value2 = i % 32768;
    uint16_t f5Value2 = i % 65536;
    uint64_t f7Value2 = i;
    uint32_t vrid2 = 0;
    uint32_t vrfIndex2 = 0;
    uint32_t destIpAddr2 = 0;
    uint8_t maskLen2 = 0;

    uint8_t fixedValue[SIMPLE_LABEL_FIXED_SIZE] = {0};
    uint8_t f9Value = 0;
    uint16_t f10Value = 0;
    uint8_t fixedValueR[SIMPLE_LABEL_FIXED_SIZE] = {0};
    uint8_t f9ValueR = 0;
    uint16_t f10ValueR = 0;
    uint8_t f13Value = i & 0xf;
    uint8_t f13ValueR = 0;

    ret = GmcGetVertexPropertyByName(stmt, (char *)"F1", &f1Value2, sizeof(uint64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f1Value, f1Value2);

    ret = GmcGetVertexPropertyByName(stmt, (char *)"F2", &f2Value2, sizeof(int32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f2Value, f2Value2);

    ret = GmcGetVertexPropertyByName(stmt, (char *)"F4", &f4Value2, sizeof(int16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f4Value, f4Value2);

    ret = GmcGetVertexPropertyByName(stmt, (char *)"F5", &f5Value2, sizeof(uint16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f5Value, f5Value2);

    if (!isDefaultValue) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = j;
        }
        f9Value = i % 31;
        f10Value = i % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = 0xff;
        }
        f9Value = 0x1f;
        f10Value = 0x3ff;
    }
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F8", fixedValueR, SIMPLE_LABEL_FIXED_SIZE, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = memcmp(fixedValue, fixedValueR, SIMPLE_LABEL_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetVertexPropertyByName(stmt, (char *)"F9", &f9ValueR, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(f9Value, f9ValueR);
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F10", &f10ValueR, sizeof(uint16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(f10Value, f10ValueR);

    ret = GmcGetVertexPropertyByName(stmt, (char *)"F3", &vrid2, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(vrid, vrid2);
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F11", &vrfIndex2, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(vrfIndex, vrfIndex2);

    if (i <= MAX_MASK_LEN_16) {
        destIpAddr = ((i + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if (i > MAX_MASK_LEN_16 && i <= MAX_MASK_LEN_24) {
        destIpAddr = ((i + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((i + 2));
        maskLen = ((32) & 0xff);
    }
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F12", &destIpAddr2, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(destIpAddr, destIpAddr2);
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F6", &maskLen2, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(maskLen, maskLen2);
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F13", &f13ValueR, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(f13Value, f13ValueR);
}

void TestSimpleT2NewVersionGetNewValue(GmcStmtT *stmt, int64_t value, bool fieldIsNull = false)
{
    if (!fieldIsNull) {
        int64_t f14 = value;
        int32_t ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_INT64, &f14);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint64_t f15 = value;
        ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_UINT64, &f15);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        int32_t f16 = value;
        ret = queryPropertyAndCompare(stmt, "F16", GMC_DATATYPE_INT32, &f16);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint32_t f17 = value;
        ret = queryPropertyAndCompare(stmt, "F17", GMC_DATATYPE_UINT32, &f17);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        int16_t f18 = (value) & 0x7fff;
        ret = queryPropertyAndCompare(stmt, "F18", GMC_DATATYPE_INT16, &f18);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint16_t f19 = (value) & 0xffff;
        ret = queryPropertyAndCompare(stmt, "F19", GMC_DATATYPE_UINT16, &f19);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        int8_t f20 = (value) & 0x7f;
        ret = queryPropertyAndCompare(stmt, "F20", GMC_DATATYPE_INT8, &f20);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint8_t f21 = (value) & 0xff;
        ret = queryPropertyAndCompare(stmt, "F21", GMC_DATATYPE_UINT8, &f21);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint64_t f22 = value;
        ret = queryPropertyAndCompare(stmt, "F22", GMC_DATATYPE_TIME, &f22);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint8_t f23[SIMPLE_LABEL2_FIXED_SIZE] = {0};
        for (int j = 0; j < SIMPLE_LABEL2_FIXED_SIZE; j++) {
            f23[j] = j;
        }
        ret = queryPropertyAndCompare(stmt, "F23", GMC_DATATYPE_FIXED, f23);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint8_t f24 = (value) & 0x1f;
        ret = queryPropertyAndCompare(stmt, "F24", GMC_DATATYPE_BITFIELD8, &f24);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint16_t f25 = (value) & 0x3ff;
        ret = queryPropertyAndCompare(stmt, "F25", GMC_DATATYPE_BITFIELD16, &f25);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint32_t f26 = (value) & 0x1ffff;
        ret = queryPropertyAndCompare(stmt, "F26", GMC_DATATYPE_BITFIELD32, &f26);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint64_t f27 = (value) & 0x1ffffffff;
        ret = queryPropertyAndCompare(stmt, "F27", GMC_DATATYPE_BITFIELD64, &f27);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        bool f28 = value;
        ret = queryPropertyAndCompare(stmt, "F28", GMC_DATATYPE_BOOL, &f28);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        float f29 = value;
        ret = queryPropertyAndCompare(stmt, "F29", GMC_DATATYPE_FLOAT, &f29);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        double f30 = value;
        ret = queryPropertyAndCompare(stmt, "F30", GMC_DATATYPE_DOUBLE, &f30);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint8_t f31Bits[1] = {0xff};
        GmcBitMapT f31 = {0};
        f31.beginPos = 0;
        f31.endPos = 8 - 1;
        f31.bits = f31Bits;
        ret = queryPropertyAndCompare(stmt, "F31", GMC_DATATYPE_BITMAP, f31Bits);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    } else {
        int32_t ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_INT64, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_UINT64, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F16", GMC_DATATYPE_INT32, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F17", GMC_DATATYPE_UINT32, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F18", GMC_DATATYPE_INT16, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F19", GMC_DATATYPE_UINT16, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F20", GMC_DATATYPE_INT8, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F21", GMC_DATATYPE_UINT8, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F22", GMC_DATATYPE_TIME, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F23", GMC_DATATYPE_FIXED, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F24", GMC_DATATYPE_BITFIELD8, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F25", GMC_DATATYPE_BITFIELD16, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F26", GMC_DATATYPE_BITFIELD32, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F27", GMC_DATATYPE_BITFIELD64, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F28", GMC_DATATYPE_BOOL, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F29", GMC_DATATYPE_FLOAT, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F30", GMC_DATATYPE_DOUBLE, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint8_t f31Bits[1] = {0x00};
        GmcBitMapT f31 = {0};
        f31.beginPos = 0;
        f31.endPos = 8 - 1;
        f31.bits = f31Bits;
        ret = queryPropertyAndCompare(stmt, "F31", GMC_DATATYPE_BITMAP, f31Bits);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestSimpleT2NewVersionGetFaild(GmcStmtT *stmt, int64_t value)
{
    int64_t f14 = value;
    int32_t ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_INT64, &f14);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint64_t f15 = value;
    ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_UINT64, &f15);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    int32_t f16 = value;
    ret = queryPropertyAndCompare(stmt, "F16", GMC_DATATYPE_INT32, &f16);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint32_t f17 = value;
    ret = queryPropertyAndCompare(stmt, "F17", GMC_DATATYPE_UINT32, &f17);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    int16_t f18 = (value) & 0x7fff;
    ret = queryPropertyAndCompare(stmt, "F18", GMC_DATATYPE_INT16, &f18);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint16_t f19 = (value) & 0xffff;
    ret = queryPropertyAndCompare(stmt, "F19", GMC_DATATYPE_UINT16, &f19);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    int8_t f20 = (value) & 0x7f;
    ret = queryPropertyAndCompare(stmt, "F20", GMC_DATATYPE_INT8, &f20);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint8_t f21 = (value) & 0xff;
    ret = queryPropertyAndCompare(stmt, "F21", GMC_DATATYPE_UINT8, &f21);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint64_t f22 = value;
    ret = queryPropertyAndCompare(stmt, "F22", GMC_DATATYPE_TIME, &f22);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint8_t f23[SIMPLE_LABEL2_FIXED_SIZE] = {0};
    for (int j = 0; j < SIMPLE_LABEL2_FIXED_SIZE; j++) {
        f23[j] = j;
    }
    ret = queryPropertyAndCompare(stmt, "F23", GMC_DATATYPE_FIXED, f23);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint8_t f24 = (value) & 0x1f;
    ret = queryPropertyAndCompare(stmt, "F24", GMC_DATATYPE_BITFIELD8, &f24);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint16_t f25 = (value) & 0x3ff;
    ret = queryPropertyAndCompare(stmt, "F25", GMC_DATATYPE_BITFIELD16, &f25);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint32_t f26 = (value) & 0x1ffff;
    ret = queryPropertyAndCompare(stmt, "F26", GMC_DATATYPE_BITFIELD32, &f26);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint64_t f27 = (value) & 0x1ffffffff;
    ret = queryPropertyAndCompare(stmt, "F27", GMC_DATATYPE_BITFIELD64, &f27);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    bool f28 = value;
    ret = queryPropertyAndCompare(stmt, "F28", GMC_DATATYPE_BOOL, &f28);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    float f29 = value;
    ret = queryPropertyAndCompare(stmt, "F29", GMC_DATATYPE_FLOAT, &f29);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    double f30 = value;
    ret = queryPropertyAndCompare(stmt, "F30", GMC_DATATYPE_DOUBLE, &f30);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint8_t f31Bits[1] = {0xff};
    GmcBitMapT f31 = {0};
    f31.beginPos = 0;
    f31.endPos = 8 - 1;
    f31.bits = f31Bits;
    ret = queryPropertyAndCompare(stmt, "F31", GMC_DATATYPE_BITMAP, f31Bits);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
}

void TestSimpleT3FullfieldsUpdateGetNewValue(GmcStmtT *stmt, int64_t value, bool filedIsNull = false)
{
    int ret = GMERR_OK;
    if (!filedIsNull) {
        uint64_t i = value;
        uint8_t f32[SIMPLE_LABEL_ADD_FIXED_SIZE] = {0};
        memset(f32, 0, SIMPLE_LABEL_ADD_FIXED_SIZE);
        f32[0] = 65;
        f32[SIMPLE_LABEL_ADD_FIXED_SIZE - 1] = i % 256;
        ret = queryPropertyAndCompare(stmt, "F32", GMC_DATATYPE_FIXED, f32);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = queryPropertyAndCompare(stmt, "F32", GMC_DATATYPE_FIXED, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestSimpleT3FullfieldsUpdateGetFailed(GmcStmtT *stmt, int64_t value)
{
    int ret = GMERR_OK;
    bool isNull = false;
    uint64_t i = value;
    uint8_t f32[SIMPLE_LABEL_ADD_FIXED_SIZE] = {0};
    (void)snprintf((char *)f32, sizeof(f32), "f%01329d", i);
    ret = GmcGetVertexPropertyByName(stmt, "F32", f32, SIMPLE_LABEL_ADD_FIXED_SIZE, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = queryPropertyAndCompare(stmt, "F32", GMC_DATATYPE_FIXED, f32);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
}

void TestSimpleT3NewVersionGetNewValue(GmcStmtT *stmt, int64_t value)
{
    int ret = GMERR_OK;
    uint64_t i = value;
    uint8_t f23[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f23, sizeof(f23), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F23", GMC_DATATYPE_FIXED, f23);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f24[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f24, sizeof(f24), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F24", GMC_DATATYPE_FIXED, f24);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f25[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f25, sizeof(f25), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F25", GMC_DATATYPE_FIXED, f25);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f26[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f26, sizeof(f26), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F26", GMC_DATATYPE_FIXED, f26);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f27[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f27, sizeof(f27), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F27", GMC_DATATYPE_FIXED, f27);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f28[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f28, sizeof(f28), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F28", GMC_DATATYPE_FIXED, f28);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f29[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f29, sizeof(f29), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F29", GMC_DATATYPE_FIXED, f29);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f30[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f30, sizeof(f30), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F30", GMC_DATATYPE_FIXED, f30);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f31[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f31, sizeof(f31), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F31", GMC_DATATYPE_FIXED, f31);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f32[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f32, sizeof(f32), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F32", GMC_DATATYPE_FIXED, f32);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f33[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f33, sizeof(f33), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F33", GMC_DATATYPE_FIXED, f33);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f34[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f34, sizeof(f34), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F34", GMC_DATATYPE_FIXED, f34);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f35[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f35, sizeof(f35), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F35", GMC_DATATYPE_FIXED, f35);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f36[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f36, sizeof(f36), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F36", GMC_DATATYPE_FIXED, f36);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f37[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f37, sizeof(f37), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F37", GMC_DATATYPE_FIXED, f37);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f38[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f38, sizeof(f38), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F38", GMC_DATATYPE_FIXED, f38);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f39[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f39, sizeof(f39), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F39", GMC_DATATYPE_FIXED, f39);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f40[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f40, sizeof(f40), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F40", GMC_DATATYPE_FIXED, f40);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f41[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f41, sizeof(f41), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F41", GMC_DATATYPE_FIXED, f41);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f42[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f42, sizeof(f42), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F42", GMC_DATATYPE_FIXED, f42);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f43[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f43, sizeof(f43), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F43", GMC_DATATYPE_FIXED, f43);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f44[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f44, sizeof(f44), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F44", GMC_DATATYPE_FIXED, f44);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f45[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f45, sizeof(f45), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F45", GMC_DATATYPE_FIXED, f45);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f46[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f46, sizeof(f46), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F46", GMC_DATATYPE_FIXED, f46);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f47[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f47, sizeof(f47), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F47", GMC_DATATYPE_FIXED, f47);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f48[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f48, sizeof(f48), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F48", GMC_DATATYPE_FIXED, f48);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f49[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f49, sizeof(f49), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F49", GMC_DATATYPE_FIXED, f49);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f50[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f50, sizeof(f50), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F50", GMC_DATATYPE_FIXED, f50);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f51[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f51, sizeof(f51), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F51", GMC_DATATYPE_FIXED, f51);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f52[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f52, sizeof(f52), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F52", GMC_DATATYPE_FIXED, f52);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f53[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f53, sizeof(f53), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F53", GMC_DATATYPE_FIXED, f53);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f54[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f54, sizeof(f54), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F54", GMC_DATATYPE_FIXED, f54);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f55[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f55, sizeof(f55), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F55", GMC_DATATYPE_FIXED, f55);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f56[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f56, sizeof(f56), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F56", GMC_DATATYPE_FIXED, f56);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f57[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f57, sizeof(f57), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F57", GMC_DATATYPE_FIXED, f57);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f58[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f58, sizeof(f58), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F58", GMC_DATATYPE_FIXED, f58);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f59[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f59, sizeof(f59), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F59", GMC_DATATYPE_FIXED, f59);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f60[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f60, sizeof(f60), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F60", GMC_DATATYPE_FIXED, f60);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f61[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f61, sizeof(f61), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F61", GMC_DATATYPE_FIXED, f61);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f62[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f62, sizeof(f62), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F62", GMC_DATATYPE_FIXED, f62);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f63[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f63, sizeof(f63), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F63", GMC_DATATYPE_FIXED, f63);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f64[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f64, sizeof(f64), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F64", GMC_DATATYPE_FIXED, f64);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f65[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f65, sizeof(f65), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F65", GMC_DATATYPE_FIXED, f65);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f66[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f66, sizeof(f66), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F66", GMC_DATATYPE_FIXED, f66);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f67[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f67, sizeof(f67), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F67", GMC_DATATYPE_FIXED, f67);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f68[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f68, sizeof(f68), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F68", GMC_DATATYPE_FIXED, f68);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f69[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f69, sizeof(f69), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F69", GMC_DATATYPE_FIXED, f69);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f70[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f70, sizeof(f70), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F70", GMC_DATATYPE_FIXED, f70);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f71[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f71, sizeof(f71), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F71", GMC_DATATYPE_FIXED, f71);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f72[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f72, sizeof(f72), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F72", GMC_DATATYPE_FIXED, f72);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f73[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f73, sizeof(f73), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F73", GMC_DATATYPE_FIXED, f73);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f74[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f74, sizeof(f74), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F74", GMC_DATATYPE_FIXED, f74);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f75[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f75, sizeof(f75), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F75", GMC_DATATYPE_FIXED, f75);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f76[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f76, sizeof(f76), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F76", GMC_DATATYPE_FIXED, f76);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f77[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f77, sizeof(f77), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F77", GMC_DATATYPE_FIXED, f77);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f78[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f78, sizeof(f78), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F78", GMC_DATATYPE_FIXED, f78);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f79[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f79, sizeof(f79), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F79", GMC_DATATYPE_FIXED, f79);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f80[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f80, sizeof(f80), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F80", GMC_DATATYPE_FIXED, f80);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f81[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f81, sizeof(f81), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F81", GMC_DATATYPE_FIXED, f81);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f82[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f82, sizeof(f82), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F82", GMC_DATATYPE_FIXED, f82);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f83[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f83, sizeof(f83), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F83", GMC_DATATYPE_FIXED, f83);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f84[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f84, sizeof(f84), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F84", GMC_DATATYPE_FIXED, f84);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f85[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f85, sizeof(f85), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F85", GMC_DATATYPE_FIXED, f85);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f86[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f86, sizeof(f86), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F86", GMC_DATATYPE_FIXED, f86);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f87[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f87, sizeof(f87), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F87", GMC_DATATYPE_FIXED, f87);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f88[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f88, sizeof(f88), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F88", GMC_DATATYPE_FIXED, f88);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f89[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f89, sizeof(f89), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F89", GMC_DATATYPE_FIXED, f89);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f90[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f90, sizeof(f90), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F90", GMC_DATATYPE_FIXED, f90);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f91[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f91, sizeof(f91), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F91", GMC_DATATYPE_FIXED, f91);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f92[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f92, sizeof(f92), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F92", GMC_DATATYPE_FIXED, f92);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f93[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f93, sizeof(f93), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F93", GMC_DATATYPE_FIXED, f93);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f94[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f94, sizeof(f94), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F94", GMC_DATATYPE_FIXED, f94);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f95[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f95, sizeof(f95), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F95", GMC_DATATYPE_FIXED, f95);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f96[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f96, sizeof(f96), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F96", GMC_DATATYPE_FIXED, f96);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f97[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f97, sizeof(f97), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F97", GMC_DATATYPE_FIXED, f97);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f98[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f98, sizeof(f98), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F98", GMC_DATATYPE_FIXED, f98);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f99[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f99, sizeof(f99), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F99", GMC_DATATYPE_FIXED, f99);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f100[SIMPLE_LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    (void)snprintf((char *)f100, sizeof(f100), "f%013310d", i);
    ret = queryPropertyAndCompare(stmt, "F100", GMC_DATATYPE_FIXED, f100);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSimpleT1UpdateGetOldPropertyByName(GmcStmtT *stmt, int64_t i, bool isDefaultValue = true)
{
    int ret = 0;
    bool isNull = 0;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    int16_t f4Value = i % 32768;
    uint16_t f5Value = i % 65536;
    uint64_t f7Value = i;

    uint64_t f1Value2 = i;
    int32_t f2Value2 = i;
    int16_t f4Value2 = i % 32768;
    uint16_t f5Value2 = i % 65536;
    uint64_t f7Value2 = i;

    uint8_t fixedValue[SIMPLE_LABEL_FIXED_SIZE] = {0};
    uint8_t f9Value = 0;
    uint16_t f10Value = 0;
    uint8_t fixedValueR[SIMPLE_LABEL_FIXED_SIZE] = {0};
    uint8_t f9ValueR = 0;
    uint16_t f10ValueR = 0;
    uint8_t f13Value = i & 0xf;
    uint8_t f13ValueR = 0;
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F1", &f1Value2, sizeof(uint64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f1Value, f1Value2);

    ret = GmcGetVertexPropertyByName(stmt, (char *)"F2", &f2Value2, sizeof(int32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f2Value, f2Value2);

    ret = GmcGetVertexPropertyByName(stmt, (char *)"F4", &f4Value2, sizeof(int16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f4Value, f4Value2);

    ret = GmcGetVertexPropertyByName(stmt, (char *)"F5", &f5Value2, sizeof(uint16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f5Value, f5Value2);

    if (!isDefaultValue) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = j;
        }
        f9Value = i % 31;
        f10Value = i % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = 0xff;
        }
        f9Value = 0x1f;
        f10Value = 0x3ff;
    }
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F8", fixedValueR, SIMPLE_LABEL_FIXED_SIZE, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = memcmp(fixedValue, fixedValueR, SIMPLE_LABEL_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetVertexPropertyByName(stmt, (char *)"F9", &f9ValueR, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(f9Value, f9ValueR);
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F10", &f10ValueR, sizeof(uint16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(f10Value, f10ValueR);
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F13", &f13ValueR, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(f13Value, f13ValueR);
}

void TestSimpleT1GetLpmProperty(GmcStmtT *stmt, int64_t i)
{
    int ret = 0;
    bool isNull = false;
    uint32_t vrid = 0;
    uint32_t vrfIndex = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    uint32_t vrid2 = 0;
    uint32_t vrfIndex2 = 0;
    uint32_t destIpAddr2 = 0;
    uint8_t maskLen2 = 0;
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F3", &vrid2, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(vrid, vrid2);
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F11", &vrfIndex2, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(vrfIndex, vrfIndex2);

    if (i <= MAX_MASK_LEN_16) {
        destIpAddr = ((i + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if (i > MAX_MASK_LEN_16 && i <= MAX_MASK_LEN_24) {
        destIpAddr = ((i + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((i + 2));
        maskLen = ((32) & 0xff);
    }
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F12", &destIpAddr2, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(destIpAddr, destIpAddr2);
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F6", &maskLen2, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(maskLen, maskLen2);
}

void TestSimpleT1NewVersionGetNewValue(GmcStmtT *stmt, int64_t i)
{
    uint64_t f14Value = i;
    uint64_t f14ValueR = 0;
    bool isNull = false;
    int32_t ret = GmcGetVertexPropertyByName(stmt, (char *)"F14", &f14ValueR, sizeof(uint64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(f14Value, f14ValueR);
}

void TestSimpleT1NewVersionGetOldValue(GmcStmtT *stmt, int64_t i)
{
    uint64_t f14Value = i;
    uint64_t f14ValueR = 0;
    bool isNull = false;
    int32_t ret = GmcGetVertexPropertyByName(stmt, (char *)"F14", &f14ValueR, sizeof(uint64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(true, isNull);
}

void TestSimpleT1InsertOrReplace(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue,
                                 uint32_t schemaVersion, GmcOperationTypeE operationType, bool isDefaultValue = true)
{
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = startValue; i < endValue; i++) {
        TestSimpleT1SetPk(stmt, i);
        TestSimpleT1OldVersionSetProperty(stmt, i, isDefaultValue);
        if (schemaVersion == 0) {
            TestSimpleT1NewFieldSetFailed(stmt, i);
        } else if (schemaVersion == 1) {
            TestSimpleT1NewFieldSetOk(stmt, i);
        } else if (schemaVersion == 2) {
            TestSimpleT2NewFieldSetOk(stmt, i);
        } else if (schemaVersion == 3) {
            TestSimpleT3NewFieldSetOk(stmt, i);
        }
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(stmt, 1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestSimpleT1MergeOrUpdate(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue,
                               uint32_t schemaVersion, GmcOperationTypeE operationType,
                               bool isDefaultValue = true, int64_t updateValue = 0)
{
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    if (operationType == GMC_OPERATION_MERGE) {
        for (int i = startValue; i < endValue; i++) {
            TestSimpleT1PkIndexSet(stmt, i);
            TestSimpleT1OldVersionSetProperty(stmt, i, isDefaultValue);
            if (schemaVersion == 0) {
                TestSimpleT1NewFieldSetFailed(stmt, i);
            } else if (schemaVersion == 1) {
                TestSimpleT1NewFieldSetOk(stmt, i);
            } else if (schemaVersion == 2) {
                TestSimpleT2NewFieldSetOk(stmt, i);
            } else if (schemaVersion == 3) {
                TestSimpleT3NewFieldSetOk(stmt, i);
            }
            ret = GmcExecute(stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = TestGetAffactRows(stmt, 1);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    } else {
            for (int i = startValue; i < endValue; i++) {
            TestSimpleT1HashclusterIndexSet(stmt, i);
            TestSimpleT1UpdateSetProperty(stmt, i + updateValue, isDefaultValue);
            if (schemaVersion == 0) {
                TestSimpleT1NewFieldSetFailed(stmt, i);
            } else if (schemaVersion == 1) {
                TestSimpleT1NewFieldSetOk(stmt, i + updateValue);
            } else if (schemaVersion == 2) {
                TestSimpleT2NewFieldSetOk(stmt, i + updateValue);
            } else if (schemaVersion == 3) {
                TestSimpleT3NewFieldSetOk(stmt, i + updateValue);
            }
            ret = GmcExecute(stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = TestGetAffactRows(stmt, 1);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }
}

void TestSimpleT1PkScan(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue,
                        uint32_t schemaVersion, bool isDefaultValue = true, int64_t updateValue = 0)
{
    bool isFinish = true;
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = startValue; i < endValue; i++) {
        TestSimpleT1PkIndexSet(stmt, i);
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        TestSimpleT1UpdateGetOldPropertyByName(stmt, i + updateValue, isDefaultValue);
        TestSimpleT1GetLpmProperty(stmt, i);
        if (schemaVersion == 0) {
            TestSimpleT1NewVersionGetOldValue(stmt, i + updateValue);
        } else if (schemaVersion == 1) {
            TestSimpleT1NewVersionGetNewValue(stmt, i + updateValue);
        } else if (schemaVersion == 2) {
            TestSimpleT2NewVersionGetNewValue(stmt, i + updateValue);
        } else if (schemaVersion == 3) {
            TestSimpleT3NewVersionGetNewValue(stmt, i + updateValue);
        }
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(true, isFinish);
    }
}

void TestSimpleT1HashclusterScan(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue,
                                 uint32_t schemaVersion, bool isDefaultValue = true, int64_t updateValue = 0)
{
    bool isFinish = true;
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = startValue; i < endValue; i++) {
        TestSimpleT1HashclusterIndexSet(stmt, i + updateValue);
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        TestSimpleT1UpdateGetOldPropertyByName(stmt, i + updateValue, isDefaultValue);
        TestSimpleT1GetLpmProperty(stmt, i);
        if (schemaVersion == 0) {
            TestSimpleT1NewVersionGetOldValue(stmt, i);
        } else if (schemaVersion == 1) {
            TestSimpleT1NewVersionGetNewValue(stmt, i + updateValue);
        } else if (schemaVersion == 2) {
            TestSimpleT2NewVersionGetNewValue(stmt, i + updateValue);
        } else if (schemaVersion == 3) {
            TestSimpleT3NewVersionGetNewValue(stmt, i + updateValue);
        }
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(true, isFinish);
    }
}

void TestSimpleT2Delete(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue, uint32_t keyId,
                        uint32_t schemaVersion, int64_t updateValue = 0)
{
    bool isFinish = true;
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = startValue; i < endValue; i++) {
        if (keyId == 0) {
            TestSimpleT1PkIndexSet(stmt, i);
        } else if (keyId == 1) {
            TestSimpleT1HashclusterIndexSet(stmt, i + updateValue);
        } else if (keyId == 2) {
            TestSimpleT1LocalhashIndexSet(stmt, i + updateValue);
        } else if (keyId == 3) {
            TestSimpleT1LocalIndexRangeSet(stmt, i, endValue);
            ret = GmcExecute(stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            break;
        } else if (keyId == 4) {
            TestSimpleT1LpmIndexSet(stmt, i);
        }
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestSimpleT1LocalScan(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue,
                           uint32_t schemaVersion, int32_t *fetchNum, bool isDefaultValue = true,
                           int64_t updateValue = 0)
{
    bool isFinish = true;
    int64_t f0Value;
    bool isNull = 0;
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    TestSimpleT1LocalIndexRangeSet(stmt, startValue, endValue);
    ret = GmcExecute(stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isFinish);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    while (!isFinish) {
        (*fetchNum)++;
        ret = GmcGetVertexPropertyByName(stmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        TestSimpleT1UpdateGetOldPropertyByName(stmt, f0Value + updateValue, isDefaultValue);
        TestSimpleT1GetLpmProperty(stmt, f0Value);
        if (schemaVersion == 0) {
        } else if (schemaVersion == 1) {
            TestSimpleT1NewVersionGetNewValue(stmt, f0Value + updateValue);
        } else if (schemaVersion == 2) {
            TestSimpleT2NewVersionGetNewValue(stmt, f0Value + updateValue);
        } else if (schemaVersion == 3) {
            TestSimpleT3NewVersionGetNewValue(stmt, f0Value + updateValue);
        }
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestSimpleT1LocalhashScan(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue,
                               uint32_t schemaVersion, bool isDefaultValue = true, int64_t updateValue = 0)
{
    bool isFinish = true;
    int64_t f0Value;
    bool isNull = 0;
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = startValue; i < endValue; i++) {
        TestSimpleT1LocalhashIndexSet(stmt, i);
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        TestSimpleT1UpdateGetOldPropertyByName(stmt, i + updateValue, isDefaultValue);
        TestSimpleT1GetLpmProperty(stmt, i);
        if (schemaVersion == 0) {
        } else if (schemaVersion == 1) {
            TestSimpleT1NewVersionGetNewValue(stmt, i + updateValue);
        } else if (schemaVersion == 2) {
            TestSimpleT2NewVersionGetNewValue(stmt, i + updateValue);
        } else if (schemaVersion == 3) {
            TestSimpleT3NewVersionGetNewValue(stmt, i + updateValue);
        }
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(true, isFinish);
    }
}

void TestSimpleT1LpmScan(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue,
                         uint32_t schemaVersion, bool isDefaultValue = true, int64_t updateValue = 0)
{
    bool isFinish = true;
    int64_t f0Value;
    bool isNull = 0;
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = startValue; i < endValue; i++) {
        TestSimpleT1LpmIndexSet(stmt, i);
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        TestSimpleT1UpdateGetOldPropertyByName(stmt, i + updateValue, isDefaultValue);
        TestSimpleT1GetLpmProperty(stmt, i);
        if (schemaVersion == 0) {
        } else if (schemaVersion == 1) {
            TestSimpleT1NewVersionGetNewValue(stmt, i + updateValue);
        } else if (schemaVersion == 2) {
            TestSimpleT2NewVersionGetNewValue(stmt, i + updateValue);
        } else if (schemaVersion == 3) {
            TestSimpleT3NewVersionGetNewValue(stmt, i + updateValue);
        }
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(true, isFinish);
    }
}

void TestSimpleT3BatchWriteVertex(GmcConnT *conn, GmcStmtT *stmt, GtSimplelabelCfgT vertexCfg,
                                  bool isDefaultValue = true, int64_t updateValue = 0)
{
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t coefficient = vertexCfg.coefficient;
    GmcOperationTypeE optType = vertexCfg.optType;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;

    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    GmcBatchOptionT batchOption;
    int ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int32_t batchCount = 0;
    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, optType);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (optType == GMC_OPERATION_MERGE) {
            TestSimpleT1PkIndexSet(stmt, i);
        } else {
            TestSimpleT1SetPk(stmt, i);
        }
        // 设置所有根节点属性
        TestSimpleT1OldVersionSetProperty(stmt, i, isDefaultValue);
        TestSimpleT3NewFieldSetOk(stmt, i);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        batchCount++;

        if (batchCount != vertexCount && i != startPkVal + vertexCount - 1) {
            continue;
        }
        ret = GmcBatchExecute(batch, &batchRet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        AW_FUN_Log(LOG_INFO, "successNum: %d\n", successNum);
        AW_FUN_Log(LOG_INFO, "totalNum: %d\n", totalNum);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        batchCount = 0;
    }
    GmcBatchDestroy(batch);
}

int TestSimpleT3BatchUpdateVertex(GmcConnT *conn, GmcStmtT *stmt, GtSimplelabelCfgT vertexCfg,
                                  bool isDefaultValue = true, int64_t updateValue = 0)
{
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t coefficient = vertexCfg.coefficient;
    GmcOperationTypeE optType = vertexCfg.optType;

    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    int ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, optType);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSimpleT1HashclusterIndexSet(stmt, i);
        TestSimpleT1UpdateSetProperty(stmt, i + updateValue, isDefaultValue);
        TestSimpleT3NewFieldSetOk(stmt, i + updateValue);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchExecute(batch, &batchRet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        AW_MACRO_EXPECT_EQ_INT(1, successNum);
        AW_MACRO_EXPECT_EQ_INT(1, totalNum);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    GmcBatchDestroy(batch);
    return ret;
}

void TestSimpleTNewOldVersionWrite(GmcStmtT *stmt, GtSimplelabelCfgT vertexCfg, uint32_t schemaVersion,
                                   bool isDefaultValue = true)
{
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t endValue = vertexCfg.count;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t coefficient = vertexCfg.coefficient;
    GmcOperationTypeE optType = vertexCfg.optType;
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, schemaVersion, optType);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = startPkVal; i < endValue; i++) {
        if (optType == GMC_OPERATION_MERGE) {
            TestSimpleT1PkIndexSet(stmt, i);
        } else {
            TestSimpleT1SetPk(stmt, i);
        }
        TestSimpleT1OldVersionSetProperty(stmt, i, isDefaultValue);
        if (schemaVersion == 0) {
            TestSimpleT2NewFieldSetFailed(stmt, i);
            TestSimpleT3FullFieldUpdateSetFailed(stmt, i);
        } else if (schemaVersion == 2) {
            TestSimpleT2NewFieldSetOk(stmt, i);
        } else if (schemaVersion == 3) {
            TestSimpleT2NewFieldSetOk(stmt, i);
            TestSimpleT3FullFieldUpdateSetOk(stmt, i);
        }
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(stmt, expAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

int TestSimpleTNewOldVersionUpdate(GmcStmtT *stmt, GtSimplelabelCfgT vertexCfg, uint32_t schemaVersion, uint32_t keyId,
                                   bool isDefaultValue = true, int32_t updateValue = 0)
{
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t endValue = vertexCfg.count;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t coefficient = vertexCfg.coefficient;
    GmcOperationTypeE optType = vertexCfg.optType;
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, schemaVersion, optType);
    RETURN_IFERR(ret);
    for (int i = startPkVal; i < endValue; i++) {
        if (optType == GMC_OPERATION_MERGE) {
            TestSimpleT1PkIndexSet(stmt, i);
        } else {
            if (keyId == 0) {
            TestSimpleT1PkIndexSet(stmt, i);
            } else if (keyId == 1) {
                TestSimpleT1HashclusterIndexSet(stmt, i);
            } else if (keyId == 2) {
                TestSimpleT1LocalhashIndexSet(stmt, i);
            } else if (keyId == 3) {
                TestSimpleT1LocalIndexSet(stmt, i);
            } else if (keyId == 4) {
                TestSimpleT1LpmIndexSet(stmt, i);
            } else {
                AW_FUN_Log(LOG_ERROR, "keyId error!! keyId = %d", keyId);
            }
        }
        TestSimpleT1UpdateSetProperty(stmt, i + updateValue, isDefaultValue);
        if (schemaVersion == 0) {
            TestSimpleT2NewFieldSetFailed(stmt, i + updateValue);
            TestSimpleT3FullFieldUpdateSetFailed(stmt, i + updateValue);
        } else if (schemaVersion == 2) {
            TestSimpleT2NewFieldSetOk(stmt, i + updateValue);
            TestSimpleT3FullFieldUpdateSetFailed(stmt, i + updateValue);
        } else if (schemaVersion == 3) {
            TestSimpleT2NewFieldSetOk(stmt, i + updateValue);
            TestSimpleT3FullFieldUpdateSetOk(stmt, i + updateValue);
        }
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = TestGetAffactRows(stmt, expAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}


int TestSimpleTNewOldVersionRead(GmcStmtT *stmt, GtSimplelabelCfgRead vertexCfg, uint32_t schemaVersion, uint32_t keyId,
                                 bool isDefaultValue = true, int32_t updateValue = 0)
{
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t endValue = vertexCfg.count;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t coefficient = vertexCfg.coefficient;
    bool fieldIsNull[8] = {0};
    int32_t localFlag = 0;
    uint32_t fetchNum = 0;
    for (int i = 0; i < 8; i++) {
        fieldIsNull[i] = vertexCfg.fieldIsNull[i];
    }
    bool isFinish = false;
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, schemaVersion, GMC_OPERATION_SCAN);
    AW_FUN_Log(LOG_INFO, "g_labelName = %s schemaVersion = %d", g_labelName, schemaVersion);
    RETURN_IFERR(ret);
    for (int i = startPkVal; i < endValue; i++) {
        if (keyId == 0) {
        TestSimpleT1PkIndexSet(stmt, i);
        } else if (keyId == 1) {
            TestSimpleT1HashclusterIndexSet(stmt, i + updateValue);
        } else if (keyId == 2) {
            TestSimpleT1LocalhashIndexSet(stmt, i + updateValue);
        } else if (keyId == 3) {
            TestSimpleT1LocalIndexSet(stmt, i);
        } else if (keyId == 4) {
            TestSimpleT1LpmIndexSet(stmt, i);
        } else {
            AW_FUN_Log(LOG_ERROR, "keyId error!! keyId = %d", keyId);
        }
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = GmcFetch(stmt, &isFinish);
        RETURN_IFERR(ret);
        if (keyId == 3 && localFlag == 0) {
            localFlag = 1;
            int64_t f0Value = 0;
            bool isNull = false;
            bool newFieldIsNull[2] = {true};
            while (!isFinish) {
                fetchNum++;
                ret = GmcGetVertexPropertyByName(stmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                RETURN_IFERR(ret);
                if (f0Value >= startPkVal && f0Value < endValue) {
                    newFieldIsNull[0] = fieldIsNull[1];
                    newFieldIsNull[1] = fieldIsNull[2];
                } else {
                    if (f0Value >= 0 && f0Value < 100) {
                        newFieldIsNull[0] = true;
                        newFieldIsNull[1] = true;
                    } else if (f0Value >= 100 && f0Value < 200) {
                        newFieldIsNull[0] = false;
                        newFieldIsNull[1] = true;
                    } else {
                        newFieldIsNull[0] = false;
                        newFieldIsNull[1] = false;
                    }
                }
                if (f0Value >= startPkVal && f0Value < endValue) {
                    TestSimpleT1UpdateGetOldPropertyByName(stmt, f0Value + updateValue, isDefaultValue);
                    TestSimpleT1GetLpmProperty(stmt, f0Value);
                    if (schemaVersion == 0) {
                        TestSimpleT2NewVersionGetFaild(stmt, f0Value + updateValue);
                        TestSimpleT3FullfieldsUpdateGetFailed(stmt, f0Value + updateValue);
                    } else if (schemaVersion == 2) {
                        TestSimpleT2NewVersionGetNewValue(stmt, f0Value + updateValue, newFieldIsNull[0]);
                        TestSimpleT3FullfieldsUpdateGetFailed(stmt, f0Value + updateValue);
                    } else if (schemaVersion == 3) {
                        TestSimpleT2NewVersionGetNewValue(stmt, f0Value + updateValue, newFieldIsNull[0]);
                        TestSimpleT3FullfieldsUpdateGetNewValue(stmt, f0Value + updateValue, newFieldIsNull[1]);
                    }
                }
                ret = GmcFetch(stmt, &isFinish);
                RETURN_IFERR(ret);
            }
            AW_MACRO_EXPECT_EQ_INT(expAffectRows, fetchNum);
            fetchNum = 0;
            return 0;
        } else if (keyId != 3) {
            TestSimpleT1UpdateGetOldPropertyByName(stmt, i + updateValue, isDefaultValue);
            TestSimpleT1GetLpmProperty(stmt, i);
            if (schemaVersion == 0) {
                TestSimpleT2NewVersionGetFaild(stmt, i + updateValue);
                TestSimpleT3FullfieldsUpdateGetFailed(stmt, i + updateValue);
            } else if (schemaVersion == 2) {
                TestSimpleT2NewVersionGetNewValue(stmt, i + updateValue, fieldIsNull[1]);
                TestSimpleT3FullfieldsUpdateGetFailed(stmt, i + updateValue);
            } else if (schemaVersion == 3) {
                TestSimpleT2NewVersionGetNewValue(stmt, i + updateValue, fieldIsNull[1]);
                TestSimpleT3FullfieldsUpdateGetNewValue(stmt, i + updateValue, fieldIsNull[2]);
            }
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(true, isFinish);
            return ret;
        }
    }
    return ret;
}


int TestSimpleTNewOldVersionUpdateRead(GmcStmtT *stmt, GtSimplelabelCfgRead vertexCfg, uint32_t schemaVersion,
    uint32_t keyId, bool isDefaultValue, int32_t updateValue1 = 0, int32_t updateValue2 = 0, int32_t updateValue3 = 0)
{
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t endValue = vertexCfg.count;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t coefficient = vertexCfg.coefficient;
    bool fieldIsNull[8] = {0};
    int32_t localFlag = 0;
    uint32_t fetchNum = 0;
    for (int i = 0; i < 8; i++) {
        fieldIsNull[i] = vertexCfg.fieldIsNull[i];
    }
    bool isFinish = false;
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, schemaVersion, GMC_OPERATION_SCAN);
    AW_FUN_Log(LOG_INFO, "g_labelName = %s schemaVersion = %d", g_labelName, schemaVersion);
    RETURN_IFERR(ret);
    for (int i = startPkVal; i < endValue; i++) {
        if (keyId == 0) {
        TestSimpleT1PkIndexSet(stmt, i);
        } else if (keyId == 1) {
            TestSimpleT1HashclusterIndexSet(stmt, i + updateValue1);
        } else if (keyId == 2) {
            TestSimpleT1LocalhashIndexSet(stmt, i + updateValue1);
        } else if (keyId == 3) {
            TestSimpleT1LocalIndexSet(stmt, i + updateValue1);
        } else if (keyId == 4) {
            TestSimpleT1LpmIndexSet(stmt, i);
        } else {
            AW_FUN_Log(LOG_ERROR, "keyId error!! keyId = %d", keyId);
        }
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = GmcFetch(stmt, &isFinish);
        RETURN_IFERR(ret);
        if (keyId == 3 && localFlag == 0) {
            localFlag = 1;
            int64_t f0Value = 0;
            bool isNull = false;
            bool newFieldIsNull[2] = {true};
            while (!isFinish) {
                fetchNum++;
                ret = GmcGetVertexPropertyByName(stmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                RETURN_IFERR(ret);
                if (f0Value >= startPkVal && f0Value < endValue) {
                    newFieldIsNull[0] = fieldIsNull[1];
                    newFieldIsNull[1] = fieldIsNull[2];
                } else {
                    if (f0Value >= 0 && f0Value < 100) {
                        newFieldIsNull[0] = true;
                        newFieldIsNull[1] = true;
                    } else if (f0Value >= 100 && f0Value < 200) {
                        newFieldIsNull[0] = false;
                        newFieldIsNull[1] = true;
                    } else {
                        newFieldIsNull[0] = false;
                        newFieldIsNull[1] = false;
                    }
                }
                if (f0Value >= startPkVal && f0Value < endValue) {
                    TestSimpleT1UpdateGetOldPropertyByName(stmt, f0Value + updateValue1, isDefaultValue);
                    TestSimpleT1GetLpmProperty(stmt, f0Value);
                    if (schemaVersion == 0) {
                        TestSimpleT2NewVersionGetFaild(stmt, f0Value + updateValue2);
                        TestSimpleT3FullfieldsUpdateGetFailed(stmt, f0Value + updateValue3);
                    } else if (schemaVersion == 2) {
                        TestSimpleT2NewVersionGetNewValue(stmt, f0Value + updateValue2, newFieldIsNull[0]);
                        TestSimpleT3FullfieldsUpdateGetFailed(stmt, f0Value + updateValue3);
                    } else if (schemaVersion == 3) {
                        TestSimpleT2NewVersionGetNewValue(stmt, f0Value + updateValue2, newFieldIsNull[0]);
                        TestSimpleT3FullfieldsUpdateGetNewValue(stmt, f0Value + updateValue3, newFieldIsNull[1]);
                    }
                }
                ret = GmcFetch(stmt, &isFinish);
                RETURN_IFERR(ret);
            }
            AW_MACRO_EXPECT_EQ_INT(expAffectRows, fetchNum);
            fetchNum = 0;
            return 0;
        } else if (keyId != 3) {
            TestSimpleT1UpdateGetOldPropertyByName(stmt, i + updateValue1, isDefaultValue);
            TestSimpleT1GetLpmProperty(stmt, i);
            if (schemaVersion == 0) {
                TestSimpleT2NewVersionGetFaild(stmt, i + updateValue2);
                TestSimpleT3FullfieldsUpdateGetFailed(stmt, i + updateValue3);
            } else if (schemaVersion == 2) {
                TestSimpleT2NewVersionGetNewValue(stmt, i + updateValue2, fieldIsNull[1]);
                TestSimpleT3FullfieldsUpdateGetFailed(stmt, i + updateValue3);
            } else if (schemaVersion == 3) {
                TestSimpleT2NewVersionGetNewValue(stmt, i + updateValue2, fieldIsNull[1]);
                TestSimpleT3FullfieldsUpdateGetNewValue(stmt, i + updateValue3, fieldIsNull[2]);
            }
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(true, isFinish);
        }
    }
    return ret;
}

int TestSimpleTWholeRead(GmcStmtT *stmt, GtSimplelabelCfgRead vertexCfg, uint32_t schemaVersion, bool isDefaultValue)
{
    int ret = 0;
    bool isFinish = false;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    bool fieldIsNull[8] = {0};
    uint32_t fetchNum = 0;
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, schemaVersion, GMC_OPERATION_SCAN);
    AW_FUN_Log(LOG_INFO, "g_labelName = %s schemaVersion = %d", g_labelName, schemaVersion);
    RETURN_IFERR(ret);
    ret = GmcExecute(stmt);
    RETURN_IFERR(ret);
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        fetchNum++;
        int64_t f0Value = 0;
        bool isNull = false;
        bool newFieldIsNull[2] = {true};
        ret = GmcGetVertexPropertyByName(stmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
        RETURN_IFERR(ret);
        if (f0Value >= 0 && f0Value < expAffectRows / 3) {
            newFieldIsNull[0] = true;
            newFieldIsNull[1] = true;
        } else if (f0Value >= (expAffectRows / 3) && f0Value < (expAffectRows / 3 * 2)) {
            newFieldIsNull[0] = false;
            newFieldIsNull[1] = true;
        } else {
            newFieldIsNull[0] = false;
            newFieldIsNull[1] = false;
        }
        TestSimpleT1UpdateGetOldPropertyByName(stmt, f0Value, isDefaultValue);
        TestSimpleT1GetLpmProperty(stmt, f0Value);
        if (schemaVersion == 0) {
            TestSimpleT2NewVersionGetFaild(stmt, f0Value);
            TestSimpleT3FullfieldsUpdateGetFailed(stmt, f0Value);
        } else if (schemaVersion == 2) {
            TestSimpleT2NewVersionGetNewValue(stmt, f0Value, newFieldIsNull[0]);
            TestSimpleT3FullfieldsUpdateGetFailed(stmt, f0Value);
        } else if (schemaVersion == 3) {
            TestSimpleT2NewVersionGetNewValue(stmt, f0Value, newFieldIsNull[0]);
            TestSimpleT3FullfieldsUpdateGetNewValue(stmt, f0Value, newFieldIsNull[1]);
        }
    }
    return GMERR_OK;
}

void SubSimpleTCallBackWithNewVersion(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    char labelName[128] = {0};
    unsigned int labelNameLen = 0;
    uint32_t size;
    int count = 300;
    int updateValue = 300;
    char keyName[128] = {0};
    SnUserDataT *userData1 = (SnUserDataT *)userData;
    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }
        for (int i = 0; i < info->labelCount; i++) {
            (void)memset(labelName, 0, sizeof(labelName));
            labelNameLen = sizeof(labelName);
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);
            switch (info->msgType) {
                case 5: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_DELETE: {
                            // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            bool newFieldIsNull[2] = {true};
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value >= 0 && f0Value < count / 3) {
                                newFieldIsNull[0] = true;
                                newFieldIsNull[1] = true;
                            } else if (f0Value >= count / 3 && f0Value < count / 3 * 2) {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = true;
                            } else {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = false;
                            }
                            TestSimpleT1UpdateGetOldPropertyByName(subStmt, f0Value, false);
                            TestSimpleT1GetLpmProperty(subStmt, f0Value);
                            TestSimpleT2NewVersionGetNewValue(subStmt, f0Value, newFieldIsNull[0]);
                            TestSimpleT3FullfieldsUpdateGetNewValue(subStmt, f0Value, newFieldIsNull[1]);
                            break;
                        }
                        default: {
                            AW_FUN_Log(LOG_ERROR, "invalid eventType:%d  %d\r\n", info->eventType,
                                __LINE__);
                            break;
                        }
                    }
                    break;
                }
                case 6: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_INSERT: {
                            // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            bool newFieldIsNull[2] = {true};
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value >= 0 && f0Value < count / 3) {
                                newFieldIsNull[0] = true;
                                newFieldIsNull[1] = true;
                            } else if (f0Value >= count / 3 && f0Value < count / 3 * 2) {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = true;
                            } else {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = false;
                            }
                            TestSimpleT1UpdateGetOldPropertyByName(subStmt, f0Value, false);
                            TestSimpleT1GetLpmProperty(subStmt, f0Value);
                            TestSimpleT2NewVersionGetNewValue(subStmt, f0Value, newFieldIsNull[0]);
                            TestSimpleT3FullfieldsUpdateGetNewValue(subStmt, f0Value, newFieldIsNull[1]);
                            break;
                        }
                        default: {
                            AW_FUN_Log(LOG_ERROR, "invalid eventType %d  line:%d\r\n",
                                info->eventType, __LINE__);
                            break;
                        }
                    }
                    break;
                }
                case 7: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_UPDATE: {
                           // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            bool newFieldIsNull[2] = {true};
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value >= 0 && f0Value < count / 3 * 2) {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = true;
                            } else {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = false;
                            }
                            TestSimpleT1UpdateGetOldPropertyByName(subStmt, f0Value + updateValue, true);
                            TestSimpleT1GetLpmProperty(subStmt, f0Value);
                            TestSimpleT2NewVersionGetNewValue(subStmt, f0Value + updateValue, newFieldIsNull[0]);
                            TestSimpleT3FullfieldsUpdateGetNewValue(subStmt, f0Value + updateValue, newFieldIsNull[1]);
                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value >= 0 && f0Value < count / 3) {
                                newFieldIsNull[0] = true;
                                newFieldIsNull[1] = true;
                            } else if (f0Value >= count / 3 && f0Value < count / 3 * 2) {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = true;
                            } else {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = false;
                            }
                            TestSimpleT1UpdateGetOldPropertyByName(subStmt, f0Value, false);
                            TestSimpleT1GetLpmProperty(subStmt, f0Value);
                            TestSimpleT2NewVersionGetNewValue(subStmt, f0Value, newFieldIsNull[0]);
                            TestSimpleT3FullfieldsUpdateGetNewValue(subStmt, f0Value, newFieldIsNull[1]);
                            break;
                        }
                        case GMC_SUB_EVENT_REPLACE: {
                            // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            bool newFieldIsNull[2] = {true};
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value >= 0 && f0Value < count / 3) {
                                newFieldIsNull[0] = true;
                                newFieldIsNull[1] = true;
                            } else if (f0Value >= count / 3 && f0Value < count / 3 * 2) {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = true;
                            } else {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = false;
                            }
                            TestSimpleT1UpdateGetOldPropertyByName(subStmt, f0Value, false);
                            TestSimpleT1GetLpmProperty(subStmt, f0Value);
                            TestSimpleT2NewVersionGetNewValue(subStmt, f0Value, newFieldIsNull[0]);
                            TestSimpleT3FullfieldsUpdateGetNewValue(subStmt, f0Value, newFieldIsNull[1]);
                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
                            break;
                        }
                        case GMC_SUB_EVENT_MERGE_INSERT: {
                            // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            bool newFieldIsNull[2] = {true};
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value >= 0 && f0Value < count / 3) {
                                newFieldIsNull[0] = true;
                                newFieldIsNull[1] = true;
                            } else if (f0Value >= count / 3 && f0Value < count / 3 * 2) {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = true;
                            } else {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = false;
                            }
                            TestSimpleT1UpdateGetOldPropertyByName(subStmt, f0Value, false);
                            TestSimpleT1GetLpmProperty(subStmt, f0Value);
                            TestSimpleT2NewVersionGetNewValue(subStmt, f0Value, newFieldIsNull[0]);
                            TestSimpleT3FullfieldsUpdateGetNewValue(subStmt, f0Value, newFieldIsNull[1]);
                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
                            break;
                        }
                        default: {
                            AW_FUN_Log(LOG_ERROR, "invalid eventType %d  line:%d\r\n",
                                info->eventType, __LINE__);
                            break;
                        }
                    }
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_ERROR, "invalid eventMsgType: %d eventType:%d line:%d\r\n",
                        info->msgType, info->eventType, __LINE__);
                    break;
                }
            }
        }
        userData1->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                userData1->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                userData1->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                userData1->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_INSERT: {
                userData1->mergeInsertNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                userData1->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                userData1->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                userData1->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                userData1->agedNum++;
                break;
            }
            default: {
                break;
            }
        }
    }
}


void SubSimpleTCallBackWithOldVersion(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    char labelName[128] = {0};
    unsigned int labelNameLen = 0;
    uint32_t size;
    int count = 300;
    int updateValue = 300;
    char keyName[128] = {0};
    SnUserDataT *userData1 = (SnUserDataT *)userData;
    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }
        for (int i = 0; i < info->labelCount; i++) {
            (void)memset(labelName, 0, sizeof(labelName));
            labelNameLen = sizeof(labelName);
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);
            switch (info->msgType) {
                case 5: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_DELETE: {
                         // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            bool newFieldIsNull[2] = {true};
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value >= 0 && f0Value < count / 3) {
                                newFieldIsNull[0] = true;
                                newFieldIsNull[1] = true;
                            } else if (f0Value >= count / 3 && f0Value < count / 3 * 2) {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = true;
                            } else {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = false;
                            }
                            TestSimpleT1UpdateGetOldPropertyByName(subStmt, f0Value, false);
                            TestSimpleT1GetLpmProperty(subStmt, f0Value);
                            TestSimpleT2NewVersionGetNewValue(subStmt, f0Value, newFieldIsNull[0]);
                            TestSimpleT3FullfieldsUpdateGetFailed(subStmt, f0Value);
                            break;
                        }
                        default: {
                            AW_FUN_Log(LOG_ERROR, "invalid eventType:%d  %d\r\n", info->eventType,
                                __LINE__);
                            break;
                        }
                    }
                    break;
                }
                case 6: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_INSERT: {
                            // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            bool newFieldIsNull[2] = {true};
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value >= 0 && f0Value < count / 3) {
                                newFieldIsNull[0] = true;
                                newFieldIsNull[1] = true;
                            } else if (f0Value >= count / 3 && f0Value < count / 3 * 2) {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = true;
                            } else {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = false;
                            }
                            TestSimpleT1UpdateGetOldPropertyByName(subStmt, f0Value, false);
                            TestSimpleT1GetLpmProperty(subStmt, f0Value);
                            TestSimpleT2NewVersionGetNewValue(subStmt, f0Value, newFieldIsNull[0]);
                            TestSimpleT3FullfieldsUpdateGetFailed(subStmt, f0Value);
                            break;
                        }
                        default: {
                            AW_FUN_Log(LOG_ERROR, "invalid eventType %d  line:%d\r\n",
                                info->eventType, __LINE__);
                            break;
                        }
                    }
                    break;
                }
                case 7: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_UPDATE: {
                           // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            bool newFieldIsNull[2] = {true};
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            TestSimpleT1UpdateGetOldPropertyByName(subStmt, f0Value + updateValue, true);
                            TestSimpleT1GetLpmProperty(subStmt, f0Value);
                            TestSimpleT2NewVersionGetFaild(subStmt, f0Value);
                            TestSimpleT3FullfieldsUpdateGetFailed(subStmt, f0Value);
                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            TestSimpleT1UpdateGetOldPropertyByName(subStmt, f0Value, false);
                            TestSimpleT1GetLpmProperty(subStmt, f0Value);
                            TestSimpleT2NewVersionGetFaild(subStmt, f0Value);
                            TestSimpleT3FullfieldsUpdateGetFailed(subStmt, f0Value);
                            break;
                        }
                        case GMC_SUB_EVENT_REPLACE: {
                            // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            bool newFieldIsNull[2] = {true};
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value >= 0 && f0Value < count / 3) {
                                newFieldIsNull[0] = true;
                                newFieldIsNull[1] = true;
                            } else if (f0Value >= count / 3 && f0Value < count / 3 * 2) {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = true;
                            } else {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = false;
                            }
                            TestSimpleT1UpdateGetOldPropertyByName(subStmt, f0Value, false);
                            TestSimpleT1GetLpmProperty(subStmt, f0Value);
                            TestSimpleT2NewVersionGetFaild(subStmt, f0Value);
                            TestSimpleT3FullfieldsUpdateGetFailed(subStmt, f0Value);
                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
                            break;
                        }
                        case GMC_SUB_EVENT_MERGE_INSERT: {
                            // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            bool newFieldIsNull[2] = {true};
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value >= 0 && f0Value < count / 3) {
                                newFieldIsNull[0] = true;
                                newFieldIsNull[1] = true;
                            } else if (f0Value >= count / 3 && f0Value < count / 3 * 2) {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = true;
                            } else {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = false;
                            }
                            TestSimpleT1UpdateGetOldPropertyByName(subStmt, f0Value, false);
                            TestSimpleT1GetLpmProperty(subStmt, f0Value);
                            TestSimpleT2NewVersionGetNewValue(subStmt, f0Value, newFieldIsNull[0]);
                            TestSimpleT3FullfieldsUpdateGetFailed(subStmt, f0Value);
                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
                            break;
                        }
                        case GMC_SUB_EVENT_MERGE_UPDATE: {
                           // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            bool newFieldIsNull[2] = {true};
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            if (f0Value >= 0 && f0Value < count / 3 * 2) {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = true;
                            } else {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = false;
                            }
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            TestSimpleT1UpdateGetOldPropertyByName(subStmt, f0Value + updateValue, true);
                            TestSimpleT1GetLpmProperty(subStmt, f0Value);
                            TestSimpleT2NewVersionGetNewValue(subStmt, f0Value + updateValue, newFieldIsNull[0]);
                            TestSimpleT3FullfieldsUpdateGetFailed(subStmt, f0Value);
                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            if (f0Value >= 0 && f0Value < count / 3) {
                                newFieldIsNull[0] = true;
                                newFieldIsNull[1] = true;
                            } else if (f0Value >= count / 3 && f0Value < count / 3 * 2) {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = true;
                            } else {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = false;
                            }
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            TestSimpleT1UpdateGetOldPropertyByName(subStmt, f0Value, false);
                            TestSimpleT1GetLpmProperty(subStmt, f0Value);
                            TestSimpleT2NewVersionGetNewValue(subStmt, f0Value, newFieldIsNull[0]);
                            TestSimpleT3FullfieldsUpdateGetFailed(subStmt, f0Value);
                            break;
                        }
                        default: {
                            AW_FUN_Log(LOG_ERROR, "invalid eventType %d  line:%d\r\n",
                                info->eventType, __LINE__);
                            break;
                        }
                    }
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_ERROR, "invalid eventMsgType: %d eventType:%d line:%d\r\n",
                        info->msgType, info->eventType, __LINE__);
                    break;
                }
            }
        }
        userData1->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                userData1->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                userData1->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                userData1->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_INSERT: {
                userData1->mergeInsertNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_UPDATE: {
                userData1->mergeUpdateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                userData1->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                userData1->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                userData1->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                userData1->agedNum++;
                break;
            }
            default: {
                break;
            }
        }
    }
}

int CheckAccountStatus(GmcStmtT *stmt, const char *labelName)
{
    int ret = 0;
    GmcCheckInfoT *checkInfo = NULL;
    ret = GmcGetCheckInfo(stmt, labelName, GMC_FULL_TABLE, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    GmcCheckStatusE checkStatus;
    ret = GmcGetCheckStatus(checkInfo, &checkStatus);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(checkStatus, GMC_CHECK_STATUS_NORMAL);
    return ret;
}

void TestSimpleT1InsertOrReplaceBatch(GmcConnT *conn, GmcStmtT *stmt, char *labelName, int64_t startValue,
                                      int64_t endValue, uint32_t schemaVersion, GmcOperationTypeE operationType,
                                      bool isDefaultValue = true)
{
    int ret;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    unsigned int opNum = endValue - startValue;

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = startValue; i < endValue; i++) {
        TestSimpleT1SetPk(stmt, i);
        TestSimpleT1OldVersionSetProperty(stmt, i, isDefaultValue);
        if (schemaVersion == 0) {
            TestSimpleT1NewFieldSetFailed(stmt, i);
        } else if (schemaVersion == 1) {
            TestSimpleT1NewFieldSetOk(stmt, i);
        } else if (schemaVersion == 2) {
            TestSimpleT2NewFieldSetOk(stmt, i);
        } else if (schemaVersion == 3) {
            TestSimpleT3NewFieldSetOk(stmt, i);
        }

        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(opNum, totalNum);
    AW_MACRO_EXPECT_EQ_INT(opNum, successNum);
    AW_FUN_Log(LOG_INFO, "successNum: %d\n", successNum);
    AW_FUN_Log(LOG_INFO, "totalNum: %d\n", totalNum);
    GmcBatchDestroy(batch);
}

void TestSimpleT1MergeOrUpdateBatch(GmcConnT *conn, GmcStmtT *stmt, char *labelName, int64_t startValue,
                                    int64_t endValue, uint32_t schemaVersion, GmcOperationTypeE operationType,
                                    bool isDefaultValue = true, int64_t updateValue = 0)
{
    int ret;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    unsigned int opNum = endValue - startValue;

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    if (operationType == GMC_OPERATION_MERGE) {
        for (int i = startValue; i < endValue; i++) {
            TestSimpleT1PkIndexSet(stmt, i);
            TestSimpleT1UpdateSetProperty(stmt, i, isDefaultValue);

            if (schemaVersion == 0) {
                TestSimpleT1NewFieldSetFailed(stmt, i);
            } else if (schemaVersion == 1) {
                TestSimpleT1NewFieldSetOk(stmt, i + updateValue);
            } else if (schemaVersion == 2) {
                TestSimpleT2NewFieldSetOk(stmt, i + updateValue);
            } else if (schemaVersion == 3) {
                TestSimpleT3NewFieldSetOk(stmt, i + updateValue);
            }

            ret = GmcBatchAddDML(batch, stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 批处理提交
        ret = GmcBatchExecute(batch, &batchRet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(opNum, totalNum);
        AW_MACRO_EXPECT_EQ_INT(opNum, successNum);
        AW_FUN_Log(LOG_INFO, "successNum: %d\n", successNum);
        AW_FUN_Log(LOG_INFO, "totalNum: %d\n", totalNum);
        GmcBatchDestroy(batch);
    } else {
            for (int i = startValue; i < endValue; i++) {
            TestSimpleT1HashclusterIndexSet(stmt, i);
            TestSimpleT1UpdateSetProperty(stmt, i, isDefaultValue);
            if (schemaVersion == 0) {
                TestSimpleT1NewFieldSetFailed(stmt, i);
            } else if (schemaVersion == 1) {
                TestSimpleT1NewFieldSetOk(stmt, i + updateValue);
            } else if (schemaVersion == 2) {
                TestSimpleT2NewFieldSetOk(stmt, i + updateValue);
            } else if (schemaVersion == 3) {
                TestSimpleT3NewFieldSetOk(stmt, i + updateValue);
            }

            ret = GmcBatchAddDML(batch, stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 批处理提交
        ret = GmcBatchExecute(batch, &batchRet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(opNum, totalNum);
        AW_MACRO_EXPECT_EQ_INT(opNum, successNum);
        AW_FUN_Log(LOG_INFO, "successNum: %d\n", successNum);
        AW_FUN_Log(LOG_INFO, "totalNum: %d\n", totalNum);
        GmcBatchDestroy(batch);
    }
}

void TestSimpleT1PkScanAfterUpdate(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue,
                                   uint32_t schemaVersion, bool isDefaultValue = true, int64_t updateValue = 0)
{
    bool isFinish = true;
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = startValue; i < endValue; i++) {
        TestSimpleT1PkIndexSet(stmt, i);
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        TestSimpleT1UpdateGetOldPropertyByName(stmt, i, isDefaultValue);
        TestSimpleT1GetLpmProperty(stmt, i);
        if (schemaVersion == 0) {
            TestSimpleT1NewVersionGetOldValue(stmt, i + updateValue);
        } else if (schemaVersion == 1) {
            TestSimpleT1NewVersionGetNewValue(stmt, i + updateValue);
        } else if (schemaVersion == 2) {
            TestSimpleT2NewVersionGetNewValue(stmt, i + updateValue);
        } else if (schemaVersion == 3) {
            TestSimpleT3NewVersionGetNewValue(stmt, i + updateValue);
        }
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(true, isFinish);
    }
}

void TestSimpleT1LocalhashScanAfterUpdate(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue,
                                          uint32_t schemaVersion, bool isDefaultValue = true, int64_t updateValue = 0)
{
    bool isFinish = true;
    int64_t f0Value;
    bool isNull = 0;
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = startValue; i < endValue; i++) {
        TestSimpleT1LocalhashIndexSet(stmt, i);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSimpleT1UpdateGetOldPropertyByName(stmt, i, isDefaultValue);
        TestSimpleT1GetLpmProperty(stmt, i);
        if (schemaVersion == 0) {
        } else if (schemaVersion == 1) {
            TestSimpleT1NewVersionGetNewValue(stmt, i + updateValue);
        } else if (schemaVersion == 2) {
            TestSimpleT2NewVersionGetNewValue(stmt, i + updateValue);
        } else if (schemaVersion == 3) {
            TestSimpleT3NewVersionGetNewValue(stmt, i + updateValue);
        }
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(true, isFinish);
    }
}

int TestSimpleT1LpmScanAfterUpdate(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue,
                                   uint32_t schemaVersion, bool isDefaultValue = true, int64_t updateValue = 0)
{
    bool isFinish = true;
    int64_t f0Value;
    bool isNull = 0;
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = startValue; i < endValue; i++) {
        TestSimpleT1LpmIndexSet(stmt, i);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSimpleT1UpdateGetOldPropertyByName(stmt, i, isDefaultValue);
        TestSimpleT1GetLpmProperty(stmt, i);
        if (schemaVersion == 0) {
        } else if (schemaVersion == 1) {
            TestSimpleT1NewVersionGetNewValue(stmt, i + updateValue);
        } else if (schemaVersion == 2) {
            TestSimpleT2NewVersionGetNewValue(stmt, i + updateValue);
        } else if (schemaVersion == 3) {
            TestSimpleT3NewVersionGetNewValue(stmt, i + updateValue);
        }
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(true, isFinish);
    }
    return 0;
}

/*-------------------------------------特殊复杂表-----------------------------------*/
typedef struct TagSpeciallabelCfg {
    int32_t startVal;       // 主键或其他非成员索引的起始值
    uint32_t count;         // 主键或其他非成员索引的数量
    int32_t coefficient;    // 字段值生成系数, 通过startVal和coefficient组合生成不同的整形和浮点型字段值
    int32_t expAffectRows;    // 预期的affectRows
    int32_t threadId;    // 线程Id
    uint16_t t1VCount;
    uint16_t t2VCount;
    uint32_t schemaVersion;
    GmcOperationTypeE optType;
    bool fieldIsNull[8];
} GtSpeciallabelCfgT;

#define STRING_LEN   16
#define BYTES_LEN    256
#define STRING2_LEN   (13 * 1024)
#define STRING3_LEN   (32 * 1024)
char *g_labelName2 = (char *)"specialLabel";

void GtSpeciallabel2GetNode(GmcStmtT *stmt, GmcNodeT **root, GmcNodeT **t1V)
{
    GmcNodeT *Root, *t1;
    int32_t ret = GmcGetRootNode(stmt, &Root);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetChild(Root, "T1V", &t1);
    if (ret == GMERR_NO_DATA) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
    } else {
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    *root = Root;
    *t1V = t1;
}

void TestSpecialT2UpdateSetOldProperty(GmcNodeT *node, int64_t i, char *bytesValue, bool isDefaultValue = true)
{
    int ret = 0;
    int64_t f0Value = i;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    uint32_t f3Value = i;
    int16_t f4Value = i % 32768;
    uint16_t f5Value = i% 65536;
    uint64_t f7Value = i;
    uint8_t f13Value = i & 0xf;
    ret = GmcNodeSetPropertyByName(node, (char *)"F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F4", GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F5", GMC_DATATYPE_UINT16, &f5Value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F7", GMC_DATATYPE_TIME, &f7Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint8_t fixedValue[SIMPLE_LABEL_FIXED_SIZE] = {0};
    uint8_t f9Value;
    uint16_t f10Value;
    if (!isDefaultValue) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = j;
        }
        f9Value = i % 31;
        f10Value = i % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = 0xff;
        }
        f9Value = 0x1f;
        f10Value = 0x3ff;
    }
    ret = GmcNodeSetPropertyByName(node, (char *)"F8", GMC_DATATYPE_FIXED, fixedValue, SIMPLE_LABEL_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F9", GMC_DATATYPE_BITFIELD8, &f9Value, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F10", GMC_DATATYPE_BITFIELD16, &f10Value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F13", GMC_DATATYPE_BITFIELD8, &f13Value, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint8_t f14[BYTES_LEN] = {0};
    (void)snprintf((char *)f14, BYTES_LEN, "%s", bytesValue);
    ret = GmcNodeSetPropertyByName(node, "F14", GMC_DATATYPE_BYTES, f14, strlen(bytesValue));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSpecialT2UpdateSetNewProperty(GmcNodeT *node, char *bytesValue, char *stringValue)
{
    uint8_t f15[BYTES_LEN] = {0};
    (void)snprintf((char *)f15, BYTES_LEN, "%s", bytesValue);
    int32_t ret = GmcNodeSetPropertyByName(node, "F15", GMC_DATATYPE_BYTES, f15, strlen(bytesValue));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint8_t f16[STRING2_LEN] = {0};
    (void)snprintf((char *)f16, STRING2_LEN, "%s", stringValue);
    ret = GmcNodeSetPropertyByName(node, "F16", GMC_DATATYPE_STRING, f16, strlen(stringValue));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSpecialT3UpdateSetNewProperty(GmcNodeT *node, int64_t value, char *bytesValue, char *stringValue)
{
    uint8_t f15[BYTES_LEN] = {0};
    (void)snprintf((char *)f15, BYTES_LEN, "%s", bytesValue);
    int32_t ret = GmcNodeSetPropertyByName(node, "F15", GMC_DATATYPE_BYTES, f15, strlen(bytesValue));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint8_t f16[STRING2_LEN] = {0};
    (void)snprintf((char *)f16, STRING2_LEN, "%s", stringValue);
    ret = GmcNodeSetPropertyByName(node, "F16", GMC_DATATYPE_STRING, f16, strlen(stringValue));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char *stringTest = (char *)malloc(STRING3_LEN);
    if (stringTest == NULL) {
        AW_FUN_Log(LOG_ERROR, "stringTest is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)stringTest, STRING3_LEN, "s%032766d", value);
    ret = GmcNodeSetPropertyByName(node, "F17", GMC_DATATYPE_STRING, stringTest, strlen(stringTest));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "F18", GMC_DATATYPE_STRING, stringTest, strlen(stringTest));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "F19", GMC_DATATYPE_STRING, stringTest, strlen(stringTest));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "F20", GMC_DATATYPE_STRING, stringTest, strlen(stringTest));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "F21", GMC_DATATYPE_BYTES, stringTest, strlen(stringTest));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(stringTest);
}


void TestSpecialT3UpdateSetMidProperty(GmcNodeT *node, char *bytesValue, char *stringValue)
{
    uint8_t f15[BYTES_LEN] = {0};
    (void)snprintf((char *)f15, BYTES_LEN, "%s", bytesValue);
    int32_t ret = GmcNodeSetPropertyByName(node, "F15", GMC_DATATYPE_BYTES, f15, strlen(bytesValue));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint8_t f16[STRING2_LEN] = {0};
    (void)snprintf((char *)f16, STRING2_LEN, "%s", stringValue);
    ret = GmcNodeSetPropertyByName(node, "F16", GMC_DATATYPE_STRING, f16, strlen(stringValue));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSpecialT3UpdateSetNewestProperty(GmcNodeT *node, int64_t value)
{
    char *stringTest = (char *)malloc(STRING3_LEN);
    if (stringTest == NULL) {
        AW_FUN_Log(LOG_ERROR, "stringTest is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)stringTest, STRING3_LEN, "s%032766d", value);
    int32_t ret = GmcNodeSetPropertyByName(node, "F17", GMC_DATATYPE_STRING, stringTest, strlen(stringTest));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "F18", GMC_DATATYPE_STRING, stringTest, strlen(stringTest));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "F19", GMC_DATATYPE_STRING, stringTest, strlen(stringTest));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "F20", GMC_DATATYPE_STRING, stringTest, strlen(stringTest));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "F21", GMC_DATATYPE_BYTES, stringTest, strlen(stringTest));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(stringTest);
}

void TestSpecialT3UpdateSetMidPropertyFailed(GmcNodeT *node, char *bytesValue, char *stringValue)
{
    uint8_t f15[BYTES_LEN] = {0};
    (void)snprintf((char *)f15, BYTES_LEN, "%s", bytesValue);
    int32_t ret = GmcNodeSetPropertyByName(node, "F15", GMC_DATATYPE_BYTES, f15, strlen(bytesValue));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    uint8_t f16[STRING2_LEN] = {0};
    (void)snprintf((char *)f16, STRING2_LEN, "%s", stringValue);
    ret = GmcNodeSetPropertyByName(node, "F16", GMC_DATATYPE_STRING, f16, strlen(stringValue));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
}

void TestSpecialT3UpdateSetNewestPropertyFailed(GmcNodeT *node)
{
    char *stringTest = (char *)"test";
    int32_t ret = GmcNodeSetPropertyByName(node, "F17", GMC_DATATYPE_STRING, stringTest, strlen(stringTest));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeSetPropertyByName(node, "F18", GMC_DATATYPE_STRING, stringTest, strlen(stringTest));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeSetPropertyByName(node, "F19", GMC_DATATYPE_STRING, stringTest, strlen(stringTest));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeSetPropertyByName(node, "F20", GMC_DATATYPE_STRING, stringTest, strlen(stringTest));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeSetPropertyByName(node, "F21", GMC_DATATYPE_BYTES, stringTest, strlen(stringTest));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
}


void TestSpecialT3UpdateGetOldPropertyByName(GmcNodeT *node, int64_t i, char *bytesValue, bool isDefaultValue = true)
{
    int ret = 0;
    bool isNull = 0;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    int16_t f4Value = i % 32768;
    uint16_t f5Value = i % 65536;
    uint64_t f7Value = i;

    uint64_t f1Value2 = i;
    int32_t f2Value2 = i;
    int16_t f4Value2 = i % 32768;
    uint16_t f5Value2 = i % 65536;
    uint64_t f7Value2 = i;

    uint8_t fixedValue[SIMPLE_LABEL_FIXED_SIZE] = {0};
    uint8_t f9Value = 0;
    uint16_t f10Value = 0;
    uint8_t fixedValueR[SIMPLE_LABEL_FIXED_SIZE] = {0};
    uint8_t f9ValueR = 0;
    uint16_t f10ValueR = 0;
    uint8_t f13Value = i & 0xf;
    uint8_t f13ValueR = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F1", &f1Value2, sizeof(uint64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f1Value, f1Value2);

    ret = GmcNodeGetPropertyByName(node, (char *)"F2", &f2Value2, sizeof(int32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f2Value, f2Value2);

    ret = GmcNodeGetPropertyByName(node, (char *)"F4", &f4Value2, sizeof(int16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f4Value, f4Value2);

    ret = GmcNodeGetPropertyByName(node, (char *)"F5", &f5Value2, sizeof(uint16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f5Value, f5Value2);

    if (!isDefaultValue) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = j;
        }
        f9Value = i % 31;
        f10Value = i % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = 0xff;
        }
        f9Value = 0x1f;
        f10Value = 0x3ff;
    }
    ret = GmcNodeGetPropertyByName(node, (char *)"F8", fixedValueR, SIMPLE_LABEL_FIXED_SIZE, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = memcmp(fixedValue, fixedValueR, SIMPLE_LABEL_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeGetPropertyByName(node, (char *)"F9", &f9ValueR, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(f9Value, f9ValueR);
    ret = GmcNodeGetPropertyByName(node, (char *)"F10", &f10ValueR, sizeof(uint16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(f10Value, f10ValueR);
    ret = GmcNodeGetPropertyByName(node, (char *)"F13", &f13ValueR, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(f13Value, f13ValueR);
    ret = queryNodePropertyAndCompare(node, (char *)"F14", GMC_DATATYPE_BYTES, bytesValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSpecialT3GetLpmProperty(GmcNodeT *node, int64_t i)
{
    int ret = 0;
    bool isNull = false;
    uint32_t vrid = 0;
    uint32_t vrfIndex = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    uint32_t vrid2 = 0;
    uint32_t vrfIndex2 = 0;
    uint32_t destIpAddr2 = 0;
    uint8_t maskLen2 = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F3", &vrid2, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(vrid, vrid2);
    ret = GmcNodeGetPropertyByName(node, (char *)"F11", &vrfIndex2, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(vrfIndex, vrfIndex2);

    if (i <= MAX_MASK_LEN_16) {
        destIpAddr = ((i + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if (i > MAX_MASK_LEN_16 && i <= MAX_MASK_LEN_24) {
        destIpAddr = ((i + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((i + 2));
        maskLen = ((32) & 0xff);
    }
    ret = GmcNodeGetPropertyByName(node, (char *)"F12", &destIpAddr2, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(destIpAddr, destIpAddr2);
    ret = GmcNodeGetPropertyByName(node, (char *)"F6", &maskLen2, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(maskLen, maskLen2);
}

void TestSpecialT3MidVersionGetNewField(GmcNodeT *node, char *bytesValue, char *stringValue, bool fieldIsNull)
{
    if (!fieldIsNull) {
    int32_t ret = queryNodePropertyAndCompare(node, (char *)"F15", GMC_DATATYPE_BYTES, bytesValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, (char *)"F16", GMC_DATATYPE_STRING, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    } else {
        int32_t ret = queryNodePropertyAndCompare(node, (char *)"F15", GMC_DATATYPE_BYTES, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, (char *)"F16", GMC_DATATYPE_STRING, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestSpecialT3MidVersionGetNewFieldFailed(GmcNodeT *node, char *bytesValue, char *stringValue)
{
    int32_t ret = 0;
    bool isNull;
    uint32_t sizeValue = 0;
    char bytesV[BYTES_LEN] = {0};
    char stringV[BYTES_LEN] = {0};
    ret = GmcNodeGetPropertySizeByName(node, "F15", &sizeValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeGetPropertySizeByName(node, "F16", &sizeValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeGetPropertyByName(node, "F15", bytesV, strlen(bytesValue), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeGetPropertyByName(node, "F16", stringV, strlen(stringValue), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
}

void TestSpecialT3MostNewVersionGetNewFieldFailed(GmcNodeT *node, int64_t value)
{
    int32_t ret = 0;
    bool isNull;
    uint32_t sizeValue = 0;
    char *stringTest = (char *)malloc(STRING3_LEN);
    if (stringTest == NULL) {
        AW_FUN_Log(LOG_ERROR, "stringTest is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }

    ret = GmcNodeGetPropertySizeByName(node, "F17", &sizeValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeGetPropertySizeByName(node, "F18", &sizeValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeGetPropertySizeByName(node, "F21", &sizeValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeGetPropertyByName(node, "F17", stringTest, STRING3_LEN, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeGetPropertyByName(node, "F18", stringTest, STRING3_LEN, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeGetPropertyByName(node, "F19", stringTest, STRING3_LEN, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeGetPropertyByName(node, "F20", stringTest, STRING3_LEN, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeGetPropertyByName(node, "F21", stringTest, STRING3_LEN, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    free(stringTest);
}

void TestSpecialT3MostNewVersionGetNewField(GmcNodeT *node, int64_t value, bool fieldIsNull)
{
    int64_t valueWrite = value;
    char *stringTest = (char *)malloc(STRING3_LEN);
    if (stringTest == NULL) {
        AW_FUN_Log(LOG_ERROR, "stringTest is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)stringTest, STRING3_LEN, "s%032766d", valueWrite);
    if (!fieldIsNull) {
        int32_t ret = queryNodePropertyAndCompare(node, (char *)"F17", GMC_DATATYPE_STRING, stringTest);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, (char *)"F18", GMC_DATATYPE_STRING, stringTest);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, (char *)"F19", GMC_DATATYPE_STRING, stringTest);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, (char *)"F20", GMC_DATATYPE_STRING, stringTest);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, (char *)"F21", GMC_DATATYPE_BYTES, stringTest);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    } else {
        int32_t ret = queryNodePropertyAndCompare(node, (char *)"F17", GMC_DATATYPE_STRING, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, (char *)"F18", GMC_DATATYPE_STRING, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, (char *)"F19", GMC_DATATYPE_STRING, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, (char *)"F20", GMC_DATATYPE_STRING, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, (char *)"F21", GMC_DATATYPE_BYTES, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    free(stringTest);
}

void GtSpeciallabel3GeneralComparePropertyVector(GmcNodeT *node, int64_t value, char *stringValue)
{
    uint32_t v1Value = value;
    int32_t ret = queryNodePropertyAndCompare(node, (char *)"V1", GMC_DATATYPE_UINT32, &v1Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, (char *)"V2", GMC_DATATYPE_UINT32, &v1Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, (char *)"V4", GMC_DATATYPE_STRING, stringValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t v3Bits[2] = {0x55, 0x55};
    GmcBitMapT v3 = {0};
    v3.beginPos = 0;
    v3.endPos = 8 - 1;
    v3.bits = v3Bits;
    ret = queryNodePropertyAndCompare(node, (char *)"V3", GMC_DATATYPE_BITMAP, &v3Bits);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void GtSpeciallabel3GeneralGetVector(
    GmcNodeT *node, int64_t index, char *stringValue, uint16_t t1Count, uint16_t t2Count)
{
    int ret = 0, i = 0;
    GmcNodeT *t2V = NULL;
    for (i = 0; i < t1Count; ++i) {
        ret = GmcNodeGetElementByIndex(node, i, &node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GtSpeciallabel3GeneralComparePropertyVector(node, index, stringValue);
        ret = GmcNodeGetChild(node, "T2V", &t2V);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (uint32_t k = 0; k < t2Count; k++) {
            ret = GmcNodeGetElementByIndex(t2V, k, &t2V);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            GtSpeciallabel3GeneralComparePropertyVector(t2V, index, stringValue);
        }
    }
}


void TestSpecialT2PkScan(GmcStmtT *stmt, int64_t startValue, int64_t endValue,
                         uint32_t schemaVersion, char *bytesValue, char *stringValue)
{
    bool isFinish = true;
    GmcNodeT *root = NULL, *t1Node = NULL;
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName2, schemaVersion, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = startValue; i < endValue; i++) {
        TestSimpleT1PkIndexSet(stmt, i);
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GtSpeciallabel2GetNode(stmt, &root, &t1Node);
        ret = queryNodePropertyAndCompare(root, (char *)"F15", GMC_DATATYPE_BYTES, bytesValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(root, (char *)"F16", GMC_DATATYPE_STRING, stringValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(true, isFinish);
    }
}


void SubSpecialTCallBackWithNewVersion(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    char labelName[128] = {0};
    unsigned int labelNameLen = 0;
    uint32_t size;
    int count = 300;
#ifdef ENV_RTOSV2X
    count = 30;
#endif
    int updateValue = 300;
    char keyName[128] = {0};
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    SnUserDataT *userData1 = (SnUserDataT *)userData;
    bool eof = false;
    GmcNodeT *root = NULL, *t1V = NULL;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }
        for (int i = 0; i < info->labelCount; i++) {
            (void)memset(labelName, 0, sizeof(labelName));
            labelNameLen = sizeof(labelName);
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);
            switch (info->msgType) {
                case 5: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_DELETE: {
                            // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            bool newFieldIsNull[2] = {true};
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtSpeciallabel2GetNode(subStmt, &root, &t1V);
                            // 获取查询到的数据
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtSpeciallabel2GetNode(subStmt, &root, &t1V);
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value >= 0 && f0Value < count / 3) {
                                newFieldIsNull[0] = true;
                                newFieldIsNull[1] = true;
                            } else if (f0Value >= count / 3 && f0Value < count / 3 * 2) {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = true;
                            } else {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = false;
                            }
                            TestSpecialT3UpdateGetOldPropertyByName(root, f0Value, bytesValue, true);
                            TestSpecialT3GetLpmProperty(root, f0Value);
                            GtSpeciallabel3GeneralGetVector(t1V, f0Value, stringValue, 3, 3);
                            TestSpecialT3MidVersionGetNewField(root, bytesValue, stringValue, newFieldIsNull[0]);
                            TestSpecialT3MostNewVersionGetNewField(root, f0Value, newFieldIsNull[1]);
                            break;
                        }
                        default: {
                            AW_FUN_Log(LOG_ERROR, "invalid eventType:%d  %d\r\n", info->eventType,
                                __LINE__);
                            break;
                        }
                    }
                    break;
                }
                case 6: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_INSERT: {
                            // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            bool newFieldIsNull[2] = {true};
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtSpeciallabel2GetNode(subStmt, &root, &t1V);
                            // 获取查询到的数据
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtSpeciallabel2GetNode(subStmt, &root, &t1V);
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value >= 0 && f0Value < count / 3) {
                                newFieldIsNull[0] = true;
                                newFieldIsNull[1] = true;
                            } else if (f0Value >= count / 3 && f0Value < count / 3 * 2) {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = true;
                            } else {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = false;
                            }
                            TestSpecialT3UpdateGetOldPropertyByName(root, f0Value, bytesValue, true);
                            TestSpecialT3GetLpmProperty(root, f0Value);
                            GtSpeciallabel3GeneralGetVector(t1V, f0Value, stringValue, 3, 3);
                            TestSpecialT3MidVersionGetNewField(root, bytesValue, stringValue, newFieldIsNull[0]);
                            TestSpecialT3MostNewVersionGetNewField(root, f0Value, newFieldIsNull[1]);
                            break;
                        }
                        default: {
                            AW_FUN_Log(LOG_ERROR, "invalid eventType %d  line:%d\r\n",
                                info->eventType, __LINE__);
                            break;
                        }
                    }
                    break;
                }
                case 7: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_UPDATE: {
                           // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            bool newFieldIsNull[2] = {true};
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtSpeciallabel2GetNode(subStmt, &root, &t1V);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtSpeciallabel2GetNode(subStmt, &root, &t1V);
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            newFieldIsNull[0] = false;
                            newFieldIsNull[1] = false;
                            TestSpecialT3UpdateGetOldPropertyByName(root, f0Value + updateValue, bytesValue, true);
                            TestSpecialT3GetLpmProperty(root, f0Value);
                            GtSpeciallabel3GeneralGetVector(t1V, f0Value, stringValue, 3, 3);
                            TestSpecialT3MidVersionGetNewField(root, bytesValue, stringValue, newFieldIsNull[0]);
                            TestSpecialT3MostNewVersionGetNewField(root, f0Value + updateValue, newFieldIsNull[1]);
                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtSpeciallabel2GetNode(subStmt, &root, &t1V);
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value >= 0 && f0Value < count / 3) {
                                newFieldIsNull[0] = true;
                                newFieldIsNull[1] = true;
                            } else if (f0Value >= count / 3 && f0Value < count / 3 * 2) {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = true;
                            } else {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = false;
                            }
                            TestSpecialT3UpdateGetOldPropertyByName(root, f0Value, bytesValue, false);
                            TestSpecialT3GetLpmProperty(root, f0Value);
                            GtSpeciallabel3GeneralGetVector(t1V, f0Value, stringValue, 3, 3);
                            TestSpecialT3MidVersionGetNewField(root, bytesValue, stringValue, newFieldIsNull[0]);
                            TestSpecialT3MostNewVersionGetNewField(root, f0Value, newFieldIsNull[1]);
                            break;
                        }
                        case GMC_SUB_EVENT_REPLACE: {
                            // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            bool newFieldIsNull[2] = {true};
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtSpeciallabel2GetNode(subStmt, &root, &t1V);
                            // 获取查询到的数据
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtSpeciallabel2GetNode(subStmt, &root, &t1V);
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value >= 0 && f0Value < count / 3) {
                                newFieldIsNull[0] = true;
                                newFieldIsNull[1] = true;
                            } else if (f0Value >= count / 3 && f0Value < count / 3 * 2) {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = true;
                            } else {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = false;
                            }
                            TestSpecialT3UpdateGetOldPropertyByName(root, f0Value, bytesValue, true);
                            TestSpecialT3GetLpmProperty(root, f0Value);
                            GtSpeciallabel3GeneralGetVector(t1V, f0Value, stringValue, 3, 3);
                            TestSpecialT3MidVersionGetNewField(root, bytesValue, stringValue, newFieldIsNull[0]);
                            TestSpecialT3MostNewVersionGetNewField(root, f0Value, newFieldIsNull[1]);
                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
                            break;
                        }
                        case GMC_SUB_EVENT_MERGE_INSERT: {
                            // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            bool newFieldIsNull[2] = {true};
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtSpeciallabel2GetNode(subStmt, &root, &t1V);
                            // 获取查询到的数据
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtSpeciallabel2GetNode(subStmt, &root, &t1V);
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value >= 0 && f0Value < count / 3) {
                                newFieldIsNull[0] = true;
                                newFieldIsNull[1] = true;
                            } else if (f0Value >= count / 3 && f0Value < count / 3 * 2) {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = true;
                            } else {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = false;
                            }
                            TestSpecialT3UpdateGetOldPropertyByName(root, f0Value, bytesValue, true);
                            TestSpecialT3GetLpmProperty(root, f0Value);
                            GtSpeciallabel3GeneralGetVector(t1V, f0Value, stringValue, 3, 3);
                            TestSpecialT3MidVersionGetNewField(root, bytesValue, stringValue, newFieldIsNull[0]);
                            TestSpecialT3MostNewVersionGetNewField(root, f0Value, newFieldIsNull[1]);
                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
                            break;
                        }
                        default: {
                            AW_FUN_Log(LOG_ERROR, "invalid eventType %d  line:%d\r\n",
                                info->eventType, __LINE__);
                            break;
                        }
                    }
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_ERROR, "invalid eventMsgType: %d eventType:%d line:%d\r\n",
                        info->msgType, info->eventType, __LINE__);
                    break;
                }
            }
        }
        userData1->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                userData1->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                userData1->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                userData1->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_INSERT: {
                userData1->mergeInsertNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                userData1->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                userData1->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                userData1->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                userData1->agedNum++;
                break;
            }
            default: {
                break;
            }
        }
    }
}

void SubSpecialTCallBackWithOldVersion(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    char labelName[128] = {0};
    unsigned int labelNameLen = 0;
    uint32_t size;
    int count = 300;
#ifdef ENV_RTOSV2X
    count = 30;
#endif
    int updateValue = 300;
    char keyName[128] = {0};
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    SnUserDataT *userData1 = (SnUserDataT *)userData;
    bool eof = false;
    GmcNodeT *root = NULL, *t1V = NULL;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }
        for (int i = 0; i < info->labelCount; i++) {
            (void)memset(labelName, 0, sizeof(labelName));
            labelNameLen = sizeof(labelName);
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);
            switch (info->msgType) {
                case 5: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_DELETE: {
                            // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtSpeciallabel2GetNode(subStmt, &root, &t1V);
                            // 获取查询到的数据
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtSpeciallabel2GetNode(subStmt, &root, &t1V);
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            TestSpecialT3UpdateGetOldPropertyByName(root, f0Value, bytesValue, false);
                            TestSpecialT3GetLpmProperty(root, f0Value);
                            GtSpeciallabel3GeneralGetVector(t1V, f0Value, stringValue, 3, 3);
                            TestSpecialT3MidVersionGetNewFieldFailed(root, bytesValue, stringValue);
                            TestSpecialT3MostNewVersionGetNewFieldFailed(root, f0Value);
                            break;
                        }
                        default: {
                            AW_FUN_Log(LOG_ERROR, "invalid eventType:%d  %d\r\n", info->eventType,
                                __LINE__);
                            break;
                        }
                    }
                    break;
                }
                case 6: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_INSERT: {
                            // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtSpeciallabel2GetNode(subStmt, &root, &t1V);
                            // 获取查询到的数据
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtSpeciallabel2GetNode(subStmt, &root, &t1V);
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            TestSpecialT3UpdateGetOldPropertyByName(root, f0Value, bytesValue, false);
                            TestSpecialT3GetLpmProperty(root, f0Value);
                            GtSpeciallabel3GeneralGetVector(t1V, f0Value, stringValue, 3, 3);
                            TestSpecialT3MidVersionGetNewFieldFailed(root, bytesValue, stringValue);
                            TestSpecialT3MostNewVersionGetNewFieldFailed(root, f0Value);
                            break;
                        }
                        default: {
                            AW_FUN_Log(LOG_ERROR, "invalid eventType %d  line:%d\r\n",
                                info->eventType, __LINE__);
                            break;
                        }
                    }
                    break;
                }
                case 7: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_UPDATE: {
                           // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            bool newFieldIsNull[2] = {true};
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtSpeciallabel2GetNode(subStmt, &root, &t1V);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtSpeciallabel2GetNode(subStmt, &root, &t1V);
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            newFieldIsNull[0] = false;
                            newFieldIsNull[1] = false;
                            TestSpecialT3UpdateGetOldPropertyByName(root, f0Value + updateValue, bytesValue, false);
                            TestSpecialT3GetLpmProperty(root, f0Value);
                            GtSpeciallabel3GeneralGetVector(t1V, f0Value, stringValue, 3, 3);
                            TestSpecialT3MidVersionGetNewField(root, bytesValue, stringValue, newFieldIsNull[0]);
                            TestSpecialT3MostNewVersionGetNewFieldFailed(root, f0Value);
                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtSpeciallabel2GetNode(subStmt, &root, &t1V);
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value >= 0 && f0Value < count / 3) {
                                newFieldIsNull[0] = true;
                                newFieldIsNull[1] = true;
                            } else if (f0Value >= count / 3 && f0Value < count / 3 * 2) {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = true;
                            } else {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = false;
                            }
                            TestSpecialT3UpdateGetOldPropertyByName(root, f0Value, bytesValue, true);
                            TestSpecialT3GetLpmProperty(root, f0Value);
                            GtSpeciallabel3GeneralGetVector(t1V, f0Value, stringValue, 3, 3);
                            TestSpecialT3MidVersionGetNewField(root, bytesValue, stringValue, newFieldIsNull[0]);
                            TestSpecialT3MostNewVersionGetNewFieldFailed(root, f0Value);
                            break;
                        }
                        case GMC_SUB_EVENT_REPLACE: {
                            // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            bool newFieldIsNull[2] = {true};
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtSpeciallabel2GetNode(subStmt, &root, &t1V);
                            // 获取查询到的数据
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtSpeciallabel2GetNode(subStmt, &root, &t1V);
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value >= 0 && f0Value < count / 3) {
                                newFieldIsNull[0] = true;
                                newFieldIsNull[1] = true;
                            } else if (f0Value >= count / 3 && f0Value < count / 3 * 2) {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = true;
                            } else {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = false;
                            }
                            TestSpecialT3UpdateGetOldPropertyByName(root, f0Value, bytesValue, false);
                            TestSpecialT3GetLpmProperty(root, f0Value);
                            GtSpeciallabel3GeneralGetVector(t1V, f0Value, stringValue, 3, 3);
                            TestSpecialT3MidVersionGetNewField(root, bytesValue, stringValue, newFieldIsNull[0]);
                            TestSpecialT3MostNewVersionGetNewFieldFailed(root, f0Value);
                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
                            break;
                        }
                        case GMC_SUB_EVENT_MERGE_INSERT: {
                            // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            bool newFieldIsNull[2] = {true};
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtSpeciallabel2GetNode(subStmt, &root, &t1V);
                            // 获取查询到的数据
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtSpeciallabel2GetNode(subStmt, &root, &t1V);
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value >= 0 && f0Value < count / 3) {
                                newFieldIsNull[0] = true;
                                newFieldIsNull[1] = true;
                            } else if (f0Value >= count / 3 && f0Value < count / 3 * 2) {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = true;
                            } else {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = false;
                            }
                            TestSpecialT3UpdateGetOldPropertyByName(root, f0Value, bytesValue, true);
                            TestSpecialT3GetLpmProperty(root, f0Value);
                            GtSpeciallabel3GeneralGetVector(t1V, f0Value, stringValue, 3, 3);
                            TestSpecialT3MidVersionGetNewField(root, bytesValue, stringValue, newFieldIsNull[0]);
                            TestSpecialT3MostNewVersionGetNewFieldFailed(root, f0Value);
                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
                            break;
                        }
                        case GMC_SUB_EVENT_MERGE_UPDATE: {
                            // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            bool newFieldIsNull[2] = {true};
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtSpeciallabel2GetNode(subStmt, &root, &t1V);
                            // 获取查询到的数据
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtSpeciallabel2GetNode(subStmt, &root, &t1V);
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            newFieldIsNull[0] = false;
                            newFieldIsNull[1] = false;
                            TestSpecialT3UpdateGetOldPropertyByName(root, f0Value + updateValue, bytesValue, false);
                            TestSpecialT3GetLpmProperty(root, f0Value);
                            GtSpeciallabel3GeneralGetVector(t1V, f0Value, stringValue, 3, 3);
                            TestSpecialT3MidVersionGetNewField(root, bytesValue, stringValue, newFieldIsNull[0]);
                            TestSpecialT3MostNewVersionGetNewFieldFailed(root, f0Value);
                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtSpeciallabel2GetNode(subStmt, &root, &t1V);
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value >= 0 && f0Value < count / 3) {
                                newFieldIsNull[0] = true;
                                newFieldIsNull[1] = true;
                            } else if (f0Value >= count / 3 && f0Value < count / 3 * 2) {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = true;
                            } else {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = false;
                            }
                            TestSpecialT3UpdateGetOldPropertyByName(root, f0Value, bytesValue, true);
                            TestSpecialT3GetLpmProperty(root, f0Value);
                            GtSpeciallabel3GeneralGetVector(t1V, f0Value, stringValue, 3, 3);
                            TestSpecialT3MidVersionGetNewField(root, bytesValue, stringValue, newFieldIsNull[0]);
                            TestSpecialT3MostNewVersionGetNewFieldFailed(root, f0Value);
                            break;
                        }
                        default: {
                            AW_FUN_Log(LOG_ERROR, "invalid eventType %d  line:%d\r\n",
                                info->eventType, __LINE__);
                            break;
                        }
                    }
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_ERROR, "invalid eventMsgType: %d eventType:%d line:%d\r\n",
                        info->msgType, info->eventType, __LINE__);
                    break;
                }
            }
        }
        userData1->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                userData1->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                userData1->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                userData1->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_INSERT: {
                userData1->mergeInsertNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_UPDATE: {
                userData1->mergeUpdateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                userData1->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                userData1->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                userData1->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                userData1->agedNum++;
                break;
            }
            default: {
                break;
            }
        }
    }
}

int TestSpecialTWholeRead(GmcStmtT *stmt, GtSpeciallabelCfgT vertexCfg, char *bytesValue, char *stringValue,
                          bool isDefaultValue)
{
    int ret = 0;
    bool isFinish = false;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    uint32_t schemaVersion = vertexCfg.schemaVersion;
    uint32_t fetchNum = 0;
    GmcNodeT *root, *t1V;
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName2, schemaVersion, GMC_OPERATION_SCAN);
    AW_FUN_Log(LOG_INFO, "g_labelName2 = %s schemaVersion = %d", g_labelName2, schemaVersion);
    RETURN_IFERR(ret);
    ret = GmcExecute(stmt);
    RETURN_IFERR(ret);
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        fetchNum++;
        int64_t f0Value = 0;
        bool isNull = false;
        bool newFieldIsNull[2] = {true};
        GtSpeciallabel2GetNode(stmt, &root, &t1V);
        ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
        RETURN_IFERR(ret);
        if (f0Value >= 0 && f0Value < expAffectRows / 3) {
            newFieldIsNull[0] = true;
            newFieldIsNull[1] = true;
        } else if (f0Value >= (expAffectRows / 3) && f0Value < (expAffectRows / 3 * 2)) {
            newFieldIsNull[0] = false;
            newFieldIsNull[1] = true;
        } else {
            newFieldIsNull[0] = false;
            newFieldIsNull[1] = false;
        }
        TestSpecialT3UpdateGetOldPropertyByName(root, f0Value, bytesValue, isDefaultValue);
        TestSpecialT3GetLpmProperty(root, f0Value);
        GtSpeciallabel3GeneralGetVector(t1V, f0Value, stringValue, 3, 3);
        if (schemaVersion == 0) {
            TestSpecialT3MidVersionGetNewFieldFailed(root, bytesValue, stringValue);
            TestSpecialT3MostNewVersionGetNewFieldFailed(root, f0Value);
        } else if (schemaVersion == 1) {
            TestSpecialT3MidVersionGetNewField(root, bytesValue, stringValue, newFieldIsNull[0]);
            TestSpecialT3MostNewVersionGetNewFieldFailed(root, f0Value);
        } else if (schemaVersion == 2) {
            TestSpecialT3MidVersionGetNewField(root, bytesValue, stringValue, newFieldIsNull[0]);
            TestSpecialT3MostNewVersionGetNewField(root, f0Value, newFieldIsNull[1]);
        }
    }
    AW_MACRO_EXPECT_EQ_INT(expAffectRows, fetchNum);
    return GMERR_OK;
}


#endif
