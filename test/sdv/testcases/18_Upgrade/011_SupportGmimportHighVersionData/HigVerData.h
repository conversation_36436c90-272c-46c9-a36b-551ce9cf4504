/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: HigVerData.h
 * Description:
 * Author: yang<PERSON>wen ywx1060383
 * Create: 2023-08-03
 */

#ifndef HIG_VER_DATA_H
#define HIG_VER_DATA_H
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "t_datacom_lite.h"

/*-------------------------------------特殊复杂表-----------------------------------*/
#define SIMPLE_LABEL_FIXED_SIZE 9
#define MAX_MASK_LEN_16 1000
#define MAX_MASK_LEN_24 2501000
typedef struct TagSpeciallabelCfg {
    int32_t startVal;  // 主键或其他非成员索引的起始值
    uint32_t count;    // 主键或其他非成员索引的数量
    int32_t coefficient;  // 字段值生成系数, 通过startVal和coefficient组合生成不同的整形和浮点型字段值
    int32_t expAffectRows;  // 预期的affectRows
    int32_t threadId;       // 线程Id
    uint16_t t1VCount;
    uint16_t t2VCount;
    uint32_t schemaVersion;
    GmcOperationTypeE optType;
    bool fieldIsNull[8];
} GtSpeciallabelCfgT;

#pragma pack(1)
typedef struct TagSpeciallabelT2VVertex {
    uint32_t v1;
    uint32_t v2;
    uint8_t v3[2];
    uint16_t v4Len;
    uint8_t *v4;
} GtSpeciallabelT2VVertexT;
#pragma pack()

// 升级前
#pragma pack(1)
typedef struct TagSpeciallabelT1VVertex {
    uint32_t v1;
    uint32_t v2;
    uint8_t v3[2];
    uint16_t v4Len;
    uint8_t *v4;
    uint16_t t2VCount;
    GtSpeciallabelT2VVertexT *t2V;
} GtSpeciallabelT1VVertexT;
#pragma pack()

// 升级后
#pragma pack(1)
typedef struct TagSpeciallabelT1VVertex2 {
    uint32_t v1;
    uint32_t v2;
    uint8_t v3[2];
    uint16_t v4Len;
    uint8_t *v4;
    uint16_t t2VCount;
    GtSpeciallabelT2VVertexT *t2V;
    uint16_t v5Len;
    uint8_t *v5;
} GtSpeciallabelT1VVertexT2;
#pragma pack()

// 升级后
#pragma pack(1)
typedef struct TagSpeciallabelT3VVertex3 {
    uint32_t v1;
    uint16_t v2Len;
    uint8_t *v2;
} GtSpeciallabelT3VVertexT3;
#pragma pack()

// 升级前
#pragma pack(1)
typedef struct TagSpeciallabelVertex {
    int64_t f0;
    uint64_t f1;
    int32_t f2;
    uint32_t f3;
    int16_t f4;
    uint16_t f5;
    uint8_t f6;
    uint64_t f7;
    uint8_t f8[9];
    uint8_t f9 : 5;
    uint8_t res1 : 3;
    uint16_t f10 : 10;
    uint16_t res2 : 6;
    uint32_t f11;
    uint32_t f12;
    uint8_t f13 : 4;
    uint8_t res3 : 4;
    uint16_t f14Len;
    uint8_t *f14;
    uint16_t t1VCount;
    GtSpeciallabelT1VVertexT *t1V;
} GtSpeciallabelVertexT;
#pragma pack()

// 升级后
#pragma pack(1)
typedef struct TagSpeciallabel2Vertex {
    int64_t f0;
    uint64_t f1;
    int32_t f2;
    uint32_t f3;
    int16_t f4;
    uint16_t f5;
    uint8_t f6;
    uint64_t f7;
    uint8_t f8[9];
    uint8_t f9 : 5;
    uint8_t res1 : 3;
    uint16_t f10 : 10;
    uint16_t res2 : 6;
    uint32_t f11;
    uint32_t f12;
    uint8_t f13 : 4;
    uint8_t res3 : 4;
    uint16_t f14Len;
    uint8_t *f14;
    uint16_t t1VCount;
    GtSpeciallabelT1VVertexT *t1V;
    uint16_t f15Len;
    uint8_t *f15;
    uint16_t f16Len;
    uint8_t *f16;
} GtSpeciallabel2VertexT;
#pragma pack()

// 升级后
#pragma pack(1)
typedef struct TagSpeciallabel2Vertex2 {
    int64_t f0;
    uint64_t f1;
    int32_t f2;
    uint32_t f3;
    int16_t f4;
    uint16_t f5;
    uint8_t f6;
    uint64_t f7;
    uint8_t f8[9];
    uint8_t f9 : 5;
    uint8_t res1 : 3;
    uint16_t f10 : 10;
    uint16_t res2 : 6;
    uint32_t f11;
    uint32_t f12;
    uint8_t f13 : 4;
    uint8_t res3 : 4;
    uint16_t f14Len;
    uint8_t *f14;
    uint16_t t1VCount;
    GtSpeciallabelT1VVertexT2 *t1V;
} GtSpeciallabel2VertexT2;
#pragma pack()

// 升级后
#pragma pack(1)
typedef struct TagSpeciallabel2Vertex3 {
    int64_t f0;
    uint64_t f1;
    int32_t f2;
    uint32_t f3;
    int16_t f4;
    uint16_t f5;
    uint8_t f6;
    uint64_t f7;
    uint8_t f8[9];
    uint8_t f9 : 5;
    uint8_t res1 : 3;
    uint16_t f10 : 10;
    uint16_t res2 : 6;
    uint32_t f11;
    uint32_t f12;
    uint8_t f13 : 4;
    uint8_t res3 : 4;
    uint16_t f14Len;
    uint8_t *f14;
    uint16_t t1VCount;
    GtSpeciallabelT1VVertexT *t1V;
    uint16_t t3VFlag;
    GtSpeciallabelT3VVertexT3 *t3V;
} GtSpeciallabel2VertexT3;
#pragma pack()
/*-------------------------------------特殊复杂表-----------------------------------*/

GmcConnT *g_conn;
GmcStmtT *g_stmt;

#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];
char g_configJson[128] = "{\"max_record_count\" : 10000}";
char g_dWay[64] = "sync";
char g_labelName[] = "specialLabel";

int TestUpdateVertexLabel(char *schemaPath, char *expectValue, char *labelName = NULL, char *uWay = (char *)"online",
    char *nsName = g_testNameSpace)
{
    char *schema = NULL;
    readJanssonFile(schemaPath, &schema);
    EXPECT_NE((void *)NULL, schema);
    free(schema);
    // gmddl工具升级表操作
    char cmd[512] = {0};
    int ret = 0;
    if (labelName) {
        (void)snprintf(
            cmd, 512, "%s/gmddl -c alter -t %s -f %s -u %s -ns %s", g_toolPath, labelName, schemaPath, uWay, nsName);
    } else {
        (void)snprintf(cmd, 512, "%s/gmddl -c alter -f %s -u %s -ns %s", g_toolPath, schemaPath, uWay, nsName);
    }
    AW_FUN_Log(LOG_INFO, "cmd: %s", cmd);
    ret = executeCommand(cmd, expectValue);
    if (ret != GMERR_OK) {
        system(cmd);
    }
    return ret;
}

int TestDownGradeVertexLabel(
    char *labelName, uint32_t schemaVersion, char *expectValue, char *dWay = g_dWay, char *nsName = g_testNameSpace)
{
    char cmd[512] = {0};
    int ret = 0;
    (void)snprintf(
        cmd, 512, "%s/gmddl -c alter -t %s -v %d -d %s -ns %s", g_toolPath, labelName, schemaVersion, dWay, nsName);
    ret = executeCommand(cmd, expectValue);
    if (ret != GMERR_OK) {
        system(cmd);
    }
    return ret;
}

int recordInsertVertex(GmcStmtT *stmt, char *labelName, int32_t insertValue, int version = 0)
{
    int ret = 0;
    if (version == 0) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, 0xFFFFFFFF, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    uint8_t value0 = insertValue;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT8, &value0, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int8_t value1 = insertValue;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint16_t value2 = insertValue;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT16, &value2, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int16_t value3 = insertValue;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT16, &value3, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t value4 = insertValue;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT32, &value4, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t value5 = insertValue;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_INT32, &value5, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t value6 = insertValue;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_UINT64, &value6, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t value7 = insertValue;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_INT64, &value7, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char value8 = insertValue;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_UCHAR, &value8, sizeof(char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char value9 = insertValue;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_CHAR, &value9, sizeof(char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    float value10 = insertValue;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_FLOAT, &value10, sizeof(float));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    double value11 = insertValue;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_DOUBLE, &value11, sizeof(double));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool value12 = false;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_BOOL, &value12, sizeof(bool));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t value13 = insertValue;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &value13, sizeof(value13));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char value14[] = "string";
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, value14, strlen(value14));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (version >= 1) {
        uint8_t value15 = insertValue;
        ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_UINT8, &value15, sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int8_t value16 = insertValue;
        ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_INT8, &value16, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint16_t value17 = insertValue;
        ret = GmcSetVertexProperty(stmt, "F17", GMC_DATATYPE_UINT16, &value17, sizeof(uint16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int16_t value18 = insertValue;
        ret = GmcSetVertexProperty(stmt, "F18", GMC_DATATYPE_INT16, &value18, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t value19 = insertValue;
        ret = GmcSetVertexProperty(stmt, "F19", GMC_DATATYPE_UINT32, &value19, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int32_t value20 = insertValue;
        ret = GmcSetVertexProperty(stmt, "F20", GMC_DATATYPE_INT32, &value20, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint64_t value21 = insertValue;
        ret = GmcSetVertexProperty(stmt, "F21", GMC_DATATYPE_UINT64, &value21, sizeof(uint64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int64_t value22 = insertValue;
        ret = GmcSetVertexProperty(stmt, "F22", GMC_DATATYPE_INT64, &value22, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char value23 = insertValue;
        ret = GmcSetVertexProperty(stmt, "F23", GMC_DATATYPE_UCHAR, &value23, sizeof(char));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char value24 = insertValue;
        ret = GmcSetVertexProperty(stmt, "F24", GMC_DATATYPE_CHAR, &value24, sizeof(char));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        float value25 = insertValue;
        ret = GmcSetVertexProperty(stmt, "F25", GMC_DATATYPE_FLOAT, &value25, sizeof(float));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        double value26 = insertValue;
        ret = GmcSetVertexProperty(stmt, "F26", GMC_DATATYPE_DOUBLE, &value26, sizeof(double));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool value27 = false;
        ret = GmcSetVertexProperty(stmt, "F27", GMC_DATATYPE_BOOL, &value27, sizeof(bool));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint64_t value28 = insertValue;
        ret = GmcSetVertexProperty(stmt, "F28", GMC_DATATYPE_TIME, &value28, sizeof(value28));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (version == 2) {
            ret = GmcSetVertexProperty(stmt, "F29", GMC_DATATYPE_INT64, &value22, sizeof(int64_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    ret = GmcExecute(stmt);
    return ret;
}

int recordInsertGeneralVertex(GmcStmtT *stmt, char *labelName, int32_t insertValue, int version = 0, int addField = 1)
{
    int ret = 0;
    if (version == 0) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, 0xFFFFFFFF, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    GmcNodeT *root, *T1, *T2, *T3, *U1, *U2;
    ret = GmcGetRootNode(stmt, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t value0 = insertValue;
    ret = GmcNodeSetPropertyByName(root, "F0", GMC_DATATYPE_UINT8, &value0, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int8_t value1 = insertValue;
    ret = GmcNodeSetPropertyByName(root, "F1", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint16_t value2 = insertValue;
    ret = GmcNodeSetPropertyByName(root, "F2", GMC_DATATYPE_UINT16, &value2, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int16_t value3 = insertValue;
    ret = GmcNodeSetPropertyByName(root, "F3", GMC_DATATYPE_INT16, &value3, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t value4 = insertValue;
    ret = GmcNodeSetPropertyByName(root, "F4", GMC_DATATYPE_UINT32, &value4, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t value5 = insertValue;
    ret = GmcNodeSetPropertyByName(root, "F5", GMC_DATATYPE_INT32, &value5, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t value6 = insertValue;
    ret = GmcNodeSetPropertyByName(root, "F6", GMC_DATATYPE_UINT64, &value6, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t value7 = insertValue;
    ret = GmcNodeSetPropertyByName(root, "F7", GMC_DATATYPE_INT64, &value7, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char value8 = insertValue;
    ret = GmcNodeSetPropertyByName(root, "F8", GMC_DATATYPE_UCHAR, &value8, sizeof(char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char value9 = insertValue;
    ret = GmcNodeSetPropertyByName(root, "F9", GMC_DATATYPE_CHAR, &value9, sizeof(char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    float value10 = insertValue;
    ret = GmcNodeSetPropertyByName(root, "F10", GMC_DATATYPE_FLOAT, &value10, sizeof(float));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    double value11 = insertValue;
    ret = GmcNodeSetPropertyByName(root, "F11", GMC_DATATYPE_DOUBLE, &value11, sizeof(double));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool value12 = false;
    ret = GmcNodeSetPropertyByName(root, "F12", GMC_DATATYPE_BOOL, &value12, sizeof(bool));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t value13 = insertValue;
    ret = GmcNodeSetPropertyByName(root, "F13", GMC_DATATYPE_TIME, &value13, sizeof(value13));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char value14[] = "string";
    ret = GmcNodeSetPropertyByName(root, "F14", GMC_DATATYPE_STRING, value14, strlen(value14));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeGetChild(root, "T1", &T1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeAppendElement(T1, &T1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T1, "A0", GMC_DATATYPE_UINT8, &value0, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T1, "A1", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T1, "A2", GMC_DATATYPE_UINT16, &value2, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T1, "A3", GMC_DATATYPE_INT16, &value3, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T1, "A4", GMC_DATATYPE_UINT32, &value4, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T1, "A5", GMC_DATATYPE_INT32, &value5, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T1, "A6", GMC_DATATYPE_UINT64, &value6, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T1, "A7", GMC_DATATYPE_INT64, &value7, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T1, "A8", GMC_DATATYPE_UCHAR, &value8, sizeof(char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T1, "A9", GMC_DATATYPE_CHAR, &value9, sizeof(char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T1, "A10", GMC_DATATYPE_FLOAT, &value10, sizeof(float));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T1, "A11", GMC_DATATYPE_DOUBLE, &value11, sizeof(double));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T1, "A12", GMC_DATATYPE_BOOL, &value12, sizeof(bool));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T1, "A13", GMC_DATATYPE_TIME, &value13, sizeof(value13));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T1, "A14", GMC_DATATYPE_STRING, value14, strlen(value14));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeGetChild(T1, "U1", &U1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeAppendElement(U1, &U1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(U1, "B0", GMC_DATATYPE_UINT8, &value0, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(U1, "B1", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(U1, "B2", GMC_DATATYPE_UINT16, &value2, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(U1, "B3", GMC_DATATYPE_INT16, &value3, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(U1, "B4", GMC_DATATYPE_UINT32, &value4, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(U1, "B5", GMC_DATATYPE_INT32, &value5, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(U1, "B6", GMC_DATATYPE_UINT64, &value6, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(U1, "B7", GMC_DATATYPE_INT64, &value7, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(U1, "B8", GMC_DATATYPE_UCHAR, &value8, sizeof(char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(U1, "B9", GMC_DATATYPE_CHAR, &value9, sizeof(char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(U1, "B10", GMC_DATATYPE_FLOAT, &value10, sizeof(float));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(U1, "B11", GMC_DATATYPE_DOUBLE, &value11, sizeof(double));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(U1, "B12", GMC_DATATYPE_BOOL, &value12, sizeof(bool));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(U1, "B13", GMC_DATATYPE_TIME, &value13, sizeof(value13));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(U1, "B14", GMC_DATATYPE_STRING, value14, strlen(value14));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeGetChild(root, "T2", &T2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeAppendElement(T2, &T2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T2, "C0", GMC_DATATYPE_UINT8, &value0, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T2, "C1", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T2, "C2", GMC_DATATYPE_UINT16, &value2, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T2, "C3", GMC_DATATYPE_INT16, &value3, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T2, "C4", GMC_DATATYPE_UINT32, &value4, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T2, "C5", GMC_DATATYPE_INT32, &value5, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T2, "C6", GMC_DATATYPE_UINT64, &value6, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T2, "C7", GMC_DATATYPE_INT64, &value7, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T2, "C8", GMC_DATATYPE_UCHAR, &value8, sizeof(char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T2, "C9", GMC_DATATYPE_CHAR, &value9, sizeof(char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T2, "C10", GMC_DATATYPE_FLOAT, &value10, sizeof(float));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T2, "C11", GMC_DATATYPE_DOUBLE, &value11, sizeof(double));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T2, "C12", GMC_DATATYPE_BOOL, &value12, sizeof(bool));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T2, "C13", GMC_DATATYPE_TIME, &value13, sizeof(value13));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(T2, "C14", GMC_DATATYPE_STRING, value14, strlen(value14));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeGetChild(T2, "U2", &U2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeAppendElement(U2, &U2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(U2, "D0", GMC_DATATYPE_UINT8, &value0, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(U2, "D1", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(U2, "D2", GMC_DATATYPE_UINT16, &value2, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(U2, "D3", GMC_DATATYPE_INT16, &value3, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(U2, "D4", GMC_DATATYPE_UINT32, &value4, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(U2, "D5", GMC_DATATYPE_INT32, &value5, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(U2, "D6", GMC_DATATYPE_UINT64, &value6, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(U2, "D7", GMC_DATATYPE_INT64, &value7, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(U2, "D8", GMC_DATATYPE_UCHAR, &value8, sizeof(char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(U2, "D9", GMC_DATATYPE_CHAR, &value9, sizeof(char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(U2, "D10", GMC_DATATYPE_FLOAT, &value10, sizeof(float));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(U2, "D11", GMC_DATATYPE_DOUBLE, &value11, sizeof(double));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(U2, "D12", GMC_DATATYPE_BOOL, &value12, sizeof(bool));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(U2, "D13", GMC_DATATYPE_TIME, &value13, sizeof(value13));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(U2, "D14", GMC_DATATYPE_STRING, value14, strlen(value14));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (addField == 1) {
        uint8_t value15 = insertValue;
        ret = GmcNodeSetPropertyByName(root, "F15", GMC_DATATYPE_UINT8, &value15, sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int8_t value16 = insertValue;
        ret = GmcNodeSetPropertyByName(root, "F16", GMC_DATATYPE_INT8, &value16, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint16_t value17 = insertValue;
        ret = GmcNodeSetPropertyByName(root, "F17", GMC_DATATYPE_UINT16, &value17, sizeof(uint16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int16_t value18 = insertValue;
        ret = GmcNodeSetPropertyByName(root, "F18", GMC_DATATYPE_INT16, &value18, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t value19 = insertValue;
        ret = GmcNodeSetPropertyByName(root, "F19", GMC_DATATYPE_UINT32, &value19, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int32_t value20 = insertValue;
        ret = GmcNodeSetPropertyByName(root, "F20", GMC_DATATYPE_INT32, &value20, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint64_t value21 = insertValue;
        ret = GmcNodeSetPropertyByName(root, "F21", GMC_DATATYPE_UINT64, &value21, sizeof(uint64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int64_t value22 = insertValue;
        ret = GmcNodeSetPropertyByName(root, "F22", GMC_DATATYPE_INT64, &value22, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char value23 = insertValue;
        ret = GmcNodeSetPropertyByName(root, "F23", GMC_DATATYPE_UCHAR, &value23, sizeof(char));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char value24 = insertValue;
        ret = GmcNodeSetPropertyByName(root, "F24", GMC_DATATYPE_CHAR, &value24, sizeof(char));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        float value25 = insertValue;
        ret = GmcNodeSetPropertyByName(root, "F25", GMC_DATATYPE_FLOAT, &value25, sizeof(float));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        double value26 = insertValue;
        ret = GmcNodeSetPropertyByName(root, "F26", GMC_DATATYPE_DOUBLE, &value26, sizeof(double));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool value27 = false;
        ret = GmcNodeSetPropertyByName(root, "F27", GMC_DATATYPE_BOOL, &value27, sizeof(bool));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint64_t value28 = insertValue;
        ret = GmcNodeSetPropertyByName(root, "F28", GMC_DATATYPE_TIME, &value28, sizeof(value28));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else if (addField == 2) {
        char value15[] = "string";
        ret = GmcNodeSetPropertyByName(root, "F15", GMC_DATATYPE_STRING, &value15, strlen(value15));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(root, "F16", GMC_DATATYPE_BYTES, &value15, strlen(value15));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else if (addField == 3) {
        ret = GmcNodeGetChild(root, "T3", &T3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T3, "E0", GMC_DATATYPE_UINT8, &value0, sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T3, "E1", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T3, "E2", GMC_DATATYPE_UINT16, &value2, sizeof(uint16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T3, "E3", GMC_DATATYPE_INT16, &value3, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T3, "E4", GMC_DATATYPE_UINT32, &value4, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T3, "E5", GMC_DATATYPE_INT32, &value5, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T3, "E6", GMC_DATATYPE_UINT64, &value6, sizeof(uint64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T3, "E7", GMC_DATATYPE_INT64, &value7, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T3, "E8", GMC_DATATYPE_UCHAR, &value8, sizeof(char));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T3, "E9", GMC_DATATYPE_CHAR, &value9, sizeof(char));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T3, "E10", GMC_DATATYPE_FLOAT, &value10, sizeof(float));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T3, "E11", GMC_DATATYPE_DOUBLE, &value11, sizeof(double));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T3, "E12", GMC_DATATYPE_BOOL, &value12, sizeof(bool));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T3, "E13", GMC_DATATYPE_TIME, &value13, sizeof(value13));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else if (addField == 4) {
        ret = GmcNodeSetPropertyByName(T1, "A17", GMC_DATATYPE_UINT8, &value0, sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T1, "A18", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T1, "A19", GMC_DATATYPE_UINT16, &value2, sizeof(uint16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T1, "A20", GMC_DATATYPE_INT16, &value3, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T1, "A21", GMC_DATATYPE_UINT32, &value4, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T1, "A22", GMC_DATATYPE_INT32, &value5, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T1, "A23", GMC_DATATYPE_UINT64, &value6, sizeof(uint64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T1, "A24", GMC_DATATYPE_INT64, &value7, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T1, "A25", GMC_DATATYPE_UCHAR, &value8, sizeof(char));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T1, "A26", GMC_DATATYPE_CHAR, &value9, sizeof(char));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T1, "A27", GMC_DATATYPE_FLOAT, &value10, sizeof(float));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T1, "A28", GMC_DATATYPE_DOUBLE, &value11, sizeof(double));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T1, "A29", GMC_DATATYPE_BOOL, &value12, sizeof(bool));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T1, "A30", GMC_DATATYPE_TIME, &value13, sizeof(value13));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T1, "A31", GMC_DATATYPE_STRING, value14, strlen(value14));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else if (addField == 5) {
        char value17[] = "string";
        ret = GmcNodeSetPropertyByName(T1, "A17", GMC_DATATYPE_STRING, &value17, strlen(value17));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(T1, "A18", GMC_DATATYPE_BYTES, &value17, strlen(value17));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcExecute(stmt);
    return ret;
}

void GtSpeciallabelStructFreeT2V(GtSpeciallabelT2VVertexT *vertex)
{
    if (vertex->v4) {
        free(vertex->v4);
    }
}

void GtSpeciallabelStructFreeT1V(GtSpeciallabelT1VVertexT *vertex)
{
    if (vertex->v4) {
        free(vertex->v4);
    }
    for (int32_t i = 0; i < vertex->t2VCount; i++) {
        GtSpeciallabelStructFreeT2V(&(vertex->t2V[i]));
    }
}

void GtSpeciallabelStructFreeT1V2(GtSpeciallabelT1VVertexT2 *vertex)
{
    if (vertex->v4) {
        free(vertex->v4);
    }
    if (vertex->v5) {
        free(vertex->v5);
    }
    for (int32_t i = 0; i < vertex->t2VCount; i++) {
        GtSpeciallabelStructFreeT2V(&(vertex->t2V[i]));
    }
}

void GtSpeciallabelStructFreeT1V22(GtSpeciallabelT1VVertexT *vertex)
{
    if (vertex->v4) {
        free(vertex->v4);
    }
    for (int32_t i = 0; i < vertex->t2VCount; i++) {
        GtSpeciallabelStructFreeT2V(&(vertex->t2V[i]));
    }
}

void GtSpeciallabel2StructFree(GtSpeciallabel2VertexT *vertex)
{
    if (vertex->f14) {
        free(vertex->f14);
    }
    for (int32_t i = 0; i < vertex->t1VCount; i++) {
        GtSpeciallabelStructFreeT1V(&(vertex->t1V[i]));
    }
    if (vertex->f15) {
        free(vertex->f15);
    }
    if (vertex->f16) {
        free(vertex->f16);
    }
}

void GtSpeciallabel2StructFree2(GtSpeciallabel2VertexT2 *vertex)
{
    if (vertex->f14) {
        free(vertex->f14);
    }
    for (int32_t i = 0; i < vertex->t1VCount; i++) {
        GtSpeciallabelStructFreeT1V2(&(vertex->t1V[i]));
    }
}

void GtSpeciallabel2StructFree3(GtSpeciallabel2VertexT3 *vertex)
{
    if (vertex->f14) {
        free(vertex->f14);
    }
    for (int32_t i = 0; i < vertex->t1VCount; i++) {
        GtSpeciallabelStructFreeT1V22(&(vertex->t1V[i]));
    }
}

void GtSpeciallabelStructSetT2VProperty(GtSpeciallabelT2VVertexT *vertex, int32_t value, char *stringValue)
{
    vertex->v1 = value;
    vertex->v2 = value;
    uint8_t bitmap[2] = {0x55, 0x55};
    (void)memcpy(vertex->v3, bitmap, sizeof(vertex->v3));

    vertex->v4Len = strlen(stringValue) + 1;
    if (!vertex->v4) {
        vertex->v4 = (uint8_t *)malloc(vertex->v4Len);
    }
    if (vertex->v4 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->v4 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->v4, vertex->v4Len, "%s", stringValue);
}

void GtSpeciallabelStructSetT3VProperty(GtSpeciallabelT3VVertexT3 *vertex, int32_t value, char *stringValue)
{
    vertex->v1 = value;

    vertex->v2Len = strlen(stringValue) + 1;
    if (!vertex->v2) {
        vertex->v2 = (uint8_t *)malloc(vertex->v2Len);
    }
    if (vertex->v2 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->v2 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->v2, vertex->v2Len, "%s", stringValue);
}

void GtSpeciallabelStructSetT1VProperty(
    GtSpeciallabelT1VVertexT *vertex, int32_t value, char *stringValue, uint16_t t2VCount)
{
    vertex->v1 = value;
    vertex->v2 = value;
    uint8_t bitmap[2] = {0x55, 0x55};
    memcpy(vertex->v3, bitmap, sizeof(vertex->v3));

    vertex->v4Len = strlen(stringValue) + 1;
    if (!vertex->v4) {
        vertex->v4 = (uint8_t *)malloc(vertex->v4Len);
    }
    if (vertex->v4 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->v4 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->v4, vertex->v4Len, "%s", stringValue);
    vertex->t2VCount = t2VCount;
    for (int32_t i = 0; i < vertex->t2VCount; i++) {
        GtSpeciallabelStructSetT2VProperty(&vertex->t2V[i], value, stringValue);
    }
}

void GtSpeciallabelStructSetT1VProperty2(
    GtSpeciallabelT1VVertexT2 *vertex, int32_t value, char *stringValue, uint16_t t2VCount)
{
    vertex->v1 = value;
    vertex->v2 = value;
    uint8_t bitmap[2] = {0x55, 0x55};
    memcpy(vertex->v3, bitmap, sizeof(vertex->v3));

    vertex->v4Len = strlen(stringValue) + 1;
    if (!vertex->v4) {
        vertex->v4 = (uint8_t *)malloc(vertex->v4Len);
    }
    if (vertex->v4 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->v4 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->v4, vertex->v4Len, "%s", stringValue);

    vertex->v5Len = strlen(stringValue) + 1;
    if (!vertex->v5) {
        vertex->v5 = (uint8_t *)malloc(vertex->v5Len);
    }
    if (vertex->v5 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->v5 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->v5, vertex->v5Len, "%s", stringValue);

    vertex->t2VCount = t2VCount;
    for (int32_t i = 0; i < vertex->t2VCount; i++) {
        GtSpeciallabelStructSetT2VProperty(&vertex->t2V[i], value, stringValue);
    }
}

void GtSpeciallabel2StructSetPk(GtSpeciallabel2VertexT *vertex, int64_t value)
{
    vertex->f0 = value;
}

void GtSpeciallabel2StructSetPk2(GtSpeciallabel2VertexT2 *vertex, int64_t value)
{
    vertex->f0 = value;
}

void GtSpeciallabel2StructSetPk3(GtSpeciallabel2VertexT3 *vertex, int64_t value)
{
    vertex->f0 = value;
}

void GtSpeciallabel2StructSetHashcluster(GtSpeciallabel2VertexT *vertex, int64_t value, int64_t updateValue = 0)
{
    uint64_t f1Value = value + updateValue;
    int32_t f2Value = value + updateValue;
    vertex->f1 = f1Value;
    vertex->f2 = f2Value;
}

void GtSpeciallabel2StructSetHashcluster2(GtSpeciallabel2VertexT2 *vertex, int64_t value, int64_t updateValue = 0)
{
    uint64_t f1Value = value + updateValue;
    int32_t f2Value = value + updateValue;
    vertex->f1 = f1Value;
    vertex->f2 = f2Value;
}

void GtSpeciallabel2StructSetHashcluster3(GtSpeciallabel2VertexT3 *vertex, int64_t value, int64_t updateValue = 0)
{
    uint64_t f1Value = value + updateValue;
    int32_t f2Value = value + updateValue;
    vertex->f1 = f1Value;
    vertex->f2 = f2Value;
}

void GtSpeciallabel2StructSetLocalhash(GtSpeciallabel2VertexT *vertex, int64_t value, int64_t updateValue = 0)
{
    int16_t f4Value = (value + updateValue) % 32768;
    uint16_t f5Value = (value + updateValue) % 65536;
    vertex->f4 = f4Value;
    vertex->f5 = f5Value;
}

void GtSpeciallabel2StructSetLocalhash2(GtSpeciallabel2VertexT2 *vertex, int64_t value, int64_t updateValue = 0)
{
    int16_t f4Value = (value + updateValue) % 32768;
    uint16_t f5Value = (value + updateValue) % 65536;
    vertex->f4 = f4Value;
    vertex->f5 = f5Value;
}

void GtSpeciallabel2StructSetLocalhash3(GtSpeciallabel2VertexT3 *vertex, int64_t value, int64_t updateValue = 0)
{
    int16_t f4Value = (value + updateValue) % 32768;
    uint16_t f5Value = (value + updateValue) % 65536;
    vertex->f4 = f4Value;
    vertex->f5 = f5Value;
}

void GtSpeciallabel2StructSetLocal(GtSpeciallabel2VertexT *vertex, int64_t value)
{
    uint32_t f3Value = 0;
    vertex->f3 = f3Value;
}

void GtSpeciallabel2StructSetLocal2(GtSpeciallabel2VertexT2 *vertex, int64_t value)
{
    uint32_t f3Value = 0;
    vertex->f3 = f3Value;
}

void GtSpeciallabel2StructSetLocal3(GtSpeciallabel2VertexT3 *vertex, int64_t value)
{
    uint32_t f3Value = 0;
    vertex->f3 = f3Value;
}

void GtSpeciallabel2StructSetLpm4(GtSpeciallabel2VertexT *vertex, int64_t value)
{
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    vertex->f3 = 0;
    vertex->f11 = 0;
    if ((value) <= MAX_MASK_LEN_16) {
        destIpAddr = ((value + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if ((value) > MAX_MASK_LEN_16 && (value) <= MAX_MASK_LEN_24) {
        destIpAddr = ((value + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((value + 2));
        maskLen = ((32) & 0xff);
    }
    vertex->f12 = destIpAddr;
    vertex->f6 = maskLen;
}

void GtSpeciallabel2StructSetLpm42(GtSpeciallabel2VertexT2 *vertex, int64_t value)
{
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    vertex->f3 = 0;
    vertex->f11 = 0;
    if ((value) <= MAX_MASK_LEN_16) {
        destIpAddr = ((value + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if ((value) > MAX_MASK_LEN_16 && (value) <= MAX_MASK_LEN_24) {
        destIpAddr = ((value + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((value + 2));
        maskLen = ((32) & 0xff);
    }
    vertex->f12 = destIpAddr;
    vertex->f6 = maskLen;
}

void GtSpeciallabel2StructSetLpm43(GtSpeciallabel2VertexT3 *vertex, int64_t value)
{
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    vertex->f3 = 0;
    vertex->f11 = 0;
    if ((value) <= MAX_MASK_LEN_16) {
        destIpAddr = ((value + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if ((value) > MAX_MASK_LEN_16 && (value) <= MAX_MASK_LEN_24) {
        destIpAddr = ((value + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((value + 2));
        maskLen = ((32) & 0xff);
    }
    vertex->f12 = destIpAddr;
    vertex->f6 = maskLen;
}

void GtSpeciallabel2StructSetOldProperty(GtSpeciallabel2VertexT *vertex, int64_t value, uint16_t t1VCount,
    uint16_t t2VCount, char *bytesValue, char *stringValue, bool isDefaultValue = true, int32_t updateValue = 0)
{
    vertex->f1 = value + updateValue;
    vertex->f2 = value + updateValue;

    vertex->f4 = (value + updateValue) % 32768;
    vertex->f5 = (value + updateValue) % 65536;
    vertex->f7 = value + updateValue;
    if (!isDefaultValue) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            vertex->f8[j] = j;
        }
        vertex->f9 = (value + updateValue) % 31;
        vertex->f10 = (value + updateValue) % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            vertex->f8[j] = 0xff;
        }
        vertex->f9 = 0x1f;
        vertex->f10 = 0x3ff;
    }
    vertex->f13 = (value + updateValue) & 0xf;
    vertex->f14Len = strlen(bytesValue);
    if (!vertex->f14) {
        vertex->f14 = (uint8_t *)malloc(vertex->f14Len + 1);
    }
    if (vertex->f14 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->f14 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->f14, vertex->f14Len + 1, "%s", bytesValue);
    vertex->t1VCount = t1VCount;
    for (int32_t i = 0; i < vertex->t1VCount; i++) {
        GtSpeciallabelStructSetT1VProperty(&vertex->t1V[i], value, stringValue, t2VCount);
    }
}

void GtSpeciallabel2StructSetOldProperty2(GtSpeciallabel2VertexT2 *vertex, int64_t value, uint16_t t1VCount,
    uint16_t t2VCount, char *bytesValue, char *stringValue, bool isDefaultValue = true, int32_t updateValue = 0)
{
    vertex->f1 = value + updateValue;
    vertex->f2 = value + updateValue;

    vertex->f4 = (value + updateValue) % 32768;
    vertex->f5 = (value + updateValue) % 65536;
    vertex->f7 = value + updateValue;
    if (!isDefaultValue) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            vertex->f8[j] = j;
        }
        vertex->f9 = (value + updateValue) % 31;
        vertex->f10 = (value + updateValue) % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            vertex->f8[j] = 0xff;
        }
        vertex->f9 = 0x1f;
        vertex->f10 = 0x3ff;
    }
    vertex->f13 = (value + updateValue) & 0xf;
    vertex->f14Len = strlen(bytesValue);
    if (!vertex->f14) {
        vertex->f14 = (uint8_t *)malloc(vertex->f14Len + 1);
    }
    if (vertex->f14 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->f14 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->f14, vertex->f14Len + 1, "%s", bytesValue);
    vertex->t1VCount = t1VCount;
    for (int32_t i = 0; i < vertex->t1VCount; i++) {
        GtSpeciallabelStructSetT1VProperty2(&vertex->t1V[i], value, stringValue, t2VCount);
    }
}

void GtSpeciallabel2StructSetOldProperty3(GtSpeciallabel2VertexT3 *vertex, int64_t value, uint16_t t1VCount,
    uint16_t t2VCount, char *bytesValue, char *stringValue, bool isDefaultValue = true, int32_t updateValue = 0)
{
    vertex->f1 = value + updateValue;
    vertex->f2 = value + updateValue;

    vertex->f4 = (value + updateValue) % 32768;
    vertex->f5 = (value + updateValue) % 65536;
    vertex->f7 = value + updateValue;
    if (!isDefaultValue) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            vertex->f8[j] = j;
        }
        vertex->f9 = (value + updateValue) % 31;
        vertex->f10 = (value + updateValue) % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            vertex->f8[j] = 0xff;
        }
        vertex->f9 = 0x1f;
        vertex->f10 = 0x3ff;
    }
    vertex->f13 = (value + updateValue) & 0xf;
    vertex->f14Len = strlen(bytesValue);
    if (!vertex->f14) {
        vertex->f14 = (uint8_t *)malloc(vertex->f14Len + 1);
    }
    if (vertex->f14 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->f14 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->f14, vertex->f14Len + 1, "%s", bytesValue);
    vertex->t1VCount = t1VCount;
    for (int32_t i = 0; i < vertex->t1VCount; i++) {
        GtSpeciallabelStructSetT1VProperty(&vertex->t1V[i], value, stringValue, t2VCount);
    }

    vertex->t3VFlag = 1;
    GtSpeciallabelStructSetT3VProperty(vertex->t3V, value, stringValue);
}

void GtSpeciallabel2StructSetNewProperty(GtSpeciallabel2VertexT *vertex, char *bytesValue, char *stringValue)
{
    vertex->f15Len = strlen(bytesValue);
    if (!vertex->f15) {
        vertex->f15 = (uint8_t *)malloc(vertex->f15Len + 1);
    }
    if (vertex->f15 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->f15 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->f15, vertex->f15Len + 1, "%s", bytesValue);
    vertex->f16Len = strlen(stringValue) + 1;
    if (!vertex->f16) {
        vertex->f16 = (uint8_t *)malloc(vertex->f16Len);
    }
    if (vertex->f16 == NULL) {
        AW_FUN_Log(LOG_ERROR, "vertex->f16 is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)vertex->f16, vertex->f16Len, "%s", stringValue);
}

void GtSpeciallabel2StructSetProperty(GtSpeciallabel2VertexT *vertex, int32_t value, uint16_t t1VCount,
    uint16_t t2VCount, char *bytesValue, char *stringValue, bool isDefaultValue = true, int32_t coefficient = 0)
{
    GtSpeciallabel2StructSetPk(vertex, value);
    GtSpeciallabel2StructSetHashcluster(vertex, value, coefficient);
    GtSpeciallabel2StructSetLocalhash(vertex, value, coefficient);
    GtSpeciallabel2StructSetLocal(vertex, value);
    GtSpeciallabel2StructSetLpm4(vertex, value);
    GtSpeciallabel2StructSetOldProperty(
        vertex, value, t1VCount, t2VCount, bytesValue, stringValue, isDefaultValue, coefficient);
    GtSpeciallabel2StructSetNewProperty(vertex, bytesValue, stringValue);
}

void GtSpeciallabel2StructSetProperty2(GtSpeciallabel2VertexT2 *vertex, int32_t value, uint16_t t1VCount,
    uint16_t t2VCount, char *bytesValue, char *stringValue, bool isDefaultValue = true, int32_t coefficient = 0)
{
    GtSpeciallabel2StructSetPk2(vertex, value);
    GtSpeciallabel2StructSetHashcluster2(vertex, value, coefficient);
    GtSpeciallabel2StructSetLocalhash2(vertex, value, coefficient);
    GtSpeciallabel2StructSetLocal2(vertex, value);
    GtSpeciallabel2StructSetLpm42(vertex, value);
    GtSpeciallabel2StructSetOldProperty2(
        vertex, value, t1VCount, t2VCount, bytesValue, stringValue, isDefaultValue, coefficient);
}

void GtSpeciallabel2StructSetProperty3(GtSpeciallabel2VertexT3 *vertex, int32_t value, uint16_t t1VCount,
    uint16_t t2VCount, char *bytesValue, char *stringValue, bool isDefaultValue = true, int32_t coefficient = 0)
{
    GtSpeciallabel2StructSetPk3(vertex, value);
    GtSpeciallabel2StructSetHashcluster3(vertex, value, coefficient);
    GtSpeciallabel2StructSetLocalhash3(vertex, value, coefficient);
    GtSpeciallabel2StructSetLocal3(vertex, value);
    GtSpeciallabel2StructSetLpm43(vertex, value);
    GtSpeciallabel2StructSetOldProperty3(
        vertex, value, t1VCount, t2VCount, bytesValue, stringValue, isDefaultValue, coefficient);
}

int GtSpeciallabel2StructWrite(GmcStmtT *stmt, GtSpeciallabelCfgT vertexCfg, char *bytesValue, char *stringValue,
    bool isDefaultValue = true, int addField = 1)
{
    int ret = GMERR_OK;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t threadId = vertexCfg.threadId;
    uint16_t t1VCount = vertexCfg.t1VCount;
    uint16_t t2VCount = vertexCfg.t2VCount;
    uint32_t schemaVersion = vertexCfg.schemaVersion;
    GmcOperationTypeE optType = vertexCfg.optType;
    TestLabelInfoT labelInfo = {g_labelName, schemaVersion, g_testNameSpace};

    if (addField == 1) {
        GtSpeciallabel2VertexT *vertex = (GtSpeciallabel2VertexT *)malloc(sizeof(GtSpeciallabel2VertexT));
        if (vertex == NULL) {
            AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
            return 1;
        }
        (void)memset(vertex, 0, sizeof(GtSpeciallabel2VertexT));

        GtSpeciallabelT1VVertexT *t1V = (GtSpeciallabelT1VVertexT *)malloc(sizeof(GtSpeciallabelT1VVertexT) * t1VCount);
        if (t1V == NULL) {
            AW_FUN_Log(LOG_ERROR, "t1V is NULL\n");
            return 1;
        }
        (void)memset(t1V, 0, sizeof(GtSpeciallabelT1VVertexT) * t1VCount);
        vertex->t1V = t1V;

        GtSpeciallabelT2VVertexT *t2V =
            (GtSpeciallabelT2VVertexT *)malloc(sizeof(GtSpeciallabelT2VVertexT) * t1VCount * t2VCount);
        if (t2V == NULL) {
            AW_FUN_Log(LOG_ERROR, "t2V is NULL\n");
            return 1;
        }
        (void)memset(t2V, 0, sizeof(GtSpeciallabelT2VVertexT) * t1VCount * t2VCount);
        for (int32_t i = 0; i < t1VCount; i++) {
            t1V[i].t2V = &t2V[t2VCount * ((0) + i)];
        }

        for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
            ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, schemaVersion, optType);
            RETURN_IFERR(ret);

            GtSpeciallabel2StructSetProperty(
                vertex, i, t1VCount, t2VCount, bytesValue, stringValue, isDefaultValue, coefficient);

            ret = testStructSetVertexWithBuf(stmt, vertex, &labelInfo);
            RETURN_IFERR(ret);

            ret = GmcExecute(stmt);
            RETURN_IFERR(ret);
            ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expAffectRows);
            RETURN_IFERR(ret);
        }

        GtSpeciallabel2StructFree(vertex);
        free(t2V);
        free(t1V);
        free(vertex);
    } else if (addField == 2) {
        GtSpeciallabel2VertexT2 *vertex = (GtSpeciallabel2VertexT2 *)malloc(sizeof(GtSpeciallabel2VertexT2));
        if (vertex == NULL) {
            AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
            return 1;
        }
        (void)memset(vertex, 0, sizeof(GtSpeciallabel2VertexT2));

        GtSpeciallabelT1VVertexT2 *t1V =
            (GtSpeciallabelT1VVertexT2 *)malloc(sizeof(GtSpeciallabelT1VVertexT2) * t1VCount);
        if (t1V == NULL) {
            AW_FUN_Log(LOG_ERROR, "t1V is NULL\n");
            return 1;
        }
        (void)memset(t1V, 0, sizeof(GtSpeciallabelT1VVertexT2) * t1VCount);
        vertex->t1V = t1V;

        GtSpeciallabelT2VVertexT *t2V =
            (GtSpeciallabelT2VVertexT *)malloc(sizeof(GtSpeciallabelT2VVertexT) * t1VCount * t2VCount);
        if (t2V == NULL) {
            AW_FUN_Log(LOG_ERROR, "t2V is NULL\n");
            return 1;
        }
        (void)memset(t2V, 0, sizeof(GtSpeciallabelT2VVertexT) * t1VCount * t2VCount);
        for (int32_t i = 0; i < t1VCount; i++) {
            t1V[i].t2V = &t2V[t2VCount * ((0) + i)];
        }

        for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
            ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, schemaVersion, optType);
            RETURN_IFERR(ret);

            GtSpeciallabel2StructSetProperty2(
                vertex, i, t1VCount, t2VCount, bytesValue, stringValue, isDefaultValue, coefficient);

            ret = testStructSetVertexWithBuf(stmt, vertex, &labelInfo);
            RETURN_IFERR(ret);

            ret = GmcExecute(stmt);
            RETURN_IFERR(ret);
            ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expAffectRows);
            RETURN_IFERR(ret);
        }

        GtSpeciallabel2StructFree2(vertex);
        free(t2V);
        free(t1V);
        free(vertex);
    } else if (addField == 3) {
        GtSpeciallabel2VertexT3 *vertex = (GtSpeciallabel2VertexT3 *)malloc(sizeof(GtSpeciallabel2VertexT3));
        if (vertex == NULL) {
            AW_FUN_Log(LOG_ERROR, "vertex is NULL\n");
            return 1;
        }
        (void)memset(vertex, 0, sizeof(GtSpeciallabel2VertexT3));

        GtSpeciallabelT1VVertexT *t1V = (GtSpeciallabelT1VVertexT *)malloc(sizeof(GtSpeciallabelT1VVertexT) * t1VCount);
        if (t1V == NULL) {
            AW_FUN_Log(LOG_ERROR, "t1V is NULL\n");
            return 1;
        }
        (void)memset(t1V, 0, sizeof(GtSpeciallabelT1VVertexT) * t1VCount);
        vertex->t1V = t1V;

        GtSpeciallabelT2VVertexT *t2V =
            (GtSpeciallabelT2VVertexT *)malloc(sizeof(GtSpeciallabelT2VVertexT) * t1VCount * t2VCount);
        if (t2V == NULL) {
            AW_FUN_Log(LOG_ERROR, "t2V is NULL\n");
            return 1;
        }
        (void)memset(t2V, 0, sizeof(GtSpeciallabelT2VVertexT) * t1VCount * t2VCount);
        for (int32_t i = 0; i < t1VCount; i++) {
            t1V[i].t2V = &t2V[t2VCount * ((0) + i)];
        }

        GtSpeciallabelT3VVertexT3 *t3V = (GtSpeciallabelT3VVertexT3 *)malloc(sizeof(GtSpeciallabelT3VVertexT3));
        if (t3V == NULL) {
            AW_FUN_Log(LOG_ERROR, "t3V is NULL\n");
            return 1;
        }
        (void)memset(t3V, 0, sizeof(GtSpeciallabelT3VVertexT3));
        vertex->t3V = t3V;

        for (int32_t i = startPkVal; i < startPkVal + vertexCount; i++) {
            ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, schemaVersion, optType);
            RETURN_IFERR(ret);

            GtSpeciallabel2StructSetProperty3(
                vertex, i, t1VCount, t2VCount, bytesValue, stringValue, isDefaultValue, coefficient);

            ret = testStructSetVertexWithBuf(stmt, vertex, &labelInfo);
            RETURN_IFERR(ret);

            ret = GmcExecute(stmt);
            RETURN_IFERR(ret);
            ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expAffectRows);
            RETURN_IFERR(ret);
        }
        GtSpeciallabel2StructFree3(vertex);
        free(t2V);
        free(t1V);
        free(vertex);
    }

    return GMERR_OK;
}
#endif
