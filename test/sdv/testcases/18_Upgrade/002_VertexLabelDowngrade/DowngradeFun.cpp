/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2012-2018. All rights reserved.
 Description  :表结构降级功能测试
 Author       : wuxiaochun wx753022
 Modification :
 Date         : 2022/10/18
**************************************************************************** */
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <time.h>

#include "gtest/gtest.h"

#include "VertexLabelDowngradeStruct.h"

class DowngradeTest : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void DowngradeTest::SetUpTestCase()
{
    int ret = 0;
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void DowngradeTest::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}

void DowngradeTest::SetUp()
{
    // 建连
    int ret = 0;
    char *schemaParth = (char *)"./schemaFile/simpleLabel1.gmjson";

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创表
    ret = TestCreateLabel(g_stmt, schemaParth, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
    char errorMsg1[128] = {0}, errorMsg2[128] = {0};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INVALID_PROPERTY);
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
}

void DowngradeTest::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = 0;
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
}

/* ****************************************************************************
 Description  : 001.label存在多个版本号，写入数据，对最新版本号进行回退后，对其余版本号进行读数据操作
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DowngradeTest, Upgrade_002_003_001)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int fetchNum = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10;
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    char *schemaUpdateParth = (char *)"./schemaFile/SimpleLabelUpgradeFullFields.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SimpleLabelUpgradeFullFields2.gmjson";
    uint32_t schemaVersion = 2;

    GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_INSERT};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg1, 0, true);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSimplelabelCfgT vertexCfg2 = {(int32_t)endValue, endValue * 2, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg2, 2, true);
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSimplelabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue * 3, 0, 1, GMC_OPERATION_MERGE};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg3, 3, true);

    ret = TestDownGradeVertexLabel(g_labelName, schemaVersion, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 核查
    GtSimplelabelCfgRead pkReadCfg = {startValue, endValue, 0, (int32_t)endValue * 3, {false,true,true}};
    GtSimplelabelCfgRead pkReadCfg2 = {(int32_t)endValue, endValue * 2, 0, (int32_t)endValue * 3, {false,false,true}};
    GtSimplelabelCfgRead pkReadCfg3 = {(int32_t)endValue * 2, endValue * 3, 0, (int32_t)endValue * 3,
                                       {false,false,false}};
    for (uint32_t keyId = 0; keyId < 5; keyId++) {
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg, 0, keyId);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg, 2, keyId);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg2, 0, keyId);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg2, 2, keyId);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg3, 0, keyId);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg3, 2, keyId);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 002.label存在多个版本号，写入数据，对最新版本号进行回退后，对其余版本号进行普通insert
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DowngradeTest, Upgrade_002_003_002)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int fetchNum = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10;
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    char *schemaUpdateParth = (char *)"./schemaFile/SimpleLabelUpgradeFullFields.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SimpleLabelUpgradeFullFields2.gmjson";
    uint32_t schemaVersion = 2;

    GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_INSERT};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg1, 0, true);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSimplelabelCfgT vertexCfg2 = {(int32_t)endValue, endValue * 2, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg2, 2, true);
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSimplelabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue * 3, 0, 1, GMC_OPERATION_MERGE};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg3, 3, true);

    ret = TestDownGradeVertexLabel(g_labelName, schemaVersion, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GtSimplelabelCfgT vertexCfg4 = {(int32_t)endValue * 3, endValue * 4, 0, 1, GMC_OPERATION_INSERT};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg4, 0, true);
    GtSimplelabelCfgT vertexCfg5 = {(int32_t)endValue * 4, endValue * 5, 0, 1, GMC_OPERATION_INSERT};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg5, 2, true);
    // 核查
    GtSimplelabelCfgRead pkReadCfg = {startValue, endValue, 0, (int32_t)endValue * 5, {false,true,true}};
    GtSimplelabelCfgRead pkReadCfg2 = {(int32_t)endValue, endValue * 2, 0, (int32_t)endValue * 5, {false,false,true}};
    GtSimplelabelCfgRead pkReadCfg3 = {(int32_t)endValue * 2, endValue * 3, 0, (int32_t)endValue * 5,
                                       {false,false,false}};
    GtSimplelabelCfgRead pkReadCfg4 = {(int)endValue * 3, endValue * 4, 0, (int32_t)endValue * 5, {false,true,true}};
    GtSimplelabelCfgRead pkReadCfg5 = {(int)endValue * 4, endValue * 5, 0, (int32_t)endValue * 5, {false,false,true}};
    for (uint32_t keyId = 0; keyId < 5; keyId++) {
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg, 0, keyId);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg, 2, keyId);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg2, 0, keyId);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg2, 2, keyId);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg3, 0, keyId);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg3, 2, keyId);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg4, 0, keyId);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg4, 2, keyId);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg5, 0, keyId);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg5, 2, keyId);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 003.label存在多个版本号，写入数据，对最新版本号进行回退后，对其余版本号进行普通replace
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DowngradeTest, Upgrade_002_003_003)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int fetchNum = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10;
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    char *schemaUpdateParth = (char *)"./schemaFile/SimpleLabelUpgradeFullFields.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SimpleLabelUpgradeFullFields2.gmjson";
    uint32_t schemaVersion = 2;

    GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_INSERT};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg1, 0, true);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSimplelabelCfgT vertexCfg2 = {(int32_t)endValue, endValue * 2, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg2, 2, true);
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSimplelabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue * 3, 0, 1, GMC_OPERATION_MERGE};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg3, 3, true);

    ret = TestDownGradeVertexLabel(g_labelName, schemaVersion, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GtSimplelabelCfgT vertexCfg4 = {0, endValue * 3, 0, 2, GMC_OPERATION_REPLACE};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg4, 2, false);
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg4, 0, false);

    GtSimplelabelCfgT vertexCfg5 = {(int)endValue * 3, endValue * 4, 0, 1, GMC_OPERATION_REPLACE};
    GtSimplelabelCfgT vertexCfg6 = {(int)endValue * 4, endValue * 5, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg5, 0, false);
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg6, 2, false);
    // 核查
    GtSimplelabelCfgRead pkReadCfg = {startValue, endValue * 4, 0, (int32_t)endValue * 5, {false,true,true}};
    GtSimplelabelCfgRead pkReadCfg2 = {(int)endValue * 4, endValue * 5, 0, (int32_t)endValue * 5, {false,false,true}};
    for (uint32_t keyId = 0; keyId < 5; keyId++) {
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg, 0, keyId, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg, 2, keyId, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (keyId != 3) {
            ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg2, 2, keyId, false);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg2, 0, keyId, false);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 004.label存在多个版本号，写入数据，对最新版本号进行回退后，对其余版本号进行普通merge
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DowngradeTest, Upgrade_002_003_004)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int fetchNum = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10;
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    char *schemaUpdateParth = (char *)"./schemaFile/SimpleLabelUpgradeFullFields.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SimpleLabelUpgradeFullFields2.gmjson";
    uint32_t schemaVersion = 2;

    GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_INSERT};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg1, 0, true);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSimplelabelCfgT vertexCfg2 = {(int32_t)endValue, endValue * 2, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg2, 2, true);
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSimplelabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue * 3, 0, 1, GMC_OPERATION_MERGE};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg3, 3, true);

    ret = TestDownGradeVertexLabel(g_labelName, schemaVersion, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t updateValue = 500;
    vertexCfg1.optType = GMC_OPERATION_MERGE;
    vertexCfg1.expAffectRows = 2;
    vertexCfg2.optType = GMC_OPERATION_MERGE;
    vertexCfg2.expAffectRows = 2;
    vertexCfg3.optType = GMC_OPERATION_MERGE;
    vertexCfg3.expAffectRows = 2;
    TestSimpleTNewOldVersionUpdate(g_stmt, vertexCfg1, 0, 1, false, updateValue);
    TestSimpleTNewOldVersionUpdate(g_stmt, vertexCfg2, 2, 1, false, updateValue);
    TestSimpleTNewOldVersionUpdate(g_stmt, vertexCfg3, 2, 1, false, updateValue);

    GtSimplelabelCfgT vertexCfg4 = {(int32_t)endValue * 3, endValue * 4, 0, 1, GMC_OPERATION_MERGE};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg4, 0, true);
    GtSimplelabelCfgT vertexCfg5 = {(int32_t)endValue * 4, endValue * 5, 0, 1, GMC_OPERATION_MERGE};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg5, 2, true);

    // 核查
    GtSimplelabelCfgRead pkReadCfg = {(int)endValue, endValue * 3, 0, (int32_t)endValue * 5, {false,false,true}};
    GtSimplelabelCfgRead pkReadCfg2 = {0, endValue, 0, (int32_t)endValue * 5, {false,true,true}};
    GtSimplelabelCfgRead pkReadCfg3 = {(int)endValue * 3, endValue * 4, 0, (int32_t)endValue * 5, {false,true,true}};
    GtSimplelabelCfgRead pkReadCfg4 = {(int)endValue * 4, endValue * 5, 0, (int32_t)endValue * 5, {false,false,true}};
    for (uint32_t keyId = 0; keyId < 5; keyId++) {
        AW_FUN_Log(LOG_INFO, "KeyId = %d\n", keyId);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg, 0, keyId, false, updateValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg, 2, keyId, false, updateValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg2, 0, keyId, false, updateValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg2, 2, keyId, false, updateValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg3, 0, keyId, true);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg3, 2, keyId, true);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg4, 0, keyId, true);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg4, 2, keyId, true);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 005.label存在多个版本号，写入数据，对最新版本号进行回退后，对其余版本号进行普通update
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DowngradeTest, Upgrade_002_003_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int fetchNum = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10;
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    char *schemaUpdateParth = (char *)"./schemaFile/SimpleLabelUpgradeFullFields.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SimpleLabelUpgradeFullFields2.gmjson";
    uint32_t schemaVersion = 2;

    GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_INSERT};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg1, 0, true);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSimplelabelCfgT vertexCfg2 = {(int32_t)endValue, endValue * 2, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg2, 2, true);
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSimplelabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue * 3, 0, 1, GMC_OPERATION_MERGE};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg3, 3, true);

    ret = TestDownGradeVertexLabel(g_labelName, schemaVersion, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t updateValue = 300;
    vertexCfg1.optType = GMC_OPERATION_UPDATE;
    vertexCfg2.optType = GMC_OPERATION_UPDATE;
    vertexCfg3.optType = GMC_OPERATION_UPDATE;

    TestSimpleTNewOldVersionUpdate(g_stmt, vertexCfg1, 0, 1, false, updateValue);
    TestSimpleTNewOldVersionUpdate(g_stmt, vertexCfg2, 2, 2, false, updateValue);
    TestSimpleTNewOldVersionUpdate(g_stmt, vertexCfg3, 2, 0, false, updateValue);

    // 核查
    GtSimplelabelCfgRead pkReadCfg = {(int)endValue, endValue * 3, 0, (int32_t)endValue * 3, {false,false,false}};
    GtSimplelabelCfgRead pkReadCfg2 = {0, endValue, 0, (int32_t)endValue * 3, {false,true,true}};
    for (uint32_t keyId = 0; keyId < 5; keyId++) {
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg, 0, keyId, false, updateValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg, 2, keyId, false, updateValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg2, 0, keyId, false, updateValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg2, 2, keyId, false, updateValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 006.label存在多个版本号，写入数据，对最新版本号进行回退后，对其余版本号进行普通delete
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DowngradeTest, Upgrade_002_003_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int fetchNum = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10;
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    char *schemaUpdateParth = (char *)"./schemaFile/SimpleLabelUpgradeFullFields.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SimpleLabelUpgradeFullFields2.gmjson";
    uint32_t schemaVersion = 2;

    GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_INSERT};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg1, 0, true);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSimplelabelCfgT vertexCfg2 = {(int32_t)endValue, endValue * 2, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg2, 2, true);
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSimplelabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue * 3, 0, 1, GMC_OPERATION_MERGE};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg3, 3, true);

    ret = TestDownGradeVertexLabel(g_labelName, schemaVersion, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除
    TestSimpleT2Delete(g_stmt, g_labelName, startValue, endValue, 0, 0);
    TestSimpleT2Delete(g_stmt, g_labelName, endValue, endValue * 3, 1, 2);
    // 核查
    fetchNum = 0;
    TestSimpleT1LocalScan(g_stmt, g_labelName, startValue, endValue * 3, schemaVersion, &fetchNum);
    AW_MACRO_ASSERT_EQ_INT(0, fetchNum);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 007.label存在多个版本号，写入数据，对最新版本号进行回退后，对其余版本号进行结构化读写更新
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DowngradeTest, Upgrade_002_003_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int fetchNum = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10;
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    char *schemaUpdateParth = (char *)"./schemaFile/SimpleLabelUpgradeFullFields.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SimpleLabelUpgradeFullFields2.gmjson";
    uint32_t schemaVersion = 2;

    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 老版本结构化写
    schemaVersion = 0;
    GtSimplelabelStructCfgT vertexCfg = {startValue, endValue, 0, 1, 0};
    ret = GtSimplelabel0StructInsertOrReplace(g_stmt, g_labelName, vertexCfg, schemaVersion, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 中间版本结构化写
    schemaVersion = 2;
    GtSimplelabelStructCfgT vertexCfg2 = {(int32_t)endValue, endValue, 0, 1, 0};
    ret = GtSimplelabel2StructInsertOrReplace(g_stmt, g_labelName, vertexCfg2, schemaVersion, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 最新版本结构化写
    schemaVersion = 3;
    GtSimplelabelStructCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue, 0, 1, 0};
    ret = GtSimplelabel5StructInsertOrReplace(g_stmt, g_labelName, vertexCfg3, schemaVersion, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = TestDownGradeVertexLabel(g_labelName, 2, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构化写
    GtSimplelabelStructCfgT vertexCfg4 = {(int)endValue * 3, endValue, 0, 1, 0};
    ret = GtSimplelabel0StructInsertOrReplace(g_stmt, g_labelName, vertexCfg4, 0, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSimplelabelStructCfgT vertexCfg5 = {(int)endValue * 4, endValue, 0, 1, 0};
    ret = GtSimplelabel2StructInsertOrReplace(g_stmt, g_labelName, vertexCfg5, 2, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 结构化读
    schemaVersion = 0;
    vertexCfg.expAffectRows = endValue * 5;
    for (uint32_t keyId = 0; keyId < 5; keyId++) {
        ret = GtSimplelabel0StructIndexScan(g_stmt, g_labelName, vertexCfg, keyId, schemaVersion);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (keyId != 3) {
            ret = GtSimplelabel0StructIndexScan(g_stmt, g_labelName, vertexCfg2, keyId, schemaVersion);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GtSimplelabel0StructIndexScan(g_stmt, g_labelName, vertexCfg3, keyId, schemaVersion);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GtSimplelabel0StructIndexScan(g_stmt, g_labelName, vertexCfg4, keyId, schemaVersion);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GtSimplelabel0StructIndexScan(g_stmt, g_labelName, vertexCfg5, keyId, schemaVersion);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }
    schemaVersion = 2;
    for (uint32_t keyId = 0; keyId < 5; keyId++) {
        ret = GtSimplelabel2StructIndexScan(g_stmt, g_labelName, vertexCfg, keyId, schemaVersion, true, 0, true);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (keyId != 3) {
            ret = GtSimplelabel2StructIndexScan(g_stmt, g_labelName, vertexCfg4, keyId, schemaVersion, true, 0, true);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GtSimplelabel2StructIndexScan(g_stmt, g_labelName, vertexCfg5, keyId, schemaVersion, true, 0, false);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GtSimplelabel2StructIndexScan(g_stmt, g_labelName, vertexCfg2, keyId, schemaVersion, true, 0, false);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GtSimplelabel2StructIndexScan(g_stmt, g_labelName, vertexCfg3, keyId, schemaVersion, true, 0, false);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 008.label存在多个版本号，写入数据，对最新版本号进行回退后，对其余版本号进行批量读写更新
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DowngradeTest, Upgrade_002_003_008)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int fetchNum = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10;
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    char *schemaUpdateParth = (char *)"./schemaFile/SimpleLabelUpgradeFullFields.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SimpleLabelUpgradeFullFields2.gmjson";
    uint32_t schemaVersion = 2;
    int32_t updateValue = 500;

    GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_INSERT};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg1, 0, true);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSimplelabelCfgT vertexCfg2 = {(int32_t)endValue, endValue * 2, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg2, 2, true);
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSimplelabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue * 3, 0, 1, GMC_OPERATION_MERGE};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg3, 3, true);

    ret = TestDownGradeVertexLabel(g_labelName, schemaVersion, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSimpleT1InsertOrReplaceBatch(g_conn, g_stmt, g_labelName, (int32_t)endValue * 3, endValue * 4,
        0, GMC_OPERATION_INSERT, true);
    TestSimpleT1InsertOrReplaceBatch(g_conn, g_stmt, g_labelName, (int32_t)endValue * 4, endValue * 5,
        2, GMC_OPERATION_INSERT, true);
    // merge-update
    TestSimpleT1MergeOrUpdateBatch(g_conn, g_stmt, g_labelName, startValue, endValue,
        2, GMC_OPERATION_MERGE, false, updateValue);
    TestSimpleT1MergeOrUpdateBatch(g_conn, g_stmt, g_labelName, endValue, endValue * 2,
        0, GMC_OPERATION_MERGE, false, updateValue);
    TestSimpleT1MergeOrUpdateBatch(g_conn, g_stmt, g_labelName, endValue * 2, endValue * 3,
        2, GMC_OPERATION_MERGE, false, updateValue);
    // 核查
    GtSimplelabelCfgRead pkReadCfg = {startValue, endValue, 0, (int32_t)endValue * 5, {false,false,true}};
    GtSimplelabelCfgRead pkReadCfg2 = {(int32_t)endValue, endValue * 2, 0, (int32_t)endValue * 5, {false,false,true}};
    GtSimplelabelCfgRead pkReadCfg3 = {(int32_t)endValue * 2, endValue * 3, 0, (int32_t)endValue * 5,
                                       {false,false,false}};
    GtSimplelabelCfgRead pkReadCfg4 = {(int)endValue * 3, endValue * 4, 0, (int32_t)endValue * 5, {false,true,true}};
    GtSimplelabelCfgRead pkReadCfg5 = {(int)endValue * 4, endValue * 5, 0, (int32_t)endValue * 5, {false,false,true}};
    for (uint32_t keyId = 0; keyId < 3; keyId++) {
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg, 0, keyId, false, updateValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg, 2, keyId, false, updateValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg2, 0, keyId, false, updateValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg3, 0, keyId, false, updateValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg3, 2, keyId, false, updateValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg4, 0, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg4, 2, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg5, 0, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg5, 2, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 009.label存在多个版本号，写入数据，对最新版本号进行回退后，对表当前最新版本号进行DML订阅推送
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DowngradeTest, Upgrade_002_003_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int fetchNum = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10;
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    char *schemaUpdateParth = (char *)"./schemaFile/SimpleLabelUpgradeFullFields.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SimpleLabelUpgradeFullFields2.gmjson";
    uint32_t schemaVersion = 2;
    int32_t updateValue = 500;

    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSimplelabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue * 3, 0, 1, GMC_OPERATION_INSERT};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg3, 3, false);

    ret = TestDownGradeVertexLabel(g_labelName, schemaVersion, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 订阅
    char *subInfo = NULL;
    int chanRingLen = 64;
    char subConnName[128] = "subTabel";
    GmcStmtT *stmtSub = NULL;
    GmcConnT *testSubConn = NULL;
    char subName[128] = "subSimpleLabel";
    SnUserDataT *userData = NULL;
    ret = testSubConnect(&testSubConn, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    readJanssonFile("schemaFile/simpleTSub.gmjson", &subInfo);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, subInfo);
    userData = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData, 0, sizeof(SnUserDataT));
    GmcSubConfigT tmpSubInfo;
    tmpSubInfo.subsName = subName;
    tmpSubInfo.configJson = subInfo;
    ret = GmcSubscribe(g_stmt, &tmpSubInfo, testSubConn, SubSimpleTCallBackWithOldVersion, userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(subInfo);

    // insert写数据
    GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_INSERT};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg1, 0, false);
    GtSimplelabelCfgT vertexCfg2 = {(int32_t)endValue, endValue * 2, 0, 1, GMC_OPERATION_INSERT};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg2, 2, false);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INSERT, (int32_t)endValue * 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // replace写数据
    vertexCfg1.expAffectRows = 2;
    vertexCfg1.optType = GMC_OPERATION_REPLACE;
    vertexCfg2.expAffectRows = 2;
    vertexCfg2.optType = GMC_OPERATION_REPLACE;
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg1, 2, false);
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg2, 0, false);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_REPLACE, (int32_t)endValue * 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // merge-update
    vertexCfg1.optType = GMC_OPERATION_MERGE;
    vertexCfg2.optType = GMC_OPERATION_MERGE;
    vertexCfg3.optType = GMC_OPERATION_MERGE;
    vertexCfg3.expAffectRows = 2;
    TestSimpleTNewOldVersionUpdate(g_stmt, vertexCfg1, 2, 0, true, updateValue);
    TestSimpleTNewOldVersionUpdate(g_stmt, vertexCfg2, 2, 0, true, updateValue);
    TestSimpleTNewOldVersionUpdate(g_stmt, vertexCfg3, 2, 0, true, updateValue);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_MERGE_UPDATE, (int32_t)endValue * 3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // update
    vertexCfg1.optType = GMC_OPERATION_UPDATE;
    vertexCfg1.expAffectRows = 1;
    vertexCfg2.optType = GMC_OPERATION_UPDATE;
    vertexCfg2.expAffectRows = 1;
    vertexCfg3.optType = GMC_OPERATION_UPDATE;
    vertexCfg3.expAffectRows = 1;
    TestSimpleTNewOldVersionUpdate(g_stmt, vertexCfg1, 2, 0, false);
    TestSimpleTNewOldVersionUpdate(g_stmt, vertexCfg2, 2, 0, false);
    TestSimpleTNewOldVersionUpdate(g_stmt, vertexCfg3, 2, 0, false);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_UPDATE, (int32_t)endValue * 3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 删数据
    TestSimpleT2Delete(g_stmt, g_labelName, 0, (int64_t)endValue *3, 0, 2);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_DELETE, (int32_t)endValue * 3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 取消订阅
    ret = GmcUnSubscribe(g_stmt, subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(userData);
    // 释放订阅连接
    ret = testSubDisConnect(testSubConn, stmtSub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");


}

/* ****************************************************************************
 Description  : 010.label存在多个版本号，写入数据，对旧版本号进行删除后，对其余版本号进行读数据操作
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DowngradeTest, Upgrade_002_003_010)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int fetchNum = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10;
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    char *schemaUpdateParth = (char *)"./schemaFile/SimpleLabelUpgradeFullFields.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SimpleLabelUpgradeFullFields2.gmjson";
    uint32_t schemaVersion = 0;

    GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_INSERT};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg1, 0, true);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSimplelabelCfgT vertexCfg2 = {(int32_t)endValue, endValue * 2, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg2, 2, true);
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSimplelabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue * 3, 0, 1, GMC_OPERATION_MERGE};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg3, 3, true);

    ret = TestDownGradeVertexLabel(g_labelName, schemaVersion, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 核查
    GtSimplelabelCfgRead pkReadCfg = {startValue, endValue, 0, (int32_t)endValue * 3, {false,true,true}};
    GtSimplelabelCfgRead pkReadCfg2 = {(int32_t)endValue, endValue * 2, 0, (int32_t)endValue * 3, {false,false,true}};
    GtSimplelabelCfgRead pkReadCfg3 = {(int32_t)endValue * 2, endValue * 3, 0, (int32_t)endValue * 3,
                                       {false,false,false}};
    for (uint32_t keyId = 0; keyId < 5; keyId++) {
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg, 0, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg2, 0, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg3, 0, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 011.label存在多个版本号，写入数据，对旧版本号进行删除后，对其余版本号进行普通insert
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DowngradeTest, Upgrade_002_003_011)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int fetchNum = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10;
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    char *schemaUpdateParth = (char *)"./schemaFile/SimpleLabelUpgradeFullFields.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SimpleLabelUpgradeFullFields2.gmjson";
    uint32_t schemaVersion = 0;

    GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_INSERT};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg1, 0, true);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSimplelabelCfgT vertexCfg2 = {(int32_t)endValue, endValue * 2, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg2, 2, true);
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSimplelabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue * 3, 0, 1, GMC_OPERATION_MERGE};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg3, 3, true);

    ret = TestDownGradeVertexLabel(g_labelName, schemaVersion, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GtSimplelabelCfgT vertexCfg4 = {(int32_t)endValue * 3, endValue * 4, 0, 1, GMC_OPERATION_INSERT};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg4, 0, true);
    GtSimplelabelCfgT vertexCfg5 = {(int32_t)endValue * 4, endValue * 5, 0, 1, GMC_OPERATION_INSERT};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg5, 0, true);
    // 核查
    GtSimplelabelCfgRead pkReadCfg = {startValue, endValue, 0, (int32_t)endValue * 5, {false,true,true}};
    GtSimplelabelCfgRead pkReadCfg2 = {(int32_t)endValue, endValue * 2, 0, (int32_t)endValue * 5, {false,false,true}};
    GtSimplelabelCfgRead pkReadCfg3 = {(int32_t)endValue * 2, endValue * 3, 0, (int32_t)endValue * 5,
                                       {false,false,false}};
    GtSimplelabelCfgRead pkReadCfg4 = {(int)endValue * 3, endValue * 4, 0, (int32_t)endValue * 5, {false,true,true}};
    GtSimplelabelCfgRead pkReadCfg5 = {(int)endValue * 4, endValue * 5, 0, (int32_t)endValue * 5, {false,false,false}};
    for (uint32_t keyId = 0; keyId < 5; keyId++) {
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg, 0, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg2, 0, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg3, 0, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg4, 0, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg5, 0, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 012.label存在多个版本号，写入数据，对旧版本号进行删除后，对其余版本号进行普通replace
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DowngradeTest, Upgrade_002_003_012)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int fetchNum = 0;
    int32_t startValue = 0;
    uint32_t endValue = 1;
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    char *schemaUpdateParth = (char *)"./schemaFile/SimpleLabelUpgradeFullFields.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SimpleLabelUpgradeFullFields2.gmjson";
    uint32_t schemaVersion = 0;

    GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_INSERT};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg1, 0, true);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSimplelabelCfgT vertexCfg2 = {(int32_t)endValue, endValue * 2, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg2, 2, true);
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSimplelabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue * 3, 0, 1, GMC_OPERATION_MERGE};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg3, 3, true);

    GtSimplelabelCfgRead pkReadCfg2 = {(int32_t)endValue * 2, endValue * 3, 0, (int32_t)endValue * 3, {false,false,false}};
    ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg2, 3, 0, true);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = TestDownGradeVertexLabel(g_labelName, schemaVersion, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GtSimplelabelCfgT vertexCfg4 = {startValue, endValue * 3, 0, 2, GMC_OPERATION_REPLACE};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg4, schemaVersion, false);
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg4, schemaVersion, false);

    GtSimplelabelCfgT vertexCfg5 = {(int32_t)endValue * 4, endValue * 5, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg5, schemaVersion, true);
    GtSimplelabelCfgT vertexCfg6 = {(int32_t)endValue * 5, endValue * 6, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg6, schemaVersion, true);

    // 核查
    GtSimplelabelCfgRead pkReadCfg = {startValue, endValue * 3, 0, (int32_t)endValue * 5, {false,false,false}};
    GtSimplelabelCfgRead pkReadCfg4 = {(int)endValue * 4, endValue * 5, 0, (int32_t)endValue * 5, {false,false,true}};
    GtSimplelabelCfgRead pkReadCfg3 = {(int)endValue * 5, endValue * 6, 0, (int32_t)endValue * 5, {false,false,false}};

    for (uint32_t keyId = 0; keyId < 5; keyId++) {
        AW_FUN_Log(LOG_INFO, "keyId = %d \n", keyId);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg, schemaVersion, keyId, false);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg4, schemaVersion, keyId, true);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg3, schemaVersion, keyId, true);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 013.label存在多个版本号，写入数据，对旧版本号进行删除后，对其余版本号进行普通merge
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DowngradeTest, Upgrade_002_003_013)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int fetchNum = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10;
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    char *schemaUpdateParth = (char *)"./schemaFile/SimpleLabelUpgradeFullFields.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SimpleLabelUpgradeFullFields2.gmjson";
    uint32_t schemaVersion = 0;

    GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_INSERT};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg1, 0, true);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSimplelabelCfgT vertexCfg2 = {(int32_t)endValue, endValue * 2, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg2, 2, true);
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSimplelabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue * 3, 0, 1, GMC_OPERATION_MERGE};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg3, 3, true);
    ret = TestDownGradeVertexLabel(g_labelName, schemaVersion, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // merge-update
    int32_t updateValue = 600;
    vertexCfg1.optType = GMC_OPERATION_MERGE;
    vertexCfg1.expAffectRows = 2;
    vertexCfg2.optType = GMC_OPERATION_MERGE;
    vertexCfg2.expAffectRows = 2;
    vertexCfg3.optType = GMC_OPERATION_MERGE;
    vertexCfg3.expAffectRows = 2;
    TestSimpleTNewOldVersionUpdate(g_stmt, vertexCfg1, 0, 1, false, updateValue);
    TestSimpleTNewOldVersionUpdate(g_stmt, vertexCfg2, 0, 1, false, updateValue);
    TestSimpleTNewOldVersionUpdate(g_stmt, vertexCfg3, 0, 1, false, updateValue);
    // merge-insert
    GtSimplelabelCfgT vertexCfg4 = {(int32_t)endValue * 3, endValue * 4, 0, 1, GMC_OPERATION_MERGE};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg4, 0, true);
    GtSimplelabelCfgT vertexCfg5 = {(int32_t)endValue * 5, endValue * 6, 0, 1, GMC_OPERATION_MERGE};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg5, 0, true);

    // 核查
    GtSimplelabelCfgRead pkReadCfg = {(int)endValue, endValue * 3, 0, (int32_t)endValue * 5, {false,false,false}};
    GtSimplelabelCfgRead pkReadCfg2 = {0, endValue, 0, (int32_t)endValue * 5, {false,true,true}};
    GtSimplelabelCfgRead pkReadCfg3 = {(int)endValue * 3, endValue * 4, 0, (int32_t)endValue * 5, {false,true,true}};
    GtSimplelabelCfgRead pkReadCfg4 = {(int)endValue * 5, endValue * 6, 0, (int32_t)endValue * 5, {false,false,false}};
    for (uint32_t keyId = 0; keyId < 5; keyId++) {
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg, 0, keyId, false, updateValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg2, 0, keyId, false, updateValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg3, 0, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg4, 0, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 014.label存在多个版本号，写入数据，对旧版本号进行删除后，对其余版本号进行普通update
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DowngradeTest, Upgrade_002_003_014)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int fetchNum = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10;
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    char *schemaUpdateParth = (char *)"./schemaFile/SimpleLabelUpgradeFullFields.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SimpleLabelUpgradeFullFields2.gmjson";
    uint32_t schemaVersion = 0;

    GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_INSERT};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg1, 0, true);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSimplelabelCfgT vertexCfg2 = {(int32_t)endValue, endValue * 2, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg2, 2, true);
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSimplelabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue * 3, 0, 1, GMC_OPERATION_MERGE};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg3, 3, true);

    ret = TestDownGradeVertexLabel(g_labelName, 2, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t updateValue = 300;
    vertexCfg1.optType = GMC_OPERATION_UPDATE;
    vertexCfg2.optType = GMC_OPERATION_UPDATE;
    vertexCfg3.optType = GMC_OPERATION_UPDATE;

    TestSimpleTNewOldVersionUpdate(g_stmt, vertexCfg1, schemaVersion, 1, false, updateValue);
    TestSimpleTNewOldVersionUpdate(g_stmt, vertexCfg2, schemaVersion, 2, false, updateValue);
    TestSimpleTNewOldVersionUpdate(g_stmt, vertexCfg3, schemaVersion, 0, false, updateValue);

    // 核查
    GtSimplelabelCfgRead pkReadCfg = {(int)endValue, endValue * 3, 0, (int32_t)endValue * 3, {false,false,false}};
    GtSimplelabelCfgRead pkReadCfg2 = {0, endValue, 0, (int32_t)endValue * 3, {false,true,true}};
    for (uint32_t keyId = 0; keyId < 5; keyId++) {
        ret = TestSimpleTNewOldVersionRead2(g_stmt, pkReadCfg, 2, keyId, false, updateValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead2(g_stmt, pkReadCfg2, 2, keyId, false, updateValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 015.label存在多个版本号，写入数据，对旧版本号进行删除后，对其余版本号进行普通delete
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DowngradeTest, Upgrade_002_003_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int fetchNum = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10;
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    char *schemaUpdateParth = (char *)"./schemaFile/SimpleLabelUpgradeFullFields.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SimpleLabelUpgradeFullFields2.gmjson";
    uint32_t schemaVersion = 2;

    GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_INSERT};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg1, 0, true);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSimplelabelCfgT vertexCfg2 = {(int32_t)endValue, endValue * 2, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg2, 2, true);
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSimplelabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue * 3, 0, 1, GMC_OPERATION_MERGE};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg3, 3, true);

    ret = TestDownGradeVertexLabel(g_labelName, schemaVersion, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除
    TestSimpleT2Delete(g_stmt, g_labelName, startValue, endValue, 0, 0);
    TestSimpleT2Delete(g_stmt, g_labelName, endValue, endValue * 3, 1, 2);
    // 核查
    fetchNum = 0;
    TestSimpleT1LocalScan(g_stmt, g_labelName, startValue, endValue * 3, 0, &fetchNum);
    AW_MACRO_ASSERT_EQ_INT(0, fetchNum);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 016.label存在多个版本号，写入数据，对旧版本号进行删除后，对其余版本号进行结构化读写更新
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DowngradeTest, Upgrade_002_003_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int fetchNum = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10;
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    char *schemaUpdateParth = (char *)"./schemaFile/SimpleLabelUpgradeFullFields.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SimpleLabelUpgradeFullFields2.gmjson";
    uint32_t schemaVersion = 2;

    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 老版本结构化写
    schemaVersion = 0;
    GtSimplelabelStructCfgT vertexCfg = {startValue, endValue, 0, 1, 0};
    ret = GtSimplelabel0StructInsertOrReplace(g_stmt, g_labelName, vertexCfg, schemaVersion, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 中间版本结构化写
    schemaVersion = 2;
    GtSimplelabelStructCfgT vertexCfg2 = {(int32_t)endValue, endValue, 0, 1, 0};
    ret = GtSimplelabel2StructInsertOrReplace(g_stmt, g_labelName, vertexCfg2, schemaVersion, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 最新版本结构化写
    schemaVersion = 3;
    GtSimplelabelStructCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue, 0, 1, 0};
    ret = GtSimplelabel5StructInsertOrReplace(g_stmt, g_labelName, vertexCfg3, schemaVersion, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = TestDownGradeVertexLabel(g_labelName, 2, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 结构化写
    GtSimplelabelStructCfgT vertexCfg4 = {(int)endValue * 3, endValue, 0, 1, 0};
    ret = GtSimplelabel0StructInsertOrReplace(g_stmt, g_labelName, vertexCfg4, 0, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSimplelabelStructCfgT vertexCfg5 = {(int)endValue * 4, endValue, 0, 1, 0};
    ret = GtSimplelabel2StructInsertOrReplace(g_stmt, g_labelName, vertexCfg5, 2, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 结构化读
    schemaVersion = 0;
    vertexCfg.expAffectRows = endValue * 5;
    for (uint32_t keyId = 0; keyId < 5; keyId++) {
        ret = GtSimplelabel0StructIndexScan(g_stmt, g_labelName, vertexCfg, keyId, schemaVersion);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (keyId != 3) {
            ret = GtSimplelabel0StructIndexScan(g_stmt, g_labelName, vertexCfg2, keyId, schemaVersion);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GtSimplelabel0StructIndexScan(g_stmt, g_labelName, vertexCfg3, keyId, schemaVersion);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GtSimplelabel0StructIndexScan(g_stmt, g_labelName, vertexCfg4, keyId, schemaVersion);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GtSimplelabel0StructIndexScan(g_stmt, g_labelName, vertexCfg5, keyId, schemaVersion);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }

    schemaVersion = 2;
    bool isNull[2] = {true, true};
    for (uint32_t keyId = 0; keyId < 5; keyId++) {
        isNull[0] = true;
        isNull[1] = true;
        ret = GtSimplelabel2StructIndexScan(g_stmt, g_labelName, vertexCfg, keyId, 2, true, 0, isNull[0]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (keyId != 3) {
            ret = GtSimplelabel2StructIndexScan(g_stmt, g_labelName, vertexCfg4, keyId, 2, true, 0, isNull[0]);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            isNull[0] = false;
            ret = GtSimplelabel2StructIndexScan(g_stmt, g_labelName, vertexCfg2, keyId, 2, true, 0, isNull[0]);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            isNull[1] = false;
            ret = GtSimplelabel2StructIndexScan(g_stmt, g_labelName, vertexCfg3, keyId, 2, true, 0, isNull[0]);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GtSimplelabel2StructIndexScan(g_stmt, g_labelName, vertexCfg5, keyId, 2, true, 0, isNull[0]);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 017.label存在多个版本号，写入数据，对旧版本号进行删除后，对其余版本号进行批量读写更新
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DowngradeTest, Upgrade_002_003_017)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int fetchNum = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10;
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    char *schemaUpdateParth = (char *)"./schemaFile/SimpleLabelUpgradeFullFields.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SimpleLabelUpgradeFullFields2.gmjson";
    uint32_t schemaVersion = 2;
    int32_t updateValue = 500;

    GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_INSERT};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg1, 0, true);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSimplelabelCfgT vertexCfg2 = {(int32_t)endValue, endValue * 2, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg2, 2, true);

    GtSimplelabelCfgRead pkReadCfgx = {(int32_t)endValue, endValue * 2, 0, (int32_t)endValue * 2, {false,false,true}};
    ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfgx, 2, 0, true, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSimplelabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue * 3, 0, 1, GMC_OPERATION_MERGE};
    TestSimpleTNewOldVersionWrite(g_stmt, vertexCfg3, 3, true);

    ret = TestDownGradeVertexLabel(g_labelName, schemaVersion, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSimpleT1InsertOrReplaceBatch(g_conn, g_stmt, g_labelName, (int32_t)endValue * 3, endValue * 4,
        2, GMC_OPERATION_INSERT, true);
    TestSimpleT1InsertOrReplaceBatch(g_conn, g_stmt, g_labelName, (int32_t)endValue * 4, endValue * 5,
        0, GMC_OPERATION_INSERT, true);
    // merge-update
    TestSimpleT1MergeOrUpdateBatch(g_conn, g_stmt, g_labelName, startValue, endValue,
        2, GMC_OPERATION_MERGE, false, updateValue);
    TestSimpleT1MergeOrUpdateBatch(g_conn, g_stmt, g_labelName, endValue, endValue * 2,
        0, GMC_OPERATION_MERGE, false, updateValue);
    TestSimpleT1MergeOrUpdateBatch(g_conn, g_stmt, g_labelName, endValue * 2, endValue * 3,
        2, GMC_OPERATION_MERGE, false, updateValue);
    // 核查
    GtSimplelabelCfgRead pkReadCfg = {startValue, endValue, 0, (int32_t)endValue * 5, {false,false,true}};
    GtSimplelabelCfgRead pkReadCfg2 = {(int32_t)endValue, endValue * 2, 0, (int32_t)endValue * 5, {false,false,false}};
    GtSimplelabelCfgRead pkReadCfg3 = {(int32_t)endValue * 2, endValue * 3, 0, (int32_t)endValue * 5,
                                       {false,false,false}};
    GtSimplelabelCfgRead pkReadCfg4 = {(int)endValue * 3, endValue * 4, 0, (int32_t)endValue * 5, {false,false,true}};
    GtSimplelabelCfgRead pkReadCfg5 = {(int)endValue * 4, endValue * 5, 0, (int32_t)endValue * 5, {false,true,true}};
    for (uint32_t keyId = 0; keyId < 3; keyId++) {
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg, 2, keyId, false, updateValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg, 0, keyId, false, updateValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead2(g_stmt, pkReadCfg2, 2, keyId, false, updateValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg2, 0, keyId, false, updateValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg3, 0, keyId, false, updateValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg4, 0, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg4, 2, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg5, 0, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSimpleTNewOldVersionRead(g_stmt, pkReadCfg5, 2, keyId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 018.label存在多个版本号，写入数据，对表从大对象回退到小对象后，对其余版本号进行普通insert写和读数据操作
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DowngradeTest, Upgrade_002_003_018)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test_start");
    int ret = 0;
    int64_t startValue = 0;
    int64_t endValue = 40;
#ifdef ENV_RTOSV2X
    endValue = 5;
#endif
    int fetchNum = 0;
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    int32_t schemaVersion = 3;
    char *schemaUpdateParth = (char *)"./schemaFile/generalLabel1_Upgrade_1M_BigObject.gmjson";
    char *schemaParth = (char *)"./schemaFile/generalLabel1.gmjson";
    // 创表
    ret = TestCreateLabel(g_stmt, schemaParth, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // InsertOrReplace插入数据
    TestGeneralT1Write(
        g_stmt, g_labelName3, startValue, endValue, 0, (char *)"string", 0, GMC_OPERATION_INSERT);
    TestGeneralT1Write(
        g_stmt, g_labelName3, endValue, endValue * 2, 0, (char *)"string", schemaVersion, GMC_OPERATION_INSERT);
    ret = TestDownGradeVertexLabel(g_labelName3, 0, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 旧版本读
    schemaVersion = 0;
    TestGeneralT1Write(
        g_stmt, g_labelName3, endValue * 2, endValue * 3, 0, (char *)"string", schemaVersion, GMC_OPERATION_INSERT);
    // 主键读
    TestGeneralT1PkScan(g_stmt, g_labelName3, startValue, endValue * 3, (char *)"string", 0, schemaVersion);

    // hashcluster读
    TestGeneralT1HashclusterScan(g_stmt, g_labelName3, startValue, endValue * 3, (char *)"string", 0, schemaVersion);
    ret = GmcDropVertexLabel(g_stmt, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 019.label存在多个版本号，写入数据，对表从大对象回退到小对象后，对其余版本号进行普通replace写和读数据操作
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DowngradeTest, Upgrade_002_003_019)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    int ret = 0;
    int64_t startValue = 0;
    int64_t endValue = 40;
#ifdef ENV_RTOSV2X
    endValue = 5;
#endif
    int fetchNum = 0;
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    uint32_t schemaVersion = 3;
    char *schemaUpdateParth = (char *)"./schemaFile/generalLabel1_Upgrade_1M_BigObject.gmjson";
    char *schemaParth = (char *)"./schemaFile/generalLabel1.gmjson";
    char bigString = 'b';
    // 创表
    ret = TestCreateLabel(g_stmt, schemaParth, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // InsertOrReplace插入数据
    TestGeneralT1Write(
        g_stmt, g_labelName3, startValue, endValue, 0, (char *)"string", 0, GMC_OPERATION_INSERT);
    TestGeneralT1Write(
        g_stmt, g_labelName3, endValue, endValue * 2, 0, (char *)"string", schemaVersion, GMC_OPERATION_INSERT);
    ret = TestDownGradeVertexLabel(g_labelName3, 0, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 旧版本写
    schemaVersion = 0;
    TestGeneralT1Write(
        g_stmt, g_labelName3, endValue * 2, endValue * 3, 0, (char *)"test11", schemaVersion, GMC_OPERATION_REPLACE,
        1, false, bigString);

    TestGeneralT1Write(
        g_stmt, g_labelName3, startValue, endValue * 2, 0, (char *)"test11", schemaVersion, GMC_OPERATION_REPLACE,
        2, false, bigString);
    // 主键读
    TestGeneralT1PkScan(g_stmt, g_labelName3, startValue, endValue * 3, (char *)"test11", 0, schemaVersion, false,
        0, bigString);

    // hashcluster读
    TestGeneralT1HashclusterScan(g_stmt, g_labelName3, startValue, endValue * 3, (char *)"test11", 0, schemaVersion,
        false, 0, bigString);
    ret = GmcDropVertexLabel(g_stmt, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 020.label存在多个版本号，写入数据，对表从大对象回退到小对象后，对其余版本号进行普通merge写和读数据操作
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DowngradeTest, Upgrade_002_003_020)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test_start");
    int ret = 0;
    int64_t startValue = 0;
    int64_t endValue = 40;
#ifdef ENV_RTOSV2X
    endValue = 5;
#endif
    int fetchNum = 0;
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    uint32_t schemaVersion = 3;
    char *schemaUpdateParth = (char *)"./schemaFile/generalLabel1_Upgrade_1M_BigObject.gmjson";
    char *schemaParth = (char *)"./schemaFile/generalLabel1.gmjson";
    char bigString = 'b';
    int64_t updateValue = 1000;
    // 创表
    ret = TestCreateLabel(g_stmt, schemaParth, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // InsertOrReplace插入数据
    TestGeneralT1Write(
        g_stmt, g_labelName3, startValue, endValue, 0, (char *)"string", 0, GMC_OPERATION_INSERT);
    TestGeneralT1Write(
        g_stmt, g_labelName3, endValue, endValue * 2, 0, (char *)"string", schemaVersion, GMC_OPERATION_INSERT);
    ret = TestDownGradeVertexLabel(g_labelName3, 0, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 旧版本写
    schemaVersion = 0;
    TestGeneralT1Write(
        g_stmt, g_labelName3, endValue * 2, endValue * 3, 0, (char *)"string", schemaVersion, GMC_OPERATION_MERGE,
        1, true, bigString);

    TestGeneralT1MergeUpdate(
        g_stmt, g_labelName3, startValue, endValue * 2, (char *)"test11", schemaVersion, GMC_OPERATION_MERGE,
        false, updateValue, bigString);
    // 主键读
    TestGeneralT1PkScan(g_stmt, g_labelName3, startValue, endValue * 2, (char *)"test11", 0, schemaVersion, false,
        updateValue, bigString);
    TestGeneralT1PkScan(g_stmt, g_labelName3, endValue * 2, endValue * 3, (char *)"string", 0, schemaVersion, true,
        0, bigString);
    // hashcluster读
    TestGeneralT1HashclusterScan(g_stmt, g_labelName3, startValue, endValue * 2, (char *)"test11", 0, schemaVersion,
        false, updateValue, bigString);
    TestGeneralT1HashclusterScan(g_stmt, g_labelName3, endValue * 2, endValue * 3, (char *)"string", 0, schemaVersion,
        true, 0, bigString);

    ret = GmcDropVertexLabel(g_stmt, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 021.label存在多个版本号，写入数据，对表从大对象回退到小对象后，对其余版本号进行普通update写和读数据操作
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DowngradeTest, Upgrade_002_003_021)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test_start");
    int ret = 0;
    int64_t startValue = 0;
    int64_t endValue = 50;
#ifdef ENV_RTOSV2X
    endValue = 5;
#endif
    int fetchNum = 0;
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    uint32_t schemaVersion = 3;
    char *schemaUpdateParth = (char *)"./schemaFile/generalLabel1_Upgrade_1M_BigObject.gmjson";
    char *schemaParth = (char *)"./schemaFile/generalLabel1.gmjson";
    char bigString = 'b';
    int64_t updateValue = 1000;
    // 创表
    ret = TestCreateLabel(g_stmt, schemaParth, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // InsertOrReplace插入数据
    TestGeneralT1Write(
        g_stmt, g_labelName3, startValue, endValue, 0, (char *)"string", 0, GMC_OPERATION_INSERT);
    TestGeneralT1Write(
        g_stmt, g_labelName3, endValue, endValue * 2, 0, (char *)"string", schemaVersion, GMC_OPERATION_INSERT);
    ret = TestDownGradeVertexLabel(g_labelName3, 0, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 旧版本写
    schemaVersion = 0;
    TestGeneralT1MergeUpdate(
        g_stmt, g_labelName3, startValue, endValue * 2, (char *)"test11", schemaVersion, GMC_OPERATION_UPDATE,
        false, updateValue, bigString);
    // 主键读
    TestGeneralT1PkScan(g_stmt, g_labelName3, startValue, endValue * 2, (char *)"test11", 0, schemaVersion, false,
        updateValue, bigString);
    // hashcluster读
    TestGeneralT1HashclusterScan(g_stmt, g_labelName3, startValue, endValue * 2, (char *)"test11", 0, schemaVersion,
        false, updateValue, bigString);
    ret = GmcDropVertexLabel(g_stmt, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 022.label存在多个版本号，写入数据，对表从大对象回退到小对象后，对其余版本号进行普通delete写和读数据操作
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DowngradeTest, Upgrade_002_003_022)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test_start");
    int ret = 0;
    int64_t startValue = 0;
    int64_t endValue = 50;
#ifdef ENV_RTOSV2X
    endValue = 5;
#endif
    int fetchNum = 0;
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    uint32_t schemaVersion = 3;
    char *schemaUpdateParth = (char *)"./schemaFile/generalLabel1_Upgrade_1M_BigObject.gmjson";
    char *schemaParth = (char *)"./schemaFile/generalLabel1.gmjson";
    // 创表
    ret = TestCreateLabel(g_stmt, schemaParth, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // InsertOrReplace插入数据
    TestGeneralT1Write(
        g_stmt, g_labelName3, startValue, endValue, 0, (char *)"string", 0, GMC_OPERATION_INSERT, true);
    TestGeneralT1Write(
        g_stmt, g_labelName3, endValue, endValue * 2, 0, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true);
    ret = TestDownGradeVertexLabel(g_labelName3, 0, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 旧版本读
    schemaVersion = 0;
    // 主键delete
    TestGeneralT1PkDelete(g_stmt, g_labelName3, startValue, endValue * 2, schemaVersion);
    // local读
    TestGeneralT1LocalScan(
        g_stmt, g_labelName3, startValue, endValue * 2, (char *)"string", 0, schemaVersion, &fetchNum, true);
    AW_MACRO_ASSERT_EQ_INT(0, fetchNum);
    ret = GmcDropVertexLabel(g_stmt, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 023.label存在多个版本号，写入数据，对表从大对象回退到小对象后，对其余版本号进行结构化insert写和读数据操作
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DowngradeTest, Upgrade_002_003_023)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10;
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    bool isDefaultValue = true;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    uint32_t schemaVersion = 1;
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SpecialTableSchemaBigObj.gmjson";
    char *schemaParth = (char *)"./schemaFile/SpecialTableSchema.gmjson";

    // 创表
    ret = TestCreateLabel(g_stmt, schemaParth, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    GtSpeciallabelCfgT vertexCfg2 = {(int32_t)endValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_REPLACE};
    GtSpeciallabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue, 0, 1, 0, 3, 3, 2, GMC_OPERATION_REPLACE};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfg, bytesValue, stringValue, isDefaultValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg2, bytesValue, stringValue, isDefaultValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel3StructWrite(g_stmt, vertexCfg3, bytesValue, stringValue, isDefaultValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = TestDownGradeVertexLabel(g_labelName2, schemaVersion, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 其余版本写数据
    GtSpeciallabelCfgT vertexCfg4 = {(int32_t)endValue * 3, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    GtSpeciallabelCfgT vertexCfg5 = {(int32_t)endValue * 4, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_INSERT};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfg4, bytesValue, stringValue, isDefaultValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg5, bytesValue, stringValue, isDefaultValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    vertexCfg.optType = GMC_OPERATION_SCAN;
    vertexCfg.expAffectRows = endValue * 5;
    vertexCfg.fieldIsNull[1] = true;
    vertexCfg.fieldIsNull[2] = true;

    vertexCfg4.optType = GMC_OPERATION_SCAN;
    vertexCfg4.expAffectRows = endValue * 5;
    vertexCfg4.fieldIsNull[1] = true;
    vertexCfg4.fieldIsNull[2] = true;

    vertexCfg2.optType = GMC_OPERATION_SCAN;
    vertexCfg2.expAffectRows = endValue * 5;
    vertexCfg2.fieldIsNull[1] = false;
    vertexCfg2.fieldIsNull[2] = true;

    vertexCfg5.optType = GMC_OPERATION_SCAN;
    vertexCfg5.expAffectRows = endValue * 5;
    vertexCfg5.fieldIsNull[1] = false;
    vertexCfg5.fieldIsNull[2] = true;

    vertexCfg3.optType = GMC_OPERATION_SCAN;
    vertexCfg3.expAffectRows = endValue * 5;
    vertexCfg3.fieldIsNull[1] = false;
    vertexCfg3.fieldIsNull[2] = false;

    for (uint32_t keyId = 0; keyId < 5; keyId++) {
        AW_FUN_Log(LOG_INFO, "keyId %d\n", keyId);
        vertexCfg.schemaVersion = 1;
        vertexCfg2.schemaVersion = 1;
        vertexCfg3.schemaVersion = 1;
        vertexCfg4.schemaVersion = 1;
        vertexCfg5.schemaVersion = 1;
        ret = GtSpeciallabel2StructIndexScan(g_stmt, vertexCfg, keyId, bytesValue, stringValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GtSpeciallabel2StructIndexScan(g_stmt, vertexCfg2, keyId, bytesValue, stringValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GtSpeciallabel2StructIndexScan(g_stmt, vertexCfg3, keyId, bytesValue, stringValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GtSpeciallabel2StructIndexScan(g_stmt, vertexCfg4, keyId, bytesValue, stringValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GtSpeciallabel2StructIndexScan(g_stmt, vertexCfg5, keyId, bytesValue, stringValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        vertexCfg.schemaVersion = 0;
        vertexCfg2.schemaVersion = 0;
        vertexCfg3.schemaVersion = 0;
        vertexCfg4.schemaVersion = 0;
        vertexCfg5.schemaVersion = 0;
        ret = GtSpeciallabelStructIndexScan(g_stmt, vertexCfg, keyId, bytesValue, stringValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GtSpeciallabelStructIndexScan(g_stmt, vertexCfg2, keyId, bytesValue, stringValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GtSpeciallabelStructIndexScan(g_stmt, vertexCfg3, keyId, bytesValue, stringValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GtSpeciallabelStructIndexScan(g_stmt, vertexCfg4, keyId, bytesValue, stringValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GtSpeciallabelStructIndexScan(g_stmt, vertexCfg5, keyId, bytesValue, stringValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
 
    // 全表扫描
    GtSpeciallabelCfgT pkReadCfg = {startValue, endValue * 5, 0, (int32_t)endValue * 5, 0, 3, 3, 0, GMC_OPERATION_SCAN};
    ret = TestSpecialTWholeRead(g_stmt, pkReadCfg, bytesValue, stringValue, isDefaultValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    pkReadCfg.schemaVersion = 1;
    ret = TestSpecialTWholeRead(g_stmt, pkReadCfg, bytesValue, stringValue, isDefaultValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 024.label存在多个版本号，写入数据，对表从大对象回退到小对象后，对其余版本号进行结构化replace写和读数据操作
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DowngradeTest, Upgrade_002_003_024)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10;
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    bool isDefaultValue = false;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    uint32_t schemaVersion = 1;
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SpecialTableSchemaBigObj.gmjson";
    char *schemaParth = (char *)"./schemaFile/SpecialTableSchema.gmjson";

    // 创表
    ret = TestCreateLabel(g_stmt, schemaParth, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    GtSpeciallabelCfgT vertexCfg2 = {(int32_t)endValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_REPLACE};
    GtSpeciallabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue, 0, 1, 0, 3, 3, 2, GMC_OPERATION_REPLACE};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel3StructWrite(g_stmt, vertexCfg3, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = TestDownGradeVertexLabel(g_labelName2, schemaVersion, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 其余版本写数据
    vertexCfg.optType = GMC_OPERATION_REPLACE;
    vertexCfg.schemaVersion = 1;
    vertexCfg.expAffectRows = 2;
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg, bytesValue, stringValue, isDefaultValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    vertexCfg2.schemaVersion = 0;
    vertexCfg2.expAffectRows = 2;
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfg2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    vertexCfg2.schemaVersion = 1;
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg2, bytesValue, stringValue, isDefaultValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    vertexCfg3.schemaVersion = 1;
    vertexCfg3.expAffectRows = 2;
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg3, bytesValue, stringValue, isDefaultValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 其余版本写数据
    GtSpeciallabelCfgT vertexCfg4 = {(int32_t)endValue * 3, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_REPLACE};
    GtSpeciallabelCfgT vertexCfg5 = {(int32_t)endValue * 4, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_REPLACE};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfg4, bytesValue, stringValue, isDefaultValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg5, bytesValue, stringValue, isDefaultValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    vertexCfg.optType = GMC_OPERATION_SCAN;
    vertexCfg.expAffectRows = endValue * 5;
    vertexCfg.fieldIsNull[1] = false;
    vertexCfg.fieldIsNull[2] = true;

    vertexCfg2.optType = GMC_OPERATION_SCAN;
    vertexCfg2.expAffectRows = endValue * 5;
    vertexCfg2.fieldIsNull[1] = false;
    vertexCfg2.fieldIsNull[2] = true;

    vertexCfg3.optType = GMC_OPERATION_SCAN;
    vertexCfg3.expAffectRows = endValue * 5;
    vertexCfg3.fieldIsNull[1] = false;
    vertexCfg3.fieldIsNull[2] = true;

    vertexCfg4.optType = GMC_OPERATION_SCAN;
    vertexCfg4.expAffectRows = endValue * 5;
    vertexCfg4.fieldIsNull[1] = true;
    vertexCfg4.fieldIsNull[2] = true;

    vertexCfg5.optType = GMC_OPERATION_SCAN;
    vertexCfg5.expAffectRows = endValue * 5;
    vertexCfg5.fieldIsNull[1] = false;
    vertexCfg5.fieldIsNull[2] = true;
    AW_FUN_Log(LOG_INFO, "not struct read \n");
    vertexCfg4.schemaVersion = 1;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg4, 0, bytesValue, stringValue, isDefaultValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    for (uint32_t keyId = 0; keyId < 1; keyId++) {
        AW_FUN_Log(LOG_INFO, "keyId %d\n", keyId);
        vertexCfg.schemaVersion = 1;
        vertexCfg2.schemaVersion = 1;
        vertexCfg3.schemaVersion = 1;
        vertexCfg4.schemaVersion = 1;
        vertexCfg5.schemaVersion = 1;

        ret = GtSpeciallabel2StructIndexScan(g_stmt, vertexCfg, keyId, bytesValue, stringValue, isDefaultValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GtSpeciallabel2StructIndexScan(g_stmt, vertexCfg2, keyId, bytesValue, stringValue, isDefaultValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GtSpeciallabel2StructIndexScan(g_stmt, vertexCfg3, keyId, bytesValue, stringValue, isDefaultValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GtSpeciallabel2StructIndexScan(g_stmt, vertexCfg4, keyId, bytesValue, stringValue, isDefaultValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GtSpeciallabel2StructIndexScan(g_stmt, vertexCfg5, keyId, bytesValue, stringValue, isDefaultValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        vertexCfg.schemaVersion = 0;
        vertexCfg2.schemaVersion = 0;
        vertexCfg3.schemaVersion = 0;
        vertexCfg4.schemaVersion = 0;
        vertexCfg5.schemaVersion = 0;
        ret = GtSpeciallabelStructIndexScan(g_stmt, vertexCfg, keyId, bytesValue, stringValue, isDefaultValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GtSpeciallabelStructIndexScan(g_stmt, vertexCfg2, keyId, bytesValue, stringValue, isDefaultValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GtSpeciallabelStructIndexScan(g_stmt, vertexCfg3, keyId, bytesValue, stringValue, isDefaultValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GtSpeciallabelStructIndexScan(g_stmt, vertexCfg4, keyId, bytesValue, stringValue, isDefaultValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GtSpeciallabelStructIndexScan(g_stmt, vertexCfg5, keyId, bytesValue, stringValue, isDefaultValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(g_stmt, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 025.label存在多个版本号，写入数据，对表从大对象回退到小对象后，对其余版本号进行结构化merge写和读数据操作
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DowngradeTest, Upgrade_002_003_025)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10;
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    bool isDefaultValue = false;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    uint32_t schemaVersion = 1;
    int32_t updateValue = 500;
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SpecialTableSchemaBigObj.gmjson";
    char *schemaParth = (char *)"./schemaFile/SpecialTableSchema.gmjson";

    // 创表
    ret = TestCreateLabel(g_stmt, schemaParth, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    GtSpeciallabelCfgT vertexCfg2 = {(int32_t)endValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_REPLACE};
    GtSpeciallabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue, 0, 1, 0, 3, 3, 2, GMC_OPERATION_REPLACE};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel3StructWrite(g_stmt, vertexCfg3, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = TestDownGradeVertexLabel(g_labelName2, schemaVersion, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 其余版本写数据
    vertexCfg.optType = GMC_OPERATION_MERGE;
    vertexCfg.schemaVersion = 1;
    vertexCfg.expAffectRows = 2;
    vertexCfg.coefficient = updateValue;
    ret = GtSpeciallabel3StructUpdateOrMerge(g_stmt, vertexCfg, 0, bytesValue, stringValue, isDefaultValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    vertexCfg2.optType = GMC_OPERATION_MERGE;
    vertexCfg2.schemaVersion = 0;
    vertexCfg2.expAffectRows = 2;
    ret = GtSpeciallabel3StructUpdateOrMerge(g_stmt, vertexCfg2, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    vertexCfg2.schemaVersion = 1;
    vertexCfg2.coefficient = updateValue;
    ret = GtSpeciallabel3StructUpdateOrMerge(g_stmt, vertexCfg2, 0, bytesValue, stringValue, isDefaultValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    vertexCfg3.optType = GMC_OPERATION_MERGE;
    vertexCfg3.schemaVersion = 1;
    vertexCfg3.expAffectRows = 2;
    vertexCfg3.coefficient = updateValue;
    ret = GtSpeciallabel3StructUpdateOrMerge(g_stmt, vertexCfg3, 0, bytesValue, stringValue, isDefaultValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 其余版本写数据
    GtSpeciallabelCfgT vertexCfg4 = {(int32_t)endValue * 3, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_MERGE};
    GtSpeciallabelCfgT vertexCfg5 = {(int32_t)endValue * 4, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_MERGE};
    ret = GtSpeciallabel3StructUpdateOrMerge(g_stmt, vertexCfg4, 0, bytesValue, stringValue, isDefaultValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel3StructUpdateOrMerge(g_stmt, vertexCfg5, 1, bytesValue, stringValue, isDefaultValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    vertexCfg.optType = GMC_OPERATION_SCAN;
    vertexCfg.expAffectRows = endValue * 5;
    vertexCfg.fieldIsNull[1] = false;
    vertexCfg.fieldIsNull[2] = true;
    vertexCfg.coefficient = updateValue;

    vertexCfg2.optType = GMC_OPERATION_SCAN;
    vertexCfg2.expAffectRows = endValue * 5;
    vertexCfg2.fieldIsNull[1] = false;
    vertexCfg2.fieldIsNull[2] = true;
    vertexCfg2.coefficient = updateValue;

    vertexCfg3.optType = GMC_OPERATION_SCAN;
    vertexCfg3.expAffectRows = endValue * 5;
    vertexCfg3.fieldIsNull[1] = false;
    vertexCfg3.fieldIsNull[2] = true;
    vertexCfg3.coefficient = updateValue;

    vertexCfg4.optType = GMC_OPERATION_SCAN;
    vertexCfg4.expAffectRows = endValue * 5;
    vertexCfg4.fieldIsNull[1] = true;
    vertexCfg4.fieldIsNull[2] = true;

    vertexCfg5.optType = GMC_OPERATION_SCAN;
    vertexCfg5.expAffectRows = endValue * 5;
    vertexCfg5.fieldIsNull[1] = false;
    vertexCfg5.fieldIsNull[2] = true;

    for (uint32_t keyId = 0; keyId < 5; keyId++) {
        AW_FUN_Log(LOG_INFO, "keyId %d\n", keyId);
        vertexCfg.schemaVersion = 1;
        vertexCfg2.schemaVersion = 1;
        vertexCfg3.schemaVersion = 1;
        vertexCfg4.schemaVersion = 1;
        vertexCfg5.schemaVersion = 1;
        ret = GtSpeciallabel2StructIndexScan(g_stmt, vertexCfg, keyId, bytesValue, stringValue, isDefaultValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GtSpeciallabel2StructIndexScan(g_stmt, vertexCfg2, keyId, bytesValue, stringValue, isDefaultValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GtSpeciallabel2StructIndexScan(g_stmt, vertexCfg3, keyId, bytesValue, stringValue, isDefaultValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GtSpeciallabel2StructIndexScan(g_stmt, vertexCfg4, keyId, bytesValue, stringValue, isDefaultValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GtSpeciallabel2StructIndexScan(g_stmt, vertexCfg5, keyId, bytesValue, stringValue, isDefaultValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        vertexCfg.schemaVersion = 0;
        vertexCfg2.schemaVersion = 0;
        vertexCfg3.schemaVersion = 0;
        vertexCfg4.schemaVersion = 0;
        vertexCfg5.schemaVersion = 0;
        ret = GtSpeciallabelStructIndexScan(g_stmt, vertexCfg, keyId, bytesValue, stringValue, isDefaultValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GtSpeciallabelStructIndexScan(g_stmt, vertexCfg2, keyId, bytesValue, stringValue, isDefaultValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GtSpeciallabelStructIndexScan(g_stmt, vertexCfg3, keyId, bytesValue, stringValue, isDefaultValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GtSpeciallabelStructIndexScan(g_stmt, vertexCfg4, keyId, bytesValue, stringValue, isDefaultValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GtSpeciallabelStructIndexScan(g_stmt, vertexCfg5, keyId, bytesValue, stringValue, isDefaultValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(g_stmt, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 026.label存在多个版本号，写入数据，对表从大对象回退到小对象后，对其余版本号进行结构化update写和读数据操作
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DowngradeTest, Upgrade_002_003_026)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10;
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    bool isDefaultValue = false;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    uint32_t schemaVersion = 1;
    int32_t updateValue = 500;
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SpecialTableSchemaBigObj.gmjson";
    char *schemaParth = (char *)"./schemaFile/SpecialTableSchema.gmjson";

    // 创表
    ret = TestCreateLabel(g_stmt, schemaParth, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    GtSpeciallabelCfgT vertexCfg2 = {(int32_t)endValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_REPLACE};
    GtSpeciallabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue, 0, 1, 0, 3, 3, 2, GMC_OPERATION_REPLACE};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel3StructWrite(g_stmt, vertexCfg3, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = TestDownGradeVertexLabel(g_labelName2, schemaVersion, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 其余版本写数据
    vertexCfg.optType = GMC_OPERATION_UPDATE;
    vertexCfg.schemaVersion = 1;
    vertexCfg.expAffectRows = 1;
    vertexCfg.coefficient = updateValue;
    ret = GtSpeciallabel3StructUpdateOrMerge(g_stmt, vertexCfg, 0, bytesValue, stringValue, isDefaultValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    vertexCfg2.optType = GMC_OPERATION_UPDATE;
    vertexCfg2.schemaVersion = 0;
    vertexCfg2.expAffectRows = 1;
    ret = GtSpeciallabel3StructUpdateOrMerge(g_stmt, vertexCfg2, 1, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    vertexCfg2.schemaVersion = 1;
    vertexCfg2.coefficient = updateValue;
    ret = GtSpeciallabel3StructUpdateOrMerge(g_stmt, vertexCfg2, 2, bytesValue, stringValue, isDefaultValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    vertexCfg3.optType = GMC_OPERATION_UPDATE;
    vertexCfg3.schemaVersion = 1;
    vertexCfg3.expAffectRows = 1;
    vertexCfg3.coefficient = updateValue;
    ret = GtSpeciallabel3StructUpdateOrMerge(g_stmt, vertexCfg3, 0, bytesValue, stringValue, isDefaultValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    vertexCfg.optType = GMC_OPERATION_SCAN;
    vertexCfg.expAffectRows = endValue * 3;
    vertexCfg.fieldIsNull[1] = false;
    vertexCfg.fieldIsNull[2] = true;
    vertexCfg.coefficient = updateValue;

    vertexCfg2.optType = GMC_OPERATION_SCAN;
    vertexCfg2.expAffectRows = endValue * 3;
    vertexCfg2.fieldIsNull[1] = false;
    vertexCfg2.fieldIsNull[2] = true;
    vertexCfg2.coefficient = updateValue;

    vertexCfg3.optType = GMC_OPERATION_SCAN;
    vertexCfg3.expAffectRows = endValue * 3;
    vertexCfg3.fieldIsNull[1] = false;
    vertexCfg3.fieldIsNull[2] = true;
    vertexCfg3.coefficient = updateValue;

    for (uint32_t keyId = 0; keyId < 5; keyId++) {
        AW_FUN_Log(LOG_INFO, "keyId %d\n", keyId);
        for (uint32_t id = 0; id < 2; id++) {
            vertexCfg.schemaVersion = id;
            vertexCfg2.schemaVersion = id;
            vertexCfg3.schemaVersion = id;
            ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, keyId, bytesValue, stringValue, false);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg2, keyId, bytesValue, stringValue, false);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg3, keyId, bytesValue, stringValue, false);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
        vertexCfg.schemaVersion = 1;
        vertexCfg2.schemaVersion = 1;
        vertexCfg3.schemaVersion = 1;
        ret = GtSpeciallabel2StructIndexScan(g_stmt, vertexCfg, keyId, bytesValue, stringValue, false);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GtSpeciallabel2StructIndexScan(g_stmt, vertexCfg2, keyId, bytesValue, stringValue, false);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GtSpeciallabel2StructIndexScan(g_stmt, vertexCfg3, keyId, bytesValue, stringValue, false);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        vertexCfg.schemaVersion = 0;
        vertexCfg2.schemaVersion = 0;
        vertexCfg3.schemaVersion = 0;
        ret = GtSpeciallabelStructIndexScan(g_stmt, vertexCfg, keyId, bytesValue, stringValue, false);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GtSpeciallabelStructIndexScan(g_stmt, vertexCfg2, keyId, bytesValue, stringValue, false);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GtSpeciallabelStructIndexScan(g_stmt, vertexCfg3, keyId, bytesValue, stringValue, false);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(g_stmt, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 027.label存在多个版本号，写入数据，对表从大对象回退到小对象后，对其余版本号进行结构化delete写和读数据操作
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DowngradeTest, Upgrade_002_003_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10;
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    bool isDefaultValue = false;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    uint32_t schemaVersion = 1;
    int32_t updateValue = 500;
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SpecialTableSchemaBigObj.gmjson";
    char *schemaParth = (char *)"./schemaFile/SpecialTableSchema.gmjson";

    // 创表
    ret = TestCreateLabel(g_stmt, schemaParth, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    GtSpeciallabelCfgT vertexCfg2 = {(int32_t)endValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_REPLACE};
    GtSpeciallabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue, 0, 1, 0, 3, 3, 2, GMC_OPERATION_REPLACE};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel3StructWrite(g_stmt, vertexCfg3, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = TestDownGradeVertexLabel(g_labelName2, schemaVersion, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 其余版本写数据
    // 批量delete
    vertexCfg.optType = GMC_OPERATION_DELETE;
    vertexCfg.schemaVersion = 1;
    ret = GtSpeciallabel2StructBatchDelete(g_conn, g_stmt, vertexCfg, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    vertexCfg2.schemaVersion = 0;
    vertexCfg2.optType = GMC_OPERATION_DELETE;
    vertexCfg2.expAffectRows = endValue * 2;
    ret = GtSpeciallabel3StructDelete(g_stmt, vertexCfg2, 3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    vertexCfg3.optType = GMC_OPERATION_SCAN;
    vertexCfg3.schemaVersion = 0;
    vertexCfg3.expAffectRows = 0;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg3, 3, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg3.schemaVersion = 1;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg3, 3, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
 
    ret = GmcDropVertexLabel(g_stmt, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 028.label存在多个版本号，写入数据，对表从大对象回退到小对象后，对最新版本号进行DML订阅推送
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DowngradeTest, Upgrade_002_003_028)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10;
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    bool isDefaultValue = false;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SpecialTableSchemaBigObj.gmjson";
    char *schemaParth = (char *)"./schemaFile/SpecialTableSchema.gmjson";

    // 创表
    ret = TestCreateLabel(g_stmt, schemaParth, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 写数据
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT, {false, true, true}};
    GtSpeciallabelCfgT vertexCfg2 = {(int32_t)endValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_INSERT,
                                     {false, false, true}};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfg, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg2, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = TestDownGradeVertexLabel(g_labelName2, 1, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 订阅
    char *subInfo = NULL;
    int chanRingLen = 64;
    char subConnName[128] = "subTabel";
    GmcStmtT *stmtSub = NULL;
    GmcConnT *testSubConn = NULL;
    char subName[128] = "subSpecialLabel";
    SnUserDataT *userData = NULL;
    ret = testSubConnect(&testSubConn, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    readJanssonFile("schemaFile/specialTSub.gmjson", &subInfo);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, subInfo);
    userData = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData, 0, sizeof(SnUserDataT));
    GmcSubConfigT tmpSubInfo;
    tmpSubInfo.subsName = subName;
    tmpSubInfo.configJson = subInfo;
    ret = GmcSubscribe(g_stmt, &tmpSubInfo, testSubConn, SubSpecialTCallBackWithOldVersion, userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(subInfo);
    // insert
    GtSpeciallabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_INSERT,
                                      {false, false, true}};
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg3, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INSERT, (int32_t)endValue * 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // replace
    GtSpeciallabelCfgT vertexCfg4 = {startValue, endValue * 3, 0, 2, 0, 3, 3, 1, GMC_OPERATION_REPLACE,
                                     {false, false, true}};
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg4, bytesValue, stringValue, true);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_REPLACE, (int32_t)endValue * 3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // merge-inert
    GtSpeciallabelCfgT vertexCfg5 = {(int32_t)endValue * 3, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_MERGE,
                                     {false, false, true}};
    ret = GtSpeciallabel3StructUpdateOrMerge(g_stmt, vertexCfg5, 0, bytesValue, stringValue, true);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_MERGE_INSERT, (int32_t)endValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // merge update数据
    vertexCfg4.coefficient = 300;
    vertexCfg4.optType = GMC_OPERATION_MERGE;
    ret = GtSpeciallabel3StructUpdateOrMerge(g_stmt, vertexCfg4, 0, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_MERGE_UPDATE, (int32_t)endValue * 3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // update
    vertexCfg4.coefficient = 0;
    vertexCfg4.expAffectRows = 1;
    vertexCfg4.optType = GMC_OPERATION_UPDATE;
    ret = GtSpeciallabel3StructUpdateOrMerge(g_stmt, vertexCfg4, 0, bytesValue, stringValue, true);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_UPDATE, (int32_t)endValue * 3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // delete
    vertexCfg4.schemaVersion = 1;
    vertexCfg4.optType = GMC_OPERATION_DELETE;
    vertexCfg4.expAffectRows = 40;
    ret = GtSpeciallabel3StructDelete(g_stmt, vertexCfg4, 3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 取消订阅
    ret = GmcUnSubscribe(g_stmt, subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 释放订阅连接
    ret = testSubDisConnect(testSubConn, stmtSub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(userData);
    ret = GmcDropVertexLabel(g_stmt, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 029.一般复杂表添加node节点升级，进行普通insert写和读数据操作
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DowngradeTest, Upgrade_002_003_029)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test_start");
    int ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    char *expectValue = (char *)"upgrade successfully";
    int32_t schemaVersion = 0;
    char *schemaUpdateParth = (char *)"./schemaFile/generalLabelNode.gmjson";
    char *schemaParth = (char *)"./schemaFile/generalLabel1.gmjson";
    bool isDefaultValue = false;
    uint8_t ipValue = 0;
    // 创表
    ret = TestCreateLabel(g_stmt, schemaParth, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtGeneralLabelCfg vertexCfg = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_INSERT, {false, true, true}};
    GtGeneralLabelCfg vertexCfg2 = {(int)endValue, endValue * 2, 1, 3, 3, 2, GMC_OPERATION_INSERT, {false, false}};
    // Insert插入数据
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfg, ipValue, bytesValue, stringValue, isDefaultValue);
    isDefaultValue = true;
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfg2, ipValue, bytesValue, stringValue, isDefaultValue);
    isDefaultValue = false;
    for (int32_t keyId = 0; keyId < 5; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg keyId = %d\n", keyId);
        vertexCfg.schemaVersion = 0;
        TestGeneralT2NewOldVersionRead(g_stmt, vertexCfg, keyId, ipValue, bytesValue, stringValue, isDefaultValue);
        vertexCfg.schemaVersion = 2;
        TestGeneralT2NewOldVersionRead(g_stmt, vertexCfg, keyId, ipValue, bytesValue, stringValue, isDefaultValue);
    }
    isDefaultValue = true;
    for (int32_t keyId = 0; keyId < 4; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg2 keyId = %d\n", keyId);
        vertexCfg.schemaVersion = 0;
        TestGeneralT2NewOldVersionRead(g_stmt, vertexCfg2, keyId, ipValue, bytesValue, stringValue, isDefaultValue);
        vertexCfg.schemaVersion = 2;
        TestGeneralT2NewOldVersionRead(g_stmt, vertexCfg2, keyId, ipValue, bytesValue, stringValue, isDefaultValue);
    }
    ret = GmcDropVertexLabel(g_stmt, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 030.一般复杂表添加node节点升级，进行普通replace写和读数据操作
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DowngradeTest, Upgrade_002_003_030)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    int ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    int32_t schemaVersion = 0;
    char *schemaUpdateParth = (char *)"./schemaFile/generalLabelNode.gmjson";
    char *schemaParth = (char *)"./schemaFile/generalLabel1.gmjson";
    bool isDefaultValue = false;
    uint8_t ipValue = 0;
    // 创表
    ret = TestCreateLabel(g_stmt, schemaParth, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtGeneralLabelCfg vertexCfg = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_REPLACE, {false, false}};
    GtGeneralLabelCfg vertexCfg2 = {(int)endValue, endValue * 2, 1, 3, 3, 2, GMC_OPERATION_REPLACE, {false, true}};
    // replace插入新数据
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfg, ipValue, bytesValue, stringValue, isDefaultValue);
    isDefaultValue = true;
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfg2, ipValue, bytesValue, stringValue, isDefaultValue);
    // replace旧数据
    vertexCfg.schemaVersion = 2;
    vertexCfg.expAffectRows = 2;
    vertexCfg2.schemaVersion = 0;
    vertexCfg2.expAffectRows = 2;
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfg, ipValue, bytesValue, stringValue, isDefaultValue);
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfg2, ipValue, bytesValue, stringValue, isDefaultValue);

    for (int32_t keyId = 0; keyId < 5; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg keyId = %d\n", keyId);
        vertexCfg.schemaVersion = 0;
        TestGeneralT2NewOldVersionRead(g_stmt, vertexCfg, keyId, ipValue, bytesValue, stringValue, isDefaultValue);
        vertexCfg.schemaVersion = 2;
        TestGeneralT2NewOldVersionRead(g_stmt, vertexCfg, keyId, ipValue, bytesValue, stringValue, isDefaultValue);
    }
    for (int32_t keyId = 0; keyId < 4; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg2 keyId = %d\n", keyId);
        vertexCfg.schemaVersion = 0;
        TestGeneralT2NewOldVersionRead(g_stmt, vertexCfg2, keyId, ipValue, bytesValue, stringValue, isDefaultValue);
        vertexCfg.schemaVersion = 2;
        TestGeneralT2NewOldVersionRead(g_stmt, vertexCfg2, keyId, ipValue, bytesValue, stringValue, isDefaultValue);
    }
    ret = GmcDropVertexLabel(g_stmt, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 031.一般复杂表添加node节点升级，进行普通merge写和读数据操作
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DowngradeTest, Upgrade_002_003_031)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    int ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    char *expectValue = (char *)"upgrade successfully";
    int32_t schemaVersion = 0;
    char *schemaUpdateParth = (char *)"./schemaFile/generalLabelNode.gmjson";
    char *schemaParth = (char *)"./schemaFile/generalLabel1.gmjson";
    bool isDefaultValue = false;
    uint8_t ipValue = 0;
    int32_t updateValue = 500;
    // 创表
    ret = TestCreateLabel(g_stmt, schemaParth, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtGeneralLabelCfg vertexCfg = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_MERGE, {false, false}};
    GtGeneralLabelCfg vertexCfg2 = {(int)endValue, endValue * 2, 1, 3, 3, 2, GMC_OPERATION_MERGE, {false, false}};
    // merge插入新数据
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfg, ipValue, bytesValue, stringValue, isDefaultValue);
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfg2, ipValue, bytesValue, stringValue, isDefaultValue);
    // merge update旧数据
    vertexCfg.schemaVersion = 2;
    vertexCfg.expAffectRows = 2;
    TestGeneralT2NewOldVersionMergeUpdate(g_stmt, vertexCfg, 0, bytesValue, stringValue, isDefaultValue, updateValue);
    vertexCfg2.schemaVersion = 0;
    vertexCfg2.expAffectRows = 2;
    TestGeneralT2NewOldVersionMergeUpdate(g_stmt, vertexCfg2, 0, bytesValue, stringValue, isDefaultValue, updateValue);

    for (int32_t keyId = 0; keyId < 5; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg keyId = %d\n", keyId);
        vertexCfg.schemaVersion = 0;
        TestGeneralT2NewOldVersionRead(g_stmt, vertexCfg, keyId, ipValue, bytesValue, stringValue, isDefaultValue,
            updateValue, true);
        vertexCfg.schemaVersion = 2;
        TestGeneralT2NewOldVersionRead(g_stmt, vertexCfg, keyId, ipValue, bytesValue, stringValue, isDefaultValue,
            updateValue, true);
    }
    for (int32_t keyId = 0; keyId < 4; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg2 keyId = %d\n", keyId);
        vertexCfg.schemaVersion = 0;
        TestGeneralT2NewOldVersionRead(g_stmt, vertexCfg2, keyId, ipValue, bytesValue, stringValue, isDefaultValue,
            updateValue, false);
        vertexCfg.schemaVersion = 2;
        TestGeneralT2NewOldVersionRead(g_stmt, vertexCfg2, keyId, ipValue, bytesValue, stringValue, isDefaultValue,
            updateValue, false);
    }
    ret = GmcDropVertexLabel(g_stmt, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 032.一般复杂表添加node节点升级，进行普通update写和读数据操作
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DowngradeTest, Upgrade_002_003_032)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    int ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    char *expectValue = (char *)"upgrade successfully";
    int32_t schemaVersion = 0;
    char *schemaUpdateParth = (char *)"./schemaFile/generalLabelNode.gmjson";
    char *schemaParth = (char *)"./schemaFile/generalLabel1.gmjson";
    bool isDefaultValue = false;
    uint8_t ipValue = 0;
    int32_t updateValue = 500;
    // 创表
    ret = TestCreateLabel(g_stmt, schemaParth, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtGeneralLabelCfg vertexCfg = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_MERGE, {false, false}};
    GtGeneralLabelCfg vertexCfg2 = {(int)endValue, endValue * 2, 1, 3, 3, 2, GMC_OPERATION_MERGE, {false, false}};
    // merge插入新数据
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfg, ipValue, bytesValue, stringValue, isDefaultValue);
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfg2, ipValue, bytesValue, stringValue, isDefaultValue);
    //update旧数据
    vertexCfg.schemaVersion = 2;
    vertexCfg.optType = GMC_OPERATION_UPDATE;
    TestGeneralT2NewOldVersionMergeUpdate(g_stmt, vertexCfg, 1, bytesValue, stringValue, isDefaultValue, updateValue);
    vertexCfg2.schemaVersion = 0;
    vertexCfg2.optType = GMC_OPERATION_UPDATE;
    TestGeneralT2NewOldVersionMergeUpdate(g_stmt, vertexCfg2, 2, bytesValue, stringValue, isDefaultValue, updateValue);

    for (int32_t keyId = 0; keyId < 5; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg keyId = %d\n", keyId);
        vertexCfg.schemaVersion = 0;
        TestGeneralT2NewOldVersionRead(g_stmt, vertexCfg, keyId, ipValue, bytesValue, stringValue, isDefaultValue,
            updateValue, true);
        vertexCfg.schemaVersion = 2;
        TestGeneralT2NewOldVersionRead(g_stmt, vertexCfg, keyId, ipValue, bytesValue, stringValue, isDefaultValue,
            updateValue, true);
    }
    for (int32_t keyId = 0; keyId < 4; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg2 keyId = %d\n", keyId);
        vertexCfg.schemaVersion = 0;
        TestGeneralT2NewOldVersionRead(g_stmt, vertexCfg2, keyId, ipValue, bytesValue, stringValue, isDefaultValue,
            updateValue, false);
        vertexCfg.schemaVersion = 2;
        TestGeneralT2NewOldVersionRead(g_stmt, vertexCfg2, keyId, ipValue, bytesValue, stringValue, isDefaultValue,
            updateValue, false);
    }
    ret = GmcDropVertexLabel(g_stmt, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 033.一般复杂表添加node节点升级，进行普通delete写和读数据操作
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DowngradeTest, Upgrade_002_003_033)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    int ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    char *expectValue = (char *)"upgrade successfully";
    int32_t schemaVersion = 0;
    char *schemaUpdateParth = (char *)"./schemaFile/generalLabelNode.gmjson";
    char *schemaParth = (char *)"./schemaFile/generalLabel1.gmjson";
    bool isDefaultValue = false;
    uint8_t ipValue = 0;
    uint32_t fetchNum = 0;
    // 创表
    ret = TestCreateLabel(g_stmt, schemaParth, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtGeneralLabelCfg vertexCfg = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_MERGE, {false, false}};
    GtGeneralLabelCfg vertexCfg2 = {(int)endValue, endValue * 2, 1, 3, 3, 2, GMC_OPERATION_MERGE, {false, false}};
    // merge插入新数据
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfg, ipValue, bytesValue, stringValue, isDefaultValue);
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfg2, ipValue, bytesValue, stringValue, isDefaultValue);

    // 主键delete
    TestGeneralT1PkDelete(g_stmt, g_labelName3, startValue, endValue * 2, schemaVersion);

    TestGeneralT2LocalScan(g_stmt, g_labelName3, startValue, endValue * 2, schemaVersion, &fetchNum);
    AW_MACRO_ASSERT_EQ_INT(0, fetchNum);
    ret = GmcDropVertexLabel(g_stmt, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 034.一般复杂表添加node节点升级后，进行订阅推送
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DowngradeTest, Upgrade_002_003_034)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    int ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    char *expectValue = (char *)"upgrade successfully";
    int32_t schemaVersion = 0;
    char *schemaUpdateParth = (char *)"./schemaFile/generalLabelNode.gmjson";
    char *schemaParth = (char *)"./schemaFile/generalLabel1.gmjson";
    bool isDefaultValue = true;
    uint8_t ipValue = 0;
    uint32_t fetchNum = 0;
    // 创表
    ret = TestCreateLabel(g_stmt, schemaParth, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 订阅
    char *subInfo = NULL;
    int chanRingLen = 64;
    char subConnName[128] = "subTabel";
    GmcStmtT *stmtSub = NULL;
    GmcConnT *testSubConn = NULL;
    char subName[128] = "subGeneralLabel";
    SnUserDataT *userData = NULL;
    ret = testSubConnect(&testSubConn, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    readJanssonFile("schemaFile/generalTSub.gmjson", &subInfo);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, subInfo);
    userData = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData, 0, sizeof(SnUserDataT));
    GmcSubConfigT tmpSubInfo;
    tmpSubInfo.subsName = subName;
    tmpSubInfo.configJson = subInfo;
    ret = GmcSubscribe(g_stmt, &tmpSubInfo, testSubConn, SubGeneralTCallBackWithNewVersion, userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(subInfo);
    GtGeneralLabelCfg vertexCfg = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_INSERT, {false, true}};
    GtGeneralLabelCfg vertexCfg2 = {(int)endValue, endValue * 2, 1, 3, 3, 2, GMC_OPERATION_INSERT, {false, false}};
    // 插入新数据
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfg, ipValue, bytesValue, stringValue, isDefaultValue);
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfg2, ipValue, bytesValue, stringValue, isDefaultValue);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INSERT, (int32_t)endValue * 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 主键delete
    TestGeneralT1PkDelete(g_stmt, g_labelName3, startValue, endValue * 2, schemaVersion);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_DELETE, (int32_t)endValue * 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 插入新数据
    vertexCfg.optType = GMC_OPERATION_REPLACE;
    vertexCfg2.optType = GMC_OPERATION_REPLACE;
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfg, ipValue, bytesValue, stringValue, isDefaultValue);
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfg2, ipValue, bytesValue, stringValue, isDefaultValue);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_REPLACE, (int32_t)endValue * 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 取消订阅
    ret = GmcUnSubscribe(g_stmt, subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(userData);
    // 释放订阅连接
    ret = testSubDisConnect(testSubConn, stmtSub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 035.一般复杂表添加node节点升级写数据，降级再读写
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DowngradeTest, Upgrade_002_003_035)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test_start");
    int ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    int32_t schemaVersion = 2;
    char *schemaUpdateParth = (char *)"./schemaFile/generalLabelNode.gmjson";
    char *schemaParth = (char *)"./schemaFile/generalLabel1.gmjson";
    bool isDefaultValue = false;
    uint8_t ipValue = 0;
    // 创表
    ret = TestCreateLabel(g_stmt, schemaParth, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtGeneralLabelCfg vertexCfg = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_REPLACE, {false, false}};
    GtGeneralLabelCfg vertexCfg2 = {(int)endValue, endValue * 2, 1, 3, 3, 2, GMC_OPERATION_REPLACE, {false, true}};
    // replace插入新数据
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfg, ipValue, bytesValue, stringValue, isDefaultValue);
    isDefaultValue = true;
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfg2, ipValue, bytesValue, stringValue, isDefaultValue);
    ret = TestDownGradeVertexLabel(g_labelName3, 0, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // replace旧数据
    vertexCfg.schemaVersion = 0xffffffff;
    vertexCfg.expAffectRows = 2;
    vertexCfg2.schemaVersion = 0xffffffff;
    vertexCfg2.expAffectRows = 2;
    isDefaultValue = true;
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfg, ipValue, bytesValue, stringValue, isDefaultValue);
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfg2, ipValue, bytesValue, stringValue, isDefaultValue);

    GtGeneralLabelCfg vertexCfg3 = {(int)endValue * 2, endValue * 3, 1, 3, 3, 0xffffffff, GMC_OPERATION_INSERT};
    // insert插入新数据
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfg3, ipValue, bytesValue, stringValue, isDefaultValue);

    for (int32_t keyId = 0; keyId < 5; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg keyId = %d\n", keyId);
        TestGeneralT2NewOldVersionRead(g_stmt, vertexCfg, keyId, ipValue, bytesValue, stringValue, isDefaultValue);
        vertexCfg.schemaVersion = 0;
        TestGeneralT2NewOldVersionRead(g_stmt, vertexCfg, keyId, ipValue, bytesValue, stringValue, isDefaultValue);
    }
    for (int32_t keyId = 0; keyId < 4; keyId++) {
        AW_FUN_Log(LOG_STEP, "scan vertexCfg2 keyId = %d\n", keyId);
        TestGeneralT2NewOldVersionRead(g_stmt, vertexCfg2, keyId, ipValue, bytesValue, stringValue, isDefaultValue);
        TestGeneralT2NewOldVersionRead(g_stmt, vertexCfg3, keyId, ipValue, bytesValue, stringValue, isDefaultValue);
        vertexCfg2.schemaVersion = 0;
        TestGeneralT2NewOldVersionRead(g_stmt, vertexCfg2, keyId, ipValue, bytesValue, stringValue, isDefaultValue);
    }
    ret = GmcDropVertexLabel(g_stmt, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 036.一般复杂表添加node节点升级后写数据，进行降级再订阅推送
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DowngradeTest, Upgrade_002_003_036)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test_start");
    int ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    int32_t schemaVersion = 2;
    char *schemaUpdateParth = (char *)"./schemaFile/generalLabelNode.gmjson";
    char *schemaParth = (char *)"./schemaFile/generalLabel1.gmjson";
    bool isDefaultValue = true;
    uint8_t ipValue = 0;
    uint32_t fetchNum = 0;
    // 创表
    ret = TestCreateLabel(g_stmt, schemaParth, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GtGeneralLabelCfg vertexCfg = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_REPLACE, {false, false}};
    GtGeneralLabelCfg vertexCfg2 = {(int)endValue, endValue * 2, 1, 3, 3, 2, GMC_OPERATION_REPLACE, {false, true}};
    // replace插入新数据
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfg, ipValue, bytesValue, stringValue, isDefaultValue);
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfg2, ipValue, bytesValue, stringValue, isDefaultValue);
    ret = TestDownGradeVertexLabel(g_labelName3, 0, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 订阅
    char *subInfo = NULL;
    int chanRingLen = 64;
    char subConnName[128] = "subTabel";
    GmcStmtT *stmtSub = NULL;
    GmcConnT *testSubConn = NULL;
    char subName[128] = "subGeneralLabel";
    SnUserDataT *userData = NULL;
    ret = testSubConnect(&testSubConn, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    readJanssonFile("schemaFile/generalTSub2.gmjson", &subInfo);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, subInfo);
    userData = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData, 0, sizeof(SnUserDataT));
    GmcSubConfigT tmpSubInfo;
    tmpSubInfo.subsName = subName;
    tmpSubInfo.configJson = subInfo;
    ret = GmcSubscribe(g_stmt, &tmpSubInfo, testSubConn, SubGeneralTCallBackWithOldVersion, userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(subInfo);

    // 主键delete
    TestGeneralT1PkDelete(g_stmt, g_labelName3, startValue, endValue * 2, 0xffffffff);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_DELETE, (int32_t)endValue * 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg.optType = GMC_OPERATION_INSERT;
    vertexCfg2.optType = GMC_OPERATION_INSERT;
    vertexCfg2.schemaVersion = 0xffffffff;
    // 插入新数据
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfg, ipValue, bytesValue, stringValue, isDefaultValue);
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfg2, ipValue, bytesValue, stringValue, isDefaultValue);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INSERT, (int32_t)endValue * 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmt, subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(userData);
    // 释放订阅连接
    ret = testSubDisConnect(testSubConn, stmtSub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_labelName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 037.特殊复杂表末尾添加node节点后的读写
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DowngradeTest, Upgrade_002_003_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10;
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    bool isDefaultValue = false;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SpecialTableSchemaNode.gmjson";
    char *schemaParth = (char *)"./schemaFile/SpecialTableSchema.gmjson";

    // 创表
    ret = TestCreateLabel(g_stmt, schemaParth, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 写数据
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT, {false, true, true}};
    GtSpeciallabelCfgT vertexCfg2 = {(int32_t)endValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_REPLACE,
                                     {false, false, true}};
    GtSpeciallabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue, 0, 1, 0, 3, 3, 2, GMC_OPERATION_INSERT,
                                     {false, false, false}};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfg, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg2, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel4StructWrite(g_stmt, vertexCfg3, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg.expAffectRows = 30;
    vertexCfg2.expAffectRows = 30;
    vertexCfg3.expAffectRows = 30;
    for (int keyId = 0; keyId < 4; keyId++) {
        ret = TestSpecialT4NewOldVersionGeneralRead(g_stmt, vertexCfg, keyId, bytesValue, stringValue, false);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestSpecialT4NewOldVersionGeneralRead(g_stmt, vertexCfg2, keyId, bytesValue, stringValue, false);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestSpecialT4NewOldVersionGeneralRead(g_stmt, vertexCfg3, keyId, bytesValue, stringValue, false);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(g_stmt, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 038.特殊复杂表末尾添加node节点后降级，降级后的版本读写
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(DowngradeTest, Upgrade_002_003_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int32_t startValue = 0;
    uint32_t endValue = 10;
    char *expectValue = (char *)"upgrade successfully";
    char *expectValue2 = (char *)"degrade successfully";
    bool isDefaultValue = false;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    char *schemaUpdateParth = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    char *schemaUpdateParth2 = (char *)"./schemaFile/SpecialTableSchemaNode.gmjson";
    char *schemaParth = (char *)"./schemaFile/SpecialTableSchema.gmjson";

    // 创表
    ret = TestCreateLabel(g_stmt, schemaParth, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 升级表
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestUpdateVertexLabel(schemaUpdateParth2, expectValue, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 写数据
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT, {false, true, true}};
    GtSpeciallabelCfgT vertexCfg2 = {(int32_t)endValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_INSERT,
                                     {false, false, true}};
    GtSpeciallabelCfgT vertexCfg3 = {(int32_t)endValue * 2, endValue, 0, 1, 0, 3, 3, 2, GMC_OPERATION_INSERT,
                                     {false, false, false}};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfg, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg2, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel4StructWrite(g_stmt, vertexCfg3, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = TestDownGradeVertexLabel(g_labelName2, 1, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GtSpeciallabelCfgT vertexCf4 = {(int32_t)endValue * 3, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT,
                                    {false, true, true}};
    GtSpeciallabelCfgT vertexCfg5 = {(int32_t)endValue * 4, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_REPLACE,
                                     {false, false, true}};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCf4, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg5, bytesValue, stringValue, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    vertexCfg.expAffectRows = endValue * 5;
    vertexCfg2.expAffectRows = endValue * 5;
    vertexCfg3.expAffectRows = endValue * 5;
    vertexCf4.expAffectRows = endValue * 5;
    vertexCfg5.expAffectRows = endValue * 5;
    vertexCfg.schemaVersion = 0;
    vertexCfg2.schemaVersion = 0;
    vertexCfg3.schemaVersion = 0;
    vertexCf4.schemaVersion = 0;
    vertexCfg5.schemaVersion = 0;
    for (int keyId = 0; keyId < 4; keyId++) {
        ret = TestSpecialT4NewOldVersionGeneralRead(g_stmt, vertexCfg, keyId, bytesValue, stringValue, false);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestSpecialT4NewOldVersionGeneralRead(g_stmt, vertexCfg2, keyId, bytesValue, stringValue, false);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestSpecialT4NewOldVersionGeneralRead(g_stmt, vertexCfg3, keyId, bytesValue, stringValue, false);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestSpecialT4NewOldVersionGeneralRead(g_stmt, vertexCf4, keyId, bytesValue, stringValue, false);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestSpecialT4NewOldVersionGeneralRead(g_stmt, vertexCfg5, keyId, bytesValue, stringValue, false);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    vertexCfg.schemaVersion = 1;
    vertexCfg2.schemaVersion = 1;
    vertexCfg3.schemaVersion = 1;
    vertexCf4.schemaVersion = 1;
    vertexCfg5.schemaVersion = 1;
    for (int keyId = 0; keyId < 4; keyId++) {
        ret = TestSpecialT4NewOldVersionGeneralRead(g_stmt, vertexCfg, keyId, bytesValue, stringValue, false);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestSpecialT4NewOldVersionGeneralRead(g_stmt, vertexCfg2, keyId, bytesValue, stringValue, false);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestSpecialT4NewOldVersionGeneralRead(g_stmt, vertexCfg3, keyId, bytesValue, stringValue, false);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestSpecialT4NewOldVersionGeneralRead(g_stmt, vertexCf4, keyId, bytesValue, stringValue, false);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestSpecialT4NewOldVersionGeneralRead(g_stmt, vertexCfg5, keyId, bytesValue, stringValue, false);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(g_stmt, g_labelName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
