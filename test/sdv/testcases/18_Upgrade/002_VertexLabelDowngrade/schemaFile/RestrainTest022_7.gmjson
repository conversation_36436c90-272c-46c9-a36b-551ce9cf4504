[{"type": "record", "name": "<PERSON><PERSON><PERSON><PERSON>", "schema_version": 7, "special_complex": true, "fields": [{"name": "F0", "type": "int64", "nullable": false}, {"name": "F1", "type": "uint64", "nullable": true}, {"name": "F2", "type": "int32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": false}, {"name": "F4", "type": "int16", "nullable": true}, {"name": "F5", "type": "uint16", "nullable": true}, {"name": "F6", "type": "int8", "nullable": true}, {"name": "F7", "type": "uint8", "nullable": false}, {"name": "F8", "type": "boolean", "nullable": true}, {"name": "F9", "type": "float", "nullable": true}, {"name": "F10", "type": "double", "nullable": true}, {"name": "F11", "type": "time", "nullable": true}, {"name": "F12", "type": "char", "nullable": true}, {"name": "F13", "type": "uchar", "nullable": true}, {"name": "F14", "type": "fixed", "nullable": false, "size": 9, "default": "0xffffffffffffffffff"}, {"name": "F15", "type": "uint8: 5", "nullable": false, "default": "0x1f"}, {"name": "F16", "type": "uint16: 10", "nullable": false, "default": "0x3ff"}, {"name": "F17", "type": "uint32: 17", "nullable": false, "default": "0x1ffff"}, {"name": "F18", "type": "uint64: 35", "nullable": false, "default": "0x7ffffffff"}, {"name": "F19", "type": "bitmap", "size": 16}, {"name": "F20", "type": "uint32", "nullable": false}, {"name": "F21", "type": "uint32", "nullable": false}, {"name": "T1_V", "type": "record", "vector": true, "size": 64, "fields": [{"name": "V1", "type": "uint32"}, {"name": "V2", "type": "uint32"}, {"name": "V3", "type": "bitmap", "size": 16}, {"name": "V4", "type": "string", "size": 16}, {"name": "V5", "type": "bytes", "nullable": true}, {"name": "V6", "type": "bytes", "nullable": true, "size": 16}, {"name": "V7", "type": "string", "nullable": true, "size": 16}]}, {"name": "F22", "type": "bytes", "nullable": true}, {"name": "F23", "type": "string", "nullable": true, "size": 16}, {"name": "F24", "type": "string", "nullable": true, "size": 16}, {"name": "F25", "type": "bytes", "nullable": true, "size": 16}, {"name": "F26", "type": "bytes", "nullable": true, "size": 16}, {"name": "F27", "type": "bytes", "nullable": true, "size": 16}, {"name": "F28", "type": "bytes", "nullable": true, "size": 16}], "keys": [{"name": "primary_key", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"name": "hashcluster_unique_key", "index": {"type": "hashcluster"}, "fields": ["F1", "F2"], "constraints": {"unique": true}}, {"name": "localhash_key", "fields": ["F4", "F5"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}, {"name": "local_key", "fields": ["F3"], "index": {"type": "local"}, "constraints": {"unique": false}}, {"name": "lpm4_key", "fields": ["F3", "F20", "F21", "F7"], "index": {"type": "lpm4_tree_bitmap"}, "constraints": {"unique": true}}, {"name": "mem_key", "index": {"type": "none"}, "node": "T1_V", "fields": ["V1"]}]}]