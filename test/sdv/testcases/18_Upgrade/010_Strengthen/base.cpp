/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2012-2018. All rights reserved.
 Description  :表结构升降级用例加固：基础场景
 Author       : l<PERSON><PERSON>hai lwx1068802
 Modification :
 Date         : 2023/11/10
**************************************************************************** */
#include "tools.h"

class base : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void base::SetUpTestCase(){}
void base::TearDownTestCase(){}
void base::SetUp(){}
void base::TearDown(){}

// 定长表升级变长表、升级成大对象表
TEST_F(base, Upgrade_010_005_001)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    system("start.sh -f");

    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建表V0版本
    ret = CreateVertexLabel(g_stmt, "schemaFile/label5.gmjson", g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char cmd[1024];
    (void)snprintf(cmd, 1024, "%s/gmddl -c alter -t label5 -f schemaFile/label5V1.gmjson -u online -ns %s", g_toolPath, g_testNameSpace);
    ret = executeCommand(cmd, "Alter schema upgrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表
    ret = GmcDropVertexLabel(g_stmt, "label5");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcDetachAllShmSeg();
	testEnvClean();
}

void *ThrDoMerge(void *args)
{
    int ret = 0;
    int thrIndex = *((int *)args);
    GmcConnT *thrConn = NULL;
    GmcStmtT *thrStmt = NULL;
    ret = testGmcConnect(&thrConn, &thrStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char ch = 'a';
    uint32_t times = 0;
    while (true) {
        ret = GmcPrepareStmtByLabelNameWithVersion(thrStmt, "label7", thrIndex, GMC_OPERATION_MERGE);
        if (ret != GMERR_OK) {
            break;
        }
        ret = GmcSetIndexKeyValue(thrStmt, 0, GMC_DATATYPE_UINT32, &times, sizeof(uint32_t));
        if (ret != GMERR_OK) {
            break;
        }
        ret = GmcSetIndexKeyName(thrStmt, "primary_key");
        if (ret != GMERR_OK) {
            break;
        }
        ret = GmcExecute(thrStmt);
        if (ret != GMERR_OK) {
            break;
        }

        char fV0[FIXED_LEN_10];
        memset(fV0, 0, FIXED_LEN_10);
        fV0[0] = '0';
        fV0[1] = ch;
        ret = GmcSetVertexProperty(thrStmt, (char *)"FV0", GMC_DATATYPE_FIXED, fV0, FIXED_LEN_10);
        if (ret != GMERR_OK) {
            break;
        }
        if (thrIndex >= 1) {
            char fV1[FIXED_LEN_10];
            memset(fV1, 0, FIXED_LEN_10);
            fV1[0] = '1';
            fV1[1] = ch;
            ret = GmcSetVertexProperty(thrStmt, (char *)"FV1", GMC_DATATYPE_FIXED, fV1, FIXED_LEN_10);
            if (ret != GMERR_OK) {
                break;
            }
        }
        if (thrIndex >= 2) {
            char fV2[FIXED_LEN_10];
            memset(fV2, 0, FIXED_LEN_10);
            fV2[0] = '2';
            fV2[1] = ch;
            ret = GmcSetVertexProperty(thrStmt, (char *)"FV2", GMC_DATATYPE_FIXED, fV2, FIXED_LEN_10);
            if (ret != GMERR_OK) {
                break;
            }
        }
        if (thrIndex >= 3) {
            char fV3[FIXED_LEN_10];
            memset(fV3, 0, FIXED_LEN_10);
            fV3[0] = '3';
            fV3[1] = ch;
            ret = GmcSetVertexProperty(thrStmt, (char *)"FV3", GMC_DATATYPE_FIXED, fV3, FIXED_LEN_10);
            if (ret != GMERR_OK) {
                break;
            }
        }
        if (thrIndex >= 4) {
            char fV4[FIXED_LEN_10];
            memset(fV4, 0, FIXED_LEN_10);
            fV4[0] = '4';
            fV4[1] = ch;
            ret = GmcSetVertexProperty(thrStmt, (char *)"FV4", GMC_DATATYPE_FIXED, fV4, FIXED_LEN_10);
            if (ret != GMERR_OK) {
                break;
            }
        }
        if (thrIndex >= 5) {
            char fV5[FIXED_LEN_10];
            memset(fV5, 0, FIXED_LEN_10);
            fV5[0] = '5';
            fV5[1] = ch;
            ret = GmcSetVertexProperty(thrStmt, (char *)"FV5", GMC_DATATYPE_FIXED, fV5, FIXED_LEN_10);
            if (ret != GMERR_OK) {
                break;
            }
        }
        if (thrIndex >= 6) {
            char fV6[FIXED_LEN_10];
            memset(fV6, 0, FIXED_LEN_10);
            fV6[0] = '6';
            fV6[1] = ch;
            ret = GmcSetVertexProperty(thrStmt, (char *)"FV6", GMC_DATATYPE_FIXED, fV6, FIXED_LEN_10);
            if (ret != GMERR_OK) {
                break;
            }
        }
        if (thrIndex >= 7) {
            char fV7[FIXED_LEN_10];
            memset(fV7, 0, FIXED_LEN_10);
            fV7[0] = '7';
            fV7[1] = ch;
            ret = GmcSetVertexProperty(thrStmt, (char *)"FV7", GMC_DATATYPE_FIXED, fV7, FIXED_LEN_10);
            if (ret != GMERR_OK) {
                break;
            }
        }
        ret = GmcExecute(thrStmt);
        if (ret != GMERR_OK) {
            break;
        }
        times++;
    }
    ret = testGmcDisconnect(thrConn, thrStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

void *ThrDoScan(void *args)
{
    int ret = 0;
    int thrIndex = *((int *)args);
    GmcConnT *thrConn = NULL;
    GmcStmtT *thrStmt = NULL;
    ret = testGmcConnect(&thrConn, &thrStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t times = 0;
    char ch = 'a';
    bool isVersionExist = true;
    while (isVersionExist) {
        ret = GmcPrepareStmtByLabelNameWithVersion(thrStmt, "label7", thrIndex, GMC_OPERATION_SCAN);
        if (ret != GMERR_OK) {
            break;
        }
        ret = GmcSetIndexKeyValue(thrStmt, 0, GMC_DATATYPE_UINT32, &times, sizeof(uint32_t));
        if (ret != GMERR_OK) {
            break;
        }
        ret = GmcSetIndexKeyName(thrStmt, "primary_key");
        if (ret != GMERR_OK) {
            break;
        }
        ret = GmcExecute(thrStmt);
        if (ret != GMERR_OK) {
            break;
        }

        bool isFinish = false;
        ret = GmcFetch(thrStmt, &isFinish);
        if (isFinish) {
            break;
        }
        if (ret != GMERR_OK) {
            break;
        }

        bool isNull = false;
        char expectFV0[FIXED_LEN_10];
        memset(expectFV0, 0, FIXED_LEN_10);
        expectFV0[0] = '0';
        expectFV0[1] = ch;
        char valueFV0[FIXED_LEN_10];
        memset(valueFV0, 0, FIXED_LEN_10);

        ret = GmcGetVertexPropertyByName(thrStmt, "FV0", valueFV0, FIXED_LEN_10, &isNull);
        if (ret == GMERR_OK && isNull == false) {
            EXPECT_EQ(GMERR_OK, memcmp((char *)valueFV0, (char *)expectFV0, FIXED_LEN_10));
        }

        if (thrIndex >= 1) {
            char expectFV1[FIXED_LEN_10];
            memset(expectFV1, 0, FIXED_LEN_10);
            expectFV1[0] = '1';
            expectFV1[1] = ch;
            char valueFV1[FIXED_LEN_10];
            memset(valueFV1, 0, FIXED_LEN_10);
            ret = GmcGetVertexPropertyByName(thrStmt, "FV1", valueFV1, FIXED_LEN_10, &isNull);
            if (ret == GMERR_OK && isNull == false) {
                EXPECT_EQ(GMERR_OK, memcmp((char *)valueFV1, (char *)expectFV1, FIXED_LEN_10));
            } else if (ret != GMERR_NO_DATA) {
                isVersionExist = false;
            }
        }
        if (thrIndex >= 2) {
            char expectFV2[FIXED_LEN_10];
            memset(expectFV2, 0, FIXED_LEN_10);
            expectFV2[0] = '2';
            expectFV2[1] = ch;
            char valueFV2[FIXED_LEN_10];
            memset(valueFV2, 0, FIXED_LEN_10);
            ret = GmcGetVertexPropertyByName(thrStmt, "FV2", valueFV2, FIXED_LEN_10, &isNull);
            if (ret == GMERR_OK && isNull == false) {
                EXPECT_EQ(GMERR_OK, memcmp((char *)valueFV2, (char *)expectFV2, FIXED_LEN_10));
            } else if (ret != GMERR_NO_DATA) {
                isVersionExist = false;
            }
        }
        if (thrIndex >= 3) {
            char expectFV3[FIXED_LEN_10];
            memset(expectFV3, 0, FIXED_LEN_10);
            expectFV3[0] = '3';
            expectFV3[1] = ch;
            char valueFV3[FIXED_LEN_10];
            memset(valueFV3, 0, FIXED_LEN_10);
            ret = GmcGetVertexPropertyByName(thrStmt, "FV3", valueFV3, FIXED_LEN_10, &isNull);
            if (ret == GMERR_OK && isNull == false) {
                EXPECT_EQ(GMERR_OK, memcmp((char *)valueFV3, (char *)expectFV3, FIXED_LEN_10));
            } else if (ret != GMERR_NO_DATA) {
                isVersionExist = false;
            }
        }
        if (thrIndex >= 4) {
            char expectFV4[FIXED_LEN_10];
            memset(expectFV4, 0, FIXED_LEN_10);
            expectFV4[0] = '4';
            expectFV4[1] = ch;
            char valueFV4[FIXED_LEN_10];
            memset(valueFV4, 0, FIXED_LEN_10);
            ret = GmcGetVertexPropertyByName(thrStmt, "FV4", valueFV4, FIXED_LEN_10, &isNull);
            if (ret == GMERR_OK && isNull == false) {
                EXPECT_EQ(GMERR_OK, memcmp((char *)valueFV4, (char *)expectFV4, FIXED_LEN_10));
            } else if (ret != GMERR_NO_DATA) {
                isVersionExist = false;
            }
        }
        if (thrIndex >= 5) {
            char expectFV5[FIXED_LEN_10];
            memset(expectFV5, 0, FIXED_LEN_10);
            expectFV5[0] = '5';
            expectFV5[1] = ch;
            char valueFV5[FIXED_LEN_10];
            memset(valueFV5, 0, FIXED_LEN_10);
            ret = GmcGetVertexPropertyByName(thrStmt, "FV5", valueFV5, FIXED_LEN_10, &isNull);
            if (ret == GMERR_OK && isNull == false) {
                EXPECT_EQ(GMERR_OK, memcmp((char *)valueFV5, (char *)expectFV5, FIXED_LEN_10));
            } else if (ret != GMERR_NO_DATA) {
                isVersionExist = false;
            }
        }
        if (thrIndex >= 6) {
            char expectFV6[FIXED_LEN_10];
            memset(expectFV6, 0, FIXED_LEN_10);
            expectFV6[0] = '6';
            expectFV6[1] = ch;
            char valueFV6[FIXED_LEN_10];
            memset(valueFV6, 0, FIXED_LEN_10);
            ret = GmcGetVertexPropertyByName(thrStmt, "FV6", valueFV6, FIXED_LEN_10, &isNull);
            if (ret == GMERR_OK && isNull == false) {
                EXPECT_EQ(GMERR_OK, memcmp((char *)valueFV6, (char *)expectFV6, FIXED_LEN_10));
            } else if (ret != GMERR_NO_DATA) {
                isVersionExist = false;
            }
        }

        if (thrIndex >= 7) {
            char expectFV7[FIXED_LEN_10];
            memset(expectFV7, 0, FIXED_LEN_10);
            expectFV7[0] = '7';
            expectFV7[1] = ch;
            char valueFV7[FIXED_LEN_10];
            memset(valueFV7, 0, FIXED_LEN_10);
            ret = GmcGetVertexPropertyByName(thrStmt, "FV7", valueFV7, FIXED_LEN_10, &isNull);
            if (ret == GMERR_OK && isNull == false) {
                EXPECT_EQ(GMERR_OK, memcmp((char *)valueFV7, (char *)expectFV7, FIXED_LEN_10));
            } else if (ret != GMERR_NO_DATA) {
                isVersionExist = false;
            }
        }
        times++;
    }
    ret = testGmcDisconnect(thrConn, thrStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

void *ThrDoDeleteAllFast(void *args)
{
    int ret = 0;
    GmcConnT *thrConn = NULL;
    GmcStmtT *thrStmt = NULL;
    ret = testGmcConnect(&thrConn, &thrStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(thrStmt, "label7");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

int g_version = 0;
void *ThrDoDownGradeSeries(void *args)
{
    int ret = 0;
    int thrIndex = *((int *)args);

    char cmd[1024];
    while (g_version != thrIndex) {};
    (void)snprintf(cmd, 1024, "%s/gmddl -c alter -t label7 -d sync -ns %s", g_toolPath, g_testNameSpace);
    usleep(10);
    ret = executeCommand(cmd, "Alter schema degrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_version--;
    return NULL;
}

void *ThrDoView(void *args)
{
    sleep(1);
    // 如何判断此处是合并降级而不是一次一次降级，开发还没提供校验方法
    system("gmsysview -q V\\$QRY_SCHEMA_DEGRADE_INFO -f TABLE_NAME=label7");
    return NULL;
}

// 表合并降级期间并发每个版本都被读写、清表。
TEST_F(base, Upgrade_010_005_002)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    system("start.sh -f");

    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表
    ret = GmcDropVertexLabel(g_stmt, "label7");

    // 建表V0版本
    ret = CreateVertexLabel(g_stmt, "schemaFile/label7.gmjson", g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // V0->V1->V2->V3->V4->V5->V6->V7
    char cmd[1024];
    (void)snprintf(cmd, 1024, "%s/gmddl -c alter -t label7 -f schemaFile/label7V1.gmjson -u online -ns %s", g_toolPath, g_testNameSpace);
    ret = executeCommand(cmd, "Alter schema upgrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(cmd, 1024, "%s/gmddl -c alter -t label7 -f schemaFile/label7V2.gmjson -u online -ns %s", g_toolPath, g_testNameSpace);
    ret = executeCommand(cmd, "Alter schema upgrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(cmd, 1024, "%s/gmddl -c alter -t label7 -f schemaFile/label7V3.gmjson -u online -ns %s", g_toolPath, g_testNameSpace);
    ret = executeCommand(cmd, "Alter schema upgrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(cmd, 1024, "%s/gmddl -c alter -t label7 -f schemaFile/label7V4.gmjson -u online -ns %s", g_toolPath, g_testNameSpace);
    ret = executeCommand(cmd, "Alter schema upgrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(cmd, 1024, "%s/gmddl -c alter -t label7 -f schemaFile/label7V5.gmjson -u online -ns %s", g_toolPath, g_testNameSpace);
    ret = executeCommand(cmd, "Alter schema upgrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(cmd, 1024, "%s/gmddl -c alter -t label7 -f schemaFile/label7V6.gmjson -u online -ns %s", g_toolPath, g_testNameSpace);
    ret = executeCommand(cmd, "Alter schema upgrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(cmd, 1024, "%s/gmddl -c alter -t label7 -f schemaFile/label7V7.gmjson -u online -ns %s", g_toolPath, g_testNameSpace);
    ret = executeCommand(cmd, "Alter schema upgrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int dataNum = 0;
#if defined ENV_RTOSV2
    dataNum = 100;
#else
    dataNum = 10000;
#endif
    for (int i = 0; i < dataNum; i++) {
        ret = MergeAndCheck7(g_stmt, "label7", i, 'a', 7, 7);
    }

    // 连续降级
    int thrNum = 8;
    pthread_t thrDownGradeSeries[thrNum];
    pthread_t thrMerge[thrNum];
    pthread_t thrScan[thrNum];
    pthread_t thrDeleteAllFast;
    pthread_t thrView;
    int thrIndex[thrNum];
    for (int i = 0; i < thrNum; i++) {
        thrIndex[i] = i;
    }
    for (int i = 1; i < thrNum; i++) {
        ret = pthread_create(&thrMerge[i], NULL, ThrDoMerge, (void *)&thrIndex[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = 1; i < thrNum; i++) {
        ret = pthread_create(&thrScan[i], NULL, ThrDoScan, (void *)&thrIndex[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    
    ret = pthread_create(&thrDeleteAllFast, NULL, ThrDoDeleteAllFast, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_version = 7;
    for (int i = 1; i < thrNum; i++) {
        ret = pthread_create(&thrDownGradeSeries[i], NULL, ThrDoDownGradeSeries, (void *)&thrIndex[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = pthread_create(&thrView, NULL, ThrDoView, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 1; i < thrNum; i++) {
        pthread_join(thrMerge[i], NULL);
        pthread_join(thrScan[i], NULL);
    }
    for (int i = 1; i < thrNum; i++) {
        pthread_join(thrDownGradeSeries[i], NULL);
    }
    pthread_join(thrDeleteAllFast, NULL);
    pthread_join(thrView, NULL);
    
    ret = executeCommand((char *)"gmsysview -q V\\$QRY_SHOW_SCHEMA_DEGRADE -f TABLE_NAME=label7", "SCHEMA_VERSIONS_COUNT: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表
    ret = GmcDropVertexLabel(g_stmt, "label7");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcDetachAllShmSeg();
	testEnvClean();
}

// 升级版本号复用（使用同样的schema和版本号）
TEST_F(base, Upgrade_010_005_003)
{
    int ret = 0;
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test_start");
    AddWhiteList(GMERR_INVALID_PROPERTY);

    AW_CHECK_LOG_BEGIN();
    // 建连（同步）
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建表V0版本
    ret = CreateVertexLabel(g_stmt, "schemaFile/label1.gmjson", g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    char cmd[1024];
    // V0->V1
    (void)snprintf(cmd, 1024, "%s/gmddl -c alter -t label1 -f schemaFile/label1V1.gmjson -u online -ns %s", g_toolPath, g_testNameSpace);
    ret = executeCommand(cmd, "Alter schema upgrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // V1->V0
    (void)snprintf(cmd, 1024, "%s/gmddl -c alter -t label1 -d sync -ns %s", g_toolPath, g_testNameSpace);
    ret = executeCommand(cmd, "Alter schema degrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // V0->V1
    (void)snprintf(cmd, 1024, "%s/gmddl -c alter -t label1 -f schemaFile/label1V1.gmjson -u online -ns %s", g_toolPath, g_testNameSpace);
    ret = executeCommand(cmd, "Alter schema upgrade successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表
    ret = GmcDropVertexLabel(g_stmt, "label1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 断连
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();

    GmcDetachAllShmSeg();
    testEnvClean();
}
