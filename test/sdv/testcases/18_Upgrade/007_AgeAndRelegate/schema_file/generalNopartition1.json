[{"type": "record", "name": "general", "schema_version": 1, "fields": [{"name": "F0", "type": "int64", "nullable": false}, {"name": "F1", "type": "uint64", "nullable": true}, {"name": "F2", "type": "int32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": false}, {"name": "F4", "type": "int16", "nullable": true}, {"name": "F5", "type": "uint16", "nullable": true}, {"name": "F6", "type": "uint8", "nullable": false}, {"name": "F7", "type": "time", "nullable": true}, {"name": "F8", "type": "fixed", "nullable": false, "size": 16}, {"name": "F9", "type": "string", "size": 20, "nullable": true}, {"name": "F10", "type": "uint8: 5", "nullable": false, "default": "0x1f"}, {"name": "F11", "type": "uint16: 10", "nullable": false, "default": "0x3ff"}, {"name": "F12", "type": "uint32", "nullable": false}, {"name": "F13", "type": "uint32", "nullable": false}, {"name": "T1", "type": "record", "vector": true, "fields": [{"name": "F0", "type": "int64", "nullable": true}, {"name": "F1", "type": "uint64", "nullable": true}, {"name": "F2", "type": "string", "size": 100, "nullable": true}, {"name": "F3", "type": "fixed", "nullable": false, "size": 9, "default": "0xffffffffffffffffff"}, {"name": "F4", "type": "bytes", "nullable": false, "size": 10, "default": "0xffffffffffffffffffff"}, {"name": "T2", "type": "record", "vector": true, "fields": [{"name": "F0", "type": "int64", "nullable": false}, {"name": "F1", "type": "uint64", "nullable": false}, {"name": "F2", "type": "int32", "nullable": false}, {"name": "F3", "type": "uint32", "nullable": false}, {"name": "F4", "type": "string", "size": 20, "nullable": true}, {"name": "F5", "type": "fixed", "size": 7, "nullable": true}]}]}, {"name": "F14", "type": "string", "nullable": true}], "keys": [{"node": "general", "name": "primary_key", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "general", "name": "hashcluster_unique_key", "index": {"type": "hashcluster"}, "fields": ["F1", "F2"], "constraints": {"unique": true}}, {"node": "general", "name": "localhash_key", "fields": ["F4", "F5"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}, {"node": "general", "name": "local_key", "fields": ["F3"], "index": {"type": "local"}, "constraints": {"unique": false}}, {"node": "general", "name": "lpm6_key", "fields": ["F12", "F13", "F8", "F6"], "index": {"type": "lpm6_tree_bitmap"}, "constraints": {"unique": true}}, {"node": "T1", "name": "member_key", "index": {"type": "none"}, "fields": ["F0"], "constraints": {"unique": true}}, {"node": "T1/T2", "name": "member_key", "index": {"type": "none"}, "fields": ["F3"], "constraints": {"unique": false}}]}]