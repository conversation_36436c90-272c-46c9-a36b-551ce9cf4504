[{"comment": "前缀表，对应7#表", "version": "2.0", "type": "record", "name": "simple", "schema_version": 1, "fields": [{"name": "vr_id", "type": "uint32", "comment": "Vs索引"}, {"name": "vrf_index", "type": "uint32", "comment": "VpnInstace索引"}, {"name": "dest_ip_addr", "type": "uint32", "comment": "目的地址"}, {"name": "mask_len", "type": "partition", "nullable": false, "comment": "标识Nhp或NhpG"}, {"name": "nhp_group_flag", "type": "uint8", "comment": "掩码长度"}, {"name": "qos_profile_id", "type": "uint16", "comment": "QosID"}, {"name": "primary_label", "type": "uint32", "comment": "标签"}, {"name": "attribute_id", "type": "uint32", "comment": "属性ID"}, {"name": "nhp_group_id", "type": "uint32", "comment": "下一跳索引还是下一跳组索引，根据nhp_group_flag决定"}, {"name": "path_flags", "type": "uint32", "comment": "path标记"}, {"name": "flags", "type": "uint32", "comment": "标志(path完备性)"}, {"name": "status_high_prio", "type": "uint8"}, {"name": "status_normal_prio", "type": "uint8"}, {"name": "errcode_high_prio", "type": "uint8"}, {"name": "errcode_normal_prio", "type": "uint8", "default": 1}, {"name": "svc_ctx_high_prio", "type": "fixed", "size": 34, "default": "ffffffffffffffffffffffffffffffffff", "comment": "高优先级FWM_SERVICE返回的svcCtx"}, {"name": "svc_ctx_normal_prio", "type": "fixed", "size": 34, "default": "ffffffffffffffffffffffffffffffffff", "comment": "普通优先级FWM_SERVICE返回的svcCtx"}, {"name": "app_source_id", "type": "uint32"}, {"name": "table_smooth_id", "type": "uint32"}, {"name": "app_obj_id", "type": "uint64"}, {"name": "app_version", "type": "uint32"}, {"name": "trace", "type": "uint64"}, {"name": "route_flags", "type": "uint16", "comment": "路由标记"}, {"name": "reserved", "type": "uint16", "comment": "预留"}, {"name": "upgrade1", "type": "uint32", "nullable": true}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "simple", "fields": ["vr_id", "vrf_index", "dest_ip_addr"], "constraints": {"unique": true}, "comment": "根据主键索引"}, {"name": "localhash_key", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "simple", "fields": ["qos_profile_id", "nhp_group_id"], "constraints": {"unique": false}, "comment": "根据nhp_group_id + vrid索引"}, {"name": "localhash_key_2", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "simple", "fields": ["primary_label", "attribute_id"], "constraints": {"unique": true}}, {"name": "localhash_key_3", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "simple", "fields": ["qos_profile_id", "svc_ctx_normal_prio"], "constraints": {"unique": false}}, {"name": "localhash_key_new", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "simple", "fields": ["qos_profile_id", "status_high_prio"], "constraints": {"unique": false}}, {"name": "hashcluster_key", "index": {"type": "hashcluster"}, "node": "simple", "fields": ["qos_profile_id", "nhp_group_id"], "constraints": {"unique": false}}, {"name": "hashcluster_key_str", "index": {"type": "hashcluster"}, "node": "simple", "fields": ["vr_id", "vrf_index", "dest_ip_addr", "svc_ctx_high_prio"], "constraints": {"unique": false}}, {"name": "local_key", "index": {"type": "local"}, "node": "simple", "fields": ["primary_label", "qos_profile_id"], "constraints": {"unique": false}}]}]