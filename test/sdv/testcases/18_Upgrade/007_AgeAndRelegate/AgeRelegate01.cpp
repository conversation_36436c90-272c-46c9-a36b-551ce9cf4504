/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2012-2018. All rights reserved.
 Description  :表升级交互全表对账
 Author       : youwanyong wx1157510
 Modification :
 Date         : 2022/07/27
**************************************************************************** */
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <time.h>
#include "gtest/gtest.h"
#include "AgeAndRelege.h"

int end_num = 100;
const char *g_subConnName = "subConnName";

char cmd[512];
#define FULLTABLE 0xff

class GeneralTable : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void GeneralTable::SetUpTestCase()
{
    int ret = 0;
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void GeneralTable::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}

void GeneralTable::SetUp()
{
    // 建连
    int ret = 0;
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    testSnMallocUserData(&user_data, end_num * 3, 0);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    AW_CHECK_LOG_BEGIN();
}

void GeneralTable::TearDown()
{
    AW_CHECK_LOG_END();
    if (user_data) {
        testSnFreeUserData(user_data);
    }
    int ret = 0;
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
}

/* ****************************************************************************
 Description  : 001.简单表 表升级，表中无数据，开启全表对账，表升级，结束全表对账 表升级成功
 ok
**************************************************************************** */
TEST_F(GeneralTable, Upgrade_007_001)
{

    AW_FUN_Log(LOG_STEP, "test_start");
    // 建表
    const char *labelName = "simple";
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/simpleNoPartition.gmjson", &schema);  // 拥有partition字段的表是分区表
    EXPECT_NE((void *)NULL, schema);
    int ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    // 开启全表对账
    uint8_t partition = 0xff;
    ret = GmcBeginCheck(g_stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);

    char *expectValue = (char *)"upgrade successfully";
    uint32_t schemaVersion = 1;
    char *schemaUpdateParth = (char *)"./schema_file/simpleNoPartition2.gmjson";

    // 进行表升级
    querySchemaVersionView(labelName, "VERSION: 0", "VERTEX_LABEL_LEVEL: VERTEX_LEVEL_SIMPLE");
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // query old queue status
    queryOldStatusView();

    //  end check
    ret = GmcEndCheck(g_stmt, labelName, partition, false);
    EXPECT_EQ(GMERR_OK, ret);
    // query old version
    queryOldInfoView(labelName, "PARTITION_ID: 255", "CHECK_STATUS: 0", "CHECK_VERSION: 1", "OLD_CHECK_VERSION: 0");
    // query schema version
    querySchemaVersionView(labelName, "VERSION: 1", "VERTEX_LABEL_LEVEL: VERTEX_LEVEL_SIMPLE");

    // 删表
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test_end");
}

/* ****************************************************************************
 Description  : 002.一般复杂表 表升级，插入数据，开启全表对账，表升级，结束全表对账，数据老化，表升级成功
 ok
**************************************************************************** */
TEST_F(GeneralTable, Upgrade_007_002)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    // 建表
    const char *labelName = "general";
    char *schema = NULL;
    int64_t startValue = 0;
    int64_t endValue = 100;
    uint32_t schemaVersion = 0;
    GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/generalNopartition.gmjson", &schema);  // 拥有partition字段的表是分区表
    EXPECT_NE((void *)NULL, schema);
    int ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    // insert data
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true);

    // scan
    TestGeneralT1PkScan(g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, true);

    // 开启全表对账
    uint8_t partition = 0xff;
    ret = GmcBeginCheck(g_stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);

    char *expectValue = (char *)"upgrade successfully";
    schemaVersion = 1;
    char *schemaUpdateParth = (char *)"./schema_file/generalNopartition1.gmjson";

    // 进行表升级
    querySchemaVersionView(labelName, "VERSION: 0", "VERTEX_LABEL_LEVEL: VERTEX_LEVEL_GENERAL");
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // query old queue status
    queryOldStatusView();

    //  end check
    ret = GmcEndCheck(g_stmt, labelName, partition, false);
    EXPECT_EQ(GMERR_OK, ret);
    startValue = 100;
    // scan
    int32_t record = 0;
    ret = readTable(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    // query old version
    queryOldInfoView(labelName, "PARTITION_ID: 255", "CHECK_STATUS: 0", "CHECK_VERSION: 1", "OLD_CHECK_VERSION: 0");
    // query schema version
    querySchemaVersionView(labelName, "VERSION: 1", "VERTEX_LABEL_LEVEL: VERTEX_LEVEL_GENERAL");

    // 删表
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test_end");
}

/* ****************************************************************************
 Description  : 003.特殊复杂表
表升级，插入数据，开启全表对账，表升级，插入新数据，结束全表对账，数据老化，数据查询存在新数据，表升级成功
**************************************************************************** */
TEST_F(GeneralTable, Upgrade_007_003)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    // 建表
    const char *labelName = "special";
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/specialNoPartition.gmjson", &schema);  // 拥有partition字段的表是分区表
    EXPECT_NE((void *)NULL, schema);
    int ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    // 开启全表对账
    uint8_t partition = 0xff;
    ret = GmcBeginCheck(g_stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);

    char *expectValue = (char *)"upgrade successfully";
    uint32_t schemaVersion = 1;
    char *schemaUpdateParth = (char *)"./schema_file/specialNoPartition1.gmjson";

    // 进行表升级
    querySchemaVersionView(labelName, "VERSION: 0", "VERTEX_LABEL_LEVEL: VERTEX_LEVEL_SPECIAL");
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int32_t startValue = 0;
    uint32_t endValue = 100;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    // 开始，结束，updatevalue,affectrows,thread,v1count,v2,count,schema_version
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_INSERT};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // scan
    vertexCfg.optType = GMC_OPERATION_SCAN;
    vertexCfg.expAffectRows = endValue;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // query old queue status
    queryOldStatusView();

    //  end check
    ret = GmcEndCheck(g_stmt, labelName, partition, false);
    EXPECT_EQ(GMERR_OK, ret);
    // query old version
    queryOldInfoView(labelName, "PARTITION_ID: 255", "CHECK_STATUS: 0", "CHECK_VERSION: 1", "OLD_CHECK_VERSION: 0");
    // query schema version
    querySchemaVersionView(labelName, "VERSION: 1", "VERTEX_LABEL_LEVEL: VERTEX_LEVEL_SPECIAL");

    // 删表
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test_end");
}

/* ****************************************************************************
 Description  : 004.一般复杂表
表升级，插入数据，开启全表对账，表升级，更新部分数据，结束全表对账，未更新数据老化，数据查询已更新数据，表升级成功 ok
**************************************************************************** */
TEST_F(GeneralTable, Upgrade_007_004)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    // 建表
    const char *labelName = "general";
    char *schema = NULL;
    int64_t startValue = 0;
    int64_t endValue = 100;
    uint32_t schemaVersion = 0;
    GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/generalNopartition.gmjson", &schema);  // 拥有partition字段的表是分区表
    EXPECT_NE((void *)NULL, schema);
    int ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    // insert data
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true);

    // scan
    TestGeneralT1PkScan(g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, true);
    int32_t record = 100;
    ret = readTable(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    // 开启全表对账
    uint8_t partition = 0xff;
    ret = GmcBeginCheck(g_stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    isAgeBegin = true;

    char *expectValue = (char *)"upgrade successfully";
    schemaVersion = 1;
    char *schemaUpdateParth = (char *)"./schema_file/generalNopartition1.gmjson";

    // 进行表升级
    querySchemaVersionView(labelName, "VERSION: 0", "VERTEX_LABEL_LEVEL: VERTEX_LEVEL_GENERAL");
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // replace a part
    startValue = 50;

    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, GMC_OPERATION_REPLACE, true);

    // query old queue status
    queryOldStatusView();

    //  end check
    isAgeBegin = false;
    ret = GmcEndCheck(g_stmt, labelName, partition, false);
    EXPECT_EQ(GMERR_OK, ret);
    startValue = 50;
    record = 50;
    ret = readTable(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    // scan
    TestGeneralT1PkScan(g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, true);

    // query old version
    queryOldInfoView(labelName, "PARTITION_ID: 255", "CHECK_STATUS: 0", "CHECK_VERSION: 1", "OLD_CHECK_VERSION: 0");
    // query schema version
    querySchemaVersionView(labelName, "VERSION: 1", "VERTEX_LABEL_LEVEL: VERTEX_LEVEL_GENERAL");

    // 删表
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test_end");
}

/* ****************************************************************************
 Description  :
005.简单表，表升级，表升级后全量订阅，插入数据，开启对帐，更新部分数据，结束全表对账，对账异常，恢复数据，
表升级,老化数据回滚，表升级成功
**************************************************************************** */
TEST_F(GeneralTable, Upgrade_007_005)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    // 建表
    AW_FUN_Log(LOG_STEP, "1.建表");
    const char *labelName = "simple";
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/simpleNoPartition.gmjson", &schema);  // 拥有partition字段的表是分区表
    EXPECT_NE((void *)NULL, schema);
    int ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    char *expectValue = (char *)"upgrade successfully";
    uint32_t schemaVersion = 1;
    char *schemaUpdateParth = (char *)"./schema_file/simpleNoPartition2.gmjson";
    // 进行表升级
    AW_FUN_Log(LOG_STEP, "2.进行表升级");
    querySchemaVersionView(labelName, "VERSION: 0", "VERTEX_LABEL_LEVEL: VERTEX_LEVEL_SIMPLE");
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建全量订阅
    // 开启订阅
    // 创建订阅连接
    AW_FUN_Log(LOG_STEP, "3.创建全量订阅");
    GmcConnT *g_subChan = NULL;
    GmcStmtT *stmt_sub = NULL;
    int chanRingLen = 256;
    ret = testSubConnect(&g_subChan, &stmt_sub, 1, g_epoll_reg_info, g_subConnName, &chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *sub_info1 = NULL;
    const char *subName1 = "subVertexLabel_commit";
    readJanssonFile("./schema_file/sub_simple_all.gmjson", &sub_info1);
    EXPECT_NE((void *)NULL, sub_info1);
    GmcSubConfigT tmp_subInfo;
    tmp_subInfo.subsName = subName1;
    tmp_subInfo.configJson = sub_info1;
    ret = GmcSubscribe(g_stmt, &tmp_subInfo, g_subChan, sn_callback_fullsub, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info1);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INITIAL_LOAD_EOF,
        1);  // 此处需要接受EOF标记位, 如不加影响后面增量dml操作user_data
    EXPECT_EQ(GMERR_OK, ret);

    int32_t startNums = 0;
    int32_t endNums = 100;
    // insert 100 data
    ret = InsertAndReplaceSimpleTabel(g_stmt, labelName, startNums, endNums, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ScanSimpleTable(g_stmt, labelName, startNums, endNums);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启全表对账
    AW_FUN_Log(LOG_STEP, "4.开启全表对账");
    uint8_t partition = 0xff;
    ret = GmcBeginCheck(g_stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);

    // replace data
    startNums = 50;
    ret = InsertAndReplaceSimpleTabel(g_stmt, labelName, startNums, endNums, GMC_OPERATION_REPLACE, schemaVersion);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // query old queue status
    queryOldStatusView();

    //  end check
    AW_FUN_Log(LOG_STEP, "5.结束全表对账");
    ret = GmcEndCheck(g_stmt, labelName, partition, false);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_REPLACE, 50);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 100);
    EXPECT_EQ(GMERR_OK, ret);
    ret = ScanSimpleTable(g_stmt, labelName, startNums, endNums, schemaVersion);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.视图校验");
    // query old version
    queryOldInfoView(labelName, "PARTITION_ID: 255", "CHECK_STATUS: 0", "CHECK_VERSION: 1", "OLD_CHECK_VERSION: 0");
    // query schema version
    querySchemaVersionView(labelName, "VERSION: 1", "VERTEX_LABEL_LEVEL: VERTEX_LEVEL_SIMPLE");

    // 删除订阅关系
    ret = GmcUnSubscribe(g_stmt, subName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 断开订阅链接
    ret = testSubDisConnect(g_subChan, stmt_sub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表
    AW_FUN_Log(LOG_STEP, "7.删表");
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test_end");
}

/* ****************************************************************************
 Description  : 006.特殊复杂表，表升级，条件订阅， 插入数据，开启对帐，更新部分数据，结束全表对账，对账异常，恢复数据，
表升级,老化数据回滚，表升级成功
**************************************************************************** */
TEST_F(GeneralTable, Upgrade_007_006)
{
    AW_FUN_Log(LOG_STEP, "test_end");
    // 建表
    const char *labelName = "special";
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/specialNoPartition.gmjson", &schema);  // 拥有partition字段的表是分区表
    EXPECT_NE((void *)NULL, schema);
    int ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    char *expectValue = (char *)"upgrade successfully";
    uint32_t schemaVersion = 1;
    char *schemaUpdateParth = (char *)"./schema_file/specialNoPartition1.gmjson";

    // 进行表升级
    querySchemaVersionView(labelName, "VERSION: 0", "VERTEX_LABEL_LEVEL: VERTEX_LEVEL_SPECIAL");
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 开启订阅
    // 创建订阅连接
    GmcConnT *g_subChan = NULL;
    GmcStmtT *stmt_sub = NULL;
    int chanRingLen = 256;
    ret = testSubConnect(&g_subChan, &stmt_sub, 1, g_epoll_reg_info, g_subConnName, &chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *sub_info1 = NULL;
    const char *subName1 = "subAged";
    readJanssonFile("./schema_file/sub_special_age.gmjson", &sub_info1);
    EXPECT_NE((void *)NULL, sub_info1);
    GmcSubConfigT tmp_subInfo;
    tmp_subInfo.subsName = subName1;
    tmp_subInfo.configJson = sub_info1;
    ret = GmcSubscribe(g_stmt, &tmp_subInfo, g_subChan, sn_callback_newversion, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info1);
    char *sub_info2 = NULL;
    const char *subName2 = "subVertexLabel";
    readJanssonFile("./schema_file/sub_special_pk.gmjson", &sub_info2);
    EXPECT_NE((void *)NULL, sub_info2);
    tmp_subInfo.subsName = subName2;
    tmp_subInfo.configJson = sub_info2;
    ret = GmcSubscribe(g_stmt, &tmp_subInfo, g_subChan, sn_callback_newversion, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info2);

    int32_t startValue = 0;
    uint32_t endValue = 100;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    // 开始，结束，updatevalue,affectrows,thread,v1count,v2,count,schema_version
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // scan
    vertexCfg.optType = GMC_OPERATION_SCAN;
    vertexCfg.expAffectRows = endValue;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int32_t counts = 1;
    AW_FUN_Log(LOG_STEP, "订阅接收insert事件");
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, counts);
    EXPECT_EQ(GMERR_OK, ret);

    // 开启全表对账
    AW_FUN_Log(LOG_STEP, "开启全表对账");
    uint8_t partition = 0xff;
    ret = GmcBeginCheck(g_stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    vertexCfg.optType = GMC_OPERATION_REPLACE;
    vertexCfg.schemaVersion = 1;
    vertexCfg.startVal = 50;
    vertexCfg.count = 50;
    vertexCfg.expAffectRows = 2;
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // query old queue status
    queryOldStatusView();

    //  end check
    AW_FUN_Log(LOG_STEP, "end check");
    ret = GmcEndCheck(g_stmt, labelName, partition, false);
    EXPECT_EQ(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 订阅接收
    // 等待replace事件推送完成
    AW_FUN_Log(LOG_STEP, "订阅接收replace事件");
    counts = 0;
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_REPLACE, counts);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "订阅接收age事件");  //  条件满足也会往age事件推送一次
    counts = 100;
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_AGED, counts);
    EXPECT_EQ(GMERR_OK, ret);

    // query old version
    queryOldInfoView(labelName, "PARTITION_ID: 255", "CHECK_STATUS: 0", "CHECK_VERSION: 1", "OLD_CHECK_VERSION: 0");
    // query schema version
    querySchemaVersionView(labelName, "VERSION: 1", "VERTEX_LABEL_LEVEL: VERTEX_LEVEL_SPECIAL");

    // 删除订阅关系
    ret = GmcUnSubscribe(g_stmt, subName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, subName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 断开订阅链接
    ret = testSubDisConnect(g_subChan, stmt_sub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删表
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test_end");
}

/* ****************************************************************************
 Description  : 007.简单表，多次表升级，插入数据， 表升级, 开启对帐，更新部分数据，
表升级,，结束对账，对账异常，恢复数据  表升级,老化数据回滚，表升级成功
**************************************************************************** */
TEST_F(GeneralTable, Upgrade_007_007)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    // 建表
    const char *labelName = "simple";
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/simpleNoPartition.gmjson", &schema);  // 拥有partition字段的表是分区表
    EXPECT_NE((void *)NULL, schema);
    int ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    // insert 100 data
    AW_FUN_Log(LOG_STEP, "insert 100 data");
    int32_t startNums = 0;
    int32_t endNums = 0;
    ret = InsertAndReplaceSimpleTabel(g_stmt, labelName, startNums, endNums, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ScanSimpleTable(g_stmt, labelName, startNums, endNums);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *expectValue = (char *)"upgrade successfully";
    uint32_t schemaVersion = 1;
    char *schemaUpdateParth = (char *)"./schema_file/simpleNoPartition2.gmjson";

    // 进行表升级
    AW_FUN_Log(LOG_STEP, "进行表升级1");
    querySchemaVersionView(labelName, "VERSION: 0", "VERTEX_LABEL_LEVEL: VERTEX_LEVEL_SIMPLE");
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 开启全表对账
    uint8_t partition = 0xff;
    ret = GmcBeginCheck(g_stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    startNums = 50;
    ret = InsertAndReplaceSimpleTabel(g_stmt, labelName, startNums, endNums, GMC_OPERATION_REPLACE, schemaVersion);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    schemaUpdateParth = (char *)"./schema_file/simpleNoPartition3.gmjson";

    // 进行表升级
    AW_FUN_Log(LOG_STEP, "进行表升级2");
    querySchemaVersionView(labelName, "VERSION: 1", "VERTEX_LABEL_LEVEL: VERTEX_LEVEL_SIMPLE");
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // query old queue status
    queryOldStatusView();

    //  end check
    ret = GmcEndCheck(g_stmt, labelName, partition, false);
    EXPECT_EQ(GMERR_OK, ret);
    ret = ScanSimpleTable(g_stmt, labelName, startNums, endNums, schemaVersion);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    schemaUpdateParth = (char *)"./schema_file/simpleNoPartition4.gmjson";

    // 进行表升级
    AW_FUN_Log(LOG_STEP, "进行表升级3");
    querySchemaVersionView(labelName, "VERSION: 2", "VERTEX_LABEL_LEVEL: VERTEX_LEVEL_SIMPLE");
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // query old version
    queryOldInfoView(labelName, "PARTITION_ID: 255", "CHECK_STATUS: 0", "CHECK_VERSION: 1", "OLD_CHECK_VERSION: 0");
    // query schema version
    querySchemaVersionView(labelName, "VERSION: 3", "VERTEX_LABEL_LEVEL: VERTEX_LEVEL_SIMPLE");

    // 删表
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test_end");
}

/* ****************************************************************************
 Description  : 008.一般复杂表，多次表升级，插入数据， 表升级, 条件订阅，开启对帐，更新部分数据，
表升级,，结束全表对账，  表升级,  未更新数据老化，数据查询已更新数据，表升级成功
**************************************************************************** */
TEST_F(GeneralTable, Upgrade_007_008)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    // 建表
    const char *labelName = "general";
    char *schema = NULL;
    int64_t startValue = 0;
    int64_t endValue = 100;
    uint32_t schemaVersion = 0;
    GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/generalNopartition.gmjson", &schema);  // 拥有partition字段的表是分区表
    EXPECT_NE((void *)NULL, schema);
    int ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    // 开启订阅
    // 创建订阅连接
    GmcConnT *g_subChan = NULL;
    GmcStmtT *stmt_sub = NULL;
    int chanRingLen = 256;
    ret = testSubConnect(&g_subChan, &stmt_sub, 1, g_epoll_reg_info, g_subConnName, &chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *sub_info1 = NULL;
    const char *subName1 = "subAged";
    readJanssonFile("./schema_file/sub_general_age.gmjson", &sub_info1);
    EXPECT_NE((void *)NULL, sub_info1);
    GmcSubConfigT tmp_subInfo;
    tmp_subInfo.subsName = subName1;
    tmp_subInfo.configJson = sub_info1;
    ret = GmcSubscribe(g_stmt, &tmp_subInfo, g_subChan, sn_callback_newversion, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info1);
    char *sub_info2 = NULL;
    const char *subName2 = "subVertexLabel";
    readJanssonFile("./schema_file/sub_general_pk.gmjson", &sub_info2);
    EXPECT_NE((void *)NULL, sub_info2);
    tmp_subInfo.subsName = subName2;
    tmp_subInfo.configJson = sub_info2;
    ret = GmcSubscribe(g_stmt, &tmp_subInfo, g_subChan, sn_callback_newversion, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info2);

    // insert data
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, GMC_OPERATION_INSERT, true);

    // scan
    TestGeneralT1PkScan(g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, true);
    int32_t record = 100;
    ret = readTable(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    int32_t counts = 1;
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, counts);
    EXPECT_EQ(GMERR_OK, ret);

    // 开启全表对账
    uint8_t partition = 0xff;
    ret = GmcBeginCheck(g_stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);
    isAgeBegin = true;

    char *expectValue = (char *)"upgrade successfully";
    schemaVersion = 1;
    char *schemaUpdateParth = (char *)"./schema_file/generalNopartition1.gmjson";

    // 进行表升级
    querySchemaVersionView(labelName, "VERSION: 0", "VERTEX_LABEL_LEVEL: VERTEX_LEVEL_GENERAL");
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // replace a part
    startValue = 50;
    TestGeneralT1InsertOrReplace(
        g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, GMC_OPERATION_REPLACE, true);

    // query old queue status
    queryOldStatusView();

    //  end check
    isAgeBegin = false;
    ret = GmcEndCheck(g_stmt, labelName, partition, false);
    EXPECT_EQ(GMERR_OK, ret);
    startValue = 50;
    record = 50;
    ret = readTable(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    // scan
    TestGeneralT1PkScan(g_stmt, labelName, startValue, endValue, (char *)"string", schemaVersion, true);

    // 订阅接收
    // 等待replace事件推送完成
    AW_FUN_Log(LOG_STEP, "订阅接收replace事件");
    counts = 0;
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_REPLACE, counts);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "订阅接收age事件");  //  条件满足也会往age事件推送一次
    counts = 100;
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_AGED, counts);
    EXPECT_EQ(GMERR_OK, ret);

    // query old version
    queryOldInfoView(labelName, "PARTITION_ID: 255", "CHECK_STATUS: 0", "CHECK_VERSION: 1", "OLD_CHECK_VERSION: 0");
    // query schema version
    querySchemaVersionView(labelName, "VERSION: 1", "VERTEX_LABEL_LEVEL: VERTEX_LEVEL_GENERAL");

    // 删除订阅关系
    ret = GmcUnSubscribe(g_stmt, subName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, subName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 断开订阅链接
    ret = testSubDisConnect(g_subChan, stmt_sub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test_end");
}

/* ****************************************************************************
 Description  : 009.特殊复杂表，多次表升级，插入数据， 表升级, 开启对帐， 表升级,，更新部分数据，结束全表对账，  表升级,
未更新数据老化，数据查询已更新数据，表升级成功
**************************************************************************** */
TEST_F(GeneralTable, Upgrade_007_009)
{
    AW_FUN_Log(LOG_STEP, "test_start");
    // 建表
    const char *labelName = "special";
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/specialNoPartition.gmjson", &schema);  // 拥有partition字段的表是分区表
    EXPECT_NE((void *)NULL, schema);
    int ret = GmcCreateVertexLabel(g_stmt, schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    int32_t startValue = 0;
    uint32_t endValue = 100;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    // 开始，结束，updatevalue,affectrows,thread,v1count,v2,count,schema_version
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char *expectValue = (char *)"upgrade successfully";
    uint32_t schemaVersion = 1;
    char *schemaUpdateParth = (char *)"./schema_file/specialNoPartition1.gmjson";

    // 进行表升级
    querySchemaVersionView(labelName, "VERSION: 0", "VERTEX_LABEL_LEVEL: VERTEX_LEVEL_SPECIAL");
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 开启订阅
    // 创建订阅连接
    GmcConnT *g_subChan = NULL;
    GmcStmtT *stmt_sub = NULL;
    int chanRingLen = 256;
    ret = testSubConnect(&g_subChan, &stmt_sub, 1, g_epoll_reg_info, g_subConnName, &chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *sub_info1 = NULL;
    const char *subName1 = "subAged";
    readJanssonFile("./schema_file/sub_special_age.gmjson", &sub_info1);
    EXPECT_NE((void *)NULL, sub_info1);
    GmcSubConfigT tmp_subInfo;
    tmp_subInfo.subsName = subName1;
    tmp_subInfo.configJson = sub_info1;
    ret = GmcSubscribe(g_stmt, &tmp_subInfo, g_subChan, sn_callback_newversion, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info1);

    // scan
    vertexCfg.optType = GMC_OPERATION_SCAN;
    vertexCfg.expAffectRows = endValue;
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int32_t counts = 2;

    // 开启全表对账
    AW_FUN_Log(LOG_STEP, "开启全表对账");
    uint8_t partition = 0xff;
    ret = GmcBeginCheck(g_stmt, labelName, FULLTABLE);
    EXPECT_EQ(GMERR_OK, ret);

    schemaVersion = 2;
    schemaUpdateParth = (char *)"./schema_file/specialNoPartition2.gmjson";

    // 进行表升级
    querySchemaVersionView(labelName, "VERSION: 1", "VERTEX_LABEL_LEVEL: VERTEX_LEVEL_SPECIAL");
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    vertexCfg.optType = GMC_OPERATION_REPLACE;
    vertexCfg.schemaVersion = 1;
    vertexCfg.startVal = 50;
    vertexCfg.count = 50;
    vertexCfg.expAffectRows = 2;
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // query old queue status
    queryOldStatusView();

    //  end check
    AW_FUN_Log(LOG_STEP, "end check");
    ret = GmcEndCheck(g_stmt, labelName, partition, false);
    EXPECT_EQ(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfg, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 订阅接收
    // 等待replace事件推送完成
    AW_FUN_Log(LOG_STEP, "订阅接收age事件");
    counts = 50;
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_AGED, counts);
    EXPECT_EQ(GMERR_OK, ret);

    schemaVersion = 3;
    schemaUpdateParth = (char *)"./schema_file/specialNoPartition3.gmjson";

    // 进行表升级
    querySchemaVersionView(labelName, "VERSION: 2", "VERTEX_LABEL_LEVEL: VERTEX_LEVEL_SPECIAL");
    ret = TestUpdateVertexLabel(schemaUpdateParth, expectValue, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // query old version
    queryOldInfoView(labelName, "PARTITION_ID: 255", "CHECK_STATUS: 0", "CHECK_VERSION: 1", "OLD_CHECK_VERSION: 0");
    // query schema version
    querySchemaVersionView(labelName, "VERSION: 3", "VERTEX_LABEL_LEVEL: VERTEX_LEVEL_SPECIAL");

    // 删除订阅关系
    ret = GmcUnSubscribe(g_stmt, subName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 断开订阅链接
    ret = testSubDisConnect(g_subChan, stmt_sub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删表
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test_end");
}
