/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
/*****************************************************************************
 Description  : 树模型支持值校验
 Notes        :
 History      :
 Author       : yangfuwen ywx1060383
 Modification :
*****************************************************************************/

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "Tree_Asyn_DML_test.h"

#define LABELNAME_MAX_LENGTH 128
#define SCHEMA_JSON_SIZE 1024

AsyncUserDataT data = {0};
GmcConnT *g_conn_async = NULL;
GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt_async = NULL;
GmcStmtT *g_stmt = NULL;
GmcStmtT *g_stmt_list = NULL;
GmcNodeT *g_rootNode = NULL;
GmcNodeT *g_childNode[10] = {0};
int32_t ret;
GmcBatchT *batch = NULL;
char *test_schema1 = NULL;
char *edge_schema = NULL;
char g_labelName_root[LABELNAME_MAX_LENGTH] = "Con_0";
char g_labelName_list[LABELNAME_MAX_LENGTH] = "list_1";
char g_labelName_default_root[LABELNAME_MAX_LENGTH] = "Con_0_default";
char g_labelName_default_list[LABELNAME_MAX_LENGTH] = "list_1_default";
char g_labelName_conflict_root[LABELNAME_MAX_LENGTH] = "Con_0_conflict";
char g_labelName_conflict_list[LABELNAME_MAX_LENGTH] = "list_1_conflict";
char g_labelName_max_root[LABELNAME_MAX_LENGTH] = "Con_0_max_string";
const char *g_edgeLabe = "Con_0:list_1";
const char *g_edgeLabe_default = "Con_0_default:list_1_default";
const char *g_edgeLabe_conflict = "Con_0_conflict:list_1_conflict";
char g_configJson[128] =
    "{\"max_record_count\" : 10000, \"yang_model\" : 1, \"auto_increment\" : 1, \"isFastReadUncommitted\":0}";
const char *g_config_auto_increment = R"(
{
    "max_record_count" : 10000,
    "auto_increment" : 1,
    "yang_model" : 1,
    "isFastReadUncommitted" : 0
})";

class Tree_Asyn_DML_test : public testing::Test {
public:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh ");
        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    };

    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    };
    virtual void SetUp();
    virtual void TearDown();
};

void Tree_Asyn_DML_test::SetUp()
{
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    g_mSTrxConfig.readOnly = false;

    int ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_list);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    AsyncUserDataT userData = {0};
    const char *namespace1 = "NamespaceA01902";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcClearNamespaceAsync(g_stmt_async, namespace1, ClearNSCallbak, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    readJanssonFile("schema_file/tree_model_dml.gmjson", &test_schema1);
    ASSERT_NE((void *)NULL, test_schema1);
    AW_FUN_Log(LOG_STEP, "建表");
    ret = GmcCreateVertexLabelAsync(g_stmt_async, test_schema1, g_configJson, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    free(test_schema1);

    AW_FUN_Log(LOG_STEP, "建边");
    readJanssonFile("schema_file/tree_model_edge_label.gmjson", &edge_schema);
    ASSERT_NE((void *)NULL, edge_schema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, edge_schema, g_configJson, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    free(edge_schema);
    AW_CHECK_LOG_BEGIN();
}

void Tree_Asyn_DML_test::TearDown()
{
    AW_CHECK_LOG_END();

    const char *namespace1 = "NamespaceA01902";
    AsyncUserDataT userData = {0};
    int ret = 0;

    ret = GmcClearNamespaceAsync(g_stmt_async, namespace1, ClearNSCallbak, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    GmcFreeStmt(g_stmt_list);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 099.container作为根节点，根节点子节点整型，浮点型数据定义range约束，string定义length和pattern，invert_pattern约束
// 异步insert时设置符合定义范围的数据
TEST_F(Tree_Asyn_DML_test, Yang_019_099)
{
    AW_FUN_Log(LOG_STEP, "启动事务");
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName_root, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_0
    ret = testYangSetVertexPropertyInt_Root(g_rootNode, 1, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Root(
        g_rootNode, (char *)"G", (char *)"GMDB", (char *)"GMDB", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_1
    ret = GmcYangEditChildNode(g_rootNode, "Con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_Con_1(g_childNode[1], 1, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Con_1(g_childNode[1], (char *)"G", (char *)"GMDB", (char *)"GMDB",
        (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Case_2
    ret = GmcYangEditChildNode(g_rootNode, "Choice_1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[2], "Case_2", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_Case_2(g_childNode[3], 1, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Case_2(g_childNode[3], (char *)"G", (char *)"GMDB", (char *)"GMDB",
        (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_labelName_list, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_childNode[5]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_List_1(g_childNode[5], 1, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_List_1(g_childNode[5], (char *)"G", (char *)"GMDB", (char *)"GMDB",
        (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_2
    ret = GmcYangEditChildNode(g_childNode[5], "Con_2", GMC_OPERATION_INSERT, &g_childNode[6]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_Con_2(g_childNode[6], 1, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Con_2(g_childNode[6], (char *)"G", (char *)"GMDB", (char *)"GMDB",
        (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Case_3
    ret = GmcYangEditChildNode(g_childNode[5], "Choice_2", GMC_OPERATION_INSERT, &g_childNode[7]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[7], "Case_3", GMC_OPERATION_INSERT, &g_childNode[8]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_Case_3(g_childNode[8], 1, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Case_3(g_childNode[8], (char *)"G", (char *)"GMDB", (char *)"GMDB",
        (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(2, data.totalNum);
    ASSERT_EQ(2, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SubtreeFilterCbParam Data_1 = {0};
    Data_1.expectReplyJson = "~";
    Data_1.expectStatus = GMERR_OK;
    Data_1.step = 0;
    GmcSubtreeFilterItemT filter_state = {.rootName = "Con_0",
        .subtree = {.json = g_subtreeJson},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = GMC_SUBTREE_FILTER_DEFAULT};
    GmcSubtreeFilterT filters_1 = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter_state,
    };

    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters_1, NULL, AsyncSubtreeFilterCb, &Data_1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data_1);
    EXPECT_EQ(GMERR_OK, Data_1.expectStatus);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验数据
    char *result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat a.txt");
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_json99, result));
    free(result);
}

// 100.container作为根节点，根节点子节点整型，浮点型数据定义range约束，string定义length和pattern，invert_pattern约束
// 异步replace_insert时设置符合定义范围的数据
TEST_F(Tree_Asyn_DML_test, Yang_019_100)
{
    AW_FUN_Log(LOG_STEP, "启动事务");
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName_root, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_0
    ret = testYangSetVertexPropertyInt_Root(g_rootNode, 9, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Root(
        g_rootNode, (char *)"G", (char *)"GMDB", (char *)"GMDB", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_1
    ret = GmcYangEditChildNode(g_rootNode, "Con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_Con_1(g_childNode[1], 9, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Con_1(g_childNode[1], (char *)"G", (char *)"GMDB", (char *)"GMDB",
        (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Case_2
    ret = GmcYangEditChildNode(g_rootNode, "Choice_1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[2], "Case_2", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_Case_2(g_childNode[3], 9, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Case_2(g_childNode[3], (char *)"G", (char *)"GMDB", (char *)"GMDB",
        (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_labelName_list, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_childNode[5]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_List_1(g_childNode[5], 9, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_List_1(g_childNode[5], (char *)"G", (char *)"GMDB", (char *)"GMDB",
        (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_2
    ret = GmcYangEditChildNode(g_childNode[5], "Con_2", GMC_OPERATION_INSERT, &g_childNode[6]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_Con_2(g_childNode[6], 9, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Con_2(g_childNode[6], (char *)"G", (char *)"GMDB", (char *)"GMDB",
        (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Case_3
    ret = GmcYangEditChildNode(g_childNode[5], "Choice_2", GMC_OPERATION_INSERT, &g_childNode[7]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[7], "Case_3", GMC_OPERATION_INSERT, &g_childNode[8]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_Case_3(g_childNode[8], 9, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Case_3(g_childNode[8], (char *)"G", (char *)"GMDB", (char *)"GMDB",
        (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(2, data.totalNum);
    ASSERT_EQ(2, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SubtreeFilterCbParam Data_1 = {0};
    Data_1.expectReplyJson = "~";
    Data_1.expectStatus = GMERR_OK;
    Data_1.step = 0;
    GmcSubtreeFilterItemT filter_state = {.rootName = "Con_0",
        .subtree = {.json = g_subtreeJson},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = GMC_SUBTREE_FILTER_DEFAULT};
    GmcSubtreeFilterT filters_1 = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter_state,
    };

    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters_1, NULL, AsyncSubtreeFilterCb, &Data_1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data_1);
    EXPECT_EQ(GMERR_OK, Data_1.expectStatus);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验数据
    char *result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat a.txt");
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_json100, result));
    free(result);
}

// 101.container作为根节点，根节点子节点整型，浮点型数据定义range约束，string定义length和pattern，invert_pattern约束
// 异步merge_insert时设置符合定义范围的数据
TEST_F(Tree_Asyn_DML_test, Yang_019_101)
{
    AW_FUN_Log(LOG_STEP, "启动事务");
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName_root, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_0
    ret = testYangSetVertexPropertyInt_Root(g_rootNode, 9, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Root(
        g_rootNode, (char *)"G", (char *)"GMDB", (char *)"GMDB", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_1
    ret = GmcYangEditChildNode(g_rootNode, "Con_1", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_Con_1(g_childNode[1], 9, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Con_1(
        g_childNode[1], (char *)"G", (char *)"GMDB", (char *)"GMDB", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Case_2
    ret = GmcYangEditChildNode(g_rootNode, "Choice_1", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[2], "Case_2", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_Case_2(g_childNode[3], 9, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Case_2(
        g_childNode[3], (char *)"G", (char *)"GMDB", (char *)"GMDB", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    char listPkName[] = "list_1_PK";
    uint32_t pkValue = 9;
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_labelName_list, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_list, listPkName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_list, 1, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_childNode[5]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_List_1(g_childNode[5], 9, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_List_1(
        g_childNode[5], (char *)"G", (char *)"GMDB", (char *)"GMDB", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_2
    ret = GmcYangEditChildNode(g_childNode[5], "Con_2", GMC_OPERATION_MERGE, &g_childNode[6]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_Con_2(g_childNode[6], 9, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Con_2(
        g_childNode[6], (char *)"G", (char *)"GMDB", (char *)"GMDB", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Case_3
    ret = GmcYangEditChildNode(g_childNode[5], "Choice_2", GMC_OPERATION_MERGE, &g_childNode[7]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[7], "Case_3", GMC_OPERATION_MERGE, &g_childNode[8]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_Case_3(g_childNode[8], 9, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Case_3(
        g_childNode[8], (char *)"G", (char *)"GMDB", (char *)"GMDB", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(2, data.totalNum);
    ASSERT_EQ(2, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SubtreeFilterCbParam Data_1 = {0};
    Data_1.expectReplyJson = "~";
    Data_1.expectStatus = GMERR_OK;
    Data_1.step = 0;
    GmcSubtreeFilterItemT filter_state = {.rootName = "Con_0",
        .subtree = {.json = g_subtreeJson},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = GMC_SUBTREE_FILTER_DEFAULT};
    GmcSubtreeFilterT filters_1 = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter_state,
    };

    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters_1, NULL, AsyncSubtreeFilterCb, &Data_1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data_1);
    EXPECT_EQ(GMERR_OK, Data_1.expectStatus);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验数据
    char *result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat a.txt");
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_json100, result));
    free(result);
}

// 102.container作为根节点，根节点子节点整型，浮点型数据定义range约束，string定义length和pattern，invert_pattern约束
// 异步replace_update时设置符合定义范围的数据
TEST_F(Tree_Asyn_DML_test, Yang_019_102)
{
    AW_FUN_Log(LOG_STEP, "启动事务");
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName_root, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_0
    ret = testYangSetVertexPropertyInt_Root(g_rootNode, 1, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Root(
        g_rootNode, (char *)"G", (char *)"GMDB", (char *)"GMDB", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_1
    ret = GmcYangEditChildNode(g_rootNode, "Con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_Con_1(g_childNode[1], 1, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Con_1(g_childNode[1], (char *)"G", (char *)"GMDB", (char *)"GMDB",
        (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Case_2
    ret = GmcYangEditChildNode(g_rootNode, "Choice_1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[2], "Case_2", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_Case_2(g_childNode[3], 1, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Case_2(g_childNode[3], (char *)"G", (char *)"GMDB", (char *)"GMDB",
        (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_labelName_list, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_childNode[5]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_List_1(g_childNode[5], 1, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_List_1(g_childNode[5], (char *)"G", (char *)"GMDB", (char *)"GMDB",
        (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_2
    ret = GmcYangEditChildNode(g_childNode[5], "Con_2", GMC_OPERATION_INSERT, &g_childNode[6]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_Con_2(g_childNode[6], 1, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Con_2(g_childNode[6], (char *)"G", (char *)"GMDB", (char *)"GMDB",
        (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Case_3
    ret = GmcYangEditChildNode(g_childNode[5], "Choice_2", GMC_OPERATION_INSERT, &g_childNode[7]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[7], "Case_3", GMC_OPERATION_INSERT, &g_childNode[8]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_Case_3(g_childNode[8], 1, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Case_3(g_childNode[8], (char *)"G", (char *)"GMDB", (char *)"GMDB",
        (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(2, data.totalNum);
    ASSERT_EQ(2, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SubtreeFilterCbParam Data_1 = {0};
    Data_1.expectReplyJson = "~";
    Data_1.expectStatus = GMERR_OK;
    Data_1.step = 0;
    GmcSubtreeFilterItemT filter_state = {.rootName = "Con_0",
        .subtree = {.json = g_subtreeJson},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = GMC_SUBTREE_FILTER_DEFAULT};
    GmcSubtreeFilterT filters_1 = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter_state,
    };

    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters_1, NULL, AsyncSubtreeFilterCb, &Data_1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data_1);
    EXPECT_EQ(GMERR_OK, Data_1.expectStatus);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验数据
    char *result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat a.txt");
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_json102, result));
    free(result);

    AW_FUN_Log(LOG_STEP, "replace_update符合定义范围的数据");

    AW_FUN_Log(LOG_STEP, "启动事务");
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName_root, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_0
    ret = testYangSetVertexPropertyInt_Root(g_rootNode, 9, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Root(
        g_rootNode, (char *)"G", (char *)"GMDB", (char *)"GMDB", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_1
    ret = GmcYangEditChildNode(g_rootNode, "Con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_Con_1(g_childNode[1], 9, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Con_1(g_childNode[1], (char *)"G", (char *)"GMDB", (char *)"GMDB",
        (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Case_2
    ret = GmcYangEditChildNode(g_rootNode, "Choice_1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[2], "Case_2", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_Case_2(g_childNode[3], 9, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Case_2(g_childNode[3], (char *)"G", (char *)"GMDB", (char *)"GMDB",
        (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_labelName_list, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_childNode[5]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_List_1(g_childNode[5], 9, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_List_1(g_childNode[5], (char *)"G", (char *)"GMDB", (char *)"GMDB",
        (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_2
    ret = GmcYangEditChildNode(g_childNode[5], "Con_2", GMC_OPERATION_INSERT, &g_childNode[6]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_Con_2(g_childNode[6], 9, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Con_2(g_childNode[6], (char *)"G", (char *)"GMDB", (char *)"GMDB",
        (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Case_3
    ret = GmcYangEditChildNode(g_childNode[5], "Choice_2", GMC_OPERATION_INSERT, &g_childNode[7]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[7], "Case_3", GMC_OPERATION_INSERT, &g_childNode[8]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_Case_3(g_childNode[8], 9, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Case_3(g_childNode[8], (char *)"G", (char *)"GMDB", (char *)"GMDB",
        (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(2, data.totalNum);
    ASSERT_EQ(2, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Data_1 = {0};
    Data_1.expectReplyJson = "~";
    Data_1.expectStatus = GMERR_OK;
    Data_1.step = 0;
    filter_state = {.rootName = "Con_0",
        .subtree = {.json = g_subtreeJson},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = GMC_SUBTREE_FILTER_DEFAULT};
    filters_1 = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter_state,
    };

    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters_1, NULL, AsyncSubtreeFilterCb, &Data_1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data_1);
    EXPECT_EQ(GMERR_OK, Data_1.expectStatus);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验数据
    snprintf(g_command, MAX_CMD_SIZE, "cat a.txt");
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_json100, result));
    free(result);
}

// 103.container作为根节点，根节点子节点整型，浮点型数据定义range约束，string定义length和pattern，invert_pattern约束
// 异步merge_update时设置符合定义范围的数据
TEST_F(Tree_Asyn_DML_test, Yang_019_103)
{
    AW_FUN_Log(LOG_STEP, "启动事务");
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName_root, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_0
    ret = testYangSetVertexPropertyInt_Root(g_rootNode, 1, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Root(
        g_rootNode, (char *)"G", (char *)"GMDB", (char *)"GMDB", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_1
    ret = GmcYangEditChildNode(g_rootNode, "Con_1", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_Con_1(g_childNode[1], 1, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Con_1(
        g_childNode[1], (char *)"G", (char *)"GMDB", (char *)"GMDB", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Case_2
    ret = GmcYangEditChildNode(g_rootNode, "Choice_1", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[2], "Case_2", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_Case_2(g_childNode[3], 1, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Case_2(
        g_childNode[3], (char *)"G", (char *)"GMDB", (char *)"GMDB", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    char listPkName[] = "list_1_PK";
    uint32_t pkValue = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_labelName_list, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_list, listPkName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_list, 1, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_childNode[5]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_List_1(g_childNode[5], 1, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_List_1(
        g_childNode[5], (char *)"G", (char *)"GMDB", (char *)"GMDB", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_2
    ret = GmcYangEditChildNode(g_childNode[5], "Con_2", GMC_OPERATION_MERGE, &g_childNode[6]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_Con_2(g_childNode[6], 1, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Con_2(
        g_childNode[6], (char *)"G", (char *)"GMDB", (char *)"GMDB", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Case_3
    ret = GmcYangEditChildNode(g_childNode[5], "Choice_2", GMC_OPERATION_MERGE, &g_childNode[7]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[7], "Case_3", GMC_OPERATION_MERGE, &g_childNode[8]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_Case_3(g_childNode[8], 1, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Case_3(
        g_childNode[8], (char *)"G", (char *)"GMDB", (char *)"GMDB", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(2, data.totalNum);
    ASSERT_EQ(2, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SubtreeFilterCbParam Data_1 = {0};
    Data_1.expectReplyJson = "~";
    Data_1.expectStatus = GMERR_OK;
    Data_1.step = 0;
    GmcSubtreeFilterItemT filter_state = {.rootName = "Con_0",
        .subtree = {.json = g_subtreeJson},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = GMC_SUBTREE_FILTER_DEFAULT};
    GmcSubtreeFilterT filters_1 = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter_state,
    };

    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters_1, NULL, AsyncSubtreeFilterCb, &Data_1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data_1);
    EXPECT_EQ(GMERR_OK, Data_1.expectStatus);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验数据
    char *result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat a.txt");
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_json99, result));
    free(result);

    AW_FUN_Log(LOG_STEP, "merge_update符合定义范围的数据");

    AW_FUN_Log(LOG_STEP, "启动事务");
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName_root, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_0
    ret = testYangSetVertexPropertyInt_Root(g_rootNode, 9, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Root(
        g_rootNode, (char *)"G", (char *)"GMDB", (char *)"GMDB", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_1
    ret = GmcYangEditChildNode(g_rootNode, "Con_1", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_Con_1(g_childNode[1], 9, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Con_1(
        g_childNode[1], (char *)"G", (char *)"GMDB", (char *)"GMDB", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Case_2
    ret = GmcYangEditChildNode(g_rootNode, "Choice_1", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[2], "Case_2", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_Case_2(g_childNode[3], 9, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Case_2(
        g_childNode[3], (char *)"G", (char *)"GMDB", (char *)"GMDB", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_labelName_list, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_list, listPkName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_list, 1, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_childNode[5]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_List_1(g_childNode[5], 9, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_List_1(
        g_childNode[5], (char *)"G", (char *)"GMDB", (char *)"GMDB", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_2
    ret = GmcYangEditChildNode(g_childNode[5], "Con_2", GMC_OPERATION_MERGE, &g_childNode[6]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_Con_2(g_childNode[6], 9, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Con_2(
        g_childNode[6], (char *)"G", (char *)"GMDB", (char *)"GMDB", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Case_3
    ret = GmcYangEditChildNode(g_childNode[5], "Choice_2", GMC_OPERATION_MERGE, &g_childNode[7]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[7], "Case_3", GMC_OPERATION_MERGE, &g_childNode[8]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_Case_3(g_childNode[8], 9, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Case_3(
        g_childNode[8], (char *)"G", (char *)"GMDB", (char *)"GMDB", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(2, data.totalNum);
    ASSERT_EQ(2, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Data_1 = {0};
    Data_1.expectReplyJson = "~";
    Data_1.expectStatus = GMERR_OK;
    Data_1.step = 0;
    filter_state = {.rootName = "Con_0",
        .subtree = {.json = g_subtreeJson},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = GMC_SUBTREE_FILTER_DEFAULT};
    filters_1 = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter_state,
    };

    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters_1, NULL, AsyncSubtreeFilterCb, &Data_1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data_1);
    EXPECT_EQ(GMERR_OK, Data_1.expectStatus);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验数据
    snprintf(g_command, MAX_CMD_SIZE, "cat a.txt");
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_json103, result));
    free(result);
}

// 104.container作为根节点，根节点子节点中range与default共同设置，不设置该字段，异步插入数据
TEST_F(Tree_Asyn_DML_test, Yang_019_104)
{
    readJanssonFile("schema_file/tree_model_default.gmjson", &test_schema1);
    ASSERT_NE((void *)NULL, test_schema1);
    AW_FUN_Log(LOG_STEP, "创建default表");
    ret = GmcCreateVertexLabelAsync(g_stmt_async, test_schema1, g_configJson, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    free(test_schema1);

    AW_FUN_Log(LOG_STEP, "创建default表的边");
    readJanssonFile("schema_file/tree_model_default_edge_label.gmjson", &edge_schema);
    ASSERT_NE((void *)NULL, edge_schema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, edge_schema, g_configJson, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    free(edge_schema);

    AW_FUN_Log(LOG_STEP, "启动事务");
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName_default_root, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_1
    uint8_t a1Value = 1;
    ret = GmcYangEditChildNode(g_rootNode, "Con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeField(
        g_childNode[1], GMC_DATATYPE_UINT8, &a1Value, sizeof(uint8_t), "A1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_labelName_default_list, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_childNode[5]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_List_1_Lp(g_childNode[5], 1, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SubtreeFilterCbParam Data_1 = {0};
    Data_1.expectReplyJson = "~";
    Data_1.expectStatus = GMERR_OK;
    Data_1.step = 0;
    GmcSubtreeFilterItemT filter_state = {.rootName = "Con_0_default",
        .subtree = {.json = g_subtreeJson},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = GMC_SUBTREE_FILTER_DEFAULT};
    GmcSubtreeFilterT filters_1 = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter_state,
    };

    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters_1, NULL, AsyncSubtreeFilterCb, &Data_1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data_1);
    EXPECT_EQ(GMERR_OK, Data_1.expectStatus);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验数据
    char *result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat a.txt");
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_json104, result));
    free(result);

    AW_FUN_Log(LOG_STEP, "删除default边");
    const char *namespace1 = "NamespaceA01902";
    ret = GmcClearNamespaceAsync(g_stmt_async, namespace1, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
}

// 105.container作为根节点，根节点子节点中range与default共同设置，设置该字段在range范围上，异步插入数据
TEST_F(Tree_Asyn_DML_test, Yang_019_105)
{
    readJanssonFile("schema_file/tree_model_default.gmjson", &test_schema1);
    ASSERT_NE((void *)NULL, test_schema1);
    AW_FUN_Log(LOG_STEP, "创建default表");
    ret = GmcCreateVertexLabelAsync(g_stmt_async, test_schema1, g_configJson, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    free(test_schema1);

    AW_FUN_Log(LOG_STEP, "创建default表的边");
    readJanssonFile("schema_file/tree_model_default_edge_label.gmjson", &edge_schema);
    ASSERT_NE((void *)NULL, edge_schema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, edge_schema, g_configJson, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    free(edge_schema);

    AW_FUN_Log(LOG_STEP, "启动事务");
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName_default_root, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_0
    ret = testYangSetVertexPropertyInt_Default_Root(g_rootNode, 1, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Default_Root(
        g_rootNode, (char *)"G", (char *)"GMDB", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_1
    ret = GmcYangEditChildNode(g_rootNode, "Con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_Default_Con_1(g_childNode[1], 1, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Default_Con_1(g_childNode[1], (char *)"G", (char *)"GMDB", (char *)"GMDB",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_labelName_default_list, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_childNode[5]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_List_1_Lp(g_childNode[5], 1, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_Default_List_1(g_childNode[5], 1, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Default_List_1(g_childNode[5], (char *)"G", (char *)"GMDB", (char *)"GMDB",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // Con_2
    ret = GmcYangEditChildNode(g_childNode[5], "Con_2", GMC_OPERATION_INSERT, &g_childNode[6]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_Default_Con_2(g_childNode[6], 1, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Default_Con_2(g_childNode[6], (char *)"G", (char *)"GMDB", (char *)"GMDB",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SubtreeFilterCbParam Data_1 = {0};
    Data_1.expectReplyJson = "~";
    Data_1.expectStatus = GMERR_OK;
    Data_1.step = 0;
    GmcSubtreeFilterItemT filter_state = {.rootName = "Con_0_default",
        .subtree = {.json = g_subtreeJson},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = GMC_SUBTREE_FILTER_DEFAULT};
    GmcSubtreeFilterT filters_1 = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter_state,
    };

    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters_1, NULL, AsyncSubtreeFilterCb, &Data_1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data_1);
    EXPECT_EQ(GMERR_OK, Data_1.expectStatus);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验数据
    char *result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat a.txt");
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_json105, result));
    free(result);

    AW_FUN_Log(LOG_STEP, "删除default边");
    const char *namespace1 = "NamespaceA01902";
    ret = GmcClearNamespaceAsync(g_stmt_async, namespace1, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
}

// 106.container作为根节点，根节点子节点整型，浮点型数据定义range约束，string定义length和pattern，invert_pattern约束
// 异步insert时设置不符合定义范围的数据
TEST_F(Tree_Asyn_DML_test, Yang_019_106)
{
    AddWhiteList(GMERR_INVALID_PROPERTY);
    AW_FUN_Log(LOG_STEP, "启动事务");
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName_root, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_0
    AW_FUN_Log(LOG_STEP, "Con_0, insert不符合范围的数据");
    testYangSetVertexPropertyError_Root(g_rootNode, GMC_YANG_PROPERTY_OPERATION_CREATE);
    // Con_1
    AW_FUN_Log(LOG_STEP, "Con_1, insert不符合范围的数据");
    ret = GmcYangEditChildNode(g_rootNode, "Con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexPropertyError_Con_1_Or_Con2(g_childNode[1], GMC_YANG_PROPERTY_OPERATION_CREATE);
    // Case_2
    AW_FUN_Log(LOG_STEP, "Case_2, insert不符合范围的数据");
    ret = GmcYangEditChildNode(g_rootNode, "Choice_1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[2], "Case_2", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexPropertyError_Case_2_Or_Case_3(g_childNode[3], GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    AW_FUN_Log(LOG_STEP, "list_1, insert不符合范围的数据");
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_labelName_list, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_childNode[5]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_List_1_Lp(g_childNode[5], 1, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexPropertyError_List_1(g_childNode[5], GMC_YANG_PROPERTY_OPERATION_CREATE);
    // Con_2
    AW_FUN_Log(LOG_STEP, "Con_2, insert不符合范围的数据");
    ret = GmcYangEditChildNode(g_childNode[5], "Con_2", GMC_OPERATION_INSERT, &g_childNode[6]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexPropertyError_Con_1_Or_Con2(g_childNode[6], GMC_YANG_PROPERTY_OPERATION_CREATE);
    // Case_3
    AW_FUN_Log(LOG_STEP, "Case_3, insert不符合范围的数据");
    ret = GmcYangEditChildNode(g_childNode[5], "Choice_2", GMC_OPERATION_INSERT, &g_childNode[7]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[7], "Case_3", GMC_OPERATION_INSERT, &g_childNode[8]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexPropertyError_Case_2_Or_Case_3(g_childNode[8], GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(2, data.totalNum);
    ASSERT_EQ(2, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SubtreeFilterCbParam Data_1 = {0};
    Data_1.expectReplyJson = "~";
    Data_1.expectStatus = GMERR_OK;
    Data_1.step = 0;
    GmcSubtreeFilterItemT filter_state = {.rootName = "Con_0",
        .subtree = {.json = g_subtreeJson},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = GMC_SUBTREE_FILTER_DEFAULT};
    GmcSubtreeFilterT filters_1 = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter_state,
    };

    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters_1, NULL, AsyncSubtreeFilterCb, &Data_1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data_1);
    EXPECT_EQ(GMERR_OK, Data_1.expectStatus);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验数据
    char *result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat a.txt");
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_json106, result));
    free(result);
}

// 107.container作为根节点，根节点子节点整型，浮点型数据定义range约束，string定义length和pattern，invert_pattern约束
// 异步replace_insert时设置不符合定义范围的数据
TEST_F(Tree_Asyn_DML_test, Yang_019_107)
{
    AddWhiteList(GMERR_INVALID_PROPERTY);
    AW_FUN_Log(LOG_STEP, "启动事务");
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName_root, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_0
    AW_FUN_Log(LOG_STEP, "Con_0, replace_insert不符合范围的数据");
    testYangSetVertexPropertyError_Root(g_rootNode, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    // Con_1
    AW_FUN_Log(LOG_STEP, "Con_1, replace_insert不符合范围的数据");
    ret = GmcYangEditChildNode(g_rootNode, "Con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexPropertyError_Con_1_Or_Con2(g_childNode[1], GMC_YANG_PROPERTY_OPERATION_CREATE);
    // Case_2
    AW_FUN_Log(LOG_STEP, "Case_2, replace_insert不符合范围的数据");
    ret = GmcYangEditChildNode(g_rootNode, "Choice_1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[2], "Case_2", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexPropertyError_Case_2_Or_Case_3(g_childNode[3], GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    AW_FUN_Log(LOG_STEP, "list_1, replace_insert不符合范围的数据");
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_labelName_list, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_childNode[5]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_List_1_Lp(g_childNode[5], 1, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexPropertyError_List_1(g_childNode[5], GMC_YANG_PROPERTY_OPERATION_REPLACE);
    // Con_2
    AW_FUN_Log(LOG_STEP, "Con_2, replace_insert不符合范围的数据");
    ret = GmcYangEditChildNode(g_childNode[5], "Con_2", GMC_OPERATION_INSERT, &g_childNode[6]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexPropertyError_Con_1_Or_Con2(g_childNode[6], GMC_YANG_PROPERTY_OPERATION_CREATE);
    // Case_3
    AW_FUN_Log(LOG_STEP, "Case_3, replace_insert不符合范围的数据");
    ret = GmcYangEditChildNode(g_childNode[5], "Choice_2", GMC_OPERATION_INSERT, &g_childNode[7]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[7], "Case_3", GMC_OPERATION_INSERT, &g_childNode[8]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexPropertyError_Case_2_Or_Case_3(g_childNode[8], GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(2, data.totalNum);
    ASSERT_EQ(2, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SubtreeFilterCbParam Data_1 = {0};
    Data_1.expectReplyJson = "~";
    Data_1.expectStatus = GMERR_OK;
    Data_1.step = 0;
    GmcSubtreeFilterItemT filter_state = {.rootName = "Con_0",
        .subtree = {.json = g_subtreeJson},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = GMC_SUBTREE_FILTER_DEFAULT};
    GmcSubtreeFilterT filters_1 = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter_state,
    };

    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters_1, NULL, AsyncSubtreeFilterCb, &Data_1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data_1);
    EXPECT_EQ(GMERR_OK, Data_1.expectStatus);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验数据
    char *result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat a.txt");
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_json106, result));
    free(result);
}

// 108.container作为根节点，根节点子节点整型，浮点型数据定义range约束，string定义length和pattern，invert_pattern约束
// 异步merge_insert时设置不符合定义范围的数据
TEST_F(Tree_Asyn_DML_test, Yang_019_108)
{
    AddWhiteList(GMERR_INVALID_PROPERTY);
    AW_FUN_Log(LOG_STEP, "启动事务");
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName_root, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_0
    AW_FUN_Log(LOG_STEP, "Con_0, merge_insert不符合范围的数据");
    testYangSetVertexPropertyError_Root(g_rootNode, GMC_YANG_PROPERTY_OPERATION_MERGE);
    // Con_1
    AW_FUN_Log(LOG_STEP, "Con_1, merge_insert不符合范围的数据");
    ret = GmcYangEditChildNode(g_rootNode, "Con_1", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexPropertyError_Con_1_Or_Con2(g_childNode[1], GMC_YANG_PROPERTY_OPERATION_MERGE);
    // Case_2
    AW_FUN_Log(LOG_STEP, "Case_2, merge_insert不符合范围的数据");
    ret = GmcYangEditChildNode(g_rootNode, "Choice_1", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[2], "Case_2", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexPropertyError_Case_2_Or_Case_3(g_childNode[3], GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    AW_FUN_Log(LOG_STEP, "list_1, merge_insert不符合范围的数据");
    char listPkName[] = "list_1_PK";
    uint32_t pkValue = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_labelName_list, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_list, listPkName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_list, 1, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_childNode[5]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_List_1_Lp(g_childNode[5], 1, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexPropertyError_List_1(g_childNode[5], GMC_YANG_PROPERTY_OPERATION_MERGE);
    // Con_2
    AW_FUN_Log(LOG_STEP, "Con_2, merge_insert不符合范围的数据");
    ret = GmcYangEditChildNode(g_childNode[5], "Con_2", GMC_OPERATION_MERGE, &g_childNode[6]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexPropertyError_Con_1_Or_Con2(g_childNode[6], GMC_YANG_PROPERTY_OPERATION_MERGE);
    // Case_3
    AW_FUN_Log(LOG_STEP, "Case_3, merge_insert不符合范围的数据");
    ret = GmcYangEditChildNode(g_childNode[5], "Choice_2", GMC_OPERATION_MERGE, &g_childNode[7]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[7], "Case_3", GMC_OPERATION_MERGE, &g_childNode[8]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexPropertyError_Case_2_Or_Case_3(g_childNode[8], GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(2, data.totalNum);
    ASSERT_EQ(2, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SubtreeFilterCbParam Data_1 = {0};
    Data_1.expectReplyJson = "~";
    Data_1.expectStatus = GMERR_OK;
    Data_1.step = 0;
    GmcSubtreeFilterItemT filter_state = {.rootName = "Con_0",
        .subtree = {.json = g_subtreeJson},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = GMC_SUBTREE_FILTER_DEFAULT};
    GmcSubtreeFilterT filters_1 = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter_state,
    };

    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters_1, NULL, AsyncSubtreeFilterCb, &Data_1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data_1);
    EXPECT_EQ(GMERR_OK, Data_1.expectStatus);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验数据
    char *result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat a.txt");
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_json106, result));
    free(result);
}

// 109.container作为根节点，根节点子节点整型，浮点型数据定义range约束，string定义length和pattern，invert_pattern约束
// 异步replace_update时设置不符合定义范围的数据
TEST_F(Tree_Asyn_DML_test, Yang_019_109)
{
    AddWhiteList(GMERR_INVALID_PROPERTY);
    AW_FUN_Log(LOG_STEP, "启动事务");
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName_root, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_0
    ret = testYangSetVertexPropertyInt_Root(g_rootNode, 9, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Root(
        g_rootNode, (char *)"G", (char *)"GMDB", (char *)"GMDB", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // Con_1
    ret = GmcYangEditChildNode(g_rootNode, "Con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_Con_1(g_childNode[1], 9, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Con_1(g_childNode[1], (char *)"G", (char *)"GMDB", (char *)"GMDB",
        (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // Case_2
    ret = GmcYangEditChildNode(g_rootNode, "Choice_1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[2], "Case_2", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_Case_2(g_childNode[3], 9, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Case_2(g_childNode[3], (char *)"G", (char *)"GMDB", (char *)"GMDB",
        (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_labelName_list, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_childNode[5]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_List_1(g_childNode[5], 9, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_List_1(g_childNode[5], (char *)"G", (char *)"GMDB", (char *)"GMDB",
        (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // Con_2
    ret = GmcYangEditChildNode(g_childNode[5], "Con_2", GMC_OPERATION_INSERT, &g_childNode[6]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_Con_2(g_childNode[6], 9, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Con_2(g_childNode[6], (char *)"G", (char *)"GMDB", (char *)"GMDB",
        (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // Case_3
    ret = GmcYangEditChildNode(g_childNode[5], "Choice_2", GMC_OPERATION_INSERT, &g_childNode[7]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[7], "Case_3", GMC_OPERATION_INSERT, &g_childNode[8]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_Case_3(g_childNode[8], 9, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Case_3(g_childNode[8], (char *)"G", (char *)"GMDB", (char *)"GMDB",
        (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(2, data.totalNum);
    ASSERT_EQ(2, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SubtreeFilterCbParam Data_1 = {0};
    Data_1.expectReplyJson = "~";
    Data_1.expectStatus = GMERR_OK;
    Data_1.step = 0;
    GmcSubtreeFilterItemT filter_state = {.rootName = "Con_0",
        .subtree = {.json = g_subtreeJson},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = GMC_SUBTREE_FILTER_DEFAULT};
    GmcSubtreeFilterT filters_1 = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter_state,
    };

    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters_1, NULL, AsyncSubtreeFilterCb, &Data_1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data_1);
    EXPECT_EQ(GMERR_OK, Data_1.expectStatus);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验数据
    char *result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat a.txt");
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_json100, result));
    free(result);

    AW_FUN_Log(LOG_STEP, "replace_update设置不符合定义范围的数据");
    AW_FUN_Log(LOG_STEP, "启动事务");
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName_root, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_0
    AW_FUN_Log(LOG_STEP, "Con_0, replace_update不符合范围的数据");
    testYangSetVertexPropertyError_Root(g_rootNode, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    // Con_1
    AW_FUN_Log(LOG_STEP, "Con_1, replace_update不符合范围的数据");
    ret = GmcYangEditChildNode(g_rootNode, "Con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexPropertyError_Con_1_Or_Con2(g_childNode[1], GMC_YANG_PROPERTY_OPERATION_CREATE);
    // Case_2
    AW_FUN_Log(LOG_STEP, "Case_2, replace_update不符合范围的数据");
    ret = GmcYangEditChildNode(g_rootNode, "Choice_1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[2], "Case_2", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexPropertyError_Case_2_Or_Case_3(g_childNode[3], GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    AW_FUN_Log(LOG_STEP, "list_1, replace_update不符合范围的数据");
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_labelName_list, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_childNode[5]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_List_1_Lp(g_childNode[5], 9, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexPropertyError_List_1(g_childNode[5], GMC_YANG_PROPERTY_OPERATION_REPLACE);
    // Con_2
    AW_FUN_Log(LOG_STEP, "Con_2, replace_update不符合范围的数据");
    ret = GmcYangEditChildNode(g_childNode[5], "Con_2", GMC_OPERATION_INSERT, &g_childNode[6]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexPropertyError_Con_1_Or_Con2(g_childNode[6], GMC_YANG_PROPERTY_OPERATION_CREATE);
    // Case_3
    AW_FUN_Log(LOG_STEP, "Case_3, replace_update不符合范围的数据");
    ret = GmcYangEditChildNode(g_childNode[5], "Choice_2", GMC_OPERATION_INSERT, &g_childNode[7]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[7], "Case_3", GMC_OPERATION_INSERT, &g_childNode[8]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexPropertyError_Case_2_Or_Case_3(g_childNode[8], GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(2, data.totalNum);
    ASSERT_EQ(2, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Data_1.expectReplyJson = "~";
    Data_1.expectStatus = GMERR_OK;
    Data_1.step = 0;
    filter_state = {.rootName = "Con_0",
        .subtree = {.json = g_subtreeJson},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = GMC_SUBTREE_FILTER_DEFAULT};
    filters_1 = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter_state,
    };

    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters_1, NULL, AsyncSubtreeFilterCb, &Data_1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data_1);
    EXPECT_EQ(GMERR_OK, Data_1.expectStatus);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验数据
    snprintf(g_command, MAX_CMD_SIZE, "cat a.txt");
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_json109, result));
    free(result);
}

// 110.container作为根节点，根节点子节点整型，浮点型数据定义range约束，string定义length和pattern，invert_pattern约束
// 异步merge_update时设置不符合定义范围的数据
TEST_F(Tree_Asyn_DML_test, Yang_019_110)
{
    AddWhiteList(GMERR_INVALID_PROPERTY);
    AW_FUN_Log(LOG_STEP, "启动事务");
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName_root, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_0
    ret = testYangSetVertexPropertyInt_Root(g_rootNode, 1, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Root(
        g_rootNode, (char *)"G", (char *)"GMDB", (char *)"GMDB", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_1
    ret = GmcYangEditChildNode(g_rootNode, "Con_1", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_Con_1(g_childNode[1], 1, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Con_1(
        g_childNode[1], (char *)"G", (char *)"GMDB", (char *)"GMDB", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Case_2
    ret = GmcYangEditChildNode(g_rootNode, "Choice_1", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[2], "Case_2", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_Case_2(g_childNode[3], 1, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Case_2(
        g_childNode[3], (char *)"G", (char *)"GMDB", (char *)"GMDB", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    char listPkName[] = "list_1_PK";
    uint32_t pkValue = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_labelName_list, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_list, listPkName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_list, 1, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_childNode[5]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_List_1(g_childNode[5], 1, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_List_1(
        g_childNode[5], (char *)"G", (char *)"GMDB", (char *)"GMDB", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_2
    ret = GmcYangEditChildNode(g_childNode[5], "Con_2", GMC_OPERATION_MERGE, &g_childNode[6]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_Con_2(g_childNode[6], 1, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Con_2(
        g_childNode[6], (char *)"G", (char *)"GMDB", (char *)"GMDB", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Case_3
    ret = GmcYangEditChildNode(g_childNode[5], "Choice_2", GMC_OPERATION_MERGE, &g_childNode[7]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[7], "Case_3", GMC_OPERATION_MERGE, &g_childNode[8]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_Case_3(g_childNode[8], 1, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Case_3(
        g_childNode[8], (char *)"G", (char *)"GMDB", (char *)"GMDB", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(2, data.totalNum);
    ASSERT_EQ(2, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SubtreeFilterCbParam Data_1 = {0};
    Data_1.expectReplyJson = "~";
    Data_1.expectStatus = GMERR_OK;
    Data_1.step = 0;
    GmcSubtreeFilterItemT filter_state = {.rootName = "Con_0",
        .subtree = {.json = g_subtreeJson},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = GMC_SUBTREE_FILTER_DEFAULT};
    GmcSubtreeFilterT filters_1 = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter_state,
    };

    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters_1, NULL, AsyncSubtreeFilterCb, &Data_1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data_1);
    EXPECT_EQ(GMERR_OK, Data_1.expectStatus);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验数据
    char *result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat a.txt");
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_json99, result));
    free(result);

    AW_FUN_Log(LOG_STEP, "merge_update不符合定义范围的数据");
    AW_FUN_Log(LOG_STEP, "启动事务");
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName_root, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_0
    AW_FUN_Log(LOG_STEP, "Con_0, merge_update不符合范围的数据");
    testYangSetVertexPropertyError_Root(g_rootNode, GMC_YANG_PROPERTY_OPERATION_MERGE);
    // Con_1
    AW_FUN_Log(LOG_STEP, "Con_1, merge_update不符合范围的数据");
    ret = GmcYangEditChildNode(g_rootNode, "Con_1", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexPropertyError_Con_1_Or_Con2(g_childNode[1], GMC_YANG_PROPERTY_OPERATION_MERGE);
    // Case_2
    AW_FUN_Log(LOG_STEP, "Case_2, merge_update不符合范围的数据");
    ret = GmcYangEditChildNode(g_rootNode, "Choice_1", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[2], "Case_2", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexPropertyError_Case_2_Or_Case_3(g_childNode[3], GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    AW_FUN_Log(LOG_STEP, "list_1, merge_update不符合范围的数据");
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_labelName_list, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_list, listPkName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_list, 1, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_childNode[5]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexPropertyError_List_1(g_childNode[5], GMC_YANG_PROPERTY_OPERATION_MERGE);
    // Con_2
    AW_FUN_Log(LOG_STEP, "Con_2, merge_update不符合范围的数据");
    ret = GmcYangEditChildNode(g_childNode[5], "Con_2", GMC_OPERATION_MERGE, &g_childNode[6]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexPropertyError_Con_1_Or_Con2(g_childNode[6], GMC_YANG_PROPERTY_OPERATION_MERGE);
    // Case_3
    AW_FUN_Log(LOG_STEP, "Case_3, merge_update不符合范围的数据");
    ret = GmcYangEditChildNode(g_childNode[5], "Choice_2", GMC_OPERATION_MERGE, &g_childNode[7]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[7], "Case_3", GMC_OPERATION_MERGE, &g_childNode[8]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexPropertyError_Case_2_Or_Case_3(g_childNode[8], GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(2, data.totalNum);
    ASSERT_EQ(2, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Data_1 = {0};
    Data_1.expectReplyJson = "~";
    Data_1.expectStatus = GMERR_OK;
    Data_1.step = 0;
    filter_state = {.rootName = "Con_0",
        .subtree = {.json = g_subtreeJson},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = GMC_SUBTREE_FILTER_DEFAULT};
    filters_1 = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter_state,
    };

    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters_1, NULL, AsyncSubtreeFilterCb, &Data_1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data_1);
    EXPECT_EQ(GMERR_OK, Data_1.expectStatus);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验数据
    snprintf(g_command, MAX_CMD_SIZE, "cat a.txt");
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_json99, result));
    free(result);
}

// 111.container作为根节点，根节点子节点string定义相同的pattern，invert_pattern(^GMDB$)约束在一个字段上，异步插入数据
TEST_F(Tree_Asyn_DML_test, Yang_019_111)
{
    AddWhiteList(GMERR_INVALID_PROPERTY);
    readJanssonFile("schema_file/tree_model_same_pattern_and_invert_pattern.gmjson", &test_schema1);
    ASSERT_NE((void *)NULL, test_schema1);
    AW_FUN_Log(LOG_STEP, "创建conflict表");
    ret = GmcCreateVertexLabelAsync(g_stmt_async, test_schema1, g_configJson, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    free(test_schema1);

    AW_FUN_Log(LOG_STEP, "创建conflict表的边");
    readJanssonFile(
        "schema_file/tree_model_same_pattern_and_invert_pattern_edge_label.gmjson", &edge_schema);
    ASSERT_NE((void *)NULL, edge_schema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, edge_schema, g_configJson, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    free(edge_schema);

    AW_FUN_Log(LOG_STEP, "启动事务");
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName_conflict_root, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_0
    AW_FUN_Log(LOG_STEP, "Con_0_conflict, GmcYangSetNodeProperty失败");
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)"GMDB", (strlen("GMDB")), "F11",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)"1", (strlen("1")), "F11",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)"~!@#$%^&*()_+",
        (strlen((char *)"~!@#$%^&*()_+")), "F11", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    // Con_1
    AW_FUN_Log(LOG_STEP, "Con_1, GmcYangSetNodeProperty失败");
    ret = GmcYangEditChildNode(g_rootNode, "Con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeField(g_childNode[1], GMC_DATATYPE_STRING, (char *)"GMDB", (strlen("GMDB")), "A11",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(
        g_childNode[1], GMC_DATATYPE_STRING, (char *)"1", (strlen("1")), "A11", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_childNode[1], GMC_DATATYPE_STRING, (char *)"~!@#$%^&*()_+", (strlen("~!@#$%^&*()_+")),
        "A11", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    AW_FUN_Log(LOG_STEP, "list_1_conflict, GmcYangSetNodeProperty失败");
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_labelName_conflict_list, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_childNode[5]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_List_1_Lp(g_childNode[5], 1, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeField(g_childNode[5], GMC_DATATYPE_STRING, (char *)"GMDB", (strlen("GMDB")), "L11",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(
        g_childNode[5], GMC_DATATYPE_STRING, (char *)"1", (strlen("1")), "L11", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_childNode[5], GMC_DATATYPE_STRING, (char *)"~!@#$%^&*()_+", (strlen("~!@#$%^&*()_+")),
        "L11", GMC_YANG_PROPERTY_OPERATION_CREATE);

    // Con_2
    AW_FUN_Log(LOG_STEP, "Con_2, GmcYangSetNodeProperty失败");
    ret = GmcYangEditChildNode(g_childNode[5], "Con_2", GMC_OPERATION_INSERT, &g_childNode[6]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeField(g_childNode[6], GMC_DATATYPE_STRING, (char *)"GMDB", (strlen("GMDB")), "A11",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(
        g_childNode[6], GMC_DATATYPE_STRING, (char *)"1", (strlen("1")), "A11", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_childNode[6], GMC_DATATYPE_STRING, (char *)"~!@#$%^&*()_+", (strlen("~!@#$%^&*()_+")),
        "A11", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SubtreeFilterCbParam Data_1 = {0};
    Data_1.expectReplyJson = "~";
    Data_1.expectStatus = GMERR_OK;
    Data_1.step = 0;
    GmcSubtreeFilterItemT filter_state = {.rootName = "Con_0_conflict",
        .subtree = {.json = g_subtreeJson},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = GMC_SUBTREE_FILTER_DEFAULT};
    GmcSubtreeFilterT filters_1 = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter_state,
    };

    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters_1, NULL, AsyncSubtreeFilterCb, &Data_1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data_1);
    EXPECT_EQ(GMERR_OK, Data_1.expectStatus);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验数据
    char *result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat a.txt");
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_json111, result));
    free(result);

    AW_FUN_Log(LOG_STEP, "删除default边");
    const char *namespace1 = "NamespaceA01902";
    ret = GmcClearNamespaceAsync(g_stmt_async, namespace1, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
}

// 112.container作为根节点，根节点子节点range，length，pattern，invert_pattern与default共同设置
// 设置该字段不在范围上，然后再设置在范围上，异步插入数据
TEST_F(Tree_Asyn_DML_test, Yang_019_112)
{
    AddWhiteList(GMERR_INVALID_PROPERTY);
    readJanssonFile("schema_file/tree_model_default.gmjson", &test_schema1);
    ASSERT_NE((void *)NULL, test_schema1);
    AW_FUN_Log(LOG_STEP, "创建default表");
    ret = GmcCreateVertexLabelAsync(g_stmt_async, test_schema1, g_configJson, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    free(test_schema1);

    AW_FUN_Log(LOG_STEP, "创建default表的边");
    readJanssonFile("schema_file/tree_model_default_edge_label.gmjson", &edge_schema);
    ASSERT_NE((void *)NULL, edge_schema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, edge_schema, g_configJson, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    free(edge_schema);

    AW_FUN_Log(LOG_STEP, "启动事务");
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName_default_root, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_0
    AW_FUN_Log(LOG_STEP, "Con_0_default, GmcYangSetNodeProperty不符合范围的数据");
    testYangSetVertexPropertyError_Default_Root(g_rootNode, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_FUN_Log(LOG_STEP, "Con_0_default, GmcYangSetNodeProperty符合范围的数据");
    ret = testYangSetVertexPropertyInt_Default_Root(g_rootNode, 1, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Default_Root(
        g_rootNode, (char *)"G", (char *)"GMDB", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // Con_1
    AW_FUN_Log(LOG_STEP, "Con_1, GmcYangSetNodeProperty不符合范围的数据");
    ret = GmcYangEditChildNode(g_rootNode, "Con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexPropertyError_Default_Con_1_Or_Con2(g_childNode[1], GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_FUN_Log(LOG_STEP, "Con_1, GmcYangSetNodeProperty符合范围的数据");
    ret = testYangSetVertexPropertyInt_Default_Con_1(g_childNode[1], 1, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Default_Con_1(g_childNode[1], (char *)"G", (char *)"GMDB", (char *)"GMDB",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    AW_FUN_Log(LOG_STEP, "list_1_default, GmcYangSetNodeProperty不符合范围的数据");
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_labelName_default_list, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_childNode[5]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyInt_List_1_Lp(g_childNode[5], 1, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexPropertyError_Default_List_1(g_childNode[5], GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_FUN_Log(LOG_STEP, "list_1_default, GmcYangSetNodeProperty符合范围的数据");
    ret = testYangSetVertexPropertyInt_Default_List_1(g_childNode[5], 1, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Default_List_1(g_childNode[5], (char *)"G", (char *)"GMDB", (char *)"GMDB",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "Con_2, GmcYangSetNodeProperty不符合范围的数据");
    // Con_2
    ret = GmcYangEditChildNode(g_childNode[5], "Con_2", GMC_OPERATION_INSERT, &g_childNode[6]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexPropertyError_Default_Con_1_Or_Con2(g_childNode[6], GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_FUN_Log(LOG_STEP, "Con_2, GmcYangSetNodeProperty符合范围的数据");
    ret = testYangSetVertexPropertyInt_Default_Con_2(g_childNode[6], 1, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexPropertyString_Default_Con_2(g_childNode[6], (char *)"G", (char *)"GMDB", (char *)"GMDB",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SubtreeFilterCbParam Data_1 = {0};
    Data_1.expectReplyJson = "~";
    Data_1.expectStatus = GMERR_OK;
    Data_1.step = 0;
    GmcSubtreeFilterItemT filter_state = {.rootName = "Con_0_default",
        .subtree = {.json = g_subtreeJson},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = GMC_SUBTREE_FILTER_DEFAULT};
    GmcSubtreeFilterT filters_1 = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter_state,
    };

    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters_1, NULL, AsyncSubtreeFilterCb, &Data_1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data_1);
    EXPECT_EQ(GMERR_OK, Data_1.expectStatus);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验数据
    char *result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat a.txt");
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_json112, result));
    free(result);

    AW_FUN_Log(LOG_STEP, "删除default边");
    const char *namespace1 = "NamespaceA01902";
    ret = GmcClearNamespaceAsync(g_stmt_async, namespace1, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
}

// 113.container作为根节点，根节点子节点设置length的范围为min..max，插入数据时设置64K+1的字符串，然后再设置64K的字符串
TEST_F(Tree_Asyn_DML_test, Yang_019_113)
{
    AddWhiteList(GMERR_INVALID_PROPERTY);
    readJanssonFile("schema_file/tree_model_min_and_max_length.gmjson", &test_schema1);
    ASSERT_NE((void *)NULL, test_schema1);
    AW_FUN_Log(LOG_STEP, "创建min..max表");
    ret = GmcCreateVertexLabelAsync(g_stmt_async, test_schema1, g_configJson, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    free(test_schema1);

    AW_FUN_Log(LOG_STEP, "启动事务");
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName_max_root, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_0
    AW_FUN_Log(LOG_STEP, "设置Con_0, 64k+1长度失败");
    char Over64k[1024 * 64 + 2] = {0};
    memset(Over64k, 'a', sizeof(Over64k));
    Over64k[1024 * 64 + 1] = '\0';
    AW_FUN_Log(LOG_STEP, "Over64k length is %d", (strlen(Over64k)));
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, Over64k, (strlen(Over64k)), "F11", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    AW_FUN_Log(LOG_STEP, "设置Con_0, 64k长度成功");
    char String64k[1024 * 64 + 1] = {0};
    memset(String64k, 'a', sizeof(String64k));
    String64k[1024 * 64] = '\0';
    AW_FUN_Log(LOG_STEP, "String64k length is %d", (strlen(String64k)));
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, String64k, (strlen(String64k)), "F11", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Con_1
    AW_FUN_Log(LOG_STEP, "设置Con_1, 64k+1长度失败");
    ret = GmcYangEditChildNode(g_rootNode, "Con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeField(
        g_childNode[1], GMC_DATATYPE_STRING, Over64k, (strlen(Over64k)), "A11", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    AW_FUN_Log(LOG_STEP, "设置Con_1, 64k长度成功");
    ret = testYangSetNodeField(
        g_childNode[1], GMC_DATATYPE_STRING, String64k, (strlen(String64k)), "A11", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SubtreeFilterCbParam Data_1 = {0};
    Data_1.expectReplyJson = "~";
    Data_1.expectStatus = GMERR_OK;
    Data_1.step = 0;
    GmcSubtreeFilterItemT filter_state = {.rootName = "Con_0_max_string",
        .subtree = {.json = g_subtreeJson},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = GMC_SUBTREE_FILTER_DEFAULT};
    GmcSubtreeFilterT filters_1 = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter_state,
    };

    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters_1, NULL, AsyncSubtreeFilterCb, &Data_1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data_1);
    EXPECT_EQ(GMERR_OK, Data_1.expectStatus);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "删除min..max表");
    ret = GmcDropVertexLabelAsync(g_stmt_async, g_labelName_max_root, drop_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
}

// 119.pattern数组内元素互相矛盾，建表成功，写入失败
TEST_F(Tree_Asyn_DML_test, Yang_019_119)
{
    AddWhiteList(GMERR_INVALID_PROPERTY);
    char labelName[LABELNAME_MAX_LENGTH] = "Con_0_contradiction";
    readJanssonFile("error_schema_file/tree_model_pattern_inner_contradiction.gmjson", &test_schema1);
    ASSERT_NE((void *)NULL, test_schema1);
    AW_FUN_Log(LOG_STEP, "建表");
    ret = GmcCreateVertexLabelAsync(g_stmt_async, test_schema1, g_configJson, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    free(test_schema1);

    AW_FUN_Log(LOG_STEP, "启动事务");
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)"GMDB", (strlen("GMDB")), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // pattern数组内元素互相矛盾无法写入数据
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)"GMDB", (strlen("GMDB")), "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SubtreeFilterCbParam Data_1 = {0};
    Data_1.expectReplyJson = "~";
    Data_1.expectStatus = GMERR_OK;
    Data_1.step = 0;
    GmcSubtreeFilterItemT filter_state = {.rootName = "Con_0_contradiction",
        .subtree = {.json = g_subtreeJson},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = GMC_SUBTREE_FILTER_DEFAULT};
    GmcSubtreeFilterT filters_1 = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter_state,
    };

    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters_1, NULL, AsyncSubtreeFilterCb, &Data_1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data_1);
    EXPECT_EQ(GMERR_OK, Data_1.expectStatus);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验数据
    char *result = NULL;
    snprintf(g_command, MAX_CMD_SIZE, "cat a.txt");
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(expect_json119, result));
    free(result);

    ret = GmcDropVertexLabelAsync(g_stmt_async, labelName, drop_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
}
