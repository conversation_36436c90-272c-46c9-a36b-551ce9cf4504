/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: 034
 * Author: ywx1037054
 * Create: 2022-9-5
 * 补充兼容性用例，当前视图补充场景与YANG功能并无关联，使用视图V3和V5设计思路与实现原理不同，不能实现完全对应
 * 当前是对V5不关注的视图部分功能和字段做了加固测试
 */
#include "tools.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
GmcStmtT *g_stmt_sync[10] = {0};
GmcConnT *g_conn_async = NULL;
GmcStmtT *g_stmt_async = NULL;
GmcStmtT *g_stmt_root = NULL;
GmcStmtT *g_stmt_list[10] = {0};
GmcNodeT *g_rootNode = NULL;
GmcNodeT *g_childNode[40] = {0};

class V3_HashDfx : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void V3_HashDfx::SetUpTestCase()
{
    int ret;
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void V3_HashDfx::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}

void V3_HashDfx::SetUp()
{
    int ret;
    AsyncUserDataT data = {0};
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t i = 0;
    for (i = 0; i < 10; i++) {
        ret = GmcAllocStmt(g_conn_async, &g_stmt_list[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcAllocStmt(g_conn, &g_stmt_sync[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = g_namespace;
    nspCfg.tablespaceName = NULL;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观
    AW_FUN_Log(LOG_INFO, "namespaceName: %s\n", g_namespace);

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 创建Vertex表
   testCreateLabelMix(g_stmt_async);
   testCreateLabelCL8(g_stmt_async);
   testCreateLabelCCHCA8(g_stmt_async);
   testCreateLabelC8(g_stmt_async);
    // 同步连接也使用namespace
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void V3_HashDfx::TearDown()
{
    int ret;
    uint32_t i = 0;
    AsyncUserDataT data = {0};
    // 异步删除namespace
    ret = GmcClearNamespaceAsync(g_stmt_async, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
     // 删除namespace
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcDropNamespaceAsync(g_stmt_async, g_namespace, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    for (i = 0; i < 10; i++) {
        GmcFreeStmt(g_stmt_sync[i]);
    }
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    GmcFreeStmt(g_stmt_root);
    for (i = 0; i < 10; i++) {
        GmcFreeStmt(g_stmt_list[i]);
    }
    testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_conn_async = NULL;
    g_stmt_async = NULL;
    g_stmt_root = NULL;
    for (i = 0; i < 10; i++) {
        g_stmt_list[i] = NULL;
        g_stmt_sync[i] = NULL;
    }

    g_rootNode = NULL;
    for (i = 0; i < 10; i++) {
        g_childNode[i] = NULL;
    }
}

class V3_HashDfx2 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};
void V3_HashDfx2::SetUpTestCase()
{
    int ret;
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void V3_HashDfx2::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}

void V3_HashDfx2::SetUp()
{
}

void V3_HashDfx2::TearDown()
{
}

class V3_HashDfx3 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};
void V3_HashDfx3::SetUpTestCase()
{
    int ret;
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=80\"");
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void V3_HashDfx3::TearDownTestCase()
{
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}

void V3_HashDfx3::SetUp()
{
}

void V3_HashDfx3::TearDown()
{
}

class V3_HashDfx4 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};
void V3_HashDfx4::SetUpTestCase()
{
    int ret;
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=200\"");
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void V3_HashDfx4::TearDownTestCase()
{
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}

void V3_HashDfx4::SetUp()
{
}

void V3_HashDfx4::TearDown()
{
}

void prepares(int start_num, int end_num)
{
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "C_MIX1_N1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = start_num;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

//     // 设置child节点
    for (uint32_t i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "C_MIX1_N2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        testYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        ret = GmcYangEditChildNode(g_childNode[1], "C_MIX1_N3", GMC_OPERATION_INSERT, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

         ret = GmcYangEditChildNode(g_childNode[2], "C_MIX1_N4", GMC_OPERATION_INSERT, &g_childNode[3]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         fieldValue = i;
         testYangSetNodeProperty(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

         ret = GmcYangEditChildNode(g_childNode[3], "C_MIX1_N5", GMC_OPERATION_INSERT, &g_childNode[4]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         fieldValue = i;
         testYangSetNodeProperty(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
         ret = GmcBatchAddDML(batch, g_stmt_list[1]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "C_MIX1_N6", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_list[1], g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[6]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        testYangSetNodeProperty(g_childNode[6], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

      ret = GmcYangEditChildNode(g_childNode[6], "C_MIX1_N7", GMC_OPERATION_INSERT, &g_childNode[7]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

      ret = GmcYangEditChildNode(g_childNode[7], "C_MIX1_N8", GMC_OPERATION_INSERT, &g_childNode[8]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      fieldValue = i;
      testYangSetNodeProperty(g_childNode[8], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
      ret = GmcBatchAddDML(batch, g_stmt_list[2]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   }
    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

GmcConnT *connAsync2;
GmcStmtT *stmtAsync2;
GmcStmtT *g_stmt_root2 = NULL;
GmcStmtT *g_stmt_list2[10];
GmcNodeT *g_childNode2[40] = {0};
GmcNodeT *g_rootNode2 = NULL;
void preparesConn(int start_num, int end_num)
{
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(connAsync2, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(connAsync2, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2, "C_MIX1_N1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root2, &g_rootNode2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = start_num;
    testYangSetNodeProperty(g_rootNode2, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list2[1], "C_MIX1_N2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root2, g_stmt_list2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list2[1], &g_childNode2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        testYangSetNodeProperty(g_childNode2[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        ret = GmcYangEditChildNode(g_childNode2[1], "C_MIX1_N3", GMC_OPERATION_INSERT, &g_childNode2[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

         ret = GmcYangEditChildNode(g_childNode2[2], "C_MIX1_N4", GMC_OPERATION_INSERT, &g_childNode2[3]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         fieldValue = i;
         testYangSetNodeProperty(g_childNode2[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

         ret = GmcYangEditChildNode(g_childNode2[3], "C_MIX1_N5", GMC_OPERATION_INSERT, &g_childNode2[4]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         fieldValue = i;
         testYangSetNodeProperty(g_childNode2[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
         ret = GmcBatchAddDML(batch, g_stmt_list2[1]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmt_list2[2], "C_MIX1_N6", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_list2[1], g_stmt_list2[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list2[2], &g_childNode2[6]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        testYangSetNodeProperty(g_childNode2[6], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

      ret = GmcYangEditChildNode(g_childNode2[6], "C_MIX1_N7", GMC_OPERATION_INSERT, &g_childNode2[7]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

      ret = GmcYangEditChildNode(g_childNode2[7], "C_MIX1_N8", GMC_OPERATION_INSERT, &g_childNode2[8]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      fieldValue = i;
      testYangSetNodeProperty(g_childNode2[8], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
      ret = GmcBatchAddDML(batch, g_stmt_list[2]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   }
    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(connAsync2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void preparesDel()
{
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "C_MIX1_N1", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void preparesCL8(int sNum, int eNum)
{
    GmcBatchT *batch = NULL;

    // 启动事务
    int ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "C_L_8_N1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    int fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = sNum; i < eNum; i++) {
      ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "C_L_8_N2", GMC_OPERATION_INSERT);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[2]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      fieldValue = 100 + i;
      testYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
      ret = GmcBatchAddDML(batch, g_stmt_list[1]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      for (uint32_t i = sNum; i < eNum; i++) {
         ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "C_L_8_N3", GMC_OPERATION_INSERT);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         ret = GmcYangBindChild(batch, g_stmt_list[1], g_stmt_list[2]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[3]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         fieldValue = 100 + i;
         testYangSetNodeProperty(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
         ret = GmcBatchAddDML(batch, g_stmt_list[2]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         for (uint32_t i = sNum; i < eNum; i++) {
            ret = testGmcPrepareStmtByLabelName(g_stmt_list[3], "C_L_8_N4", GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_list[2], g_stmt_list[3]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_list[3], &g_childNode[4]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            fieldValue = 100 + i;
            testYangSetNodeProperty(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
            ret = GmcBatchAddDML(batch, g_stmt_list[3]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            for (uint32_t i = sNum; i < eNum; i++) {
               ret = testGmcPrepareStmtByLabelName(g_stmt_list[4], "C_L_8_N5", GMC_OPERATION_INSERT);
               AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
               ret = GmcYangBindChild(batch, g_stmt_list[3], g_stmt_list[4]);
               AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
               ret = GmcGetRootNode(g_stmt_list[4], &g_childNode[5]);
               AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
               fieldValue = 100 + i;
               testYangSetNodeProperty(g_childNode[5], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
               ret = GmcBatchAddDML(batch, g_stmt_list[4]);
               AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
               for (uint32_t i = sNum; i < eNum; i++) {
                  ret = testGmcPrepareStmtByLabelName(g_stmt_list[5], "C_L_8_N6", GMC_OPERATION_INSERT);
                  AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                  ret = GmcYangBindChild(batch, g_stmt_list[4], g_stmt_list[5]);
                  AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                  ret = GmcGetRootNode(g_stmt_list[5], &g_childNode[6]);
                  AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                  fieldValue = 100 + i;
                  testYangSetNodeProperty(g_childNode[6], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
                  ret = GmcBatchAddDML(batch, g_stmt_list[5]);
                  AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                  for (uint32_t i = sNum; i < eNum; i++) {
                     ret = testGmcPrepareStmtByLabelName(g_stmt_list[6], "C_L_8_N7", GMC_OPERATION_INSERT);
                     AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                     ret = GmcYangBindChild(batch, g_stmt_list[5], g_stmt_list[6]);
                     AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                     ret = GmcGetRootNode(g_stmt_list[6], &g_childNode[7]);
                     AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                     fieldValue = 100 + i;
                     testYangSetNodeProperty(g_childNode[7], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
                     ret = GmcBatchAddDML(batch, g_stmt_list[6]);
                     AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                     for (uint32_t i = sNum; i < eNum; i++) {
                        ret = testGmcPrepareStmtByLabelName(g_stmt_list[7], "C_L_8_N8", GMC_OPERATION_INSERT);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        ret = GmcYangBindChild(batch, g_stmt_list[6], g_stmt_list[7]);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        ret = GmcGetRootNode(g_stmt_list[7], &g_childNode[8]);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        fieldValue = 100 + i;
                        testYangSetNodeProperty(g_childNode[8], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
                           ret = GmcBatchAddDML(batch, g_stmt_list[7]);
                           AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                     }
                  }
               }
            }
         }
      }
   }
    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void preparesCCHCA8(int sNum)
{
   GmcBatchT *batch = NULL;

   // 启动事务
   int ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // 设置批处理batch参数
   ret = testBatchPrepare(g_conn_async, &batch);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // 设置根节点
   ret = testGmcPrepareStmtByLabelName(g_stmt_root, "C_CHCA_8_N1", GMC_OPERATION_INSERT);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = GmcYangSetRoot(batch, g_stmt_root);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // 设置属性值

   int fieldValue = sNum;
   testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

   ret = GmcYangEditChildNode(g_rootNode, "C_CHCA_8_N2", GMC_OPERATION_INSERT, &g_childNode[1]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   ret = GmcYangEditChildNode(g_childNode[1], "C_CHCA_8_N3", GMC_OPERATION_INSERT, &g_childNode[2]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   fieldValue = sNum;

   testYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
   ret = GmcYangEditChildNode(g_childNode[2], "C_CHCA_8_N4", GMC_OPERATION_INSERT, &g_childNode[3]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   ret = GmcYangEditChildNode(g_childNode[3], "C_CHCA_8_N5", GMC_OPERATION_INSERT, &g_childNode[4]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   fieldValue = sNum;
   testYangSetNodeProperty(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

   ret = GmcYangEditChildNode(g_childNode[4], "C_CHCA_8_N6", GMC_OPERATION_INSERT, &g_childNode[5]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   ret = GmcYangEditChildNode(g_childNode[5], "C_CHCA_8_N7", GMC_OPERATION_INSERT, &g_childNode[6]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   fieldValue = sNum;
   testYangSetNodeProperty(g_childNode[6], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

   ret = GmcYangEditChildNode(g_childNode[6], "C_CHCA_8_N8", GMC_OPERATION_INSERT, &g_childNode[7]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[7], "C_CHCA_8_N9", GMC_OPERATION_INSERT, &g_childNode[8]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = sNum;
    testYangSetNodeProperty(g_childNode[8], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   // 批处理提交
   AsyncUserDataT data = {0};
   ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testWaitAsyncRecv(&data);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
   AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
   AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
   GmcBatchDestroy(batch);
   memset(&data, 0, sizeof(AsyncUserDataT));
   // 提交事务
   ret = testTransCommitAsync(g_conn_async);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* MIX1 Yang模型示例
                          C_MIX1_N1(con) —— —— ——  2个...
                             |                    |
                          C_MIX1_N2(list1)     C_MIX1_N2(list2)...
                             |
                          C_MIX1_N3(choice)
                             |
                          C_MIX1_N4(case)
                             |
                          C_MIX1_N5(con)
                             |
                          C_MIX1_N6(list1)
                             |
                          C_MIX1_N7(choice)
                             |
                          C_MIX1_N8(case)
*/
/*****************************************************************************
 Description  : 001.混合类型表，分别写入1，60，500，1k，5K，条数据后查看视图
 Info  : 部分打散模式—— 
*****************************************************************************/
// 001.混合类型表，分别写入1，60，500，1k，5K，条数据后查看视图
TEST_F(V3_HashDfx, Yang_034_003_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    sleep(1);
    ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f ENTRY_USED=0 | grep C_MIX1_N2",
                   (char *)"LABEL_NAME: C_MIX1_N2");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f ENTRY_USED=0 | grep C_MIX1_N6",
                   (char *)"LABEL_NAME: C_MIX1_N6");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    prepares(0, 1);
    sleep(1);
    ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f ENTRY_USED=1 | grep C_MIX1_N2",
                   (char *)"LABEL_NAME: C_MIX1_N2");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f ENTRY_USED=1 | grep C_MIX1_N6",
                   (char *)"LABEL_NAME: C_MIX1_N6");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    preparesDel();

    prepares(0, 60);
    sleep(1);
    ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f ENTRY_USED=60 | grep C_MIX1_N2",
                   (char *)"LABEL_NAME: C_MIX1_N2");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f ENTRY_USED=60 | grep C_MIX1_N6",
                   (char *)"LABEL_NAME: C_MIX1_N6");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    preparesDel();

    prepares(0, 500);
    sleep(1);
    ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f ENTRY_USED=500 | grep C_MIX1_N2",
                   (char *)"LABEL_NAME: C_MIX1_N2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f ENTRY_USED=500 | grep C_MIX1_N6",
                   (char *)"LABEL_NAME: C_MIX1_N6");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    preparesDel();

    prepares(0, 5000);
    sleep(1);
    ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f ENTRY_USED=5000 | grep C_MIX1_N2",
                   (char *)"LABEL_NAME: C_MIX1_N2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f ENTRY_USED=5000 | grep C_MIX1_N6",
                   (char *)"LABEL_NAME: C_MIX1_N6");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    preparesDel();

    prepares(0, 1000);
    sleep(1);
    ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f ENTRY_USED=1000 | grep C_MIX1_N2",
                   (char *)"LABEL_NAME: C_MIX1_N2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f ENTRY_USED=1000 | grep C_MIX1_N6",
                   (char *)"LABEL_NAME: C_MIX1_N6");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    preparesDel();
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 002.一张表，一个数组，数组不为空但不写数据，查看内存和索引视图
 Info  : 部分打散模式—— 
*****************************************************************************/
// 002.一张表，一个数组，数组不为空但不写数据，查看内存和索引视图
TEST_F(V3_HashDfx, Yang_034_003_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f LABEL_NAME=C_MIX1_N2",
                         (char *)"ENTRY_USED: 0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_HEAP_VERTEX_LABEL_STAT -f VERTEXLABEL_NAME=C_MIX1_N2",
                         (char *)"HEAP_ROW_NUM: 0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f LABEL_NAME=C_MIX1_N6",
                         (char *)"ENTRY_USED: 0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_HEAP_VERTEX_LABEL_STAT -f VERTEXLABEL_NAME=C_MIX1_N6",
                         (char *)"HEAP_ROW_NUM: 0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 003.一张表，一个数组，写入元素后删除数组节点，查看内存和索引视图
 Info  : 部分打散模式—— 
*****************************************************************************/
// 003.一张表，一个数组，写入元素后删除数组节点，查看内存和索引视图
TEST_F(V3_HashDfx, Yang_034_003_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    prepares(0, 5000);
    preparesDel(); // 由于IOT环境性能不稳定查看ENTRY_USED不好做校验
    sleep(10);
    if (g_envType != 2) {
        ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f ENTRY_USED=0 | grep C_MIX1_N2",
                   (char *)"LABEL_NAME: C_MIX1_N2");
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f ENTRY_USED=0 | grep C_MIX1_N6",
                   (char *)"LABEL_NAME: C_MIX1_N6");
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_HEAP_VERTEX_LABEL_STAT -f HEAP_ROW_NUM=0 | grep C_MIX1_N2",
                   (char *)"VERTEXLABEL_NAME: C_MIX1_N2");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_HEAP_VERTEX_LABEL_STAT -f HEAP_ROW_NUM=0 | grep C_MIX1_N6",
                   (char *)"VERTEXLABEL_NAME: C_MIX1_N6");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 004.一张表，两个数组嵌套，有唯一性字段，查看索引视图
 Info  : 部分打散模式—— 
*****************************************************************************/
// 004.一张表，两个数组嵌套，有唯一性字段，查看索引视图
TEST_F(V3_HashDfx, Yang_034_003_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    preparesCL8(0, 1);
    ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f ENTRY_USED=1",
                   (char *)"LABEL_NAME: C_L_8_N7");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_HEAP_VERTEX_LABEL_STAT -f HEAP_ROW_NUM=1",
                   (char *)"VERTEXLABEL_NAME: C_L_8_N7");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 005.一张表，多个数组，choice-case下数组节点切换，查看索引视图
 Info  : V5没有切换这个概念，用例只是对改结构表查询视图
*****************************************************************************/
// 005.一张表，多个数组，choice-case下数组节点切换，查看索引视图
TEST_F(V3_HashDfx, Yang_034_003_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    preparesCCHCA8(10);
    ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f ENTRY_USED=1",
                   (char *)"LABEL_NAME: C_CHCA_8_N1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_HEAP_VERTEX_LABEL_STAT -f HEAP_ROW_NUM=1",
                   (char *)"VERTEXLABEL_NAME: C_CHCA_8_N1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 006.混合类型表，删表后查看索引和内存视图
 Info  : 即不含List的表
*****************************************************************************/
// 006.混合类型表，删表后查看索引和内存视图
TEST_F(V3_HashDfx2, Yang_034_003_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    AsyncUserDataT data = {0};

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t i = 0;
    for (i = 0; i < 10; i++) {
        ret = GmcAllocStmt(g_conn_async, &g_stmt_list[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcAllocStmt(g_conn, &g_stmt_sync[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = g_namespace;
    nspCfg.tablespaceName = NULL;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观
    AW_FUN_Log(LOG_INFO, "namespaceName: %s\n", g_namespace);

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 创建Vertex表
   testCreateLabelMix(g_stmt_async);
   testCreateLabelCL8(g_stmt_async);
   testCreateLabelCCHCA8(g_stmt_async);
   testCreateLabelC8(g_stmt_async);
    // 同步连接也使用namespace
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    prepares(0, 5000);
    preparesDel();
    testDropLabelMix(g_stmt_async);
    ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f ENTRY_USED=0",
                   (char *)"LABEL_NAME: C_CHCA_8_N1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_HEAP_VERTEX_LABEL_STAT -f HEAP_ROW_NUM=0",
                   (char *)"VERTEXLABEL_NAME: C_CHCA_8_N1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f ENTRY_USED=0",
                   (char *)"LABEL_NAME: C_MIX1_N2");
    EXPECT_NE(GMERR_OK, ret);
    ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_HEAP_VERTEX_LABEL_STAT -f HEAP_ROW_NUM=0",
                   (char *)"VERTEXLABEL_NAME: C_MIX1_N2");
    EXPECT_NE(GMERR_OK, ret);
    i = 0;
    data = {0};
    // 删除Vertex和Edge表
    ret = GmcClearNamespaceAsync(g_stmt_async, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
     // 删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, g_namespace, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    for (i = 0; i < 10; i++) {
        GmcFreeStmt(g_stmt_sync[i]);
    }
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    GmcFreeStmt(g_stmt_root);
    for (i = 0; i < 10; i++) {
        GmcFreeStmt(g_stmt_list[i]);
    }
    testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_conn_async = NULL;
    g_stmt_async = NULL;
    g_stmt_root = NULL;
    for (i = 0; i < 10; i++) {
        g_stmt_list[i] = NULL;
        g_stmt_sync[i] = NULL;
    }

    g_rootNode = NULL;
    for (i = 0; i < 10; i++) {
        g_childNode[i] = NULL;
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 007.混合类型表，删命名空间后查看索引和内存视图
 Info  : 直接删命名空间内含有数据则会报错
*****************************************************************************/
// 007.混合类型表，删命名空间后查看索引和内存视图
TEST_F(V3_HashDfx2, Yang_034_003_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    AsyncUserDataT data = {0};
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t i = 0;
    for (i = 0; i < 10; i++) {
        ret = GmcAllocStmt(g_conn_async, &g_stmt_list[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcAllocStmt(g_conn, &g_stmt_sync[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = g_namespace;
    nspCfg.tablespaceName = NULL;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观
    AW_FUN_Log(LOG_INFO, "namespaceName: %s\n", g_namespace);

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 创建Vertex表
   testCreateLabelMix(g_stmt_async);
   testCreateLabelCL8(g_stmt_async);
   testCreateLabelCCHCA8(g_stmt_async);
   testCreateLabelC8(g_stmt_async);
    // 同步连接也使用namespace
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    prepares(0, 5000);
    preparesDel();
    
    ret = GmcDropNamespaceAsync(g_stmt_async, g_namespace, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    i = 0;
    data = {0};
    // 异步删除namespace
    ret = GmcClearNamespaceAsync(g_stmt_async, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
     // 删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, g_namespace, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f ENTRY_USED=0",
                   (char *)"LABEL_NAME: C_MIX1_N2");
    EXPECT_NE(GMERR_OK, ret);
    ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_HEAP_VERTEX_LABEL_STAT -f HEAP_ROW_NUM=0",
                   (char *)"VERTEXLABEL_NAME: C_MIX1_N2");
    EXPECT_NE(GMERR_OK, ret);
    for (i = 0; i < 10; i++) {
        GmcFreeStmt(g_stmt_sync[i]);
    }
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    GmcFreeStmt(g_stmt_root);
    for (i = 0; i < 10; i++) {
        GmcFreeStmt(g_stmt_list[i]);
    }
    testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_conn_async = NULL;
    g_stmt_async = NULL;
    g_stmt_root = NULL;
    for (i = 0; i < 10; i++) {
        g_stmt_list[i] = NULL;
        g_stmt_sync[i] = NULL;
    }

    g_rootNode = NULL;
    for (i = 0; i < 10; i++) {
        g_childNode[i] = NULL;
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 008.混合类型表，建立命名空间后查看索引和内存视图
 Info  : 不建表
*****************************************************************************/
// 008.混合类型表，建立命名空间后查看索引和内存视图
TEST_F(V3_HashDfx2, Yang_034_003_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    AsyncUserDataT data = {0};

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t i = 0;
    for (i = 0; i < 10; i++) {
        ret = GmcAllocStmt(g_conn_async, &g_stmt_list[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcAllocStmt(g_conn, &g_stmt_sync[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = g_namespace;
    nspCfg.tablespaceName = NULL;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观
    AW_FUN_Log(LOG_INFO, "namespaceName: %s\n", g_namespace);

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -f ENTRY_USED=0",
                   (char *)"LABEL_NAME: C_MIX1_N2");
    EXPECT_NE(GMERR_OK, ret);
    ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_HEAP_VERTEX_LABEL_STAT -f HEAP_ROW_NUM=0",
                   (char *)"VERTEXLABEL_NAME: C_MIX1_N2");
    EXPECT_NE(GMERR_OK, ret);

    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, g_namespace, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    for (i = 0; i < 10; i++) {
        GmcFreeStmt(g_stmt_sync[i]);
    }
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    GmcFreeStmt(g_stmt_root);
    for (i = 0; i < 10; i++) {
        GmcFreeStmt(g_stmt_list[i]);
    }
    testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_conn_async = NULL;
    g_stmt_async = NULL;
    g_stmt_root = NULL;
    for (i = 0; i < 10; i++) {
        g_stmt_list[i] = NULL;
        g_stmt_sync[i] = NULL;
    }

    g_rootNode = NULL;
    for (i = 0; i < 10; i++) {
        g_childNode[i] = NULL;
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 009.循环建表、获取schema、释放schema，获取计数；
 Info  : V5没有释放schema功能，主要查看视图中的引用计数
*****************************************************************************/
// 009.循环建表、获取schema、释放schema，获取计数；
TEST_F(V3_HashDfx2, Yang_034_003_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    AsyncUserDataT data = {0};

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t i = 0;
    for (i = 0; i < 10; i++) {
        ret = GmcAllocStmt(g_conn_async, &g_stmt_list[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcAllocStmt(g_conn, &g_stmt_sync[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = g_namespace;
    nspCfg.tablespaceName = NULL;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观
    AW_FUN_Log(LOG_INFO, "namespaceName: %s\n", g_namespace);

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 适配用例校验方式，如果失败打印视图信息
    ret = executeCommand((char *)"gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=C_MIX2_N8 |\
    grep 'VERTEX_LABEL_NAME: C_MIX2_N8' |wc -l", (char *)"0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=C_MIX2_N8");
    }
    for (int i = 0; i < 1; i++) {
        testCreateLabelMix(g_stmt_async);
        sleep(4);
        ret = executeCommand((char *)"gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=C_MIX2_N8 |\
    grep 'VERTEX_LABEL_NAME: C_MIX2_N8' |wc -l", (char *)"1");
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK) {
            system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=C_MIX2_N8");
        }
    }
    i = 0;
    data = {0};
    testDropLabelMix(g_stmt_async);
    ret = GmcDropNamespaceAsync(g_stmt_async, g_namespace, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    for (i = 0; i < 10; i++) {
        GmcFreeStmt(g_stmt_sync[i]);
    }
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    GmcFreeStmt(g_stmt_root);
    for (i = 0; i < 10; i++) {
        GmcFreeStmt(g_stmt_list[i]);
    }
    testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_conn_async = NULL;
    g_stmt_async = NULL;
    g_stmt_root = NULL;
    for (i = 0; i < 10; i++) {
        g_stmt_list[i] = NULL;
        g_stmt_sync[i] = NULL;
    }

    g_rootNode = NULL;
    for (i = 0; i < 10; i++) {
        g_childNode[i] = NULL;
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 010.循环建表、获取schema、删表，获取计数；
 Info  : 删表没有视图
*****************************************************************************/
// 010.循环建表、获取schema、删表，获取计数；
TEST_F(V3_HashDfx2, Yang_034_003_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    AsyncUserDataT data = {0};

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t i = 0;
    for (i = 0; i < 10; i++) {
        ret = GmcAllocStmt(g_conn_async, &g_stmt_list[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcAllocStmt(g_conn, &g_stmt_sync[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = g_namespace;
    nspCfg.tablespaceName = NULL;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观
    AW_FUN_Log(LOG_INFO, "namespaceName: %s\n", g_namespace);

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    for (int i = 0; i < 1; i++) {
        testCreateLabelMix(g_stmt_async);
        ret = executeCommand((char *)"gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=C_MIX2_N8",
                            (char *)"REF_COUNT: 1");
        EXPECT_EQ(GMERR_OK, ret);
        testDropLabelMix(g_stmt_async);
        EXPECT_EQ(GMERR_OK, ret);
        // •由于删表操作是服务端通过3s定时器进行删除操作，因此查询视图时，表可能未被删除，只是被标记删除。
        sleep(3);
        ret = executeCommand((char *)"gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=C_MIX2_N8",
                            (char *)"REF_COUNT: 0");
        EXPECT_NE(GMERR_OK, ret);
    }
    i = 0;
    data = {0};
    ret = GmcDropNamespaceAsync(g_stmt_async, g_namespace, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    for (i = 0; i < 10; i++) {
        GmcFreeStmt(g_stmt_sync[i]);
    }
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    GmcFreeStmt(g_stmt_root);
    for (i = 0; i < 10; i++) {
        GmcFreeStmt(g_stmt_list[i]);
    }
    testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_conn_async = NULL;
    g_stmt_async = NULL;
    g_stmt_root = NULL;
    for (i = 0; i < 10; i++) {
        g_stmt_list[i] = NULL;
        g_stmt_sync[i] = NULL;
    }

    g_rootNode = NULL;
    for (i = 0; i < 10; i++) {
        g_childNode[i] = NULL;
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 011.循环建表、获取schema、断连，获取计数；
 Info  : 断连没有视图，改为不断调用gmsysview视图，查看视图工具本身引用次数
*****************************************************************************/
// 011.循环建表、获取schema、断连，获取计数；
TEST_F(V3_HashDfx2, Yang_034_003_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    AsyncUserDataT data = {0};

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t i = 0;
    for (i = 0; i < 10; i++) {
        ret = GmcAllocStmt(g_conn_async, &g_stmt_list[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcAllocStmt(g_conn, &g_stmt_sync[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = g_namespace;
    nspCfg.tablespaceName = NULL;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观
    AW_FUN_Log(LOG_INFO, "namespaceName: %s\n", g_namespace);

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    testCreateLabelMix(g_stmt_async);
    // 查询STORAGE_MEMDATA_STAT视图本身并没有增加引用
    for (int i = 0; i < 10; i++) {
        ret = executeCommand((char *)"gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=C_MIX2_N8",
                            (char *)"REF_COUNT: 1");
        EXPECT_EQ(GMERR_OK, ret);
        system("gmsysview -q V\\$STORAGE_HEAP_VERTEX_LABEL_STAT -f VERTEXLABEL_NAME=C_MIX1_N2 >2.txt");
        sleep(2);
    }
    i = 0;
    data = {0};
    testDropLabelMix(g_stmt_async);
    ret = GmcDropNamespaceAsync(g_stmt_async, g_namespace, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    for (i = 0; i < 10; i++) {
        GmcFreeStmt(g_stmt_sync[i]);
    }
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    GmcFreeStmt(g_stmt_root);
    for (i = 0; i < 10; i++) {
        GmcFreeStmt(g_stmt_list[i]);
    }
    testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_conn_async = NULL;
    g_stmt_async = NULL;
    g_stmt_root = NULL;
    for (i = 0; i < 10; i++) {
        g_stmt_list[i] = NULL;
        g_stmt_sync[i] = NULL;
    }

    g_rootNode = NULL;
    for (i = 0; i < 10; i++) {
        g_childNode[i] = NULL;
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 012.excute后观察视图引用次数是否增加
 Info  : 引用和excute操作次数没有必然关联
 2023.3.6 REF_COUNT变化太频繁, 改为[1,12]即可
*****************************************************************************/
// 012.excute后观察视图引用次数是否增加
TEST_F(V3_HashDfx, Yang_034_003_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    prepares(0, 5000);
    preparesDel();
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=C_MIX1_N2 >temp.txt");
    system("cat temp.txt |grep REF_COUNT |grep -v 'REF_COUNT: 0'|awk -F '[:]' '{print $2}' >temp3.txt");// soho环境会有不同表空间残留，查询结果需要过滤
    FILE *pf = fopen("temp3.txt", "r");
    if (pf == NULL) {
        AW_FUN_Log(LOG_STEP, "fopen error.\n");
    }
    int ch;
    char nums[128] = {0};
    int i = 0;
    while ( (ch = fgetc(pf)) != EOF) {
        if (ch >= 48 && ch <= 57 ) {
            nums[i++] = ch;
        }
    }
    int refcount = atoi(nums);
    EXPECT_GE(refcount, 1);
    EXPECT_LE(refcount, 12);
    AW_MACRO_EXPECT_NE_INT(refcount, 0);
    fclose(pf);
    system("rm -rf ./temp3.txt");
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 013.10个连接都excute表后观察视图引用次数是否增加
 Info  : REF_COUNT增加至20
*****************************************************************************/
// 013.10个连接都excute表后观察视图引用次数是否增加
TEST_F(V3_HashDfx2, Yang_034_003_013)
{
#ifdef DIRECT_WRITE
#else
    GmcConnT *g_conn[10] = {0};
    GmcStmtT *g_stmt[10] = {0};
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    for (int i = 0; i < 10; i++) {
        ret = testGmcConnect(&g_conn[i], &g_stmt[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char const *g_schema = "[{\"type\":\"record\", \"name\":\"Normal\", \"fields\":[{\"name\":\"F0\", \"type\":\"uint32\"},"
                       "{\"name\":\"F1\", \"type\":\"uint32\"},{\"name\":\"F2\", "
                       "\"type\":\"uint32\"},{\"name\":\"F3\", \"type\":\"uint32\"}],"
                       "\"keys\":[{\"node\":\"Normal\", \"name\":\"Normal_K0\", \"fields\":[\"F0\"], "
                       "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]";
    const char *V_config = "{\"max_record_num\":100000000,\"defragmentation\":true}";
    ret = GmcCreateVertexLabel(g_stmt[0], g_schema, V_config);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt[i], "Normal", GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = i * 100; j < i * 100 + 100; j++) {
            ret = GmcSetVertexProperty(g_stmt[i], "F0", GMC_DATATYPE_UINT32, &j, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(g_stmt[i], "F1", GMC_DATATYPE_UINT32, &j, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(g_stmt[i]);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=Normal >temp.txt");
    system("cat temp.txt |grep REF_COUNT |awk -F '[:]' '{print $2}' >temp3.txt");
    FILE *pf = fopen("temp3.txt", "r");
    if (pf == NULL) {
        AW_FUN_Log(LOG_STEP, "fopen error.\n");
    }
    int ch;
    char nums[128] = {0};
    int i = 0;
    while ( (ch = fgetc(pf)) != EOF) {
        if (ch >= 48 && ch <= 57 ) {
            nums[i++] = ch;
        }
    }
    int refcount = atoi(nums);
    EXPECT_GE(refcount, 1);
    EXPECT_LE(refcount, 35);
    AW_MACRO_EXPECT_NE_INT(refcount, 0);
    fclose(pf);
    system("rm -rf ./temp3.txt");

    ret = GmcDropVertexLabel(g_stmt[0], "Normal");
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        ret = testGmcDisconnect(g_conn[i], g_stmt[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
#endif
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 014.5个连接插入数据，5个连接删除数据excute表后观察视图引用次数是否增加
 Info  : REF_COUNT增加至20
*****************************************************************************/
// 014.5个连接插入数据，5个连接删除数据excute表后观察视图引用次数是否增加
TEST_F(V3_HashDfx2, Yang_034_003_014)
{
#ifdef DIRECT_WRITE
#else
    GmcConnT *g_conn[10] = {0};
    GmcStmtT *g_stmt[10] = {0};
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    for (int i = 0; i < 10; i++) {
        ret = testGmcConnect(&g_conn[i], &g_stmt[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char const *g_schema = "[{\"type\":\"record\", \"name\":\"Normal\", \"fields\":[{\"name\":\"F0\", \"type\":\"uint32\"},"
                       "{\"name\":\"F1\", \"type\":\"uint32\"},{\"name\":\"F2\", "
                       "\"type\":\"uint32\"},{\"name\":\"F3\", \"type\":\"uint32\"}],"
                       "\"keys\":[{\"node\":\"Normal\", \"name\":\"Normal_K0\", \"fields\":[\"F0\"], "
                       "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]";
    const char *V_config = "{\"max_record_num\":100000000,\"defragmentation\":true}";
    ret = GmcCreateVertexLabel(g_stmt[0], g_schema, V_config);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < 5; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt[i], "Normal", GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = i * 100; j < i * 100 + 100; j++) {
            ret = GmcSetVertexProperty(g_stmt[i], "F0", GMC_DATATYPE_UINT32, &j, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(g_stmt[i], "F1", GMC_DATATYPE_UINT32, &j, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(g_stmt[i]);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=Normal >temp0.txt");
    system("cat temp0.txt |grep REF_COUNT |awk -F '[:]' '{print $2}' >temp2.txt");
    FILE *pf0 = fopen("temp2.txt", "r");
    if (pf0 == NULL) {
        AW_FUN_Log(LOG_STEP, "fopen error.\n");
    }
    int ch0;
    char nums0[128] = {0};
    int i0 = 0;
    while ( (ch0 = fgetc(pf0)) != EOF) {
        if (ch0 >= 48 && ch0 <= 57 ) {
            nums0[i0++] = ch0;
        }
    }
    int refcount0 = atoi(nums0);
    EXPECT_GE(refcount0, 1);
    EXPECT_LE(refcount0, 35);
    AW_MACRO_EXPECT_NE_INT(refcount0, 0);
    fclose(pf0);
    system("rm -rf ./temp0.txt");
    system("rm -rf ./temp2.txt");
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 5; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt[i], "Normal", GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = (i - 5) * 100; j < (i - 5) * 100 + 100; j++) {
            ret = GmcSetIndexKeyName(g_stmt[i], "Normal_K0");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(g_stmt[i], 0, GMC_DATATYPE_UINT32, &j, sizeof(j));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(g_stmt[i]);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=Normal >temp.txt");
    system("cat temp.txt |grep REF_COUNT |awk -F '[:]' '{print $2}' >temp3.txt");
    FILE *pf = fopen("temp3.txt", "r");
    if (pf == NULL) {
        AW_FUN_Log(LOG_STEP, "fopen error.\n");
    }
    int ch;
    char nums[128] = {0};
    int i = 0;
    while ( (ch = fgetc(pf)) != EOF) {
        if (ch >= 48 && ch <= 57 ) {
            nums[i++] = ch;
        }
    }
    int refcount = atoi(nums);
    EXPECT_GE(refcount, 1);
    EXPECT_LE(refcount, 35);
    AW_MACRO_EXPECT_NE_INT(refcount, 0);
    fclose(pf);
    system("rm -rf ./temp.txt");
    system("rm -rf ./temp3.txt");
    ret = GmcDropVertexLabel(g_stmt[0], "Normal");
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        ret = testGmcDisconnect(g_conn[i], g_stmt[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
#endif
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 015.建表插入数据查看引用计数，客户端退出后再次查看计数
 Info  : 计数和客户端退出无关
*****************************************************************************/
// 015.建表插入数据查看引用计数，客户端退出后再次查看计数
TEST_F(V3_HashDfx2, Yang_034_003_015)
{
    GmcConnT *g_conn[10] = {0};
    GmcStmtT *g_stmt[10] = {0};
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    for (int i = 0; i < 10; i++) {
        ret = testGmcConnect(&g_conn[i], &g_stmt[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char const *g_schema = "[{\"type\":\"record\", \"name\":\"Normal\", \"fields\":[{\"name\":\"F0\", \"type\":\"uint32\"},"
                       "{\"name\":\"F1\", \"type\":\"uint32\"},{\"name\":\"F2\", "
                       "\"type\":\"uint32\"},{\"name\":\"F3\", \"type\":\"uint32\"}],"
                       "\"keys\":[{\"node\":\"Normal\", \"name\":\"Normal_K0\", \"fields\":[\"F0\"], "
                       "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]";
    const char *V_config = "{\"max_record_num\":100000000,\"defragmentation\":true}";
    ret = GmcCreateVertexLabel(g_stmt[0], g_schema, V_config);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < 5; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt[i], "Normal", GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = i * 100; j < i * 100 + 100; j++) {
            ret = GmcSetVertexProperty(g_stmt[i], "F0", GMC_DATATYPE_UINT32, &j, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(g_stmt[i], "F1", GMC_DATATYPE_UINT32, &j, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(g_stmt[i]);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    for (int i = 5; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt[i], "Normal", GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = (i - 5) * 100; j < (i - 5) * 100 + 100; j++) {
            ret = GmcSetIndexKeyName(g_stmt[i], "Normal_K0");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(g_stmt[i], 0, GMC_DATATYPE_UINT32, &j, sizeof(j));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(g_stmt[i]);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    int status;
    pid_t pid, pid2;
    char command[128];
    ret = GtExecSystemCmd("./V3_HashDFX_034_process_01 &");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    usleep(1000);
    ret = executeCommand((char *)"ps -ef |grep V3_HashDFX_034_process_01 | grep -v grep | wc -l", (char *)"1");
    if (ret == 0) {
        // 适配用例，1225sdv构建，环境性能差的时候，进程未拉起来，判断进程存在才kill
        ret = GtExecSystemCmd("kill -9 `pidof V3_HashDFX_034_process_01`");
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    int memCompactEnable = 0;
    ret = TestGetConfigValueInt("memCompactEnable", &memCompactEnable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "memCompactEnable %d", memCompactEnable);
#ifdef DIRECT_WRITE
    AW_FUN_Log(LOG_STEP, "DIRECT_WRITE");
    if (memCompactEnable == 0) {
        AW_FUN_Log(LOG_STEP, "memCompactEnable = 0");
        ret = executeCommand((char *)"gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=Normal",
                            (char *)"REF_COUNT: 0");
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        AW_FUN_Log(LOG_STEP, "DIRECT_WRITE memCompactEnable %d", memCompactEnable);
        ret = executeCommand((char *)"gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=Normal",
                            (char *)"REF_COUNT: 2");
        EXPECT_EQ(GMERR_OK, ret);
    }
#else
    if (memCompactEnable == 0) {
        ret = executeCommand((char *)"gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=Normal",
                            (char *)"REF_COUNT: 20");
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        // sdv0407构建适配用例欧拉仿真开启enableReleaseDevice、memCompactEnable配置项
        ret = executeCommand((char *)"gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=Normal",
                            (char *)"REF_COUNT: 2");
        EXPECT_EQ(GMERR_OK, ret);
    }
    
#endif
    ret = GmcDropVertexLabel(g_stmt[0], "Normal");
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        ret = testGmcDisconnect(g_conn[i], g_stmt[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
    
}

/*****************************************************************************
 Description  : 016.建满链接，每个连接都执行Execute操作，查看计数
 Info  : 满链接看不到视图
*****************************************************************************/
// 016.建满链接，每个连接都执行Execute操作，查看计数
TEST_F(V3_HashDfx2, Yang_034_003_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn_t[MAX_CONN_SIZE ] = {0};
    GmcStmtT *stmt_t[MAX_CONN_SIZE ] = {0};
    int ret, i;
    int value = 0;
#if defined(ENV_RTOSV2X)
    uint32_t existConnNum = 0;
    ret = testGetConnNum(&existConnNum);
    EXPECT_EQ(0, ret);
    value = existConnNum;
#elif defined(ENV_RTOSV2)  // AC环境预留的2个逃生通道普通用户无法使用，报18004
    value = 2;
#elif defined(ENV_SUSE)
    value = 1;
#endif
    int cnt = 0;
    for (i = 0; i < MAX_CONN_SIZE - value; i++) {
        ret = testGmcConnect(&conn_t[i], &stmt_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
        cnt ++;
    }
    AW_FUN_Log(LOG_INFO, "thr connect cnt is :%d\n", cnt);

    char const *g_schema = "[{\"type\":\"record\", \"name\":\"Normal\", \"fields\":[{\"name\":\"F0\", \"type\":\"uint32\"},"
                       "{\"name\":\"F1\", \"type\":\"uint32\"},{\"name\":\"F2\", "
                       "\"type\":\"uint32\"},{\"name\":\"F3\", \"type\":\"uint32\"}],"
                       "\"keys\":[{\"node\":\"Normal\", \"name\":\"Normal_K0\", \"fields\":[\"F0\"], "
                       "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]";
    const char *V_config = "{\"max_record_num\":100000000,\"defragmentation\":true}";
    ret = GmcCreateVertexLabel(stmt_t[0], g_schema, V_config);
    EXPECT_EQ(GMERR_OK, ret);
    if (g_envType != 2) {
        for (int i = 0; i < 512; i++) {
            ret = testGmcPrepareStmtByLabelName(stmt_t[i], "Normal", GMC_OPERATION_INSERT);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t j = i * 10; j < i * 10 + 10; j++) {
                ret = GmcSetVertexProperty(stmt_t[i], "F0", GMC_DATATYPE_UINT32, &j, sizeof(uint32_t));
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcSetVertexProperty(stmt_t[i], "F1", GMC_DATATYPE_UINT32, &j, sizeof(uint32_t));
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcExecute(stmt_t[i]);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        for (int i = 512; i < MAX_CONN_SIZE - value; i++) {
            ret = testGmcPrepareStmtByLabelName(stmt_t[i], "Normal", GMC_OPERATION_DELETE);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t j = (i - 512) * 10; j < (i - 512) * 10 + 10; j++) {
                ret = GmcSetIndexKeyName(stmt_t[i], "Normal_K0");
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt_t[i], 0, GMC_DATATYPE_UINT32, &j, sizeof(j));
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcExecute(stmt_t[i]);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
#if defined(ENV_RTOSV2X)
        ret = executeCommand((char *)"gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=Normal",
                              (char *)"10002");
#elif defined(ENV_RTOSV2)
        ret = executeCommand((char *)"gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=Normal",
                              (char *)"Normal");
#endif
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(stmt_t[0], "Normal");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (i = 0; i < MAX_CONN_SIZE - value; i++) {
        ret = testGmcDisconnect(conn_t[i], stmt_t[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 017.创建3张表，创建5个链接,
 链接1删数据集1的表，链接2断开，链接3删数据集3
 查看各链接计数；
 Info  : 
*****************************************************************************/
// 017.创建3张表，创建5个链接,链接1删数据集1的表，链接2断开，链接3删数据集3
TEST_F(V3_HashDfx2, Yang_034_003_017)
{
    GmcConnT *conn_t[MAX_CONN_SIZE ] = {0};
    GmcStmtT *stmt_t[MAX_CONN_SIZE ] = {0};
    int ret, i;
    int value = 0;
    char Normal_schemas[1024] = {0};
    const char *V_config = "{\"max_record_num\":100000000,\"defragmentation\":true}";
    int cnt = 0;
    for (i = 0; i < 3; i++) {
        ret = testGmcConnect(&conn_t[i], &stmt_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
        cnt ++;
    }
    ret = GmcCreateNamespace(stmt_t[0], "case17_1", "gmdbv5");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateNamespace(stmt_t[0], "case17_2", "gmdbv5");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateNamespace(stmt_t[0], "case17_3", "gmdbv5");
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "thr connect cnt is :%d\n", cnt);
    char nameSpaceName[3][48] = {"case17_1", "case17_2", "case17_3"};
    char tableName[3][48] = {"Normal0", "Normal1", "Normal2"};
    for (int k = 0; k < 3; k++) {
        ret = GmcUseNamespace(stmt_t[0], nameSpaceName[k]);
        EXPECT_EQ(GMERR_OK, ret);
        for (int i = 0; i < 3; i++) {
            snprintf(Normal_schemas, 1023,
                "[{\"type\":\"record\", \"name\":\"Normal%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"uint32\"},"
                "{\"name\":\"F1\", \"type\":\"uint32\"},{\"name\":\"F2\", \"type\":\"uint32\"},{\"name\":\"F3\", "
                "\"type\":\"uint32\"}],"
                "\"keys\":[{\"node\":\"Normal%d\", \"name\":\"Normal_K0\", \"fields\":[\"F0\"], "
                "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
                i, i);
            ret = GmcCreateVertexLabel(stmt_t[0], Normal_schemas, V_config);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    ret = GmcUseNamespace(stmt_t[0], nameSpaceName[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 3; i++) {
        ret = GmcDropVertexLabel(stmt_t[0], tableName[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = executeCommand((char *)"gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=Normal1",
                        (char *)"REF_COUNT: 0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt_t[1], nameSpaceName[1]);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_t[1], stmt_t[1]);
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand((char *)"gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=Normal1",
                        (char *)"REF_COUNT: 0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt_t[2], nameSpaceName[2]);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 3; i++) {
        ret = GmcDropVertexLabel(stmt_t[2], tableName[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcDropNamespace(stmt_t[2], nameSpaceName[2]);
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand((char *)"gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=Normal1",
                        (char *)"REF_COUNT: 0");
    EXPECT_EQ(GMERR_OK, ret);

    // 清除资源
    ret = GmcUseNamespace(stmt_t[2], nameSpaceName[1]);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < 3; i++) {
        ret = GmcDropVertexLabel(stmt_t[2], tableName[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcDropNamespace(stmt_t[2], nameSpaceName[1]);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropNamespace(stmt_t[2], nameSpaceName[0]);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(conn_t[0], stmt_t[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_t[2], stmt_t[2]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 018.cli_info 有客户端时，查询不存在的客户端进程
 Info  : 视图不能指定查询特定客户端PID，会全体显示出来，校验当前PID是否正确即可[只在HPE下有效]
*****************************************************************************/
// 018.cli_info 有客户端时，查询不存在的客户端进程
TEST_F(V3_HashDfx, Yang_034_003_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    sleep(5);
    int ret = 0;
    char pidCommand[64] = {0};
    (void)snprintf(pidCommand, 64, "PROCESS_PID: %d", getpid());
    int pidss = getpid();
    if (g_envType == 2) {
        sleep(30);
        ret = executeCommand((char *)"gmsysview -q V\\$CLT_PROCESS_CONN",
                             pidCommand);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 019.cli_info 反复建链断链，查询客户端信息
 Info  : 
 1.建链，cli_info查询信息
 2.断链，cli_info查询信息
 3.反复进行
*****************************************************************************/
// 019.cli_info 反复建链断链，查询客户端信息
TEST_F(V3_HashDfx, Yang_034_003_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    sleep(5);
    GmcConnT *conn_t[MAX_CONN_SIZE ] = {0};
    GmcStmtT *stmt_t[MAX_CONN_SIZE ] = {0};
    int ret, i;
    char pidCommand[64] = {0};
    (void)snprintf(pidCommand, 64, "PROCESS_PID: %d", getpid());
    for (i = 0; i < 3; i++) {
        ret = testGmcConnect(&conn_t[0], &stmt_t[0]);
        EXPECT_EQ(GMERR_OK, ret);
        if (g_envType == 2) {
            sleep(30);
            ret = executeCommand((char *)"gmsysview -q V\\$CLT_PROCESS_CONN",
                                pidCommand);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = testGmcDisconnect(conn_t[0], stmt_t[0]);
        EXPECT_EQ(GMERR_OK, ret);
        if (g_envType == 2) {
            sleep(30);
            ret = executeCommand((char *)"gmsysview -q V\\$CLT_PROCESS_CONN",
                                pidCommand);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 020.CLT_PROCESS_CONN视图测试proc_msg，单个客户端建1023个连接
 Info  : proc_msg等同于V$DRT_CONN_STAT——————————TOTAL_MSG_COUNT
*****************************************************************************/
// 020.CLT_PROCESS_CONN视图测试proc_msg，单个客户端建1023个连接
TEST_F(V3_HashDfx2, Yang_034_003_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn_t[MAX_CONN_SIZE ] = {0};
    GmcStmtT *stmt_t[MAX_CONN_SIZE ] = {0};
    int ret, i;
    int value = 0;
#if defined(ENV_RTOSV2X)
    uint32_t existConnNum = 0;
    ret = testGetConnNum(&existConnNum);
    EXPECT_EQ(0, ret);
    value = existConnNum;
#elif defined(ENV_RTOSV2)
    value = 2;
#elif defined(ENV_SUSE)
    value = 1;
#endif
    int cnt = 0;
    for (i = 0; i < MAX_CONN_SIZE - value -1; i++) {
        ret = testGmcConnect(&conn_t[i], &stmt_t[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        cnt ++;
    }
    ret = executeCommand((char *)"gmsysview -q V\\$DRT_CONN_STAT",
                         (char *)"TOTAL_MSG_COUNT: 1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (i = 0; i < MAX_CONN_SIZE - value -1; i++) {
        ret = testGmcDisconnect(conn_t[i], stmt_t[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 021.CLT_PROCESS_CONN视图测试proc_msg，2个客户端，每个客户端10个连接
 Info  : proc_msg等同于V$DRT_CONN_STAT——————————TOTAL_MSG_COUNT
*****************************************************************************/
// 021.CLT_PROCESS_CONN视图测试proc_msg，2个客户端，每个客户端10个连接
TEST_F(V3_HashDfx2, Yang_034_003_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn_t[MAX_CONN_SIZE ] = {0};
    GmcStmtT *stmt_t[MAX_CONN_SIZE ] = {0};
    int ret, i;
    int value = 0;
    char pidCommand[64] = {0};
    (void)snprintf(pidCommand, 64, "CLT_PROC_ID: %d", getpid());
#if defined(ENV_RTOSV2X)
    uint32_t existConnNum = 0;
    ret = testGetConnNum(&existConnNum);
    EXPECT_EQ(0, ret);
    value = existConnNum;
#elif defined(ENV_RTOSV2)
    value = 2;
#endif
    ret = GtExecSystemCmd("./V3_HashDFX_034_process_02 >V3_HashDFX_034_process_02.log &");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    usleep(100);
    ret = GtExecSystemCmd("./V3_HashDFX_034_process_03 >V3_HashDFX_034_process_03.log &");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 022.CLT_PROCESS_CONN视图,客户端没建连视图查询的客户端进程是连接进程
 Info  : proc_msg等同于V$DRT_CONN_STAT——————————TOTAL_MSG_COUNT
*****************************************************************************/
// 022.CLT_PROCESS_CONN视图,客户端建连失败
TEST_F(V3_HashDfx2, Yang_034_003_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn_t[MAX_CONN_SIZE ] = {0};
    GmcStmtT *stmt_t[MAX_CONN_SIZE ] = {0};
    int ret, i;
    int value = 0;
    char pidCommand[64] = {0};
    (void)snprintf(pidCommand, 64, "CLT_PROC_ID: %d", getpid());
#if defined(ENV_RTOSV2X)
    uint32_t existConnNum = 0;
    ret = testGetConnNum(&existConnNum);
    EXPECT_EQ(0, ret);
    value = existConnNum;
#elif defined(ENV_RTOSV2)
    value = 2;
#endif
    ret = GtExecSystemCmd("./V3_HashDFX_034_process_01 &");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (i = 0; i < 10; i++) {
    ret = testGmcConnect(&conn_t[i], &stmt_t[i]);
    EXPECT_EQ(GMERR_OK, ret);
    }
    ret = executeCommand((char *)"gmsysview -q V\\$DRT_CONN_STAT", pidCommand, (char *)"TOTAL_MSG_COUNT: 1");
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < 10; i++) {
        ret = testGmcDisconnect(conn_t[i], stmt_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 023.不写入数据查询,查看所有表空间和单个表空间内容，不用筛选表-f形式
 Info  : 
*****************************************************************************/
// 023.不写入数据查询,查看所有表空间和单个表空间内容，不用筛选表-f形式
TEST_F(V3_HashDfx2, Yang_034_003_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("gmadmin -cfgName enableClusterHash -cfgVal 1"); 
    GmcConnT *conn_t[MAX_CONN_SIZE ] = {0};
    GmcStmtT *stmt_t[MAX_CONN_SIZE ] = {0};
    int ret, i;
    for (i = 0; i < 3; i++) {
        ret = testGmcConnect(&conn_t[i], &stmt_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    char tableName[3][48] = {"Normal0", "Normal1", "Normal2"};
    char Normal_schemas[1024] = {0};
    const char *V_config = "{\"max_record_num\":100000000,\"defragmentation\":true}";
    for (int i = 0; i < 3; i++) {
            snprintf(Normal_schemas, 1023,
            "[{\"type\":\"record\", \"name\":\"Normal%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"uint32\"},"
            "{\"name\":\"F1\", \"type\":\"uint32\"},{\"name\":\"F2\", \"type\":\"uint32\"},{\"name\":\"F3\", "
            "\"type\":\"uint32\"}],"
            "\"keys\":[{\"node\":\"Normal%d\", \"name\":\"Normal_K0\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
            i, i);
        GmcDropVertexLabel(stmt_t[i], tableName[i]);// 建表前删表
        ret = GmcCreateVertexLabel(stmt_t[0], Normal_schemas, V_config);
        EXPECT_EQ(GMERR_OK, ret);
    }
    int clusterValue = 0;
    ret = TestGetConfigValueInt((const char*)"enableClusterHash", &clusterValue);
    EXPECT_EQ(GMERR_OK, ret);
    if (clusterValue == 0) {
        ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_HEAP_VERTEX_LABEL_STAT",
                             (char *)"VERTEXLABEL_NAME: Normal0", (char *)"TOTAL_PAGE_SIZE: 0");
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < 3; i++) {
        ret = GmcDropVertexLabel(stmt_t[i], tableName[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (i = 0; i < 3; i++) {
        ret = testGmcDisconnect(conn_t[i], stmt_t[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    system("gmadmin -cfgName enableClusterHash -cfgVal 0"); 
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 024.不写入数据查询,查看所有表空间和单个表空间内容，用筛选表-f形式
 Info  : 
*****************************************************************************/
// 024.不写入数据查询,查看所有表空间和单个表空间内容，用筛选表-f形式
TEST_F(V3_HashDfx2, Yang_034_003_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("gmadmin -cfgName enableClusterHash -cfgVal 1"); 
    GmcConnT *conn_t[MAX_CONN_SIZE ] = {0};
    GmcStmtT *stmt_t[MAX_CONN_SIZE ] = {0};
    int ret, i;
    for (i = 0; i < 3; i++) {
        ret = testGmcConnect(&conn_t[i], &stmt_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    char tableName[3][48] = {"Normal0", "Normal1", "Normal2"};
    char Normal_schemas[1024] = {0};
    const char *V_config = "{\"max_record_num\":100000000,\"defragmentation\":true}";
    for (int i = 0; i < 3; i++) {
            snprintf(Normal_schemas, 1023,
            "[{\"type\":\"record\", \"name\":\"Normal%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"uint32\"},"
            "{\"name\":\"F1\", \"type\":\"uint32\"},{\"name\":\"F2\", \"type\":\"uint32\"},{\"name\":\"F3\", "
            "\"type\":\"uint32\"}],"
            "\"keys\":[{\"node\":\"Normal%d\", \"name\":\"Normal_K0\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
            i, i);
        GmcDropVertexLabel(stmt_t[i], tableName[i]);// 建表前删表
        ret = GmcCreateVertexLabel(stmt_t[0], Normal_schemas, V_config);
        EXPECT_EQ(GMERR_OK, ret);
    }
    int clusterValue = 0;
    ret = TestGetConfigValueInt((const char*)"enableClusterHash", &clusterValue);
    EXPECT_EQ(GMERR_OK, ret);
    if (clusterValue == 0) {
        ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_HEAP_VERTEX_LABEL_STAT -f VERTEXLABEL_NAME=Normal0",
                             (char *)"TOTAL_PAGE_SIZE: 0");// 进入聚簇容器中不是HEAP视图查询范围
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < 3; i++) {
        ret = GmcDropVertexLabel(stmt_t[i], tableName[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (i = 0; i < 3; i++) {
        ret = testGmcDisconnect(conn_t[i], stmt_t[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    system("gmadmin -cfgName enableClusterHash -cfgVal 0"); 
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 025. 一个连接，一个表，写入PK=1数据，更新写入20W数据后清空表数据
 Info  : 过程中查看所有表空间和单个表空间内容
*****************************************************************************/
// 025. 一个连接，一个表，写入PK=1数据，更新写入20W数据后清空表数据
TEST_F(V3_HashDfx2, Yang_034_003_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("gmadmin -cfgName enableClusterHash -cfgVal 1");
    int clusterValue = 0;
    int ret = 0;
    ret = TestGetConfigValueInt((const char*)"enableClusterHash", &clusterValue);
    EXPECT_EQ(GMERR_OK, ret);
    if (g_envType != 2) { // IOT无法插入该数据量
        GmcConnT *conn_t[MAX_CONN_SIZE ] = {0};
        GmcStmtT *stmt_t[MAX_CONN_SIZE ] = {0};
        int i;
        for (i = 0; i < 1; i++) {
            ret = testGmcConnect(&conn_t[i], &stmt_t[i]);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
        char tableName[3][48] = {"Normal0", "Normal1", "Normal2"};
        char Normal_schemas[1024] = {0};
        const char *V_config = "{\"max_record_num\":100000000,\"defragmentation\":true}";
        for (int i = 0; i < 1; i++) {
                snprintf(Normal_schemas, 1023,
                "[{\"type\":\"record\", \"name\":\"Normal%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"uint32\"},"
                "{\"name\":\"F1\", \"type\":\"uint32\"},{\"name\":\"F2\", \"type\":\"uint32\"},{\"name\":\"F3\", "
                "\"type\":\"uint32\"}],"
                "\"keys\":[{\"node\":\"Normal%d\", \"name\":\"Normal_K0\", \"fields\":[\"F0\"], "
                "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
                i, i);
            GmcDropVertexLabel(stmt_t[i], tableName[i]);// 建表前清理环境
            ret = GmcCreateVertexLabel(stmt_t[0], Normal_schemas, V_config);
            EXPECT_EQ(GMERR_OK, ret);
        }
        for (int i = 0; i < 1; i++) {
            ret = testGmcPrepareStmtByLabelName(stmt_t[0], "Normal0", GMC_OPERATION_INSERT);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(stmt_t[0], "F0", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(stmt_t[0], "F1", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt_t[0]);
            EXPECT_EQ(GMERR_OK, ret);
        }
        if (clusterValue == 0) {
            ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_HEAP_VERTEX_LABEL_STAT -f VERTEXLABEL_NAME=Normal0", (char *)"HEAP_ROW_NUM: 1");
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        for (int i = 0; i < 200000; i++) {
            ret = testGmcPrepareStmtByLabelName(stmt_t[0], "Normal0", GMC_OPERATION_REPLACE);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(stmt_t[0], "F0", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(stmt_t[0], "F1", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt_t[0]);
            EXPECT_EQ(GMERR_OK, ret);
        }
        if (clusterValue == 0) {
            ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_HEAP_VERTEX_LABEL_STAT -f VERTEXLABEL_NAME=Normal0", (char *)"HEAP_ROW_NUM: 200000");
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcTruncateVertexLabel(stmt_t[0], "Normal0");
        EXPECT_EQ(GMERR_OK, ret);
        if (clusterValue == 0) {
            ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_HEAP_VERTEX_LABEL_STAT -f VERTEXLABEL_NAME=Normal0",
                                (char *)"TOTAL_PAGE_SIZE: 0");
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        for (int i = 0; i < 1; i++) {
            ret = GmcDropVertexLabel(stmt_t[i], tableName[i]);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
        for (i = 0; i < 1; i++) {
            ret = testGmcDisconnect(conn_t[i], stmt_t[i]);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }
    system("gmadmin -cfgName enableClusterHash -cfgVal 0"); 
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 026. 一个连接，多个表，每个都写入20w数据后清空表数据
 Info  : 过程中查看存储空间 STORAGE_MEMDATA_STAT
*****************************************************************************/
// 026. 一个连接，多个表，每个都写入20w数据后清空表数据
TEST_F(V3_HashDfx3, Yang_034_003_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("gmadmin -cfgName enableClusterHash -cfgVal 1"); 
    if (g_envType != 2) { // IOT无法插入该数据量
        GmcConnT *conn_t[MAX_CONN_SIZE ] = {0};
        GmcStmtT *stmt_t[MAX_CONN_SIZE ] = {0};
        int ret, i;
        for (i = 0; i < 1; i++) {
            ret = testGmcConnect(&conn_t[i], &stmt_t[i]);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
        ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_MEMDATA_STAT",
                            (char *)"CHUNK_UTILIZATION_RATIO: 0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char Normal_schemas[1024] = {0};
        const char *V_config = "{\"max_record_num\":100000000,\"defragmentation\":true}";
        for (int i = 0; i < 100; i++) {
                snprintf(Normal_schemas, 1023,
                "[{\"type\":\"record\", \"name\":\"Normal%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"uint32\"},"
                "{\"name\":\"F1\", \"type\":\"uint32\"},{\"name\":\"F2\", \"type\":\"uint32\"},{\"name\":\"F3\", "
                "\"type\":\"uint32\"}],"
                "\"keys\":[{\"node\":\"Normal%d\", \"name\":\"Normal_K0\", \"fields\":[\"F0\"], "
                "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
                i, i);
            ret = GmcCreateVertexLabel(stmt_t[0], Normal_schemas, V_config);
            EXPECT_EQ(GMERR_OK, ret);
        }
        char labelNames[128] = {0};
        int insertNum = 10000;
        if (g_envType == 3) {
            insertNum = 50000;// 光启20w条 用例执行超时10min;10万条部分环境在10min正负30s，调小数据量
        }
        for (int i = 0; i < 100; i++) {
            (void)snprintf(labelNames, 128, "Normal%d", i);
            ret = testGmcPrepareStmtByLabelName(stmt_t[0], labelNames, GMC_OPERATION_INSERT);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t j = 0; j < insertNum; j++) {
                ret = GmcSetVertexProperty(stmt_t[0], "F0", GMC_DATATYPE_UINT32, &j, sizeof(uint32_t));
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcSetVertexProperty(stmt_t[0], "F1", GMC_DATATYPE_UINT32, &j, sizeof(uint32_t));
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcExecute(stmt_t[0]);
            }
            if (ret == 10001) {
                AW_FUN_Log(LOG_STEP, "mem full in loop:%d\n", i);
                break;
            }
        }
        system("gmsysview -q V\\$STORAGE_MEMDATA_STAT");
        if (g_envType == 3) {
            ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_MEMDATA_STAT",
                            (char *)"CHUNK_UTILIZATION_RATIO");// 光启环境性能差， 30*200000数据无法写满，且用例会超时10min
        } else {
           ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_MEMDATA_STAT",
                            (char *)"CHUNK_UTILIZATION_RATIO: 100");
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int i = 0; i < 100; i++) {
            (void)snprintf(labelNames, 128, "Normal%d", i);
            ret = GmcTruncateVertexLabel(stmt_t[0], labelNames);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_MEMDATA_STAT",
                            (char *)"CHUNK_UTILIZATION_RATIO");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int i = 0; i < 100; i++) {
            (void)snprintf(labelNames, 128, "Normal%d", i);
            ret = GmcDropVertexLabel(stmt_t[0], labelNames);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
        for (i = 0; i < 1; i++) {
            ret = testGmcDisconnect(conn_t[i], stmt_t[i]);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }
    system("gmadmin -cfgName enableClusterHash -cfgVal 0"); 
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 027. 1024个表，写入数据，查询
 Info  : 过程中查看 STORAGE_HEAP_STAT
*****************************************************************************/
// 027. 1024个表，写入数据，查询
TEST_F(V3_HashDfx4, Yang_034_003_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("gmadmin -cfgName enableClusterHash -cfgVal 1");
    int clusterValue = 0;
    int ret = 0;
    ret = TestGetConfigValueInt((const char*)"enableClusterHash", &clusterValue);
    EXPECT_EQ(GMERR_OK, ret);
    if (g_envType != 2) { // IOT受其他用例和组件干扰表总量不稳定
        GmcConnT *conn_t[MAX_CONN_SIZE ] = {0};
        GmcStmtT *stmt_t[MAX_CONN_SIZE ] = {0};
        int i;
        for (i = 0; i < 1; i++) {
            ret = testGmcConnect(&conn_t[i], &stmt_t[i]);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }

        char Normal_schemas[1024] = {0};
        const char *V_config = "{\"max_record_num\":100000000,\"defragmentation\":true}";
        for (int i = 0; i < 1024; i++) {
                snprintf(Normal_schemas, 1023,
                "[{\"type\":\"record\", \"name\":\"Normal%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"uint32\"},"
                "{\"name\":\"F1\", \"type\":\"uint32\"},{\"name\":\"F2\", \"type\":\"uint32\"},{\"name\":\"F3\", "
                "\"type\":\"uint32\"}],"
                "\"keys\":[{\"node\":\"Normal%d\", \"name\":\"Normal_K0\", \"fields\":[\"F0\"], "
                "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
                i, i);
            ret = GmcCreateVertexLabel(stmt_t[0], Normal_schemas, V_config);
            EXPECT_EQ(GMERR_OK, ret);
        }
        char labelNames[128] = {0};
        for (int i = 0; i < 1024; i++) {
            (void)snprintf(labelNames, 128, "Normal%d", i);
            ret = testGmcPrepareStmtByLabelName(stmt_t[0], labelNames, GMC_OPERATION_INSERT);
            EXPECT_EQ(GMERR_OK, ret);
            for (uint32_t j = 0; j < 100; j++) {
                ret = GmcSetVertexProperty(stmt_t[0], "F0", GMC_DATATYPE_UINT32, &j, sizeof(uint32_t));
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcSetVertexProperty(stmt_t[0], "F1", GMC_DATATYPE_UINT32, &j, sizeof(uint32_t));
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcExecute(stmt_t[0]);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        for (int i = 0; i < 1024; i++) {
            (void)snprintf(labelNames, 128, "Normal%d", i);
            ret = GmcTruncateVertexLabel(stmt_t[0], labelNames);
            EXPECT_EQ(GMERR_OK, ret);
        }
        if (clusterValue == 0) {
            ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_HEAP_STAT -f LABEL_NAME=Normal1023",
                                 (char *)"WRITE_BYTES: 0");
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        for (int i = 0; i < 1024; i++) {
            (void)snprintf(labelNames, 128, "Normal%d", i);
            ret = GmcDropVertexLabel(stmt_t[0], labelNames);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
        for (i = 0; i < 1; i++) {
            ret = testGmcDisconnect(conn_t[i], stmt_t[i]);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }
    system("gmadmin -cfgName enableClusterHash -cfgVal 0"); 
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 清除系统命名空间外所有命名空间
int32_t DropAllNamespaceYang34()
{
    int32_t ret;
    GmcConnT *tempConnAsync = NULL;
    GmcStmtT *tempStmtAsync = NULL;
    AsyncUserDataT tempData = {0};
    ret = testGmcConnect(&tempConnAsync, &tempStmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    RETURN_IFERR(ret);
    char cmd[] = "gmsysview -q 'V$CATA_NAMESPACE_INFO' | grep NAMESPACE_NAME | awk '{print $NF}'| sed 's/^[[:space:]]*//;s/[[:space:]]*$//'|grep -v -E 'system|public|sysview'";
    FILE *fp = popen(cmd, "r");
    if (fp == NULL) {
        printf("DropAllNamespace popen(%s) failed.\n", cmd);
        return T_FAILED;
    }
    char buf[200];
    uint64_t length;
    (void)memset(buf, 0, sizeof(buf));
    while (fgets(buf, sizeof(buf), fp) != NULL) {
        length = strlen(buf);
        while (length > 0 && (buf[length - 1] == '\n' || buf[length - 1] == '\r')) {
            buf[length - 1] = '\0';
            --length;
        }
        // 清理nsp
        ret = GmcClearNamespaceAsync(tempStmtAsync, buf, ClearNSCallbak, &tempData);
        RETURN_IFERR(ret);
        printf("%s********************\n", buf);
        ret = testWaitAsyncRecv(&tempData);
        RETURN_IFERR(ret);
        memset(&tempData, 0, sizeof(AsyncUserDataT));
        // 删除nsp
        ret = GmcDropNamespaceAsync(tempStmtAsync, buf, drop_namespace_callback, &tempData);
        RETURN_IFERR(ret);
        ret = testWaitAsyncRecv(&tempData);
        RETURN_IFERR(ret);
        memset(&tempData, 0, sizeof(AsyncUserDataT));
    }
    pclose(fp);
    ret = testGmcDisconnect(tempConnAsync, tempStmtAsync);
    RETURN_IFERR(ret);
    return T_OK;
}

/*****************************************************************************
 Description  : 028. 255个命名空间，写入数据【V5最大为64个】主要查看内存
 Info  : 当前没有视图同时含有命名空间和整体内存数据
 CATA_VERTEX_LABEL_INFO观察命名空间ID，STORAGE_HEAP_STAT查看对应的单表空间
*****************************************************************************/
// 028. 64个命名空间，写入数查看内存
TEST_F(V3_HashDfx2, Yang_034_003_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("gmadmin -cfgName enableClusterHash -cfgVal 1"); 
    if (g_envType != 2) { //IOT空间受其他用例和组件干扰不稳定
        GmcConnT *conn_t[MAX_CONN_SIZE ] = {0};
        GmcStmtT *stmt_t[MAX_CONN_SIZE ] = {0};
        int ret, i;
        // 清理nsp
        ret = DropAllNamespaceYang34();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        system("gmsysview -q V\\$CATA_NAMESPACE_INFO |grep NAMESPACE_NAME:");
        for (i = 0; i < 1; i++) {
            ret = testGmcConnect(&conn_t[i], &stmt_t[i]);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
        char spaceNames[128] = {0};
        for (i = 0; i < 64; i++) {
            (void)snprintf(spaceNames, 128, "spacename%d", i);
            ret = GmcCreateNamespace(stmt_t[0], spaceNames, "gmdbv5");
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
        char Normal_schemas[1024] = {0};
        char labelNames[128] = {0};
        const char *V_config = "{\"max_record_num\":100000000,\"defragmentation\":true}";
        for (i = 0; i < 64; i++) {
            (void)snprintf(spaceNames, 128, "spacename%d", i);
            ret = GmcUseNamespace(stmt_t[0], spaceNames);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            for (int i = 0; i < 3; i++) {
                    snprintf(Normal_schemas, 1023,
                    "[{\"type\":\"record\", \"name\":\"Normal%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"uint32\"},"
                    "{\"name\":\"F1\", \"type\":\"uint32\"},{\"name\":\"F2\", \"type\":\"uint32\"},{\"name\":\"F3\", "
                    "\"type\":\"uint32\"}],"
                    "\"keys\":[{\"node\":\"Normal%d\", \"name\":\"Normal_K0\", \"fields\":[\"F0\"], "
                    "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
                    i, i);
                ret = GmcCreateVertexLabel(stmt_t[0], Normal_schemas, V_config);
                EXPECT_EQ(GMERR_OK, ret);
                (void)snprintf(labelNames, 128, "Normal%d", i);
                ret = testGmcPrepareStmtByLabelName(stmt_t[0], labelNames, GMC_OPERATION_INSERT);
                EXPECT_EQ(GMERR_OK, ret);
                for (uint32_t j = 0; j < 100; j++) {
                    ret = GmcSetVertexProperty(stmt_t[0], "F0", GMC_DATATYPE_UINT32, &j, sizeof(uint32_t));
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcSetVertexProperty(stmt_t[0], "F1", GMC_DATATYPE_UINT32, &j, sizeof(uint32_t));
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcExecute(stmt_t[0]);
                    EXPECT_EQ(GMERR_OK, ret);
                }
            }
        }
        ret = executeCommand((char *)"gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=Normal1",
                            (char *)"NAMESPACE_ID: ");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (i = 0; i < 64; i++) {
            (void)snprintf(spaceNames, 128, "spacename%d", i);
            ret = GmcUseNamespace(stmt_t[0], spaceNames);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            for (int i = 0; i < 3; i++) {
                (void)snprintf(labelNames, 128, "Normal%d", i);
                ret = GmcDropVertexLabel(stmt_t[0], labelNames);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            }
        }
        for (i = 0; i < 64; i++) {
            (void)snprintf(spaceNames, 128, "spacename%d", i);
            ret = GmcDropNamespace(stmt_t[0], spaceNames);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
        for (i = 0; i < 1; i++) {
            ret = testGmcDisconnect(conn_t[i], stmt_t[i]);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }
    system("gmadmin -cfgName enableClusterHash -cfgVal 0"); 
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 029. record没有表、没有数据集、参数错误测试
 Info  : 不使用交互式查询
*****************************************************************************/
// 029. record没有表、没有数据集、参数错误测试
TEST_F(V3_HashDfx2, Yang_034_003_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn_t[MAX_CONN_SIZE ] = {0};
    GmcStmtT *stmt_t[MAX_CONN_SIZE ] = {0};
    int ret, i;
    for (i = 0; i < 1; i++) {
        ret = testGmcConnect(&conn_t[i], &stmt_t[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_VERTEX_COUNT",
                         (char *)"record count");
#if (RUN_INDEPENDENT) && !(ENV_SUSE)
    // 过滤光启 自带的表
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
#else
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
    for (i = 0; i < 1; i++) {
        ret = testGmcDisconnect(conn_t[i], stmt_t[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 030. record查看20W大数据
 Info  : 
*****************************************************************************/
// 030. record查看20W大数据
TEST_F(V3_HashDfx2, Yang_034_003_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    if (g_envType != 2) { // 大数据量引起IOT概率崩溃
        GmcConnT *conn_t[MAX_CONN_SIZE ] = {0};
        GmcStmtT *stmt_t[MAX_CONN_SIZE ] = {0};
        int ret, i;
        for (i = 0; i < 1; i++) {
            ret = testGmcConnect(&conn_t[i], &stmt_t[i]);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
        char tableName[3][48] = {"Normal0", "Normal1", "Normal2"};
        char Normal_schemas[1024] = {0};
        const char *V_config = "{\"max_record_num\":100000000,\"defragmentation\":true}";
        for (int i = 0; i < 1; i++) {
                snprintf(Normal_schemas, 1023,
                "[{\"type\":\"record\", \"name\":\"Normal%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"uint32\"},"
                "{\"name\":\"F1\", \"type\":\"uint32\"},{\"name\":\"F2\", \"type\":\"uint32\"},{\"name\":\"F3\", "
                "\"type\":\"uint32\"}],"
                "\"keys\":[{\"node\":\"Normal%d\", \"name\":\"Normal_K0\", \"fields\":[\"F0\"], "
                "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
                i, i);
            ret = GmcCreateVertexLabel(stmt_t[0], Normal_schemas, V_config);
            EXPECT_EQ(GMERR_OK, ret);
        }
        for (int i = 0; i < 200000; i++) {
            ret = testGmcPrepareStmtByLabelName(stmt_t[0], "Normal0", GMC_OPERATION_INSERT);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(stmt_t[0], "F0", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(stmt_t[0], "F1", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt_t[0]);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_VERTEX_COUNT", (char *)"record count: 200000");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcTruncateVertexLabel(stmt_t[0], "Normal0");
        EXPECT_EQ(GMERR_OK, ret);

        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int i = 0; i < 1; i++) {
            ret = GmcDropVertexLabel(stmt_t[i], tableName[i]);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
        for (i = 0; i < 1; i++) {
            ret = testGmcDisconnect(conn_t[i], stmt_t[i]);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 031. record查看choice-case表数据
 Info  : 可以看到当前表类型决定了在部分打散中始终是一个表
*****************************************************************************/
// 031. record查看choice-case表数据
TEST_F(V3_HashDfx, Yang_034_003_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    preparesCCHCA8(1);
    int ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_VERTEX_COUNT -f table=NamespaceA.C_CHCA_8_N1",
                            (char *)"record count: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 032. record查看c-list表数据
 Info  : 
*****************************************************************************/
// 032. record查看c-list表数据
TEST_F(V3_HashDfx, Yang_034_003_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    preparesCL8(20001, 20002);
    int ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_VERTEX_COUNT -f table=NamespaceA.C_L_8_N8",
                            (char *)"record count: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_VERTEX_COUNT -f table=NamespaceA.C_L_8_N7",
                            (char *)"record count: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 033. record查看混合类型表数据
 Info  : 
*****************************************************************************/
// 033. record查看混合类型表数据
TEST_F(V3_HashDfx, Yang_034_003_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    prepares(1, 2000);
    int ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_VERTEX_COUNT -f table=NamespaceA.C_MIX1_N6",
                            (char *)"record count: 1999");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_VERTEX_COUNT -f table=NamespaceA.C_MIX1_N1",
                            (char *)"record count: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand((char *)"gmsysview -q V\\$STORAGE_VERTEX_COUNT -f table=NamespaceA.C_MIX1_N2",
                            (char *)"record count: 1999");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 034. record查看混合类型表数据主键为in8类型数据
 Info  : 只允许uint32_t类型
*****************************************************************************/
// 034.混合类型表，建立命名空间后查看索引和内存视图
TEST_F(V3_HashDfx2, Yang_034_003_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    AsyncUserDataT data = {0};

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t i = 0;
    for (i = 0; i < 10; i++) {
        ret = GmcAllocStmt(g_conn_async, &g_stmt_list[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcAllocStmt(g_conn, &g_stmt_sync[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = g_namespace;
    nspCfg.tablespaceName = NULL;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观
    AW_FUN_Log(LOG_INFO, "namespaceName: %s\n", g_namespace);

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    testCreateLabelCint8(g_stmt_async);
    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, g_namespace, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    for (i = 0; i < 10; i++) {
        GmcFreeStmt(g_stmt_sync[i]);
    }
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    GmcFreeStmt(g_stmt_root);
    for (i = 0; i < 10; i++) {
        GmcFreeStmt(g_stmt_list[i]);
    }
    testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_conn_async = NULL;
    g_stmt_async = NULL;
    g_stmt_root = NULL;
    for (i = 0; i < 10; i++) {
        g_stmt_list[i] = NULL;
        g_stmt_sync[i] = NULL;
    }

    g_rootNode = NULL;
    for (i = 0; i < 10; i++) {
        g_childNode[i] = NULL;
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
