/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: 034
 * Author: ywx1037054
 * Create: 2022-9-5
 */
#include "Yang_Tree_Adapt.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
GmcStmtT *g_stmt_sync[10] = {0};
GmcConnT *g_conn_async = NULL;
GmcStmtT *g_stmt_async = NULL;
GmcStmtT *g_stmt_root = NULL;
GmcStmtT *g_stmt_list[10] = {0};
GmcNodeT *g_rootNode = NULL;
GmcNodeT *g_childNode[40] = {0};

class Gmsysview_subtree : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void Gmsysview_subtree::SetUpTestCase()
{
    int ret;
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void Gmsysview_subtree::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}

void Gmsysview_subtree::SetUp()
{
    int ret;
    AsyncUserDataT data = {0};
    char g_errorCode01[1024] = {0};
    char g_errorCode02[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t i = 0;
    for (i = 0; i < 10; i++) {
        ret = GmcAllocStmt(g_conn_async, &g_stmt_list[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcAllocStmt(g_conn, &g_stmt_sync[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = g_namespace;
    nspCfg.tablespaceName = NULL;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观
    AW_FUN_Log(LOG_INFO, "namespaceName: %s\n", g_namespace);

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 创建Vertex表
   testCreateLabelMix(g_stmt_async);
   testCreateLabelCL8(g_stmt_async);
   testCreateLabelCCHCA8(g_stmt_async);
   testCreateLabelC8(g_stmt_async);
    // 同步连接也使用namespace
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void Gmsysview_subtree::TearDown()
{
    AW_CHECK_LOG_END();
    int ret;
    uint32_t i = 0;
    AsyncUserDataT data = {0};

   // 异步删除namespace
    ret = GmcClearNamespaceAsync(g_stmt_async, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
     // 删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, g_namespace, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    for (i = 0; i < 10; i++) {
        GmcFreeStmt(g_stmt_sync[i]);
    }
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    GmcFreeStmt(g_stmt_root);
    for (i = 0; i < 10; i++) {
        GmcFreeStmt(g_stmt_list[i]);
    }
    testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_conn_async = NULL;
    g_stmt_async = NULL;
    g_stmt_root = NULL;
    for (i = 0; i < 10; i++) {
        g_stmt_list[i] = NULL;
        g_stmt_sync[i] = NULL;
    }

    g_rootNode = NULL;
    for (i = 0; i < 10; i++) {
        g_childNode[i] = NULL;
    }
}

void prepares()
{
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "C_MIX1_N1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "C_MIX1_N2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 100 + i;
        testYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        ret = GmcYangEditChildNode(g_childNode[1], "C_MIX1_N3", GMC_OPERATION_INSERT, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

         ret = GmcYangEditChildNode(g_childNode[2], "C_MIX1_N4", GMC_OPERATION_INSERT, &g_childNode[3]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         fieldValue = 100+i;
         testYangSetNodeProperty(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

         ret = GmcYangEditChildNode(g_childNode[3], "C_MIX1_N5", GMC_OPERATION_INSERT, &g_childNode[4]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         fieldValue = 100+i;
         testYangSetNodeProperty(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
         ret = GmcBatchAddDML(batch, g_stmt_list[1]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "C_MIX1_N6", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_list[1], g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[6]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 55 + i;
        testYangSetNodeProperty(g_childNode[6], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

      ret = GmcYangEditChildNode(g_childNode[6], "C_MIX1_N7", GMC_OPERATION_INSERT, &g_childNode[7]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

      ret = GmcYangEditChildNode(g_childNode[7], "C_MIX1_N8", GMC_OPERATION_INSERT, &g_childNode[8]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      fieldValue = 55+i;
      testYangSetNodeProperty(g_childNode[8], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
      ret = GmcBatchAddDML(batch, g_stmt_list[2]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   }
    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(5, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(5, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/* MIX1 Yang模型示例
                          C_MIX1_N1(con) —— —— ——  2个...
                             |                    |
                          C_MIX1_N2(list1)     C_MIX1_N2(list2)...
                             |
                          C_MIX1_N3(choice)
                             |
                          C_MIX1_N4(case)
                             |
                          C_MIX1_N5(con)
                             |
                          C_MIX1_N6(list1)
                             |
                          C_MIX1_N7(choice)
                             |
                          C_MIX1_N8(case)
*/
/*****************************************************************************
 Description  : 001.MIX1类型, 内容过滤，第一层开始计算深度，深度为1
 Info  : 部分打散模式——-全部默认
*****************************************************************************/
TEST_F(Gmsysview_subtree, Yang_034_002_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "C_MIX1_N1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "C_MIX1_N2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 100 + i;
        testYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        ret = GmcYangEditChildNode(g_childNode[1], "C_MIX1_N3", GMC_OPERATION_INSERT, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

         ret = GmcYangEditChildNode(g_childNode[2], "C_MIX1_N4", GMC_OPERATION_INSERT, &g_childNode[3]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         fieldValue = 100+i;
         testYangSetNodeProperty(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

         ret = GmcYangEditChildNode(g_childNode[3], "C_MIX1_N5", GMC_OPERATION_INSERT, &g_childNode[4]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         fieldValue = 100+i;
         testYangSetNodeProperty(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
         ret = GmcBatchAddDML(batch, g_stmt_list[1]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "C_MIX1_N6", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_list[1], g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[6]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 55 + i;
        testYangSetNodeProperty(g_childNode[6], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

      ret = GmcYangEditChildNode(g_childNode[6], "C_MIX1_N7", GMC_OPERATION_INSERT, &g_childNode[7]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

      ret = GmcYangEditChildNode(g_childNode[7], "C_MIX1_N8", GMC_OPERATION_INSERT, &g_childNode[8]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      fieldValue = 55+i;
      testYangSetNodeProperty(g_childNode[8], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
      ret = GmcBatchAddDML(batch, g_stmt_list[2]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   }
    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(5, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(5, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree 查询
    char *ReplyJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Reply/Reply_02_01.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0;
    AW_FUN_Log(LOG_STEP, "步骤1.设置回调数据和期望json");

    char *FilterJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Filter/Filter_01_1.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    char *reply = NULL;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;

    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤2.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    snprintf(g_gcmd, 1024, "%s/gmsysview -s %s subtree -ns NamespaceA03402 -rn %s -json '%s'"
                           " >./Yang_Tree_Schema/SysviewReply/Sysview_02_01.json\n", g_toolPath,
        g_connServer, "C_MIX1_N1", FilterJson);
    AW_FUN_Log(LOG_INFO, "%s", g_gcmd);
    system(g_gcmd);
    memset(g_gcmd, 0, sizeof(g_gcmd));
    AW_FUN_Log(LOG_STEP, "步骤3.subtree查询和等待响应");


    char *SysviewJson = NULL;
    readJanssonFile("Yang_Tree_Schema/SysviewReply/Sysview_02_01.json", &SysviewJson);
    EXPECT_NE((void *)NULL, SysviewJson);
    EXPECT_TRUE(testYangJsonIsEqual(ReplyJson, SysviewJson));
    free(SysviewJson);
    free(ReplyJson);
    free(FilterJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 002.MIX1类型, 内容过滤，第一层开始计算深度，深度为1
 Info  : 部分打散模式——-depth 0 -defaultMode REPORT_ALL_TAGGED - c CONFIG
*****************************************************************************/
TEST_F(Gmsysview_subtree, Yang_034_002_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "C_MIX1_N1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "C_MIX1_N2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 100 + i;
        testYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        ret = GmcYangEditChildNode(g_childNode[1], "C_MIX1_N3", GMC_OPERATION_INSERT, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

         ret = GmcYangEditChildNode(g_childNode[2], "C_MIX1_N4", GMC_OPERATION_INSERT, &g_childNode[3]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         fieldValue = 100+i;
         testYangSetNodeProperty(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

         ret = GmcYangEditChildNode(g_childNode[3], "C_MIX1_N5", GMC_OPERATION_INSERT, &g_childNode[4]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         fieldValue = 100+i;
         testYangSetNodeProperty(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
         ret = GmcBatchAddDML(batch, g_stmt_list[1]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "C_MIX1_N6", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_list[1], g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[6]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 55 + i;
        testYangSetNodeProperty(g_childNode[6], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

      ret = GmcYangEditChildNode(g_childNode[6], "C_MIX1_N7", GMC_OPERATION_INSERT, &g_childNode[7]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

      ret = GmcYangEditChildNode(g_childNode[7], "C_MIX1_N8", GMC_OPERATION_INSERT, &g_childNode[8]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      fieldValue = 55+i;
      testYangSetNodeProperty(g_childNode[8], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
      ret = GmcBatchAddDML(batch, g_stmt_list[2]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   }
    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(5, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(5, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree 查询
    char *ReplyJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Reply/Reply_02_02.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0;
    AW_FUN_Log(LOG_STEP, "步骤1.设置回调数据和期望json");

    char *FilterJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Filter/Filter_01_1.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED;
    filter.configFlag = GMC_SUBTREE_FILTER_CONFIG;
    filter.maxDepth = 0;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;

    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    snprintf(g_gcmd, 1024, "%s/gmsysview -s %s subtree -ns NamespaceA03402 -rn %s -json '%s' -depth 0 -defaultMode REPORT_ALL_TAGGED"
                           " -configFlag CONFIG >./Yang_Tree_Schema/SysviewReply/Sysview_02_02.json\n", g_toolPath,
        g_connServer, "C_MIX1_N1", FilterJson);
    AW_FUN_Log(LOG_INFO, "%s", g_gcmd);
    system(g_gcmd);
    memset(g_gcmd, 0, sizeof(g_gcmd));
    AW_FUN_Log(LOG_STEP, "步骤3.subtree查询和等待响应");

    char *SysviewJson = NULL;
    readJanssonFile("Yang_Tree_Schema/SysviewReply/Sysview_02_02.json", &SysviewJson);
    EXPECT_NE((void *)NULL, SysviewJson);
    EXPECT_TRUE(testYangJsonIsEqual(ReplyJson, SysviewJson));
    free(SysviewJson);
    free(ReplyJson);
    free(FilterJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

const char *FilterJson_02=
    R"({
        "SubT1Con_8_C": [
            {
                "SubT2Con_8_C":[
                    {
                        "SubT3Con_8_C":[
                                {
                                        "SubT4Con_8_C":[
                                                {
                                                    "F0": 9
                                                }
                                        ]
                                }
                            ]
                    }
                ]
            }
        ]
})";
const char *FilterJson_03=
    R"({
                                                    "F1": 1
})";

const char *FilterJson_04=R"()";

/*****************************************************************************
 Description  : 003.MIX1类型,内容过滤 第一层起始位
 Info  : 其他参数默认，subtreejson为空
*****************************************************************************/
TEST_F(Gmsysview_subtree, Yang_034_002_003)
{
    int ret = 0;
    prepares();
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char g_errorCode01[1024] = {0};
    char g_errorCode02[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    SubtreeFilterCbParam Data = {0};
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0;
    AW_FUN_Log(LOG_STEP, "步骤1.设置回调数据和期望json");

    char *FilterJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Filter/Filter_01_1.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson_04;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED;
    filter.configFlag = GMC_SUBTREE_FILTER_CONFIG;
    filter.maxDepth = 0;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;

    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data, 1, 160);
    ASSERT_EQ(1004000, Data.expectStatus);
    snprintf(g_gcmd, 1024, "%s/gmsysview -s %s subtree -ns NamespaceA03402 -rn %s -json '%s' -depth 0 -defaultMode REPORT_ALL_TAGGED"
                           " -configFlag CONFIG >./Yang_Tree_Schema/SysviewReply/Sysview_02_03.json\n", g_toolPath,
        g_connServer, "C_MIX1_N1", FilterJson_04);
    AW_FUN_Log(LOG_INFO, "%s", g_gcmd);
    system(g_gcmd);
    memset(g_gcmd, 0, sizeof(g_gcmd));
    free(FilterJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}


const char *FilterJson_complex=
    R"({
    "C_MIX1_N2":
    [
        {
            "C_MIX1_N3":
                {
                    "C_MIX1_N4":
                    {
                        "F1": 100,
                        "F3": 3.141
                    }
                }
        }
    ]
})";
/*****************************************************************************
 Description  : 004.MIX1类型,内容过滤 第一层起始位
 Info  : 其他参数默认，subtreejson为内容+叶子+容器
*****************************************************************************/
TEST_F(Gmsysview_subtree, Yang_034_002_004)
{
    int ret = 0;
    prepares();
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *ReplyJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Reply/Reply_02_04.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0;

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson_complex;
    filter.jsonFlag =  GMC_JSON_INDENT(4);

    char *reply = NULL;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;

    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤2.视图查询");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);

    snprintf(g_gcmd, 1024, "%s/gmsysview -s %s subtree -ns NamespaceA03402 -rn %s -json '%s'"
                           " >./Yang_Tree_Schema/SysviewReply/Sysview_02_04.json\n", g_toolPath,
        g_connServer, "C_MIX1_N1", FilterJson_complex);
    AW_FUN_Log(LOG_INFO, "%s", g_gcmd);
    system(g_gcmd);
    memset(g_gcmd, 0, sizeof(g_gcmd));

    char *SysviewJson = NULL;
    readJanssonFile("Yang_Tree_Schema/SysviewReply/Sysview_02_04.json", &SysviewJson);
    EXPECT_NE((void *)NULL, SysviewJson);
    EXPECT_TRUE(testYangJsonIsEqual(ReplyJson, SysviewJson));
    free(SysviewJson);
    free(ReplyJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 005.MIX1类型,内容过滤 第一层起始位
 Info  : -depth深度为1，其他参数为默认
*****************************************************************************/
TEST_F(Gmsysview_subtree, Yang_034_002_005)
{
    int ret = 0;
    prepares();
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *ReplyJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Reply/Reply_02_05.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0;

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson_complex;
    filter.jsonFlag =  GMC_JSON_INDENT(4);

    filter.maxDepth = 1;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;

    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.视图查询");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    snprintf(g_gcmd, 1024, "%s/gmsysview -s %s subtree -ns NamespaceA03402 -rn %s -json '%s'"
                           " -depth 1 >./Yang_Tree_Schema/SysviewReply/Sysview_02_05.json", g_toolPath,
        g_connServer, "C_MIX1_N1", FilterJson_complex);
    AW_FUN_Log(LOG_INFO, "%s", g_gcmd);
    system(g_gcmd);
    memset(g_gcmd, 0, sizeof(g_gcmd));

    char *SysviewJson = NULL;
    readJanssonFile("Yang_Tree_Schema/SysviewReply/Sysview_02_05.json", &SysviewJson);
    EXPECT_NE((void *)NULL, SysviewJson);
    EXPECT_TRUE(testYangJsonIsEqual(ReplyJson, SysviewJson));
    free(SysviewJson);
    free(ReplyJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 006.MIX1类型,内容过滤 第一层起始位
 Info  : -depth深度为超越模型深度
*****************************************************************************/
TEST_F(Gmsysview_subtree, Yang_034_002_006)
{
    int ret = 0;
    prepares();
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *ReplyJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Reply/Reply_02_06.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0;

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson_complex;
    filter.jsonFlag =  GMC_JSON_INDENT(4);

    char *reply = NULL;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;

    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤2.视图查询");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);

    snprintf(g_gcmd, 1024, "%s/gmsysview -s %s subtree -ns NamespaceA03402 -rn %s -json '%s' -depth 10"
                           "  >./Yang_Tree_Schema/SysviewReply/Sysview_02_06.json\n", g_toolPath,
        g_connServer, "C_MIX1_N1", FilterJson_complex);
    AW_FUN_Log(LOG_INFO, "%s", g_gcmd);
    system(g_gcmd);
    memset(g_gcmd, 0, sizeof(g_gcmd));

    char *SysviewJson = NULL;
    readJanssonFile("Yang_Tree_Schema/SysviewReply/Sysview_02_06.json", &SysviewJson);
    EXPECT_NE((void *)NULL, SysviewJson);
    EXPECT_TRUE(testYangJsonIsEqual(ReplyJson, SysviewJson));
    free(SysviewJson);
    free(ReplyJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 007.MIX1类型,内容过滤 第一层起始位
 Info  : -d深度为300， 其他参数为默认
*****************************************************************************/
TEST_F(Gmsysview_subtree, Yang_034_002_007)
{
    int ret = 0;
    prepares();
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *ReplyJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Reply/Reply_02_07.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0;

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson_complex;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.configFlag = GMC_SUBTREE_FILTER_CONFIG;
    filter.maxDepth = 300;
    char *reply = NULL;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;

    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤2.视图查询");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);

    snprintf(g_gcmd, 1024, "%s/gmsysview -s %s subtree -ns NamespaceA03402 -rn %s -json '%s'"
                            "-depth 300 >./Yang_Tree_Schema/SysviewReply/Sysview_02_07.json", g_toolPath,
        g_connServer, "C_MIX1_N1", FilterJson_complex);
    AW_FUN_Log(LOG_INFO, "%s", g_gcmd);
    system(g_gcmd);
    memset(g_gcmd, 0, sizeof(g_gcmd));

    const char *LastError=(const char *)"Not normal parameter value";
    ret = executeCommand((char *)"grep -r 'ErrorCodeDescription' Yang_Tree_Schema/SysviewReply/Sysview_02_07.json",
                         (const char *)LastError);
    EXPECT_EQ(GMERR_OK, ret);
    free(ReplyJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 008.MIX1类型,内容过滤 第一层起始位
 Info  : -depth深度为-1， 其他参数为默认
*****************************************************************************/
TEST_F(Gmsysview_subtree, Yang_034_002_008)
{
    int ret = 0;
    prepares();
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SubtreeFilterCbParam Data = {0};
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0;

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson_complex;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.configFlag = GMC_SUBTREE_FILTER_CONFIG;
    filter.maxDepth = -1;
    char *reply = NULL;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;

    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤2.视图查询");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);

    snprintf(g_gcmd, 1024, "%s/gmsysview -s %s subtree -ns NamespaceA03402 -rn %s"
                            " -json '%s' -depth -1"
                            " >./Yang_Tree_Schema/SysviewReply/Sysview_02_08.json", g_toolPath,
        g_connServer, "C_MIX1_N1", FilterJson_complex);
    AW_FUN_Log(LOG_INFO, "%s", g_gcmd);
    system(g_gcmd);
    memset(g_gcmd, 0, sizeof(g_gcmd));

    const char *LastError=(const char *)"Param of -depth should be [1, 255]";
    ret = executeCommand((char *)"grep -r 'WARN' Yang_Tree_Schema/SysviewReply/Sysview_02_08.json",
                         (const char *)LastError);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 009.MIX1类型,内容过滤 第一层起始位
 Info  : -depth 1 -defaultMode EXPLICIT， 其他参数为默认
*****************************************************************************/
TEST_F(Gmsysview_subtree, Yang_034_002_009)
{
    int ret = 0;
    prepares();
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *ReplyJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Reply/Reply_02_09.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0;

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson_complex;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_EXPLICIT;
    filter.maxDepth = 1;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;

    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.视图查询");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);

    snprintf(g_gcmd, 1024, "%s/gmsysview -s %s subtree -ns NamespaceA03402 -rn %s -json '%s' -depth 1 -defaultMode EXPLICIT"
                           " >./Yang_Tree_Schema/SysviewReply/Sysview_02_09.json\n", g_toolPath,
        g_connServer, "C_MIX1_N1", FilterJson_complex);
    AW_FUN_Log(LOG_INFO, "%s", g_gcmd);
    system(g_gcmd);
    memset(g_gcmd, 0, sizeof(g_gcmd));

    char *SysviewJson = NULL;
    readJanssonFile("Yang_Tree_Schema/SysviewReply/Sysview_02_09.json", &SysviewJson);
    EXPECT_NE((void *)NULL, SysviewJson);
    EXPECT_TRUE(testYangJsonIsEqual(ReplyJson, SysviewJson));
    free(SysviewJson);
    free(ReplyJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 010.MIX1类型,内容过滤 第一层起始位
 Info  : -depth 1 -defaultMode TRIM， 其他参数为默认
*****************************************************************************/
TEST_F(Gmsysview_subtree, Yang_034_002_010)
{
    int ret = 0;
    prepares();
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *ReplyJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Reply/Reply_02_10.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0;

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson_complex;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    filter.maxDepth = 1;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;

    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.视图查询");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);

    snprintf(g_gcmd, 1024, "%s/gmsysview -s %s subtree -ns NamespaceA03402 -rn %s -json '%s' -depth 1 -defaultMode TRIM"
                           " >./Yang_Tree_Schema/SysviewReply/Sysview_02_10.json\n", g_toolPath,
        g_connServer, "C_MIX1_N1", FilterJson_complex);
    AW_FUN_Log(LOG_INFO, "%s", g_gcmd);
    system(g_gcmd);
    memset(g_gcmd, 0, sizeof(g_gcmd));

    char *SysviewJson = NULL;
    readJanssonFile("Yang_Tree_Schema/SysviewReply/Sysview_02_10.json", &SysviewJson);
    EXPECT_NE((void *)NULL, SysviewJson);
    EXPECT_TRUE(testYangJsonIsEqual(ReplyJson, SysviewJson));
    free(SysviewJson);
    free(ReplyJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 011.MIX1类型,内容过滤 第一层起始位
 Info  : -depth 1 -defaultMode REPORT_ALL_TAGGED， 其他参数为默认
*****************************************************************************/
TEST_F(Gmsysview_subtree, Yang_034_002_011)
{
    int ret = 0;
    prepares();
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *ReplyJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Reply/Reply_02_11.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0;

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson_complex;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED;
    filter.maxDepth = 1;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;

    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.视图查询");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);

    snprintf(g_gcmd, 1024, "%s/gmsysview -s %s subtree -ns NamespaceA03402 -rn %s -json '%s' -depth 1 -defaultMode REPORT_ALL_TAGGED"
                           " >./Yang_Tree_Schema/SysviewReply/Sysview_02_11.json\n", g_toolPath,
        g_connServer, "C_MIX1_N1", FilterJson_complex);
    AW_FUN_Log(LOG_INFO, "%s", g_gcmd);
    system(g_gcmd);
    memset(g_gcmd, 0, sizeof(g_gcmd));

    char *SysviewJson = NULL;
    readJanssonFile("Yang_Tree_Schema/SysviewReply/Sysview_02_11.json", &SysviewJson);
    EXPECT_NE((void *)NULL, SysviewJson);
    EXPECT_TRUE(testYangJsonIsEqual(ReplyJson, SysviewJson));
    free(SysviewJson);
    free(ReplyJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 012.MIX1类型,内容过滤 第一层起始位
 Info  : -defaultMode EXPLICIT -configFlag DEFAULT ， 其他参数为默认
*****************************************************************************/
TEST_F(Gmsysview_subtree, Yang_034_002_012)
{
    int ret = 0;
    prepares();
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *ReplyJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Reply/Reply_02_12.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0;

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson_complex;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_EXPLICIT;
    filter.configFlag = GMC_SUBTREE_FILTER_DEFAULT;
    filter.maxDepth = 1;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;

    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.视图查询");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);

    snprintf(g_gcmd, 1024, "%s/gmsysview -s %s subtree -ns NamespaceA03402 -rn %s -json '%s' -depth 1 -defaultMode EXPLICIT"
                           " -configFlag DEFAULT >./Yang_Tree_Schema/SysviewReply/Sysview_02_12.json\n", g_toolPath,
        g_connServer, "C_MIX1_N1", FilterJson_complex);
    AW_FUN_Log(LOG_INFO, "%s", g_gcmd);
    system(g_gcmd);
    memset(g_gcmd, 0, sizeof(g_gcmd));

    char *SysviewJson = NULL;
    readJanssonFile("Yang_Tree_Schema/SysviewReply/Sysview_02_12.json", &SysviewJson);
    EXPECT_NE((void *)NULL, SysviewJson);
    EXPECT_TRUE(testYangJsonIsEqual(ReplyJson, SysviewJson));
    free(SysviewJson);
    free(ReplyJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 013.MIX1类型,内容过滤 第一层起始位
 Info  : -depth 1 -defaultMode EXPLICIT -configFlag DEFAULT
*****************************************************************************/
TEST_F(Gmsysview_subtree, Yang_034_002_013)
{
    int ret = 0;
    prepares();
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *ReplyJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Reply/Reply_02_13.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0;

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson_complex;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_EXPLICIT;
    filter.configFlag = GMC_SUBTREE_FILTER_DEFAULT;
    filter.maxDepth = 1;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;

    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.视图查询");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);

    snprintf(g_gcmd, 1024, "%s/gmsysview -s %s subtree -ns NamespaceA03402 -rn %s -json '%s' -depth 1 -defaultMode EXPLICIT"
                           " -configFlag DEFAULT >./Yang_Tree_Schema/SysviewReply/Sysview_02_13.json\n", g_toolPath,
        g_connServer, "C_MIX1_N1", FilterJson_complex);
    AW_FUN_Log(LOG_INFO, "%s", g_gcmd);
    system(g_gcmd);
    memset(g_gcmd, 0, sizeof(g_gcmd));

    char *SysviewJson = NULL;
    readJanssonFile("Yang_Tree_Schema/SysviewReply/Sysview_02_13.json", &SysviewJson);
    EXPECT_NE((void *)NULL, SysviewJson);
    EXPECT_TRUE(testYangJsonIsEqual(ReplyJson, SysviewJson));
    free(SysviewJson);
    free(ReplyJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 015.MIX1类型,内容过滤 第一层起始位
 Info  : -depth 1 -defaultMode REPORT_ALL_TAGGED -configFlag DEFAULT
*****************************************************************************/
TEST_F(Gmsysview_subtree, Yang_034_002_015)
{
    int ret = 0;
    prepares();
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *ReplyJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Reply/Reply_02_15.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0;

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson_complex;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED;
    filter.configFlag = GMC_SUBTREE_FILTER_DEFAULT;
    filter.maxDepth = 1;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;

    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.视图查询");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);

    snprintf(g_gcmd, 1024, "%s/gmsysview -s %s subtree -ns NamespaceA03402 -rn %s -json '%s' -depth 1 -defaultMode REPORT_ALL_TAGGED"
                           " -configFlag DEFAULT >./Yang_Tree_Schema/SysviewReply/Sysview_02_15.json\n", g_toolPath,
        g_connServer, "C_MIX1_N1", FilterJson_complex);
    AW_FUN_Log(LOG_INFO, "%s", g_gcmd);
    system(g_gcmd);
    memset(g_gcmd, 0, sizeof(g_gcmd));

    char *SysviewJson = NULL;
    readJanssonFile("Yang_Tree_Schema/SysviewReply/Sysview_02_15.json", &SysviewJson);
    EXPECT_NE((void *)NULL, SysviewJson);
    EXPECT_TRUE(testYangJsonIsEqual(ReplyJson, SysviewJson));
    free(SysviewJson);
    free(ReplyJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 016.MIX1类型,内容过滤 第一层起始位
 Info  : -depth 1 -defaultMode EXPLICIT -configFlag CONFIG
*****************************************************************************/
TEST_F(Gmsysview_subtree, Yang_034_002_016)
{
    int ret = 0;
    prepares();
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *ReplyJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Reply/Reply_02_16.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0;

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson_complex;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_EXPLICIT;
    filter.configFlag = GMC_SUBTREE_FILTER_CONFIG;
    filter.maxDepth = 1;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;

    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.视图查询");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);

    snprintf(g_gcmd, 1024, "%s/gmsysview -s %s subtree -ns NamespaceA03402 -rn %s -json '%s' -depth 1 -defaultMode EXPLICIT"
                           " -configFlag CONFIG >./Yang_Tree_Schema/SysviewReply/Sysview_02_16.json\n", g_toolPath,
        g_connServer, "C_MIX1_N1", FilterJson_complex);
    AW_FUN_Log(LOG_INFO, "%s", g_gcmd);
    system(g_gcmd);
    memset(g_gcmd, 0, sizeof(g_gcmd));

    char *SysviewJson = NULL;
    readJanssonFile("Yang_Tree_Schema/SysviewReply/Sysview_02_16.json", &SysviewJson);
    EXPECT_NE((void *)NULL, SysviewJson);
    EXPECT_TRUE(testYangJsonIsEqual(ReplyJson, SysviewJson));
    free(SysviewJson);
    free(ReplyJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 017.MIX1类型,内容过滤 第一层起始位
 Info  : -depth 1 -defaultMode TRIM -configFlag CONFIG
*****************************************************************************/
TEST_F(Gmsysview_subtree, Yang_034_002_017)
{
    int ret = 0;
    prepares();
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *ReplyJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Reply/Reply_02_17.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0;

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson_complex;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    filter.configFlag = GMC_SUBTREE_FILTER_CONFIG;
    filter.maxDepth = 1;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;

    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.视图查询");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);

    snprintf(g_gcmd, 1024, "%s/gmsysview -s %s subtree -ns NamespaceA03402 -rn %s -json '%s' -depth 1 -defaultMode TRIM -configFlag CONFIG"
                           " >./Yang_Tree_Schema/SysviewReply/Sysview_02_17.json\n", g_toolPath,
        g_connServer, "C_MIX1_N1", FilterJson_complex);
    AW_FUN_Log(LOG_INFO, "%s", g_gcmd);
    system(g_gcmd);
    memset(g_gcmd, 0, sizeof(g_gcmd));

    char *SysviewJson = NULL;
    readJanssonFile("Yang_Tree_Schema/SysviewReply/Sysview_02_17.json", &SysviewJson);
    EXPECT_NE((void *)NULL, SysviewJson);
    EXPECT_TRUE(testYangJsonIsEqual(ReplyJson, SysviewJson));
    free(SysviewJson);
    free(ReplyJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 018.MIX1类型,内容过滤 第一层起始位
 Info  : -depth 1 -defaultMode REPORT_ALL_TAGGED -configFlag CONFIG
*****************************************************************************/
TEST_F(Gmsysview_subtree, Yang_034_002_018)
{
    int ret = 0;
    prepares();
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *ReplyJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Reply/Reply_02_18.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0;

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson_complex;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED;
    filter.configFlag = GMC_SUBTREE_FILTER_CONFIG;
    filter.maxDepth = 1;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;

    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.视图查询");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);

    snprintf(g_gcmd, 1024, "%s/gmsysview -s %s subtree -ns NamespaceA03402 -rn %s -json '%s' -depth 1 -defaultMode REPORT_ALL_TAGGED"
                           " -configFlag CONFIG >./Yang_Tree_Schema/SysviewReply/Sysview_02_18.json\n", g_toolPath,
        g_connServer, "C_MIX1_N1", FilterJson_complex);
    AW_FUN_Log(LOG_INFO, "%s", g_gcmd);
    system(g_gcmd);
    memset(g_gcmd, 0, sizeof(g_gcmd));

    char *SysviewJson = NULL;
    readJanssonFile("Yang_Tree_Schema/SysviewReply/Sysview_02_18.json", &SysviewJson);
    EXPECT_NE((void *)NULL, SysviewJson);
    EXPECT_TRUE(testYangJsonIsEqual(ReplyJson, SysviewJson));
    free(SysviewJson);
    free(ReplyJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 019.MIX1类型,内容过滤 第一层起始位
 Info  : -depth 1 -defaultMode EXPLICIT -configFlag STATE
*****************************************************************************/
TEST_F(Gmsysview_subtree, Yang_034_002_019)
{
    int ret = 0;
    prepares();
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *ReplyJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Reply/Reply_02_19.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0;

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson_complex;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_EXPLICIT;
    filter.configFlag = GMC_SUBTREE_FILTER_STATE;
    filter.maxDepth = 1;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;

    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.视图查询");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);

    snprintf(g_gcmd, 1024, "%s/gmsysview -s %s subtree -ns NamespaceA03402 -rn %s -json '%s' -depth 1 -defaultMode EXPLICIT"
                           " -configFlag STATE >./Yang_Tree_Schema/SysviewReply/Sysview_02_19.json\n", g_toolPath,
        g_connServer, "C_MIX1_N1", FilterJson_complex);
    AW_FUN_Log(LOG_INFO, "%s", g_gcmd);
    system(g_gcmd);
    memset(g_gcmd, 0, sizeof(g_gcmd));

    char *SysviewJson = NULL;
    readJanssonFile("Yang_Tree_Schema/SysviewReply/Sysview_02_19.json", &SysviewJson);
    EXPECT_NE((void *)NULL, SysviewJson);
    EXPECT_TRUE(testYangJsonIsEqual(ReplyJson, SysviewJson));
    free(SysviewJson);
    free(ReplyJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 020.MIX1类型,内容过滤 第一层起始位
 Info  : -depth 1 -defaultMode TRIM -configFlag STATE
*****************************************************************************/
TEST_F(Gmsysview_subtree, Yang_034_002_020)
{
    int ret = 0;
    prepares();
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *ReplyJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Reply/Reply_02_20.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0;

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson_complex;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    filter.configFlag = GMC_SUBTREE_FILTER_STATE;
    filter.maxDepth = 1;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;

    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.视图查询");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);

    snprintf(g_gcmd, 1024, "%s/gmsysview -s %s subtree -ns NamespaceA03402 -rn %s -json '%s' -depth 1 -defaultMode TRIM -configFlag STATE"
                           " >./Yang_Tree_Schema/SysviewReply/Sysview_02_20.json\n", g_toolPath,
        g_connServer, "C_MIX1_N1", FilterJson_complex);
    AW_FUN_Log(LOG_INFO, "%s", g_gcmd);
    system(g_gcmd);
    memset(g_gcmd, 0, sizeof(g_gcmd));

    char *SysviewJson = NULL;
    readJanssonFile("Yang_Tree_Schema/SysviewReply/Sysview_02_20.json", &SysviewJson);
    EXPECT_NE((void *)NULL, SysviewJson);
    EXPECT_TRUE(testYangJsonIsEqual(ReplyJson, SysviewJson));
    free(SysviewJson);
    free(ReplyJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 022.MIX1类型,内容过滤 第一层起始位
 Info  : -depth 1 -defaultMode REPORT_ALL_TAGGED -configFlag STATE
*****************************************************************************/
TEST_F(Gmsysview_subtree, Yang_034_002_022)
{
    int ret = 0;
    prepares();
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *ReplyJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Reply/Reply_02_22.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0;

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson_complex;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED;
    filter.configFlag = GMC_SUBTREE_FILTER_STATE;
    filter.maxDepth = 1;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;

    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.视图查询");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);

    snprintf(g_gcmd, 1024, "%s/gmsysview -s %s subtree -ns NamespaceA03402 -rn %s -json '%s' -depth 1 "
        "-defaultMode REPORT_ALL_TAGGED -configFlag STATE"
        ">./Yang_Tree_Schema/SysviewReply/Sysview_02_22.json", g_toolPath,
        g_connServer, "C_MIX1_N1", FilterJson_complex);
    AW_FUN_Log(LOG_INFO, "%s", g_gcmd);
    system(g_gcmd);
    memset(g_gcmd, 0, sizeof(g_gcmd));

    char *SysviewJson = NULL;
    readJanssonFile("Yang_Tree_Schema/SysviewReply/Sysview_02_22.json", &SysviewJson);
    EXPECT_NE((void *)NULL, SysviewJson);
    EXPECT_TRUE(testYangJsonIsEqual(ReplyJson, SysviewJson));
    free(SysviewJson);
    free(ReplyJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 023.MIX1类型,内容过滤 第一层起始位
 Info  :  -depth 0 -defaultMode EXPLICIT -configFlag DEFAULT
*****************************************************************************/
TEST_F(Gmsysview_subtree, Yang_034_002_023)
{
    int ret = 0;
    prepares();
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *ReplyJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Reply/Reply_02_23.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0;

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson_complex;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_EXPLICIT;
    filter.configFlag = GMC_SUBTREE_FILTER_DEFAULT;
    filter.maxDepth = 0;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;

    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.视图查询");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);

    snprintf(g_gcmd, 1024, "%s/gmsysview -s %s subtree -ns NamespaceA03402 -rn %s -json '%s' -depth 0 -defaultMode EXPLICIT"
                           " -configFlag DEFAULT >./Yang_Tree_Schema/SysviewReply/Sysview_02_23.json\n", g_toolPath,
        g_connServer, "C_MIX1_N1", FilterJson_complex);
    AW_FUN_Log(LOG_INFO, "%s", g_gcmd);
    system(g_gcmd);
    memset(g_gcmd, 0, sizeof(g_gcmd));

    char *SysviewJson = NULL;
    readJanssonFile("Yang_Tree_Schema/SysviewReply/Sysview_02_23.json", &SysviewJson);
    EXPECT_NE((void *)NULL, SysviewJson);
    EXPECT_TRUE(testYangJsonIsEqual(ReplyJson, SysviewJson));
    free(SysviewJson);
    free(ReplyJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 024.MIX1类型,内容过滤 第一层起始位
 Info  : -depth 0 -defaultMode TRIM -configFlag DEFAULT
*****************************************************************************/
TEST_F(Gmsysview_subtree, Yang_034_002_024)
{
    int ret = 0;
    prepares();
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *ReplyJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Reply/Reply_02_24.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0;

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson_complex;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    filter.configFlag = GMC_SUBTREE_FILTER_DEFAULT;
    filter.maxDepth = 0;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;

    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.视图查询");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);

    snprintf(g_gcmd, 1024, "%s/gmsysview -s %s subtree -ns NamespaceA03402 -rn %s -json '%s' -depth 0 -defaultMode TRIM -configFlag DEFAULT"
                           " >./Yang_Tree_Schema/SysviewReply/Sysview_02_24.json\n", g_toolPath,
        g_connServer, "C_MIX1_N1", FilterJson_complex);
    AW_FUN_Log(LOG_INFO, "%s", g_gcmd);
    system(g_gcmd);
    memset(g_gcmd, 0, sizeof(g_gcmd));

    char *SysviewJson = NULL;
    readJanssonFile("Yang_Tree_Schema/SysviewReply/Sysview_02_24.json", &SysviewJson);
    EXPECT_NE((void *)NULL, SysviewJson);
    EXPECT_TRUE(testYangJsonIsEqual(ReplyJson, SysviewJson));
    free(SysviewJson);
    free(ReplyJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 025.MIX1类型,内容过滤 第一层起始位
 Info  : -depth 0 -defaultMode REPORT_ALL_TAGGED -configFlag DEFAULT
*****************************************************************************/
TEST_F(Gmsysview_subtree, Yang_034_002_025)
{
    int ret = 0;
    prepares();
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *ReplyJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Reply/Reply_02_25.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0;

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson_complex;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED;
    filter.configFlag = GMC_SUBTREE_FILTER_DEFAULT;
    filter.maxDepth = 0;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;

    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.视图查询");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);

    snprintf(g_gcmd, 1024, "%s/gmsysview -s %s subtree -ns NamespaceA03402 -rn %s -json '%s' -depth 0 -defaultMode REPORT_ALL_TAGGED"
                           " -configFlag DEFAULT >./Yang_Tree_Schema/SysviewReply/Sysview_02_25.json\n", g_toolPath,
        g_connServer, "C_MIX1_N1", FilterJson_complex);
    AW_FUN_Log(LOG_INFO, "%s", g_gcmd);
    system(g_gcmd);
    memset(g_gcmd, 0, sizeof(g_gcmd));

    char *SysviewJson = NULL;
    readJanssonFile("Yang_Tree_Schema/SysviewReply/Sysview_02_25.json", &SysviewJson);
    EXPECT_NE((void *)NULL, SysviewJson);
    EXPECT_TRUE(testYangJsonIsEqual(ReplyJson, SysviewJson));
    free(SysviewJson);
    free(ReplyJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 026.MIX1类型,内容过滤 第一层起始位
 Info  : -depth 0 -defaultMode EXPLICIT -configFlag CONFIG
*****************************************************************************/
TEST_F(Gmsysview_subtree, Yang_034_002_026)
{
    int ret = 0;
    prepares();
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *ReplyJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Reply/Reply_02_26.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0;

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson_complex;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_EXPLICIT;
    filter.configFlag = GMC_SUBTREE_FILTER_CONFIG;
    filter.maxDepth = 0;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;

    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.视图查询");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);

    snprintf(g_gcmd, 1024, "%s/gmsysview -s %s subtree -ns NamespaceA03402 -rn %s -json '%s' -depth 0 -defaultMode EXPLICIT"
                           " -configFlag CONFIG >./Yang_Tree_Schema/SysviewReply/Sysview_02_26.json\n", g_toolPath,
        g_connServer, "C_MIX1_N1", FilterJson_complex);
    AW_FUN_Log(LOG_INFO, "%s", g_gcmd);
    system(g_gcmd);
    memset(g_gcmd, 0, sizeof(g_gcmd));

    char *SysviewJson = NULL;
    readJanssonFile("Yang_Tree_Schema/SysviewReply/Sysview_02_26.json", &SysviewJson);
    EXPECT_NE((void *)NULL, SysviewJson);
    EXPECT_TRUE(testYangJsonIsEqual(ReplyJson, SysviewJson));
    free(SysviewJson);
    free(ReplyJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 027.MIX1类型,内容过滤 第一层起始位
 Info  :  -depth 0 -defaultMode TRIM -configFlag CONFIG
*****************************************************************************/
TEST_F(Gmsysview_subtree, Yang_034_002_027)
{
    int ret = 0;
    prepares();
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *ReplyJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Reply/Reply_02_27.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0;

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson_complex;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    filter.configFlag = GMC_SUBTREE_FILTER_CONFIG;
    filter.maxDepth = 0;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;

    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.视图查询");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);

    snprintf(g_gcmd, 1024, "%s/gmsysview -s %s subtree -ns NamespaceA03402 -rn %s -json '%s' -depth 0 -defaultMode TRIM -configFlag CONFIG"
                           " >./Yang_Tree_Schema/SysviewReply/Sysview_02_27.json\n", g_toolPath,
        g_connServer, "C_MIX1_N1", FilterJson_complex);
    AW_FUN_Log(LOG_INFO, "%s", g_gcmd);
    system(g_gcmd);
    memset(g_gcmd, 0, sizeof(g_gcmd));

    char *SysviewJson = NULL;
    readJanssonFile("Yang_Tree_Schema/SysviewReply/Sysview_02_27.json", &SysviewJson);
    EXPECT_NE((void *)NULL, SysviewJson);
    EXPECT_TRUE(testYangJsonIsEqual(ReplyJson, SysviewJson));
    free(SysviewJson);
    free(ReplyJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 028.MIX1类型,内容过滤 第一层起始位
 Info  : -depth 0 -defaultMode REPORT_ALL_TAGGED -configFlag CONFIG
*****************************************************************************/
TEST_F(Gmsysview_subtree, Yang_034_002_028)
{
    int ret = 0;
    prepares();
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *ReplyJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Reply/Reply_02_28.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0;

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson_complex;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED;
    filter.configFlag = GMC_SUBTREE_FILTER_CONFIG;
    filter.maxDepth = 0;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;

    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.视图查询");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);

    snprintf(g_gcmd, 1024, "%s/gmsysview -s %s subtree -ns NamespaceA03402 -rn %s -json '%s' -depth 0 -defaultMode REPORT_ALL_TAGGED"
                           " -configFlag CONFIG >./Yang_Tree_Schema/SysviewReply/Sysview_02_28.json\n", g_toolPath,
        g_connServer, "C_MIX1_N1", FilterJson_complex);
    AW_FUN_Log(LOG_INFO, "%s", g_gcmd);
    system(g_gcmd);
    memset(g_gcmd, 0, sizeof(g_gcmd));

    char *SysviewJson = NULL;
    readJanssonFile("Yang_Tree_Schema/SysviewReply/Sysview_02_28.json", &SysviewJson);
    EXPECT_NE((void *)NULL, SysviewJson);
    EXPECT_TRUE(testYangJsonIsEqual(ReplyJson, SysviewJson));
    free(SysviewJson);
    free(ReplyJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 029.MIX1类型,内容过滤 第一层起始位
 Info  : -depth 0 -defaultMode EXPLICIT -configFlag STATE
*****************************************************************************/
TEST_F(Gmsysview_subtree, Yang_034_002_029)
{
    int ret = 0;
    prepares();
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *ReplyJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Reply/Reply_02_29.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0;

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson_complex;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_EXPLICIT;
    filter.configFlag = GMC_SUBTREE_FILTER_STATE;
    filter.maxDepth = 0;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;

    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.视图查询");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);

    snprintf(g_gcmd, 1024, "%s/gmsysview -s %s subtree -ns NamespaceA03402 -rn %s -json '%s' -depth 0 -defaultMode EXPLICIT"
                           " -configFlag STATE >./Yang_Tree_Schema/SysviewReply/Sysview_02_29.json\n", g_toolPath,
        g_connServer, "C_MIX1_N1", FilterJson_complex);
    AW_FUN_Log(LOG_INFO, "%s", g_gcmd);
    system(g_gcmd);
    memset(g_gcmd, 0, sizeof(g_gcmd));

    char *SysviewJson = NULL;
    readJanssonFile("Yang_Tree_Schema/SysviewReply/Sysview_02_29.json", &SysviewJson);
    EXPECT_NE((void *)NULL, SysviewJson);
    EXPECT_TRUE(testYangJsonIsEqual(ReplyJson, SysviewJson));
    free(SysviewJson);
    free(ReplyJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 030.MIX1类型,内容过滤 第一层起始位
 Info  : -depth 0 -defaultMode TRIM -configFlag STATE
*****************************************************************************/
TEST_F(Gmsysview_subtree, Yang_034_002_030)
{
    int ret = 0;
    prepares();
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *ReplyJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Reply/Reply_02_30.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0;

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson_complex;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    filter.configFlag = GMC_SUBTREE_FILTER_STATE;
    filter.maxDepth = 0;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;

    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.视图查询");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);

    snprintf(g_gcmd, 1024, "%s/gmsysview -s %s subtree -ns NamespaceA03402 -rn %s -json '%s' -depth 0 -defaultMode TRIM -configFlag STATE"
                           " >./Yang_Tree_Schema/SysviewReply/Sysview_02_30.json\n", g_toolPath,
        g_connServer, "C_MIX1_N1", FilterJson_complex);
    AW_FUN_Log(LOG_INFO, "%s", g_gcmd);
    system(g_gcmd);
    memset(g_gcmd, 0, sizeof(g_gcmd));

    char *SysviewJson = NULL;
    readJanssonFile("Yang_Tree_Schema/SysviewReply/Sysview_02_30.json", &SysviewJson);
    EXPECT_NE((void *)NULL, SysviewJson);
    EXPECT_TRUE(testYangJsonIsEqual(ReplyJson, SysviewJson));
    free(SysviewJson);
    free(ReplyJson);
    // 事务提交
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 031.MIX1类型,内容过滤 第一层起始位
 Info  : -depth 0 -defaultMode REPORT_ALL_TAGGED -configFlag STATE
*****************************************************************************/
TEST_F(Gmsysview_subtree, Yang_034_002_031)
{
    int ret = 0;
    prepares();
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *ReplyJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Reply/Reply_02_31.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0;

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson_complex;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED;
    filter.configFlag = GMC_SUBTREE_FILTER_STATE;
    filter.maxDepth = 0;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;

    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.视图查询");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);

    snprintf(g_gcmd, 1024, "%s/gmsysview -s %s subtree -ns NamespaceA03402 -rn %s -json '%s' -depth 0 -defaultMode REPORT_ALL_TAGGED"
                           " -configFlag STATE >./Yang_Tree_Schema/SysviewReply/Sysview_02_31.json\n", g_toolPath,
        g_connServer, "C_MIX1_N1", FilterJson_complex);
    AW_FUN_Log(LOG_INFO, "%s", g_gcmd);
    system(g_gcmd);
    memset(g_gcmd, 0, sizeof(g_gcmd));

    char *SysviewJson = NULL;
    readJanssonFile("Yang_Tree_Schema/SysviewReply/Sysview_02_31.json", &SysviewJson);
    EXPECT_NE((void *)NULL, SysviewJson);
    EXPECT_TRUE(testYangJsonIsEqual(ReplyJson, SysviewJson));
    free(SysviewJson);
    free(ReplyJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 032.MIX1类型,内容过滤 第一层起始位
 Info  : 其他默认传入filterjson超长
*****************************************************************************/
TEST_F(Gmsysview_subtree, Yang_034_002_032)
{
    char g_errorCode01[1024] = {0};
    char g_errorCode02[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_INVALID_JSON_CONTENT);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char strLong[65536];
    memset(strLong, 'a', 65534);
    char strLongJson[65600];
    char g_gcmd32[102400];
    snprintf(strLongJson, 65600, "{\"F6\":%s}", strLong);
    int ret = 0;
    prepares();
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *ReplyJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Reply/Reply_02_32.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0;

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = strLongJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED;
    filter.configFlag = GMC_SUBTREE_FILTER_STATE;
    filter.maxDepth = 0;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;

    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.视图查询");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data, 1, 520);
    sleep(10);
    EXPECT_EQ(1004006, Data.expectStatus);

    snprintf(g_gcmd32, 75536, "%s/gmsysview -s %s subtree -ns NamespaceA03402 -rn %s -json '%s' -depth 0 -defaultMode REPORT_ALL_TAGGED"
                              " -configFlag STATE >./Yang_Tree_Schema/SysviewReply/Sysview_02_32.json\n", g_toolPath,
        g_connServer, "C_MIX1_N1", strLongJson);
    AW_FUN_Log(LOG_INFO, "%s", g_gcmd32);
    system(g_gcmd32);
    memset(g_gcmd32, 0, sizeof(g_gcmd32));

    ret = executeCommand((char *)"grep -r 'unsound' Yang_Tree_Schema/SysviewReply/Sysview_02_32.json",
                         (char *)"The parameter of option(\"-json\") is overflow(longer) than the length((1, 4096))");
    EXPECT_EQ(GMERR_OK, ret);
    free(ReplyJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_TRUNCATION_WHITE_LIST(1, "GMERR-1004006, Incorrect json content");
    AW_FUN_Log(LOG_STEP, "test end.");
}


/*****************************************************************************
 Description  : 033.MIX1类型,内容过滤 第一层起始位
 Info  : 其他默认 RootName为不存在
*****************************************************************************/
TEST_F(Gmsysview_subtree, Yang_034_002_033)
{
    int ret = 0;
    prepares();
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *ReplyJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Reply/Reply_02_33.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0;

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson_complex;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED;
    filter.configFlag = GMC_SUBTREE_FILTER_STATE;
    filter.maxDepth = 0;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;

    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.视图查询");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data, 1, 3000);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);

    snprintf(g_gcmd, 1024, "%s/gmsysview -s %s subtree -ns NamespaceA03402 -rn %s -json '%s' -depth 0 -defaultMode REPORT_ALL_TAGGED"
                           " -configFlag STATE"
                           " >./Yang_Tree_Schema/SysviewReply/Sysview_02_33.json", g_toolPath,
        g_connServer, "SubT0Con_8_CXXX", FilterJson_complex);
    AW_FUN_Log(LOG_INFO, "%s", g_gcmd);
    system(g_gcmd);
    memset(g_gcmd, 0, sizeof(g_gcmd));

    ret = executeCommand((char *)"grep -r '9010' Yang_Tree_Schema/SysviewReply/Sysview_02_33.json",
                         (char *)"Unable to execute subtree filter");
    EXPECT_EQ(GMERR_OK, ret);
    free(ReplyJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 034.MIX1类型,内容过滤 第一层起始位
 Info  : 其他默认 RootName非法
*****************************************************************************/
TEST_F(Gmsysview_subtree, Yang_034_002_034)
{
    int ret = 0;
    prepares();
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *ReplyJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Reply/Reply_02_34.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0;

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson_complex;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED;
    filter.configFlag = GMC_SUBTREE_FILTER_STATE;
    filter.maxDepth = 0;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;

    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.视图查询");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data, 1, 3000);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);

    snprintf(g_gcmd, 1024, "%s/gmsysview -s %s subtree -ns NamespaceA03402 -rn %s -json '%s'"
                            " -depth 0 -defaultMode REPORT_ALL_TAGGED -configFlag STATE"
                            " >./Yang_Tree_Schema/SysviewReply/Sysview_02_34.json", g_toolPath,
        g_connServer, "!!", FilterJson_complex);
    AW_FUN_Log(LOG_INFO, "%s", g_gcmd);
    system(g_gcmd);
    memset(g_gcmd, 0, sizeof(g_gcmd));

    ret = executeCommand((char *)"grep -r '9010' Yang_Tree_Schema/SysviewReply/Sysview_02_34.json",
                         (char *)"Unable to execute subtree filter");
    EXPECT_EQ(GMERR_OK, ret);
    free(ReplyJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

const char *FilterJson_error=
    R"({{{{{{{
    "C_MIX1_N2":
    [
        {
            "C_MIX1_N3":
                {
                    "C_MIX1_N4":
                    {
                        "F1": 100,
                        "F3": 3.141
                    }
                }
        }
    ]
}}}}}}}}})";
/*****************************************************************************
 Description  : 035.MIX1类型,内容过滤 第一层起始位
 Info  : subtreejson内容非法
*****************************************************************************/
TEST_F(Gmsysview_subtree, Yang_034_002_035)
{
    char g_errorCode01[1024] = {0};
    char g_errorCode02[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_INVALID_JSON_CONTENT);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    int ret = 0;
    prepares();
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *ReplyJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Reply/Reply_02_35.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0;

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson_error;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED;
    filter.configFlag = GMC_SUBTREE_FILTER_STATE;
    filter.maxDepth = 0;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;

    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.视图查询");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data, 1, 3000);
    ASSERT_EQ(1004006, Data.expectStatus);

    snprintf(g_gcmd, 1024, "%s/gmsysview -s %s subtree -ns NamespaceA03402 -rn %s -json '%s'"
                           " >./Yang_Tree_Schema/SysviewReply/Sysview_02_35.json\n", g_toolPath,
        g_connServer, "C_MIX1_N1", FilterJson_error);
    AW_FUN_Log(LOG_INFO, "%s", g_gcmd);
    system(g_gcmd);
    memset(g_gcmd, 0, sizeof(g_gcmd));

    ret = executeCommand((char *)"grep -r 'Incorrect' Yang_Tree_Schema/SysviewReply/Sysview_02_35.json",
                         (char *)"ErrorCodeDescription: Incorrect json content");
    EXPECT_EQ(GMERR_OK, ret);
    free(ReplyJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
const char *FilterJson_null=
    R"({
    "C_MIX1_N2":
    [
        {
            "C_MIX1_N3":
                {
                    "C_MIX1_N4":
                    {
                        "F1": 1010,
                        "F3": 3.141
                    }
                }
        }
    ]
})";
/*****************************************************************************
 Description  : 036.MIX1类型,内容过滤 第一层起始位
 Info  : subtreejson内容正常，但查询数据为空
*****************************************************************************/
TEST_F(Gmsysview_subtree, Yang_034_002_036)
{
    int ret = 0;
    prepares();
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *ReplyJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Reply/Reply_02_36.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0;

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson_null;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED;
    filter.configFlag = GMC_SUBTREE_FILTER_STATE;
    filter.maxDepth = 0;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;

    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.视图查询");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);

    snprintf(g_gcmd, 1024, "%s/gmsysview -s %s subtree -ns NamespaceA03402 -rn %s -json '%s' -depth 0 -defaultMode REPORT_ALL_TAGGED"
                           " -configFlag STATE >./Yang_Tree_Schema/SysviewReply/Sysview_02_36.json\n", g_toolPath,
        g_connServer, "C_MIX1_N1", FilterJson_null);
    AW_FUN_Log(LOG_INFO, "%s", g_gcmd);
    system(g_gcmd);
    memset(g_gcmd, 0, sizeof(g_gcmd));

    char *SysviewJson = NULL;
    readJanssonFile("Yang_Tree_Schema/SysviewReply/Sysview_02_36.json", &SysviewJson);
    EXPECT_NE((void *)NULL, SysviewJson);
    EXPECT_TRUE(testYangJsonIsEqual(ReplyJson, SysviewJson));
    free(SysviewJson);
    free(ReplyJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 037.MIX1类型,内容过滤 第一层起始位
 Info  : -depth 0 -defaultMode XXX(参数非法) -configFlag STATE
*****************************************************************************/
TEST_F(Gmsysview_subtree, Yang_034_002_037)
{
    int ret = 0;
    prepares();
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *ReplyJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Reply/Reply_02_37.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0;

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson_null;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED;
    filter.configFlag = GMC_SUBTREE_FILTER_STATE;
    filter.maxDepth = 0;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;

    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.视图查询");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);

    snprintf(g_gcmd, 1024, "%s/gmsysview -s %s subtree -ns NamespaceA03402 -rn %s -json '%s' -depth 0 -defaultMode 666 -configFlag STATE"
                           " >./Yang_Tree_Schema/SysviewReply/Sysview_02_37.json", g_toolPath,
        g_connServer, "C_MIX1_N1", FilterJson_null);
    AW_FUN_Log(LOG_INFO, "%s", g_gcmd);
    system(g_gcmd);
    memset(g_gcmd, 0, sizeof(g_gcmd));

    ret = executeCommand((char *)"grep -r 'WARN' Yang_Tree_Schema/SysviewReply/Sysview_02_37.json",
                         (char *)"for subtree default mode, param: 666.");
    EXPECT_EQ(GMERR_OK, ret);
    free(ReplyJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 038.MIX1类型,内容过滤 第一层起始位
 Info  : -depth 0 -defaultMode XXX(参数正常) -configFlag YYY(参数非法)
*****************************************************************************/
TEST_F(Gmsysview_subtree, Yang_034_002_038)
{
    int ret = 0;
    prepares();
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *ReplyJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Reply/Reply_02_38.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0;

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson_null;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_EXPLICIT;
    filter.configFlag = GMC_SUBTREE_FILTER_STATE;
    filter.maxDepth = 0;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;

    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.视图查询");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);

    snprintf(g_gcmd, 1024, "%s/gmsysview -s %s subtree -ns NamespaceA03402 -rn %s -json '%s' -depth 0 -defaultMode EXPLICIT"
                           " -configFlag STATE >./Yang_Tree_Schema/SysviewReply/Sysview_02_38.json\n", g_toolPath,
        g_connServer, "C_MIX1_N1", FilterJson_null);
    AW_FUN_Log(LOG_INFO, "%s", g_gcmd);
    system(g_gcmd);
    memset(g_gcmd, 0, sizeof(g_gcmd));

    char *SysviewJson = NULL;
    readJanssonFile("Yang_Tree_Schema/SysviewReply/Sysview_02_38.json", &SysviewJson);
    EXPECT_NE((void *)NULL, SysviewJson);
    EXPECT_TRUE(testYangJsonIsEqual(ReplyJson, SysviewJson));
    free(SysviewJson);
    free(ReplyJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 039.MIX1类型,内容过滤 第一层起始位
 Info  : subtreejson(含有多个换行)
*****************************************************************************/
TEST_F(Gmsysview_subtree, Yang_034_002_039)
{
    int ret = 0;
    char g_errorCode01[1024] = {0};
    char g_errorCode02[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_INVALID_JSON_CONTENT);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    prepares();
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *ReplyJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Reply/Reply_02_39.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0;

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson_null;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_EXPLICIT;
    filter.configFlag = GMC_SUBTREE_FILTER_STATE;
    filter.maxDepth = 0;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;

    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.视图查询");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);

    snprintf(g_gcmd3, 1677, "%s/gmsysview -s %s subtree -ns NamespaceA03402 -rn %s -json '%s' -depth 0"
                           " -defaultMode EXPLICIT -configFlag STATE"
                           " >./Yang_Tree_Schema/SysviewReply/Sysview_02_39.json", g_toolPath,
        g_connServer, "C_MIX1_N1", "\"F\n3\":\n0\n");
    AW_FUN_Log(LOG_INFO, "%s", g_gcmd3);
    system(g_gcmd3);
    memset(g_gcmd3, 0, sizeof(g_gcmd3));

    ret = executeCommand((char *)"grep -r 'ErrorCodeDescription' Yang_Tree_Schema/SysviewReply/Sysview_02_39.json",
                         (char *)"Incorrect json content");
    EXPECT_EQ(GMERR_OK, ret);
    free(ReplyJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 040.MIX1类型,内容过滤 第一层起始位
 Info  : -depth 字母 -defaultMode XXX -configFlag STATE
*****************************************************************************/
TEST_F(Gmsysview_subtree, Yang_034_002_040)
{
    int ret = 0;
    prepares();
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *ReplyJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Reply/Reply_02_40.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0;

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson_complex;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_EXPLICIT;
    filter.configFlag = GMC_SUBTREE_FILTER_STATE;
    filter.maxDepth = 0;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;

    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.视图查询");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);

    snprintf(g_gcmd, 1024, "%s/gmsysview -s %s subtree -ns NamespaceA03402 -rn %s -json '%s' -depth YHL"
                           " -defaultMode EXPLICIT -configFlag STATE"
                           "  >./Yang_Tree_Schema/SysviewReply/Sysview_02_40.json", g_toolPath,
        g_connServer, "C_MIX1_N1", FilterJson_complex);
    AW_FUN_Log(LOG_INFO, "%s", g_gcmd);
    system(g_gcmd);
    memset(g_gcmd, 0, sizeof(g_gcmd));
    system("cat ./Yang_Tree_Schema/SysviewReply/Sysview_02_40.json");
    ret = executeCommand((char *)"grep -r 'WARN' Yang_Tree_Schema/SysviewReply/Sysview_02_40.json",
                         (char *)"Param of -depth should be [1, 255]");
    EXPECT_EQ(GMERR_OK, ret);
    free(ReplyJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 041.MIX1类型,内容过滤 第一层起始位
 Info  : -depth 0 -defaultMode XXX -configFlag STATECONFIG
*****************************************************************************/
TEST_F(Gmsysview_subtree, Yang_034_002_041)
{
    int ret = 0;
    prepares();
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *ReplyJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Reply/Reply_02_41.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0;

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson_complex;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_EXPLICIT;
    filter.configFlag = GMC_SUBTREE_FILTER_CONFIG;
    filter.maxDepth = 0;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;

    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.视图查询");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);

    snprintf(g_gcmd, 1024, "%s/gmsysview -s %s subtree -ns NamespaceA03402 -rn %s -json '%s' -depth 0"
                           " -defaultMode EXPLICIT -configFlag STATECONFIG"
                            " >./Yang_Tree_Schema/SysviewReply/Sysview_02_41.json", g_toolPath,
        g_connServer, "C_MIX1_N1", FilterJson_complex);
    AW_FUN_Log(LOG_INFO, "%s", g_gcmd);
    system(g_gcmd);
    memset(g_gcmd, 0, sizeof(g_gcmd));
    ret = executeCommand((char *)"grep -r 'unsound' Yang_Tree_Schema/SysviewReply/Sysview_02_41.json",
                         (char *)"The parameter of option(\"-configFlag\") is overflow(longer) than the length((1, 8))");
    EXPECT_EQ(GMERR_OK, ret);
    free(ReplyJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 042.MIX1类型,内容过滤 第一层起始位
 Info  : -depth 0 -defaultMode TRIMEXPLICIT -configFlag STATECONFIG
*****************************************************************************/
TEST_F(Gmsysview_subtree, Yang_034_002_042)
{
    int ret = 0;
    prepares();
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *ReplyJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Reply/Reply_02_42.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0;

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson_complex;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED;
    filter.configFlag = GMC_SUBTREE_FILTER_CONFIG;
    filter.maxDepth = 0;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;

    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.视图查询");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);

    snprintf(g_gcmd, 1024, "%s/gmsysview -s %s subtree -ns NamespaceA03402 -rn %s -json '%s' -depth 0"
                           " -defaultMode TRIMEXPLICIT -configFlag STATECONFIG"
                           " >./Yang_Tree_Schema/SysviewReply/Sysview_02_42.json", g_toolPath,
        g_connServer, "C_MIX1_N1", FilterJson_complex);
    AW_FUN_Log(LOG_INFO, "%s", g_gcmd);
    system(g_gcmd);
    memset(g_gcmd, 0, sizeof(g_gcmd));
    ret = executeCommand((char *)"grep -r 'ErrorCodeDescription' Yang_Tree_Schema/SysviewReply/Sysview_02_42.json",
                         (char *)"ErrorCodeDescription: Syntax unsucc");
    EXPECT_EQ(GMERR_OK, ret);
    free(ReplyJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 043.MIX1类型,内容过滤 第一层起始位
 Info  : -depth 0 -defaultMode XXX -configFlag 0
*****************************************************************************/
TEST_F(Gmsysview_subtree, Yang_034_002_043)
{
    int ret = 0;
    prepares();
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *ReplyJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Reply/Reply_02_43.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0;

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson_complex;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    filter.configFlag = GMC_SUBTREE_FILTER_CONFIG;
    filter.maxDepth = 0;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;

    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.视图查询");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);

    snprintf(g_gcmd, 1024, "%s/gmsysview -s %s subtree -ns NamespaceA03402 -rn %s -json '%s' -depth 0 -defaultMode TRIM -configFlag 0"
                           " >./Yang_Tree_Schema/SysviewReply/Sysview_02_43.json", g_toolPath,
        g_connServer, "C_MIX1_N1", FilterJson_complex);
    AW_FUN_Log(LOG_INFO, "%s", g_gcmd);
    system(g_gcmd);
    memset(g_gcmd, 0, sizeof(g_gcmd));

    ret = executeCommand((char *)"grep -r 'WARN' Yang_Tree_Schema/SysviewReply/Sysview_02_43.json",
                         (char *)"Inv param for subtree cfg flag, param: 0");
    EXPECT_EQ(GMERR_OK, ret);
    free(ReplyJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 044.MIX1类型,内容过滤 第一层起始位
 Info  : -depth 0 -defaultMode 1 -configFlag YYY
*****************************************************************************/
TEST_F(Gmsysview_subtree, Yang_034_002_044)
{
    int ret = 0;
    prepares();
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *ReplyJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Reply/Reply_02_44.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0;

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson_complex;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED;
    filter.configFlag = GMC_SUBTREE_FILTER_STATE;
    filter.maxDepth = 0;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;

    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.视图查询");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);

    snprintf(g_gcmd, 1024, "%s/gmsysview -s %s subtree -ns NamespaceA03402 -rn %s -json '%s'"
                           " -depth 0 -defaultMode 1 -configFlag STATE"
                           " >./Yang_Tree_Schema/SysviewReply/Sysview_02_44.json", g_toolPath,
        g_connServer, "C_MIX1_N1", FilterJson_complex);
    AW_FUN_Log(LOG_INFO, "%s", g_gcmd);
    system(g_gcmd);
    memset(g_gcmd, 0, sizeof(g_gcmd));

    ret = executeCommand((char *)"grep -r 'ErrorCodeDescription' Yang_Tree_Schema/SysviewReply/Sysview_02_44.json",
                         (char *)"Not normal parameter value");
    EXPECT_EQ(GMERR_OK, ret);
    free(ReplyJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 045.MIX1类型,内容过滤 第一层起始位
 Info  : -depth  -defaultMode  -config
*****************************************************************************/
TEST_F(Gmsysview_subtree, Yang_034_002_045)
{
    int ret = 0;
    prepares();
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *ReplyJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Reply/Reply_02_45.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0;

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson_complex;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED;
    filter.configFlag = GMC_SUBTREE_FILTER_CONFIG;
    filter.maxDepth = 0;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;

    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.视图查询");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);

    snprintf(g_gcmd, 1024, "%s/gmsysview -s %s subtree -ns NamespaceA03402 -rn %s -json '%s'"
                            " -depth  -defaultMode  -configFlag "
                            " >./Yang_Tree_Schema/SysviewReply/Sysview_02_45.json", g_toolPath,
        g_connServer, "C_MIX1_N1", FilterJson_complex);
    AW_FUN_Log(LOG_INFO, "%s", g_gcmd);
    system(g_gcmd);
    memset(g_gcmd, 0, sizeof(g_gcmd));

    ret = executeCommand((char *)"grep -r 'ErrorCodeDescription' Yang_Tree_Schema/SysviewReply/Sysview_02_45.json",
                         (char *)"Not normal parameter value");
    EXPECT_EQ(GMERR_OK, ret);
    free(ReplyJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
void GetViewFieldResultFilter(const char *viewName, const char *fieldName, const char *filter, char *result, int resLen)
{
    char command[1024];
    snprintf(command, 1024, "gmsysview -s %s -q %s -f %s |grep -E '%s' |awk -F '[:,]' '{print $2}'",
        g_connServer, viewName, filter, fieldName);
    AW_FUN_Log(LOG_INFO, "%s\n", command);

    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        printf("popen(%s) error./n", command);
    }
    EXPECT_NE((void *)NULL, pf);
    while (NULL != fgets(result, resLen, pf))
        ;
    (void)pclose(pf);
}
/*****************************************************************************
 Description  : 046.MIX1类型,内容过滤 第一层起始位
 Info  : 视图V$CATA_VERTEX_LABEL_INFO查询多表的VERTEX_TYPE_YANG
*****************************************************************************/
TEST_F(Gmsysview_subtree, Yang_034_002_046)
{
    int ret = 0;
    prepares();
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *ReplyJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Reply/Reply_02_46.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0;

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson_complex;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED;
    filter.configFlag = GMC_SUBTREE_FILTER_CONFIG;
    filter.maxDepth = 0;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;

    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.视图查询");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);

    snprintf(g_gcmd, 1024, "%s/gmsysview -s %s -q V\\$CATA_VERTEX_LABEL_INFO \n",
             g_toolPath, g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", g_gcmd);
    system(g_gcmd);
    memset(g_gcmd, 0, sizeof(g_gcmd));

    char names2[128];
    snprintf(names2, 128, "VERTEX_LABEL_NAME=\'C_MIX1_N2\'");
    sleep(10);

    char cmdOutput2[64] = {0};
    (void)memset_s(cmdOutput2, sizeof(cmdOutput2), 0, sizeof(cmdOutput2));
    GetViewFieldResultFilter("V\\$CATA_VERTEX_LABEL_INFO", "VERTEX_LABEL_TYPE", names2, cmdOutput2, 64);
    int length = strlen(cmdOutput2);
    cmdOutput2[length - 1] = '\0';
    EXPECT_STREQ(cmdOutput2, " VERTEX_TYPE_YANG");
    free(ReplyJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

void *client_thread_047(void *args)
{
    int number = *((int*)args);
    GmcStmtT *g_stmt_async_s;
    GmcConnT *g_conn_async_s;
    int32_t ret = 0;
    ret = testGmcConnect(&g_conn_async_s, &g_stmt_async_s, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    char *ReturnJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Reply/Reply_02_47.json", &ReturnJson);
    EXPECT_NE((void *)NULL, ReturnJson);
    snprintf(g_gcmd, 1024, "%s/gmsysview -s %s subtree -ns NamespaceA03402 -rn %s -json '%s' -depth 0"
                           " -defaultMode REPORT_ALL_TAGGED"
                           " >./Yang_Tree_Schema/SysviewReply/Sysview_02_47.json", g_toolPath,
        g_connServer, "C_MIX1_N1", FilterJson_04);
    system(g_gcmd);
    memset(g_gcmd, 0, sizeof(g_gcmd));

    char *SysviewJson = NULL;
    readJanssonFile("Yang_Tree_Schema/SysviewReply/Sysview_02_47.json", &SysviewJson);
    EXPECT_NE((void *)NULL, SysviewJson);
    EXPECT_TRUE(testYangJsonIsEqual(ReturnJson, SysviewJson));
    free(SysviewJson);
    free(ReturnJson);
}
/*****************************************************************************
 Description  : 047.MIX1类型,内容过滤 第一层起始位
 Info  : 64线程并发调用工具查询subtree
*****************************************************************************/
TEST_F(Gmsysview_subtree, Yang_034_002_047)
{
    int ret = 0;
    prepares();
    AW_FUN_Log(LOG_STEP, "步骤1.插入数据");
    pthread_t client_thr_01, client_thr_02;
    int numss = 0;
    if (g_envType == 2) {
        numss = 8;
    } else {
        numss = 64;
    }
    void *thr_ret[65] = {0};
    int index[65];
    for (int i = 0; i < numss; i++) {
        index[i] = i;
        ret = pthread_create(&client_thr_01, NULL, client_thread_047, (void *)&index[i]);
        ASSERT_EQ(GMERR_OK, ret);
        pthread_join(client_thr_01, &thr_ret[1]);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 048.MIX1类型,内容过滤 第一层起始位
 Info  : gmsysview subtree A subtreejson -depth X(参数正常) -defaultMode Y(参数正常) -configFlag Z(参数正常)【-d,-mode,-c和A subtreejson顺序随意对调】
*****************************************************************************/
TEST_F(Gmsysview_subtree, Yang_034_002_048)
{
    AW_FUN_Log(LOG_STEP, "步骤1.插入数据");
    char *ReplyJson = NULL;
    readJanssonFile("Yang_Tree_Schema/FilterReply/Reply/Reply_02_48.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    int ret = 0;
    prepares();
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    snprintf(g_gcmd, 1024, "%s/gmsysview -s %s subtree -ns NamespaceA03402 -rn %s -depth 0 -json '%s'"
                           " -configFlag CONFIG -defaultMode REPORT_ALL_TAGGED"
                           " >./Yang_Tree_Schema/SysviewReply/Sysview_02_48.json",
                           g_toolPath, g_connServer, "C_MIX1_N1", FilterJson_complex);
    AW_FUN_Log(LOG_INFO, "%s", g_gcmd);
    system(g_gcmd);
    memset(g_gcmd, 0, sizeof(g_gcmd));

    char *SysviewJson = NULL;
    readJanssonFile("Yang_Tree_Schema/SysviewReply/Sysview_02_48.json", &SysviewJson);
    EXPECT_NE((void *)NULL, SysviewJson);
    AW_FUN_Log(LOG_INFO, "%s", SysviewJson);
    ASSERT_TRUE(testYangJsonIsEqual(ReplyJson, SysviewJson));
    free(SysviewJson);
    free(ReplyJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
