/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: SupW3cScene.cpp
 * Description: 支持YANG值校验支持w3c标准的正则表达业务正则表达式测试
 * Author: ya<PERSON><PERSON><PERSON> ywx1060383
 * Create: 2024-07-26
 */

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "SupW3c.h"

class SupW3cScene : public testing::Test {
public:
    static void SetUpTestCase()
    {
#ifdef FEATURE_PERSISTENCE
        system("mkdir -p /data/gmdb/; rm -rf /data/gmdb/*");
#endif
        system("sh $TEST_HOME/tools/start.sh ");
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    };

    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
    };
    virtual void SetUp();
    virtual void TearDown();
};

void SupW3cScene::SetUp()
{
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    g_mSTrxConfig.readOnly = false;

    int ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_leaflist);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置namespace级别为：可重复读 + 乐观事务
    AsyncUserDataT userData = {0};
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = g_namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_INVALID_PROPERTY);
}

void SupW3cScene::TearDown()
{
    AW_CHECK_LOG_END();
    AsyncUserDataT userData = {0};
    int ret = GmcClearNamespaceAsync(g_stmt_async, g_namespace1, ClearNSCallbak, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcDropNamespaceAsync(g_stmt_async, g_namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    GmcFreeStmt(g_stmt_list);
    GmcFreeStmt(g_stmt_leaflist);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 062.pattern字符串"[ -~]*"，先设置不符合数据，再设置符合数据
TEST_F(SupW3cScene, Yang_092_062)
{
    AW_FUN_Log(LOG_STEP, "创建Yang表，开启事务");
    int ret = 0;
    AsyncUserDataT data = {0};
    char vertexPath[] = "./schemaFile/Yang_092_062.gmjson";
    char edgePath[] = "./schemaFile/edgeLabel1.gmjson";
    TestCreateLabel(g_stmt_async, vertexPath, edgePath);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_labelNameRoot, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置符合数据");
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("abcXYZ"), strlen("abcXYZ"), "F0",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("  --~~"), strlen("  --~~"), "F27",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("+-*/"), strlen("+-*/"), "F28", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("   "), strlen("   "), "F29", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("---"), strlen("---"), "F30", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("~~~"), strlen("~~~"), "F31", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 063.pattern字符串"([^?]*)"，先设置不符合数据，再设置符合数据
TEST_F(SupW3cScene, Yang_092_063)
{
    AW_FUN_Log(LOG_STEP, "创建Yang表，开启事务");
    int ret = 0;
    AsyncUserDataT data = {0};
    char vertexPath[] = "./schemaFile/Yang_092_063.gmjson";
    char edgePath[] = "./schemaFile/edgeLabel1.gmjson";
    TestCreateLabel(g_stmt_async, vertexPath, edgePath);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_labelNameRoot, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置不符合数据");
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("?"), strlen("?"), "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("abcXYZ?"), strlen("abcXYZ?"), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置符合数据");
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("abcXYZ"), strlen("abcXYZ"), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("^^^"), strlen("^^^"), "F27", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("+-*/"), strlen("+-*/"), "F28", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("^^^"), strlen("^^^"), "F29", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("~!@#$%^&*()<>,./;:'[]{}"),
        strlen("~!@#$%^&*()<>,./;:'[]{}"), "F30", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("0123456789"), strlen("0123456789"),
        "F31", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 064.pattern字符串"\\*|[^\\*].*"，先设置不符合数据，再设置符合数据
TEST_F(SupW3cScene, Yang_092_064)
{
    AW_FUN_Log(LOG_STEP, "创建Yang表，开启事务");
    int ret = 0;
    AsyncUserDataT data = {0};
    char vertexPath[] = "./schemaFile/Yang_092_064.gmjson";
    char edgePath[] = "./schemaFile/edgeLabel1.gmjson";
    TestCreateLabel(g_stmt_async, vertexPath, edgePath);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_labelNameRoot, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置不符合数据");
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("**"), strlen("**"), "F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("*a"), strlen("*a"), "F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置符合数据");
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("*"), strlen("*"), "F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("a*"), strlen("a*"), "F27", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("\\*"), strlen("\\*"), "F28", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("0\n"), strlen("0\n"), "F29", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("!a\n"), strlen("!a\n"), "F30", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("+\\r"), strlen("+\\r"), "F31", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("ab9\\r"), strlen("ab9\\r"), "F32",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 065.pattern字符串"$0$.*|$1$[a-zA-Z0-9./]{1,8}$[a-zA-Z0-9./]{22}|$5$(rounds=\\d+$)?[a-zA-Z0-9./]{1,16}$
// [a-zA-Z0-9./]{43}|$6$(rounds=\\d+$)?[a-zA-Z0-9./]{1,16}$[a-zA-Z0-9./]{86}",
// "$0$.*|$1$[a-zA-Z0-9./]{1,8}$[a-zA-Z0-9./]{22}|$5$(rounds=\\d+$)?[a-zA-Z0-9./]{1,16}$[a-zA-Z0-9./]{43}|$6$
// (rounds=\\d+$)?[a-zA-Z0-9./]{1,16}$[a-zA-Z0-9./]{86}|$3b$.*"，先设置不符合数据，再设置符合数据
TEST_F(SupW3cScene, Yang_092_065)
{
    AW_FUN_Log(LOG_STEP, "创建Yang表，开启事务");
    int ret = 0;
    AsyncUserDataT data = {0};
    char vertexPath[] = "./schemaFile/Yang_092_065.gmjson";
    char edgePath[] = "./schemaFile/edgeLabel1.gmjson";
    TestCreateLabel(g_stmt_async, vertexPath, edgePath);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_labelNameRoot, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置不符合数据");
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("$1$"), strlen("$1$"), "F3", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("$1$-"), strlen("$1$-"), "F3", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("$1$123456789"), strlen("$1$123456789"), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("$1$0$abcdefghij0123456789a"),
        strlen("$1$0$abcdefghij0123456789a"), "F27", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("$1$$abcdefghij0123456789a"),
        strlen("$1$$abcdefghij0123456789a"), "F27", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("$1$123456789$abcdefghij0123456789a"),
        strlen("$1$123456789$abcdefghij0123456789a"), "F27", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("$1$0$abcdefghij0123456789abc"),
        strlen("$1$0$abcdefghij0123456789abc"), "F27", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING,
        (char *)("$4$0$abcdefghij012345678901234567890123456789123"),
        strlen("$4$0$abcdefghij012345678901234567890123456789123"), "F27", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING,
        (char *)("$6$0$0123456789abcdefghij0123456789abcdefghij0123456789abcdefghij0123456789abcdefghij1234567"),
        strlen("$6$0$0123456789abcdefghij0123456789abcdefghij0123456789abcdefghij0123456789abcdefghij1234567"), "F27",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING,
        (char *)("$6$0$0123456789abcdefghij0123456789abcdefghij0123456789abcdefghij0123456789abcdefghij12345"),
        strlen("$6$0$0123456789abcdefghij0123456789abcdefghij0123456789abcdefghij0123456789abcdefghij12345"), "F27",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING,
        (char *)("$6$0$abcdefghij012345678901234567890123456789123"),
        strlen("$6$0$abcdefghij012345678901234567890123456789123"), "F27", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("$3b$"), strlen("$3b$"), "F27", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("$3b$.*"), strlen("$3b$.*"), "F27",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置符合数据");
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("$0$"), strlen("$0$"), "F3", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("$1$0$abcdefghij0123456789ab"),
        strlen("$1$0$abcdefghij0123456789ab"), "F27", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING,
        (char *)("$5$0$abcdefghij012345678901234567890123456789123"),
        strlen("$5$0$abcdefghij012345678901234567890123456789123"), "F28", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING,
        (char *)("$5$rounds=0123456789$0$abcdefghij012345678901234567890123456789123"),
        strlen("$5$rounds=0123456789$0$abcdefghij012345678901234567890123456789123"), "F29",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING,
        (char *)("$6$0$0123456789abcdefghij0123456789abcdefghij0123456789abcdefghij0123456789abcdefghij123456"),
        strlen("$6$0$0123456789abcdefghij0123456789abcdefghij0123456789abcdefghij0123456789abcdefghij123456"), "F30",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING,
        (char *)("$6$rounds=0123456789$0$"
                 "0123456789abcdefghij0123456789abcdefghij0123456789abcdefghij0123456789abcdefghij123456"),
        strlen("$6$rounds=0123456789$0$"
               "0123456789abcdefghij0123456789abcdefghij0123456789abcdefghij0123456789abcdefghij123456"),
        "F31", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 066.pattern字符串"([01]?[0-9]|2[0-3]):[0-5][0-9]-([01]?[0-9]|2[0-3]):[0-5][0-9]|all"，先设置不符合数据，再设置符合数据
TEST_F(SupW3cScene, Yang_092_066)
{
    AW_FUN_Log(LOG_STEP, "创建Yang表，开启事务");
    int ret = 0;
    AsyncUserDataT data = {0};
    char vertexPath[] = "./schemaFile/Yang_092_066.gmjson";
    char edgePath[] = "./schemaFile/edgeLabel1.gmjson";
    TestCreateLabel(g_stmt_async, vertexPath, edgePath);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_labelNameRoot, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置不符合数据");
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("000:00-23:59"), strlen("000:00-23:59"), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("00:00-23:599"), strlen("00:00-23:599"), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("-00:00-23:59"), strlen("-00:00-23:59"), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("00:00+23:59"), strlen("00:00+23:59"), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("00;00-23:59"), strlen("00;00-23:59"), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("0:000-23:59"), strlen("0:000-23:59"), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("alll"), strlen("alll"), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("al"), strlen("al"), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("ll"), strlen("ll"), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("ALL"), strlen("ALL"), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置符合数据");
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("00:00-23:59"), strlen("00:00-23:59"),
        "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("12:00-11:00"), strlen("12:00-11:00"),
        "F27", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("all"), strlen("all"), "F32", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 067.pattern字符串"([01]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]-([01]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]"，
// 先设置不符合数据，再设置符合数据
TEST_F(SupW3cScene, Yang_092_067)
{
    AW_FUN_Log(LOG_STEP, "创建Yang表，开启事务");
    int ret = 0;
    AsyncUserDataT data = {0};
    char vertexPath[] = "./schemaFile/Yang_092_067.gmjson";
    char edgePath[] = "./schemaFile/edgeLabel1.gmjson";
    TestCreateLabel(g_stmt_async, vertexPath, edgePath);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_labelNameRoot, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置不符合数据");
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("000:00:00-23:59:59"),
        strlen("000:00:00-23:59:59"), "F5", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("00:00:00-23:59:599"),
        strlen("00:00:00-23:59:599"), "F5", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("00:00:00-23:59:60"),
        strlen("00:00:00-23:59:60"), "F5", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("00:00:00-23:60:59"),
        strlen("00:00:00-23:60:59"), "F5", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("30:00:00-23:59:59"),
        strlen("30:00:00-23:59:59"), "F5", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置符合数据");
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("00:00:00-23:59:59"),
        strlen("00:00:00-23:59:59"), "F5", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("23:59:59-00:00:00"),
        strlen("23:59:59-00:00:00"), "F27", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("0:59:59-9:00:00"),
        strlen("0:59:59-9:00:00"), "F28", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 068.pattern字符串"(([0-1](\\.[1-3]?[0-9]))|(2\\.(0|([1-9]\\d*))))(\\.(0|([1-9]\\d*)))*"，
// 先设置不符合数据，再设置符合数据
TEST_F(SupW3cScene, Yang_092_068)
{
    AW_FUN_Log(LOG_STEP, "创建Yang表，开启事务");
    int ret = 0;
    AsyncUserDataT data = {0};
    char vertexPath[] = "./schemaFile/Yang_092_068.gmjson";
    char edgePath[] = "./schemaFile/edgeLabel1.gmjson";
    TestCreateLabel(g_stmt_async, vertexPath, edgePath);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_labelNameRoot, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置不符合数据");
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("3.0.0"), strlen("3.0.0"), "F6", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("0.40.0"), strlen("0.40.0"), "F6",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("2.+0.0"), strlen("2.+0.0"), "F6",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置符合数据");
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("0.0.0"), strlen("0.0.0"), "F6", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("0.0.999999999"),
        strlen("0.0.999999999"), "F27", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("1.39.99999"), strlen("1.39.99999"),
        "F28", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("2.99999.999999999"),
        strlen("2.99999.999999999"), "F29", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("1.0.0.0.0.0.0.0.0"),
        strlen("1.0.0.0.0.0.0.0.0"), "F30", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING,
        (char *)("0.39.9999999.9999999.9999999.9999999.9999999"),
        strlen("0.39.9999999.9999999.9999999.9999999.9999999"), "F31", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 069.pattern字符串"([0-7](-[0-7])?(,[0-7](-[0-7])?)*)|any"，先设置不符合数据，再设置符合数据
TEST_F(SupW3cScene, Yang_092_069)
{
    AW_FUN_Log(LOG_STEP, "创建Yang表，开启事务");
    int ret = 0;
    AsyncUserDataT data = {0};
    char vertexPath[] = "./schemaFile/Yang_092_069.gmjson";
    char edgePath[] = "./schemaFile/edgeLabel1.gmjson";
    TestCreateLabel(g_stmt_async, vertexPath, edgePath);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_labelNameRoot, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置不符合数据");
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("8,7"), strlen("8,7"), "F7", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("9,7"), strlen("9,7"), "F7", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("-1,0"), strlen("-1,0"), "F7", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("0+0"), strlen("0+0"), "F7", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("0，0"), strlen("0，0"), "F7", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("-0,0-0,0-0,0-0"), strlen("-0,0-0,0-0,0-0"),
        "F7", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("an"), strlen("an"), "F7", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("ny"), strlen("ny"), "F7", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("anyy"), strlen("anyy"), "F7", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("aany"), strlen("aany"), "F7", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("ANY"), strlen("ANY"), "F7", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置符合数据");
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("0,0"), strlen("0,0"), "F7", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("0-0,0-0,0-0,0-0"),
        strlen("0-0,0-0,0-0,0-0"), "F27", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("7-7,7-7,7-7,7-7"),
        strlen("7-7,7-7,7-7,7-7"), "F28", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("7,7"), strlen("7,7"), "F29", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("any"), strlen("any"), "F30", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 070.pattern字符串"(([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\\.){3}
// ([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])(%[\\p{N}\\p{L}]+)?", "[0-9\\.]*"，先设置不符合数据，再设置符合数据
TEST_F(SupW3cScene, Yang_092_070)
{
    AW_FUN_Log(LOG_STEP, "创建Yang表，开启事务");
    int ret = 0;
    AsyncUserDataT data = {0};
    char vertexPath[] = "./schemaFile/Yang_092_070.gmjson";
    char edgePath[] = "./schemaFile/edgeLabel1.gmjson";
    TestCreateLabel(g_stmt_async, vertexPath, edgePath);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_labelNameRoot, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置不符合数据");
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("256.255.255.255"), strlen("256.255.255.255"),
        "F8", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("192.168.0.1%1"), strlen("192.168.0.1%1"),
        "F8", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("1921.168.0.a"), strlen("1921.168.0.a"), "F8",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("1921.168.0.A"), strlen("1921.168.0.A"), "F8",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("192+168.0.1"), strlen("192+168.0.1"), "F8",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置符合数据");
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("192.168.0.1"), strlen("192.168.0.1"),
        "F8", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("255.255.255.255"),
        strlen("255.255.255.255"), "F27", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("0.0.0.0"), strlen("0.0.0.0"), "F28",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 071.pattern字符串"(([0-9]|[0-5][0-9]|[6][0-3])(-([0-9]|[0-5][0-9]|[6][0-3]))?(,([0-9]|[0-5][0-9]|[6][0-3])
// (-([0-9]|[0-5][0-9]|[6][0-3]))?)*)|any"，先设置不符合数据，再设置符合数据
TEST_F(SupW3cScene, Yang_092_071)
{
    AW_FUN_Log(LOG_STEP, "创建Yang表，开启事务");
    int ret = 0;
    AsyncUserDataT data = {0};
    char vertexPath[] = "./schemaFile/Yang_092_071.gmjson";
    char edgePath[] = "./schemaFile/edgeLabel1.gmjson";
    TestCreateLabel(g_stmt_async, vertexPath, edgePath);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_labelNameRoot, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置不符合数据");
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("0.0"), strlen("0.0"), "F9", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("64,63"), strlen("64,63"), "F9", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("-1,0"), strlen("-1,0"), "F9", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("0-0-0,0"), strlen("0-0-0,0"), "F9",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置符合数据");
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("0,0"), strlen("0,0"), "F9", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("63,63"), strlen("63,63"), "F27", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("63-63,63-63,63-63,63-63,63-63"),
        strlen("63-63,63-63,63-63,63-63,63-63"), "F28", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("00-00,00-00,00-00,00-00,00-00"),
        strlen("00-00,00-00,00-00,00-00,00-00"), "F29", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("any"), strlen("any"), "F32", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 072.pattern字符串"(([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\\.){3}([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]
// |25[0-5])/(([0-9])|([1-2][0-9])|(3[0-2]))|((:|[0-9a-fA-F]{0,4}):)([0-9a-fA-F]{0,4}:){0,5}((([0-9a-fA-F]{0,4}:)?
// (:|[0-9a-fA-F]{0,4}))|(((25[0-5]|2[0-4][0-9]|[01]?[0-9]?[0-9])\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9]?[0-9])))
// (/(([0-9])|([0-9]{2})|(1[0-1][0-9])|(12[0-8])))|(([^:]+:){6}(([^:]+:[^:]+)|(.*\\..*)))|((([^:]+:)*[^:]+)?
// ::(([^:]+:)*[^:]+)?)(/.+)"，先设置不符合数据，再设置符合数据
TEST_F(SupW3cScene, Yang_092_072)
{
    AW_FUN_Log(LOG_STEP, "创建Yang表，开启事务");
    int ret = 0;
    AsyncUserDataT data = {0};
    char vertexPath[] = "./schemaFile/Yang_092_072.gmjson";
    char edgePath[] = "./schemaFile/edgeLabel1.gmjson";
    TestCreateLabel(g_stmt_async, vertexPath, edgePath);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_labelNameRoot, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置不符合数据");
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("0.0"), strlen("0.0"), "F10", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("256.168.0.1/24"), strlen("256.168.0.1/24"),
        "F10", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("192.168.0.1/33"), strlen("192.168.0.1/33"),
        "F10", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)(":g000:0db8:85a3:0000:0000:8a2e:0370:7334"),
        strlen(":g000:0db8:85a3:0000:0000:8a2e:0370:7334"), "F10", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("2001::0db8:85a3:0000:0000:8a2e:0370:7334"),
        strlen("2001::0db8:85a3:0000:0000:8a2e:0370:7334"), "F10", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置符合数据");
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("192.168.0.1/24"),
        strlen("192.168.0.1/24"), "F10", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret =
        TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("2001:0db8:85a3:0000:0000:8a2e:0370:7334"),
            strlen("2001:0db8:85a3:0000:0000:8a2e:0370:7334"), "F27", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING,
        (char *)("g001:0db8:85a3:0000:0000:8a2e:0370:7334"), strlen("g001:0db8:85a3:0000:0000:8a2e:0370:7334"), "F28",
        GMC_YANG_PROPERTY_OPERATION_CREATE);  // 符合模糊匹配
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING,
        (char *)("666666:0db8:85a3:0000:0000:8a2e:0370:7334"), strlen("666666:0db8:85a3:0000:0000:8a2e:0370:7334"),
        "F29",
        GMC_YANG_PROPERTY_OPERATION_CREATE);  // 符合模糊匹配
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("xx:xx:xx:xx:xx:xx:xx:xx"),
        strlen("xx:xx:xx:xx:xx:xx:xx:xx"), "F30", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("::/128"), strlen("::/128"), "F31",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("xx:xx:xx:xx:xx:xx:."),
        strlen("xx:xx:xx:xx:xx:xx:."), "F32", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("128.128"), strlen("128.128"), "F100",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 073.pattern字符串"(([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\\.){3}
// ([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])(%[\\p{N}\\p{L}]+)?|((:|[0-9a-fA-F]{0,4}):)([0-9a-fA-F]{0,4}:){0,5}
// ((([0-9a-fA-F]{0,4}:)?(:|[0-9a-fA-F]{0,4}))|(((25[0-5]|2[0-4][0-9]|[01]?[0-9]?[0-9])\\.){3}
// (25[0-5]|2[0-4][0-9]|[01]?[0-9]?[0-9])))(%[\\p{N}\\p{L}]+)?|(([^:]+:){6}(([^:]+:[^:]+)|
// (.*\\..*)))|((([^:]+:)*[^:]+)?::(([^:]+:)*[^:]+)?)(%.+)?|((([a-zA-Z0-9_]([a-zA-Z0-9\\-_]){0,61})?[a-zA-Z0-9]\\.)*
// ([a-zA-Z0-9_]([a-zA-Z0-9\\-_]){0,61})?[a-zA-Z0-9]\\.?)|\\."，先设置不符合数据，再设置符合数据
TEST_F(SupW3cScene, Yang_092_073)
{
    AW_FUN_Log(LOG_STEP, "创建Yang表，开启事务");
    int ret = 0;
    AsyncUserDataT data = {0};
    char vertexPath[] = "./schemaFile/Yang_092_073.gmjson";
    char edgePath[] = "./schemaFile/edgeLabel1.gmjson";
    TestCreateLabel(g_stmt_async, vertexPath, edgePath);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_labelNameRoot, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置不符合数据");
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("256.168.0.1/24"), strlen("256.168.0.1/24"),
        "F11", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("0000.168.0.1/24"), strlen("0000.168.0.1/24"),
        "F11", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)(":2001:0db8:85a3:0000:0000:8a2e:0370:7334"),
        strlen(":2001:0db8:85a3:0000:0000:8a2e:0370:7334"), "F11", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)(""), strlen(""), "F11", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置符合数据");
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING,
        (char *)("g2001:0db8:85a3:0000:0000:8a2e:0370:7334"), strlen("g2001:0db8:85a3:0000:0000:8a2e:0370:7334"), "F11",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING,
        (char *)("2001:0db8:85a3:0000:0000:8a2e:255.255.255.255%128"),
        strlen("2001:0db8:85a3:0000:0000:8a2e:255.255.255.255%128"), "F27", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("255.255.255.255%32"),
        strlen("255.255.255.255%32"), "F28", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret =
        TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffff"),
            strlen("ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffff"), "F29", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("::"), strlen("::"), "F30", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("a"), strlen("a"), "F31", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 074. pattern字符串"([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])
//  (\\.([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])){3}|(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|
//  ([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}
//  (:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}
//  (:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|
//  [0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:))"，先设置不符合数据，再设置符合数据
TEST_F(SupW3cScene, Yang_092_074)
{
    AW_FUN_Log(LOG_STEP, "创建Yang表，开启事务");
    int ret = 0;
    AsyncUserDataT data = {0};
    char vertexPath[] = "./schemaFile/Yang_092_074.gmjson";
    char edgePath[] = "./schemaFile/edgeLabel1.gmjson";
    TestCreateLabel(g_stmt_async, vertexPath, edgePath);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_labelNameRoot, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置不符合数据");
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)(""), strlen(""), "F12", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("256.255.255.255"), strlen("256.255.255.255"),
        "F12", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("256.255.255.255/32"),
        strlen("256.255.255.255/32"), "F12", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("0255.255.255.255"),
        strlen("0255.255.255.255"), "F12", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("0:0:0:0:0:0:0:0:"),
        strlen("0:0:0:0:0:0:0:0:"), "F12", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("g:0:0:0:0:0:0:0"), strlen("g:0:0:0:0:0:0:0"),
        "F12", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("G:0:0:0:0:0:0:0"), strlen("G:0:0:0:0:0:0:0"),
        "F12", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置符合数据");
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("255.255.255.255"),
        strlen("255.255.255.255"), "F12", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("0:0:0:0:0:0:0:0"),
        strlen("0:0:0:0:0:0:0:0"), "F27", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("f:f:f:f:f:f:f:f"),
        strlen("f:f:f:f:f:f:f:f"), "F28", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret =
        TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffff"),
            strlen("ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffff"), "F29", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("0::"), strlen("0::"), "F30", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("::"), strlen("::"), "F31", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 075. pattern字符串"([0-9]|[1-9][0-9]|[1-9][0-9][0-9]|[1-3][0-9][0-9][0-9]|40[0-8][0-9]|409[0-5])
//  ([,-]([1-9]|[1-9][0-9]|[1-9][0-9][0-9]|[1-3][0-9][0-9][0-9]|40[0-8][0-9]|409[0-5]))*"，先设置不符合数据，再设置符合数据
TEST_F(SupW3cScene, Yang_092_075)
{
    AW_FUN_Log(LOG_STEP, "创建Yang表，开启事务");
    int ret = 0;
    AsyncUserDataT data = {0};
    char vertexPath[] = "./schemaFile/Yang_092_075.gmjson";
    char edgePath[] = "./schemaFile/edgeLabel1.gmjson";
    TestCreateLabel(g_stmt_async, vertexPath, edgePath);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_labelNameRoot, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置不符合数据");
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("00"), strlen("00"), "F13", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("4096"), strlen("4096"), "F13", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("4095,0"), strlen("4095,0"), "F13",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("4095+1"), strlen("4095+1"), "F13",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("4095,4096"), strlen("4095,4096"), "F13",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置符合数据");
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("0"), strlen("0"), "F13", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("4095"), strlen("4095"), "F27", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("4095,4095-4095,4095-4095,4095-4095"),
        strlen("4095,4095-4095,4095-4095,4095-4095"), "F28", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("0,9,10-99,100-999,4095"),
        strlen("0,9,10-99,100-999,4095"), "F29", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 076. pattern字符串"((:|[0-9a-fA-F]{0,4}):)([0-9a-fA-F]{0,4}:){0,5}((([0-9a-fA-F]{0,4}:)?(:|[0-9a-fA-F]{0,4}))|
//  (((25[0-5]|2[0-4][0-9]|[01]?[0-9]?[0-9])\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9]?[0-9])))(/(([0-9])|([0-9]{2})|
//  (1[0-1][0-9])|(12[0-8])))", "(([^:]+:){6}(([^:]+:[^:]+)|(.*\\..*)))|((([^:]+:)*[^:]+)?::(([^:]+:)*[^:]+)?)(/.+)"，
//  先设置不符合数据，再设置符合数据
TEST_F(SupW3cScene, Yang_092_076)
{
    AW_FUN_Log(LOG_STEP, "创建Yang表，开启事务");
    int ret = 0;
    AsyncUserDataT data = {0};
    char vertexPath[] = "./schemaFile/Yang_092_076.gmjson";
    char edgePath[] = "./schemaFile/edgeLabel1.gmjson";
    TestCreateLabel(g_stmt_async, vertexPath, edgePath);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_labelNameRoot, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置不符合数据");
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("9999:9999:9999:9999:9999:9999:9999:9999/129"),
        strlen("9999:9999:9999:9999:9999:9999:9999:9999/129"), "F14", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("999g:9999:9999:9999:9999:9999:9999:9999/128"),
        strlen("999g:9999:9999:9999:9999:9999:9999:9999/128"), "F14", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("999G:9999:9999:9999:9999:9999:9999:9999/128"),
        strlen("999G:9999:9999:9999:9999:9999:9999:9999/128"), "F14", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("a:123:abcd:ef:12ef:12ab:89EF:ABab/-1"),
        strlen("a:123:abcd:ef:12ef:12ab:89EF:ABab/-1"), "F14", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置符合数据");
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING,
        (char *)("9999:9999:9999:9999:9999:9999:9999:9999/128"), strlen("9999:9999:9999:9999:9999:9999:9999:9999/128"),
        "F14", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING,
        (char *)("ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffff/128"), strlen("ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffff/128"),
        "F27", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("a:123:abcd:ef:12ef:12ab:89EF:ABab/0"),
        strlen("a:123:abcd:ef:12ef:12ab:89EF:ABab/0"), "F28", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 077. pattern字符串"((:|[0-9a-fA-F]{0,4}):)([0-9a-fA-F]{0,4}:){0,5}((([0-9a-fA-F]{0,4}:)?(:|[0-9a-fA-F]{0,4}))|
//  (((25[0-5]|2[0-4][0-9]|[01]?[0-9]?[0-9])\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9]?[0-9])))(%[\\p{N}\\p{L}]+)?",
//  "(([^:]+:){6}(([^:]+:[^:]+)|(.*\\..*)))|((([^:]+:)*[^:]+)?::(([^:]+:)*[^:]+)?)(%.+)?"，先设置不符合数据，再设置符合数据
TEST_F(SupW3cScene, Yang_092_077)
{
    AW_FUN_Log(LOG_STEP, "创建Yang表，开启事务");
    int ret = 0;
    AsyncUserDataT data = {0};
    char vertexPath[] = "./schemaFile/Yang_092_077.gmjson";
    char edgePath[] = "./schemaFile/edgeLabel1.gmjson";
    TestCreateLabel(g_stmt_async, vertexPath, edgePath);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_labelNameRoot, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置不符合数据");
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("256.168.0.1/24"), strlen("256.168.0.1/24"),
        "F15", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("0000.168.0.1/24"), strlen("0000.168.0.1/24"),
        "F15", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)(":2001:0db8:85a3:0000:0000:8a2e:0370:7334"),
        strlen(":2001:0db8:85a3:0000:0000:8a2e:0370:7334"), "F15", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("2001::0db8:85a3:0000:0000:8a2e:0370:7334"),
        strlen("2001::0db8:85a3:0000:0000:8a2e:0370:7334"), "F15", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("g200:0db8:85a3:0000:0000:8a2e:0370:7334"),
        strlen("g001:0db8:85a3:0000:0000:8a2e:0370:7334"), "F15", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置符合数据");
    ret =
        TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("2001:0db8:85a3:0000:0000:8a2e:0370:7334"),
            strlen("2001:0db8:85a3:0000:0000:8a2e:0370:7334"), "F15", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING,
        (char *)("2001:0db8:85a3:0000:0000:8a2e:255.255.255.255%128"),
        strlen("2001:0db8:85a3:0000:0000:8a2e:255.255.255.255%128"), "F27", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret =
        TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("FFFF:FFFF:FFFF:FFFF:FFFF:FFFF:FFFF:FFFF"),
            strlen("FFFF:FFFF:FFFF:FFFF:FFFF:FFFF:FFFF:FFFF"), "F28", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret =
        TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffff"),
            strlen("ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffff"), "F29", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("::"), strlen("::"), "F30", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 078. pattern字符串"([0-9a-fA-F]{2}(:[0-9a-fA-F]{2})*)?"，先设置不符合数据，再设置符合数据
TEST_F(SupW3cScene, Yang_092_078)
{
    AW_FUN_Log(LOG_STEP, "创建Yang表，开启事务");
    int ret = 0;
    AsyncUserDataT data = {0};
    char vertexPath[] = "./schemaFile/Yang_092_078.gmjson";
    char edgePath[] = "./schemaFile/edgeLabel1.gmjson";
    TestCreateLabel(g_stmt_async, vertexPath, edgePath);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_labelNameRoot, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置不符合数据");
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)(" "), strlen(" "), "F16", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("0000"), strlen("0000"), "F16", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("9F1:9F"), strlen("9F1:9F"), "F16",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("9F:9F1"), strlen("9F:9F1"), "F16",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("9:9F"), strlen("9:9F"), "F16", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("9F:9"), strlen("9F:9"), "F16", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("9g:9F"), strlen("9g:9F"), "F16", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("9G:9F"), strlen("9G:9F"), "F16", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置符合数据");
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("00"), strlen("00"), "F16", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_STRING, (char *)(""), strlen(""), "F27", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("9F:9F:9F:9F:9F"),
        strlen("9F:9F:9F:9F:9F"), "F28", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("Aa:11"), strlen("Aa:11"), "F29", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("aa:99:EE"), strlen("aa:99:EE"), "F30",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 079. pattern字符串"0x[A-Fa-f0-9]{4}|any|\\d+"，先设置不符合数据，再设置符合数据
TEST_F(SupW3cScene, Yang_092_079)
{
    AW_FUN_Log(LOG_STEP, "创建Yang表，开启事务");
    int ret = 0;
    AsyncUserDataT data = {0};
    char vertexPath[] = "./schemaFile/Yang_092_079.gmjson";
    char edgePath[] = "./schemaFile/edgeLabel1.gmjson";
    TestCreateLabel(g_stmt_async, vertexPath, edgePath);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_labelNameRoot, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置不符合数据");
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("0xFFFFF"), strlen("0xFFFFF"), "F17",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("0xFFF"), strlen("0xFFF"), "F17", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("0xFFFG"), strlen("0xFFFG"), "F17",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("0xfffg"), strlen("0xfffg"), "F17",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置符合数据");
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("0123456789"), strlen("0123456789"),
        "F17", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("any"), strlen("any"), "F27", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("0x0000"), strlen("0x0000"), "F28",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("0xFFFF"), strlen("0xFFFF"), "F29",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("0xffff"), strlen("0xffff"), "F30",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 080. pattern字符串"([1-9][0-9]{0,3}(-[1-9][0-9]{0,3})?(,[1-9][0-9]{0,3}(-[1-9][0-9]{0,3})?)*)"，
//  先设置不符合数据，再设置符合数据
TEST_F(SupW3cScene, Yang_092_080)
{
    AW_FUN_Log(LOG_STEP, "创建Yang表，开启事务");
    int ret = 0;
    AsyncUserDataT data = {0};
    char vertexPath[] = "./schemaFile/Yang_092_080.gmjson";
    char edgePath[] = "./schemaFile/edgeLabel1.gmjson";
    TestCreateLabel(g_stmt_async, vertexPath, edgePath);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_labelNameRoot, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置不符合数据");
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("0,1"), strlen("0,1"), "F18", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("1-1-1,1"), strlen("1-1-1,1"), "F18",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("10000,1"), strlen("10000,1"), "F18",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置符合数据");
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("1,1"), strlen("1,1"), "F18", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("1,1,1,1,1,1"), strlen("1,1,1,1,1,1"),
        "F27", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING,
        (char *)("9999-9999,9999-9999,9999-9999,9999-9999,9999-9999"),
        strlen("9999-9999,9999-9999,9999-9999,9999-9999,9999-9999"), "F28", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("2024-2025,2028-2030"),
        strlen("2024-2025,2028-2030"), "F29", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 081. pattern字符串"(([1-9]|[1-9][0-9]|[1-9][0-9][0-9]|[1-3][0-9][0-9][0-9]|40[0-8][0-9]|409[0-4])([,-]([1-9]|
//  [1-9][0-9]|[1-9][0-9][0-9]|[1-3][0-9][0-9][0-9]|40[0-8][0-9]|409[0-4]))*)?
//  |vlan-id-is-a-parameter|any|priority-tagged"，先设置不符合数据，再设置符合数据
TEST_F(SupW3cScene, Yang_092_081)
{
    AW_FUN_Log(LOG_STEP, "创建Yang表，开启事务");
    int ret = 0;
    AsyncUserDataT data = {0};
    char vertexPath[] = "./schemaFile/Yang_092_081.gmjson";
    char edgePath[] = "./schemaFile/edgeLabel1.gmjson";
    TestCreateLabel(g_stmt_async, vertexPath, edgePath);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_labelNameRoot, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置不符合数据");
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("0,4089"), strlen("0,4089"), "F19",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("1=4089"), strlen("1=4089"), "F19",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("1+4089"), strlen("1+4089"), "F19",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("1,4095"), strlen("1,4095"), "F19",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("4095,1"), strlen("4095,1"), "F19",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("1,0"), strlen("1,0"), "F19", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置符合数据");
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("1,4089"), strlen("1,4089"), "F19",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("1-4089"), strlen("1-4089"), "F27",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("vlan-id-is-a-parameter"),
        strlen("vlan-id-is-a-parameter"), "F28", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("any"), strlen("any"), "F29", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("priority-tagged"),
        strlen("priority-tagged"), "F30", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("1,4094-4089-4089,4089,4089,4094"),
        strlen("1,4089-4089-4089,4089,4089,4094"), "F31", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_STRING, (char *)(""), strlen(""), "F32", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 082. pattern字符串"((([a-zA-Z0-9_]([a-zA-Z0-9\\-_]){0,61})?[a-zA-Z0-9]\\.)*([a-zA-Z0-9_]
//  ([a-zA-Z0-9\\-_]){0,61})?[a-zA-Z0-9]\\.?)|\\."，先设置不符合数据，再设置符合数据
TEST_F(SupW3cScene, Yang_092_082)
{
    AW_FUN_Log(LOG_STEP, "创建Yang表，开启事务");
    int ret = 0;
    AsyncUserDataT data = {0};
    char vertexPath[] = "./schemaFile/Yang_092_082.gmjson";
    char edgePath[] = "./schemaFile/edgeLabel1.gmjson";
    TestCreateLabel(g_stmt_async, vertexPath, edgePath);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_labelNameRoot, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置不符合数据");
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("9.."), strlen("9.."), "F20", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("!"), strlen("!"), "F20", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)(".."), strlen(".."), "F20", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("\\."), strlen("\\."), "F20", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("\\\\."), strlen("\\\\."), "F20", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置符合数据");
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("."), strlen("."), "F20", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("a"), strlen("a"), "F27", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("9."), strlen("9."), "F28", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("_-_9._-_9._-_9._-_9.a"),
        strlen("_-_9._-_9._-_9._-_9.a"), "F29", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING,
        (char *)("zabcdefghij0123456789abcdefghij0123456789abcdefghij0123456789a9."
                 "abcdefghij0123456789abcdefghij0123456789abcdefghij0123456789a9."),
        strlen("zabcdefghij0123456789abcdefghij0123456789abcdefghij0123456789a9."
               "abcdefghij0123456789abcdefghij0123456789abcdefghij0123456789a9."),
        "F30", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 083. pattern字符串"[a-zA-Z]{4}[0-9a-fA-F]{8}"，先设置不符合数据，再设置符合数据
TEST_F(SupW3cScene, Yang_092_083)
{
    AW_FUN_Log(LOG_STEP, "创建Yang表，开启事务");
    int ret = 0;
    AsyncUserDataT data = {0};
    char vertexPath[] = "./schemaFile/Yang_092_083.gmjson";
    char edgePath[] = "./schemaFile/edgeLabel1.gmjson";
    TestCreateLabel(g_stmt_async, vertexPath, edgePath);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_labelNameRoot, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置不符合数据");
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("aaa12345678"), strlen("aaa12345678"), "F21",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("abyz0123abcg"), strlen("abyz0123abcg"), "F21",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("abyz0123abcG"), strlen("abyz0123abcG"), "F21",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("abcd123456789"), strlen("abcd123456789"),
        "F21", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置符合数据");
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("abyz0123abEF"), strlen("abyz0123abEF"),
        "F21", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("abcdabcdefab"), strlen("abcdabcdefab"),
        "F27", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("abcd12345678"), strlen("abcd12345678"),
        "F28", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("ABCDABCDEFAB"), strlen("ABCDABCDEFAB"),
        "F29", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 084. pattern字符串"\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}(\\.\\d+)?(Z|[\\+\\-]\\d{2}:\\d{2})"，
//  先设置不符合数据，再设置符合数据
TEST_F(SupW3cScene, Yang_092_084)
{
    AW_FUN_Log(LOG_STEP, "创建Yang表，开启事务");
    int ret = 0;
    AsyncUserDataT data = {0};
    char vertexPath[] = "./schemaFile/Yang_092_084.gmjson";
    char edgePath[] = "./schemaFile/edgeLabel1.gmjson";
    TestCreateLabel(g_stmt_async, vertexPath, edgePath);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_labelNameRoot, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置不符合数据");
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("9999-99-99T99:99:99.9.9+99:99"),
        strlen("9999-99-99T99:99:99.9.9+99:99"), "F23", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("10000-99-99T99:99:99.9+99:99"),
        strlen("10000-99-99T99:99:99.9+99:99"), "F23", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("9999-999-99T99:99:99.9+99:99"),
        strlen("9999-999-99T99:99:99.9+99:99"), "F23", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("0000-00-00t00:00:00.00+00:00"),
        strlen("0000-00-00t00:00:00.00+00:00"), "F23", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置符合数据");
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("2024-24-24T24:24:24Z"),
        strlen("2024-24-24T24:24:24Z"), "F23", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("2024-24-24T24:24:24+24:24"),
        strlen("2024-24-24T24:24:24+24:24"), "F27", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret =
        TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("9999-99-99T99:99:99.999999999999+99:99"),
            strlen("9999-99-99T99:99:99.999999999999+99:99"), "F28", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("0000-00-00T00:00:00.00+00:00"),
        strlen("0000-00-00T00:00:00.00+00:00"), "F29", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 085. pattern字符串"LoopBack([1-2]{0,1}[0-9]|3[0-1])"，先设置不符合数据，再设置符合数据
TEST_F(SupW3cScene, Yang_092_085)
{
    AW_FUN_Log(LOG_STEP, "创建Yang表，开启事务");
    int ret = 0;
    AsyncUserDataT data = {0};
    char vertexPath[] = "./schemaFile/Yang_092_085.gmjson";
    char edgePath[] = "./schemaFile/edgeLabel1.gmjson";
    TestCreateLabel(g_stmt_async, vertexPath, edgePath);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_labelNameRoot, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置不符合数据");
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("LoopBack"), strlen("LoopBack"), "F24",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("LoopBack32"), strlen("LoopBack32"), "F24",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("LoopBack40"), strlen("LoopBack40"), "F24",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("LoopBack111"), strlen("LoopBack111"), "F24",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置符合数据");
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("LoopBack0"), strlen("LoopBack0"), "F24",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("LoopBack10"), strlen("LoopBack10"),
        "F27", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("LoopBack29"), strlen("LoopBack29"),
        "F28", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("LoopBack30"), strlen("LoopBack30"),
        "F29", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("LoopBack31"), strlen("LoopBack31"),
        "F30", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 086. pattern字符串"((mon(,tues)?(,wed)?(,thu)?(,fri)?(,sat)?(,sun)?)|(tues(,wed)?(,thu)?(,fri)?(,sat)?(,sun)?)|
//  (wed(,thu)?(,fri)?(,sat)?(,sun)?)|(thu(,fri)?(,sat)?(,sun)?)|(fri(,sat)?(,sun)?)|(sat(,sun))|(sun))|
//  off-day|daily|working-day"，先设置不符合数据，再设置符合数据
TEST_F(SupW3cScene, Yang_092_086)
{
    AW_FUN_Log(LOG_STEP, "创建Yang表，开启事务");
    int ret = 0;
    AsyncUserDataT data = {0};
    char vertexPath[] = "./schemaFile/Yang_092_086.gmjson";
    char edgePath[] = "./schemaFile/edgeLabel1.gmjson";
    TestCreateLabel(g_stmt_async, vertexPath, edgePath);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_labelNameRoot, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置不符合数据");
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("tues,mon"), strlen("tues,mon"), "F25",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("mon,mon"), strlen("mon,mon"), "F25",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("tuess"), strlen("tuess"), "F25", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("tue"), strlen("tue"), "F25", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("TUES"), strlen("TUES"), "F25", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置符合数据");
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("mon,tues,wed,thu,fri,sat,sun"),
        strlen("mon,tues,wed,thu,fri,sat,sun"), "F25", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("tues"), strlen("tues"), "F27", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("sun"), strlen("sun"), "F28", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("off-day"), strlen("off-day"), "F29",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(
        g_rootNode, GMC_DATATYPE_STRING, (char *)("daily"), strlen("daily"), "F30", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("working-day"), strlen("working-day"),
        "F31", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 087.
// pattern字符串"Vlanif(([1-9][0-9]{0,2})|([1-3][0-9]{3})|(40[0-8][0-9])|(409[0-3]))"，先设置不符合数据，再设置符合数据
TEST_F(SupW3cScene, Yang_092_087)
{
    AW_FUN_Log(LOG_STEP, "创建Yang表，开启事务");
    int ret = 0;
    AsyncUserDataT data = {0};
    char vertexPath[] = "./schemaFile/Yang_092_087.gmjson";
    char edgePath[] = "./schemaFile/edgeLabel1.gmjson";
    TestCreateLabel(g_stmt_async, vertexPath, edgePath);

    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_labelNameRoot, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置不符合数据");
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("Vlanif"), strlen("Vlanif"), "F26",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("Vlanif000"), strlen("Vlanif000"), "F26",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("Vlanif9999"), strlen("Vlanif9999"), "F26",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("Vlanif4999"), strlen("Vlanif4999"), "F26",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("Vlanif4100"), strlen("Vlanif4100"), "F26",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("Vlanif4094"), strlen("Vlanif4094"), "F26",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetNodeField(g_rootNode, GMC_DATATYPE_STRING, (char *)("Vlanif5093"), strlen("Vlanif5093"), "F26",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    AW_FUN_Log(LOG_STEP, "GmcYangSetNodeProperty设置符合数据");
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("Vlanif1"), strlen("Vlanif1"), "F26",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("Vlanif10"), strlen("Vlanif10"), "F27",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("Vlanif99"), strlen("Vlanif99"), "F28",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("Vlanif1000"), strlen("Vlanif1000"),
        "F29", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("Vlanif3999"), strlen("Vlanif3999"),
        "F30", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcYangSetNodeProperty(g_rootNode, GMC_DATATYPE_STRING, (char *)("Vlanif4093"), strlen("Vlanif4093"),
        "F31", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "提交事务");
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
