/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 078_YangIdentity
 * Author: hanyang
 * Create: 2024-02-19
 */
#include "Identity.h"
#include "DiffResult.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
GmcConnT *g_conn_async = NULL;
GmcStmtT *g_stmt_async = NULL;
GmcStmtT *g_stmt_root = NULL;
GmcStmtT *g_stmt_list[10] = {0};
GmcNodeT *g_rootNode = NULL;
GmcNodeT *g_childNode[40] = {0};

class YangIdentity_test : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void YangIdentity_test::SetUpTestCase()
{
    int ret;
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void YangIdentity_test::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

void YangIdentity_test::SetUp()
{
    int ret;
    AsyncUserDataT data = {0};

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t i = 0;
    for (i = 0; i < 10; i++) {
        ret = GmcAllocStmt(g_conn_async, &g_stmt_list[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 异步创建namespace
    GmcDropNamespace(g_stmt, g_namespace);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = g_namespace;
    nspCfg.tablespaceName = NULL;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcClearNamespaceAsync(g_stmt_async, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 创建表
    TestCreateLabel(g_stmt_async);

    // 设置是否只有一个ID字段，true时，校验diff相关接口出参返回值，077用例专用
    g_OneIdFieldFlag = false;

    AW_CHECK_LOG_BEGIN();
}

void YangIdentity_test::TearDown()
{
    int ret;
    uint32_t i = 0;
    AsyncUserDataT data = {0};
    AddWhiteList(GMERR_NO_DATA);
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    AW_CHECK_LOG_END();

    // 删除表
    ret = GmcClearNamespaceAsync(g_stmt_async, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, g_namespace, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    GmcFreeStmt(g_stmt_root);
    for (i = 0; i < 10; i++) {
        GmcFreeStmt(g_stmt_list[i]);
    }
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_conn_async = NULL;
    g_stmt_async = NULL;
    g_stmt_root = NULL;
    for (i = 0; i < 10; i++) {
        g_stmt_list[i] = NULL;
    }
    g_rootNode = NULL;
    for (i = 0; i < 10; i++) {
        g_childNode[i] = NULL;
    }
}

/*****************************************************************************
 Description  : 001. 不含enumrate字段，建表失败
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    char *vLabelSchema = NULL;

    readJanssonFile("./schema_file/yang_fail_01.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
}

/*****************************************************************************
 Description  : 002.enumrate为空，建表失败
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    char *vLabelSchema = NULL;

    readJanssonFile("./schema_file/yang_fail_02.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_ARRAY_SUBSCRIPT_ERROR, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
    AddWhiteList(GMERR_ARRAY_SUBSCRIPT_ERROR);
}

/*****************************************************************************
 Description  : 003.enumrate的name字段不存在，建表失败
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    char *vLabelSchema = NULL;

    readJanssonFile("./schema_file/yang_fail_03.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINE_COLUMN, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
    AddWhiteList(GMERR_UNDEFINE_COLUMN);
}

/*****************************************************************************
 Description  : 004.enumrate的name字段为空字符串，建表失败
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    char *vLabelSchema = NULL;

    readJanssonFile("./schema_file/yang_fail_04.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
    AddWhiteList(GMERR_INVALID_PROPERTY);
}

/*****************************************************************************
 Description  : 005.enumrate的name字段超过128字符，建表失败
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    char *vLabelSchema = NULL;

    readJanssonFile("./schema_file/yang_fail_05.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
    AddWhiteList(GMERR_INVALID_PROPERTY);
}

/*****************************************************************************
 Description  : 006.enumrate的name字段正好为128字符，建表成功
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    char *vLabelSchema = NULL;

    readJanssonFile("./schema_file/yang_succ_06.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    ret = GmcDropVertexLabelAsync(g_stmt_async, "root", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
}

/*****************************************************************************
 Description  : 007.enumrate的value字段不存在，建表失败
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    char *vLabelSchema = NULL;

    readJanssonFile("./schema_file/yang_fail_07.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINE_COLUMN, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
    AddWhiteList(GMERR_UNDEFINE_COLUMN);
}

/*****************************************************************************
 Description  : 008.enumrate的value字段值超过取值范围，建表失败
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    char *vLabelSchema = NULL;

    readJanssonFile("./schema_file/yang_fail_08.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
    AddWhiteList(GMERR_INVALID_PROPERTY);
}

/*****************************************************************************
 Description  : 009.enumrate的value字段值正好等于取值范围的最大和最小值，建表成功
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    char *vLabelSchema = NULL;

    readJanssonFile("./schema_file/yang_succ_09.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    ret = GmcDropVertexLabelAsync(g_stmt_async, "root", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
}

/*****************************************************************************
 Description  : 010.enumrate的derived-path字段不存在，建表失败
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    char *vLabelSchema = NULL;

    readJanssonFile("./schema_file/yang_fail_10.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINE_COLUMN, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
    AddWhiteList(GMERR_UNDEFINE_COLUMN);
}

/*****************************************************************************
 Description  : 011.enumrate的derived-path长度超过4096，建表失败
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    char *vLabelSchema = NULL;

    readJanssonFile("./schema_file/yang_fail_11.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
    AddWhiteList(GMERR_INVALID_PROPERTY);
    AW_ADD_TRUNCATION_WHITE_LIST(1, "Not normal property. derived-path len exceeds");
}

/*****************************************************************************
 Description  : 012.enumrate的derived-path长度等于4096，建表成功
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    char *vLabelSchema = NULL;

    readJanssonFile("./schema_file/yang_succ_12.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    ret = GmcDropVertexLabelAsync(g_stmt_async, "root", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
}

/*****************************************************************************
 Description  : 013.默认值不在name范围内，建表失败
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    char *vLabelSchema = NULL;

    readJanssonFile("./schema_file/yang_fail_13.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
    AddWhiteList(GMERR_INVALID_PROPERTY);
}

/*****************************************************************************
 Description  : 014.默认值不在value范围内，建表失败
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    char *vLabelSchema = NULL;

    readJanssonFile("./schema_file/yang_fail_14.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
    AddWhiteList(GMERR_INVALID_PROPERTY);
}

/*****************************************************************************
 Description  : 015.默认值name和value混用，建表成功
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    char *vLabelSchema = NULL;

    readJanssonFile("./schema_file/yang_succ_15.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    ret = GmcDropVertexLabelAsync(g_stmt_async, "root", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(g_stmt_async, "leaflist1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
}

/*****************************************************************************
 Description  : 016.enumrate个数超过最大值，建表失败
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    char *vLabelSchema = NULL;

    readJanssonFile("./schema_file/yang_fail_16.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_ARRAY_SUBSCRIPT_ERROR, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
    AddWhiteList(GMERR_ARRAY_SUBSCRIPT_ERROR);
}

/*****************************************************************************
 Description  : 017.enumrate个数达到最大值，建表成功
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    char *vLabelSchema = NULL;

    readJanssonFile("./schema_file/yang_succ_17.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    ret = GmcDropVertexLabelAsync(g_stmt_async, "root", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
}

/*****************************************************************************
 Description  : 018.多个字段的enumerate_identity设置成一样的名字，建表成功
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    char *vLabelSchema = NULL;

    readJanssonFile("./schema_file/yang_succ_18.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    ret = GmcDropVertexLabelAsync(g_stmt_async, "root", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
}

/*****************************************************************************
 Description  : 019.多个字段不设置enumerate_identity，建表成功
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    char *vLabelSchema = NULL;

    readJanssonFile("./schema_file/yang_succ_19.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    ret = GmcDropVertexLabelAsync(g_stmt_async, "root", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
}

/*****************************************************************************
 Description  : 020.非yang表，包含identity字段，建表失败
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    char *vLabelSchema = NULL;

    readJanssonFile("./schema_file/yang_fail_20.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfig, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
    AddWhiteList(GMERR_INVALID_TABLE_DEFINITION);
}

/*****************************************************************************
 Description  : 021.container节点，Insert写入Identity字段，diff查询，subtree查询
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[1], "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_childNode[1], "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[1], "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff021, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_1", "Yang_078_Func_021_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 022.container节点，Merge写入和Merge更新Identity字段，diff查询，subtree查询
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************merge insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_MERGE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    TestYangSetIDName(g_childNode[1], "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_MERGE);
    idValue = -2;
    TestYangSetIDValue(g_childNode[1], "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    TestYangSetIDName(g_childNode[1], "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff021, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_1", "Yang_078_Func_021_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************merge update***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetIDName(g_rootNode, "ID1", "level2", strlen("level2"), GMC_YANG_PROPERTY_OPERATION_MERGE);
    idValue = -3;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    TestYangSetIDName(g_rootNode, "ID3", "level-1", strlen("level-1"), GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetIDName(g_childNode[1], "ID1", "level2", strlen("level2"), GMC_YANG_PROPERTY_OPERATION_MERGE);
    idValue = -3;
    TestYangSetIDValue(g_childNode[1], "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    TestYangSetIDName(g_childNode[1], "ID3", "level-1", strlen("level-1"), GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff022, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_1", "Yang_078_Func_022_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 023.container节点，Replace写入和Replace更新Identity字段，diff查询，subtree查询
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************replace insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_REPLACE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    TestYangSetIDName(g_childNode[1], "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_REPLACE);
    idValue = -2;
    TestYangSetIDValue(g_childNode[1], "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    TestYangSetIDName(g_childNode[1], "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff021, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_1", "Yang_078_Func_021_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************replace update***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值，ID3不写数据，Replace后变为默认值
    TestYangSetIDName(g_rootNode, "ID1", "level2", strlen("level2"), GMC_YANG_PROPERTY_OPERATION_REPLACE);
    idValue = -3;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值，ID3不写数据，Replace后变为默认值
    TestYangSetIDName(g_childNode[1], "ID1", "level2", strlen("level2"), GMC_YANG_PROPERTY_OPERATION_REPLACE);
    idValue = -3;
    TestYangSetIDValue(g_childNode[1], "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff023, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_1", "Yang_078_Func_023_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 024.container节点，Delete Identity字段，diff查询，subtree查询
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[1], "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_childNode[1], "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[1], "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff021, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_1", "Yang_078_Func_021_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************delete***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_DELETE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_DELETE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_DELETE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetIDName(g_childNode[1], "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_DELETE);
    idValue = -2;
    TestYangSetIDValue(g_childNode[1], "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_DELETE);
    TestYangSetIDName(g_childNode[1], "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_DELETE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff024, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_1", "Yang_078_Func_024_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 025.container节点，Remove Identity字段，diff查询，subtree查询
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[1], "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_childNode[1], "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[1], "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff021, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_1", "Yang_078_Func_021_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************remove***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_REMOVE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_REMOVE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_REMOVE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetIDName(g_childNode[1], "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_REMOVE);
    idValue = -2;
    TestYangSetIDValue(g_childNode[1], "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_REMOVE);
    TestYangSetIDName(g_childNode[1], "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_REMOVE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff024, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_1", "Yang_078_Func_024_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 026.Identity字段作为list主键，Insert数据，diff查询，subtree查询
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[1], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[1], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[1], "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值，只有PKID2写入不同的值
        TestYangSetIDName(g_childNode[2], "PKID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        idValue = i;
        TestYangSetIDValue(g_childNode[2], "PKID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[2], "PKID3", "level-1", strlen("level-1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff026, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_2", "Yang_078_Func_026_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 027.Identity字段作为list主键，Merge数据，diff查询，subtree查询
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************merge insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_MERGE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_1", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        char idName[10] = {0};
        ret = snprintf(idName, 10, "level%d", i);
        TestSetKeyNameAndValueIDName1(g_stmt_list[1], (const char*)idName, strlen(idName));
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
        TestYangSetIDName(g_childNode[1], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_MERGE);
        TestYangSetIDName(g_childNode[1], "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_MERGE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2_2", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值，只有PKID2写入不同的值
        char idName[10] = {0};
        ret = snprintf(idName, 10, "level%d", i);
        TestSetKeyNameAndValueIDName3(g_stmt_list[2], "level1", strlen("level1"),
            (const char*)idName, strlen(idName), "level-1", strlen("level-1"));

        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff026, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_2", "Yang_078_Func_026_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************merge update***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetIDName(g_rootNode, "ID1", "level2", strlen("level2"), GMC_YANG_PROPERTY_OPERATION_MERGE);
    idValue = -3;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    TestYangSetIDName(g_rootNode, "ID3", "level-1", strlen("level-1"), GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_1", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        char idName[10] = {0};
        ret = snprintf(idName, 10, "level%d", i);
        TestSetKeyNameAndValueIDName1(g_stmt_list[1], (const char*)idName, strlen(idName));

        fieldValue = 200;
        TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
        TestYangSetIDName(g_childNode[1], "ID2", "level3", strlen("level3"), GMC_YANG_PROPERTY_OPERATION_MERGE);
        TestYangSetIDName(g_childNode[1], "ID3", "level3", strlen("level3"), GMC_YANG_PROPERTY_OPERATION_MERGE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2_2", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值，只有PKID2写入不同的值
        char idName[10] = {0};
        ret = snprintf(idName, 10, "level%d", i);
        TestSetKeyNameAndValueIDName3(g_stmt_list[2], "level1", strlen("level1"),
            (const char*)idName, strlen(idName), "level-1", strlen("level-1"));

        fieldValue = 200;
        TestYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff027, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_2", "Yang_078_Func_027_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 028.Identity字段作为list主键，Replace数据，diff查询，subtree查询
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************replace insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_REPLACE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_1", GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[1], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        TestYangSetIDName(g_childNode[1], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_REPLACE);
        TestYangSetIDName(g_childNode[1], "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2_2", GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值，只有PKID2写入不同的值
        TestYangSetIDName(g_childNode[2], "PKID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_REPLACE);
        idValue = i;
        TestYangSetIDValue(g_childNode[2], "PKID2", idValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        TestYangSetIDName(g_childNode[2], "PKID3", "level-1", strlen("level-1"), GMC_YANG_PROPERTY_OPERATION_REPLACE);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff026, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_2", "Yang_078_Func_026_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************replace update***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_1", GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[1], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        fieldValue = 200;
        TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        TestYangSetIDName(g_childNode[1], "ID2", "level3", strlen("level3"), GMC_YANG_PROPERTY_OPERATION_REPLACE);
        TestYangSetIDName(g_childNode[1], "ID3", "level3", strlen("level3"), GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2_2", GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值，只有PKID2写入不同的值
        TestYangSetIDName(g_childNode[2], "PKID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_REPLACE);
        idValue = i;
        TestYangSetIDValue(g_childNode[2], "PKID2", idValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        TestYangSetIDName(g_childNode[2], "PKID3", "level-1", strlen("level-1"), GMC_YANG_PROPERTY_OPERATION_REPLACE);
        fieldValue = 200;
        TestYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff028, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_2", "Yang_078_Func_028_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 029.Identity字段作为list主键，Delete数据，diff查询，subtree查询
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[1], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[1], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[1], "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值，只有PKID2写入不同的值
        TestYangSetIDName(g_childNode[2], "PKID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        idValue = i;
        TestYangSetIDValue(g_childNode[2], "PKID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[2], "PKID3", "level-1", strlen("level-1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff026, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_2", "Yang_078_Func_026_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************delete***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 1; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_1", GMC_OPERATION_DELETE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestSetKeyNameAndValueIDValue1(g_stmt_list[1], idValue);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 1; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2_2", GMC_OPERATION_DELETE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值，只有PKID2写入不同的值
        idValue = i;
        TestSetKeyNameAndValueIDValue3(g_stmt_list[2], 1, idValue, -1);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(5, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(5, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff029, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_2", "Yang_078_Func_029_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 030.Identity字段作为list主键，Remove数据，diff查询，subtree查询
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[1], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[1], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[1], "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值，只有PKID2写入不同的值
        TestYangSetIDName(g_childNode[2], "PKID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        idValue = i;
        TestYangSetIDValue(g_childNode[2], "PKID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[2], "PKID3", "level-1", strlen("level-1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff026, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_2", "Yang_078_Func_026_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************remove***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 1; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_1", GMC_OPERATION_REMOVE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestSetKeyNameAndValueIDValue1(g_stmt_list[1], idValue);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 1; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2_2", GMC_OPERATION_REMOVE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值，只有PKID2写入不同的值
        idValue = i;
        TestSetKeyNameAndValueIDValue3(g_stmt_list[2], 1, idValue, -1);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(5, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(5, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff029, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_2", "Yang_078_Func_029_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 031.Identity字段作为list主键，改变list节点元素顺序，diff查询，subtree查询
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    GmcYangListLocatorT listProp;
    GmcPropValueT refKey;

    /***************************insert***********************************/
    AW_FUN_Log(LOG_INFO, "===============GMC_OPERATION_INSERT===============");
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -3; i < 4; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[1], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[1], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[1], "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(8, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(8, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_2", "Yang_078_Func_031_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************merge 改变顺序,GMC_YANG_LIST_POSITION_FIRST***********************************/
    AW_FUN_Log(LOG_INFO, "===============GMC_YANG_LIST_POSITION_FIRST===============");
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点,0挪到第一个
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_FIRST, NULL);
    ret = GmcYangSetListLocator(g_stmt_list[1], &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    idValue = 0;
    TestSetKeyNameAndValueIDName1(g_stmt_list[1], "level0", strlen("level0"));

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff031_01, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_2", "Yang_078_Func_031_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcResetStmt(g_stmt_list[1]);
    /***************************merge 改变顺序,GMC_YANG_LIST_POSITION_BEFORE***********************************/
    AW_FUN_Log(LOG_INFO, "===============GMC_YANG_LIST_POSITION_BEFORE===============");
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点,3挪到-1前面
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    idValue = -1;
    GmcAttributePropertyT attrProperty;
    attrProperty.type = GMC_ATTRIBUTE_VALUE;
    attrProperty.size = sizeof(int32_t);
    attrProperty.value = &idValue;

    refKey.propertyId = 1;
    refKey.propertyName[0] = 'P';
    refKey.propertyName[1] = 'K';
    refKey.propertyName[2] = 'I';
    refKey.propertyName[3] = 'D';
    refKey.propertyName[4] = '1';
    refKey.propertyName[5] = '\0';
    refKey.type = GMC_DATATYPE_IDENTITY;
    refKey.size = sizeof(attrProperty);
    refKey.value = (void *)&attrProperty;
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_BEFORE, &refKey);
    ret = GmcYangSetListLocator(g_stmt_list[1], &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    idValue = 3;
    TestSetKeyNameAndValueIDValue1(g_stmt_list[1], idValue);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff031_02, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_2", "Yang_078_Func_031_03");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************merge 改变顺序,GMC_YANG_LIST_POSITION_AFTER***********************************/
    AW_FUN_Log(LOG_INFO, "===============GMC_YANG_LIST_POSITION_AFTER===============");
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点,-2挪到2后面
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    idValue = 2;
    attrProperty.type = GMC_ATTRIBUTE_VALUE;
    attrProperty.size = sizeof(int32_t);
    attrProperty.value = &idValue;

    refKey.propertyId = 1;
    refKey.propertyName[0] = 'P';
    refKey.propertyName[1] = 'K';
    refKey.propertyName[2] = 'I';
    refKey.propertyName[3] = 'D';
    refKey.propertyName[4] = '1';
    refKey.propertyName[5] = '\0';
    refKey.type = GMC_DATATYPE_IDENTITY;
    refKey.size = sizeof(attrProperty);
    refKey.value = (void *)&attrProperty;
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_AFTER, &refKey);
    ret = GmcYangSetListLocator(g_stmt_list[1], &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    idValue = -2;
    TestSetKeyNameAndValueIDValue1(g_stmt_list[1], idValue);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff031_03, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_2", "Yang_078_Func_031_04");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 032.Identity字段作为list主键，多个元素Insert相同数据，
                验证唯一性，同时查询ErrorPath
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert数据***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[1], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[1], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[1], "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值，只有PKID2写入不同的值
        TestYangSetIDName(g_childNode[2], "PKID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        idValue = i;
        TestYangSetIDValue(g_childNode[2], "PKID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[2], "PKID3", "level-1", strlen("level-1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************insert 相同数据，1个主键***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    idValue = -1;
    TestYangSetIDValue(g_childNode[1], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 200;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data.isValidErrorPathInfo = true;
    data.expectedErrorCode = GMC_VIOLATES_CREATE;
    data.expectedErrMsg = "target exists";
    data.expectedErrPath = "/root_2/list_2_1[PKID1=\"level-1\"]";

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, data.status);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_2", "Yang_078_Func_026_01");

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************insert 相同数据，3个主键***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值，只有PKID2写入不同的值
    TestYangSetIDName(g_childNode[2], "PKID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -1;
    TestYangSetIDValue(g_childNode[2], "PKID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[2], "PKID3", "level-1", strlen("level-1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 200;
    TestYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data.isValidErrorPathInfo = true;
    data.expectedErrorCode = GMC_VIOLATES_CREATE;
    data.expectedErrMsg = "target exists";
    data.expectedErrPath = "/root_2/list_2_2[PKID1=\"level1\",PKID2=\"level-1\",PKID3=\"level-1\"]";

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, data.status);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_2", "Yang_078_Func_026_01");
    AddWhiteList(GMERR_UNIQUE_VIOLATION);
    AddWhiteList(GMERR_PRIMARY_KEY_VIOLATION);

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 033.Identity字段作为list唯一localhash，
                多个元素Insert和merge相同数据，验证唯一性，同时查询ErrorPath
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert数据***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_3", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值，PKID1[level1] ID2写入level1
    idValue = 1;
    TestYangSetIDValue(g_childNode[1], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[1], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_3", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值，PKID1[level2] ID2写入level2
    idValue = 2;
    TestYangSetIDValue(g_childNode[1], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[1], "ID2", "level2", strlen("level2"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************insert 相同数据***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_3", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值，PKID1[level3] ID2写入level1
    idValue = 3;
    TestYangSetIDValue(g_childNode[1], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[1], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data.isValidErrorPathInfo = true;
    data.expectedErrorCode = GMC_VIOLATES_UNIQUE;
    data.expectedErrMsg = "not satisfy field unique";
    data.expectedErrPath = "/root_2/list_2_3[PKID1=\"level3\"]";

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNIQUE_VIOLATION, data.status);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_2", "Yang_078_Func_033_01");

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************merge 相同数据***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2_3", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值，PKID1[level2] ID2改为level1
    TestSetKeyNameAndValueIDName1(g_stmt_list[2], "level2", strlen("level2"));
    TestYangSetIDName(g_childNode[2], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data.isValidErrorPathInfo = true;
    data.expectedErrorCode = GMC_VIOLATES_UNIQUE;
    data.expectedErrMsg = "not satisfy field unique";
    data.expectedErrPath = "/root_2/list_2_3[PKID1=\"level2\"]";

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNIQUE_VIOLATION, data.status);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_2", "Yang_078_Func_033_01");
    AddWhiteList(GMERR_UNIQUE_VIOLATION);
    AddWhiteList(GMERR_PRIMARY_KEY_VIOLATION);

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 034.两个Identity字段的enumrate相同，都作为list的主键，写入相同的值
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -3; i < 4; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        char idName[10] = {0};
        ret = snprintf(idName, 10, "level%d", i);
        TestYangSetIDName(g_childNode[2], "PKID1", (const char*)idName, strlen(idName),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[2], "PKID2", (const char*)idName, strlen(idName),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[2], "PKID3", (const char*)idName, strlen(idName),
            GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(8, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(8, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff034, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_2", "Yang_078_Func_034_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 035.Identity字段作为leaflist主键，Insert数据，diff查询，subtree查询
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_3", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_3_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        char idName[10] = {0};
        ret = snprintf(idName, 10, "level%d", i);
        TestYangSetIDName(g_childNode[1], "PKID1", (const char*)idName, strlen(idName),
            GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "leaflist_3_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        char idName[10] = {0};
        ret = snprintf(idName, 10, "level%d", i);
        TestYangSetIDName(g_childNode[2], "PKID1", (const char*)idName, strlen(idName),
            GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff035, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_3", "Yang_078_Func_035_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 036.Identity字段作为leaflist主键，Merge数据，diff查询，subtree查询
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_3", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_MERGE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_3_1", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        char idName[10] = {0};
        ret = snprintf(idName, 10, "level%d", i);
        TestSetKeyNameAndValueIDName1(g_stmt_list[1], (const char*)idName, strlen(idName));

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "leaflist_3_2", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        char idName[10] = {0};
        ret = snprintf(idName, 10, "level%d", i);
        TestSetKeyNameAndValueIDName1(g_stmt_list[2], (const char*)idName, strlen(idName));

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff035, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_3", "Yang_078_Func_035_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 037.Identity字段作为leaflist主键，Replace数据，diff查询，subtree查询
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_3", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_REPLACE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_3_1", GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        char idName[10] = {0};
        ret = snprintf(idName, 10, "level%d", i);
        TestYangSetIDName(g_childNode[1], "PKID1", (const char*)idName, strlen(idName),
            GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "leaflist_3_2", GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        char idName[10] = {0};
        ret = snprintf(idName, 10, "level%d", i);
        TestYangSetIDName(g_childNode[2], "PKID1", (const char*)idName, strlen(idName),
            GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff035, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_3", "Yang_078_Func_035_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 038.Identity字段作为leaflist主键，Delete数据，diff查询，subtree查询
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_3", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_3_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        char idName[10] = {0};
        ret = snprintf(idName, 10, "level%d", i);
        TestYangSetIDName(g_childNode[1], "PKID1", (const char*)idName, strlen(idName),
            GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "leaflist_3_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        char idName[10] = {0};
        ret = snprintf(idName, 10, "level%d", i);
        TestYangSetIDName(g_childNode[2], "PKID1", (const char*)idName, strlen(idName),
            GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_3", "Yang_078_Func_035_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************delete***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_3", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 1; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_3_1", GMC_OPERATION_DELETE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestSetKeyNameAndValueIDValue1(g_stmt_list[1], idValue);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 1; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "leaflist_3_2", GMC_OPERATION_DELETE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestSetKeyNameAndValueIDValue1(g_stmt_list[2], idValue);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(5, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(5, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff038, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_3", "Yang_078_Func_038_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 039.Identity字段作为leaflist主键，Remove数据，diff查询，subtree查询
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_3", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_3_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        char idName[10] = {0};
        ret = snprintf(idName, 10, "level%d", i);
        TestYangSetIDName(g_childNode[1], "PKID1", (const char*)idName, strlen(idName),
            GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "leaflist_3_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        char idName[10] = {0};
        ret = snprintf(idName, 10, "level%d", i);
        TestYangSetIDName(g_childNode[2], "PKID1", (const char*)idName, strlen(idName),
            GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_3", "Yang_078_Func_035_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************delete***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_3", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 1; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_3_1", GMC_OPERATION_REMOVE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestSetKeyNameAndValueIDValue1(g_stmt_list[1], idValue);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 1; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "leaflist_3_2", GMC_OPERATION_REMOVE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestSetKeyNameAndValueIDValue1(g_stmt_list[2], idValue);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(5, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(5, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff038, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_3", "Yang_078_Func_038_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 040.全打散建表，含有多个Identity字段，DML操作正常，diff查询，subtree查询
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_4", "Yang_078_Func_040_01");

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_4", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetVertexIDName(g_stmt_root, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetVertexIDValue(g_stmt_root, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetVertexIDName(g_stmt_root, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_4", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetVertexIDValue(g_stmt_list[2], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetVertexIDName(g_stmt_list[2], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetVertexIDName(g_stmt_list[2], "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[3], "leaflist_4", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        char idName[10] = {0};
        ret = snprintf(idName, 10, "level%d", i);
        TestYangSetVertexIDName(g_stmt_list[3], "PKID1", (const char*)idName, strlen(idName),
            GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff040, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_4", "Yang_078_Func_040_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 041.container节点，Insert写入Identity字段，写入value值，diff查询，subtree查询
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = 1;
    TestYangSetIDValue(g_rootNode, "ID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = 0;
    TestYangSetIDValue(g_rootNode, "ID3", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = 1;
    TestYangSetIDValue(g_childNode[1], "ID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_childNode[1], "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = 0;
    TestYangSetIDValue(g_childNode[1], "ID3", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff021, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_1", "Yang_078_Func_021_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 042.Identity字段作为list主键，Insert数据，写入value值，diff查询，subtree查询
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = 1;
    TestYangSetIDValue(g_rootNode, "ID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = 0;
    TestYangSetIDValue(g_rootNode, "ID3", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[1], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        idValue = 1;
        TestYangSetIDValue(g_childNode[1], "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDValue(g_childNode[1], "ID3", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值，只有PKID2写入不同的值
        idValue = 1;
        TestYangSetIDValue(g_childNode[2], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        idValue = i;
        TestYangSetIDValue(g_childNode[2], "PKID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        idValue = -1;
        TestYangSetIDValue(g_childNode[2], "PKID3", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff026, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_2", "Yang_078_Func_026_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 043.Identity字段作为list主键，Merge数据，写入value值，diff查询，subtree查询
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************merge insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_MERGE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_1", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestSetKeyNameAndValueIDValue1(g_stmt_list[1], idValue);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
        TestYangSetIDName(g_childNode[1], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_MERGE);
        TestYangSetIDName(g_childNode[1], "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_MERGE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2_2", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值，只有PKID2写入不同的值
        idValue = i;
        TestSetKeyNameAndValueIDValue3(g_stmt_list[2], 1, idValue, -1);

        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff026, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_2", "Yang_078_Func_026_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************merge update***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetIDName(g_rootNode, "ID1", "level2", strlen("level2"), GMC_YANG_PROPERTY_OPERATION_MERGE);
    idValue = -3;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    TestYangSetIDName(g_rootNode, "ID3", "level-1", strlen("level-1"), GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_1", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestSetKeyNameAndValueIDValue1(g_stmt_list[1], idValue);

        fieldValue = 200;
        TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
        TestYangSetIDName(g_childNode[1], "ID2", "level3", strlen("level3"), GMC_YANG_PROPERTY_OPERATION_MERGE);
        TestYangSetIDName(g_childNode[1], "ID3", "level3", strlen("level3"), GMC_YANG_PROPERTY_OPERATION_MERGE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2_2", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值，只有PKID2写入不同的值
        idValue = i;
        TestSetKeyNameAndValueIDValue3(g_stmt_list[2], 1, idValue, -1);

        fieldValue = 200;
        TestYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff027, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_2", "Yang_078_Func_027_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 044.Identity字段作为leaflist主键，Insert数据，写入value值，diff查询，subtree查询
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_3", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_3_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[1], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "leaflist_3_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[2], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff035, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_3", "Yang_078_Func_035_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 045.并发操作写入Identity字段数据
 Author       : hanyang
*****************************************************************************/
void *Thread_045_01(void *args)
{
    AW_FUN_Log(LOG_STEP, "==============[1] Yang CREATE start==================\n\n");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    GmcConnT *conn_async = NULL;
    GmcStmtT *stmt_root = NULL;
    GmcStmtT *stmt_child = NULL;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *childNode = NULL;
    AsyncUserDataT data = {0};

    uint32_t index = 1;
    ret = testGmcConnect(&conn_async, &stmt_root, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn_async, &stmt_child);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespaceAsync(stmt_root, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt_root, "root_2", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_root, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -3; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_child, "list_2_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt_root, stmt_child);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_child, &childNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(childNode, "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = 100;
        TestYangSetNodeProperty(childNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(childNode, "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(childNode, "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_child);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    // 插入时有可能主键冲突，会失败
    if (data.status != GMERR_OK) {
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));
        AW_FUN_Log(LOG_INFO, "==============[1] Yang CREATE trans fail.==================\n\n");

        // 回滚事务
        ret = TestTransRollBackAsync(conn_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));
        AW_FUN_Log(LOG_INFO, "==============[1] Yang CREATE trans succ.==================\n\n");

        // 提交事务
        ret = GmcTransCommitAsync(conn_async, trans_commit_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    GmcFreeStmt(stmt_child);
    ret = testGmcDisconnect(conn_async, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "==============[1] Yang CREATE end==================\n\n");
}

void *Thread_045_02(void *args)
{
    AW_FUN_Log(LOG_STEP, "==============[2] Yang MERGE start==================\n\n");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    GmcConnT *conn_async = NULL;
    GmcStmtT *stmt_root = NULL;
    GmcStmtT *stmt_child = NULL;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *childNode = NULL;
    AsyncUserDataT data = {0};

    uint32_t index = 2;
    ret = testGmcConnect(&conn_async, &stmt_root, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn_async, &stmt_child);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespaceAsync(stmt_root, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt_root, "root_2", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_root, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -3; i < 4; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_child, "list_2_1", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt_root, stmt_child);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_child, &childNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        char idName[10] = {0};
        ret = snprintf(idName, 10, "level%d", i);
        TestSetKeyNameAndValueIDName1(stmt_child, (const char*)idName, strlen(idName));
        fieldValue = 100;
        TestYangSetNodeProperty(childNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
        TestYangSetIDName(childNode, "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_MERGE);
        TestYangSetIDName(childNode, "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_MERGE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_child);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(8, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(8, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = GmcTransCommitAsync(conn_async, trans_commit_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (data.status == GMERR_RESTRICT_VIOLATION) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, data.status);
        AW_FUN_Log(LOG_INFO, "==============[2] Yang MERGE trans fail.==================\n\n");
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_FUN_Log(LOG_INFO, "==============[2] Yang MERGE trans succ.==================\n\n");
    }
    AW_FUN_Log(LOG_STEP, "==============[2] Yang MERGE end==================\n\n");

    GmcFreeStmt(stmt_child);
    ret = testGmcDisconnect(conn_async, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void *Thread_045_03(void *args)
{
    AW_FUN_Log(LOG_STEP, "==============[3] Yang REPLACE start==================\n\n");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    GmcConnT *conn_async = NULL;
    GmcStmtT *stmt_root = NULL;
    GmcStmtT *stmt_child = NULL;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *childNode = NULL;
    AsyncUserDataT data = {0};

    uint32_t index = 3;
    ret = testGmcConnect(&conn_async, &stmt_root, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn_async, &stmt_child);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespaceAsync(stmt_root, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt_root, "root_2", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_root, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -3; i < 4; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_child, "list_2_1", GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt_root, stmt_child);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_child, &childNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(childNode, "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        fieldValue = 100;
        TestYangSetNodeProperty(childNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        TestYangSetIDName(childNode, "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_REPLACE);
        TestYangSetIDName(childNode, "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_child);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(8, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(8, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = GmcTransCommitAsync(conn_async, trans_commit_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (data.status == GMERR_RESTRICT_VIOLATION) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, data.status);
        AW_FUN_Log(LOG_INFO, "==============[3] Yang REPLACE trans fail.==================\n\n");
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_FUN_Log(LOG_INFO, "==============[3] Yang REPLACE trans succ.==================\n\n");
    }
    AW_FUN_Log(LOG_STEP, "==============[3] Yang REPLACE end==================\n\n");

    GmcFreeStmt(stmt_child);
    ret = testGmcDisconnect(conn_async, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// main
TEST_F(YangIdentity_test, Yang_078_Func_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 3线程并发
    pthread_t Thread[3] = {0};

    pthread_create(&Thread[0], NULL, Thread_045_01, NULL);
    pthread_create(&Thread[1], NULL, Thread_045_02, NULL);
    pthread_create(&Thread[2], NULL, Thread_045_03, NULL);

    pthread_join(Thread[0], NULL);
    pthread_join(Thread[1], NULL);
    pthread_join(Thread[2], NULL);

    AddWhiteList(GMERR_RESTRICT_VIOLATION);
    AddWhiteList(GMERR_UNIQUE_VIOLATION);
    AddWhiteList(GMERR_PRIMARY_KEY_VIOLATION);
}

/*****************************************************************************
 Description  : 046.Identity字段作为list主键，Insert数据条数超过enumerate数量
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    GmcYangListLocatorT listProp;
    GmcPropValueT refKey;

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -3; i < 4; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[1], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[1], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[1], "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点，超过enumerate个数，不存在的enumerate
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    idValue = 4;
    GmcAttributePropertyT attrProperty;
    attrProperty.type = GMC_ATTRIBUTE_VALUE;
    attrProperty.size = sizeof(int32_t);
    attrProperty.value = &idValue;

    GmcPropValueT propValue;
    memcpy(propValue.propertyName, "PKID1", (strlen("PKID1") + 1));
    propValue.type = GMC_DATATYPE_IDENTITY;
    propValue.value = (void *)&attrProperty;
    propValue.size = sizeof(attrProperty);
    ret = GmcYangSetNodeProperty(g_childNode[1], &propValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_DATA_EXCEPTION);
}

/*****************************************************************************
 Description  : 047.Identity字段作为leaflist主键，默认值个数等于enumerate数量，插入新数据报错
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    GmcYangListLocatorT listProp;
    GmcPropValueT refKey;

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_4", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -3; i < 4; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_4", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        char idName[10] = {0};
        ret = snprintf(idName, 10, "level%d", i);
        TestYangSetIDName(g_childNode[1], "PKID1", (const char*)idName, strlen(idName),
            GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点，超过enumerate个数，不存在的enumerate
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_4", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    idValue = 4;
    GmcAttributePropertyT attrProperty;
    attrProperty.type = GMC_ATTRIBUTE_VALUE;
    attrProperty.size = sizeof(int32_t);
    attrProperty.value = &idValue;

    GmcPropValueT propValue;
    memcpy(propValue.propertyName, "PKID1", (strlen("PKID1") + 1));
    propValue.type = GMC_DATATYPE_IDENTITY;
    propValue.value = (void *)&attrProperty;
    propValue.size = sizeof(attrProperty);
    ret = GmcYangSetNodeProperty(g_childNode[1], &propValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_DATA_EXCEPTION);
}

/*****************************************************************************
 Description  : 048.NP节点，Identity字段有默认值，subtree查询，
                写入和默认值相同的数据，subtree查询，
                写入和默认值不同的数据，subtree查询，
                删除字段数据，subtree查询
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************查询默认值***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_6", "Yang_078_Func_048_01");

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************写入和默认值相同的数据，subtree查询***********************************/
    /***************************写入和默认值不同的数据，subtree查询***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_6", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_6_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    // ID1没有默认值，ID2写入和默认值相同值，ID3写入和默认值不同值，ID4有默认值，不写入
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[1], "ID1", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[1], "ID2", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[1], "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_6", "Yang_078_Func_048_02", GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);
    TestSubtreeFilterObjAll(g_stmt_async, "root_6", "Yang_078_Func_048_03", GMC_DEFAULT_FILTER_TRIM);
    TestSubtreeFilterObjAll(g_stmt_async, "root_6", "Yang_078_Func_048_04", GMC_DEFAULT_FILTER_EXPLICIT);

    // subtree查询，内容过滤
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root_6", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_6_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char idName[10] = "level0";
    ret = TestYangSetFieldID(g_childNode[1], GMC_DATATYPE_IDENTITY, idName, strlen(idName),
        "ID1", GMC_YANG_PROPERTY_OPERATION_CREATE, GMC_ATTRIBUTE_NAME);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_078_Func_048_05", "root_6",
        GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************删除字段数据，subtree查询***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_6", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_6_1", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值, 删除ID1和ID3
    TestYangSetIDName(g_childNode[1], "ID1", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_DELETE);
    TestYangSetIDName(g_childNode[1], "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_DELETE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_6", "Yang_078_Func_048_06", GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);
    TestSubtreeFilterObjAll(g_stmt_async, "root_6", "Yang_078_Func_048_07", GMC_DEFAULT_FILTER_TRIM);
    TestSubtreeFilterObjAll(g_stmt_async, "root_6", "Yang_078_Func_048_08", GMC_DEFAULT_FILTER_EXPLICIT);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 049.P节点，Identity字段有默认值，subtree查询，
                写入和默认值相同的数据，subtree查询，
                写入和默认值不同的数据，subtree查询，
                删除字段数据，subtree查询
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************查询默认值***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_6", "Yang_078_Func_049_01");

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************写入和默认值相同的数据，subtree查询***********************************/
    /***************************写入和默认值不同的数据，subtree查询***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_6", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_6_2", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    // ID1没有默认值，ID2写入和默认值相同值，ID3写入和默认值不同值，ID4有默认值，不写入
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[1], "ID1", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[1], "ID2", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[1], "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_6", "Yang_078_Func_049_02", GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);
    TestSubtreeFilterObjAll(g_stmt_async, "root_6", "Yang_078_Func_049_03", GMC_DEFAULT_FILTER_TRIM);
    TestSubtreeFilterObjAll(g_stmt_async, "root_6", "Yang_078_Func_049_04", GMC_DEFAULT_FILTER_EXPLICIT);

    // subtree查询，内容过滤
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root_6", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_6_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char idName[10] = "level0";
    ret = TestYangSetFieldID(g_childNode[1], GMC_DATATYPE_IDENTITY, idName, strlen(idName),
        "ID1", GMC_YANG_PROPERTY_OPERATION_CREATE, GMC_ATTRIBUTE_NAME);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_078_Func_049_05", "root_6",
        GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************删除字段数据，subtree查询***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_6", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_6_2", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值, 删除ID1和ID3
    TestYangSetIDName(g_childNode[1], "ID1", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_DELETE);
    TestYangSetIDName(g_childNode[1], "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_DELETE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_6", "Yang_078_Func_049_06", GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);
    TestSubtreeFilterObjAll(g_stmt_async, "root_6", "Yang_078_Func_049_07", GMC_DEFAULT_FILTER_TRIM);
    TestSubtreeFilterObjAll(g_stmt_async, "root_6", "Yang_078_Func_049_08", GMC_DEFAULT_FILTER_EXPLICIT);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 050.leaflist节点，Identity字段有默认值，subtree查询，
                写入和默认值相同的数据，subtree查询，
                写入和默认值不同的数据，subtree查询，
                删除写入的数据，subtree查询
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************查询默认值***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_3", "Yang_078_Func_050_01");

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************写入和默认值相同的数据，subtree查询***********************************/
    /***************************写入和默认值不同的数据，subtree查询***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_3", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -3; i < 0; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_3_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        char idName[10] = {0};
        ret = snprintf(idName, 10, "level%d", i);
        TestYangSetIDName(g_childNode[1], "PKID1", (const char*)idName, strlen(idName),
            GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(4, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(4, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_3", "Yang_078_Func_050_02", GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);
    TestSubtreeFilterObjAll(g_stmt_async, "root_3", "Yang_078_Func_050_03", GMC_DEFAULT_FILTER_TRIM);
    TestSubtreeFilterObjAll(g_stmt_async, "root_3", "Yang_078_Func_050_04", GMC_DEFAULT_FILTER_EXPLICIT);

    // subtree查询，内容过滤
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root_3", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "leaflist_3_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char idName[10] = "level0";
    ret = TestYangSetFieldID(g_childNode[1], GMC_DATATYPE_IDENTITY, idName, strlen(idName),
        "PKID1", GMC_YANG_PROPERTY_OPERATION_CREATE, GMC_ATTRIBUTE_NAME);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "leaflist_3_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char idName1[10] = "level-1";
    ret = TestYangSetFieldID(g_childNode[1], GMC_DATATYPE_IDENTITY, idName1, strlen(idName1),
        "PKID1", GMC_YANG_PROPERTY_OPERATION_CREATE, GMC_ATTRIBUTE_NAME);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_078_Func_050_05", "root_3",
        GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************删除字段数据，subtree查询***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_3", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -3; i < -1; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_3_2", GMC_OPERATION_DELETE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestSetKeyNameAndValueIDValue1(g_stmt_list[1], idValue);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_3", "Yang_078_Func_050_06", GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);
    TestSubtreeFilterObjAll(g_stmt_async, "root_3", "Yang_078_Func_050_07", GMC_DEFAULT_FILTER_TRIM);
    TestSubtreeFilterObjAll(g_stmt_async, "root_3", "Yang_078_Func_050_08", GMC_DEFAULT_FILTER_EXPLICIT);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 051.NP节点，Identity字段有默认值，when校验后默认值可见，
                subtree查询，when校验后默认值不可见，subtree查询
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async, GMC_YANG_VALIDATION_WHEN_FORCE);

    /*
    依赖关系                              | 写入数据                             | 预期结果
    con_7_1/ID1(/root_7/F0 = 100)         | con_7_1/ID1(/root_7/F0 = 100)           | con_7_1/ID1(默认值可见)
    con_7_1/ID2(/root_7/con_7_1/ID2 = 'level0') | con_7_1/ID2(/root_7/con_7_1/ID2 = 默认值'level0')  | con_7_1/ID2(默认值可见)
    */
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_7", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_7", "Yang_078_Func_051_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************修改when条件***********************************/
    /*
    依赖关系                              | 写入数据                             | 预期结果
    con_7_1/ID1(/root_7/F0 = 100)         | con_7_1/ID1(/root_7/F0 = 200)           | con_7_1/ID1(默认值不可见)
    con_7_1/ID2(/root_7/con_7_1/ID2 = 'level0') | con_7_1/ID2(/root_7/con_7_1/ID2 = 'level1')  | con_7_1/ID2(写入数据不可见，默认值可见)
    */
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_7", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_7_1", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    TestYangSetIDName(g_childNode[1], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_7", "Yang_078_Func_051_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 052.P节点，Identity字段有默认值，when校验后默认值可见，
                subtree查询，when校验后默认值不可见，subtree查询
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async, GMC_YANG_VALIDATION_WHEN_FORCE);

    /*
    依赖关系                              | 写入数据                             | 预期结果
    con_7_2/ID1(/root_7/F0 = 100)         | con_7_2/ID1(/root_7/F0 = 100)           | con_7_2/ID1(P节点都不可见)
    con_7_2/ID2(/root_7/con_7_2/ID2 = 'level1') | con_7_2/ID2(/root_7/con_7_2/ID2 = 默认值'level0')  | con_7_2/ID2(P节点都不可见)
    */
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_7", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_7", "Yang_078_Func_052_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************修改when条件***********************************/
    /*
    依赖关系                              | 写入数据                             | 预期结果
    con_7_2/ID1(/root_7/F0 = 100)         | con_7_2/ID1(/root_7/F0 = 200)           | con_7_2/ID1(默认值不可见)
    con_7_2/ID2(/root_7/con_7_2/ID2 = 'level1') | con_7_2/ID2(/root_7/con_7_2/ID2 = 'level1')  | con_7_2/ID2(写入数据可见)
    */
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_7", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_7_2", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    TestYangSetIDName(g_childNode[1], "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_MERGE);
    TestYangSetIDName(g_childNode[1], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_7", "Yang_078_Func_052_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 053.leaflist节点，Identity字段有默认值，when校验后默认值可见，
                subtree查询，when校验后默认值不可见，subtree查询
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async, GMC_YANG_VALIDATION_WHEN_FORCE);

    /*
    依赖关系                              | 写入数据                             | 预期结果
    leaflist_7_1(/root_7/F0 = 100)         | leaflist_7_1(/root_7/F0 = 100)           | leaflist_7_1(默认值可见)
    leaflist_7_2(/root_7/leaflist_7_2[PKID1='level1']/PKID1 = 'level1') | leaflist_7_2(PKID1默认值'level0''level-3')  | leaflist_7_2(节点都不可见)
    */
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_7", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_7", "Yang_078_Func_053_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************修改when条件***********************************/
    /*
    依赖关系                              | 写入数据                             | 预期结果
    leaflist_7_1(/root_7/F0 = 100)         | leaflist_7_1(/root_7/F0 = 200)           | leaflist_7_1(节点不可见，默认值不可见)
    leaflist_7_2(/root_7/leaflist_7_2[PKID1='level1']/PKID1 = 'level1') | leaflist_7_2(PKID1='level1')  | leaflist_7_2(节点可见)
    */
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_7", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_7_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        char idName[10] = {0};
        ret = snprintf(idName, 10, "level%d", i);
        TestYangSetIDName(g_childNode[1], "PKID1", (const char*)idName, strlen(idName),
            GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "leaflist_7_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        char idName[10] = {0};
        ret = snprintf(idName, 10, "level%d", i);
        TestYangSetIDName(g_childNode[2], "PKID1", (const char*)idName, strlen(idName),
            GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_7", "Yang_078_Func_053_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 054.设置subtree查询条件时，写入Identity字段的value，操作成功
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_6", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_6_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    // ID1没有默认值，ID2写入和默认值相同值，ID3写入和默认值不同值，ID4有默认值，不写入
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[1], "ID1", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[1], "ID2", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[1], "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_6", "Yang_078_Func_054_01", GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);
    TestSubtreeFilterObjAll(g_stmt_async, "root_6", "Yang_078_Func_054_02", GMC_DEFAULT_FILTER_TRIM);
    TestSubtreeFilterObjAll(g_stmt_async, "root_6", "Yang_078_Func_054_03", GMC_DEFAULT_FILTER_EXPLICIT);

    // subtree查询，内容过滤
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root_6", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_6_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    idValue = 0;
    ret = TestYangSetFieldID(g_childNode[1], GMC_DATATYPE_IDENTITY, &idValue, sizeof(int32_t),
        "ID1", GMC_YANG_PROPERTY_OPERATION_CREATE, GMC_ATTRIBUTE_VALUE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_078_Func_054_04", "root_6",
        GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 055.list节点，Identity字段为主键删除不存在的节点，查询ErrorPath
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert数据***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[1], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[1], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[1], "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(4, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(4, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************删除不存在的数据***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_1", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    idValue = 2;
    TestSetKeyNameAndValueIDValue1(g_stmt_list[1], idValue);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data.isValidErrorPathInfo = true;
    data.expectedErrorCode = GMC_VIOLATES_DELETE;
    data.expectedErrMsg = "target not exists";
    data.expectedErrPath = "/root_2/list_2_1[PKID1=\"level2\"]";

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_DATA_EXCEPTION);
}

/*****************************************************************************
 Description  : 056.case节点，Identity字段为case下的字段，删除不存在的字段值，查询ErrorPath
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert数据***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "choice_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = GmcYangEditChildNode(g_childNode[1], "case_1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[2], "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************删除不存在的数据***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "choice_1", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = GmcYangEditChildNode(g_childNode[1], "case_1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[2], "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data.isValidErrorPathInfo = true;
    data.expectedErrorCode = GMC_VIOLATES_CREATE;
    data.expectedErrMsg = "target exists";
    data.expectedErrPath = "/root_1/choice_1/case_1";

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, data.status);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_SYNTAX_ERROR);
}

/*****************************************************************************
 Description  : 057.不符合must校验，查询ErrorPath
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 模型校验
    ModelCheck(g_stmt_async);

    /*
    依赖关系                              | 写入数据                             | 预期结果
    con_7_3/ID1(/root_7/F0 = 300)         | con_7_3/ID1(/root_7/F0 = 100)           | con_7_3/ID1(字段不可见)
    */
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_7", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_7_3", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    TestYangSetIDName(g_childNode[1], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    bool isDataService = true;

    std::atomic_uint32_t step{0};
    GmcValidateResT validateRes {.validateRes = false, .failCount = 1};
    ValidateParam param = {
        .step = &step,
        .exceptStatus = GMERR_OK,
        .validateRes = validateRes,
        .isValidateErrorPath = true,
        .expectedErrCode = GMC_VIOLATES_MUST,
        .expectedErrClauseIndex = 0,
        .expectedErrMsg = "violated must clause 0",
        .expectedErrPath = "/root_7/con_7_3/ID1",
    };
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_MUST, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_root, &cfg, AsyncValidateCb, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitValidateAsyncRecv(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, param.exceptStatus);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 058.不符合leafref校验，查询ErrorPath
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 模型校验和数据校验
    ModelCheck(g_stmt_async);

    /*
    依赖关系                              | 写入数据                             | 预期结果
    con_7_3/ID2(/root_7/con_7_1/ID2)         | con_7_3/ID2(/root_7/con_7_1/ID2默认值不等)           | con_7_3/ID2(字段不可见)
    */
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_7", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 300;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_7_1", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 300;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    TestYangSetIDName(g_childNode[1], "ID2", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_7_3", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    TestYangSetIDName(g_childNode[2], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    bool isDataService = true;

    std::atomic_uint32_t step{0};
    GmcValidateResT validateRes {.validateRes = false, .failCount = 1};
    ValidateParam param = {
        .step = &step,
        .exceptStatus = GMERR_OK,
        .validateRes = validateRes,
        .isValidateErrorPath = true,
        .expectedErrCode = GMC_VIOLATES_LEAFREF,
        .expectedErrClauseIndex = 0,
        .expectedErrMsg = "violated leaf-ref clause 0",
        .expectedErrPath = "/root_7/con_7_3/ID2",
    };
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_LEAFREF, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_root, &cfg, AsyncValidateCb, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitValidateAsyncRecv(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, param.exceptStatus);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 059.subtree子树过滤查询YangTreeschema模型，返回结果正确
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_059)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 视图校验 Xpath 合法性
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s",
        g_toolPath, g_namespace, "root_4", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    ret = executeCommand(command, "root_4", "ID1", "identity", "level3", "level1/level2/level3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(command, 0, sizeof(command));
}

/*****************************************************************************
 Description  : 060.when条件包含derived-from和derived-from-or-self，
                数据都符合条件，查询校验结果
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_060)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 模型校验
    ModelCheck(g_stmt_async);

    /*
    依赖关系
    con_8/ID1:from(/root_8/con_8/ID1, 'level3')
    con_8/ID2:from-or-self(/root_8/con_8/ID2, 'level3')
    con_8/ID3:from(/root_8/con_8/ID3, 'level3'), 默认值level2，不符合
    con_8/ID4:from-or-self(/root_8/con_8/ID4, 'level3'), 默认值level6，符合
    con_8/ID5:from(/root_8/ID1, 'level3'), 默认值level2
            from(/root_8/ID2, 'level3')
            from(/root_8/ID3, 'level3')
    con_8/ID6:from-or-self(/root_8/ID1, 'level3'), 默认值level6
            from-or-self(/root_8/ID2, 'level3')
            from-or-self(/root_8/ID3, 'level3')

    list_8_1:from(/root_8/list_8_1/PKID1, 'level3')
            from(/root_8/list_8_1/PKID1, 'level4')
            from(/root_8/list_8_1/PKID1, 'level5')
    list_8_2:from-or-self(/root_8/list_8_2/PKID1, 'level3')
            from-or-self(/root_8/list_8_2/PKID1, 'level4')
            from-or-self(/root_8/list_8_2/PKID1, 'level5')

    leaflist_8_1:from(/root_8/leaflist_8_1/PKID1, 'level3'), 默认值level2,level3，不符合
    leaflist_8_2:from-or-self(/root_8/leaflist_8_2/PKID1, 'level3'), 默认值level2,level3，符合
    */

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_8", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = 5;
    TestYangSetIDValue(g_rootNode, "ID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDValue(g_rootNode, "ID3", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_8", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = 5;
    TestYangSetIDValue(g_childNode[1], "ID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDValue(g_childNode[1], "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDValue(g_childNode[1], "ID3", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDValue(g_childNode[1], "ID4", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDValue(g_childNode[1], "ID5", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDValue(g_childNode[1], "ID6", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = 6; i < 8; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_8_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[2], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[2], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[2], "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (int32_t i = 6; i < 8; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[3], "list_8_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[3], &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[3], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[3], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[3], "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = 6; i < 8; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[4], "leaflist_8_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[4]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[4], &g_childNode[4]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[4], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[4]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = 6; i < 8; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[5], "leaflist_8_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[5]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[5], &g_childNode[5]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[5], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[5]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(9, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(9, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_8", "Yang_078_Func_060_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 061.when条件包含derived-from和derived-from-or-self，
                数据部分符合条件，查询校验结果
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_061)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async, GMC_YANG_VALIDATION_WHEN_FORCE);

    /*
    依赖关系
    con_8/ID1:from(/root_8/con_8/ID1, 'level3')
    con_8/ID2:from-or-self(/root_8/con_8/ID2, 'level3')
    con_8/ID3:from(/root_8/con_8/ID3, 'level3'), 默认值level2，不符合
    con_8/ID4:from-or-self(/root_8/con_8/ID4, 'level3'), 默认值level6，符合
    con_8/ID5:from(/root_8/ID1, 'level3'), 默认值level2
            from(/root_8/ID2, 'level3')
            from(/root_8/ID3, 'level3')
    con_8/ID6:from-or-self(/root_8/ID1, 'level3'), 默认值level6
            from-or-self(/root_8/ID2, 'level3')
            from-or-self(/root_8/ID3, 'level3')

    list_8_1:from(/root_8/list_8_1/PKID1, 'level3')
            from(/root_8/list_8_1/PKID1, 'level4')
            from(/root_8/list_8_1/PKID1, 'level5')
    list_8_2:from-or-self(/root_8/list_8_2/PKID1, 'level3')
            from-or-self(/root_8/list_8_2/PKID1, 'level4')
            from-or-self(/root_8/list_8_2/PKID1, 'level5')

    leaflist_8_1:from(/root_8/leaflist_8_1/PKID1, 'level3'), 默认值level2,level3，不符合
    leaflist_8_2:from-or-self(/root_8/leaflist_8_2/PKID1, 'level3'), 默认值level2,level3，符合
    */

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_8", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = 3;
    TestYangSetIDValue(g_rootNode, "ID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDValue(g_rootNode, "ID3", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_8", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = 3;
    TestYangSetIDValue(g_childNode[1], "ID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);  // 不可见
    TestYangSetIDValue(g_childNode[1], "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);  // 可见
    // ID3默认值不可见
    // ID4默认值可见
    TestYangSetIDValue(g_childNode[1], "ID5", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);  // 不可见
    TestYangSetIDValue(g_childNode[1], "ID6", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);  // 可见

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点，不可见
    for (int32_t i = 3; i < 5; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_8_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[2], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[2], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[2], "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点，可见
    for (int32_t i = 5; i < 7; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[3], "list_8_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[3], &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[3], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[3], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[3], "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // leaflist_8_1，不可见，leaflist_8_2，可见

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(5, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(5, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_8", "Yang_078_Func_061_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 062.must条件包含derived-from和derived-from-or-self，
                数据都符合条件，查询校验结果
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_062)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    TestCreateLabel9(g_stmt_async);

    // 模型校验
    ModelCheck(g_stmt_async);

    /*
    依赖关系
    con_9/ID1:from(/root_9/con_9/ID1, 'level3')
    con_9/ID2:from-or-self(/root_9/con_9/ID2, 'level3')
    con_9/ID3:from(/root_9/con_9/ID3, 'level3'), 默认值level2，不符合
    con_9/ID4:from-or-self(/root_9/con_9/ID4, 'level3'), 默认值level6，符合
    con_9/ID5:from(/root_9/ID1, 'level3'), 默认值level2
            from(/root_9/ID2, 'level3')
            from(/root_9/ID3, 'level3')
    con_9/ID6:from-or-self(/root_9/ID1, 'level3'), 默认值level6
            from-or-self(/root_9/ID2, 'level3')
            from-or-self(/root_9/ID3, 'level3')

    list_9_1:from(/root_9/list_9_1/PKID1, 'level3')
            from(/root_9/list_9_1/PKID1, 'level4')
            from(/root_9/list_9_1/PKID1, 'level5')
    list_9_2:from-or-self(/root_9/list_9_2/PKID1, 'level3')
            from-or-self(/root_9/list_9_2/PKID1, 'level4')
            from-or-self(/root_9/list_9_2/PKID1, 'level5')

    leaflist_9_1:from(/root_9/leaflist_9_1/PKID1, 'level3'), 默认值level2,level3，不符合
    leaflist_9_2:from-or-self(/root_9/leaflist_9_2/PKID1, 'level3'), 默认值level2,level3，符合
    */

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_9", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = 5;
    TestYangSetIDValue(g_rootNode, "ID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDValue(g_rootNode, "ID3", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_9", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = 5;
    TestYangSetIDValue(g_childNode[1], "ID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDValue(g_childNode[1], "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDValue(g_childNode[1], "ID3", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDValue(g_childNode[1], "ID4", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDValue(g_childNode[1], "ID5", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDValue(g_childNode[1], "ID6", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = 6; i < 8; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_9_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[2], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[2], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[2], "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (int32_t i = 6; i < 8; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[3], "list_9_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[3], &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[3], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[3], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[3], "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = 6; i < 8; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[4], "leaflist_9_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[4]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[4], &g_childNode[4]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[4], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[4]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = 6; i < 8; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[5], "leaflist_9_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[5]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[5], &g_childNode[5]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[5], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[5]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(9, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(9, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true, GMERR_OK, GMERR_OK, GMC_YANG_VALIDATION_MUST);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_9", "Yang_078_Func_062_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestDropLabel9(g_stmt_async);
}

/*****************************************************************************
 Description  : 063.must条件包含derived-from和derived-from-or-self，
                数据部分符合条件，查询返回的ErrorPath
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_063)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    TestCreateLabel9(g_stmt_async);

    // 模型校验
    ModelCheck(g_stmt_async);

    /*
    依赖关系
    con_9/ID1:from(/root_9/con_9/ID1, 'level3')
    con_9/ID2:from-or-self(/root_9/con_9/ID2, 'level3')
    con_9/ID3:from(/root_9/con_9/ID3, 'level3'), 默认值level2，不符合
    con_9/ID4:from-or-self(/root_9/con_9/ID4, 'level3'), 默认值level6，符合
    con_9/ID5:from(/root_9/ID1, 'level3'), 默认值level2
            from(/root_9/ID2, 'level3')
            from(/root_9/ID3, 'level3')
    con_9/ID6:from-or-self(/root_9/ID1, 'level3'), 默认值level6
            from-or-self(/root_9/ID2, 'level3')
            from-or-self(/root_9/ID3, 'level3')

    list_9_1:from(/root_9/list_9_1/PKID1, 'level3')
            from(/root_9/list_9_1/PKID1, 'level4')
            from(/root_9/list_9_1/PKID1, 'level5')
    list_9_2:from-or-self(/root_9/list_9_2/PKID1, 'level3')
            from-or-self(/root_9/list_9_2/PKID1, 'level4')
            from-or-self(/root_9/list_9_2/PKID1, 'level5')

    leaflist_9_1:from(/root_9/leaflist_9_1/PKID1, 'level3'), 默认值level2,level3，不符合
    leaflist_9_2:from-or-self(/root_9/leaflist_9_2/PKID1, 'level3'), 默认值level2,level3，符合
    */

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_9", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = 3;
    TestYangSetIDValue(g_rootNode, "ID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDValue(g_rootNode, "ID3", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_9", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = 3;
    TestYangSetIDValue(g_childNode[1], "ID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);  // 不可见
    TestYangSetIDValue(g_childNode[1], "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);  // 可见
    // ID3默认值不可见
    // ID4默认值可见
    TestYangSetIDValue(g_childNode[1], "ID5", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);  // 不可见
    TestYangSetIDValue(g_childNode[1], "ID6", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);  // 可见

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点，不可见
    for (int32_t i = 3; i < 5; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_9_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[2], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[2], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[2], "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点，可见
    for (int32_t i = 5; i < 7; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[3], "list_9_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[3], &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[3], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[3], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[3], "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(5, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(5, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    bool isDataService = true;

    std::atomic_uint32_t step{0};
    GmcValidateResT validateRes {.validateRes = false, .failCount = 1};
    ValidateParam param = {
        .step = &step,
        .exceptStatus = GMERR_OK,
        .validateRes = validateRes,
        .isValidateErrorPath = true,
        .expectedErrCode = GMC_VIOLATES_MUST,
        .expectedErrClauseIndex = 0,
        .expectedErrMsg = "violated must clause 0",
        .expectedErrPath = "/root_9/con_9/ID1"
    };
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_MUST, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_root, &cfg, AsyncValidateCb, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitValidateAsyncRecv(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, param.exceptStatus);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestDropLabel9(g_stmt_async);
}

/*****************************************************************************
 Description  : 064.leafref条件包含derived-from和derived-from-or-self，模型校验报错
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_064)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    TestCreateLabel10(g_stmt_async);

    /*
    依赖关系
    con_10/ID1:from(/root_10/con_10/ID1, 'level3')
    con_10/ID2:from-or-self(/root_10/con_10/ID2, 'level3')
    */

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmt_async, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(false, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(2, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    TestDropLabel10(g_stmt_async);
    AddWhiteList(GMERR_DATA_EXCEPTION);
    AddWhiteList(GMERR_SYNTAX_ERROR);
}

/*****************************************************************************
 Description  : 065.list和leaflist节点的Xpath条件不指定key，进行数据校验
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_065)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    TestCreateLabel14(g_stmt_async);

    // 模型校验
    ModelCheck(g_stmt_async);

    /*
    依赖关系
    con_10/ID1:from(/root_10/con_10/ID1, 'level3')
    con_10/ID2:from-or-self(/root_10/con_10/ID2, 'level3')
    */

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_14", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_14", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[1], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[1], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[1], "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "leaflist_14", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[2], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_14", "Yang_078_Func_065_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************删除level1***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_14", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_14", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    idValue = 1;
    TestSetKeyNameAndValueIDValue1(g_stmt_list[1], idValue);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "leaflist_14", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    idValue = 1;
    TestSetKeyNameAndValueIDValue1(g_stmt_list[2], idValue);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_14", "Yang_078_Func_065_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestDropLabel14(g_stmt_async);
}

/*****************************************************************************
 Description  : 066.Identity字段作为xpath条件，与字符串比较，不使用函数，进行xpath校验
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_066)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async, GMC_YANG_VALIDATION_WHEN_FORCE);

    /*
    依赖关系                              | 写入数据                             | 预期结果
    con_7_1/ID1(/root_7/F0 = 100)         | con_7_1/ID1(/root_7/F0 = 100)           | con_7_1/ID1(默认值可见)
    con_7_1/ID2(/root_7/con_7_1/ID2 = 'level0') | con_7_1/ID2(/root_7/con_7_1/ID2 = 默认值'level0')  | con_7_1/ID2(默认值可见)
    */
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_7", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_7", "Yang_078_Func_051_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************修改when条件***********************************/
    /*
    依赖关系                              | 写入数据                             | 预期结果
    con_7_1/ID1(/root_7/F0 = 100)         | con_7_1/ID1(/root_7/F0 = 200)           | con_7_1/ID1(默认值不可见)
    con_7_1/ID2(/root_7/con_7_1/ID2 = 'level0') | con_7_1/ID2(/root_7/con_7_1/ID2 = 'level1')  | con_7_1/ID2(写入数据不可见，默认值可见)
    */
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_7", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_7_1", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    TestYangSetIDName(g_childNode[1], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_7", "Yang_078_Func_051_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 067.Identity字段作为xpath条件，与数字比较，不使用函数，进行xpath校验，模型校验报错
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_067)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    GmcConnT *conn_async = NULL;
    GmcStmtT *stmt_async = NULL;
    ret = testGmcConnect(&conn_async, &stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = "ns78";
    nspCfg.tablespaceName = NULL;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观

    ret = GmcCreateNamespaceWithCfgAsync(stmt_async, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcUseNamespaceAsync(stmt_async, "ns78", use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 创建表
    TestCreateLabel11(stmt_async);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(stmt_async, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(false, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(1, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 删除表
    TestDropLabel11(stmt_async);

    // 异步删除namespace
    ret = GmcDropNamespaceAsync(stmt_async, "ns78", drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = testGmcDisconnect(conn_async, stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_SYNTAX_ERROR);
    AddWhiteList(GMERR_DATA_EXCEPTION);
}

/*****************************************************************************
 Description  : 068.when增量校验，条件包含derived-from和derived-from-or-self，
                数据都符合条件，查询校验结果
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_068)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 模型校验
    ModelCheck(g_stmt_async);

    /*
    依赖关系
    con_17/ID1:from(/root_17/con_17/ID1, 'level3')
    con_17/ID2:from-or-self(/root_17/con_17/ID2, 'level3')

    list_17_1:from(/root_17/list_17_1/PKID1, 'level3')
    list_17_2:from-or-self(/root_17/list_17_2/PKID1, 'level3')

    leaflist_17_1:from(/root_17/leaflist_17_1/PKID1, 'level3')
    leaflist_17_2:from-or-self(/root_17/leaflist_17_2/PKID1, 'level3')
    */

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_17", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = 5;
    TestYangSetIDValue(g_rootNode, "ID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDValue(g_rootNode, "ID3", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_17", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = 5;
    TestYangSetIDValue(g_childNode[1], "ID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDValue(g_childNode[1], "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = 6; i < 8; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_17_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[2], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[2], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[2], "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (int32_t i = 6; i < 8; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[3], "list_17_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[3], &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[3], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[3], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[3], "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = 6; i < 8; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[4], "leaflist_17_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[4]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[4], &g_childNode[4]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[4], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[4]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = 6; i < 8; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[5], "leaflist_17_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[5]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[5], &g_childNode[5]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[5], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[5]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(9, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(9, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true, GMERR_OK, GMERR_OK, GMC_YANG_VALIDATION_WHEN);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_17", "Yang_078_Func_068_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 069.when增量校验，条件包含derived-from和derived-from-or-self，
                数据部分符合条件，查询返回的ErrorPath
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_069)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 模型校验
    ModelCheck(g_stmt_async);

    /*
    依赖关系
    con_17/ID1:from(/root_17/con_17/ID1, 'level3')
    con_17/ID2:from-or-self(/root_17/con_17/ID2, 'level3')

    list_17_1:from(/root_17/list_17_1/PKID1, 'level3')
    list_17_2:from-or-self(/root_17/list_17_2/PKID1, 'level3')

    leaflist_17_1:from(/root_17/leaflist_17_1/PKID1, 'level3')
    leaflist_17_2:from-or-self(/root_17/leaflist_17_2/PKID1, 'level3')
    */

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_17", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = 3;
    TestYangSetIDValue(g_rootNode, "ID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDValue(g_rootNode, "ID3", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_17", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = 3;
    TestYangSetIDValue(g_childNode[1], "ID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);  // 不可见
    TestYangSetIDValue(g_childNode[1], "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);  // 可见

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点，不可见
    for (int32_t i = 3; i < 5; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_17_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[2], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[2], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[2], "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点，可见
    for (int32_t i = 5; i < 7; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[3], "list_17_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[3], &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[3], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[3], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[3], "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // leaflist_17_1，不可见，leaflist_17_2，可见

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(5, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(5, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    bool isDataService = true;

    std::atomic_uint32_t step{0};
    GmcValidateResT validateRes {.validateRes = false, .failCount = 1};
    ValidateParam param = {
        .step = &step,
        .exceptStatus = GMERR_OK,
        .validateRes = validateRes,
        .isValidateErrorPath = true,
        .expectedErrCode = GMC_VIOLATES_WHEN,
        .expectedErrClauseIndex = 0,
        .expectedErrMsg = "violated when clause 0",
        .expectedErrPath = "/root_17/con_17/ID1"
    };
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_WHEN, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_root, &cfg, AsyncValidateCb, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitValidateAsyncRecv(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, param.exceptStatus);

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 070.xpath条件包含新函数and其他条件，查询校验结果
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_070)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 模型校验
    ModelCheck(g_stmt_async);

    /*
    依赖关系
    con_12/ID1:from(derived-from(/root_12/con_12/ID1, 'level3') and /root_12/F0 = 100)
    con_12/ID2:from-or-self(derived-from-or-self(/root_12/con_12/ID2, 'level3') and /root_12/F0 = 100)
    con_12/ID3:from(derived-from(/root_12/con_12/ID3, 'level3') or /root_12/F0 = 100)
    con_12/ID4:from-or-self(derived-from-or-self(/root_12/con_12/ID4, 'level3') or /root_12/F0 = 100)
    */

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_12", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = 5;
    TestYangSetIDValue(g_rootNode, "ID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDValue(g_rootNode, "ID3", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_12", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = 5;
    TestYangSetIDValue(g_childNode[1], "ID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDValue(g_childNode[1], "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDValue(g_childNode[1], "ID3", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDValue(g_childNode[1], "ID4", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_12", "Yang_078_Func_070_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_12", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_12", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    idValue = 1;
    TestYangSetIDValue(g_childNode[1], "ID1", idValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    TestYangSetIDValue(g_childNode[1], "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    TestYangSetIDValue(g_childNode[1], "ID3", idValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    TestYangSetIDValue(g_childNode[1], "ID4", idValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_12", "Yang_078_Func_070_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 071.xpath条件包含新函数or其他条件，查询校验结果
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_071)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 模型校验
    ModelCheck(g_stmt_async);

    /*
    依赖关系
    con_12/ID1:from(derived-from(/root_12/con_12/ID1, 'level3') and /root_12/F0 = 100)
    con_12/ID2:from-or-self(derived-from-or-self(/root_12/con_12/ID2, 'level3') and /root_12/F0 = 100)
    con_12/ID3:from(derived-from(/root_12/con_12/ID3, 'level3') or /root_12/F0 = 100)
    con_12/ID4:from-or-self(derived-from-or-self(/root_12/con_12/ID4, 'level3') or /root_12/F0 = 100)
    */

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_12", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = 5;
    TestYangSetIDValue(g_rootNode, "ID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDValue(g_rootNode, "ID3", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_12", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = 5;
    TestYangSetIDValue(g_childNode[1], "ID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDValue(g_childNode[1], "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDValue(g_childNode[1], "ID3", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDValue(g_childNode[1], "ID4", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_12", "Yang_078_Func_071_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_12", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_12", "Yang_078_Func_071_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 072.xpath条件包含多条语句，包含新函数，全部满足条件，查询校验结果
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_072)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 模型校验
    ModelCheck(g_stmt_async);

    /*
    依赖关系
    con_13/ID1:when:derived-from(/root_13/con_13/ID1, 'level3')
               when:derived-from-or-self(/root_13/con_13/ID2, 'level3')
               must:derived-from(/root_8/ID1, 'level3')
               must:derived-from-or-self(/root_8/ID2, 'level3')
               leafref:/root_8/ID3
    */

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_13", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = 5;
    TestYangSetIDValue(g_rootNode, "ID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDValue(g_rootNode, "ID3", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_13", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = 5;
    TestYangSetIDValue(g_childNode[1], "ID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true, GMERR_OK, GMERR_OK, GMC_YANG_VALIDATION_ALL_FORCE);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_13", "Yang_078_Func_072_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 073.xpath条件包含多条语句，包含新函数，部分满足条件，查询校验结果
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_073)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 模型校验
    ModelCheck(g_stmt_async);

    /*
    依赖关系
    con_13/ID1:when:derived-from(/root_13/con_13/ID1, 'level3')
               when:derived-from-or-self(/root_13/con_13/ID2, 'level3')
               must:derived-from(/root_8/ID1, 'level3')
               must:derived-from-or-self(/root_8/ID2, 'level3')
               leafref:/root_8/ID3
    */

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_13", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = 5;
    TestYangSetIDValue(g_rootNode, "ID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = 3;
    TestYangSetIDValue(g_rootNode, "ID3", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_13", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = 5;
    TestYangSetIDValue(g_childNode[1], "ID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    bool isDataService = true;

    std::atomic_uint32_t step{0};
    GmcValidateResT validateRes {.validateRes = false, .failCount = 1};
    ValidateParam param = {
        .step = &step,
        .exceptStatus = GMERR_OK,
        .validateRes = validateRes,
        .isValidateErrorPath = true,
        .expectedErrCode = GMC_VIOLATES_LEAFREF,
        .expectedErrClauseIndex = 0,
        .expectedErrMsg = "violated leaf-ref clause 0",
        .expectedErrPath = "/root_13/con_13/ID1",
    };
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_root, &cfg, AsyncValidateCb, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitValidateAsyncRecv(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, param.exceptStatus);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 074.derived-path的路径实际不存在，使用新函数进行when校验，查询校验结果
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_074)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    TestCreateLabel15(g_stmt_async);

    // 模型校验
    ModelCheck(g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_15", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点，写入符合when校验的数据，由于derived-path错误，ID1不可见
    ret = GmcYangEditChildNode(g_rootNode, "con_15", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = 5;
    TestYangSetIDValue(g_childNode[1], "ID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = 3;
    TestYangSetIDValue(g_childNode[1], "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_15", "Yang_078_Func_074_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestDropLabel15(g_stmt_async);
}

/*****************************************************************************
 Description  : 075.使用新增接口通过name查询value
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_075)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_16", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_16", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[1], "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_childNode[1], "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[1], "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_16", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[2], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[2], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[2], "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[3], "leaflist_16", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[3], &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[3], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************通过name查询value***********************************/
    GmcPropValueT propValueIn = {0};
    GmcPropValueT propValueInList = {0};
    GmcPropValueT propValueOut = {0};
    int32_t idValueOut = 0;
    uint32_t idSizeOut = sizeof(int32_t);
    propValueOut = {.propertyId = 0,
        .propertyName = {0},
        .type = GMC_DATATYPE_BUTT,
        .size = idSizeOut,
        .value = (void *)&idValueOut};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_16", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************root vertex***********************************/
    char idName[10] = "level1";
    propValueIn = {.propertyId = 10,
        .propertyName = {'I', 'D', '1', 0},
        .type = GMC_DATATYPE_IDENTITY,
        .size = (uint32_t)strlen(idName),
        .value = (void *)idName};
    ret = GmcYangConvertVertexProperty(g_stmt_root, GMC_CONVERT_STR_TO_INT, &propValueIn, &propValueOut);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, *(int32_t *)propValueOut.value);
    AW_MACRO_EXPECT_EQ_INT(sizeof(int32_t), propValueOut.size);
    AW_MACRO_EXPECT_EQ_INT(10, propValueOut.propertyId);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_IDENTITY, propValueOut.type);
    AW_MACRO_EXPECT_EQ_STR("ID1", propValueOut.propertyName);

    /***************************root node***********************************/
    // 初始化out
    propValueOut.propertyId = 0;
    propValueOut.type = GMC_DATATYPE_BUTT;
    memset(propValueOut.propertyName, 0, GMC_PROPERTY_NAME_MAX_LEN);
    propValueOut.size = idSizeOut;
    idValueOut = 0;
    ret = GmcYangConvertNodeProperty(g_rootNode, GMC_CONVERT_STR_TO_INT, &propValueIn, &propValueOut);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, *(int32_t *)propValueOut.value);
    AW_MACRO_EXPECT_EQ_INT(sizeof(int32_t), propValueOut.size);
    AW_MACRO_EXPECT_EQ_INT(10, propValueOut.propertyId);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_IDENTITY, propValueOut.type);
    AW_MACRO_EXPECT_EQ_STR("ID1", propValueOut.propertyName);

    /***************************con node***********************************/
    // 初始化out
    propValueOut.size = idSizeOut;
    idValueOut = 0;
    ret = GmcYangEditChildNode(g_rootNode, "con_16", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangConvertNodeProperty(g_childNode[1], GMC_CONVERT_STR_TO_INT, &propValueIn, &propValueOut);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, *(int32_t *)propValueOut.value);
    AW_MACRO_EXPECT_EQ_INT(sizeof(int32_t), propValueOut.size);

    /***************************list vertex***********************************/
    char idNameList[10] = "level-1";
    propValueInList = {.propertyId = 0,
        .propertyName = {'P', 'K', 'I', 'D', '1', 0},
        .type = GMC_DATATYPE_IDENTITY,
        .size = (uint32_t)strlen(idNameList),
        .value = (void *)idNameList};

    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_16", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 初始化out
    propValueOut.size = idSizeOut;
    idValueOut = 0;
    ret = GmcYangConvertVertexProperty(g_stmt_list[2], GMC_CONVERT_STR_TO_INT, &propValueInList, &propValueOut);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(-1, *(int32_t *)propValueOut.value);
    AW_MACRO_EXPECT_EQ_INT(sizeof(int32_t), propValueOut.size);

    /***************************list node***********************************/
    // 初始化out
    propValueOut.size = idSizeOut;
    idValueOut = 0;
    ret = GmcYangConvertNodeProperty(g_childNode[2], GMC_CONVERT_STR_TO_INT, &propValueInList, &propValueOut);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(-1, *(int32_t *)propValueOut.value);
    AW_MACRO_EXPECT_EQ_INT(sizeof(int32_t), propValueOut.size);

    /***************************leaflist vertex***********************************/
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[3], "leaflist_16", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[3], &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 初始化out
    propValueOut.size = idSizeOut;
    idValueOut = 0;
    ret = GmcYangConvertVertexProperty(g_stmt_list[3], GMC_CONVERT_STR_TO_INT, &propValueInList, &propValueOut);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(-1, *(int32_t *)propValueOut.value);
    AW_MACRO_EXPECT_EQ_INT(sizeof(int32_t), propValueOut.size);

    /***************************leaflist node***********************************/
    // 初始化out
    propValueOut.size = idSizeOut;
    idValueOut = 0;
    ret = GmcYangConvertNodeProperty(g_childNode[3], GMC_CONVERT_STR_TO_INT, &propValueInList, &propValueOut);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(-1, *(int32_t *)propValueOut.value);
    AW_MACRO_EXPECT_EQ_INT(sizeof(int32_t), propValueOut.size);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 076.使用新增接口通过value查询name
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_076)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_16", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_16", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[1], "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_childNode[1], "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[1], "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_16", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[2], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[2], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[2], "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[3], "leaflist_16", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[3], &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[3], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************通过value查询name***********************************/
    GmcPropValueT propValueIn = {0};
    GmcPropValueT propValueInList = {0};
    GmcPropValueT propValueOut = {0};
    char idNameOut[10] = {0};
    uint32_t idSizeOut = 10;
    propValueOut = {.propertyId = 0,
        .propertyName = {0},
        .type = GMC_DATATYPE_IDENTITY,
        .size = idSizeOut,
        .value = (void *)idNameOut};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_16", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************root vertex***********************************/
    char idName[10] = "level1";
    int32_t idValueOut = 1;
    propValueIn = {.propertyId = 10,
        .propertyName = {'I', 'D', '1', 0},
        .type = GMC_DATATYPE_IDENTITY,
        .size = sizeof(int32_t),
        .value = (void *)&idValueOut};
    ret = GmcYangConvertVertexProperty(g_stmt_root, GMC_CONVERT_INT_TO_STR, &propValueIn, &propValueOut);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_STR(idName, (char *)propValueOut.value);
    AW_MACRO_EXPECT_EQ_INT((strlen(idName) + 1), propValueOut.size);
    AW_MACRO_EXPECT_EQ_INT(10, propValueOut.propertyId);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_IDENTITY, propValueOut.type);
    AW_MACRO_EXPECT_EQ_STR("ID1", propValueOut.propertyName);

    /***************************root node***********************************/
    // 初始化out
    propValueOut.size = idSizeOut;
    memset(idNameOut, 0, idSizeOut);
    ret = GmcYangConvertNodeProperty(g_rootNode, GMC_CONVERT_INT_TO_STR, &propValueIn, &propValueOut);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_STR(idName, (char *)propValueOut.value);
    AW_MACRO_EXPECT_EQ_INT((strlen(idName) + 1), propValueOut.size);
    AW_MACRO_EXPECT_EQ_INT(10, propValueOut.propertyId);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_IDENTITY, propValueOut.type);
    AW_MACRO_EXPECT_EQ_STR("ID1", propValueOut.propertyName);

    /***************************con node***********************************/
    // 初始化out
    propValueOut.size = idSizeOut;
    memset(idNameOut, 0, idSizeOut);
    ret = GmcYangEditChildNode(g_rootNode, "con_16", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangConvertNodeProperty(g_childNode[1], GMC_CONVERT_INT_TO_STR, &propValueIn, &propValueOut);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_STR(idName, (char *)propValueOut.value);
    AW_MACRO_EXPECT_EQ_INT((strlen(idName) + 1), propValueOut.size);

    /***************************list vertex***********************************/
    char idNameList[10] = "level-1";
    int32_t idValueOutList = -1;
    propValueInList = {.propertyId = 0,
        .propertyName = {'P', 'K', 'I', 'D', '1', 0},
        .type = GMC_DATATYPE_IDENTITY,
        .size = sizeof(int32_t),
        .value = (void *)&idValueOutList};

    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_16", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 初始化out
    propValueOut.size = idSizeOut;
    memset(idNameOut, 0, idSizeOut);
    ret = GmcYangConvertVertexProperty(g_stmt_list[2], GMC_CONVERT_INT_TO_STR, &propValueInList, &propValueOut);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_STR(idNameList, (char *)propValueOut.value);
    AW_MACRO_EXPECT_EQ_INT((strlen(idNameList) + 1), propValueOut.size);

    /***************************list node***********************************/
    // 初始化out
    propValueOut.size = idSizeOut;
    memset(idNameOut, 0, idSizeOut);
    ret = GmcYangConvertNodeProperty(g_childNode[2], GMC_CONVERT_INT_TO_STR, &propValueInList, &propValueOut);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_STR(idNameList, (char *)propValueOut.value);
    AW_MACRO_EXPECT_EQ_INT((strlen(idNameList) + 1), propValueOut.size);

    /***************************leaflist vertex***********************************/
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[3], "leaflist_16", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[3], &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 初始化out
    propValueOut.size = idSizeOut;
    memset(idNameOut, 0, idSizeOut);
    ret = GmcYangConvertVertexProperty(g_stmt_list[3], GMC_CONVERT_INT_TO_STR, &propValueInList, &propValueOut);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_STR(idNameList, (char *)propValueOut.value);
    AW_MACRO_EXPECT_EQ_INT((strlen(idNameList) + 1), propValueOut.size);

    /***************************leaflist node***********************************/
    // 初始化out
    propValueOut.size = idSizeOut;
    memset(idNameOut, 0, idSizeOut);
    ret = GmcYangConvertNodeProperty(g_childNode[3], GMC_CONVERT_INT_TO_STR, &propValueInList, &propValueOut);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_STR(idNameList, (char *)propValueOut.value);
    AW_MACRO_EXPECT_EQ_INT((strlen(idNameList) + 1), propValueOut.size);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 077.校验diff相关接口出参是否正确
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_077)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 设置是否只有一个ID字段，true时，校验diff相关接口出参返回值，077用例专用
    g_OneIdFieldFlag = true;

    /***********************create*******************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_18", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff077_01, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_18", "Yang_078_Func_077_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***********************update*******************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_18", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetIDName(g_rootNode, "ID1", "level3", strlen("level3"), GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff077_02, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_18", "Yang_078_Func_077_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***********************delete*******************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_18", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetIDName(g_rootNode, "ID1", "level3", strlen("level3"), GMC_YANG_PROPERTY_OPERATION_DELETE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff077_03, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_18", "Yang_078_Func_077_03");
    g_OneIdFieldFlag = false;

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 078.问题单DTS2024042607491补充用例
 Author       : hanyang
*****************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_078)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert数据***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    char path[128] = {0};
    GmcLostPathInfoT pathInfo = {.path = path, .totalPathLen = 128, .actualPathLen = 0};

    // 获取choice case path, 校验报错的情况
    ret = GmcYangGetChoiceCasePath(g_rootNode, "root_1", GMC_YANG_TYPE_VERTEX, &pathInfo);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = GmcYangGetChoiceCasePath(g_rootNode, "root_1", GMC_YANG_TYPE_NODE, &pathInfo);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = GmcYangGetChoiceCasePath(g_rootNode, "root_1", GMC_YANG_TYPE_FIELD, &pathInfo);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    // 成功的情况
    ret = GmcYangGetChoiceCasePath(g_rootNode, "ID4", GMC_YANG_TYPE_FIELD, &pathInfo);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_STR("choice_1/case_1", pathInfo.path);
    AW_MACRO_EXPECT_EQ_INT(128, pathInfo.totalPathLen);
    AW_MACRO_EXPECT_EQ_INT(16, pathInfo.actualPathLen);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, pathInfo.path, GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[2], "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[2], "ID4", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // GmcYangEditChildNode接口测试
    ret = GmcYangEditChildNode(g_rootNode, "root_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_NAME, ret);
    ret = GmcYangEditChildNode(g_rootNode, "root_1", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_NAME, ret);
    ret = GmcYangEditChildNode(g_rootNode, "root_1", GMC_OPERATION_DELETE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_NAME, ret);
    ret = GmcYangEditChildNode(g_rootNode, "root_1", GMC_OPERATION_REMOVE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_NAME, ret);
    ret = GmcYangEditChildNode(g_rootNode, "root_1", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_NAME, ret);
    ret = GmcYangEditChildNode(g_rootNode, "root_1", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_NAME, ret);
    ret = GmcYangEditChildNode(g_rootNode, "root_1", GMC_OPERATION_SCAN, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_VALUE, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询，GmcYangEditChildNode接口测试
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "root_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_1", "Yang_078_Func_078_01");
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    AddWhiteList(GMERR_INVALID_NAME);
    AddWhiteList(GMERR_DATA_EXCEPTION);
    AddWhiteList(GMERR_INVALID_VALUE);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*************************************************************************************************
 Description  : 079.问题单DTS2024050704571补充用例，开启debug日志，设置枚举或identity，不会core
 Author       : z00619264
*************************************************************************************************/
TEST_F(YangIdentity_test, Yang_078_Func_079)
{
    AW_CHECK_LOG_BEGIN(0);

    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 开启debug日志
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    system("gmlog -p Identity -l 4");

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[1], "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_childNode[1], "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[1], "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff021, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_1", "Yang_078_Func_021_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
}
