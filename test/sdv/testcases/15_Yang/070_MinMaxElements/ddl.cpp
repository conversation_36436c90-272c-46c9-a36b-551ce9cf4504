#include "tools.h"

class min_max_element_ddl_test : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void min_max_element_ddl_test::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/start.sh");
}

void min_max_element_ddl_test::TearDownTestCase(){}

void min_max_element_ddl_test::SetUp()
{
    int ret;
    AsyncUserDataT data = {0};
    
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步建连
    ret = testGmcConnect(&g_connAsync, &g_stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步创建namespace
    GmcDropNamespace(g_stmtAsync, g_namespace);
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = g_namespace;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观
    GmcDropNamespace(g_stmtAsync, g_namespace);
    ret = GmcCreateNamespaceWithCfgAsync(g_stmtAsync, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcUseNamespaceAsync(g_stmtAsync, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    g_replayIndex = 0;
    // 初始化随机数发生器
    time_t t;
    srand((unsigned) time(&t));
    system("rm -rf json");
    system("mkdir json");
}

void min_max_element_ddl_test::TearDown()
{
    int ret;
    system("rm -rf json");
    AsyncUserDataT data = {0};

    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmtAsync, g_namespace, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 断连
    ret = testGmcDisconnect(g_connAsync, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();
    GmcDetachAllShmSeg();
	testEnvClean();
}

// min/max-elements 定义在container、choice、case节点和所有类型节点下的字段上，建表
// 备注：在节点上使用超限的min-maxelements会导致建表失败，在字段上则不会
TEST_F(min_max_element_ddl_test, Yang_070_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 建表
    AsyncUserDataT data = {0};
    ret = CreateVertexLabelAsync(g_stmtAsync, "schema/vertex_label_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexLabelAsync(g_stmtAsync, "schema/vertex_label_2.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删表
    ret = DropVertexLabelAsync(g_stmtAsync, "vertex_label_1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropVertexLabelAsync(g_stmtAsync, "vertex_label_2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 2.min/max-elements 定义在 list/leaflist 节点上，值为-1，建表
TEST_F(min_max_element_ddl_test, Yang_070_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_INVALID_PROPERTY);
    // 建表
    AsyncUserDataT data = {0};
    ret = CreateVertexLabelAsync(g_stmtAsync, "schema/vertex_label_3.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = CreateVertexLabelAsync(g_stmtAsync, "schema/vertex_label_4.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = CreateVertexLabelAsync(g_stmtAsync, "schema/vertex_label_5.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = CreateVertexLabelAsync(g_stmtAsync, "schema/vertex_label_6.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
}

// 3.min/max-elements 定义在 list/leaflist 节点上，值为MAX_UINT32 + 1，建表
TEST_F(min_max_element_ddl_test, Yang_070_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AddWhiteList(GMERR_INVALID_PROPERTY);
    // 建表
    AsyncUserDataT data = {0};
    ret = CreateVertexLabelAsync(g_stmtAsync, "schema/vertex_label_7.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = CreateVertexLabelAsync(g_stmtAsync, "schema/vertex_label_8.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = CreateVertexLabelAsync(g_stmtAsync, "schema/vertex_label_9.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = CreateVertexLabelAsync(g_stmtAsync, "schema/vertex_label_10.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
}

// 4.min/max-elements 定义在 list/leaflist 节点上，值为 0，建表
TEST_F(min_max_element_ddl_test, Yang_070_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 建表
    AsyncUserDataT data = {0};
    ret = CreateVertexLabelAsync(g_stmtAsync, "schema/vertex_label_11.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexLabelAsync(g_stmtAsync, "schema/vertex_label_12.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexLabelAsync(g_stmtAsync, "schema/vertex_label_13.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexLabelAsync(g_stmtAsync, "schema/vertex_label_14.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删表
    ret = DropVertexLabelAsync(g_stmtAsync, "vertex_label_11");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropVertexLabelAsync(g_stmtAsync, "vertex_label_12");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropVertexLabelAsync(g_stmtAsync, "vertex_label_13");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropVertexLabelAsync(g_stmtAsync, "vertex_label_14");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 5.max/max-elements 定义在 list/leaflist 节点上，值为 MAX_UINT32，建表
TEST_F(min_max_element_ddl_test, Yang_070_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 建表
    AsyncUserDataT data = {0};
    ret = CreateVertexLabelAsync(g_stmtAsync, "schema/vertex_label_15.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexLabelAsync(g_stmtAsync, "schema/vertex_label_16.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexLabelAsync(g_stmtAsync, "schema/vertex_label_17.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexLabelAsync(g_stmtAsync, "schema/vertex_label_18.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删表
    ret = DropVertexLabelAsync(g_stmtAsync, "vertex_label_15");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropVertexLabelAsync(g_stmtAsync, "vertex_label_16");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropVertexLabelAsync(g_stmtAsync, "vertex_label_17");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropVertexLabelAsync(g_stmtAsync, "vertex_label_18");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 6.min-elements 定义在 list/leaflist 节点上，值为 0，max-elements 定义在 list/leaflist 节点上，值为 MAX_UINT32，建表
TEST_F(min_max_element_ddl_test, Yang_070_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 建表
    AsyncUserDataT data = {0};
    ret = CreateVertexLabelAsync(g_stmtAsync, "schema/vertex_label_19.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexLabelAsync(g_stmtAsync, "schema/vertex_label_20.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删表
    ret = DropVertexLabelAsync(g_stmtAsync, "vertex_label_19");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropVertexLabelAsync(g_stmtAsync, "vertex_label_20");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 7.min/max-elements 定义在 list/leaflist 节点上，值相等∈[0，MAX_UINT32]，建表
TEST_F(min_max_element_ddl_test, Yang_070_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    g_testNo = 7;
    int ret = 0;
    // 建表
    AsyncUserDataT data = {0};
    uint32_t listElements[1] = { RandomReplay(0, MAX_UINT32) };
    Examply("schema/changeMode/mode_1.gmjson", 1, listElements, "json/1.gmjson"); // 随机生成表定义
    ret = CreateVertexLabelAsync(g_stmtAsync, "json/1.gmjson");
    AW_MACRO_EXPECT_EQ_INT_WITH_INFO(GMERR_OK, ret, "cat json/1.gmjson");

    uint32_t leafListElements[1] = { RandomReplay(0, MAX_UINT32) };
    Examply("schema/changeMode/mode_2.gmjson", 1, leafListElements, "json/2.gmjson");
    ret = CreateVertexLabelAsync(g_stmtAsync, "json/2.gmjson");
    AW_MACRO_EXPECT_EQ_INT_WITH_INFO(GMERR_OK, ret, "cat json/2.gmjson");
    // 删表
    ret = DropVertexLabelAsync(g_stmtAsync, "changeable_mode_vertex_label_1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropVertexLabelAsync(g_stmtAsync, "changeable_mode_vertex_label_2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 8.min/max-elements 定义在 list/leaflist 节点上，max-element > min-element 且均∈[0，MAX_UINT32]，建表
TEST_F(min_max_element_ddl_test, Yang_070_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    g_testNo = 8;
    int ret = 0;
    // 建表
    AsyncUserDataT data = {0};
    uint32_t listElements[2];
    listElements[0] = RandomReplay(0, MAX_UINT32 - 1);
    listElements[1] = RandomReplay(listElements[0] + 1, MAX_UINT32);
    Examply("schema/changeMode/mode_3.gmjson", 2, listElements, "json/1.gmjson"); // 随机生成表定义
    ret = CreateVertexLabelAsync(g_stmtAsync, "json/1.gmjson");
    AW_MACRO_EXPECT_EQ_INT_WITH_INFO(GMERR_OK, ret, "cat json/1.gmjson");

    uint32_t leafListElements[2];
    leafListElements[0] = RandomReplay(0, MAX_UINT32 - 1);
    leafListElements[1] = RandomReplay(leafListElements[0] + 1, MAX_UINT32);
    Examply("schema/changeMode/mode_4.gmjson", 2, leafListElements, "json/2.gmjson");
    ret = CreateVertexLabelAsync(g_stmtAsync, "json/2.gmjson");
    AW_MACRO_EXPECT_EQ_INT_WITH_INFO(GMERR_OK, ret, "cat json/2.gmjson");
    // 删表
    ret = DropVertexLabelAsync(g_stmtAsync, "changeable_mode_vertex_label_3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropVertexLabelAsync(g_stmtAsync, "changeable_mode_vertex_label_4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 9.min/max-elements 定义在 list/leaflist 节点上，max-element < min-element 且均∈[0，MAX_UINT32]，建表
TEST_F(min_max_element_ddl_test, Yang_070_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    g_testNo = 9;
    int ret = 0;
    AddWhiteList(GMERR_INVALID_TABLE_DEFINITION);
    // 建表
    AsyncUserDataT data = {0};
    uint32_t listElements[2];
    listElements[0] = RandomReplay(1, MAX_UINT32);
    listElements[1] = RandomReplay(0, listElements[0] - 1);
    Examply("schema/changeMode/mode_3.gmjson", 2, listElements, "json/1.gmjson");
    ret = CreateVertexLabelAsync(g_stmtAsync, "json/1.gmjson");
    AW_MACRO_EXPECT_EQ_INT_WITH_INFO(GMERR_INVALID_TABLE_DEFINITION, ret, "cat json/1.gmjson");

    uint32_t leafListElements[2];
    leafListElements[0] = RandomReplay(1, MAX_UINT32);
    leafListElements[1] = RandomReplay(0, leafListElements[0] - 1);
    Examply("schema/changeMode/mode_4.gmjson", 2, leafListElements, "json/2.gmjson");
    ret = CreateVertexLabelAsync(g_stmtAsync, "json/2.gmjson");
    AW_MACRO_EXPECT_EQ_INT_WITH_INFO(GMERR_INVALID_TABLE_DEFINITION, ret, "cat json/2.gmjson");
}

// 10.leaflist中定义了default值，min-element > 0，建表
TEST_F(min_max_element_ddl_test, Yang_070_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    g_testNo = 10;
    int ret = 0;
    AddWhiteList(GMERR_INVALID_TABLE_DEFINITION);
    // 建表
    AsyncUserDataT data = {0};
    uint32_t minElements[1] = { RandomReplay(1, MAX_UINT32) };
    Examply("schema/changeMode/mode_5.gmjson", 1, minElements, "json/1.gmjson");
    ret = CreateVertexLabelAsync(g_stmtAsync, "json/1.gmjson");
    AW_MACRO_EXPECT_EQ_INT_WITH_INFO(GMERR_INVALID_TABLE_DEFINITION, ret, "cat json/1.gmjson");
}

// 11.leaflist中定义了default值，min-element = 0，建表
TEST_F(min_max_element_ddl_test, Yang_070_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 建表
    AsyncUserDataT data = {0};
    uint32_t minElements[1] = { 0 };
    Examply("schema/changeMode/mode_5.gmjson", 1, minElements, "json/1.gmjson");
    ret = CreateVertexLabelAsync(g_stmtAsync, "json/1.gmjson");
    AW_MACRO_EXPECT_EQ_INT_WITH_INFO(GMERR_OK, ret, "cat json/1.gmjson");
    // 删表
    ret = DropVertexLabelAsync(g_stmtAsync, "changeable_mode_vertex_label_5");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 12.leaflist中定义了default值，max-element < default 值个数，建表
TEST_F(min_max_element_ddl_test, Yang_070_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    g_testNo = 12;
    int ret = 0;
    AddWhiteList(GMERR_INVALID_TABLE_DEFINITION);
    // 建表
    AsyncUserDataT data = {0};
    uint32_t maxElements[1] = { RandomReplay(0, 8) };
    Examply("schema/changeMode/mode_6.gmjson", 1, maxElements, "json/1.gmjson");
    ret = CreateVertexLabelAsync(g_stmtAsync, "json/1.gmjson");
    AW_MACRO_EXPECT_EQ_INT_WITH_INFO(GMERR_INVALID_TABLE_DEFINITION, ret, "cat json/1.gmjson");
}

// 13.leaflist中定义了default值，max-element = default 值个数，建表
TEST_F(min_max_element_ddl_test, Yang_070_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 建表
    AsyncUserDataT data = {0};
    uint32_t maxElements[1] = { 9 };
    Examply("schema/changeMode/mode_6.gmjson", 1, maxElements, "json/1.gmjson");
    ret = CreateVertexLabelAsync(g_stmtAsync, "json/1.gmjson");
    AW_MACRO_EXPECT_EQ_INT_WITH_INFO(GMERR_OK, ret, "cat json/1.gmjson");
    // 删表
    ret = DropVertexLabelAsync(g_stmtAsync, "changeable_mode_vertex_label_6");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 14.leaflist中定义了default值，max-element > default 值个数，建表
TEST_F(min_max_element_ddl_test, Yang_070_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    g_testNo = 14;
    int ret = 0;
    // 建表
    AsyncUserDataT data = {0};
    uint32_t maxElements[1] = { RandomReplay(10, MAX_UINT32) };
    Examply("schema/changeMode/mode_6.gmjson", 1, maxElements, "json/1.gmjson");
    ret = CreateVertexLabelAsync(g_stmtAsync, "json/1.gmjson");
    AW_MACRO_EXPECT_EQ_INT_WITH_INFO(GMERR_OK, ret, "cat json/1.gmjson");
    // 删表
    ret = DropVertexLabelAsync(g_stmtAsync, "changeable_mode_vertex_label_6");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 15.leaflist中定义了default值，min-element = 0, max-element > default 值个数，建表
TEST_F(min_max_element_ddl_test, Yang_070_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    g_testNo = 15;
    int ret = 0;
    // 建表
    AsyncUserDataT data = {0};
    uint32_t maxElements[1] = { RandomReplay(10, MAX_UINT32) };
    Examply("schema/changeMode/mode_7.gmjson", 1, maxElements, "json/1.gmjson");
    ret = CreateVertexLabelAsync(g_stmtAsync, "json/1.gmjson");
    AW_MACRO_EXPECT_EQ_INT_WITH_INFO(GMERR_OK, ret, "cat json/1.gmjson");
    // 删表
    ret = DropVertexLabelAsync(g_stmtAsync, "changeable_mode_vertex_label_7");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
