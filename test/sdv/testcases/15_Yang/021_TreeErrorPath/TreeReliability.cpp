#include "TreeErrorPath.h"

GmcConnT *conn_async = NULL;
GmcStmtT *root_stmt_async = NULL;
uint32_t g_received = 0;
int CYCLENUM = 2;
const char *g_namespace = (const char *)"g_namespace";
const char *g_nameSpaceUsername = (const char *)"g_nameSpaceUsername";


class TreeReliability : public testing::Test {
public:
    static void SetUpTestCase()
    {
        // 重启server
        system("sh $TEST_HOME/tools/start.sh");
        if (g_envType == 0) {
            CYCLENUM = 1000;
        }
        int ret = 0;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        g_isOneThreadEpoll = true;
        ret = createEpollOneThread();
        EXPECT_EQ(GMERR_OK, ret);
    };

    static void TearDownTestCase()
    {
        g_isOneThreadEpoll = false;
        closeEpollOneThread();
        testEnvClean();
    };
    virtual void SetUp();
    virtual void TearDown();
};

void TreeReliability::SetUp()
{
    printf("\n======================TEST:BEGIN======================\n");

    // 创建同步客户端连接
    int ret = 0;
    ret = testGmcConnect(&conn_async, &root_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epollRegInfoOneThread,
          NULL, NULL, NULL, NULL, -1, 0, &g_epollDataOneThread.userEpollFd);
    EXPECT_EQ(GMERR_OK, ret);

    // 启用namespace
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = g_namespace;
    nspCfg.userName = g_nameSpaceUsername;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};

    AsyncUserDataT data = {0};
    ret = GmcCreateNamespaceWithCfgAsync(root_stmt_async, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcUseNamespaceAsync(root_stmt_async, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcClearNamespaceAsync(root_stmt_async, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    GmcYangFreeErrorPathInfo();

    AW_CHECK_LOG_BEGIN(0);
}
void TreeReliability::TearDown()
{
    AW_CHECK_LOG_END();

    // 删除namespace
    int ret = 0;
    AsyncUserDataT data = {0};
    ret = GmcClearNamespaceAsync(root_stmt_async, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcDropNamespaceAsync(root_stmt_async, g_namespace, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 关闭 client connection
    ret = testGmcDisconnect(conn_async, root_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    printf("\n======================TEST:END========================\n");
}
void PrintfFunc(int i)
{
    if (i%1000 == 0) {
            printf("\n======================Pass:%d======================\n", i);
        }
}

// 001.001.root--......--container，多层嵌套，对未创建的container设置none操作
TEST_F(TreeReliability, Yang_021_TreeReliability_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};

    // 创建表
    SixContainerVertex(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root节点
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入node container
    GmcNodeT *root = NULL;
    GmcNodeT *node1 = NULL;
    GmcNodeT *node2 = NULL;
    GmcNodeT *node3 = NULL;
    GmcNodeT *node4 = NULL;
    GmcNodeT *node5 = NULL;
    GmcNodeT *node6 = NULL;
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_INSERT, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_INSERT, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_INSERT, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_INSERT, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_INSERT, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // node6设置none操作
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_NONE, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_NONE, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_NONE, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_NONE, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_NONE, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node5, "node6", GMC_OPERATION_NONE, &node6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    // 事务提交
    ret = TestTransCommitAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002.root--......--container，多层嵌套，对不存在的container设置none操作
// 设置时报错，无error path
TEST_F(TreeReliability, Yang_021_TreeReliability_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};

    // 创建表
    SixContainerVertex(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root节点
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入node container
    GmcNodeT *root = NULL;
    GmcNodeT *node1 = NULL;
    GmcNodeT *node2 = NULL;
    GmcNodeT *node3 = NULL;
    GmcNodeT *node4 = NULL;
    GmcNodeT *node5 = NULL;
    GmcNodeT *node6 = NULL;
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_INSERT, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_INSERT, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_INSERT, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_INSERT, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_INSERT, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node5, "node6", GMC_OPERATION_INSERT, &node6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // node7设置none操作
    GmcNodeT *node7 = NULL;
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_NONE, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_NONE, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_NONE, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_NONE, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_NONE, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node5, "node6", GMC_OPERATION_NONE, &node6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node6, "node7", GMC_OPERATION_NONE, &node7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_NAME, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    // 事务提交
    ret = TestTransCommitAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003.root--......--container，多层嵌套，重复insert已创建的container
TEST_F(TreeReliability, Yang_021_TreeReliability_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};

    // 创建表
    SixContainerVertex(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root节点
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入node container
    GmcNodeT *root = NULL;
    GmcNodeT *node1 = NULL;
    GmcNodeT *node2 = NULL;
    GmcNodeT *node3 = NULL;
    GmcNodeT *node4 = NULL;
    GmcNodeT *node5 = NULL;
    GmcNodeT *node6 = NULL;
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_INSERT, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_INSERT, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_INSERT, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_INSERT, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_INSERT, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node5, "node6", GMC_OPERATION_INSERT, &node6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // node6重复insert
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_NONE, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_NONE, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_NONE, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_NONE, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_NONE, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node5, "node6", GMC_OPERATION_INSERT, &node6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_CREATE;
    data_batch.expectedErrMsg = "target exists";
    data_batch.expectedErrPath = "/Container_root/node1/node2/node3/node4/node5/node6";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004.root--......--container，多层嵌套，重复insert已写值的container字段
TEST_F(TreeReliability, Yang_021_TreeReliability_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};

    // 创建表
    SixContainerVertex(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root节点
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入node container
    GmcNodeT *root = NULL;
    GmcNodeT *node1 = NULL;
    GmcNodeT *node2 = NULL;
    GmcNodeT *node3 = NULL;
    GmcNodeT *node4 = NULL;
    GmcNodeT *node5 = NULL;
    GmcNodeT *node6 = NULL;
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_INSERT, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_INSERT, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_INSERT, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_INSERT, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_INSERT, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node5, "node6", GMC_OPERATION_INSERT, &node6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int f0_value = 1;
    GmcPropValueT propValue = {0};
    YangInitPropValue(&propValue, "F0", GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    ret = GmcYangSetNodeProperty(node6, &propValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 字段重复写值
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_NONE, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_NONE, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_NONE, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_NONE, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_NONE, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node5, "node6", GMC_OPERATION_MERGE, &node6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetNodeProperty(node6, &propValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_CREATE;
    data_batch.expectedErrMsg = "target exists";
    data_batch.expectedErrPath = "/Container_root/node1/node2/node3/node4/node5/node6/F0";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005.root--......--container，多层嵌套，delete未创建的container
TEST_F(TreeReliability, Yang_021_TreeReliability_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};

    // 创建表
    SixContainerVertex(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root节点
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入node container
    GmcNodeT *root = NULL;
    GmcNodeT *node1 = NULL;
    GmcNodeT *node2 = NULL;
    GmcNodeT *node3 = NULL;
    GmcNodeT *node4 = NULL;
    GmcNodeT *node5 = NULL;
    GmcNodeT *node6 = NULL;
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_INSERT, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_INSERT, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_INSERT, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_INSERT, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_INSERT, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // node6设置none操作
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_NONE, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_NONE, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_NONE, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_NONE, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_NONE, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node5, "node6", GMC_OPERATION_DELETE_GRAPH, &node6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
    data_batch.expectedErrMsg = "target not exists";
    data_batch.expectedErrPath = "/Container_root/node1/node2/node3/node4/node5/node6";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006.root--......--container，多层嵌套，delete未写值的container字段
TEST_F(TreeReliability, Yang_021_TreeReliability_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};

    // 创建表
    SixContainerVertex(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root节点
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入node container
    GmcNodeT *root = NULL;
    GmcNodeT *node1 = NULL;
    GmcNodeT *node2 = NULL;
    GmcNodeT *node3 = NULL;
    GmcNodeT *node4 = NULL;
    GmcNodeT *node5 = NULL;
    GmcNodeT *node6 = NULL;
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_INSERT, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_INSERT, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_INSERT, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_INSERT, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_INSERT, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node5, "node6", GMC_OPERATION_INSERT, &node6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 未写值的字段delete
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_NONE, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_NONE, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_NONE, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_NONE, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_NONE, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node5, "node6", GMC_OPERATION_MERGE, &node6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int f0_value = 1;
    GmcPropValueT propValue = {0};
    YangInitPropValue(&propValue, "F0", GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    ret = GmcYangSetNodeProperty(node6, &propValue, GMC_YANG_PROPERTY_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
    data_batch.expectedErrMsg = "target not exists";
    data_batch.expectedErrPath = "/Container_root/node1/node2/node3/node4/node5/node6/F0";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007.root--......--list(container)，多层嵌套，对未创建的container设置none操作
TEST_F(TreeReliability, Yang_021_TreeReliability_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};

    GmcStmtT *list_stmt_async = NULL;
    ret = GmcAllocStmt(conn_async, &list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建表
    SixContainerListLabel(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root节点
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入node container
    GmcNodeT *root = NULL;
    GmcNodeT *node1 = NULL;
    GmcNodeT *node2 = NULL;
    GmcNodeT *node3 = NULL;
    GmcNodeT *node4 = NULL;
    GmcNodeT *node5 = NULL;
    GmcNodeT *node6 = NULL;
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_INSERT, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_INSERT, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_INSERT, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_INSERT, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_INSERT, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入list
    int f0_value = 1;
    ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcPropValueT propValue;
    memcpy(propValue.propertyName, "F0", (strlen("F0") + 1));
    propValue.type = GMC_DATATYPE_INT32;
    propValue.value = &f0_value;
    propValue.size = sizeof(f0_value);
    ret = GmcYangSetVertexProperty(list_stmt_async, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // node6设置none操作
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_NONE, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_NONE, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_NONE, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_NONE, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_NONE, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node5, "node6", GMC_OPERATION_NONE, &node6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    // 事务提交
    ret = TestTransCommitAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(list_stmt_async);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008.root--......--list(container)，多层嵌套，对不存在的container设置none操作
// 表定义无container，设置时报错，无error path
TEST_F(TreeReliability, Yang_021_TreeReliability_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};

    GmcStmtT *list_stmt_async = NULL;
    ret = GmcAllocStmt(conn_async, &list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建表
    SixContainerListLabel(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root节点
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入node container
    GmcNodeT *root = NULL;
    GmcNodeT *node1 = NULL;
    GmcNodeT *node2 = NULL;
    GmcNodeT *node3 = NULL;
    GmcNodeT *node4 = NULL;
    GmcNodeT *node5 = NULL;
    GmcNodeT *node6 = NULL;
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_INSERT, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_INSERT, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_INSERT, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_INSERT, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_INSERT, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node5, "node6", GMC_OPERATION_INSERT, &node6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入list
    int f0_value = 1;
    ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcPropValueT propValue;
    memcpy(propValue.propertyName, "F0", (strlen("F0") + 1));
    propValue.type = GMC_DATATYPE_INT32;
    propValue.value = &f0_value;
    propValue.size = sizeof(f0_value);
    ret = GmcYangSetVertexProperty(list_stmt_async, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // node7设置none操作
    GmcNodeT *node7 = NULL;
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_NONE, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_NONE, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_NONE, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_NONE, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_NONE, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node5, "node6", GMC_OPERATION_NONE, &node6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node6, "node7", GMC_OPERATION_NONE, &node7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_NAME, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    // 事务提交
    ret = TestTransCommitAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(list_stmt_async);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009.root--......--list(container)，多层嵌套，重复insert已创建的container
TEST_F(TreeReliability, Yang_021_TreeReliability_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};

    GmcStmtT *list_stmt_async = NULL;
    ret = GmcAllocStmt(conn_async, &list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建表
    SixContainerListLabel(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root节点
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入node container
    GmcNodeT *root = NULL;
    GmcNodeT *node1 = NULL;
    GmcNodeT *node2 = NULL;
    GmcNodeT *node3 = NULL;
    GmcNodeT *node4 = NULL;
    GmcNodeT *node5 = NULL;
    GmcNodeT *node6 = NULL;
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_INSERT, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_INSERT, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_INSERT, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_INSERT, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_INSERT, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node5, "node6", GMC_OPERATION_INSERT, &node6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // node6重复insert
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_NONE, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_NONE, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_NONE, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_NONE, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_NONE, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node5, "node6", GMC_OPERATION_INSERT, &node6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入list
    int f0_value = 1;
    ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
     GmcPropValueT propValue;
    memcpy(propValue.propertyName, "F0", (strlen("F0") + 1));
    propValue.type = GMC_DATATYPE_INT32;
    propValue.value = &f0_value;
    propValue.size = sizeof(f0_value);
    ret = GmcYangSetVertexProperty(list_stmt_async, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_CREATE;
    data_batch.expectedErrMsg = "target exists";
    data_batch.expectedErrPath = "/Container_root/node1/node2/node3/node4/node5/node6";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(list_stmt_async);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010.root--......--list(container)，多层嵌套，重复insert已写值的container字段
TEST_F(TreeReliability, Yang_021_TreeReliability_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};

    GmcStmtT *list_stmt_async = NULL;
    ret = GmcAllocStmt(conn_async, &list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建表
    SixContainerListLabel(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root节点
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入node container
    GmcNodeT *root = NULL;
    GmcNodeT *node1 = NULL;
    GmcNodeT *node2 = NULL;
    GmcNodeT *node3 = NULL;
    GmcNodeT *node4 = NULL;
    GmcNodeT *node5 = NULL;
    GmcNodeT *node6 = NULL;
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_INSERT, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_INSERT, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_INSERT, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_INSERT, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_INSERT, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node5, "node6", GMC_OPERATION_INSERT, &node6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int f0_value = 1;
    GmcPropValueT propValue = {0};
    YangInitPropValue(&propValue, "F0", GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    ret = GmcYangSetNodeProperty(node6, &propValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入list
    ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memcpy(propValue.propertyName, "F0", (strlen("F0") + 1));
    propValue.type = GMC_DATATYPE_INT32;
    propValue.value = &f0_value;
    propValue.size = sizeof(f0_value);
    ret = GmcYangSetVertexProperty(list_stmt_async, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 字段重复写值
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_NONE, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_NONE, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_NONE, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_NONE, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_NONE, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node5, "node6", GMC_OPERATION_MERGE, &node6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetNodeProperty(node6, &propValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_CREATE;
    data_batch.expectedErrMsg = "target exists";
    data_batch.expectedErrPath = "/Container_root/node1/node2/node3/node4/node5/node6/F0";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(list_stmt_async);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011.root--......--list(container)，多层嵌套，delete未创建的container
TEST_F(TreeReliability, Yang_021_TreeReliability_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};

    GmcStmtT *list_stmt_async = NULL;
    ret = GmcAllocStmt(conn_async, &list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建表
    SixContainerListLabel(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root节点
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入node container
    GmcNodeT *root = NULL;
    GmcNodeT *node1 = NULL;
    GmcNodeT *node2 = NULL;
    GmcNodeT *node3 = NULL;
    GmcNodeT *node4 = NULL;
    GmcNodeT *node5 = NULL;
    GmcNodeT *node6 = NULL;
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_INSERT, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_INSERT, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_INSERT, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_INSERT, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_INSERT, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入list
    int f0_value = 1;
    ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcPropValueT propValue;
    memcpy(propValue.propertyName, "F0", (strlen("F0") + 1));
    propValue.type = GMC_DATATYPE_INT32;
    propValue.value = &f0_value;
    propValue.size = sizeof(f0_value);
    ret = GmcYangSetVertexProperty(list_stmt_async, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // node6设置none操作
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_NONE, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_NONE, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_NONE, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_NONE, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_NONE, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node5, "node6", GMC_OPERATION_DELETE_GRAPH, &node6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
    data_batch.expectedErrMsg = "target not exists";
    data_batch.expectedErrPath = "/Container_root/node1/node2/node3/node4/node5/node6";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(list_stmt_async);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012.root--......--list(container)，多层嵌套，delete未写值的container字段
TEST_F(TreeReliability, Yang_021_TreeReliability_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};

    GmcStmtT *list_stmt_async = NULL;
    ret = GmcAllocStmt(conn_async, &list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建表
    SixContainerListLabel(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root节点
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入node container
    GmcNodeT *root = NULL;
    GmcNodeT *node1 = NULL;
    GmcNodeT *node2 = NULL;
    GmcNodeT *node3 = NULL;
    GmcNodeT *node4 = NULL;
    GmcNodeT *node5 = NULL;
    GmcNodeT *node6 = NULL;
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_INSERT, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_INSERT, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_INSERT, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_INSERT, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_INSERT, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node5, "node6", GMC_OPERATION_INSERT, &node6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入list
    int f0_value = 1;
    ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcPropValueT propValue;
    memcpy(propValue.propertyName, "F0", (strlen("F0") + 1));
    propValue.type = GMC_DATATYPE_INT32;
    propValue.value = &f0_value;
    propValue.size = sizeof(f0_value);
    ret = GmcYangSetVertexProperty(list_stmt_async, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 字段重复写值
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_NONE, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_NONE, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_NONE, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_NONE, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_NONE, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node5, "node6", GMC_OPERATION_MERGE, &node6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangInitPropValue(&propValue, "F0", GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    ret = GmcYangSetNodeProperty(node6, &propValue, GMC_YANG_PROPERTY_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
    data_batch.expectedErrMsg = "target not exists";
    data_batch.expectedErrPath = "/Container_root/node1/node2/node3/node4/node5/node6/F0";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(list_stmt_async);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013.root--......--choice case，多层嵌套，创建无case节点树模型，进行mandatory校验
TEST_F(TreeReliability, Yang_021_TreeReliability_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data = {0};
    AsyncUserDataT data_batch = {0};
    AsyncUserDataT data_commit = {0};

    // 建表
    SixConChoiceCaseVertex(root_stmt_async);

    ret = TestCheckValidateModelAsync(root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE;
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;
    ret = GmcTransStartAsync(conn_async, &config, trans_start_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBatchOptionSetDiffType(&batchOption, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root节点
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入node container
    GmcNodeT *root = NULL;
    GmcNodeT *node1 = NULL;
    GmcNodeT *node2 = NULL;
    GmcNodeT *node3 = NULL;
    GmcNodeT *node4 = NULL;
    GmcNodeT *node5 = NULL;
    GmcNodeT *node6 = NULL;
    GmcNodeT *node_choice = NULL;
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_INSERT, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_INSERT, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_INSERT, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_INSERT, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_INSERT, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node5, "node6", GMC_OPERATION_INSERT, &node6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node6, "choice_node", GMC_OPERATION_INSERT, &node_choice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    // error path
    data_batch.expStatus = GMERR_OK;
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_MANDATORY_CHOICE;
    data_batch.expectedErrMsg = "mandatory verify no choice";
    data_batch.expectedErrPath = "/Container_root/node1/node2/node3/node4/node5/node6/choice_node";

    // mandatory校验
    ErrorPathMandatoryCheck(root_stmt_async, data_batch, false);

    // 事务提交
    ret = GmcTransCommitAsync(conn_async, trans_commit_callback, &data_commit);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_commit);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_commit.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014.root--list(container)，none未创建的list，场景循环10000次
TEST_F(TreeReliability, Yang_021_TreeReliability_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int i = 0;
    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;

    for (i = 0; i < CYCLENUM; i++) {
        AsyncUserDataT data = {0};
        AsyncUserDataT data_batch = {0};
        GmcStmtT *list_stmt_async = NULL;
        ret = GmcAllocStmt(conn_async, &list_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 创建root(container-list)表
        RootNodeListLabel(root_stmt_async);

        // 准备批量操作
        ret = GmcBatchOptionInit(&batchOption);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 启动事务
        ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置root节点
        ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, root_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 插入node container
        GmcNodeT *root = NULL;
        GmcNodeT *con_node = NULL;
        ret = GmcGetRootNode(root_stmt_async, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(root, "con_node", GMC_OPERATION_INSERT, &con_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, root_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecvOneThread(&data_batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

        ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // root none
        ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, root_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(root_stmt_async, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(root, "con_node", GMC_OPERATION_NONE, &con_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, root_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // none未创建的list节点
        int f0_value = 1;
        ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(list_stmt_async, "PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(list_stmt_async, 1, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, list_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // error path
        data_batch.isValidErrorPathInfo = true;
        data_batch.expectedErrorCode = GMC_VIOLATES_BUTT;
        data_batch.expectedErrMsg = NULL;
        data_batch.expectedErrPath = NULL;

        ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecvOneThread(&data_batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

        // 事务提交
        ret = TestTransCommitAsync(conn_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        GmcFreeStmt(list_stmt_async);

        PrintfFunc(i);

        ret = GmcClearNamespaceAsync(root_stmt_async, g_namespace, ClearNSCallbak, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecvOneThread(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        memset(&data, 0, sizeof(AsyncUserDataT));
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015.root--list(container)，none不存在的list字段，场景循环10000次
TEST_F(TreeReliability, Yang_021_TreeReliability_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int i = 0;
    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;

    for (i = 0; i < CYCLENUM; i++) {
        AsyncUserDataT data = {0};
        AsyncUserDataT data_batch = {0};
        // 创建表
        SixContainerVertex(root_stmt_async);

        // 准备批量操作
        ret = GmcBatchOptionInit(&batchOption);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 启动事务
        ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置root节点
        ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, root_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 插入node container
        GmcNodeT *root = NULL;
        GmcNodeT *node1 = NULL;
        GmcNodeT *node2 = NULL;
        GmcNodeT *node3 = NULL;
        GmcNodeT *node4 = NULL;
        GmcNodeT *node5 = NULL;
        GmcNodeT *node6 = NULL;
        ret = GmcGetRootNode(root_stmt_async, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_INSERT, &node1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_INSERT, &node2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_INSERT, &node3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_INSERT, &node4);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_INSERT, &node5);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(node5, "node6", GMC_OPERATION_INSERT, &node6);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, root_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data_batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecvOneThread(&data_batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

        ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // root none
        ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, root_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // node7设置none操作
        GmcNodeT *node7 = NULL;
        ret = GmcGetRootNode(root_stmt_async, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_NONE, &node1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_NONE, &node2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_NONE, &node3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_NONE, &node4);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_NONE, &node5);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(node5, "node6", GMC_OPERATION_NONE, &node6);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(node6, "node7", GMC_OPERATION_NONE, &node7);
        AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_NAME, ret);
        ret = GmcBatchAddDML(batch, root_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data_batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecvOneThread(&data_batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

        // 事务提交
        ret = TestTransCommitAsync(conn_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        PrintfFunc(i);

        ret = GmcClearNamespaceAsync(root_stmt_async, g_namespace, ClearNSCallbak, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecvOneThread(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        memset(&data, 0, sizeof(AsyncUserDataT));
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016.root--list(container)，重复insert已创建的list，场景循环10000次
TEST_F(TreeReliability, Yang_021_TreeReliability_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int i = 0;
    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;

    for (i = 0; i < CYCLENUM; i++) {
        AsyncUserDataT data = {0};
        AsyncUserDataT data_batch = {0};
        GmcStmtT *list_stmt_async = NULL;
        ret = GmcAllocStmt(conn_async, &list_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 创建root(container-list)表
        RootNodeListLabel(root_stmt_async);

        // 准备批量操作
        ret = GmcBatchOptionInit(&batchOption);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 启动事务
        ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置root节点
        ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, root_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 插入node container
        GmcNodeT *root = NULL;
        GmcNodeT *con_node = NULL;
        ret = GmcGetRootNode(root_stmt_async, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(root, "con_node", GMC_OPERATION_INSERT, &con_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, root_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 插入list
        int f0_value = 1;
        ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcPropValueT propValue;
        memcpy(propValue.propertyName, "F0", (strlen("F0") + 1));
        propValue.type = GMC_DATATYPE_INT32;
        propValue.value = &f0_value;
        propValue.size = sizeof(f0_value);
        ret = GmcYangSetVertexProperty(list_stmt_async, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, list_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecvOneThread(&data_batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

        // 事务提交
        ret = TestTransCommitAsync(conn_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 启动事务
        ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // root none
        ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, root_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(root_stmt_async, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcYangEditChildNode(root, "con_node", GMC_OPERATION_NONE, &con_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, root_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 重复insert list节点
        ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        memcpy(propValue.propertyName, "F0", (strlen("F0") + 1));
        propValue.type = GMC_DATATYPE_INT32;
        propValue.value = &f0_value;
        propValue.size = sizeof(f0_value);
        ret = GmcYangSetVertexProperty(list_stmt_async, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, list_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // error path
        data_batch.isValidErrorPathInfo = true;
        data_batch.expectedErrorCode = GMC_VIOLATES_CREATE;
        data_batch.expectedErrMsg = "target exists";
        data_batch.expectedErrPath = "/Container_root/con_node/list_child[F0=1]";

        ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecvOneThread(&data_batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, data_batch.status);

        // 事务回滚
        ret = TestTransRollBackAsync(conn_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        GmcFreeStmt(list_stmt_async);

        PrintfFunc(i);

        ret = GmcClearNamespaceAsync(root_stmt_async, g_namespace, ClearNSCallbak, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecvOneThread(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        memset(&data, 0, sizeof(AsyncUserDataT));
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 017.root--list(container)，重复insert已写值的list字段，场景循环10000次
TEST_F(TreeReliability, Yang_021_TreeReliability_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int i = 0;
    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;

    for (i = 0; i < CYCLENUM; i++) {
        AsyncUserDataT data = {0};
        AsyncUserDataT data_batch = {0};
        GmcStmtT *list_stmt_async = NULL;
        ret = GmcAllocStmt(conn_async, &list_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 创建root(container-list)表
        RootNodeListLabel(root_stmt_async);

        // 准备批量操作
        ret = GmcBatchOptionInit(&batchOption);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 启动事务
        ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置root节点
        ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, root_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 插入node container
        GmcNodeT *root = NULL;
        GmcNodeT *con_node = NULL;
        ret = GmcGetRootNode(root_stmt_async, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(root, "con_node", GMC_OPERATION_INSERT, &con_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, root_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 插入list
        int f0_value = 1;
        int f1_value = 1;
        ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        GmcPropValueT propValue = {0};
        GmcPropValueT propValueF1 = {0};
        ret = GmcGetRootNode(list_stmt_async, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        YangInitPropValue(&propValue, "F0", GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
        ret = GmcYangSetNodeProperty(root, &propValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        YangInitPropValue(&propValueF1, "F1", GMC_DATATYPE_INT32, &f1_value, sizeof(f1_value));
        ret = GmcYangSetNodeProperty(root, &propValueF1, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, list_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecvOneThread(&data_batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

        // 启动事务
        ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // root none
        ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, root_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(root_stmt_async, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcYangEditChildNode(root, "con_node", GMC_OPERATION_NONE, &con_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, root_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 重复insert list字段
        ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(list_stmt_async, "PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(list_stmt_async, 1, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(list_stmt_async, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetNodeProperty(root, &propValueF1, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, list_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // error path
        data_batch.isValidErrorPathInfo = true;
        data_batch.expectedErrorCode = GMC_VIOLATES_CREATE;
        data_batch.expectedErrMsg = "target exists";
        data_batch.expectedErrPath = "/Container_root/con_node/list_child[F0=1]/F1";

        ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecvOneThread(&data_batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data_batch.status);

        // 事务回滚
        ret = TestTransRollBackAsync(conn_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        GmcFreeStmt(list_stmt_async);

        PrintfFunc(i);

        ret = GmcClearNamespaceAsync(root_stmt_async, g_namespace, ClearNSCallbak, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecvOneThread(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        memset(&data, 0, sizeof(AsyncUserDataT));
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018.root--list(container)，delete未创建的list，场景循环10000次
TEST_F(TreeReliability, Yang_021_TreeReliability_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int i = 0;
    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;

    for (i = 0; i < CYCLENUM; i++) {
        AsyncUserDataT data = {0};
        AsyncUserDataT data_batch = {0};
        GmcStmtT *list_stmt_async = NULL;
        ret = GmcAllocStmt(conn_async, &list_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 创建root(container-list)表
        RootNodeListLabel(root_stmt_async);

        // 准备批量操作
        ret = GmcBatchOptionInit(&batchOption);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 启动事务
        ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置root节点
        ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, root_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 插入node container
        GmcNodeT *con_node = NULL;
        ret = GmcGetChildNode(root_stmt_async, "con_node", &con_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, root_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecvOneThread(&data_batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

        ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // root none
        ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, root_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *root = NULL;
        ret = GmcGetRootNode(root_stmt_async, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcYangEditChildNode(root, "con_node", GMC_OPERATION_NONE, &con_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, root_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // delete未创建的list节点
        ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_DELETE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int f0_value = 1;
        ret = GmcSetIndexKeyName(list_stmt_async, "PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(list_stmt_async, 1, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, list_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // error path
        data_batch.isValidErrorPathInfo = true;
        data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
        data_batch.expectedErrMsg = "target not exists";
        data_batch.expectedErrPath = "/Container_root/con_node/list_child[F0=1]";

        ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecvOneThread(&data_batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data_batch.status);

        // 事务回滚
        ret = TestTransRollBackAsync(conn_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        GmcFreeStmt(list_stmt_async);

        PrintfFunc(i);

        ret = GmcClearNamespaceAsync(root_stmt_async, g_namespace, ClearNSCallbak, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecvOneThread(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        memset(&data, 0, sizeof(AsyncUserDataT));
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 019.root--list(container)，delete未写值的list字段，场景循环10000次
TEST_F(TreeReliability, Yang_021_TreeReliability_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int i = 0;
    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;

    for (i = 0; i < CYCLENUM; i++) {
        AsyncUserDataT data = {0};
        AsyncUserDataT data_batch = {0};
        GmcStmtT *list_stmt_async = NULL;
        ret = GmcAllocStmt(conn_async, &list_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 创建root(container-list)表
        RootNodeListLabel(root_stmt_async);

        // 准备批量操作
        ret = GmcBatchOptionInit(&batchOption);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 启动事务
        ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置root节点
        ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, root_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 插入node container
        GmcNodeT *root = NULL;
        GmcNodeT *con_node = NULL;
        ret = GmcGetRootNode(root_stmt_async, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(root, "con_node", GMC_OPERATION_INSERT, &con_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, root_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 插入list
        ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        GmcPropValueT propValue = {0};
        int f0_value = 1;
        ret = GmcGetRootNode(list_stmt_async, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        YangInitPropValue(&propValue, "F0", GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
        ret = GmcYangSetNodeProperty(root, &propValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, list_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecvOneThread(&data_batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

        ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // root none
        ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, root_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(root_stmt_async, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcYangEditChildNode(root, "con_node", GMC_OPERATION_NONE, &con_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, root_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // delete未写值的list字段
        ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(list_stmt_async, "PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(list_stmt_async, 1, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        int f1_value = 1;
        GmcPropValueT f1Value = {0};
        YangInitPropValue(&f1Value, "F1", GMC_DATATYPE_INT32, &f1_value, sizeof(f1_value));
        ret = GmcYangSetVertexProperty(list_stmt_async, &f1Value, GMC_YANG_PROPERTY_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, list_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // error path
        data_batch.isValidErrorPathInfo = true;
        data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
        data_batch.expectedErrMsg = "target not exists";
        data_batch.expectedErrPath = "/Container_root/con_node/list_child[F0=1]/F1";

        ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecvOneThread(&data_batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data_batch.status);

        // 事务回滚
        ret = TestTransRollBackAsync(conn_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        GmcFreeStmt(list_stmt_async);

        PrintfFunc(i);

        ret = GmcClearNamespaceAsync(root_stmt_async, g_namespace, ClearNSCallbak, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecvOneThread(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        memset(&data, 0, sizeof(AsyncUserDataT));
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 020.错误操作获取error path，修正错误之后重新获取error path
TEST_F(TreeReliability, Yang_021_TreeReliability_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};
    GmcStmtT *list_stmt_async = NULL;
    ret = GmcAllocStmt(conn_async, &list_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建root(container-list)表
    RootListLabel(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root节点
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *root = NULL;
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // none未创建的list节点
    int f0_value = 1;
    ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(list_stmt_async, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(list_stmt_async, 1, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_BUTT;
    data_batch.expectedErrMsg = NULL;
    data_batch.expectedErrPath = NULL;

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    // 修正操作，insert未创建的节点
    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建的list节点
    ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(list_stmt_async, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(list_stmt_async, 1, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    data_batch.isValidErrorPathInfo = false;
    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    // 事务提交
    ret = TestTransCommitAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(list_stmt_async);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 021.规格测试，error path超过4096
TEST_F(TreeReliability, Yang_021_TreeReliability_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};

    // 创建表
    LongContainerVertex(root_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root节点
    ret = testGmcPrepareStmtByLabelName(root_stmt_async,
                                    "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                    "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                    "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入node container
    GmcNodeT *root = NULL;
    GmcNodeT *node1 = NULL;
    GmcNodeT *node2 = NULL;
    GmcNodeT *node3 = NULL;
    GmcNodeT *node4 = NULL;
    GmcNodeT *node5 = NULL;
    GmcNodeT *node6 = NULL;
    GmcNodeT *node7 = NULL;
    GmcNodeT *node8 = NULL;
    GmcNodeT *node9 = NULL;
    GmcNodeT *node10 = NULL;
    GmcNodeT *node11 = NULL;
    GmcNodeT *node12 = NULL;
    GmcNodeT *node13 = NULL;
    GmcNodeT *node14 = NULL;
    GmcNodeT *node15 = NULL;
    GmcNodeT *node16 = NULL;
    GmcNodeT *node17 = NULL;
    GmcNodeT *node18 = NULL;
    GmcNodeT *node19 = NULL;
    GmcNodeT *node20 = NULL;
    GmcNodeT *node21 = NULL;
    GmcNodeT *node22 = NULL;
    GmcNodeT *node23 = NULL;
    GmcNodeT *node24 = NULL;
    GmcNodeT *node25 = NULL;
    GmcNodeT *node26 = NULL;
    GmcNodeT *node27 = NULL;
    GmcNodeT *node28 = NULL;
    GmcNodeT *node29 = NULL;
    GmcNodeT *node30 = NULL;
    GmcNodeT *node31 = NULL;

    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa1",
                               GMC_OPERATION_INSERT, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa2",
                               GMC_OPERATION_INSERT, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa3",
                               GMC_OPERATION_INSERT, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa4",
                               GMC_OPERATION_INSERT, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa5",
                               GMC_OPERATION_INSERT, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node5,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa6",
                               GMC_OPERATION_INSERT, &node6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node6,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa7",
                               GMC_OPERATION_INSERT, &node7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node7,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa8",
                               GMC_OPERATION_INSERT, &node8);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node8,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa9",
                               GMC_OPERATION_INSERT, &node9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node9,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa10",
                               GMC_OPERATION_INSERT, &node10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node10,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa11",
                               GMC_OPERATION_INSERT, &node11);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node11,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa12",
                               GMC_OPERATION_INSERT, &node12);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node12,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa13",
                               GMC_OPERATION_INSERT, &node13);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node13,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa14",
                               GMC_OPERATION_INSERT, &node14);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node14,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa15",
                               GMC_OPERATION_INSERT, &node15);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node15,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa16",
                               GMC_OPERATION_INSERT, &node16);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node16,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa17",
                               GMC_OPERATION_INSERT, &node17);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node17,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa18",
                               GMC_OPERATION_INSERT, &node18);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node18,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa19",
                               GMC_OPERATION_INSERT, &node19);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node19,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa20",
                               GMC_OPERATION_INSERT, &node20);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node20,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa21",
                               GMC_OPERATION_INSERT, &node21);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node21,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa22",
                               GMC_OPERATION_INSERT, &node22);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node22,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa23",
                               GMC_OPERATION_INSERT, &node23);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node23,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa24",
                               GMC_OPERATION_INSERT, &node24);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node24,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa25",
                               GMC_OPERATION_INSERT, &node25);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node25,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa26",
                               GMC_OPERATION_INSERT, &node26);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node26,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa27",
                               GMC_OPERATION_INSERT, &node27);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node27,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa28",
                               GMC_OPERATION_INSERT, &node28);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node28,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa29",
                               GMC_OPERATION_INSERT, &node29);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node29,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa30",
                               GMC_OPERATION_INSERT, &node30);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node30,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa31",
                               GMC_OPERATION_INSERT, &node31);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async,
                                    "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                    "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                    "aaaaaaaaaaaaaaaaaaaaaaaaaaaaa", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重复插入node container
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa1",
                               GMC_OPERATION_NONE, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa2",
                               GMC_OPERATION_NONE, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa3",
                               GMC_OPERATION_NONE, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa4",
                               GMC_OPERATION_NONE, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa5",
                               GMC_OPERATION_NONE, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node5,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa6",
                               GMC_OPERATION_NONE, &node6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node6,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa7",
                               GMC_OPERATION_NONE, &node7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node7,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa8",
                               GMC_OPERATION_NONE, &node8);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node8,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa9",
                               GMC_OPERATION_NONE, &node9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node9,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa10",
                               GMC_OPERATION_NONE, &node10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node10,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa11",
                               GMC_OPERATION_NONE, &node11);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node11,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa12",
                               GMC_OPERATION_NONE, &node12);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node12,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa13",
                               GMC_OPERATION_NONE, &node13);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node13,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa14",
                               GMC_OPERATION_NONE, &node14);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node14,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa15",
                               GMC_OPERATION_NONE, &node15);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node15,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa16",
                               GMC_OPERATION_NONE, &node16);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node16,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa17",
                               GMC_OPERATION_NONE, &node17);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node17,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa18",
                               GMC_OPERATION_NONE, &node18);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node18,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa19",
                               GMC_OPERATION_NONE, &node19);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node19,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa20",
                               GMC_OPERATION_NONE, &node20);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node20,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa21",
                               GMC_OPERATION_NONE, &node21);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node21,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa22",
                               GMC_OPERATION_NONE, &node22);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node22,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa23",
                               GMC_OPERATION_NONE, &node23);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node23,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa24",
                               GMC_OPERATION_NONE, &node24);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node24,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa25",
                               GMC_OPERATION_NONE, &node25);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node25,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa26",
                               GMC_OPERATION_NONE, &node26);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node26,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa27",
                               GMC_OPERATION_NONE, &node27);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node27,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa28",
                               GMC_OPERATION_NONE, &node28);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node28,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa29",
                               GMC_OPERATION_NONE, &node29);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node29,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa30",
                               GMC_OPERATION_NONE, &node30);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node30,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa31",
                               GMC_OPERATION_INSERT, &node31);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_CREATE;
    data_batch.expectedErrMsg = "target exists";
    data_batch.expectedErrPath = "/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaa1/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaa2/aaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaa3/aaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa4/aaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa5"
                                 "/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaa6/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaa7/aaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaa8/aaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa9/aaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa10"
                                 "/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaa11/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaa12/aaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaa13/aaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaa14/aaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa15"
                                 "/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaa16/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaa17/aaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaa18/aaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaa19/aaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa20"
                                 "/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaa21/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaa22/aaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaa23/aaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaa24/aaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa25"
                                 "/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaa26/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaa27/aaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaa28/aaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaa29/aaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa30"
                                 "/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa31";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
