#include "TreeErrorPath.h"

GmcConnT *conn_async = NULL;
GmcStmtT *root_stmt_async = NULL;
const char *g_namespace = (const char *)"g_namespace021";
const char *g_nameSpaceUsername = (const char *)"g_nameSpaceUsername";

class newErrorCode : public testing::Test {
public:
    static void SetUpTestCase()
    {
        // 重启server
        system("sh $TEST_HOME/tools/start.sh");
        int ret = 0;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        g_isOneThreadEpoll = true;
        ret = createEpollOneThread();
        EXPECT_EQ(GMERR_OK, ret);
    };

    static void TearDownTestCase()
    {
        g_isOneThreadEpoll = false;
        closeEpollOneThread();
        testEnvClean();
    };
    virtual void SetUp();
    virtual void TearDown();
};

void newErrorCode::SetUp()
{
    // 创建同步客户端连接
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = testGmcConnect(&conn_async, &root_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epollRegInfoOneThread,
          NULL, NULL, NULL, NULL, -1, 0, &g_epollDataOneThread.userEpollFd);
    EXPECT_EQ(GMERR_OK, ret);

    // 启用namespace
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = g_namespace;
    nspCfg.userName = g_nameSpaceUsername;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};

    ret = GmcCreateNamespaceWithCfgAsync(root_stmt_async, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcUseNamespaceAsync(root_stmt_async, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcClearNamespaceAsync(root_stmt_async, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    AW_CHECK_LOG_BEGIN(0);

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    GmcYangFreeErrorPathInfo();
}
void newErrorCode::TearDown()
{
    AW_CHECK_LOG_END();
    // 删除namespace
    int ret = 0;
    AsyncUserDataT data = {0};
    ret = GmcClearNamespaceAsync(root_stmt_async, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcDropNamespaceAsync(root_stmt_async, g_namespace, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 关闭 client connection
    ret = testGmcDisconnect(conn_async, root_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
}

// 001.GMC_VIOLATES_BUTT的值为15
TEST_F(newErrorCode, Yang_021_newErrorCode_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcErrorPathInfoT msg;
    ret = GmcYangGetErrorPathInfo(&msg);

    AW_MACRO_ASSERT_EQ_INT(GMC_VIOLATES_BUTT, 15);
    AW_FUN_Log(LOG_STEP, "----GMC_VIOLATES_BUTT:%d----\n", GMC_VIOLATES_BUTT);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002.不做任何操作的情况下获取Code，预期返回GMC_VIOLATES_BUTT
TEST_F(newErrorCode, Yang_021_newErrorCode_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcErrorPathInfoT msg;
    ret = GmcYangGetErrorPathInfo(&msg);

    AW_MACRO_ASSERT_EQ_INT(GMC_VIOLATES_BUTT, msg.errorCode);
    AW_FUN_Log(LOG_STEP, "----ErrorCode:%d----\n", msg.errorCode);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/*******************************************************************************
  节点/字段不存在且父节点也不存在，只需返回操作节点的父节点：insert、replace、merge
  注：字段可看作一个子节点
  remove不生成error path
*******************************************************************************/
/*******************************************************************************
    GMC_OPERATION_INSERT,                插入
    GMC_OPERATION_REPLACE_GRAPH,         替换
    GMC_OPERATION_DELETE_GRAPH,          删除
    GMC_OPERATION_REMOVE_GRAPH,          关联删除顶点
    GMC_OPERATION_MERGE,                 合并
*******************************************************************************/
// 003.6层，不create node4、node5、node6，对node6做insert操作
TEST_F(newErrorCode, Yang_021_newErrorCode_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};

    // 创建表
    SixContainerVertex(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root节点
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入node container
    GmcNodeT *root = NULL;
    GmcNodeT *node1 = NULL;
    GmcNodeT *node2 = NULL;
    GmcNodeT *node3 = NULL;
    GmcNodeT *node4 = NULL;
    GmcNodeT *node5 = NULL;
    GmcNodeT *node6 = NULL;
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_INSERT, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_INSERT, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_INSERT, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // node6
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_NONE, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_NONE, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_NONE, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_NONE, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_NONE, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node5, "node6", GMC_OPERATION_INSERT, &node6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
    data_batch.expectedErrMsg = "target not exists";
    data_batch.expectedErrPath = "/Container_root/node1/node2/node3/node4/node5";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004.6层，不create node4、node5、node6，对node6做replace操作
TEST_F(newErrorCode, Yang_021_newErrorCode_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};

    // 创建表
    SixContainerVertex(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root节点
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入node container
    GmcNodeT *root = NULL;
    GmcNodeT *node1 = NULL;
    GmcNodeT *node2 = NULL;
    GmcNodeT *node3 = NULL;
    GmcNodeT *node4 = NULL;
    GmcNodeT *node5 = NULL;
    GmcNodeT *node6 = NULL;
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_INSERT, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_INSERT, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_INSERT, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // node6
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_NONE, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_NONE, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_NONE, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_NONE, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_NONE, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node5, "node6", GMC_OPERATION_REPLACE_GRAPH, &node6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
    data_batch.expectedErrMsg = "target not exists";
    data_batch.expectedErrPath = "/Container_root/node1/node2/node3/node4/node5";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data_batch.status);

    // 事务提交
    ret = TestTransCommitAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005.6层，不create node4、node5、node6，对node6做delete操作
TEST_F(newErrorCode, Yang_021_newErrorCode_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};

    // 创建表
    SixContainerVertex(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root节点
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入node container
    GmcNodeT *root = NULL;
    GmcNodeT *node1 = NULL;
    GmcNodeT *node2 = NULL;
    GmcNodeT *node3 = NULL;
    GmcNodeT *node4 = NULL;
    GmcNodeT *node5 = NULL;
    GmcNodeT *node6 = NULL;
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_INSERT, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_INSERT, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_INSERT, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // node6
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_NONE, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_NONE, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_NONE, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_NONE, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_NONE, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node5, "node6", GMC_OPERATION_DELETE_GRAPH, &node6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
    data_batch.expectedErrMsg = "target not exists";
    data_batch.expectedErrPath = "/Container_root/node1/node2/node3/node4/node5/node6";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, data_batch.status);

    // 事务提交
    ret = TestTransCommitAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006.6层，不create node4、node5、node6，对node6做remove操作
TEST_F(newErrorCode, Yang_021_newErrorCode_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};

    // 创建表
    SixContainerVertex(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root节点
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入node container
    GmcNodeT *root = NULL;
    GmcNodeT *node1 = NULL;
    GmcNodeT *node2 = NULL;
    GmcNodeT *node3 = NULL;
    GmcNodeT *node4 = NULL;
    GmcNodeT *node5 = NULL;
    GmcNodeT *node6 = NULL;
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_INSERT, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_INSERT, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_INSERT, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // node6
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_NONE, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_NONE, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_NONE, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_NONE, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_NONE, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node5, "node6", GMC_OPERATION_REMOVE_GRAPH, &node6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    // 事务提交
    ret = TestTransCommitAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007.6层，不create node4、node5、node6，对node6做merge操作
TEST_F(newErrorCode, Yang_021_newErrorCode_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};

    // 创建表
    SixContainerVertex(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root节点
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入node container
    GmcNodeT *root = NULL;
    GmcNodeT *node1 = NULL;
    GmcNodeT *node2 = NULL;
    GmcNodeT *node3 = NULL;
    GmcNodeT *node4 = NULL;
    GmcNodeT *node5 = NULL;
    GmcNodeT *node6 = NULL;
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_INSERT, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_INSERT, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_INSERT, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // node6
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_NONE, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_NONE, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_NONE, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_NONE, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_NONE, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node5, "node6", GMC_OPERATION_MERGE, &node6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
    data_batch.expectedErrMsg = "target not exists";
    data_batch.expectedErrPath = "/Container_root/node1/node2/node3/node4/node5";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008.6层，全部节点设置none，对node6做insert操作
TEST_F(newErrorCode, Yang_021_newErrorCode_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};

    // 创建表
    SixContainerVertex(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // node6
    GmcNodeT *root = NULL;
    GmcNodeT *node1 = NULL;
    GmcNodeT *node2 = NULL;
    GmcNodeT *node3 = NULL;
    GmcNodeT *node4 = NULL;
    GmcNodeT *node5 = NULL;
    GmcNodeT *node6 = NULL;
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_NONE, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_NONE, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_NONE, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_NONE, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_NONE, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node5, "node6", GMC_OPERATION_INSERT, &node6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
    data_batch.expectedErrMsg = "target not exists";
    data_batch.expectedErrPath = "/Container_root/node1/node2/node3/node4/node5";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009.6层，全部节点设置none，对node6做replace操作
TEST_F(newErrorCode, Yang_021_newErrorCode_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};

    // 创建表
    SixContainerVertex(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // node6
    GmcNodeT *root = NULL;
    GmcNodeT *node1 = NULL;
    GmcNodeT *node2 = NULL;
    GmcNodeT *node3 = NULL;
    GmcNodeT *node4 = NULL;
    GmcNodeT *node5 = NULL;
    GmcNodeT *node6 = NULL;
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_NONE, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_NONE, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_NONE, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_NONE, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_NONE, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node5, "node6", GMC_OPERATION_REPLACE_GRAPH, &node6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
    data_batch.expectedErrMsg = "target not exists";
    data_batch.expectedErrPath = "/Container_root/node1/node2/node3/node4/node5";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010.6层，全部节点设置none，对node6做delete操作
TEST_F(newErrorCode, Yang_021_newErrorCode_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};

    // 创建表
    SixContainerVertex(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // node6
    GmcNodeT *root = NULL;
    GmcNodeT *node1 = NULL;
    GmcNodeT *node2 = NULL;
    GmcNodeT *node3 = NULL;
    GmcNodeT *node4 = NULL;
    GmcNodeT *node5 = NULL;
    GmcNodeT *node6 = NULL;
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_NONE, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_NONE, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_NONE, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_NONE, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_NONE, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node5, "node6", GMC_OPERATION_DELETE_GRAPH, &node6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
    data_batch.expectedErrMsg = "target not exists";
    data_batch.expectedErrPath = "/Container_root/node1/node2/node3/node4/node5/node6";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011.6层，全部节点设置none，对node6做remove操作
TEST_F(newErrorCode, Yang_021_newErrorCode_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};

    // 创建表
    SixContainerVertex(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // node6
    GmcNodeT *root = NULL;
    GmcNodeT *node1 = NULL;
    GmcNodeT *node2 = NULL;
    GmcNodeT *node3 = NULL;
    GmcNodeT *node4 = NULL;
    GmcNodeT *node5 = NULL;
    GmcNodeT *node6 = NULL;
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_NONE, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_NONE, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_NONE, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_NONE, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_NONE, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node5, "node6", GMC_OPERATION_REMOVE_GRAPH, &node6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    // 事务提交
    ret = TestTransCommitAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012.6层，全部节点设置none，对node6做merge操作
TEST_F(newErrorCode, Yang_021_newErrorCode_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};

    // 创建表
    SixContainerVertex(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // node6
    GmcNodeT *root = NULL;
    GmcNodeT *node1 = NULL;
    GmcNodeT *node2 = NULL;
    GmcNodeT *node3 = NULL;
    GmcNodeT *node4 = NULL;
    GmcNodeT *node5 = NULL;
    GmcNodeT *node6 = NULL;
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_NONE, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_NONE, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_NONE, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_NONE, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_NONE, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node5, "node6", GMC_OPERATION_MERGE, &node6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
    data_batch.expectedErrMsg = "target not exists";
    data_batch.expectedErrPath = "/Container_root/node1/node2/node3/node4/node5";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/*******************************************************************************
    GMC_YANG_PROPERTY_OPERATION_CREATE,      创建字段值，字段有值则报错
    GMC_YANG_PROPERTY_OPERATION_MERGE,       修改字段值，不存在则创建，存在则更新
    GMC_YANG_PROPERTY_OPERATION_REPLACE,     修改字段值, 不存在则创建，存在则替换
    GMC_YANG_PROPERTY_OPERATION_DELETE,      删除字段值，字段无值则报错
    GMC_YANG_PROPERTY_OPERATION_REMOVE,      删除字段值
*******************************************************************************/

// 013.6层，不create node4、node5、node6，对node6字段做create操作
TEST_F(newErrorCode, Yang_021_newErrorCode_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};

    // 创建表
    SixContainerVertex(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root节点
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入node container
    GmcNodeT *root = NULL;
    GmcNodeT *node1 = NULL;
    GmcNodeT *node2 = NULL;
    GmcNodeT *node3 = NULL;
    GmcNodeT *node4 = NULL;
    GmcNodeT *node5 = NULL;
    GmcNodeT *node6 = NULL;
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_INSERT, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_INSERT, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_INSERT, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // node6
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_NONE, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_NONE, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_NONE, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_NONE, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_NONE, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node5, "node6", GMC_OPERATION_NONE, &node6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int f0_value = 1;
    GmcPropValueT propValue = {0};
    YangInitPropValue(&propValue, "F0", GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    ret = GmcYangSetNodeProperty(node6, &propValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
    data_batch.expectedErrMsg = "target not exists";
    data_batch.expectedErrPath = "/Container_root/node1/node2/node3/node4/node5/node6";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014.6层，不create node4、node5、node6，对node6字段做merge操作
TEST_F(newErrorCode, Yang_021_newErrorCode_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};

    // 创建表
    SixContainerVertex(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root节点
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入node container
    GmcNodeT *root = NULL;
    GmcNodeT *node1 = NULL;
    GmcNodeT *node2 = NULL;
    GmcNodeT *node3 = NULL;
    GmcNodeT *node4 = NULL;
    GmcNodeT *node5 = NULL;
    GmcNodeT *node6 = NULL;
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_INSERT, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_INSERT, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_INSERT, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // node6
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_NONE, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_NONE, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_NONE, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_NONE, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_NONE, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node5, "node6", GMC_OPERATION_NONE, &node6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int f0_value = 1;
    GmcPropValueT propValue = {0};
    YangInitPropValue(&propValue, "F0", GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    ret = GmcYangSetNodeProperty(node6, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
    data_batch.expectedErrMsg = "target not exists";
    data_batch.expectedErrPath = "/Container_root/node1/node2/node3/node4/node5/node6";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015.6层，不create node4、node5、node6，对node6字段做replace操作
TEST_F(newErrorCode, Yang_021_newErrorCode_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};

    // 创建表
    SixContainerVertex(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root节点
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入node container
    GmcNodeT *root = NULL;
    GmcNodeT *node1 = NULL;
    GmcNodeT *node2 = NULL;
    GmcNodeT *node3 = NULL;
    GmcNodeT *node4 = NULL;
    GmcNodeT *node5 = NULL;
    GmcNodeT *node6 = NULL;
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_INSERT, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_INSERT, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_INSERT, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // node6
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_NONE, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_NONE, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_NONE, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_NONE, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_NONE, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node5, "node6", GMC_OPERATION_NONE, &node6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int f0_value = 1;
    GmcPropValueT propValue = {0};
    YangInitPropValue(&propValue, "F0", GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    ret = GmcYangSetNodeProperty(node6, &propValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
    data_batch.expectedErrMsg = "target not exists";
    data_batch.expectedErrPath = "/Container_root/node1/node2/node3/node4/node5/node6";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016.6层，不create node4、node5、node6，对node6字段做delete操作
TEST_F(newErrorCode, Yang_021_newErrorCode_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};

    // 创建表
    SixContainerVertex(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root节点
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入node container
    GmcNodeT *root = NULL;
    GmcNodeT *node1 = NULL;
    GmcNodeT *node2 = NULL;
    GmcNodeT *node3 = NULL;
    GmcNodeT *node4 = NULL;
    GmcNodeT *node5 = NULL;
    GmcNodeT *node6 = NULL;
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_INSERT, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_INSERT, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_INSERT, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // node6
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_NONE, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_NONE, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_NONE, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_NONE, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_NONE, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node5, "node6", GMC_OPERATION_NONE, &node6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int f0_value = 1;
    GmcPropValueT propValue = {0};
    YangInitPropValue(&propValue, "F0", GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    ret = GmcYangSetNodeProperty(node6, &propValue, GMC_YANG_PROPERTY_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
    data_batch.expectedErrMsg = "target not exists";
    data_batch.expectedErrPath = "/Container_root/node1/node2/node3/node4/node5/node6/F0";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 017.6层，不create node4、node5、node6，对node6字段做remove操作
TEST_F(newErrorCode, Yang_021_newErrorCode_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};

    // 创建表
    SixContainerVertex(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root节点
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入node container
    GmcNodeT *root = NULL;
    GmcNodeT *node1 = NULL;
    GmcNodeT *node2 = NULL;
    GmcNodeT *node3 = NULL;
    GmcNodeT *node4 = NULL;
    GmcNodeT *node5 = NULL;
    GmcNodeT *node6 = NULL;
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_INSERT, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_INSERT, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_INSERT, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // node6
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_NONE, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_NONE, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_NONE, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_NONE, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_NONE, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node5, "node6", GMC_OPERATION_NONE, &node6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int f0_value = 1;
    GmcPropValueT propValue = {0};
    YangInitPropValue(&propValue, "F0", GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    ret = GmcYangSetNodeProperty(node6, &propValue, GMC_YANG_PROPERTY_OPERATION_REMOVE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    ret = TestTransCommitAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018.6层，全部节点设置none，对node6字段做create操作
TEST_F(newErrorCode, Yang_021_newErrorCode_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};

    // 创建表
    SixContainerVertex(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // node6
    GmcNodeT *root = NULL;
    GmcNodeT *node1 = NULL;
    GmcNodeT *node2 = NULL;
    GmcNodeT *node3 = NULL;
    GmcNodeT *node4 = NULL;
    GmcNodeT *node5 = NULL;
    GmcNodeT *node6 = NULL;
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_NONE, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_NONE, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_NONE, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_NONE, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_NONE, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node5, "node6", GMC_OPERATION_NONE, &node6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int f0_value = 1;
    GmcPropValueT propValue = {0};
    YangInitPropValue(&propValue, "F0", GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    ret = GmcYangSetNodeProperty(node6, &propValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
    data_batch.expectedErrMsg = "target not exists";
    data_batch.expectedErrPath = "/Container_root/node1/node2/node3/node4/node5/node6";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 019.6层，全部节点设置none，对node6字段做merge操作
TEST_F(newErrorCode, Yang_021_newErrorCode_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};

    // 创建表
    SixContainerVertex(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // node6
    GmcNodeT *root = NULL;
    GmcNodeT *node1 = NULL;
    GmcNodeT *node2 = NULL;
    GmcNodeT *node3 = NULL;
    GmcNodeT *node4 = NULL;
    GmcNodeT *node5 = NULL;
    GmcNodeT *node6 = NULL;
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_NONE, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_NONE, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_NONE, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_NONE, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_NONE, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node5, "node6", GMC_OPERATION_NONE, &node6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int f0_value = 1;
    GmcPropValueT propValue = {0};
    YangInitPropValue(&propValue, "F0", GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    ret = GmcYangSetNodeProperty(node6, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
    data_batch.expectedErrMsg = "target not exists";
    data_batch.expectedErrPath = "/Container_root/node1/node2/node3/node4/node5/node6";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 020.6层，全部节点设置none，对node6字段做replace操作
TEST_F(newErrorCode, Yang_021_newErrorCode_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};

    // 创建表
    SixContainerVertex(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // node6
    GmcNodeT *root = NULL;
    GmcNodeT *node1 = NULL;
    GmcNodeT *node2 = NULL;
    GmcNodeT *node3 = NULL;
    GmcNodeT *node4 = NULL;
    GmcNodeT *node5 = NULL;
    GmcNodeT *node6 = NULL;
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_NONE, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_NONE, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_NONE, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_NONE, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_NONE, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node5, "node6", GMC_OPERATION_NONE, &node6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int f0_value = 1;
    GmcPropValueT propValue = {0};
    YangInitPropValue(&propValue, "F0", GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    ret = GmcYangSetNodeProperty(node6, &propValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
    data_batch.expectedErrMsg = "target not exists";
    data_batch.expectedErrPath = "/Container_root/node1/node2/node3/node4/node5/node6";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 021.6层，全部节点设置none，对node6字段做delete操作
TEST_F(newErrorCode, Yang_021_newErrorCode_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};

    // 创建表
    SixContainerVertex(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // node6
    GmcNodeT *root = NULL;
    GmcNodeT *node1 = NULL;
    GmcNodeT *node2 = NULL;
    GmcNodeT *node3 = NULL;
    GmcNodeT *node4 = NULL;
    GmcNodeT *node5 = NULL;
    GmcNodeT *node6 = NULL;
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_NONE, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_NONE, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_NONE, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_NONE, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_NONE, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node5, "node6", GMC_OPERATION_NONE, &node6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int f0_value = 1;
    GmcPropValueT propValue = {0};
    YangInitPropValue(&propValue, "F0", GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    ret = GmcYangSetNodeProperty(node6, &propValue, GMC_YANG_PROPERTY_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
    data_batch.expectedErrMsg = "target not exists";
    data_batch.expectedErrPath = "/Container_root/node1/node2/node3/node4/node5/node6/F0";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 022.6层，全部节点设置none，对node6字段做remove操作
TEST_F(newErrorCode, Yang_021_newErrorCode_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};

    // 创建表
    SixContainerVertex(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // node6
    GmcNodeT *root = NULL;
    GmcNodeT *node1 = NULL;
    GmcNodeT *node2 = NULL;
    GmcNodeT *node3 = NULL;
    GmcNodeT *node4 = NULL;
    GmcNodeT *node5 = NULL;
    GmcNodeT *node6 = NULL;
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "node1", GMC_OPERATION_NONE, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1, "node2", GMC_OPERATION_NONE, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2, "node3", GMC_OPERATION_NONE, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3, "node4", GMC_OPERATION_NONE, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4, "node5", GMC_OPERATION_NONE, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node5, "node6", GMC_OPERATION_NONE, &node6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int f0_value = 1;
    GmcPropValueT propValue = {0};
    YangInitPropValue(&propValue, "F0", GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    ret = GmcYangSetNodeProperty(node6, &propValue, GMC_YANG_PROPERTY_OPERATION_REMOVE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    // 事务提交
    ret = TestTransCommitAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 023.root-list(choice case)，全部节点设置none，case insert
TEST_F(newErrorCode, Yang_021_newErrorCode_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};
    GmcStmtT *list_stmt_async = NULL;
    ret = GmcAllocStmt(conn_async, &list_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建root(container-list)表
    RootListChoiceCaseLabel(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *root = NULL;
    GmcNodeT *choice_node = NULL;
    GmcNodeT *case_node = NULL;

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // case
    int f0_value = 1;
    ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(list_stmt_async, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(list_stmt_async, 1, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetRootNode(list_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "choice_node", GMC_OPERATION_NONE, &choice_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(choice_node, "case_node", GMC_OPERATION_INSERT, &case_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
    data_batch.expectedErrMsg = "target not exists";
    data_batch.expectedErrPath = "/Container_root/list_child[F0=1]/choice_node";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(list_stmt_async);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 024.root-list(choice case)，全部节点设置none，case replace
TEST_F(newErrorCode, Yang_021_newErrorCode_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};
    GmcStmtT *list_stmt_async = NULL;
    ret = GmcAllocStmt(conn_async, &list_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建root(container-list)表
    RootListChoiceCaseLabel(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *root = NULL;
    GmcNodeT *choice_node = NULL;
    GmcNodeT *case_node = NULL;

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // case
    int f0_value = 1;
    ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(list_stmt_async, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(list_stmt_async, 1, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetRootNode(list_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "choice_node", GMC_OPERATION_NONE, &choice_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(choice_node, "case_node", GMC_OPERATION_REPLACE_GRAPH, &case_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
    data_batch.expectedErrMsg = "target not exists";
    data_batch.expectedErrPath = "/Container_root/list_child[F0=1]/choice_node";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(list_stmt_async);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 025.root-list(choice case)，全部节点设置none，case delete
TEST_F(newErrorCode, Yang_021_newErrorCode_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};
    GmcStmtT *list_stmt_async = NULL;
    ret = GmcAllocStmt(conn_async, &list_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建root(container-list)表
    RootListChoiceCaseLabel(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *root = NULL;
    GmcNodeT *choice_node = NULL;
    GmcNodeT *case_node = NULL;

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // case
    int f0_value = 1;
    ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(list_stmt_async, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(list_stmt_async, 1, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetRootNode(list_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "choice_node", GMC_OPERATION_NONE, &choice_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(choice_node, "case_node", GMC_OPERATION_DELETE_GRAPH, &case_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
    data_batch.expectedErrMsg = "target not exists";
    data_batch.expectedErrPath = "/Container_root/list_child[F0=1]/choice_node/case_node";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(list_stmt_async);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 026.root-list(choice case)，全部节点设置none，case merge
TEST_F(newErrorCode, Yang_021_newErrorCode_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};
    GmcStmtT *list_stmt_async = NULL;
    ret = GmcAllocStmt(conn_async, &list_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建root(container-list)表
    RootListChoiceCaseLabel(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *root = NULL;
    GmcNodeT *choice_node = NULL;
    GmcNodeT *case_node = NULL;

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // case
    int f0_value = 1;
    ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(list_stmt_async, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(list_stmt_async, 1, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetRootNode(list_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "choice_node", GMC_OPERATION_NONE, &choice_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(choice_node, "case_node", GMC_OPERATION_MERGE, &case_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
    data_batch.expectedErrMsg = "target not exists";
    data_batch.expectedErrPath = "/Container_root/list_child[F0=1]/choice_node";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(list_stmt_async);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 027.root-list(choice case)，全部节点设置none，case字段create
TEST_F(newErrorCode, Yang_021_newErrorCode_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};
    GmcStmtT *list_stmt_async = NULL;
    ret = GmcAllocStmt(conn_async, &list_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建root(container-list)表
    RootListChoiceCaseLabel(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int f0_value = 1;
    GmcNodeT *root = NULL;
    GmcNodeT *choice_node = NULL;
    GmcNodeT *case_node = NULL;

    GmcPropValueT propValue;
    YangInitPropValue(&propValue, "F0", GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // node case字段
    ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(list_stmt_async, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(list_stmt_async, 1, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetRootNode(list_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "choice_node", GMC_OPERATION_NONE, &choice_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(choice_node, "case_node", GMC_OPERATION_MERGE, &case_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetNodeProperty(case_node, &propValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
    data_batch.expectedErrMsg = "target not exists";
    data_batch.expectedErrPath = "/Container_root/list_child[F0=1]/choice_node";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(list_stmt_async);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 028.root-list(choice case)，全部节点设置none，case字段merge
TEST_F(newErrorCode, Yang_021_newErrorCode_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};
    GmcStmtT *list_stmt_async = NULL;
    ret = GmcAllocStmt(conn_async, &list_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建root(container-list)表
    RootListChoiceCaseLabel(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int f0_value = 1;
    GmcNodeT *root = NULL;
    GmcNodeT *choice_node = NULL;
    GmcNodeT *case_node = NULL;

    GmcPropValueT propValue;
    YangInitPropValue(&propValue, "F0", GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // node case字段
    ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(list_stmt_async, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(list_stmt_async, 1, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetRootNode(list_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "choice_node", GMC_OPERATION_NONE, &choice_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(choice_node, "case_node", GMC_OPERATION_MERGE, &case_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetNodeProperty(case_node, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
    data_batch.expectedErrMsg = "target not exists";
    data_batch.expectedErrPath = "/Container_root/list_child[F0=1]/choice_node";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(list_stmt_async);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 029.root-list(choice case)，全部节点设置none，case字段replace
TEST_F(newErrorCode, Yang_021_newErrorCode_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};
    GmcStmtT *list_stmt_async = NULL;
    ret = GmcAllocStmt(conn_async, &list_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建root(container-list)表
    RootListChoiceCaseLabel(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int f0_value = 1;
    GmcNodeT *root = NULL;
    GmcNodeT *choice_node = NULL;
    GmcNodeT *case_node = NULL;

    GmcPropValueT propValue;
    YangInitPropValue(&propValue, "F0", GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // node case字段
    ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(list_stmt_async, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(list_stmt_async, 1, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetRootNode(list_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "choice_node", GMC_OPERATION_NONE, &choice_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(choice_node, "case_node", GMC_OPERATION_MERGE, &case_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetNodeProperty(case_node, &propValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
    data_batch.expectedErrMsg = "target not exists";
    data_batch.expectedErrPath = "/Container_root/list_child[F0=1]/choice_node";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(list_stmt_async);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 030.root-list(choice case)，全部节点设置none，case字段delete
TEST_F(newErrorCode, Yang_021_newErrorCode_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};
    GmcStmtT *list_stmt_async = NULL;
    ret = GmcAllocStmt(conn_async, &list_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建root(container-list)表
    RootListChoiceCaseLabel(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int f0_value = 1;
    GmcNodeT *root = NULL;
    GmcNodeT *choice_node = NULL;
    GmcNodeT *case_node = NULL;

    GmcPropValueT propValue;
    YangInitPropValue(&propValue, "F0", GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // node case字段
    ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(list_stmt_async, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(list_stmt_async, 1, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetRootNode(list_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "choice_node", GMC_OPERATION_NONE, &choice_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(choice_node, "case_node", GMC_OPERATION_MERGE, &case_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetNodeProperty(case_node, &propValue, GMC_YANG_PROPERTY_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
    data_batch.expectedErrMsg = "target not exists";
    data_batch.expectedErrPath = "/Container_root/list_child[F0=1]/choice_node";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(list_stmt_async);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 031.root-list(choice case)，只插入root，list，case insert
TEST_F(newErrorCode, Yang_021_newErrorCode_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};
    GmcStmtT *list_stmt_async = NULL;
    ret = GmcAllocStmt(conn_async, &list_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建root(container-list)表
    RootListChoiceCaseLabel(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root节点
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入list
    int f0_value = 1;
    GmcNodeT *root = NULL;
    ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcPropValueT propValue;
    memcpy(propValue.propertyName, "F0", (strlen("F0") + 1));
    propValue.type = GMC_DATATYPE_INT32;
    propValue.value = &f0_value;
    propValue.size = sizeof(f0_value);
    ret = GmcYangSetVertexProperty(list_stmt_async, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *choice_node = NULL;
    GmcNodeT *case_node = NULL;

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重复insert node case
    ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(list_stmt_async, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(list_stmt_async, 1, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetRootNode(list_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "choice_node", GMC_OPERATION_NONE, &choice_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(choice_node, "case_node", GMC_OPERATION_INSERT, &case_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
    data_batch.expectedErrMsg = "target not exists";
    data_batch.expectedErrPath = "/Container_root/list_child[F0=1]/choice_node";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(list_stmt_async);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 032.root-list(choice case)，只插入root，list，case replace
TEST_F(newErrorCode, Yang_021_newErrorCode_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};
    GmcStmtT *list_stmt_async = NULL;
    ret = GmcAllocStmt(conn_async, &list_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建root(container-list)表
    RootListChoiceCaseLabel(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root节点
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入list
    int f0_value = 1;
    GmcNodeT *root = NULL;
    ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcPropValueT propValue;
    memcpy(propValue.propertyName, "F0", (strlen("F0") + 1));
    propValue.type = GMC_DATATYPE_INT32;
    propValue.value = &f0_value;
    propValue.size = sizeof(f0_value);
    ret = GmcYangSetVertexProperty(list_stmt_async, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *choice_node = NULL;
    GmcNodeT *case_node = NULL;

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重复insert node case
    ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(list_stmt_async, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(list_stmt_async, 1, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetRootNode(list_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "choice_node", GMC_OPERATION_NONE, &choice_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(choice_node, "case_node", GMC_OPERATION_REPLACE_GRAPH, &case_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
    data_batch.expectedErrMsg = "target not exists";
    data_batch.expectedErrPath = "/Container_root/list_child[F0=1]/choice_node";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(list_stmt_async);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 033.root-list(choice case)，只插入root，list，case delete
TEST_F(newErrorCode, Yang_021_newErrorCode_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};
    GmcStmtT *list_stmt_async = NULL;
    ret = GmcAllocStmt(conn_async, &list_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建root(container-list)表
    RootListChoiceCaseLabel(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root节点
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入list
    int f0_value = 1;
    GmcNodeT *root = NULL;
    ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcPropValueT propValue;
    memcpy(propValue.propertyName, "F0", (strlen("F0") + 1));
    propValue.type = GMC_DATATYPE_INT32;
    propValue.value = &f0_value;
    propValue.size = sizeof(f0_value);
    ret = GmcYangSetVertexProperty(list_stmt_async, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *choice_node = NULL;
    GmcNodeT *case_node = NULL;

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重复insert node case
    ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(list_stmt_async, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(list_stmt_async, 1, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetRootNode(list_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "choice_node", GMC_OPERATION_NONE, &choice_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(choice_node, "case_node", GMC_OPERATION_DELETE_GRAPH, &case_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
    data_batch.expectedErrMsg = "target not exists";
    data_batch.expectedErrPath = "/Container_root/list_child[F0=1]/choice_node/case_node";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(list_stmt_async);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 034.root-list(choice case)，只插入root，list，case merge
TEST_F(newErrorCode, Yang_021_newErrorCode_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};
    GmcStmtT *list_stmt_async = NULL;
    ret = GmcAllocStmt(conn_async, &list_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建root(container-list)表
    RootListChoiceCaseLabel(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root节点
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入list
    int f0_value = 1;
    GmcNodeT *root = NULL;
    ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcPropValueT propValue;
    memcpy(propValue.propertyName, "F0", (strlen("F0") + 1));
    propValue.type = GMC_DATATYPE_INT32;
    propValue.value = &f0_value;
    propValue.size = sizeof(f0_value);
    ret = GmcYangSetVertexProperty(list_stmt_async, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *choice_node = NULL;
    GmcNodeT *case_node = NULL;

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重复insert node case
    ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(list_stmt_async, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(list_stmt_async, 1, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetRootNode(list_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "choice_node", GMC_OPERATION_NONE, &choice_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(choice_node, "case_node", GMC_OPERATION_MERGE, &case_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
    data_batch.expectedErrMsg = "target not exists";
    data_batch.expectedErrPath = "/Container_root/list_child[F0=1]/choice_node";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(list_stmt_async);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 035.root-list(choice case)，只插入root，list，case字段create
TEST_F(newErrorCode, Yang_021_newErrorCode_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};
    GmcStmtT *list_stmt_async = NULL;
    ret = GmcAllocStmt(conn_async, &list_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建root(container-list)表
    RootListChoiceCaseLabel(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root节点
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入list
    int f0_value = 1;
    GmcNodeT *root = NULL;
    ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcPropValueT propValue;
    memcpy(propValue.propertyName, "F0", (strlen("F0") + 1));
    propValue.type = GMC_DATATYPE_INT32;
    propValue.value = &f0_value;
    propValue.size = sizeof(f0_value);
    ret = GmcYangSetVertexProperty(list_stmt_async, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *choice_node = NULL;
    GmcNodeT *case_node = NULL;

    ret = GmcBatchAddDML(batch, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重复insert已写值的node case字段
    ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(list_stmt_async, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(list_stmt_async, 1, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetRootNode(list_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "choice_node", GMC_OPERATION_NONE, &choice_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(choice_node, "case_node", GMC_OPERATION_MERGE, &case_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetNodeProperty(case_node, &propValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
    data_batch.expectedErrMsg = "target not exists";
    data_batch.expectedErrPath = "/Container_root/list_child[F0=1]/choice_node";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(list_stmt_async);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 036.root-list(choice case)，只插入root，list，case字段merge
TEST_F(newErrorCode, Yang_021_newErrorCode_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};
    GmcStmtT *list_stmt_async = NULL;
    ret = GmcAllocStmt(conn_async, &list_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建root(container-list)表
    RootListChoiceCaseLabel(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root节点
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入list
    int f0_value = 1;
    GmcNodeT *root = NULL;
    ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcPropValueT propValue;
    memcpy(propValue.propertyName, "F0", (strlen("F0") + 1));
    propValue.type = GMC_DATATYPE_INT32;
    propValue.value = &f0_value;
    propValue.size = sizeof(f0_value);
    ret = GmcYangSetVertexProperty(list_stmt_async, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *choice_node = NULL;
    GmcNodeT *case_node = NULL;

    ret = GmcBatchAddDML(batch, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重复insert已写值的node case字段
    ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(list_stmt_async, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(list_stmt_async, 1, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetRootNode(list_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "choice_node", GMC_OPERATION_NONE, &choice_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(choice_node, "case_node", GMC_OPERATION_MERGE, &case_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetNodeProperty(case_node, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
    data_batch.expectedErrMsg = "target not exists";
    data_batch.expectedErrPath = "/Container_root/list_child[F0=1]/choice_node";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(list_stmt_async);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 037.root-list(choice case)，只插入root，list，case字段replace
TEST_F(newErrorCode, Yang_021_newErrorCode_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};
    GmcStmtT *list_stmt_async = NULL;
    ret = GmcAllocStmt(conn_async, &list_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建root(container-list)表
    RootListChoiceCaseLabel(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root节点
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入list
    int f0_value = 1;
    GmcNodeT *root = NULL;
    ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcPropValueT propValue;
    memcpy(propValue.propertyName, "F0", (strlen("F0") + 1));
    propValue.type = GMC_DATATYPE_INT32;
    propValue.value = &f0_value;
    propValue.size = sizeof(f0_value);
    ret = GmcYangSetVertexProperty(list_stmt_async, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *choice_node = NULL;
    GmcNodeT *case_node = NULL;

    ret = GmcBatchAddDML(batch, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重复insert已写值的node case字段
    ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(list_stmt_async, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(list_stmt_async, 1, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetRootNode(list_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "choice_node", GMC_OPERATION_NONE, &choice_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(choice_node, "case_node", GMC_OPERATION_MERGE, &case_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetNodeProperty(case_node, &propValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
    data_batch.expectedErrMsg = "target not exists";
    data_batch.expectedErrPath = "/Container_root/list_child[F0=1]/choice_node";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(list_stmt_async);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 038.root-list(choice case)，只插入root，list，case字段delete
TEST_F(newErrorCode, Yang_021_newErrorCode_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};
    GmcStmtT *list_stmt_async = NULL;
    ret = GmcAllocStmt(conn_async, &list_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建root(container-list)表
    RootListChoiceCaseLabel(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root节点
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入list
    int f0_value = 1;
    GmcNodeT *root = NULL;
    ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcPropValueT propValue;
    memcpy(propValue.propertyName, "F0", (strlen("F0") + 1));
    propValue.type = GMC_DATATYPE_INT32;
    propValue.value = &f0_value;
    propValue.size = sizeof(f0_value);
    ret = GmcYangSetVertexProperty(list_stmt_async, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *choice_node = NULL;
    GmcNodeT *case_node = NULL;

    ret = GmcBatchAddDML(batch, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重复insert已写值的node case字段
    ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(list_stmt_async, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(list_stmt_async, 1, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetRootNode(list_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root, "choice_node", GMC_OPERATION_NONE, &choice_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(choice_node, "case_node", GMC_OPERATION_MERGE, &case_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetNodeProperty(case_node, &propValue, GMC_YANG_PROPERTY_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
    data_batch.expectedErrMsg = "target not exists";
    data_batch.expectedErrPath = "/Container_root/list_child[F0=1]/choice_node";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(list_stmt_async);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 039.root--list(container)，全部设置none，孩子container insert
TEST_F(newErrorCode, Yang_021_newErrorCode_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};
    GmcStmtT *list_stmt_async = NULL;

    ret = GmcAllocStmt(conn_async, &list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建root(container-list)表
    RootListContainerLabel(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int f0_value = 1;
    GmcPropValueT propValue = {0};
    YangInitPropValue(&propValue, "F0", GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));

    GmcNodeT *list = NULL;
    GmcNodeT *con_node = NULL;

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // node container
    ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(list_stmt_async, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(list_stmt_async, 1, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetRootNode(list_stmt_async, &list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(list, "con_node", GMC_OPERATION_INSERT, &con_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
    data_batch.expectedErrMsg = "target not exists";
    data_batch.expectedErrPath = "/Container_root/list_child[F0=1]";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(list_stmt_async);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 040.root--list(container)，全部设置none，孩子container replace
TEST_F(newErrorCode, Yang_021_newErrorCode_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};
    GmcStmtT *list_stmt_async = NULL;

    ret = GmcAllocStmt(conn_async, &list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建root(container-list)表
    RootListContainerLabel(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int f0_value = 1;
    GmcPropValueT propValue = {0};
    YangInitPropValue(&propValue, "F0", GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));

    GmcNodeT *list = NULL;
    GmcNodeT *con_node = NULL;

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // node container
    ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(list_stmt_async, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(list_stmt_async, 1, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetRootNode(list_stmt_async, &list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(list, "con_node", GMC_OPERATION_REPLACE_GRAPH, &con_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
    data_batch.expectedErrMsg = "target not exists";
    data_batch.expectedErrPath = "/Container_root/list_child[F0=1]";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(list_stmt_async);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 041.root--list(container)，全部设置none，孩子container delete
TEST_F(newErrorCode, Yang_021_newErrorCode_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};
    GmcStmtT *list_stmt_async = NULL;

    ret = GmcAllocStmt(conn_async, &list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建root(container-list)表
    RootListContainerLabel(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int f0_value = 1;
    GmcPropValueT propValue = {0};
    YangInitPropValue(&propValue, "F0", GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));

    GmcNodeT *list = NULL;
    GmcNodeT *con_node = NULL;

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // node container
    ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(list_stmt_async, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(list_stmt_async, 1, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetRootNode(list_stmt_async, &list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(list, "con_node", GMC_OPERATION_DELETE_GRAPH, &con_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
    data_batch.expectedErrMsg = "target not exists";
    data_batch.expectedErrPath = "/Container_root/list_child[F0=1]/con_node";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(list_stmt_async);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 042.root--list(container)，全部设置none，孩子container merge
TEST_F(newErrorCode, Yang_021_newErrorCode_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};
    GmcStmtT *list_stmt_async = NULL;

    ret = GmcAllocStmt(conn_async, &list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建root(container-list)表
    RootListContainerLabel(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int f0_value = 1;
    GmcPropValueT propValue = {0};
    YangInitPropValue(&propValue, "F0", GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));

    GmcNodeT *list = NULL;
    GmcNodeT *con_node = NULL;

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // node container
    ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(list_stmt_async, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(list_stmt_async, 1, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetRootNode(list_stmt_async, &list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(list, "con_node", GMC_OPERATION_MERGE, &con_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
    data_batch.expectedErrMsg = "target not exists";
    data_batch.expectedErrPath = "/Container_root/list_child[F0=1]";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(list_stmt_async);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 043.root--list(container)，全部设置none，孩子container字段create
TEST_F(newErrorCode, Yang_021_newErrorCode_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};
    GmcStmtT *list_stmt_async = NULL;

    ret = GmcAllocStmt(conn_async, &list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建root(container-list)表
    RootListContainerLabel(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root节点
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入list
    int f0_value = 1;
    ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *list = NULL;
    GmcPropValueT propValue = {0};
    ret = GmcGetRootNode(list_stmt_async, &list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangInitPropValue(&propValue, "F0", GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    ret = GmcYangSetNodeProperty(list, &propValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // node container
    GmcNodeT *con_node = NULL;
    ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(list_stmt_async, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(list_stmt_async, 1, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetRootNode(list_stmt_async, &list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(list, "con_node", GMC_OPERATION_NONE, &con_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangInitPropValue(&propValue, "F0", GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    ret = GmcYangSetNodeProperty(con_node, &propValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
    data_batch.expectedErrMsg = "target not exists";
    data_batch.expectedErrPath = "/Container_root/list_child[F0=1]/con_node";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(list_stmt_async);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 044.root--list(container)，全部设置none，孩子container字段merge
TEST_F(newErrorCode, Yang_021_newErrorCode_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};
    GmcStmtT *list_stmt_async = NULL;

    ret = GmcAllocStmt(conn_async, &list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建root(container-list)表
    RootListContainerLabel(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root节点
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入list
    int f0_value = 1;
    ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *list = NULL;
    GmcPropValueT propValue = {0};
    ret = GmcGetRootNode(list_stmt_async, &list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangInitPropValue(&propValue, "F0", GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    ret = GmcYangSetNodeProperty(list, &propValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // node container
    GmcNodeT *con_node = NULL;
    ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(list_stmt_async, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(list_stmt_async, 1, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetRootNode(list_stmt_async, &list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(list, "con_node", GMC_OPERATION_NONE, &con_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangInitPropValue(&propValue, "F0", GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    ret = GmcYangSetNodeProperty(con_node, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
    data_batch.expectedErrMsg = "target not exists";
    data_batch.expectedErrPath = "/Container_root/list_child[F0=1]/con_node";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(list_stmt_async);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 045.root--list(container)，全部设置none，孩子container字段replace
TEST_F(newErrorCode, Yang_021_newErrorCode_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};
    GmcStmtT *list_stmt_async = NULL;

    ret = GmcAllocStmt(conn_async, &list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建root(container-list)表
    RootListContainerLabel(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root节点
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入list
    int f0_value = 1;
    ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *list = NULL;
    GmcPropValueT propValue = {0};
    ret = GmcGetRootNode(list_stmt_async, &list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangInitPropValue(&propValue, "F0", GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    ret = GmcYangSetNodeProperty(list, &propValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // node container
    GmcNodeT *con_node = NULL;
    ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(list_stmt_async, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(list_stmt_async, 1, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetRootNode(list_stmt_async, &list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(list, "con_node", GMC_OPERATION_NONE, &con_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangInitPropValue(&propValue, "F0", GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    ret = GmcYangSetNodeProperty(con_node, &propValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
    data_batch.expectedErrMsg = "target not exists";
    data_batch.expectedErrPath = "/Container_root/list_child[F0=1]/con_node";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(list_stmt_async);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 046.root--list(container)，全部设置none，孩子container字段delete
TEST_F(newErrorCode, Yang_021_newErrorCode_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};
    GmcStmtT *list_stmt_async = NULL;

    ret = GmcAllocStmt(conn_async, &list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建root(container-list)表
    RootListContainerLabel(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root节点
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入list
    int f0_value = 1;
    ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *list = NULL;
    GmcPropValueT propValue = {0};
    ret = GmcGetRootNode(list_stmt_async, &list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangInitPropValue(&propValue, "F0", GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    ret = GmcYangSetNodeProperty(list, &propValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data_batch.status);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async, "Container_root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // node container
    GmcNodeT *con_node = NULL;
    ret = testGmcPrepareStmtByLabelName(list_stmt_async, "list_child", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, root_stmt_async, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(list_stmt_async, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(list_stmt_async, 1, GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetRootNode(list_stmt_async, &list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(list, "con_node", GMC_OPERATION_NONE, &con_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangInitPropValue(&propValue, "F0", GMC_DATATYPE_INT32, &f0_value, sizeof(f0_value));
    ret = GmcYangSetNodeProperty(con_node, &propValue, GMC_YANG_PROPERTY_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, list_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
    data_batch.expectedErrMsg = "target not exists";
    data_batch.expectedErrPath = "/Container_root/list_child[F0=1]/con_node/F0";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(list_stmt_async);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 047.32层模型全部节点none
TEST_F(newErrorCode, Yang_021_newErrorCode_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    AsyncUserDataT data_batch = {0};

    // 创建表
    LongContainerVertex(root_stmt_async);

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入node container
    GmcNodeT *root = NULL;
    GmcNodeT *node1 = NULL;
    GmcNodeT *node2 = NULL;
    GmcNodeT *node3 = NULL;
    GmcNodeT *node4 = NULL;
    GmcNodeT *node5 = NULL;
    GmcNodeT *node6 = NULL;
    GmcNodeT *node7 = NULL;
    GmcNodeT *node8 = NULL;
    GmcNodeT *node9 = NULL;
    GmcNodeT *node10 = NULL;
    GmcNodeT *node11 = NULL;
    GmcNodeT *node12 = NULL;
    GmcNodeT *node13 = NULL;
    GmcNodeT *node14 = NULL;
    GmcNodeT *node15 = NULL;
    GmcNodeT *node16 = NULL;
    GmcNodeT *node17 = NULL;
    GmcNodeT *node18 = NULL;
    GmcNodeT *node19 = NULL;
    GmcNodeT *node20 = NULL;
    GmcNodeT *node21 = NULL;
    GmcNodeT *node22 = NULL;
    GmcNodeT *node23 = NULL;
    GmcNodeT *node24 = NULL;
    GmcNodeT *node25 = NULL;
    GmcNodeT *node26 = NULL;
    GmcNodeT *node27 = NULL;
    GmcNodeT *node28 = NULL;
    GmcNodeT *node29 = NULL;
    GmcNodeT *node30 = NULL;
    GmcNodeT *node31 = NULL;

    // root none
    ret = testGmcPrepareStmtByLabelName(root_stmt_async,
                                    "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                    "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                    "aaaaaaaaaaaaaaaaaaaaaaaaaaaaa", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // node container
    ret = GmcGetRootNode(root_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(root,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa1",
                               GMC_OPERATION_NONE, &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node1,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa2",
                               GMC_OPERATION_NONE, &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node2,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa3",
                               GMC_OPERATION_NONE, &node3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node3,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa4",
                               GMC_OPERATION_NONE, &node4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node4,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa5",
                               GMC_OPERATION_NONE, &node5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node5,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa6",
                               GMC_OPERATION_NONE, &node6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node6,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa7",
                               GMC_OPERATION_NONE, &node7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node7,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa8",
                               GMC_OPERATION_NONE, &node8);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node8,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa9",
                               GMC_OPERATION_NONE, &node9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node9,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa10",
                               GMC_OPERATION_NONE, &node10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node10,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa11",
                               GMC_OPERATION_NONE, &node11);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node11,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa12",
                               GMC_OPERATION_NONE, &node12);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node12,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa13",
                               GMC_OPERATION_NONE, &node13);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node13,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa14",
                               GMC_OPERATION_NONE, &node14);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node14,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa15",
                               GMC_OPERATION_NONE, &node15);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node15,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa16",
                               GMC_OPERATION_NONE, &node16);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node16,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa17",
                               GMC_OPERATION_NONE, &node17);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node17,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa18",
                               GMC_OPERATION_NONE, &node18);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node18,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa19",
                               GMC_OPERATION_NONE, &node19);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node19,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa20",
                               GMC_OPERATION_NONE, &node20);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node20,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa21",
                               GMC_OPERATION_NONE, &node21);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node21,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa22",
                               GMC_OPERATION_NONE, &node22);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node22,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa23",
                               GMC_OPERATION_NONE, &node23);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node23,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa24",
                               GMC_OPERATION_NONE, &node24);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node24,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa25",
                               GMC_OPERATION_NONE, &node25);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node25,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa26",
                               GMC_OPERATION_NONE, &node26);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node26,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa27",
                               GMC_OPERATION_NONE, &node27);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node27,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa28",
                               GMC_OPERATION_NONE, &node28);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node28,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa29",
                               GMC_OPERATION_NONE, &node29);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node29,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa30",
                               GMC_OPERATION_NONE, &node30);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(node30,
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                              "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa31",
                               GMC_OPERATION_INSERT, &node31);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_DELETE;
    data_batch.expectedErrMsg = "target not exists";
    data_batch.expectedErrPath = "/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaa/aaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaa1/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa2/aaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaa3/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa4/aaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaa5/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa6/aaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaa7/aaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "a8/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaa9/aaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaa10/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa11/aaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaa12/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa13/aaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaa14/aaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa15/a"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaa16/aaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aa17/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaa18/aaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaa19/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa20/aaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaa21/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa22/aaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaa23/aaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa24"
                                 "/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaa25/aaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaa26/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa27/aaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaa28/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa29/aaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                                 "aaaaaaaaaaaaaaaa30";

    ret = GmcBatchExecuteAsync(batch, errorpath_batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data_batch.status);

    // 事务回滚
    ret = TestTransRollBackAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

