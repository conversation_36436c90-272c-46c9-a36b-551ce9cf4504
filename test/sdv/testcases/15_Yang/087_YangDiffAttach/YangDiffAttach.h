/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 087_YangDiffAttach
 * Author: hanyang
 * Create: 2024-05-09
 */
#ifndef YANG_DIFF_ATTACH_H
#define YANG_DIFF_ATTACH_H
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/epoll.h>
#include <assert.h>
#include <errno.h>
#include <stdint.h>
#include <typeinfo>
#include <limits.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "jansson.h"

// MS config
const char *g_msConfig = "{\"max_record_count\" : 1000000}";
const char *g_msConfigTrans = R"(
{
    "max_record_count":1000000,
    "isFastReadUncommitted":0,
    "auto_increment":1,
    "yang_model":1
})";


const char *g_keyName = "PK";

GmcTxConfigT g_mSTrxConfig;
const char *g_namespace1 = "NamespaceABC087_1";
const char *g_namespace2 = "NamespaceABC087_2";
const char *g_namespace3 = "NamespaceABC087_3";
const char *g_namespaceUserName = "abc";

uint32_t g_printFlag;

void TestCreateLabel(GmcStmtT *stmt)
{
    int ret = 0;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = {0};

    readJanssonFile("./schema_file/yang_01.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateVertexLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile("./schema_file/edge_01.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateEdgeLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile("./schema_file/yang_02_xpath.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateVertexLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile("./schema_file/edge_02_xpath.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateEdgeLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile("./schema_file/yang_03_big.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateVertexLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile("./schema_file/edge_03_big.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateEdgeLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
}

void TestCreateLabel04(GmcStmtT *stmt)
{
    int ret = 0;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = {0};

    readJanssonFile("./schema_file/yang_04_ns2.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateVertexLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile("./schema_file/edge_04_ns2.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateEdgeLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
}

void TestCreateLabel05(GmcStmtT *stmt)
{
    int ret = 0;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = {0};

    readJanssonFile("./schema_file/yang_05_ns2.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateVertexLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile("./schema_file/edge_05_ns2.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateEdgeLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
}

void TestCreateLabel06(GmcStmtT *stmt)
{
    int ret = 0;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = {0};

    readJanssonFile("./schema_file/yang_06_ns2.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateVertexLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile("./schema_file/edge_06_ns2.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateEdgeLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
}

void TestCreateLabel07(GmcStmtT *stmt)
{
    int ret = 0;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = {0};

    readJanssonFile("./schema_file/yang_07_ns2.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateVertexLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile("./schema_file/edge_07_ns2.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateEdgeLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
}

void TestCreateLabel08(GmcStmtT *stmt)
{
    int ret = 0;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = {0};

    readJanssonFile("./schema_file/yang_08_ns2.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateVertexLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile("./schema_file/edge_08_ns2.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateEdgeLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
}

void TestCreateLabel09(GmcStmtT *stmt)
{
    int ret = 0;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = {0};

    readJanssonFile("./schema_file/yang_09_xpath_ns2.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateVertexLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile("./schema_file/edge_09_xpath_ns2.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateEdgeLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
}

int TestCheckNull(GmcStmtT *stmt)
{
    if (stmt == NULL) {
        return -1;
    } else {
        return GMERR_OK;
    }
}

/******************************DML*******************************************/
int TestBatchPrepare(GmcConnT *conn, GmcBatchT **batch, GmcBatchTypeE batchType = GMC_BATCH_YANG,
    GmcYangDiffTypeE diffType = GMC_YANG_DIFF_DELAY_READ_ON)
{
    int ret = 0;
    GmcBatchOptionT batchOption;

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 32768);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBatchType(&batchOption, batchType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcYangBatchOptionSetDiffType(&batchOption, diffType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }

    ret = GmcBatchPrepare(conn, &batchOption, batch);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }

    return ret;
}

int TestBatchPrepareNormal(GmcConnT *conn, GmcBatchT **batch, GmcBatchTypeE batchType = GMC_BATCH_YANG,
    GmcYangDiffTypeE diffType = GMC_YANG_DIFF_DELAY_READ_ON)
{
    int ret = 0;
    GmcBatchOptionT batchOption;

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBatchType(&batchOption, batchType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcYangBatchOptionSetDiffType(&batchOption, diffType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }

    ret = GmcBatchPrepare(conn, &batchOption, batch);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }

    return ret;
}

int TestTransStartAsync(GmcConnT *conn, GmcTxConfigT Config)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransStartAsync(conn, &Config, trans_start_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        return ret;
    }
}

AsyncUserDataT data1;
int TestTransCommitAsync(GmcConnT *conn)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (data.status == GMERR_TRANSACTION_ROLLBACK) {
            AW_FUN_Log(LOG_INFO, "data.status = GMERR_TRANSACTION_ROLLBACK, the transaction will rollback.");
            memset(&data1, 0, sizeof(AsyncUserDataT));
            int ret1 = GmcTransRollBackAsync(conn, trans_rollback_callback, &data1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        }
        return ret;
    }
}

int TestTransRollBackAsync(GmcConnT *conn)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransRollBackAsync(conn, trans_commit_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

        return ret;
    }
}

/******************************校验*******************************************/
struct ValidateParam {
    std::atomic_uint32_t *step;
    int32_t exceptStatus;  // 预期的操作状态
    GmcValidateResT validateRes;     // 预期返回的mandatory校验结果
    bool isValidateErrorPath;
    GmcErrorPathCodeE expectedErrCode;
    uint32_t expectedErrClauseIndex;
    const char *expectedErrMsg;
    const char *expectedErrPath;
    uint64_t startTime;
    bool printTime;
    bool printSize;
};

void AsyncValidateCb(void *userData, GmcValidateResT validateRes, int32_t status, const char *errMsg)
{
    ValidateParam *param = (ValidateParam *)userData;
    EXPECT_EQ(param->exceptStatus, status) << errMsg;

    // 只有返回无异常时才去校验mandatory
    if (GMERR_OK == status) {
        AW_MACRO_EXPECT_EQ_INT(validateRes.validateRes, param->validateRes.validateRes);
        AW_MACRO_EXPECT_EQ_INT(validateRes.failCount, param->validateRes.failCount);
    }

    if (param->isValidateErrorPath) {
        GmcErrorPathInfoT msg;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangGetErrorPathInfo(&msg));
        // 结果检查
        AW_MACRO_EXPECT_EQ_INT(param->expectedErrCode, msg.errorCode);
        if (msg.errorCode != GMC_VIOLATES_BUTT) {
            AW_MACRO_EXPECT_EQ_INT(param->expectedErrClauseIndex, msg.errorClauseIndex);
            AW_MACRO_EXPECT_EQ_STR(param->expectedErrMsg, msg.errorMsg);
            AW_MACRO_EXPECT_EQ_STR(param->expectedErrPath, msg.errorPath);
            ASSERT_NO_FATAL_FAILURE(GmcYangFreeErrorPathInfo());
        }
    }
    (*(param->step))++;
}

int testWaitValidateAsyncRecv(void *userData, int expRecvNum = 1, int timeout = -1, bool isAutoReset = true,
    int32_t epollFd = g_epollDataOneThread.userEpollFd)
{
    int waitCnt = 0;
    struct timeval start;
    struct timeval end;
    unsigned long duration;

    gettimeofday(&start, NULL);
    struct epoll_event events[MAX_EPOLL_EVENT_COUNT];
    ValidateParam *userDatas = (ValidateParam *)userData;
    int num = *(userDatas->step);
    if (num != 0) {
        printf("%d\n", num);
    }
    while (*(userDatas->step) != expRecvNum) {
        int fdCount = epoll_wait(epollFd, events, MAX_EPOLL_EVENT_COUNT, EPOLL_TIME_OUT_MS);
        if (fdCount < 0) {
            continue;
        }
        while (fdCount > 0) {
            --fdCount;
            if (g_runMode == 1) {
                GmcHandleEvent(events[fdCount].data.fd);
            } else {
                GmcHandleRWEvent(events[fdCount].data.fd, events[fdCount].events);
            }
        }
        if (timeout > 0 && waitCnt >= timeout) {
            gettimeofday(&end, NULL);
            duration = 1000000 * (end.tv_sec - start.tv_sec) + end.tv_usec - start.tv_usec;
            printf("[INFO] Recv Timeout %lf s, all OpNum : %d,\actually recived num : %d\n", (double)duration / 1000000,
                expRecvNum, userDatas->step);
            return -1;  // 接收超时
        }
    }
    if (isAutoReset) {
        userDatas->step = 0;
    }
    return 0;
}

// exceptRes 预期mandatory校验结果，exceptStatus 预期返回状态，apiSupport 校验接口返回状态
void WhenDataCheck(GmcStmtT *stmt, bool exceptRes, int32_t exceptStatus = GMERR_OK,
    uint32_t apiSupport = GMERR_OK, uint32_t checkType = GMC_YANG_VALIDATION_ALL_FORCE)
{
    int ret = 0;
    bool isDataService = true;

    std::atomic_uint32_t step{0};
    GmcValidateResT validateRes {.validateRes = exceptRes};
    ValidateParam param = {.step = &step, .exceptStatus = exceptStatus, .validateRes = validateRes};
    GmcValidateConfigT cfg = {.type = checkType, .cfgJson = NULL};
    ret = GmcYangValidateAsync(stmt, &cfg, AsyncValidateCb, &param);
    AW_MACRO_EXPECT_EQ_INT(apiSupport, ret);

    ret = testWaitValidateAsyncRecv(&param);
    AW_MACRO_EXPECT_EQ_INT(exceptStatus, param.exceptStatus);
}

void ModelCheck(GmcStmtT *stmt)
{
    int ret = 0;

    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(stmt, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(true, checkData.validateRes);
    if (checkData.validateRes == false) {
        AW_FUN_Log(LOG_INFO, "GmcYangValidateModelAsync result is false, failcount is %d.", checkData.failCount);
    }
    memset(&checkData, 0, sizeof(YangValidateUserDataT));
}

void ModelCheckAndDataCheck(GmcConnT *conn, GmcStmtT *stmt, uint32_t checkType = GMC_YANG_VALIDATION_ALL_FORCE)
{
    int ret;

    // 模型校验
    ModelCheck(stmt);

    // 启动事务
    ret = TestTransStartAsync(conn, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 数据校验
    WhenDataCheck(stmt, true, GMERR_OK, GMERR_OK, checkType);

    // 提交事务
    ret = TestTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

int TestYangSetField(GmcNodeT *node, GmcDataTypeE type, void *value, uint32_t size,
    const char *fieldName, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    int ret1 = 0;

    GmcPropValueT propValue;
    memcpy(propValue.propertyName, fieldName, (strlen(fieldName) + 1));
    propValue.type = type;
    propValue.value = value;
    propValue.size = size;
    ret = GmcYangSetNodeProperty(node, &propValue, opType);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_DEBUG, "SetProperty failed fieldName(%s)\n", fieldName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret1 = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
        return ret;
    }

    return ret;
}

int TestYangSetFieldID(GmcNodeT *node, GmcDataTypeE type, void *value, uint32_t size,
    const char *fieldName, GmcYangPropOpTypeE opType, GmcAttributeTypeE attrType = GMC_ATTRIBUTE_NAME)
{
    int ret = 0;
    int ret1 = 0;

    GmcAttributePropertyT attrProperty;
    attrProperty.type = attrType;
    attrProperty.size = size;
    attrProperty.value = value;

    GmcPropValueT propValue;
    memcpy(propValue.propertyName, fieldName, (strlen(fieldName) + 1));
    propValue.type = type;
    propValue.value = (void *)&attrProperty;
    propValue.size = sizeof(attrProperty);
    ret = GmcYangSetNodeProperty(node, &propValue, opType);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_DEBUG, "SetProperty failed fieldName(%s)\n", fieldName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret1 = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
        return ret;
    }

    return ret;
}

// 只有list类型需要设置主键
void TestYangSetNodeProperty_PK(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    uint32_t pkValue = i;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestYangSetNodeProperty_PKStr(GmcNodeT *node, const char *strValue, GmcYangPropOpTypeE opType)
{
    int ret = 0;

    ret = TestYangSetField(node, GMC_DATATYPE_STRING, (void*)strValue, (strlen(strValue)), "PK", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestYangSetIDName(GmcNodeT *node, const char *fieldName, const char* name, uint32_t size,
    GmcYangPropOpTypeE opType)
{
    int ret = 0;
    AW_MACRO_EXPECT_NE_INT(0, size);

    char nameSet[100] = {0};
    (void)memcpy_s(nameSet, size, name, size);
    ret = TestYangSetFieldID(node, GMC_DATATYPE_IDENTITY, nameSet, size, fieldName, opType, GMC_ATTRIBUTE_NAME);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestYangSetIDValue(GmcNodeT *node, const char *fieldName, int32_t value, GmcYangPropOpTypeE opType)
{
    int ret = 0;

    int32_t valueSet = value;
    ret = TestYangSetFieldID(node, GMC_DATATYPE_IDENTITY, &valueSet, sizeof(int32_t), fieldName,
        opType, GMC_ATTRIBUTE_VALUE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestYangSetNodePropertyBasic(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    uint32_t value = i;

    uint32_t valueF0 = value;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = value;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF2 = value;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF3 = value;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = value;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestYangSetNodePropertyAllType(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;

    // 设置all type属性值
    int8_t value8 = i;
    uint8_t valueU8 = i;
    int16_t value16 = i;
    uint16_t valueU16 = i;

    char valueF5 = value8;
    ret = TestYangSetField(node, GMC_DATATYPE_CHAR, &valueF5, sizeof(char), "F5", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    unsigned char valueF6 = valueU8;
    ret = TestYangSetField(node, GMC_DATATYPE_UCHAR, &valueF6, sizeof(unsigned char), "F6", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int8_t valueF7 = value8;
    ret = TestYangSetField(node, GMC_DATATYPE_INT8, &valueF7, sizeof(int8_t), "F7", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t valueF8 = valueU8;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT8, &valueF8, sizeof(uint8_t), "F8", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int16_t valueF9 = value16;
    ret = TestYangSetField(node, GMC_DATATYPE_INT16, &valueF9, sizeof(int16_t), "F9", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint16_t valueF10 = valueU16;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT16, &valueF10, sizeof(uint16_t), "F10", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t valueF11 = i;
    ret = TestYangSetField(node, GMC_DATATYPE_INT32, &valueF11, sizeof(int32_t), "F11", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF12 = i;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF12, sizeof(uint32_t), "F12", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int64_t valueF13 = i;
    ret = TestYangSetField(node, GMC_DATATYPE_INT64, &valueF13, sizeof(int64_t), "F13", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t valueF14 = i;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT64, &valueF14, sizeof(uint64_t), "F14", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool valueF15 = true;
    ret = TestYangSetField(node, GMC_DATATYPE_BOOL, &valueF15, sizeof(bool), "F15", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    float valueF16 = i;
    ret = TestYangSetField(node, GMC_DATATYPE_FLOAT, &valueF16, sizeof(float), "F16", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    double valueF17 = i;
    ret = TestYangSetField(node, GMC_DATATYPE_DOUBLE, &valueF17, sizeof(double), "F17", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t valueF18 = i;
    ret = TestYangSetField(node, GMC_DATATYPE_TIME, &valueF18, sizeof(uint64_t), "F18", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char stringValue[8] = "string";
    ret = TestYangSetField(node, GMC_DATATYPE_STRING, stringValue, (strlen(stringValue)), "F19", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangSetField(node, GMC_DATATYPE_BYTES, stringValue, (strlen(stringValue)), "F20", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangSetField(node, GMC_DATATYPE_FIXED, stringValue, 7, "F21", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // bitmap值的设置
    GmcBitMapT bitMap = {0, 127, NULL};  // 128 10000000  1024可以存储8*128个1
    uint8_t bits[128 / 8];
    memset(bits, 0xffff, 128 / 8);
    bits[128 / 8 - 1] = '\0';
    bitMap.bits = bits;

    ret = TestYangSetField(node, GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap), "F22", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t valueSet = -1;
    ret = TestYangSetFieldID(node, GMC_DATATYPE_ENUM, &valueSet, sizeof(int32_t), "F23",
        opType, GMC_ATTRIBUTE_VALUE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueSet = 1;
    ret = TestYangSetFieldID(node, GMC_DATATYPE_IDENTITY, &valueSet, sizeof(int32_t), "F24",
        opType, GMC_ATTRIBUTE_VALUE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestYangSetNodePropertyDefault(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    uint32_t value = i;

    uint32_t valueF0 = value;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F1写入和默认值相同值
    uint32_t valueF1 = 111;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F2写入和默认值不同值
    uint32_t valueF2 = value;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F3,F4不写入默认值
}

void TestYangSetNodePropertyNum(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType, uint32_t cycleNum,
    uint32_t size = 65535)
{
    int ret = 0;
    uint32_t value = i;

    for (uint32_t j = 1; j <= cycleNum; j++) {
        char fieldName[10] = {0};
        (void)snprintf(fieldName, 10, "F%03d", j);

        // 写string数据
        uint32_t superSize = size;
        char *superValue = (char *)malloc(superSize);
        if (!superValue) {
            printf("malloc superValue failed.\n");
        }
        ASSERT_NE((void *)NULL, superValue);
        memset(superValue, 'A', (superSize - 1));
        superValue[superSize - 1] = '\0';

        ret = TestYangSetField(node, GMC_DATATYPE_STRING, superValue, (superSize - 1), fieldName, opType);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        free(superValue);
    }
}

void TestSetKeyNameAndValue(GmcStmtT *stmt, uint32_t keyvalue)
{
    int ret;

    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &keyvalue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, g_keyName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// insert root
void TestInsertRoot(GmcConnT *conn, const char * vertexName, uint32_t keyvalue)
{
    int ret;
    GmcBatchT *batch = NULL;
    GmcStmtT *stmt_root = NULL;
    GmcNodeT *rootNode = NULL;

    ret = GmcAllocStmt(conn, &stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt_root, vertexName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_root, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetNodePropertyBasic(rootNode, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(stmt_root);
}

// 写入数据
void TestInsertDataAll(GmcConnT *conn, const char * vertexName, uint32_t keyvalue)
{
    int ret;
    GmcBatchT *batch = NULL;

    GmcStmtT *stmt_root = NULL;
    GmcStmtT *stmt_list[100] = {0};
    GmcNodeT *rootNode = NULL;
    GmcNodeT *childNode[100] = {0};
    uint32_t fieldValue = 0;
    AsyncUserDataT data = {0};

    ret = GmcAllocStmt(conn, &stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 100; i++) {
        ret = GmcAllocStmt(conn, &stmt_list[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 模型校验和数据校验
    ModelCheckAndDataCheck(conn, stmt_root);

    // 启动事务
    ret = TestTransStartAsync(conn, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_root, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetNodePropertyBasic(rootNode, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置con_1
    ret = GmcYangEditChildNode(rootNode, "con_1", GMC_OPERATION_INSERT, &childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetNodePropertyBasic(childNode[1], keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodePropertyAllType(childNode[1], keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置con_2
    ret = GmcYangEditChildNode(rootNode, "con_2", GMC_OPERATION_INSERT, &childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetNodePropertyBasic(childNode[2], keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置con_2_1
    ret = GmcYangEditChildNode(childNode[2], "con_2_1", GMC_OPERATION_INSERT, &childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置con_3
    ret = GmcYangEditChildNode(rootNode, "con_3", GMC_OPERATION_INSERT, &childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetNodePropertyBasic(childNode[4], keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置con_3_1
    ret = GmcYangEditChildNode(childNode[4], "con_3_1", GMC_OPERATION_INSERT, &childNode[5]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置con_4
    ret = GmcYangEditChildNode(rootNode, "con_4", GMC_OPERATION_INSERT, &childNode[6]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetNodePropertyBasic(childNode[6], keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置con_5
    ret = GmcYangEditChildNode(rootNode, "con_5", GMC_OPERATION_INSERT, &childNode[7]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetNodePropertyBasic(childNode[7], keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置con_6
    ret = GmcYangEditChildNode(rootNode, "con_6", GMC_OPERATION_INSERT, &childNode[8]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetNodePropertyBasic(childNode[8], keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置con_7
    ret = GmcYangEditChildNode(rootNode, "con_7", GMC_OPERATION_INSERT, &childNode[9]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetNodePropertyBasic(childNode[9], keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置case_1_1
    ret = GmcYangEditChildNode(rootNode, "choice_1", GMC_OPERATION_INSERT, &childNode[10]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(childNode[10], "case_1_1", GMC_OPERATION_INSERT, &childNode[20]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetNodePropertyBasic(childNode[20], keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置case_2_1
    ret = GmcYangEditChildNode(rootNode, "choice_2", GMC_OPERATION_INSERT, &childNode[10]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(childNode[10], "case_2_1", GMC_OPERATION_INSERT, &childNode[20]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetNodePropertyBasic(childNode[20], keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list_1
    for (uint32_t i = 1; i <= 3; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_list[1], "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt_root, stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_list[1], &childNode[11]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(childNode[11], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetNodePropertyBasic(childNode[11], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetNodePropertyAllType(childNode[11], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置list_2
    for (uint32_t i = 1; i <= 3; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_list[2], "list_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt_root, stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_list[2], &childNode[21]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(childNode[21], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetNodePropertyBasic(childNode[21], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 设置con_2_1
        ret = GmcYangEditChildNode(childNode[21], "con_2_1", GMC_OPERATION_INSERT, &childNode[22]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        TestYangSetNodePropertyBasic(childNode[22], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 设置con_2_2
        ret = GmcYangEditChildNode(childNode[21], "con_2_2", GMC_OPERATION_INSERT, &childNode[23]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        TestYangSetNodePropertyBasic(childNode[23], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置list_3
    for (uint32_t i = 1; i <= 3; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_list[3], "list_3", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt_root, stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_list[3], &childNode[31]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(childNode[31], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetNodePropertyBasic(childNode[31], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置list_3_1
        for (uint32_t j = 1; j <= 3; j++) {
            ret = testGmcPrepareStmtByLabelName(stmt_list[31], "list_3_1", GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, stmt_list[3], stmt_list[31]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(stmt_list[31], &childNode[32]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 设置属性值
            fieldValue = j;
            TestYangSetNodeProperty_PK(childNode[32], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
            TestYangSetNodePropertyBasic(childNode[32], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

            // 添加DML操作
            ret = GmcBatchAddDML(batch, stmt_list[31]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    // 设置list_4_1
    for (uint32_t i = 1; i <= 3; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_list[4], "list_4_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt_root, stmt_list[4]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_list[4], &childNode[41]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(childNode[41], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetNodePropertyBasic(childNode[41], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_list[4]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置list_5_1
    for (uint32_t i = 1; i <= 3; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_list[5], "list_5_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt_root, stmt_list[5]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_list[5], &childNode[51]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(childNode[51], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetNodePropertyBasic(childNode[51], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_list[5]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置leaflist_1
    for (uint32_t i = 1; i <= 3; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_list[6], "leaflist_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt_root, stmt_list[6]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_list[6], &childNode[61]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(childNode[61], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_list[6]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置leaflist_6_1
    for (uint32_t i = 1; i <= 3; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_list[7], "leaflist_6_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt_root, stmt_list[7]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_list[7], &childNode[71]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(childNode[71], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_list[7]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置leaflist_7_1
    for (uint32_t i = 1; i <= 3; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_list[8], "leaflist_7_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt_root, stmt_list[8]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_list[8], &childNode[81]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(childNode[81], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_list[8]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(34, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(34, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(stmt_root);
    for (uint32_t i = 0; i < 100; i++) {
        GmcFreeStmt(stmt_list[i]);
    }
}

// remove root
void TestRemoveRoot(GmcConnT *conn, const char * vertexName)
{
    int ret;
    GmcBatchT *batch = NULL;
    GmcStmtT *stmt_root = NULL;

    ret = GmcAllocStmt(conn, &stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt_root, vertexName, GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(stmt_root);
}

void InitListProperty(GmcYangListLocatorT *listProperty, GmcYangListPositionE position, GmcPropValueT *referenceKey)
{
    if (listProperty == NULL) {
        return;
    }
    listProperty->position = position;
    if (referenceKey != NULL) {
        listProperty->refKeyFields = (GmcPropValueT **)malloc(sizeof(GmcPropValueT *));
        listProperty->refKeyFields[0] = referenceKey;
        listProperty->refKeyFieldsCount = 1;
    } else {
        listProperty->refKeyFields = NULL;
        listProperty->refKeyFieldsCount = 0;
    }
}

void UninitListProperty(GmcYangListLocatorT *listProperty)
{
    if (listProperty->refKeyFields == NULL) {
        return;
    }
    free(listProperty->refKeyFields);
    listProperty->refKeyFields = NULL;
}

void InitRefKeys(GmcPropValueT *refKey, uint32_t propId, void *value, GmcDataTypeE type = GMC_DATATYPE_UINT32,
    uint32_t sizeValue = 4)
{
    refKey->propertyId = propId;
    refKey->propertyName[0] = '\0';
    refKey->type = type;
    refKey->size = sizeValue;
    refKey->value = value;
}

/******************************Subtree*******************************************/
// userData结构
struct subtreeFilterCbParam {
    int step;
    int32_t expectStatus;          // 预期的操作状态
    AsyncUserDataT *data;
    const char *expectReplyJson;  // 预期返回的subtree查询结果, json字符串
};
// userData ：用户数据 replyJson ：服务端返回的子树 json status ：服务器端操作处理结果  errMsg ：错误信息
void AsyncSubtreeFilterCb(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    int ret;
    subtreeFilterCbParam *param = (subtreeFilterCbParam *)(userData);
    param->expectStatus = status;
    if (GMERR_OK != status) {
        AW_FUN_Log(LOG_ERROR, "[err] status is %d  errMsg  is %s   \n ", status, errMsg);
        return;
    }
    bool isEnd = false;
    bool isEqual = false;
    uint32_t count = 0;
    const char **jsonReply = NULL;
    ret = GmcYangFetchJsonRetDeparse(fetchRet, &isEnd, &jsonReply, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(true, isEnd);
    AW_MACRO_EXPECT_EQ_INT(1, count);
    AW_MACRO_EXPECT_NOTNULL(jsonReply);
    printf("replyJson:\n%s\n", jsonReply[0]);

    if (param->expectReplyJson != NULL) {
        isEqual = testYangJsonIsEqual((const char*)jsonReply[0], param->expectReplyJson);
        if (isEqual == true) {
            AW_MACRO_EXPECT_EQ_INT(true, isEqual);
        } else {
            printf("expectJson:\n%s\n", param->expectReplyJson);
            AW_MACRO_EXPECT_EQ_INT(true, isEqual);
        }
    } else {
        AW_FUN_Log(LOG_ERROR, "[err] no replyjson   \n ");
    }
    param->step++;
    ((AsyncUserDataT *)(param->data))->recvNum = ((AsyncUserDataT *)(param->data))->recvNum + 1;
}

void TestSubtreeFilter(GmcStmtT *stmt, const char * rootName, const char * jsonName,
    GmcSubtreeWithDefaultModeE defaultMode = GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED)
{
    int ret;

    // subtree 查询
    char replyPath[1024] = {0};
    ret = snprintf(replyPath, 1024, "SubtreeReplyJson/%s_Reply.json", jsonName);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    char *replyJson = NULL;
    readJanssonFile(replyPath, &replyJson);
    ASSERT_NE((void *)NULL, replyJson);

    subtreeFilterCbParam data = {0};
    data.expectReplyJson = replyJson;
    data.expectStatus = GMERR_OK;
    data.step = 0;

    char filterPath[1024] = {0};
    ret = snprintf(filterPath, 1024, "SubTreeFilterJson/Yang_071_Func.json");
    AW_MACRO_EXPECT_NE_INT(0, ret);
    char *filterJson = NULL;
    readJanssonFile(filterPath, &filterJson);
    ASSERT_NE((void *)NULL, filterJson);

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = rootName;
    filter.subtree.json = filterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = defaultMode;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    AsyncUserDataT asyncData = { 0 };
    data.data = &asyncData;
    ret = GmcYangSubtreeFilterExecuteAsync(stmt, &filters, NULL, AsyncSubtreeFilterCb, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(data.data, 1, 100000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.expectStatus);

    free(filterJson);
    free(replyJson);
    filterJson = NULL;
    replyJson = NULL;
}

/******************************Subtree obj模式*******************************************/
void TestSubtreeFilterObjAll(GmcStmtT *stmt, const char * rootName, const char * jsonName,
    GmcSubtreeWithDefaultModeE defaultMode = GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED)
{
    int ret;

    GmcNodeT *rootNode = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, rootName, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = NULL;
    filter.subtree.obj = rootNode;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = defaultMode;

    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_OBJ;
    filters.filter = &filter;

    char replyPath[1024] = {0};
    ret = snprintf(replyPath, 1024, "SubtreeReplyJson/%s_Reply.json", jsonName);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    char *replyJson = NULL;
    readJanssonFile(replyPath, &replyJson);
    ASSERT_NE((void *)NULL, replyJson);
    std::vector<std::string> reply(1);
    reply[0] = replyJson;

    FetchRetCbParam param = {
        .step = 0,
        .stmt = stmt,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };

    char *noDataJson = NULL;
    readJanssonFile("SubtreeReplyJson/NoData.json", &noDataJson);
    ASSERT_NE((void *)NULL, noDataJson);
    bool equal = testYangJsonIsEqual(replyJson, noDataJson);
    if (!equal) {
        ret = GmcYangSubtreeFilterExecuteAsync(stmt, &filters, NULL, AsyncFetchRetCb, &param);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcYangSubtreeFilterExecuteAsync(stmt, &filters, NULL, AsyncFetchRetCbNoData, &param);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testWaitAsyncSubtreeRecv_API(&param, 1, 100000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, param.expectStatus);
    free(replyJson);
    free(noDataJson);
    replyJson = NULL;
    noDataJson = NULL;
}

void TestSubtreeFilterObj(GmcStmtT *stmt, GmcNodeT *rootNode, const char * jsonName, const char * rootName,
    GmcSubtreeWithDefaultModeE defaultMode, uint32_t depth = 0,
    GmcSubtreeWithConfigModeE configMode = GMC_SUBTREE_FILTER_DEFAULT)
{
    int ret;

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = NULL;
    filter.subtree.obj = rootNode;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = defaultMode;
    filter.maxDepth = depth;
    filter.configFlag = configMode;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_OBJ;
    filters.filter = &filter;

    char replyPath[1024] = {0};
    ret = snprintf(replyPath, 1024, "SubtreeReplyJson/%s_Reply.json", jsonName);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    char *replyJson = NULL;
    readJanssonFile(replyPath, &replyJson);
    ASSERT_NE((void *)NULL, replyJson);
    std::vector<std::string> reply(1);
    reply[0] = replyJson;

    FetchRetCbParam param = {
        .step = 0,
        .stmt = stmt,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };

    char *noDataJson = NULL;
    readJanssonFile("SubtreeReplyJson/NoData.json", &noDataJson);
    ASSERT_NE((void *)NULL, noDataJson);
    bool equal = testYangJsonIsEqual(replyJson, noDataJson);
    if (!equal) {
        ret = GmcYangSubtreeFilterExecuteAsync(stmt, &filters, NULL, AsyncFetchRetCb, &param);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcYangSubtreeFilterExecuteAsync(stmt, &filters, NULL, AsyncFetchRetCbNoData, &param);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testWaitAsyncSubtreeRecv_API(&param, 1, 100000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, param.expectStatus);
    free(replyJson);
    free(noDataJson);
    replyJson = NULL;
    noDataJson = NULL;
}

/******************************Diff*******************************************/
string GetValueString(GmcYangNodeValueT *value)
{
    switch (value->type) {
        case GMC_DATATYPE_STRING:
        case GMC_DATATYPE_ENUM:
        case GMC_DATATYPE_IDENTITY:
            return string("") + (const char *)value->value;
        case GMC_DATATYPE_CHAR:
        case GMC_DATATYPE_UCHAR:
            return "" + to_string(*(const char *)value->value);
            break;
        case GMC_DATATYPE_INT8:
        case GMC_DATATYPE_UINT8:
            return "" + to_string(*(const uint8_t *)value->value);
        case GMC_DATATYPE_INT16:
        case GMC_DATATYPE_UINT16:
            return "" + to_string(*(const uint16_t *)value->value);
        case GMC_DATATYPE_INT32:
        case GMC_DATATYPE_UINT32:
            return "" + to_string(*(const uint32_t *)value->value);
        case GMC_DATATYPE_UINT64:
        case GMC_DATATYPE_INT64:
        case GMC_DATATYPE_TIME:
            return "" + to_string(*(const uint64_t *)value->value);
        case GMC_DATATYPE_FLOAT:
            return "" + to_string(*(const float *)value->value);
        case GMC_DATATYPE_DOUBLE:
            return "" + to_string(*(const double *)value->value);
        case GMC_DATATYPE_NULL:
            return string("NULL");
        default:
            return string("NIL:") + to_string(value->type);
    }
}

bool IsWithDefaultTag(bool withDefaultTag, const GmcYangNodeValueT *value)
{
    return withDefaultTag && value->isDefault;
}

string GetVertexString(GmcStmtT *stmt, GmcYangNodeT *info, bool isNewData, bool withDefaultTag)
{
    GmcYangNodeValueT *propValue = NULL;
    string res = "";
    string tag;
    int32_t ret;
    GmcDiffOpTypeE opType;
    EXPECT_EQ(GMERR_OK, GmcYangNodeGetDiffOpType(info, &opType));
    uint32_t propNum = 0;
    if ((isNewData && opType == GMC_DIFF_OP_CREATE) || (!isNewData && opType == GMC_DIFF_OP_REMOVE) ||
        opType == GMC_DIFF_OP_UPDATE) {
        ret = GmcYangNodeGetKeyPropNum(info, &propNum);
        if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
            return "";
        }
        EXPECT_EQ(GMERR_OK, ret);
        res += "priKey(";
        for (unsigned int i = 0; i < propNum; i++) {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetKeyPropValue(info, i, &propValue));
            string propNameString = propValue->name;
            GmcYangNodeValueT tmpPropValue = *propValue;
            GmcPropValueT outPropValue = {0};
            char enumStr[DM_MAX_NAME_LENGTH] = {0};
            outPropValue.value = enumStr;
            outPropValue.size = DM_MAX_NAME_LENGTH;
            if (propValue->type == GMC_DATATYPE_ENUM || propValue->type == GMC_DATATYPE_IDENTITY) {
                EXPECT_EQ(GMERR_OK, GmcYangConvertResultProperty(info, propValue, &outPropValue));
                tmpPropValue.value = outPropValue.value;
            }
            tag = (IsWithDefaultTag(withDefaultTag, &tmpPropValue) ? "@" : "");
            res += propNameString + ":" + tag + GetValueString(&tmpPropValue) + ",";
        }
        if (res[res.size() - 1] == ',') {
            res[res.size() - 1] = ')';
        } else {
            res += ")";
        }
    }
    bool isHasNewPrevNode = false;
    bool isHasOldPrevNode = false;
    ret = GmcYangNodeHasNewPrev(info, &isHasNewPrevNode);
    if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
        return "";
    }
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangNodeHasOldPrev(info, &isHasOldPrevNode);
    if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
        return "";
    }
    EXPECT_EQ(GMERR_OK, ret);
    if ((isNewData && isHasNewPrevNode) || (!isNewData && isHasOldPrevNode) || opType == GMC_DIFF_OP_UPDATE) {
        bool isExist = (isNewData ? isHasNewPrevNode : isHasOldPrevNode);
        if (!isExist) {
            if (res == "") {
                res = "NULL";
            }
            return res;
        }
        res += ", preKey(";
        ret = GmcYangNodeGetKeyPropNum(info, &propNum);
        if (ret != GMERR_OK) {
            if (res == "") {
                res = "NULL";
            }
            return res;
        }
        for (unsigned int i = 0; i < propNum; i++) {
            ret = isNewData ? GmcYangNodeGetNewPrevKey(info, i, &propValue) :
                              GmcYangNodeGetOldPrevKey(info, i, &propValue);
            if (ret == GMERR_INVALID_NAME) {
                continue;
            } else if (ret != GMERR_OK) {
                cout << "error:" << ret << ", " << propValue->name;
            }
            string propNameString = propValue->name;
            GmcYangNodeValueT tmpPropValue = *propValue;
            GmcPropValueT outPropValue = {0};
            char enumStr[DM_MAX_NAME_LENGTH] = {0};
            outPropValue.value = enumStr;
            outPropValue.size = DM_MAX_NAME_LENGTH;
            if (propValue->type == GMC_DATATYPE_ENUM || propValue->type == GMC_DATATYPE_IDENTITY) {
                EXPECT_EQ(GMERR_OK, GmcYangConvertResultProperty(info, propValue, &outPropValue));
                tmpPropValue.value = outPropValue.value;
            }
            tag = (IsWithDefaultTag(withDefaultTag, &tmpPropValue) ? "@" : "");
            res += propNameString + ":" + tag + GetValueString(&tmpPropValue) + ",";
        }
        if (res[res.size() - 1] == ',') {
            res[res.size() - 1] = ')';
        } else {
            res += ")";
        }
    }
    if (res == "") {
        res = "NULL";
    }
    return res;
}

string GetOpTypeString(GmcDiffOpTypeE op)
{
    switch (op) {
        case GMC_DIFF_OP_CREATE:
            return "create";
        case GMC_DIFF_OP_REMOVE:
            return "remove";
        case GMC_DIFF_OP_UPDATE:
            return "update";
        default:
            return "invalid";
    }
}

void GetYangInfoString(GmcStmtT *stmt, GmcYangNodeT *info, bool withDefaultTag, string parentFix, string &res)
{
    res = parentFix + ":";
    GmcDiffOpTypeE opType;
    EXPECT_EQ(GMERR_OK, GmcYangNodeGetDiffOpType(info, &opType));
    res += GetOpTypeString((GmcDiffOpTypeE)opType);

    GmcYangNodeTypeE nodeType;
    string newTag;
    string oldTag;
    EXPECT_EQ(GMERR_OK, GmcYangNodeGetType(info, &nodeType));
    if (nodeType == GMC_YANG_FIELD) {
        GmcYangNodeValueT *newValue = NULL;
        GmcYangNodeValueT *oldValue = NULL;
        char enumOldStr[DM_MAX_NAME_LENGTH] = {0};
        char enumNewStr[DM_MAX_NAME_LENGTH] = {0};
        GmcPropValueT outOldPropValue = {0};
        GmcPropValueT outNewPropValue = {0};
        outOldPropValue.value = &enumOldStr;
        outOldPropValue.size = DM_MAX_NAME_LENGTH;
        outNewPropValue.value = &enumNewStr;
        outNewPropValue.size = DM_MAX_NAME_LENGTH;
        if (opType == GMC_DIFF_OP_CREATE) {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetNewValue(info, &newValue));
            if (newValue->type == GMC_DATATYPE_ENUM || newValue->type == GMC_DATATYPE_IDENTITY) {
                const char *nodeName;
                EXPECT_EQ(GMERR_OK, GmcYangNodeGetName(info, &nodeName));
                newValue->name = nodeName;
                EXPECT_EQ(GMERR_OK, GmcYangConvertResultProperty(info, newValue, &outNewPropValue));
                newValue->value = outNewPropValue.value;
                newValue->size = outNewPropValue.size;
            }
            newTag = IsWithDefaultTag(withDefaultTag, newValue) ? "@" : "";
            res += "(" + newTag + GetValueString(newValue) + ")";
        } else if (opType == GMC_DIFF_OP_REMOVE) {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetOldValue(info, &oldValue));
            if (oldValue->type == GMC_DATATYPE_ENUM || oldValue->type == GMC_DATATYPE_IDENTITY) {
                const char *nodeName;
                EXPECT_EQ(GMERR_OK, GmcYangNodeGetName(info, &nodeName));
                oldValue->name = nodeName;
                EXPECT_EQ(GMERR_OK, GmcYangConvertResultProperty(info, oldValue, &outOldPropValue));
                oldValue->value = outOldPropValue.value;
                oldValue->size = outOldPropValue.size;
            }
            oldTag = IsWithDefaultTag(withDefaultTag, oldValue) ? "@" : "";
            res += "(" + oldTag + GetValueString(oldValue) + ")";
        } else {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetNewValue(info, &newValue));
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetOldValue(info, &oldValue));
            if (newValue->type == GMC_DATATYPE_ENUM || newValue->type == GMC_DATATYPE_IDENTITY) {
                const char *nodeName;
                EXPECT_EQ(GMERR_OK, GmcYangNodeGetName(info, &nodeName));
                newValue->name = nodeName;
                EXPECT_EQ(GMERR_OK, GmcYangConvertResultProperty(info, newValue, &outNewPropValue));
                newValue->value = outNewPropValue.value;
                newValue->size = outNewPropValue.size;
            }
            if (oldValue->type == GMC_DATATYPE_ENUM || oldValue->type == GMC_DATATYPE_IDENTITY) {
                const char *nodeName;
                EXPECT_EQ(GMERR_OK, GmcYangNodeGetName(info, &nodeName));
                oldValue->name = nodeName;
                EXPECT_EQ(GMERR_OK, GmcYangConvertResultProperty(info, oldValue, &outOldPropValue));
                oldValue->value = outOldPropValue.value;
                oldValue->size = outOldPropValue.size;
            }
            newTag = IsWithDefaultTag(withDefaultTag, newValue) ? "@" : "";
            oldTag = IsWithDefaultTag(withDefaultTag, oldValue) ? "@" : "";
            res += "(" + newTag + GetValueString(newValue) + "," + oldTag + GetValueString(oldValue) + ")";
        }
    } else {
        if (strcmp(GetVertexString(stmt, info, true, withDefaultTag).c_str(), "") != 0) {
            res += "[(" + GetVertexString(stmt, info, true, withDefaultTag) + "),";
        }
        if (strcmp(GetVertexString(stmt, info, false, withDefaultTag).c_str(), "") != 0) {
            res += "(" + GetVertexString(stmt, info, false, withDefaultTag) + ")]";
        }
    }
}

// 深度遍历生成diff信息字符串
void DFSYangNode(GmcStmtT *stmt, GmcYangNodeT *parent, bool withDefaultTag, string prefix, string &resStr)
{
    int ret;
    GmcYangNodeT *child = NULL;
    GmcYangNodeT *prevChild = NULL;
    string res = "";
    string diffStr;
    do {
        // 字段不一致解析失败报错
        ret = GmcYangNodeGetNext(parent, prevChild, &child);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "GmcYangNodeGetNext unsucc.");
            AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
            break;
        }
        prevChild = child;
        if (child != NULL) {
            // 打印diff信息
            const char *nodeName;
            ASSERT_EQ(GMERR_OK, GmcYangNodeGetName(child, &nodeName));
            string childName = prefix + nodeName;
            ASSERT_NO_FATAL_FAILURE(GetYangInfoString(stmt, child, withDefaultTag, childName, diffStr));
            res += diffStr + "\n";
            string childStr;
            DFSYangNode(stmt, child, withDefaultTag, nodeName + string("."), childStr);
            res += childStr;
        }
    } while (child != NULL);
    resStr += res;
}

// 比较s1 s2两个字符串，如果字符串相同，返回空串，不同返回从不同位置开始的子串
string StrCmp(string &s1, string &s2)
{
    int i = 0;
    for (; s1[i] != 0 && s2[i] != 0; i++) {
        if (s1[i] != s2[i]) {
            return s1.substr(i);
        }
    }

    if (s1[i] == 0 && s2[i] == 0) {
        return string("");
    } else if (s2[i] == 0) {
        return s1.substr(i);
    } else {
        return s2.substr(i);
    }
}

// 默认withDefaultTag = true，本特性专用
void TestCheckYangTree(GmcStmtT *stmt, const GmcYangTreeT **yangTrees, uint32_t count, vector<string> &expectReply,
    bool withDefaultTag = true)
{
    ASSERT_EQ(expectReply.size(), count);
    GmcYangNodeT *rootInfo = NULL;
    for (uint32_t i = 0; i < count; i++) {
        ASSERT_EQ(GMERR_OK, GmcYangGetRootNode(yangTrees[i], &rootInfo));
        string res;
        const char *rootName;
        ASSERT_EQ(GMERR_OK, GmcYangNodeGetName(rootInfo, &rootName));
        ASSERT_NO_FATAL_FAILURE(GetYangInfoString(stmt, rootInfo, withDefaultTag, rootName + string(""), res));
        res += "\n";
        ASSERT_NO_FATAL_FAILURE(DFSYangNode(stmt, rootInfo, withDefaultTag, rootName + string("."), res));
        char fileName[128] = {0};
        int ret = sprintf_s(fileName, sizeof(fileName), "perf_%dDiffTreeInfo", count);
        if (ret == -1) {
            AW_FUN_Log(LOG_INFO, "sprintf_s error\n");
            return;
        }
        FILE *fp = fopen(fileName, "w");
        if (fp == NULL) {
            AW_FUN_Log(LOG_INFO, "fopen error\n");
            return;
        }
        ret = fputs(res.c_str(), fp);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "fputs error\n");
            return;
        }
        ret = fclose(fp);
        if (ret == -1) {
            AW_FUN_Log(LOG_INFO, "fclose error\n");
            return;
        }

        if (g_printFlag) {
            cout << "actual diff：\n" << res;
        }
        ASSERT_STREQ(StrCmp(expectReply[i], res).c_str(), "") << i;
        ASSERT_EQ(GMERR_OK, GmcYangFreeTree(yangTrees[i]));
    }
}

void TestCheckYangTreeFail(GmcStmtT *stmt, const GmcYangTreeT **yangTrees, uint32_t count, vector<string> &expectReply,
    bool withDefaultTag = true)
{
    ASSERT_EQ(expectReply.size(), count);
    GmcYangNodeT *rootInfo = NULL;
    ASSERT_EQ(GMERR_DATA_EXCEPTION, GmcYangGetRootNode(yangTrees[0], &rootInfo));
}

void FetchDiff_callback(
    void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        uint32_t count = 0;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            const GmcYangTreeT **yangTree = NULL;
            ASSERT_EQ(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
            ASSERT_EQ((uint32_t)(*userData1->expectDiff).size(), count);
            ASSERT_TRUE(isEnd);
            TestCheckYangTree(userData1->stmt, yangTree, count, *userData1->expectDiff);
            GmcYangFreeFetchRet(fetchRet);
        }
        userData1->recvNum++;
    }
}


void TestFetchAndDeparseDiff(GmcStmtT *stmt, GmcBatchT *batch, vector<string> &expectDiff, AsyncUserDataT data,
    int rets = GMERR_OK)
{
    data.stmt = stmt;
    data.expectDiff = &expectDiff;
    int ret = GmcYangFetchDiffExecuteAsync(stmt, NULL, FetchDiff_callback, &data);
    if (ret != GMERR_OK) {
        testGmcGetLastError(NULL);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(rets, data.status);
}

void FetchDiffBuf_callback(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            const GmcYangTreeT **yangTree = NULL;
            ASSERT_EQ(GMERR_OK, GmcYangFetchDiffBufRetDeparse(fetchRet, &isEnd, &userData1->diffBufLen));
            ASSERT_TRUE(isEnd);
            if (userData1->diffBufLen != 0) {
                userData1->diffBuf = (uint8_t *)malloc(userData1->diffBufLen);
                ASSERT_TRUE(userData1->diffBuf != NULL);
                ASSERT_EQ(GMERR_OK, GmcYangFetchDiffBuf(fetchRet, userData1->diffBufLen, userData1->diffBuf));
            } else {
                AW_FUN_Log(LOG_STEP, "userData1->diffBufLen = %d", userData1->diffBufLen);
            }

            GmcYangFreeFetchRet(fetchRet);
        }
        userData1->recvNum++;
    }
}

void TestAttachDiffBuf(GmcStmtT *stmt, vector<string> &expectDiff, AsyncUserDataT *data,
    int rets = GMERR_OK)
{
    if (data->diffBufLen == 0) {
        ASSERT_TRUE(expectDiff.size() == 0);
        return;
    }
    GmcFetchRetT *fetchRet = NULL;
    EXPECT_EQ(GMERR_OK, GmcYangDiffFetchRetFromBuf(stmt, data->diffBufLen, data->diffBuf, &fetchRet));
    bool isEnd = false;
    uint32_t count = 0;
    const GmcYangTreeT **yangTree = NULL;
    EXPECT_EQ(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
    ASSERT_TRUE(isEnd);
    TestCheckYangTree(stmt, yangTree, count, expectDiff);
    GmcYangFreeFetchRet(fetchRet);
    return;
}

void TestFetchDiffBuf(GmcStmtT *stmt, AsyncUserDataT *data, int rets = GMERR_OK)
{
    int ret = GmcYangFetchDiffBufExecuteAsync(stmt, NULL, FetchDiffBuf_callback, data);
    if (ret != GMERR_OK) {
        testGmcGetLastError(NULL);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(rets, data->status);
}

/********************************接口测试***************************************/
void TestFetchDiffBufRetDeparseIF(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            const GmcYangTreeT **yangTree = NULL;

            // 接口测试
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED,
                GmcYangFetchDiffBufRetDeparse(NULL, &isEnd, &userData1->diffBufLen));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED,
                GmcYangFetchDiffBufRetDeparse(fetchRet, NULL, &userData1->diffBufLen));
            EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, GmcYangFetchDiffBufRetDeparse(fetchRet, &isEnd, NULL));

            GmcYangFreeFetchRet(fetchRet);
        }
        userData1->recvNum++;
    }
}

void TestFetchDiffBufIF(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            const GmcYangTreeT **yangTree = NULL;
            ASSERT_EQ(GMERR_OK, GmcYangFetchDiffBufRetDeparse(fetchRet, &isEnd, &userData1->diffBufLen));
            ASSERT_TRUE(isEnd);
            if (userData1->diffBufLen != 0) {
                userData1->diffBuf = (uint8_t *)malloc(userData1->diffBufLen);
                ASSERT_TRUE(userData1->diffBuf != NULL);

                // 接口测试
                EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED,
                    GmcYangFetchDiffBuf(NULL, userData1->diffBufLen, userData1->diffBuf));
                EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, GmcYangFetchDiffBuf(fetchRet, userData1->diffBufLen, NULL));
                free(userData1->diffBuf);
            } else {
                AW_FUN_Log(LOG_STEP, "userData1->diffBufLen = %d", userData1->diffBufLen);
            }

            GmcYangFreeFetchRet(fetchRet);
        }
        userData1->recvNum++;
    }
}

void TestFetchDiffBufIFLen(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            const GmcYangTreeT **yangTree = NULL;
            ASSERT_EQ(GMERR_OK, GmcYangFetchDiffBufRetDeparse(fetchRet, &isEnd, &userData1->diffBufLen));
            ASSERT_TRUE(isEnd);
            if (userData1->diffBufLen != 0) {
                userData1->diffBuf = (uint8_t *)malloc(userData1->diffBufLen);
                ASSERT_TRUE(userData1->diffBuf != NULL);
                // 接口测试
                userData1->diffBufLen = 10;
                EXPECT_EQ(GMERR_DATATYPE_MISMATCH,
                    GmcYangFetchDiffBuf(fetchRet, userData1->diffBufLen, userData1->diffBuf));
                free(userData1->diffBuf);
            } else {
                AW_FUN_Log(LOG_STEP, "userData1->diffBufLen = %d", userData1->diffBufLen);
            }

            GmcYangFreeFetchRet(fetchRet);
        }
        userData1->recvNum++;
    }
}

void TestAfterBasicDiffIF(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            const GmcYangTreeT **yangTree = NULL;

            // 接口测试
            EXPECT_EQ(GMERR_DATATYPE_MISMATCH, GmcYangFetchDiffBufRetDeparse(fetchRet, &isEnd, &userData1->diffBufLen));
            userData1->diffBufLen = 10;
            userData1->diffBuf = (uint8_t *)malloc(userData1->diffBufLen);
            ASSERT_TRUE(userData1->diffBuf != NULL);
            EXPECT_EQ(GMERR_DATATYPE_MISMATCH,
                GmcYangFetchDiffBuf(fetchRet, userData1->diffBufLen, userData1->diffBuf));
            free(userData1->diffBuf);

            GmcYangFreeFetchRet(fetchRet);
        }
        userData1->recvNum++;
    }
}

/********************************批量返回diff***************************************/
// 默认withDefaultTag = true，本特性专用
void TestCheckYangTreeAttachDef(GmcStmtT *stmt, const GmcYangTreeT **yangTrees, uint32_t index,
    vector<string> &expectReply, bool withDefaultTag = true)
{
    ASSERT_TRUE(expectReply.size() >= (index + 1));
    GmcYangNodeT *rootInfo = NULL;
    uint32_t i = 0;
    ASSERT_EQ(GMERR_OK, GmcYangGetRootNode(yangTrees[i], &rootInfo));
    string res;
    const char *rootName;
    ASSERT_EQ(GMERR_OK, GmcYangNodeGetName(rootInfo, &rootName));
    ASSERT_NO_FATAL_FAILURE(GetYangInfoString(stmt, rootInfo, withDefaultTag, rootName + string(""), res));
    res += "\n";
    ASSERT_NO_FATAL_FAILURE(DFSYangNode(stmt, rootInfo, withDefaultTag, rootName + string("."), res));
    char fileName[128] = {0};
    int ret = sprintf_s(fileName, sizeof(fileName), "perf_%dDiffTreeInfo", index);
    if (ret == -1) {
        AW_FUN_Log(LOG_INFO, "sprintf_s error\n");
        return;
    }
    FILE *fp = fopen(fileName, "w");
    if (fp == NULL) {
        AW_FUN_Log(LOG_INFO, "fopen error\n");
        return;
    }
    ret = fputs(res.c_str(), fp);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "fputs error\n");
        return;
    }
    ret = fclose(fp);
    if (ret == -1) {
        AW_FUN_Log(LOG_INFO, "fclose error\n");
        return;
    }

    if (g_printFlag) {
        cout << "actual diff：\n" << res;
    }
    ASSERT_STREQ(StrCmp(expectReply[index], res).c_str(), "") << i;
    ASSERT_EQ(GMERR_OK, GmcYangFreeTree(yangTrees[i]));
}

void FetchDiffBatch_callback(
    void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        uint32_t count = 0;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            uint32_t idx = userData1->lastExpectIdx;
            const GmcYangTreeT **yangTree = NULL;
            EXPECT_EQ(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
            TestCheckYangTreeAttachDef(userData1->stmt, yangTree, idx, *userData1->expectDiff);
            if (isEnd) {
                userData1->recvNum++;
                GmcYangFreeFetchRet(fetchRet);
                return;
            }
            // 没有获取完diff 重新触发回调函数
            userData1->lastExpectIdx = idx + count;
            EXPECT_EQ(GMERR_OK,
                GmcYangFetchDiffExecuteAsync(userData1->stmt, fetchRet, FetchDiffBatch_callback, userData1));
            return;
        }
    }
}

void TestAttachDiffBufBatch(GmcStmtT *stmt, vector<string> &expectDiff, uint32_t index, AsyncUserDataT *data,
    int rets = GMERR_OK)
{
    if (data->diffBufLen == 0) {
        ASSERT_TRUE(expectDiff.size() == 0);
        return;
    }
    GmcFetchRetT *fetchRet = NULL;
    EXPECT_EQ(GMERR_OK, GmcYangDiffFetchRetFromBuf(stmt, data->diffBufLen, data->diffBuf, &fetchRet));
    bool isEnd = false;
    uint32_t count = 0;
    const GmcYangTreeT **yangTree = NULL;
    EXPECT_EQ(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
    ASSERT_TRUE(isEnd);
    TestCheckYangTreeAttachDef(stmt, yangTree, index, expectDiff);
    GmcYangFreeFetchRet(fetchRet);
    return;
}

void FetchDiffBufBatch_callback(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    int ret = 0;
    if (userData) {
        bool isEnd = false;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            const GmcYangTreeT **yangTree = NULL;
            ASSERT_EQ(GMERR_OK, GmcYangFetchDiffBufRetDeparse(fetchRet, &isEnd, &userData1->diffBufLen));

            if (userData1->diffBufLen != 0) {
                userData1->diffBuf = (uint8_t *)malloc(userData1->diffBufLen);
                ASSERT_TRUE(userData1->diffBuf != NULL);
                ASSERT_EQ(GMERR_OK, GmcYangFetchDiffBuf(fetchRet, userData1->diffBufLen, userData1->diffBuf));
            } else {
                AW_FUN_Log(LOG_STEP, "userData1->diffBufLen = %d", userData1->diffBufLen);
            }

            GmcYangFreeFetchRet(fetchRet);

            GmcConnT *conn2 = NULL;
            GmcStmtT *stmt2 = NULL;
            ret = TestYangGmcConnect(&conn2, &stmt2, GMC_CONN_TYPE_SYNC);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcUseNamespace(stmt2, g_namespace2);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 另一个ns解析diff buffer
            uint32_t idx = userData1->lastExpectIdx;
            TestAttachDiffBufBatch(stmt2, userData1->expectDiff[0], idx, userData1);

            // 释放Buf
            if (userData1->diffBufLen!= 0) {
                free(userData1->diffBuf);
            }

            ret = testGmcDisconnect(conn2, stmt2);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            if (isEnd) {
                userData1->recvNum++;
                return;
            }

            // 没有获取完diff 重新触发回调函数
            userData1->lastExpectIdx++;
            EXPECT_EQ(GMERR_OK, GmcYangFetchDiffBufExecuteAsync(userData1->stmt, NULL,
                FetchDiffBufBatch_callback, userData1));
            return;
        }
        // userData1->recvNum++;
    }
}

void TestFetchDiffBufBatch(GmcStmtT *stmt, AsyncUserDataT *data, int rets = GMERR_OK)
{
    int ret = GmcYangFetchDiffBufExecuteAsync(stmt, NULL, FetchDiffBufBatch_callback, data);
    if (ret != GMERR_OK) {
        testGmcGetLastError(NULL);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(rets, data->status);
}


#endif /* YANG_DIFF_ATTACH_H */
