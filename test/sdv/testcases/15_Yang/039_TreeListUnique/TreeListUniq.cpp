/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: 039_TreeListUnique
 * Author: wangxuming
 * Create: 2023-2-2
 */
#include "TreeListUniq.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
GmcStmtT *g_stmt_sync[10] = {0};
GmcConnT *g_conn_async = NULL;
GmcStmtT *g_stmt_async = NULL;
GmcConnT *g_conn_async1 = NULL;
GmcStmtT *g_stmt_async1 = NULL;
GmcStmtT *g_stmt_root = NULL;
GmcStmtT *g_stmt_list[10] = {0};
GmcNodeT *g_rootNode = NULL;
GmcNodeT *g_childNode[40] = {0};

class TreeListUniq : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void TreeListUniq::SetUpTestCase()
{
    int ret;
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TreeListUniq::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

void TreeListUniq::SetUp()
{
    int ret;
    AsyncUserDataT data = { 0 };

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
     ret = testGmcConnect(&g_conn_async1, &g_stmt_async1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t i = 0;
    for (i = 0; i < 10; i++) {
        ret = GmcAllocStmt(g_conn_async, &g_stmt_list[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcAllocStmt(g_conn, &g_stmt_sync[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = g_namespace;
    nspCfg.tablespaceName = NULL;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = { GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE }; // 可重复读+乐观

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcUseNamespaceAsync(g_stmt_async1, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 创建Vertex表
    testCreateLabelAll(g_stmt_async);

    // 同步连接也使用namespace
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
}

void TreeListUniq::TearDown()
{
    int ret;
    uint32_t i = 0;
    AsyncUserDataT data = { 0 };
    AW_CHECK_LOG_END();

    // 删除Vertex和Edge表
    testDropLabelAll(g_stmt_async);

    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, g_namespace, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    for (i = 0; i < 10; i++) {
        GmcFreeStmt(g_stmt_sync[i]);
    }
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    GmcFreeStmt(g_stmt_root);
    for (i = 0; i < 10; i++) {
        GmcFreeStmt(g_stmt_list[i]);
    }
    testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcDisconnect(g_conn_async1, g_stmt_async1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_conn_async = NULL;
    g_stmt_async = NULL;
    g_conn_async1 = NULL;
    g_stmt_async1 = NULL;
    g_stmt_root = NULL;
    for (i = 0; i < 10; i++) {
        g_stmt_list[i] = NULL;
        g_stmt_sync[i] = NULL;
    }

    g_rootNode = NULL;
    for (i = 0; i < 10; i++) {
        g_childNode[i] = NULL;
    }
}

class TreeListUniqErr : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void TreeListUniqErr::SetUpTestCase()
{
    int ret;
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TreeListUniqErr::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

void TreeListUniqErr::SetUp()
{
    int ret = 0;
    ret = testGmcConnect(&g_T0_conn, &g_T0_stmt, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    TestBatchPrepare(g_T0_conn, &batch);
    AW_CHECK_LOG_BEGIN();
    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    AsyncUserDataT userData = {0};
    const char *namespace1 = "NamespaceA";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观

    ret = GmcCreateNamespaceWithCfgAsync(g_T0_stmt, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcUseNamespaceAsync(g_T0_stmt, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
}

void TreeListUniqErr::TearDown()
{
    AW_CHECK_LOG_END();
    int ret;
    const char *namespace1 = "NamespaceA";
    AsyncUserDataT userData = {0};
    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_T0_stmt, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = testGmcDisconnect(g_T0_conn, g_T0_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

class TreeListUniqDoubleIndex : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void TreeListUniqDoubleIndex::SetUpTestCase()
{
    int ret;
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TreeListUniqDoubleIndex::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

void TreeListUniqDoubleIndex::SetUp()
{
    int ret;
    AsyncUserDataT data = { 0 };

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t i = 0;
    for (i = 0; i < 10; i++) {
        ret = GmcAllocStmt(g_conn_async, &g_stmt_list[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcAllocStmt(g_conn, &g_stmt_sync[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = g_namespace;
    nspCfg.tablespaceName = NULL;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = { GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE }; // 可重复读+乐观

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 创建Vertex表
    testCreateLabelAllPls(g_stmt_async);

    // 同步连接也使用namespace
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
}

void TreeListUniqDoubleIndex::TearDown()
{
    int ret;
    uint32_t i = 0;
    AsyncUserDataT data = { 0 };
    AW_CHECK_LOG_END();

    // 删除Vertex和Edge表
    testDropLabelAll(g_stmt_async);

    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, g_namespace, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    for (i = 0; i < 10; i++) {
        GmcFreeStmt(g_stmt_sync[i]);
    }
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    GmcFreeStmt(g_stmt_root);
    for (i = 0; i < 10; i++) {
        GmcFreeStmt(g_stmt_list[i]);
    }
    testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_conn_async = NULL;
    g_stmt_async = NULL;
    g_stmt_root = NULL;
    for (i = 0; i < 10; i++) {
        g_stmt_list[i] = NULL;
        g_stmt_sync[i] = NULL;
    }

    g_rootNode = NULL;
    for (i = 0; i < 10; i++) {
        g_childNode[i] = NULL;
    }
}

/* ****************************************************************************
 Description  : 001.建跨层级节点表，验证数据写入成功，批量写，唯一性字段无null值，预期成功
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniq, Yang_039_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root", "Yang_001");

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 002.建跨层级节点表，验证数据写入成功，批量写 ，唯一性字段有null值，预期成功
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniq, Yang_039_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root", "Yang_002");

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 003.建跨层级节点表，增加一条记录，唯一性字段值不同，预期写入成功
                (除PID外，唯一性约束只有一个元素)
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniq, Yang_039_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 7;
    testYangSetNodeProperty_PK1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF1 = 30;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 40;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 8;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 9;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 10;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 11;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root", "Yang_003");

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 004.建跨层级节点表，增加一条记录，唯一性字段值相同，预期写入失败
                (除PID外，唯一性约束只有一个元素)
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniq, Yang_039_004)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNIQUE_VIOLATION, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = testTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 005.建跨层级节点表，增加一条记录，唯一性字段中的某一元素值不同，预
                期写入成功（唯一性约束含多个非null值）
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniq, Yang_039_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 9;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root", "Yang_005");

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 006.建跨层级节点表，增加一条记录，唯一性字段中的某一元素值相同，预
                期写入成功（唯一性约束含多个非null值）
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniq, Yang_039_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 7;
    testYangSetNodeProperty_PK1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 40;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 8;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 9;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 10;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 11;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root", "Yang_006");

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 007.建跨层级节点表，增加一条记录，唯一性字段中的所有元素值不同，预
                期成功（唯一性约束含多个非null值）
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniq, Yang_039_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 7;
    testYangSetNodeProperty_PK1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF1 = 30;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 40;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 8;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 9;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 10;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 11;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root", "Yang_003");

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 008. 建跨层级节点表，增加一条记录，主键值改变，唯一性字段中的所有元
                素写入相同值，预期写入失败（唯一性约束含多个非null值）
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniq, Yang_039_008)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNIQUE_VIOLATION, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = testTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 009.建跨层级节点表，增加一条记录，主键值冲突，唯一性字段中的所有元素
                值改变，预期写入失败
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniq, Yang_039_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    char errorMsg1[128] = {}, errorMsg2[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF1 = 30;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 40;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 8;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 9;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 10;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 11;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = testTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 010.建跨层级节点表，增加一条完全一样的记录，预期写入失败
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniq, Yang_039_010)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNIQUE_VIOLATION, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = testTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 011.建跨层级节点表，增加多条记录，修改后再次增加记录，引起唯一性冲
                突；预期最后写入失败
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniq, Yang_039_011)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNIQUE_VIOLATION, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = testTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 012.建跨层级节点表，增加一条记录，默认值不填，预期写入成功
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniq, Yang_039_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 7;
    testYangSetNodeProperty_PK1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0Dft(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF1 = 30;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 40;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 8;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 9;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 10;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 11;
    testYangSetNodePropertyWithoutF0DftF4(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root", "Yang_012");

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 013.建跨层级节点表，增加一条记录，默认值处填写值和默认值一样，预期
                写入失败
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniq, Yang_039_013)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "c0_F2";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[1], "c1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c1_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 7;
    testYangSetNodeProperty_PK1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0Dft(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF1 = 30;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 40;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 8;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 9;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 10;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 11;
    testYangSetNodePropertyWithoutF0DftF4(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[1], "c1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c1_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNIQUE_VIOLATION, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = testTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 014.建跨层级节点表，设置索引字段有默认值且该元素值为null，写入一条
                数据值为默认值，预期写入失败
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniq, Yang_039_014)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0Dft(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0DftF4(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[1], "c1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c1_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 7;
    testYangSetNodeProperty_PK1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0Dft(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF1 = 30;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 40;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 8;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 9;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 10;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 11;
    testYangSetNodePropertyWithoutF0DftF4(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[1], "c1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c1_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNIQUE_VIOLATION, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = testTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 015.建跨层级节点表，设置主键和唯一性字段所有元素都为自增类型，预期
                JSON格式无效
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniqErr, Yang_039_015)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AsyncUserDataT data = { 0 };
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.userData = &data;
    insertRequestCtx.insertCb = insert_vertex_callback;
    GmcBatchT *batch = NULL;

    char *vertexLabelJson = NULL;
    char *edgeLabelJson = NULL;
    TestBatchPrepare(g_T0_conn, &batch);

    // 读文件
    readJanssonFile("schema_file/unique_vertexLabelAutoIncrement.gmjson", &vertexLabelJson);
    ASSERT_NE((void *)NULL, vertexLabelJson);
    readJanssonFile("schema_file/unique_edgeLabel.gmjson", &edgeLabelJson);
    ASSERT_NE((void *)NULL, edgeLabelJson);

    // 建表
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vertexLabelJson, g_msConfigTrans100, create_vertex_label_callback,
        &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    free(vertexLabelJson);
    free(edgeLabelJson);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 016.建跨层级节点表，merge数据B某一元素值多次，第一次修改值成功；增
                加一条数据唯一性字段同B，第二次修改成其他值，最后改回原值，预期最后
                修改失败
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniq, Yang_039_016)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    const char *fieldValuex = "string";
    fieldValue = 1;
    PID = 1;
    testSetKeyNameAndValue(g_stmt_list[1], fieldValuex, PID, true);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    const char *fieldValuexx = "stringg";
    fieldValue = 1;
    PID = 1;
    testSetKeyNameAndValue(g_stmt_list[1], fieldValuexx, PID, true);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNIQUE_VIOLATION, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = testTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 017.建跨层级节点表，merge一条记录B，修改null值为非null值；写入一条
                数据和修改的相冲突，再次修改数据B回null后，再次写入刚才的数据，预期
                最后写入成功
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniq, Yang_039_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    const char *fieldValuex = "string";
    fieldValue = 1;
    PID = 1;
    testSetKeyNameAndValue(g_stmt_list[1], fieldValuex, PID, true);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    const char *fieldValuexx = "stringg";
    fieldValue = 1;
    PID = 1;
    testSetKeyNameAndValue(g_stmt_list[1], fieldValuexx, PID, true);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root", "Yang_017");

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 018.建跨层级节点表，merge一条记录B的某一元素值为null，写入一条数据
                成功，再次修改回原值，预期最后修改成功
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniq, Yang_039_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    const char *fieldValuex = "string";
    fieldValue = 1;
    PID = 1;
    testSetKeyNameAndValue(g_stmt_list[1], fieldValuex, PID, true);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    const char *fieldValuexx = "stringg";
    fieldValue = 1;
    PID = 1;
    testSetKeyNameAndValue(g_stmt_list[1], fieldValuexx, PID, true);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root", "Yang_018");

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 019.建跨层级节点表，merge一条记录唯一性字段中的所有元素值修改两次，
                第一次修改成功；写入一条数据，第二次修改回原值，预期成功
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniq, Yang_039_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    const char *fieldValuex = "string";
    fieldValue = 1;
    PID = 1;
    testSetKeyNameAndValue(g_stmt_list[1], fieldValuex, PID, true);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    const char *fieldValuexx = "stringg";
    fieldValue = 1;
    PID = 1;
    testSetKeyNameAndValue(g_stmt_list[1], fieldValuexx, PID, true);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root", "Yang_017");
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    PID = 1;
    testSetKeyNameAndValue(g_stmt_list[1], fieldValuex, PID, true);
    uint32_t newValue = 777;
    testYangSetNodePropertyWithoutF0(g_childNode[1], newValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 0;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 333;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 444;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 555;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 666;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    PID = 1;
    testSetKeyNameAndValue(g_stmt_list[1], fieldValuexx, PID, true);
    newValue = 888;
    testYangSetNodePropertyWithoutF0(g_childNode[1], newValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 60;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 33;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 44;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 55;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 66;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root", "Yang_019");

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 020.建跨层级节点表，merge一条记录，主键值不变、唯一性字段修改成和另
                一条数据相同，预期失败
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniq, Yang_039_020)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    const char *fieldValuex = "string";
    fieldValue = 1;
    PID = 1;
    testSetKeyNameAndValue(g_stmt_list[1], fieldValuex, PID, true);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    const char *fieldValuexx = "stringg";
    fieldValue = 1;
    PID = 1;
    testSetKeyNameAndValue(g_stmt_list[1], fieldValuexx, PID, true);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    PID = 1;
    testSetKeyNameAndValue(g_stmt_list[1], fieldValuex, PID, true);
    uint32_t newValue = 1;
    testYangSetNodePropertyWithoutF0(g_childNode[1], newValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 333;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 444;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 555;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNIQUE_VIOLATION, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = testTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 021.建跨层级节点表，replace数据B某一元素值多次，第一次修改值成功；
                增加一条数据唯一性字段同B，第二次修改成其他值，最后改回原值，预期最
                后修改成功
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniq, Yang_039_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 2;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF1 = 100;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 200;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 50;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root", "Yang_021");

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 022.建跨层级节点表，replace一条记录B，修改null值为非null值；再次修
                改数据B回null后，再次写入刚才的数据，预期最后写入成功
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniq, Yang_039_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 2;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 100;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 200;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 50;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root", "Yang_021");

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 023.建跨层级节点表，replace一条记录B的某一元素值为null，写入一条数
                据成功，再次修改回原值，预期最后修改成功
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniq, Yang_039_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 2;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF1 = 100;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 50;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root", "Yang_023");

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 024.建跨层级节点表，replace一条记录唯一性字段中的所有元素值修改两次
                ，第一次修改成功；写入一条数据，第二次修改回原值，预期修改成功
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniq, Yang_039_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 7;
    testYangSetNodeProperty_PK1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF1 = 30;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 40;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 8;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 9;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 10;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 11;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root", "Yang_003");

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root", "Yang_001");

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 025.建跨层级节点表，replace一条记录，主键值不变、唯一性字段修改成成
                和另一条数据相同，预期失败
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniq, Yang_039_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    char errorMsg1[128] = {}, errorMsg2[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 7;
    testYangSetNodeProperty_PK1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF1 = 30;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 40;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 8;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 9;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 10;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 11;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root", "Yang_003");

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = testTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 026.建跨层级节点表，删除一条记录，将另一条记录唯一性字段的值修改为
                与第一条记录的值相同、增加一条数据验证修改，预期成功
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniq, Yang_039_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 7;
    testYangSetNodeProperty_PK1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF1 = 30;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 40;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 8;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 9;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 10;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 11;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root", "Yang_003");
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root", "Yang_026");
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 027.建跨层级节点表，删除一条记录，再建同样的一条记录，循环多次，每
                次写入值相同，预期成功
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniq, Yang_039_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    for (uint32_t i = 0; i < 3; i++) {
        // 启动事务
        ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置批处理batch参数
        ret = testBatchPrepare(g_conn_async, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = 100;
        testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置child节点表1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = 1;
        testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 编辑子节点
        ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t valueF1 = 10;
        ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        char valueF2[8] = "string";
        ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        char valueF3[8] = "string";
        ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t valueF4 = 20;
        ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 3;
        testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 4;
        testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 5;
        testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 6;
        testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置child节点表2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = 7;
        testYangSetNodeProperty_PK1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 编辑子节点
        ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        valueF1 = 30;
        ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        valueF4 = 40;
        ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 8;
        testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 9;
        testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 10;
        testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 11;
        testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 批处理提交
        AsyncUserDataT data = { 0 };
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // subtree查询
        testSubtreeFilter(g_stmt_async, "root", "Yang_003");

        // 提交事务
        ret = testTransCommitAsync(g_conn_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 启动事务
        ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置批处理batch参数
        ret = testBatchPrepare(g_conn_async, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_DELETE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // subtree查询
        testSubtreeFilter(g_stmt_async, "root", "Yang_026");

        // 提交事务
        ret = testTransCommitAsync(g_conn_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

/* ****************************************************************************
 Description  : 028.建跨层级节点表，删除一条记录，再建同样的一条记录，修改后再写同
                样的一条记录，预期成功
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniq, Yang_039_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 7;
    testYangSetNodeProperty_PK1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF1 = 30;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 40;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 8;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 9;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 10;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 11;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root", "Yang_003");
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key
    const char *fieldValuex = "strings";
    PID = 1;
    testSetKeyNameAndValue(g_stmt_list[1], fieldValuex, PID, true);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 029.建跨层级节点表，组合操作（insert -- merge  replace -- delete
                ），在list表中写入一条含有唯一性字段的数据；更新为一条没有null值的数
                据、与原值不同；再次写入一条与更新时一样的数据；更新数据、值相同；删
                除更新后的数据；最后写入一条数据；
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniq, Yang_039_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 7;
    testYangSetNodeProperty_PK1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF1 = 30;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 40;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 8;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 9;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 10;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 11;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root", "Yang_003");
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    const char *fieldValuex = "string";
    fieldValue = 1;
    PID = 1;
    testSetKeyNameAndValue(g_stmt_list[1], fieldValuex, PID, true);
    uint32_t newValue = 1;
    testYangSetNodePropertyWithoutF0(g_childNode[1], newValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 333;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 444;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 555;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root", "Yang_029_01");
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root", "Yang_001");
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root", "Yang_026");
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ******************************************************************************
Description  : 030.key上的字段数目等于32个（包括ID/PID，如果存在）建表成功
Author		 : wangxuming
 ****************************************************************************** */
TEST_F(TreeListUniqErr, Yang_039_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = { 0 };

    readJanssonFile("schema_file/yang_tree_vertex_keynum32.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_T0_stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    AsyncUserDataT data1 = { 0 };
    readJanssonFile("schema_file/yang_tree_edge.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateEdgeLabelAsync(g_T0_stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    ret = GmcDropEdgeLabelAsync(g_T0_stmt, "root_L0", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcDropVertexLabelAsync(g_T0_stmt, "root", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(g_T0_stmt, "root::L0", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ******************************************************************************
Description  : 031.建跨层级节点表，key上的字段数目等于33个（包括ID/PID，如果存在）
Author		 : wangxuming
 ****************************************************************************** */
TEST_F(TreeListUniqErr, Yang_039_031)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = { 0 };

    readJanssonFile("schema_file/yang_tree_vertex_keynum33.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_T0_stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_ARRAY_SUBSCRIPT_ERROR, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 032.建跨层级节点表，key上不存在PID，进行全表校验，写入两条唯一性相
                同的数据，冲突
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniq, Yang_039_032)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNIQUE_VIOLATION, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = testTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ******************************************************************************
Description  : 033.建跨层级节点表，schema中唯一性约束中的constraints字段，unique设
                为false、null_check设为true
Author		 : wangxuming
 ****************************************************************************** */
TEST_F(TreeListUniqErr, Yang_039_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = { 0 };

    readJanssonFile("schema_file/yang_tree_vertex_uniqueF.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_T0_stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    ret = GmcDropVertexLabelAsync(g_T0_stmt, "root", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcDropVertexLabelAsync(g_T0_stmt, "root::L0", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ******************************************************************************
Description  : 034.建跨层级节点表，schema中唯一性约束中的constraints字段，unique设
                为true、null_check设为false
Author		 : wangxuming
 ****************************************************************************** */
TEST_F(TreeListUniqErr, Yang_039_034)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_TABLE_DEFINITION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = { 0 };

    readJanssonFile("schema_file/yang_tree_vertex_null_checkF.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_T0_stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ******************************************************************************
Description  : 035.建跨层级节点表，schema中唯一性约束中的constraints字段，unique设
                为false、null_check设为false
Author		 : wangxuming
 ****************************************************************************** */
TEST_F(TreeListUniqErr, Yang_039_035)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_TABLE_DEFINITION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = { 0 };

    readJanssonFile("schema_file/yang_tree_vertex_constraintsF.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_T0_stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ******************************************************************************
Description  : 036.唯一性字段写入的数据总长为532字节
Author		 : wangxuming
 ****************************************************************************** */
TEST_F(TreeListUniq, Yang_039_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[535];
    memset(valueF2, 'a', 531);
    valueF2[531] = '\0';
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root", "Yang_036");
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ******************************************************************************
Description  : 037.建跨层级节点表，写入的唯一性字段总长度等于533字节，写入数据失败
Author		 : wangxuming
 ****************************************************************************** */
TEST_F(TreeListUniq, Yang_039_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[535];
    memset(valueF2, 'a', 531);
    valueF2[531] = '\0';
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ******************************************************************************
Description  : 038.建跨层级节点表，一条记录建立16条二级索引，预期建表成功
Author		 : wangxuming
 ****************************************************************************** */
TEST_F(TreeListUniqErr, Yang_039_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = { 0 };

    readJanssonFile("schema_file/yang_tree_vertex_maxlistlocalhash.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_T0_stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    AsyncUserDataT data1 = { 0 };
    readJanssonFile("schema_file/yang_tree_edge.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateEdgeLabelAsync(g_T0_stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    ret = GmcDropEdgeLabelAsync(g_T0_stmt, "root_L0", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcDropVertexLabelAsync(g_T0_stmt, "root", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(g_T0_stmt, "root::L0", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 039.建跨层级节点表，16条二级索引值都相同，写入成功
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniq, Yang_039_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root", "Yang_001");
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ******************************************************************************
Description  : 040.建跨层级节点表，16条二级索引中，存在相同的唯一性字段，建表成功
Author		 : wangxuming
 ****************************************************************************** */
TEST_F(TreeListUniqErr, Yang_039_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = { 0 };

    readJanssonFile("schema_file/yang_tree_vertex_maxlistlocalhash_abnomal.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_T0_stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    AsyncUserDataT data1 = { 0 };
    readJanssonFile("schema_file/yang_tree_edge.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateEdgeLabelAsync(g_T0_stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    ret = GmcDropEdgeLabelAsync(g_T0_stmt, "root_L0", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcDropVertexLabelAsync(g_T0_stmt, "root", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(g_T0_stmt, "root::L0", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ******************************************************************************
Description  : 041.建跨层级节点表，16条二级索引中，某几条索引只存在PID，建表成功
Author		 : wangxuming
 ****************************************************************************** */
TEST_F(TreeListUniqErr, Yang_039_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = { 0 };

    readJanssonFile("schema_file/yang_tree_vertex_maxlistlocalhash_abnomalPID.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_T0_stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    AsyncUserDataT data1 = { 0 };
    readJanssonFile("schema_file/yang_tree_edge.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateEdgeLabelAsync(g_T0_stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    ret = GmcDropEdgeLabelAsync(g_T0_stmt, "root_L0", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcDropVertexLabelAsync(g_T0_stmt, "root", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(g_T0_stmt, "root::L0", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ******************************************************************************
Description  : 042.建跨层级节点表，16条二级索引中，某一字段重复出现在不同索引中，建表
                成功
Author		 : wangxuming
 ****************************************************************************** */
TEST_F(TreeListUniqErr, Yang_039_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = { 0 };

    readJanssonFile("schema_file/yang_tree_vertex_maxlistlocalhash_abnomal.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_T0_stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    AsyncUserDataT data1 = { 0 };
    readJanssonFile("schema_file/yang_tree_edge.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateEdgeLabelAsync(g_T0_stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    ret = GmcDropEdgeLabelAsync(g_T0_stmt, "root_L0", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcDropVertexLabelAsync(g_T0_stmt, "root", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(g_T0_stmt, "root::L0", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ******************************************************************************
Description  : 043.建跨层级节点表，16条二级索引中，某几条索引不存在PID，建表成功
Author		 : wangxuming
 ****************************************************************************** */
TEST_F(TreeListUniqErr, Yang_039_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = { 0 };

    readJanssonFile("schema_file/yang_tree_vertex_maxlistlocalhash_abnomalPIDnone.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_T0_stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    AsyncUserDataT data1 = { 0 };
    readJanssonFile("schema_file/yang_tree_edge.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateEdgeLabelAsync(g_T0_stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    ret = GmcDropEdgeLabelAsync(g_T0_stmt, "root_L0", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcDropVertexLabelAsync(g_T0_stmt, "root", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(g_T0_stmt, "root::L0", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ******************************************************************************
Description  : 044.建跨层级节点表，一条记录建立17条二级索引，预期建表失败
Author		 : wangxuming
 ****************************************************************************** */
TEST_F(TreeListUniqErr, Yang_039_044)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = { 0 };

    readJanssonFile("schema_file/yang_tree_vertex_beyonglistlocalhash.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_T0_stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PROGRAM_LIMIT_EXCEEDED, data.status);
    free(vLabelSchema);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 045.建跨层级节点表，2条二级索引，写入一条数据，索引值和原数据都不相
                同，预期写入成功
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniqDoubleIndex, Yang_039_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root", "Yang_001");
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  :  046.建跨层级节点表，2条二级索引，写入一条数据，一条索引值和一条数
                据的索引相同、一条和另一条数据相同，预期写入失败
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniqDoubleIndex, Yang_039_046)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNIQUE_VIOLATION, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = testTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  :  047.建跨层级节点表，2条二级索引，写入一条数据，一条索引值和原数据相
                同、一条不同（不含null），预期写入失败
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniqDoubleIndex, Yang_039_047)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNIQUE_VIOLATION, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = testTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  :  047.建跨层级节点表，2条二级索引，写入一条数据，一条索引值和原数据相
                同、一条不同（不含null），预期写入失败
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniqDoubleIndex, Yang_039_048)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNIQUE_VIOLATION, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = testTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 049.建跨层级节点表，2条二级索引，写入一条数据，每条索引值和原数据不
                都相同，预期写入成功
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniqDoubleIndex, Yang_039_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 7;
    testYangSetNodeProperty_PK1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 40;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 8;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 9;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 10;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 11;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root", "Yang_006");
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 050.建跨层级节点表，2条二级索引，修改后，写入一条数据、2条索引的值
                和修改后的数据对换，预期修改成功
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniqDoubleIndex, Yang_039_050)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    const char *fieldValuex = "string";
    fieldValue = 1;
    PID = 1;
    testSetKeyNameAndValue(g_stmt_list[1], fieldValuex, PID, true);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    const char *fieldValuexx = "stringg";
    fieldValue = 1;
    PID = 1;
    testSetKeyNameAndValue(g_stmt_list[1], fieldValuexx, PID, true);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNIQUE_VIOLATION, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = testTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 051.建跨层级节点表，2条二级索引带null，写入一条数据，索引值和原数据
                都不相同，预期写入成功
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniqDoubleIndex, Yang_039_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root", "Yang_002");
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 052.建跨层级节点表，2条二级索引，将一条数据的一条索引改成和另一条相
                同、另一条不同（不含null），预期修改失败
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniqDoubleIndex, Yang_039_052)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    const char *fieldValuex = "string";
    fieldValue = 1;
    PID = 1;
    testSetKeyNameAndValue(g_stmt_list[1], fieldValuex, PID, true);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    const char *fieldValuexx = "stringg";
    fieldValue = 1;
    PID = 1;
    testSetKeyNameAndValue(g_stmt_list[1], fieldValuexx, PID, true);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 60;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 8;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    PID = 1;
    testSetKeyNameAndValue(g_stmt_list[1], fieldValuex, PID, true);
    uint32_t newValue = 1;
    testYangSetNodePropertyWithoutF0(g_childNode[1], newValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 333;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 444;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 555;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 8;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNIQUE_VIOLATION, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = testTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 053.建跨层级节点表，2条二级索引，修改一条数据B，每条索引值和数据A不
                都相同，预期修改成功
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniqDoubleIndex, Yang_039_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    const char *fieldValuex = "string";
    fieldValue = 1;
    PID = 1;
    testSetKeyNameAndValue(g_stmt_list[1], fieldValuex, PID, true);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    const char *fieldValuexx = "stringg";
    fieldValue = 1;
    PID = 1;
    testSetKeyNameAndValue(g_stmt_list[1], fieldValuexx, PID, true);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 8;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 054.建跨层级节点表，2条二级索引，一条二级索引带默认值，重复写入，报
                唯一性冲突
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniqDoubleIndex, Yang_039_054)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0Dft(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0DftF4(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[1], "c1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c1_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 7;
    testYangSetNodeProperty_PK1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0Dft(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF1 = 30;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 40;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 8;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 9;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 10;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 11;
    testYangSetNodePropertyWithoutF0DftF4(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[1], "c1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c1_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNIQUE_VIOLATION, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = testTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ******************************************************************************
Description  : 055.建跨层级节点表，非list节点添加list_localhash索引，预期建表报错
Author		 : wangxuming
 ****************************************************************************** */
TEST_F(TreeListUniqErr, Yang_039_055)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_TABLE_DEFINITION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = { 0 };

    readJanssonFile("schema_file/unique_vertexLabelOther.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_T0_stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 056.建跨层级节点表，根据二级索引进行merge操作，预期merge失败
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniq, Yang_039_056)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    const char *fieldValuex = "string";
    fieldValue = 1;
    PID = 1;
    testSetKeyNameAndValue(g_stmt_list[1], fieldValuex, PID, true);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    const char *fieldValuexx = "stringg";
    fieldValue = 1;
    PID = 1;
    testSetKeyNameAndValue(g_stmt_list[1], fieldValuexx, PID, true);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    PID = 1;
    testSetKeyNameAndValue(g_stmt_list[1], fieldValuex, PID, true);
    uint32_t newValue = 1;
    testYangSetNodePropertyWithoutF0(g_childNode[1], newValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 333;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 444;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 555;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNIQUE_VIOLATION, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = testTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 057.建跨层级节点表，根据二级索引进行delete操作，预期delete失败
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniq, Yang_039_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 7;
    testYangSetNodeProperty_PK1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF1 = 30;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 40;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 8;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 9;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 10;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 11;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root", "Yang_003");
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root", "Yang_026");
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 058.建跨层级节点表，根据二级索引进行扫描操作，预期扫描失败
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniq, Yang_039_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    char errorMsg1[128] = {}, errorMsg2[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 7;
    testYangSetNodeProperty_PK1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF1 = 30;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 40;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 8;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 9;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 10;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 11;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root", "Yang_003");
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = testTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 059.建跨层级节点表，根据二级索引进行getCount操作，预期getCount失败
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniq, Yang_039_059)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNIQUE_VIOLATION, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = testTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ******************************************************************************
Description  : 060.建跨层级节点表，主键索引字段个数为32个，预期建表成功
Author		 : wangxuming
 ****************************************************************************** */
TEST_F(TreeListUniqErr, Yang_039_060)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = { 0 };

    readJanssonFile("schema_file/yang_tree_vertex_pknum32.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_T0_stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    AsyncUserDataT data1 = { 0 };
    readJanssonFile("schema_file/yang_tree_edge.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateEdgeLabelAsync(g_T0_stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    ret = GmcDropEdgeLabelAsync(g_T0_stmt, "root_L0", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcDropVertexLabelAsync(g_T0_stmt, "root", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(g_T0_stmt, "root::L0", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ******************************************************************************
Description  : 061.建跨层级节点表，主键索引为33个，预期建表失败
Author		 : wangxuming
 ****************************************************************************** */
TEST_F(TreeListUniqErr, Yang_039_061)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = { 0 };

    readJanssonFile("schema_file/yang_tree_vertex_keynum33.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_T0_stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_ARRAY_SUBSCRIPT_ERROR, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ******************************************************************************
Description  : 062.建跨层级节点表，list_localhash属性字段类型为float，预期建表失败
Author		 : wangxuming
 ****************************************************************************** */
TEST_F(TreeListUniqErr, Yang_039_062)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_TABLE_DEFINITION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = { 0 };

    readJanssonFile("schema_file/uniqueIsFloat.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_T0_stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ******************************************************************************
Description  : 063.建跨层级节点表，list_localhash属性字段类型为double，预期建表失败
Author		 : wangxuming
 ****************************************************************************** */
TEST_F(TreeListUniqErr, Yang_039_063)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_TABLE_DEFINITION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = { 0 };

    readJanssonFile("schema_file/uniqueIsDouble.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_T0_stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ******************************************************************************
Description  : 064.建跨层级节点表，list_localhash属性字段类型为boolean，预期建表失败
Author		 : wangxuming
 ****************************************************************************** */
TEST_F(TreeListUniqErr, Yang_039_064)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_TABLE_DEFINITION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = { 0 };

    readJanssonFile("schema_file/uniqueIsBoolean.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_T0_stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ******************************************************************************
Description  : 065.建跨层级节点表，list_localhash属性字段类型为bitmap，预期建表失败
Author		 : wangxuming
 ****************************************************************************** */
TEST_F(TreeListUniqErr, Yang_039_065)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_TABLE_DEFINITION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = { 0 };

    readJanssonFile("schema_file/uniqueIsBitmap.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_T0_stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ******************************************************************************
Description  : 066.建跨层级节点表，设置schema树模型节点深度为32层（节点+字段）建表，
                预期建表成功
Author		 : wangxuming
 ****************************************************************************** */
TEST_F(TreeListUniqErr, Yang_039_066)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = { 0 };

    readJanssonFile("schema_file/Yang_32.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_T0_stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    testDropLabelYang32(g_T0_stmt);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ******************************************************************************
Description  : 067.建跨层级节点表，设置schema树模型节点深度为33层（节点+字段）建表，
                预期建表失败
Author		 : wangxuming
 ****************************************************************************** */
TEST_F(TreeListUniqErr, Yang_039_067)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_JSON_CONTENT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = { 0 };

    readJanssonFile("schema_file/Yang_33.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_T0_stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_JSON_CONTENT, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ******************************************************************************
Description  : 068.建跨层级节点表，修改部分冲突后查询errpath
Author		 : wangxuming
 ****************************************************************************** */
TEST_F(TreeListUniq, Yang_039_068)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 9;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ******************************************************************************
Description  : 069.建跨层级节点表，修改完全冲突后查询errpath
Author		 : wangxuming
 ****************************************************************************** */
TEST_F(TreeListUniq, Yang_039_069)
{
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNIQUE_VIOLATION, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = testTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 070.建跨层级节点表，两个事务都覆盖写，然后replace修改唯一性字段，一
                个事务修改为相同的，一个字段修改为不同的
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniq, Yang_039_070)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    char errorMsg1[128] = {}, errorMsg2[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 7;
    testYangSetNodeProperty_PK1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF1 = 30;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 40;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 8;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 9;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 10;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 11;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = testTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 071.建跨层级节点表，组合操作（insert -- merge -- replace -- delete
                ）操作循环1000次，观察内存变化，差异不应很大
 Author       : wangxuming
**************************************************************************** */
TEST_F(TreeListUniq, Yang_039_071)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    int cyclenum;

    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
#if defined ENV_RTOSV2X
    cyclenum = 100;
#else
    cyclenum = 1000;
#endif

    for (int i = 0; i < cyclenum; i++) {
        GmcBatchT *batch = NULL;
        // 启动事务
        ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置批处理batch参数
        ret = testBatchPrepare(g_conn_async, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = 100;
        testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置child节点表1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = 1;
        testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 编辑子节点
        ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t valueF1 = 10;
        ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        char valueF2[8] = "string";
        ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        char valueF3[8] = "string";
        ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t valueF4 = 20;
        ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 3;
        testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 4;
        testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 5;
        testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 6;
        testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置child节点表2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = 7;
        testYangSetNodeProperty_PK1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 编辑子节点
        ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        valueF1 = 30;
        ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        valueF4 = 40;
        ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 8;
        testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 9;
        testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 10;
        testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 11;
        testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 批处理提交
        AsyncUserDataT data = { 0 };
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 提交事务
        ret = testTransCommitAsync(g_conn_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 启动事务
        ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置批处理batch参数
        ret = testBatchPrepare(g_conn_async, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = 200;
        testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置child节点1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        const char *fieldValuex = "string";
        fieldValue = 1;
        PID = 1;
        testSetKeyNameAndValue(g_stmt_list[1], fieldValuex, PID, true);
        uint32_t newValue = 1;
        testYangSetNodePropertyWithoutF0(g_childNode[1], newValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

        // 编辑子节点
        ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_MERGE, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        valueF1 = 10;
        ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        valueF4 = 20;
        ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_MERGE, &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 333;
        testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

        ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_MERGE, &g_childNode[4]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 444;
        testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

        ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_MERGE, &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 555;
        testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

        ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_MERGE, &g_childNode[4]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 6;
        testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 提交事务
        ret = testTransCommitAsync(g_conn_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 启动事务
        ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置批处理batch参数
        ret = testBatchPrepare(g_conn_async, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = 100;
        testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置child节点1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = 1;
        testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 编辑子节点
        ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_MERGE, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        valueF1 = 10;
        ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        valueF4 = 20;
        ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_MERGE, &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 3;
        testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_MERGE, &g_childNode[4]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 4;
        testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

        ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_MERGE, &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 5;
        testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_MERGE, &g_childNode[4]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 6;
        testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 提交事务
        ret = testTransCommitAsync(g_conn_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 启动事务
        ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置批处理batch参数
        ret = testBatchPrepare(g_conn_async, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_DELETE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 提交事务
        ret = testTransCommitAsync(g_conn_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

void *ThreadDML(void *args)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 7;
    testYangSetNodeProperty_PK1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF1 = 30;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 40;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 8;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 9;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 10;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 11;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    const char *fieldValuex = "string";
    fieldValue = 1;
    PID = 1;
    testSetKeyNameAndValue(g_stmt_list[1], fieldValuex, PID, true);
    uint32_t newValue = 1;
    testYangSetNodePropertyWithoutF0(g_childNode[1], newValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 333;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 444;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 555;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "root::L0", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 编辑子节点
    ret = GmcYangEditChildNode(g_childNode[1], "c0", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF1 = 10;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 20;
    ret = testYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_0", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_0_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(g_childNode[2], "c0_1", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    ret = GmcYangEditChildNode(g_childNode[3], "c0_1_0", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1111");
}

void *ThreadSubtree(void *args)
{
    sleep(5);
    int32_t ret = testTransStartAsync(g_conn_async1, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // subtree查询
    testSubtreeFilter(g_stmt_async, "root", "Yang_072");
    ret = testTransCommitAsync(g_conn_async1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2222");
}

/* ******************************************************************************
Description  : 072.建跨层级节点表，组合操作（insert -- merge -- replace -- delete）
                与subtree多线程并发
Author		 : wangxuming
 ****************************************************************************** */
TEST_F(TreeListUniq, Yang_039_072)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    pthread_t thr_01;
    pthread_t thr_02;
    ret = pthread_create(&thr_01, NULL, ThreadDML, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&thr_02, NULL, ThreadSubtree, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_join(thr_01, NULL);
    pthread_join(thr_02, NULL);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1111");
}
