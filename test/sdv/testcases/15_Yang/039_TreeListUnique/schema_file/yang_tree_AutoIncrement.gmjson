[{"type": "container", "name": "root", "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string"}, {"type": "container", "name": "company", "fields": [{"name": "comment", "type": "string"}, {"name": "name", "type": "string"}]}], "keys": [{"node": "root", "name": "PK", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::L0", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false, "auto_increment": true}, {"name": "F1", "type": "uint32", "auto_increment": true}, {"name": "F2", "type": "string"}, {"name": "F3", "type": "string"}, {"name": "F4", "type": "uint32"}, {"type": "container", "name": "c0", "fields": [{"name": "F1", "type": "uint32", "auto_increment": true}, {"name": "F2", "type": "string"}, {"name": "F3", "type": "string"}, {"name": "F4", "type": "uint32", "auto_increment": true}, {"type": "container", "name": "c0_0", "fields": [{"name": "F1", "type": "uint32"}, {"name": "F2", "type": "string"}, {"name": "F3", "type": "string"}, {"name": "F4", "type": "uint32"}, {"type": "container", "name": "c0_0_0", "fields": [{"name": "F1", "type": "uint32"}, {"name": "F2", "type": "string"}, {"name": "F3", "type": "string"}, {"name": "F4", "type": "uint32", "auto_increment": true}]}]}, {"type": "container", "name": "c0_1", "fields": [{"name": "F1", "type": "uint32"}, {"name": "F2", "type": "string"}, {"name": "F3", "type": "string"}, {"name": "F4", "type": "uint32"}, {"type": "container", "name": "c0_1_0", "fields": [{"name": "F1", "type": "uint32"}, {"name": "F2", "type": "string"}, {"name": "F3", "type": "string"}, {"name": "F4", "type": "uint32"}]}]}]}], "keys": [{"node": "root::L0", "name": "PK", "fields": ["PID", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "root::L0", "name": "UK", "index": {"type": "list_localhash"}, "constraints": {"unique": true, "null_check": true}, "fields": ["F1", "c0/F1", "c0/F4", "c0/c0_1/c0_1_0/F4"]}]}, {"name": "root::L1", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "F1", "type": "uint32"}, {"name": "F2", "type": "string"}, {"name": "F3", "type": "string"}, {"name": "F4", "type": "uint32"}, {"type": "container", "name": "c0", "fields": [{"name": "F1", "type": "uint32"}, {"name": "F2", "type": "string"}, {"name": "F3", "type": "string"}, {"name": "F4", "type": "uint32"}, {"type": "container", "name": "c0_0", "fields": [{"name": "F1", "type": "uint32"}, {"name": "F2", "type": "string"}, {"name": "F3", "type": "string"}, {"name": "F4", "type": "uint32"}, {"type": "container", "name": "c0_0_0", "fields": [{"name": "F1", "type": "uint32"}, {"name": "F2", "type": "string"}, {"name": "F3", "type": "string"}, {"name": "F4", "type": "uint32"}]}]}, {"type": "container", "name": "c0_1", "fields": [{"name": "F1", "type": "uint32"}, {"name": "F2", "type": "string"}, {"name": "F3", "type": "string"}, {"name": "F4", "type": "uint32"}, {"type": "container", "name": "c0_1_0", "fields": [{"name": "F1", "type": "uint32"}, {"name": "F2", "type": "string"}, {"name": "F3", "type": "string"}, {"name": "F4", "type": "uint32"}]}]}]}, {"type": "container", "name": "c1", "fields": [{"name": "F1", "type": "uint32"}, {"name": "F2", "type": "string"}, {"name": "F3", "type": "string"}, {"name": "F4", "type": "uint32"}, {"type": "container", "name": "c1_0", "fields": [{"name": "F1", "type": "uint32"}, {"name": "F2", "type": "string"}, {"name": "F3", "type": "string"}, {"name": "F4", "type": "uint32"}]}]}], "keys": [{"node": "root::L1", "name": "PK", "fields": ["PID", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "root::L1", "name": "UK", "index": {"type": "list_localhash"}, "constraints": {"unique": true, "null_check": true}, "fields": ["F1", "c0/F2", "c1/c1_0/F3", "c0/c0_1/c0_1_0/F4"]}]}, {"name": "root::L2", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "F1", "type": "uint32", "default": 1}, {"name": "F2", "type": "string"}, {"name": "F3", "type": "string"}, {"name": "F4", "type": "uint32"}, {"type": "container", "name": "c0", "fields": [{"name": "F1", "type": "uint32"}, {"name": "F2", "type": "string", "default": "c0_F2"}, {"name": "F3", "type": "string"}, {"name": "F4", "type": "uint32"}, {"type": "container", "name": "c0_0", "fields": [{"name": "F1", "type": "uint32"}, {"name": "F2", "type": "string"}, {"name": "F3", "type": "string"}, {"name": "F4", "type": "uint32"}, {"type": "container", "name": "c0_0_0", "fields": [{"name": "F1", "type": "uint32"}, {"name": "F2", "type": "string"}, {"name": "F3", "type": "string"}, {"name": "F4", "type": "uint32"}]}]}, {"type": "container", "name": "c0_1", "fields": [{"name": "F1", "type": "uint32"}, {"name": "F2", "type": "string"}, {"name": "F3", "type": "string"}, {"name": "F4", "type": "uint32"}, {"type": "container", "name": "c0_1_0", "fields": [{"name": "F1", "type": "uint32"}, {"name": "F2", "type": "string"}, {"name": "F3", "type": "string"}, {"name": "F4", "type": "uint32", "default": 4}]}]}]}, {"type": "container", "name": "c1", "fields": [{"name": "F1", "type": "uint32"}, {"name": "F2", "type": "string"}, {"name": "F3", "type": "string"}, {"name": "F4", "type": "uint32"}, {"type": "container", "name": "c1_0", "fields": [{"name": "F1", "type": "uint32"}, {"name": "F2", "type": "string"}, {"name": "F3", "type": "string", "default": "c1_c1_0_F3"}, {"name": "F4", "type": "uint32"}]}]}], "keys": [{"node": "root::L2", "name": "PK", "fields": ["PID", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "root::L2", "name": "UK", "index": {"type": "list_localhash"}, "constraints": {"unique": true, "null_check": true}, "fields": ["F1", "c0/F2", "c1/c1_0/F3", "c0/c0_1/c0_1_0/F4"]}]}]