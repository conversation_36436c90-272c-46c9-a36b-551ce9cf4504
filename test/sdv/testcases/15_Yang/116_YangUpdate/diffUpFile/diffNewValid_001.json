alias_ContainerOne:update[(pri<PERSON><PERSON>(ID:1)),(pri<PERSON><PERSON>(ID:1))]
alias_ContainerOne.F25:create(100)
alias_ContainerOne.ContainerTwo:create
ContainerTwo.F8:create(100)
ContainerTwo.F9:create(100)
ContainerTwo.ContainerTwoAdd:create
ContainerTwoAdd.F1:create(100)
ContainerTwoAdd.F2:create(NIL:8)
ContainerTwoAdd.F3:create(1.000000)
ContainerTwoAdd.F4:create(NIL:8)
ContainerTwoAdd.F5:create(1.000000)
ContainerTwoAdd.F6:create(string)
ContainerTwoAdd.F7:create(65)
ContainerTwoAdd.F8:create(100)
alias_ContainerOne.Choice:create
Choice.CaseOne:create
CaseOne.F0:create(100)
CaseOne.F8:create(100)
CaseOne.CaseOneAdd:create
CaseOneAdd.F1:create(100)
CaseOneAdd.F2:create(NIL:8)
CaseOneAdd.F3:create(1.000000)
CaseOneAdd.F4:create(NIL:8)
CaseOneAdd.F5:create(1.000000)
CaseOneAdd.F6:create(string)
CaseOneAdd.F7:create(65)
alias_ContainerOne.alias_ListOne:update[(priKey(PID:1,F0:str000)),(priKey(PID:1,F0:str000))]
alias_ListOne.ContainerAdd:create
ContainerAdd.F1:create(100)
ContainerAdd.F2:create(NIL:8)
ContainerAdd.F3:create(1.000000)
ContainerAdd.F4:create(NIL:8)
ContainerAdd.F5:create(1.000000)
ContainerAdd.F6:create(string)
ContainerAdd.F7:create(65)
alias_ContainerOne.alias_ListOne:update[(priKey(PID:1,F0:str001), preKey(PID:1,F0:str000)),(priKey(PID:1,F0:str001), preKey(PID:1,F0:str000))]
alias_ListOne.ContainerAdd:create
ContainerAdd.F1:create(100)
ContainerAdd.F2:create(NIL:8)
ContainerAdd.F3:create(1.000000)
ContainerAdd.F4:create(NIL:8)
ContainerAdd.F5:create(1.000000)
ContainerAdd.F6:create(string)
ContainerAdd.F7:create(65)
alias_ContainerOne.alias_ListOne:update[(priKey(PID:1,F0:str002), preKey(PID:1,F0:str001)),(priKey(PID:1,F0:str002), preKey(PID:1,F0:str001))]
alias_ListOne.ContainerAdd:create
ContainerAdd.F1:create(100)
ContainerAdd.F2:create(NIL:8)
ContainerAdd.F3:create(1.000000)
ContainerAdd.F4:create(NIL:8)
ContainerAdd.F5:create(1.000000)
ContainerAdd.F6:create(string)
ContainerAdd.F7:create(65)
alias_ContainerOne.alias_ListThree:create[(priKey(PID:1,F0:0)),(NULL)]
alias_ListThree.ID:create(1)
alias_ListThree.F8:create(100)
alias_ContainerOne.alias_ListThree:create[(priKey(PID:1,F0:1), preKey(PID:1,F0:0)),(NULL)]
alias_ListThree.ID:create(2)
alias_ListThree.F8:create(100)
alias_ContainerOne.alias_ListThree:create[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(NULL)]
alias_ListThree.ID:create(3)
alias_ListThree.F8:create(100)
