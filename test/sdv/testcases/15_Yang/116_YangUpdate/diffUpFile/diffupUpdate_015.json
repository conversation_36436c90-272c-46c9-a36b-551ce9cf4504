alias_ContainerOne:update[(pri<PERSON><PERSON>(ID:1)),(pri<PERSON><PERSON>(ID:1))]
alias_ContainerOne.F1:update(101,100)
alias_ContainerOne.F2:update(101,100)
alias_ContainerOne.F3:create(101)
alias_ContainerOne.F5:remove(555)
alias_ContainerOne.F6:create(666)
alias_ContainerOne.ContainerTwo:remove
ContainerTwo.F0:remove(100)
alias_ContainerOne.ContainerThree:create
ContainerThree.F1:create(100)
alias_ContainerOne.Choice:remove
Choice.CaseOne:remove
CaseOne.F0:remove(100)
alias_ContainerOne.alias_ListOne:update[(priKey(PID:1,F0:str000)),(priKey(PID:1,F0:str000))]
alias_ListOne.F3:remove(100)
alias_ListOne.F4:create(100)
alias_ListOne.ContainerTwo:remove
ContainerTwo.F0:remove(100)
alias_ListOne.ContainerThree:create
Container<PERSON>hree.F1:create(100)
alias_ListOne.Choice:remove
Choice.CaseOne:remove
CaseOne.F0:remove(100)
alias_ContainerOne.alias_ListOne:update[(priKey(PID:1,F0:str001), preKey(PID:1,F0:str000)),(priKey(PID:1,F0:str001), preKey(PID:1,F0:str000))]
alias_ListOne.F3:remove(100)
alias_ListOne.F4:create(100)
alias_ListOne.ContainerTwo:remove
ContainerTwo.F0:remove(100)
alias_ListOne.ContainerThree:create
ContainerThree.F1:create(100)
alias_ListOne.Choice:remove
Choice.CaseOne:remove
CaseOne.F0:remove(100)
