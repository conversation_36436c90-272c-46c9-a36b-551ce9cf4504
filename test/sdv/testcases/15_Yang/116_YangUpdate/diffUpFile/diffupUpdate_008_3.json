alias_ContainerOne:update[(pri<PERSON><PERSON>(ID:1)),(pri<PERSON><PERSON>(ID:1))]
alias_ContainerOne.F1:update(101,100)
alias_ContainerOne.F2:update(101,100)
alias_ContainerOne.F5:remove(555)
alias_ContainerOne.F6:create(666)
alias_ContainerOne.ContainerTwo:remove
ContainerTwo.F0:remove(100)
alias_ContainerOne.ContainerThree:create
ContainerThree.F1:create(100)
alias_ContainerOne.Choice:remove
Choice.CaseOne:remove
CaseOne.F0:remove(100)
alias_ContainerOne.alias_ListOne:create[(priKey(PID:1,F1:300)),(NULL)]
alias_ListOne.ID:create(3)
alias_ListOne.F0:create(str000)
alias_ListOne.F2:create(300)
alias_ListOne.F4:create(100)
alias_ListOne.F6:create(str000)
alias_ListOne.alias_LeafListThree:create[(priKey(PID:3,F0:2)),(NULL)]
alias_ListOne.alias_LeafListThree:create[(priKey(PID:3,F0:3), preKey(PID:3,F0:2)),(NULL)]
alias_ListOne.alias_LeafListThree:create[(priKey(PID:3,F0:4), preKey(PID:3,F0:3)),(NULL)]
alias_ContainerOne.alias_ListOne:create[(priKey(PID:1,F1:301), preKey(PID:1,F1:300)),(NULL)]
alias_ListOne.ID:create(4)
alias_ListOne.F0:create(str001)
alias_ListOne.F2:create(301)
alias_ListOne.F4:create(100)
alias_ListOne.alias_LeafListThree:create[(priKey(PID:4,F0:2)),(NULL)]
alias_ListOne.alias_LeafListThree:create[(priKey(PID:4,F0:3), preKey(PID:4,F0:2)),(NULL)]
alias_ListOne.alias_LeafListThree:create[(priKey(PID:4,F0:4), preKey(PID:4,F0:3)),(NULL)]
