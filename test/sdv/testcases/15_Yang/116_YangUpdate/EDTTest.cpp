/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#include "RCA_COMMON.h"
#include "tools.h"

class expand_data_type : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void expand_data_type::SetUpTestCase(){}
void expand_data_type::TearDownTestCase(){}
void expand_data_type::SetUp()
{
    system("rm -rf runLog");
}
void expand_data_type::TearDown(){}


/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_001_part1)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 001 0 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_001_part2)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 001 1 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_001_part3)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 001 2 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_001_part4)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 001 3 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_001_part5)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 001 4 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_001_part6)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 001 5 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_001_part7)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 001 6 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_001_part8)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 001 7 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_001_part9)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 001 8 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_001_part10)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 001 9 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_001_part11)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 001 10 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_001_part12)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 001 11 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_001_part13)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 001 12 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_001_part14)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 001 13 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_001_part15)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 001 14 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_001_part16)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 001 15 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_001_part17)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 001 16 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_001_part18)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 001 17 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_001_part19)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 001 18 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_001_part20)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 001 19 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_001_part21)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 001 20 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_001_part22)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 001 21 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_001_part23)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 001 22 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_001_part24)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 001 23 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_001_part25)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 001 24 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_001_part26)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 001 25 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 002.yang表，没编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_002_part1)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 002 0 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_002_part2)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 002 1 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_002_part3)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 002 2 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_002_part4)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 002 3 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_002_part5)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 002 4 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_002_part6)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 002 5 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_002_part7)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 002 6 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_002_part8)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 002 7 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_002_part9)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 002 8 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_002_part10)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 002 9 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_002_part11)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 002 10 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_002_part12)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 002 11 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_002_part13)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 002 12 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_002_part14)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 002 13 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_002_part15)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 002 14 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_002_part16)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 002 15 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_002_part17)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 002 16 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_002_part18)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 002 17 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_002_part19)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 002 18 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_002_part20)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 002 19 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_002_part21)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 002 20 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_002_part22)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 002 21 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_002_part23)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 002 22 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_002_part24)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 002 23 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_002_part25)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 002 24 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB(part1~part26)
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_002_part26)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf runLog;mkdir runLog");
    for (int i = 0; i < YANG_DATA_TYPE_NUM; i++) {
        system("./EDTCaller 002 25 " + to_string(i));
    }
    AW_MACRO_EXPECT_EQ_INT(YANG_DATA_TYPE_NUM, GetValue("grep -r '\\[       OK \\]' runLog/|wc -l"));
    AW_FUN_Log(LOG_STEP, "END");
}
