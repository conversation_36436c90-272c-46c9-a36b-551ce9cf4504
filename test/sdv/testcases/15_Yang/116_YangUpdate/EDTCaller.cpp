/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#include "../085_YangVertexScan/CPlusTools.h"

int main(int argc, char *argv[])
{
    string testID = argv[1];
    string sourceDataTypeID = argv[2];
    string updateDataTypeID = argv[3];
    system("rm -rf runDataType.txt");
    system("echo 'sourceDataTypeID: " + sourceDataTypeID + "' >> runDataType.txt");
    system("echo 'updateDataTypeID: " + updateDataTypeID + "' >> runDataType.txt");
    system("./EDTDebug --gtest_filter=*" + testID + "|tee runLog/test" + testID + "_" + sourceDataTypeID + "_"
           + updateDataTypeID + ".log");
    return 0;
}
