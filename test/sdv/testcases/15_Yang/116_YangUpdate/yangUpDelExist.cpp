/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#include "RCA_COMMON.h"
#include "../../../reliability/try.h"
#include "pstTool.h"
#include "aliasTool.h"
#include "updateTool.h"


class yangUpDelExist : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void yangUpDelExist::SetUpTestCase()
{
    // 按需持久化，启动服务
    char dbFilePath[1024] = {0};
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        printf("get env PWD fail.\n");
    }
    (void)sprintf(dbFilePath, "%s/gmdb", pwdDir);
    (void)Rmdir(dbFilePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, mkdir(dbFilePath, S_IRUSR | S_IWUSR));
    
    int ret = ChangeGmserverCfg((char *)"recover", NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeGmserverCfg((char *)"dataFileDirPath", dbFilePath));
    ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"yangAutoIndex", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 配置 public 默认事务类型
    ret = ChangeGmserverCfg((char *)"defaultTransactionType", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"defaultIsolationLevel", (char *)"2");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    ASSERT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void yangUpDelExist::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    ret = ChangeGmserverCfg((char *)"recover", NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void yangUpDelExist::SetUp()
{
    int ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务

    const char *namespace1 = "yangUpDelExist";
    const char *namespaceUserName = "abc";

    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观
    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // alloc all stmt
    TestYangAllocAllstmt();

    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void yangUpDelExist::TearDown()
{
    const char *namespace1 = "yangUpDelExist";
    TryDropNameSpace(g_stmt_async, namespace1);

    // 释放all stmt
    TestYangFreeAllstmt();

    int ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

const char *g_namespace1 = "yangUpDelExist";


 
/*****************************************************************************
 * Description  : 001.yang表，存量leaf删除，在root首部，预期失败
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangUpDelExist, Yang_116_yangUpDelExist_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    CreateVertexAndEditData();

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    ASSERT_TRUE(ret);

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("delExist01");

    // 按需刷盘并重启
    ret = flushAndRestartDbFail();
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);

    // 检查启动日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "RecoveryPath", "116_YangUpdate/update/delExist01");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "upgrading vertexLabel of file", "update_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009023", "Inv del F0");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009023", 
        "Adjust schema json for ContainerOne.");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009023",
        "parse vertex label for recovery, isCreate 0.");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);

    // 删除配置项yangUpgradeDirPath
    system("sh delCfgItem.sh \"yangUpgradeDirPath=\"");

    // 重启
    ret = restartMyDb(0, GMERR_OK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    ASSERT_TRUE(ret);

    // 再次编辑并查询diff、subtree（表未升级成功）
    EditDataAndCheck();

    AW_FUN_Log(LOG_STEP, "END");
}



/*****************************************************************************
 * Description  : 002.yang表，存量leaf删除，在root中间，预期失败
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangUpDelExist, Yang_116_yangUpDelExist_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    CreateVertexAndEditData();

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    ASSERT_TRUE(ret);

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("delExist02");

    // 按需刷盘并重启
    ret = flushAndRestartDbFail();
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);

    // 检查启动日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "RecoveryPath", "116_YangUpdate/update/delExist02");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "upgrading vertexLabel of file", "update_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009023", "Inv del F2");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009023", "Adjust schema json for ContainerOne.");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);

    // 删除配置项yangUpgradeDirPath
    system("sh delCfgItem.sh \"yangUpgradeDirPath=\"");

    // 重启
    ret = restartMyDb(0, GMERR_OK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    ASSERT_TRUE(ret);

    // 再次编辑并查询diff、subtree（表未升级成功）
    EditDataAndCheck();

    AW_FUN_Log(LOG_STEP, "END");
}



/*****************************************************************************
 * Description  : 003.yang表，存量leaf删除，在root尾部，预期失败
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangUpDelExist, Yang_116_yangUpDelExist_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    CreateVertexAndEditData();

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    ASSERT_TRUE(ret);

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("delExist03");

    // 按需刷盘并重启
    ret = flushAndRestartDbFail();
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);

    // 检查启动日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "RecoveryPath", "116_YangUpdate/update/delExist03");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "upgrading vertexLabel of file", "update_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009023", "Inv del F24");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009023", "Adjust schema json for ContainerOne.");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);

    // 删除配置项yangUpgradeDirPath
    system("sh delCfgItem.sh \"yangUpgradeDirPath=\"");

    // 重启
    ret = restartMyDb(0, GMERR_OK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    ASSERT_TRUE(ret);

    // 再次编辑并查询diff、subtree（表未升级成功）
    EditDataAndCheck();

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 004.yang表，存量leaf删除，在contain-contain首部，预期失败
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangUpDelExist, Yang_116_yangUpDelExist_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    CreateVertexAndEditData();

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    ASSERT_TRUE(ret);

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("delExist04");

    // 按需刷盘并重启
    ret = flushAndRestartDbFail();
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);

    // 检查启动日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "RecoveryPath", "116_YangUpdate/update/delExist04");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "upgrading vertexLabel of file", "update_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009023", "Inv del F0");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1015000", "Adjust node schema json for ContainerTwo");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009023", "Adjust schema json for ContainerOne");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);

    // 删除配置项yangUpgradeDirPath
    system("sh delCfgItem.sh \"yangUpgradeDirPath=\"");

    // 重启
    ret = restartMyDb(0, GMERR_OK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    ASSERT_TRUE(ret);

    // 再次编辑并查询diff、subtree（表未升级成功）
    EditDataAndCheck();

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 005.yang表，存量leaf删除，在contain-contain尾部，预期失败
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangUpDelExist, Yang_116_yangUpDelExist_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    CreateVertexAndEditData();

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    ASSERT_TRUE(ret);

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("delExist05");

    // 按需刷盘并重启
    ret = flushAndRestartDbFail();
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);

    // 检查启动日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "RecoveryPath", "116_YangUpdate/update/delExist05");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "upgrading vertexLabel of file", "update_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009023", "Inv del F7");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1015000", "Adjust node schema json for ContainerTwo");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009023", "Adjust schema json for ContainerOne");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);

    // 删除配置项yangUpgradeDirPath
    system("sh delCfgItem.sh \"yangUpgradeDirPath=\"");

    // 重启
    ret = restartMyDb(0, GMERR_OK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    ASSERT_TRUE(ret);

    // 再次编辑并查询diff、subtree（表未升级成功）
    EditDataAndCheck();

    AW_FUN_Log(LOG_STEP, "END");
}



/*****************************************************************************
 * Description  : 006.yang表，存量leaf删除，在choice-case非尾部，预期失败
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangUpDelExist, Yang_116_yangUpDelExist_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    CreateVertexAndEditData();

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    ASSERT_TRUE(ret);

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("delExist06");

    // 按需刷盘并重启
    ret = flushAndRestartDbFail();
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);

    // 检查启动日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "RecoveryPath", "116_YangUpdate/update/delExist06");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "upgrading vertexLabel of file", "update_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009023", "Inv del F7");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1015000", "Adjust node schema json for CaseOne");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009023", "Adjust schema json for ContainerOne");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);

    // 删除配置项yangUpgradeDirPath
    system("sh delCfgItem.sh \"yangUpgradeDirPath=\"");

    // 重启
    ret = restartMyDb(0, GMERR_OK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    ASSERT_TRUE(ret);

    // 再次编辑并查询diff、subtree（表未升级成功）
    EditDataAndCheck();

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 007.yang表，存量leaf删除，在list中间，预期失败
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangUpDelExist, Yang_116_yangUpDelExist_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    CreateVertexAndEditData();

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    ASSERT_TRUE(ret);

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("delExist07");

    // 按需刷盘并重启
    ret = flushAndRestartDbFail();
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);

    // 检查启动日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "RecoveryPath", "116_YangUpdate/update/delExist07");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "upgrading vertexLabel of file", "update_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009023", "Inv del F3");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009023", "Adjust schema json for ListOne.");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);

    // 删除配置项yangUpgradeDirPath
    system("sh delCfgItem.sh \"yangUpgradeDirPath=\"");

    // 重启
    ret = restartMyDb(0, GMERR_OK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    ASSERT_TRUE(ret);

    // 再次编辑并查询diff、subtree（表未升级成功）
    EditDataAndCheck();

    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 008.yang表，存量leaf删除，在list尾部，预期失败
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangUpDelExist, Yang_116_yangUpDelExist_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    CreateVertexAndEditData();

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    ASSERT_TRUE(ret);

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("delExist08");

    // 按需刷盘并重启
    ret = flushAndRestartDbFail();
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);

    // 检查启动日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "RecoveryPath", "116_YangUpdate/update/delExist08");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "upgrading vertexLabel of file", "update_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009023", "Inv del F7");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009023", "Adjust schema json for ListOne.");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);

    // 删除配置项yangUpgradeDirPath
    system("sh delCfgItem.sh \"yangUpgradeDirPath=\"");

    // 重启
    ret = restartMyDb(0, GMERR_OK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    ASSERT_TRUE(ret);

    // 再次编辑并查询diff、subtree（表未升级成功）
    EditDataAndCheck();

    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 009.yang表，存量leaf删除，在list-contain中间，预期失败
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangUpDelExist, Yang_116_yangUpDelExist_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    CreateVertexAndEditData();

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    ASSERT_TRUE(ret);

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("delExist09");

    // 按需刷盘并重启
    ret = flushAndRestartDbFail();
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);

    // 检查启动日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "RecoveryPath", "116_YangUpdate/update/delExist09");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "upgrading vertexLabel of file", "update_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009023", "Inv del F6");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1015000", "Adjust node schema json for ListContainerOne");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009023", "Adjust schema json for ListOne.");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);

    // 删除配置项yangUpgradeDirPath
    system("sh delCfgItem.sh \"yangUpgradeDirPath=\"");

    // 重启
    ret = restartMyDb(0, GMERR_OK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    ASSERT_TRUE(ret);

    // 再次编辑并查询diff、subtree（表未升级成功）
    EditDataAndCheck();

    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 010.yang表，存量leaf删除，在list-choic-case中间，预期失败
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangUpDelExist, Yang_116_yangUpDelExist_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    CreateVertexAndEditData();

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    ASSERT_TRUE(ret);

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("delExist10");

    // 按需刷盘并重启
    ret = flushAndRestartDbFail();
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);

    // 检查启动日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "RecoveryPath", "116_YangUpdate/update/delExist10");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "upgrading vertexLabel of file", "update_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009023", "Inv del F5");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1015000", "Adjust node schema json for ListchoiceCase");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009023", "Adjust schema json for ListOne.");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);

    // 删除配置项yangUpgradeDirPath
    system("sh delCfgItem.sh \"yangUpgradeDirPath=\"");

    // 重启
    ret = restartMyDb(0, GMERR_OK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    ASSERT_TRUE(ret);

    // 再次编辑并查询diff、subtree（表未升级成功）
    EditDataAndCheck();

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 011.yang表，存量leaf删除，在leaf-list下，预期建表失败
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangUpDelExist, Yang_116_yangUpDelExist_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    CreateVertexAndEditData();

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    ASSERT_TRUE(ret);

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("delExist11");

    // 按需刷盘并重启
    ret = flushAndRestartDbFail();
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);

    // 检查启动日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "RecoveryPath", "116_YangUpdate/update/delExist11");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "upgrading vertexLabel of file", "update_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009023", "Inv del F0");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009023", "Adjust schema json for LeafList.");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);

    // 删除配置项yangUpgradeDirPath
    system("sh delCfgItem.sh \"yangUpgradeDirPath=\"");

    // 重启
    ret = restartMyDb(0, GMERR_OK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    ASSERT_TRUE(ret);

    // 再次编辑并查询diff、subtree（表未升级成功）
    EditDataAndCheck();

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 012.yang表，存量contain删除，在root尾部，预期失败
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangUpDelExist, Yang_116_yangUpDelExist_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    CreateVertexAndEditData();

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    ASSERT_TRUE(ret);

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("delExist12");

    // 按需刷盘并重启
    ret = flushAndRestartDbFail();
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);

    // 检查启动日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "RecoveryPath", "116_YangUpdate/update/delExist12");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "upgrading vertexLabel of file", "update_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009023", "Inv del ContainerThree");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009023", "Adjust schema json for ContainerOne");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);

    // 删除配置项yangUpgradeDirPath
    system("sh delCfgItem.sh \"yangUpgradeDirPath=\"");

    // 重启
    ret = restartMyDb(0, GMERR_OK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    ASSERT_TRUE(ret);

    // 再次编辑并查询diff、subtree（表未升级成功）
    EditDataAndCheck();

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 013.yang表，存量contain删除，在contain-contain下，预期失败
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangUpDelExist, Yang_116_yangUpDelExist_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    CreateVertexAndEditData();

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    ASSERT_TRUE(ret);

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("delExist13");

    // 按需刷盘并重启
    ret = flushAndRestartDbFail();
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);

    // 检查启动日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "RecoveryPath", "116_YangUpdate/update/delExist13");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "upgrading vertexLabel of file", "update_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009023", "Inv del ContainerFour");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009023", "Adjust schema json for ContainerOne.");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);

    // 删除配置项yangUpgradeDirPath
    system("sh delCfgItem.sh \"yangUpgradeDirPath=\"");

    // 重启
    ret = restartMyDb(0, GMERR_OK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    ASSERT_TRUE(ret);

    // 再次编辑并查询diff、subtree（表未升级成功）
    EditDataAndCheck();

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 014.yang表，存量contain删除，在choice-case下，预期失败
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangUpDelExist, Yang_116_yangUpDelExist_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    CreateVertexAndEditData();

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    ASSERT_TRUE(ret);

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("delExist14");

    // 按需刷盘并重启
    ret = flushAndRestartDbFail();
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);

    // 检查启动日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "RecoveryPath", "116_YangUpdate/update/delExist14");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "upgrading vertexLabel of file", "update_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009023", "Inv del CaseContainerOne");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009023", "Adjust schema json for ContainerOne.");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);

    // 删除配置项yangUpgradeDirPath
    system("sh delCfgItem.sh \"yangUpgradeDirPath=\"");

    // 重启
    ret = restartMyDb(0, GMERR_OK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    ASSERT_TRUE(ret);

    // 再次编辑并查询diff、subtree（表未升级成功）
    EditDataAndCheck();

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 015.yang表，存量contain删除，在list下，预期失败
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangUpDelExist, Yang_116_yangUpDelExist_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    CreateVertexAndEditData();

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    ASSERT_TRUE(ret);

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("delExist15");

    // 按需刷盘并重启
    ret = flushAndRestartDbFail();
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);

    // 检查启动日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "RecoveryPath", "116_YangUpdate/update/delExist15");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "upgrading vertexLabel of file", "update_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009023", "Inv del ListContainerTwo");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009023", "Adjust schema json for ListOne");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);

    // 删除配置项yangUpgradeDirPath
    system("sh delCfgItem.sh \"yangUpgradeDirPath=\"");

    // 重启
    ret = restartMyDb(0, GMERR_OK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    ASSERT_TRUE(ret);

    // 再次编辑并查询diff、subtree（表未升级成功）
    EditDataAndCheck();

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 016.yang表，存量choice删除，在root下，预期失败
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangUpDelExist, Yang_116_yangUpDelExist_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    CreateVertexAndEditData();

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    ASSERT_TRUE(ret);

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("delExist16");

    // 按需刷盘并重启
    ret = flushAndRestartDbFail();
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);

    // 检查启动日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "RecoveryPath", "116_YangUpdate/update/delExist16");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "upgrading vertexLabel of file", "update_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009023", "Inv del ChoiceTwo");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009023", "Adjust schema json for ContainerOne");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);

    // 删除配置项yangUpgradeDirPath
    system("sh delCfgItem.sh \"yangUpgradeDirPath=\"");

    // 重启
    ret = restartMyDb(0, GMERR_OK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    ASSERT_TRUE(ret);

    // 再次编辑并查询diff、subtree（表未升级成功）
    EditDataAndCheck();

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 017.yang表，存量choice删除，在contain-contain下，预期失败
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangUpDelExist, Yang_116_yangUpDelExist_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge("schemaPstListDel/SubTreeVertexLabel.gmjson", "schemaPstListDel/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue,  GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue,  GMC_YANG_PROPERTY_OPERATION_MERGE, "F2");
    TestYangSetIDName(g_vertexLabelT0Node, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MAX_ELEMENTS;
    dataLef.expectedErrMsg = "violated max-elements clause";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpDelExist_017_Model");
    ASSERT_TRUE(ret);

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("delExist17");

    // 按需刷盘并重启
    ret = flushAndRestartDbFail();
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);

    // 检查启动日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "RecoveryPath", "116_YangUpdate/update/delExist17");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "upgrading vertexLabel of file", "update_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009023", "Inv del ChoiceThree");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009023", "Adjust schema json for ContainerOne");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
 
    // 删除配置项yangUpgradeDirPath
    system("sh delCfgItem.sh \"yangUpgradeDirPath=\"");

    // 重启
    ret = restartMyDb(0, GMERR_OK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpDelExist_017_Model");
    ASSERT_TRUE(ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestYangSetIDName(g_vertexLabelT0Node, "ID1", "level3", strlen("level3"), GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffUpFile/diffDelExist_017.json", NULL);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpDelExist_017");

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}



/*****************************************************************************
 * Description  : 018.yang表，存量choice删除，在choice-case下，预期失败
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangUpDelExist, Yang_116_yangUpDelExist_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge("schemaPstListDel/SubTreeVertexLabel.gmjson", "schemaPstListDel/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue,  GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue,  GMC_YANG_PROPERTY_OPERATION_MERGE, "F2");
    TestYangSetIDName(g_vertexLabelT0Node, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MAX_ELEMENTS;
    dataLef.expectedErrMsg = "violated max-elements clause";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpDelExist_017_Model");
    ASSERT_TRUE(ret);

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("delExist18");

    // 按需刷盘并重启
    ret = flushAndRestartDbFail();
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);

    // 检查启动日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "RecoveryPath", "116_YangUpdate/update/delExist18");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "upgrading vertexLabel of file", "update_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009023", "Inv del ChoiceFour");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009023", "Adjust schema json for ContainerOne");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
 
    // 删除配置项yangUpgradeDirPath
    system("sh delCfgItem.sh \"yangUpgradeDirPath=\"");

    // 重启
    ret = restartMyDb(0, GMERR_OK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpDelExist_017_Model");
    ASSERT_TRUE(ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestYangSetIDName(g_vertexLabelT0Node, "ID1", "level3", strlen("level3"), GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffUpFile/diffDelExist_017.json", NULL);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpDelExist_017");

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}



/*****************************************************************************
 * Description  : 019.yang表，存量choice删除，在list下，预期失败
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangUpDelExist, Yang_116_yangUpDelExist_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge("schemaPstListDel/SubTreeVertexLabel.gmjson", "schemaPstListDel/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue,  GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue,  GMC_YANG_PROPERTY_OPERATION_MERGE, "F2");
    TestYangSetIDName(g_vertexLabelT0Node, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MAX_ELEMENTS;
    dataLef.expectedErrMsg = "violated max-elements clause";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpDelExist_017_Model");
    ASSERT_TRUE(ret);

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("delExist19");

    // 按需刷盘并重启
    ret = flushAndRestartDbFail();
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);

    // 检查启动日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "RecoveryPath", "116_YangUpdate/update/delExist19");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "upgrading vertexLabel of file", "update_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009023", "Inv del Listchoice");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009023", "Adjust schema json for ListOne");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
 
    // 删除配置项yangUpgradeDirPath
    system("sh delCfgItem.sh \"yangUpgradeDirPath=\"");

    // 重启
    ret = restartMyDb(0, GMERR_OK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpDelExist_017_Model");
    ASSERT_TRUE(ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestYangSetIDName(g_vertexLabelT0Node, "ID1", "level3", strlen("level3"), GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffUpFile/diffDelExist_017.json", NULL);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpDelExist_017");

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 020.yang表，存量case删除，在root-choice下，预期失败
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangUpDelExist, Yang_116_yangUpDelExist_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge("schemaPstListDel/SubTreeVertexLabel.gmjson", "schemaPstListDel/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue,  GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue,  GMC_YANG_PROPERTY_OPERATION_MERGE, "F2");
    TestYangSetIDName(g_vertexLabelT0Node, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MAX_ELEMENTS;
    dataLef.expectedErrMsg = "violated max-elements clause";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpDelExist_017_Model");
    ASSERT_TRUE(ret);

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("delExist20");

    // 按需刷盘并重启
    ret = flushAndRestartDbFail();
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);

    // 检查启动日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "RecoveryPath", "116_YangUpdate/update/delExist20");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "upgrading vertexLabel of file", "update_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009023", "Inv del CaseTwo");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009023", "Adjust schema json for ContainerOne");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);

    // 删除配置项yangUpgradeDirPath
    system("sh delCfgItem.sh \"yangUpgradeDirPath=\"");

    // 重启
    ret = restartMyDb(0, GMERR_OK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpDelExist_017_Model");
    ASSERT_TRUE(ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestYangSetIDName(g_vertexLabelT0Node, "ID1", "level3", strlen("level3"), GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffUpFile/diffDelExist_017.json", NULL);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpDelExist_017");

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 021.yang表，存量case删除，在list-choice下，预期失败
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangUpDelExist, Yang_116_yangUpDelExist_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge("schemaPstListDel/SubTreeVertexLabel.gmjson", "schemaPstListDel/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue,  GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue,  GMC_YANG_PROPERTY_OPERATION_MERGE, "F2");
    TestYangSetIDName(g_vertexLabelT0Node, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MAX_ELEMENTS;
    dataLef.expectedErrMsg = "violated max-elements clause";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpDelExist_017_Model");
    ASSERT_TRUE(ret);

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("delExist21");

    // 按需刷盘并重启
    ret = flushAndRestartDbFail();
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);

    // 检查启动日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "RecoveryPath", "116_YangUpdate/update/delExist21");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "upgrading vertexLabel of file", "update_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009023", "Inv del ListchoiceCaseTwo");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009023", "Adjust schema json for ListOne");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
 
    // 删除配置项yangUpgradeDirPath
    system("sh delCfgItem.sh \"yangUpgradeDirPath=\"");

    // 重启
    ret = restartMyDb(0, GMERR_OK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpDelExist_017_Model");
    ASSERT_TRUE(ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestYangSetIDName(g_vertexLabelT0Node, "ID1", "level3", strlen("level3"), GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffUpFile/diffDelExist_017.json", NULL);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpDelExist_017");

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

