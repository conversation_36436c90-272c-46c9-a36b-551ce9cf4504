[{"type": "leaf-list", "name": "LeafListAdd", "alias": "alias_LeafListAdd", "min-elements": 0, "max-elements": 5, "clause": [{"type": "when", "formula": "/alias_ContainerOne/F1 = 100"}], "config": {"check_validity": true, "yang_model": 1, "auto_increment": 1, "isFastReadUncommitted": 0}, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}], "keys": [{"fields": ["PID", "F0"], "node": "LeafListAdd", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}]