/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#include "RCA_COMMON.h"
#include "../../../reliability/try.h"
#include "pstTool.h"
#include "updateTool.h"
#include "tools.h"

class expand_data_type : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void expand_data_type::SetUpTestCase()
{
    // 按需持久化，启动服务
    char dbFilePath[1024] = {0};
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        printf("get env PWD fail.\n");
    }
    (void)sprintf(dbFilePath, "%s/gmdb", pwdDir);
    (void)Rmdir(dbFilePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, mkdir(dbFilePath, S_IRUSR | S_IWUSR));
    
    int ret = ChangeGmserverCfg((char *)"recover", NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeGmserverCfg((char *)"dataFileDirPath", dbFilePath));
    ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"yangAutoIndex", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 配置 public 默认事务类型
    ret = ChangeGmserverCfg((char *)"defaultTransactionType", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"defaultIsolationLevel", (char *)"2");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void expand_data_type::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    ret = ChangeGmserverCfg((char *)"recover", NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void expand_data_type::SetUp()
{
    int ret = 0;
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    ASSERT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务

    const char *namespace1 = "yangUpAddNew";
    const char *namespaceUserName = "abc";

    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观
    TryDropNameSpace(g_stmt_async, namespace1);
    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void expand_data_type::TearDown()
{
    const char *namespace1 = "yangUpAddNew";
    TryDropNameSpace(g_stmt_async, namespace1);

    int ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

const char *g_namespace1 = "yangUpAddNew";
/*****************************************************************************
 * Description  : 001.yang表，编辑过的存量数据类型dataTypeA扩展为dataTypeB
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf dynFolder;mkdir -p dynFolder/source;mkdir -p dynFolder/update");
    int ret = 0;
    int sourceDataTypeID = GetValue("cat runDataType.txt", "sourceDataTypeID: ");
    string change = g_dataTypeStr[sourceDataTypeID];
    string changeFileName = "dynFolder/source/dataType" + to_string(sourceDataTypeID) + ".gmjson";
    ReplaceSomeWhere("schemaPstList/vertexAllTypeModex.gmjson", 0, change, changeFileName);
    ret = CreateVertexLabelAsync(g_stmt_async, changeFileName.c_str());
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheck();
    ret = YangDMLDataType((YangDataTypeE)sourceDataTypeID);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int updateDataTypeID = GetValue("cat runDataType.txt", "updateDataTypeID: ");
    string changeUpdate = g_dataTypeStr[updateDataTypeID];
    string updateFilePath = "dynFolder/update/dataType" + to_string(sourceDataTypeID) + "_"
                            + to_string(updateDataTypeID);
    system("mkdir -p " + updateFilePath);
    system("cp update/extraDataType/updateNamespace " + updateFilePath);
    ReplaceSomeWhere("update/extraDataType/update_vertex_0.gmjson", 0, changeUpdate,
                     updateFilePath + "/update_vertex_0.gmjson");
    // 修改配置项 yangUpgradeDirPath
    ModifyUpdateWholePathCfg(updateFilePath.c_str());
    if (IsAbleAlter((YangDataTypeE)sourceDataTypeID, (YangDataTypeE)updateDataTypeID)) {
        // 按需刷盘并重启
        ret = flushAndRestartDb();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        // 按需刷盘并重启失败
        ret = flushAndRestartDbFail();
        AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
        // 检查日志
        ret = AW_CHECK_LOG_EXIST(SERVER, 1, "unspport alterType");
        AW_MACRO_EXPECT_EQ_BOOL(true, ret);
    }
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 002.yang表，没编辑过的存量数据类型dataTypeA扩展为dataTypeB
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("rm -rf dynFolder;mkdir -p dynFolder/source;mkdir -p dynFolder/update");
    int ret = 0;
    int sourceDataTypeID = GetValue("cat runDataType.txt", "sourceDataTypeID: ");
    string change = g_dataTypeStr[sourceDataTypeID];
    string changeFileName = "dynFolder/source/dataType" + to_string(sourceDataTypeID) + ".gmjson";
    ReplaceSomeWhere("schemaPstList/vertexAllTypeModex.gmjson", 0, change, changeFileName);
    ret = CreateVertexLabelAsync(g_stmt_async, changeFileName.c_str());
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheck();

    int updateDataTypeID = GetValue("cat runDataType.txt", "updateDataTypeID: ");
    string changeUpdate = g_dataTypeStr[updateDataTypeID];
    string updateFilePath = "dynFolder/update/dataType" + to_string(sourceDataTypeID) + "_"
                            + to_string(updateDataTypeID);
    system("mkdir -p " + updateFilePath);
    system("cp update/extraDataType/updateNamespace " + updateFilePath);
    ReplaceSomeWhere("update/extraDataType/update_vertex_0.gmjson", 0, changeUpdate,
                     updateFilePath + "/update_vertex_0.gmjson");
    // 修改配置项 yangUpgradeDirPath
    ModifyUpdateWholePathCfg(updateFilePath.c_str());
    // 按需刷盘并重启（没编辑过的都能数据类型扩展）
    ret = flushAndRestartDb();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "END");
}
