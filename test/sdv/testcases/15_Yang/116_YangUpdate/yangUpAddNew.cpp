/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#include "RCA_COMMON.h"
#include "../../../reliability/try.h"
#include "pstTool.h"
#include "aliasTool.h"
#include "updateTool.h"


class yangUpAddNew : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void yangUpAddNew::SetUpTestCase()
{
    // 按需持久化，启动服务
    char dbFilePath[1024] = {0};
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        printf("get env PWD fail.\n");
    }
    (void)sprintf(dbFilePath, "%s/gmdb", pwdDir);
    (void)Rmdir(dbFilePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, mkdir(dbFilePath, S_IRUSR | S_IWUSR));
    
    int ret = ChangeGmserverCfg((char *)"recover", NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeGmserverCfg((char *)"dataFileDirPath", dbFilePath));
    ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"yangAutoIndex", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 配置 public 默认事务类型
    ret = ChangeGmserverCfg((char *)"defaultTransactionType", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"defaultIsolationLevel", (char *)"2");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    ASSERT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void yangUpAddNew::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    ret = ChangeGmserverCfg((char *)"recover", NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void yangUpAddNew::SetUp()
{
    int ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务

    const char *namespace1 = "yangUpAddNew";
    const char *namespaceUserName = "abc";

    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观
    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // alloc all stmt
    TestYangAllocAllstmt();

    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void yangUpAddNew::TearDown()
{
    const char *namespace1 = "yangUpAddNew";
    TryDropNameSpace(g_stmt_async, namespace1);

    // 释放all stmt
    TestYangFreeAllstmt();

    int ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

const char *g_namespace1 = "yangUpAddNew";



/*****************************************************************************
 * Description  : 001.yang表，新增leaf在container、choice-case、list尾部，预期成功
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangUpAddNew, Yang_116_yangUpAddNew_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    CreateVertexAndEditData();

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    EXPECT_TRUE(ret);

   AW_FUN_Log(LOG_INFO, ">>>>>> yang update.\n\n");

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("addNew01");

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查启动日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "RecoveryPath", "116_YangUpdate/update/addNew01");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "creating vertexLabel of file", "create_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "creating edgeLabel of file", "create_edge_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "upgrading vertexLabel of file", "update_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpAddNew_001_Model");
    EXPECT_TRUE(ret);

    // 再次编辑并查询diff、subtree
    EditDataAndCheck();

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F25");

    // 对 container 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH,
        &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F8");

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F0");
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F8");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
   for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);

        ret = GmcSetIndexKeyValue(g_stmt_sync_T1List, 1, GMC_DATATYPE_STRING, &fieldStr, strlen(fieldStr));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_T1List, g_keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F8");

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MAX_ELEMENTS;
    dataLef.expectedErrMsg = "violated max-elements clause";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffUpFile/diffAddNew_001.json", NULL);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpAddNew_001");

    // 提交事务
    TransCommit(g_conn_async);

    // 删除配置项yangUpgradeDirPath
    system("sh delCfgItem.sh \"yangUpgradeDirPath=\"");

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F25");

    // 对 container 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH,
        &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F8");

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F0");
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F8");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
   for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);

        ret = GmcSetIndexKeyValue(g_stmt_sync_T1List, 1, GMC_DATATYPE_STRING, &fieldStr, strlen(fieldStr));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_T1List, g_keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F8");

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    memset(&dataLef, 0, sizeof(dataLef));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MAX_ELEMENTS;
    dataLef.expectedErrMsg = "violated max-elements clause";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne";

    checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    memset(&data, 0, sizeof(AsyncUserDataT));
    SetDiffFile("diffUpFile/diffAddNew_001_2.json", NULL);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpAddNew_001_2");

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 002.yang表，新增leaf在root的非尾部，预期失败  -->  规格修改，预期改为成功
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangUpAddNew, Yang_116_yangUpAddNew_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    CreateVertexAndEditData();

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    EXPECT_TRUE(ret);

   AW_FUN_Log(LOG_INFO, ">>>>>> yang update.\n\n");

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("addNew02");

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查启动日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "RecoveryPath", "116_YangUpdate/update/addNew02");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "creating vertexLabel of file", "create_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "creating edgeLabel of file", "create_edge_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "upgrading vertexLabel of file", "update_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);

    // 删除配置项yangUpgradeDirPath
    system("sh delCfgItem.sh \"yangUpgradeDirPath=\"");

    // 重启
    ret = restartMyDb(0, GMERR_OK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    EXPECT_TRUE(ret);

    // 再次编辑并查询diff、subtree
    EditDataAndCheck();

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 003.yang表，新增leaf在contain->containe的非尾部，预期失败  -->  规格修改，预期改为成功
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangUpAddNew, Yang_116_yangUpAddNew_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    CreateVertexAndEditData();

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    EXPECT_TRUE(ret);

   AW_FUN_Log(LOG_INFO, ">>>>>> yang update.\n\n");

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("addNew03");

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查启动日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "RecoveryPath", "116_YangUpdate/update/addNew03");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "creating vertexLabel of file", "create_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "creating edgeLabel of file", "create_edge_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "upgrading vertexLabel of file", "update_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);

    // 删除配置项yangUpgradeDirPath
    system("sh delCfgItem.sh \"yangUpgradeDirPath=\"");

    // 重启
    ret = restartMyDb(0, GMERR_OK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    EXPECT_TRUE(ret);

    // 再次编辑并查询diff、subtree
    EditDataAndCheck();

    AW_FUN_Log(LOG_STEP, "END");
}
 
/*****************************************************************************
 * Description  : 004.yang表，新增leaf在case->containe的非尾部，预期失败  -->  规格修改，预期改为成功
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangUpAddNew, Yang_116_yangUpAddNew_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    CreateVertexAndEditData();

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    EXPECT_TRUE(ret);

   AW_FUN_Log(LOG_INFO, ">>>>>> yang update.\n\n");

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("addNew04");

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查启动日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "RecoveryPath", "116_YangUpdate/update/addNew04");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "creating vertexLabel of file", "create_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "creating edgeLabel of file", "create_edge_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "upgrading vertexLabel of file", "update_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);

    // 删除配置项yangUpgradeDirPath
    system("sh delCfgItem.sh \"yangUpgradeDirPath=\"");

    // 重启
    ret = restartMyDb(0, GMERR_OK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    EXPECT_TRUE(ret);

    // 再次编辑并查询diff、subtree
    EditDataAndCheck();

    AW_FUN_Log(LOG_STEP, "END");
}
 


/*****************************************************************************
 * Description  : 005.yang表，新增leaf在list->containe的非尾部，预期失败  -->  规格修改，预期改为成功
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangUpAddNew, Yang_116_yangUpAddNew_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    CreateVertexAndEditData();

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    EXPECT_TRUE(ret);

   AW_FUN_Log(LOG_INFO, ">>>>>> yang update.\n\n");

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("addNew05");

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查启动日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "RecoveryPath", "116_YangUpdate/update/addNew05");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "creating vertexLabel of file", "create_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "creating edgeLabel of file", "create_edge_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "upgrading vertexLabel of file", "update_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);

    // 删除配置项yangUpgradeDirPath
    system("sh delCfgItem.sh \"yangUpgradeDirPath=\"");

    // 重启
    ret = restartMyDb(0, GMERR_OK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    EXPECT_TRUE(ret);

    // 再次编辑并查询diff、subtree
    EditDataAndCheck();

    AW_FUN_Log(LOG_STEP, "END");
}
 

/*****************************************************************************
 * Description  : 006.yang表，新增leaf在choice-case的非尾部，预期失败  -->  规格修改，预期改为成功
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangUpAddNew, Yang_116_yangUpAddNew_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    CreateVertexAndEditData();

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    EXPECT_TRUE(ret);

   AW_FUN_Log(LOG_INFO, ">>>>>> yang update.\n\n");

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("addNew06");

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查启动日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "RecoveryPath", "116_YangUpdate/update/addNew06");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "creating vertexLabel of file", "create_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "creating edgeLabel of file", "create_edge_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "upgrading vertexLabel of file", "update_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);

    // 删除配置项yangUpgradeDirPath
    system("sh delCfgItem.sh \"yangUpgradeDirPath=\"");

    // 重启
    ret = restartMyDb(0, GMERR_OK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    EXPECT_TRUE(ret);

    // 再次编辑并查询diff、subtree
    EditDataAndCheck();

    AW_FUN_Log(LOG_STEP, "END");
}
 

/*****************************************************************************
 * Description  : 007.yang表，新增leaf在list->choice-case的非尾部，预期失败  -->  规格修改，预期改为成功
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangUpAddNew, Yang_116_yangUpAddNew_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    CreateVertexAndEditData();

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    EXPECT_TRUE(ret);

   AW_FUN_Log(LOG_INFO, ">>>>>> yang update.\n\n");

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("addNew07");

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查启动日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "RecoveryPath", "116_YangUpdate/update/addNew07");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "creating vertexLabel of file", "create_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "creating edgeLabel of file", "create_edge_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "upgrading vertexLabel of file", "update_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);

    // 删除配置项yangUpgradeDirPath
    system("sh delCfgItem.sh \"yangUpgradeDirPath=\"");

    // 重启
    ret = restartMyDb(0, GMERR_OK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    EXPECT_TRUE(ret);

    // 再次编辑并查询diff、subtree
    EditDataAndCheck();

    AW_FUN_Log(LOG_STEP, "END");
}
 

/*****************************************************************************
 * Description  : 008.yang表，新增leaf在list的非尾部，预期失败  -->  规格修改，预期改为成功
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangUpAddNew, Yang_116_yangUpAddNew_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    CreateVertexAndEditData();

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    EXPECT_TRUE(ret);

   AW_FUN_Log(LOG_INFO, ">>>>>> yang update.\n\n");

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("addNew08");

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查启动日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "RecoveryPath", "116_YangUpdate/update/addNew08");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "creating vertexLabel of file", "create_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "creating edgeLabel of file", "create_edge_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "upgrading vertexLabel of file", "update_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);

    // 删除配置项yangUpgradeDirPath
    system("sh delCfgItem.sh \"yangUpgradeDirPath=\"");

    // 重启
    ret = restartMyDb(0, GMERR_OK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    EXPECT_TRUE(ret);

    // 再次编辑并查询diff、subtree
    EditDataAndCheck();

    AW_FUN_Log(LOG_STEP, "END");
}
 
/*****************************************************************************
 * Description  : 009.yang表，新增leaf在leaf-list的非尾部，预期建表失败
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangUpAddNew, Yang_116_yangUpAddNew_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    CreateVertexAndEditData();

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    EXPECT_TRUE(ret);

   AW_FUN_Log(LOG_INFO, ">>>>>> yang update.\n\n");

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("addNew09");

    // 按需刷盘并重启
    ret = flushAndRestartDbFail();
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);

    // 检查启动日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "RecoveryPath", "116_YangUpdate/update/addNew09");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "upgrading vertexLabel of file", "update_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009003", "Inv table definition. leaflist's prope num must be three");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009003",
        "Inv table definition. parse schema of vertex label LeafList.");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);

    // 删除配置项yangUpgradeDirPath
    system("sh delCfgItem.sh \"yangUpgradeDirPath=\"");

    // 重启
    ret = restartMyDb(0, GMERR_OK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    EXPECT_TRUE(ret);

    // 再次编辑并查询diff、subtree（表未升级成功）
    EditDataAndCheck();

    AW_FUN_Log(LOG_STEP, "END");
}
 
/*****************************************************************************
 * Description  : 010.yang表，新增leaf在leaf-list的尾部，预期建表失败
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangUpAddNew, Yang_116_yangUpAddNew_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    CreateVertexAndEditData();

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    EXPECT_TRUE(ret);

   AW_FUN_Log(LOG_INFO, ">>>>>> yang update.\n\n");

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("addNew10");

    // 按需刷盘并重启
    ret = flushAndRestartDbFail();
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);

    // 检查启动日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "RecoveryPath", "116_YangUpdate/update/addNew10");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "upgrading vertexLabel of file", "update_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009003", "Inv table definition. leaflist's prope num must be three");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "GMERR-1009003",
        "Inv table definition. parse schema of vertex label LeafList.");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);

    // 删除配置项yangUpgradeDirPath
    system("sh delCfgItem.sh \"yangUpgradeDirPath=\"");

    // 重启
    ret = restartMyDb(0, GMERR_OK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    EXPECT_TRUE(ret);

    // 再次编辑并查询diff、subtree（表未升级成功）
    EditDataAndCheck();

    AW_FUN_Log(LOG_STEP, "END");
}
 

/*****************************************************************************
 * Description  : 011.yang表，新增contain在container、choice-case、list尾部，预期成功
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangUpAddNew, Yang_116_yangUpAddNew_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    CreateVertexAndEditData();

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    EXPECT_TRUE(ret);

   AW_FUN_Log(LOG_INFO, ">>>>>> yang update.\n\n");

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("addNew11");

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查启动日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "RecoveryPath", "116_YangUpdate/update/addNew11");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "creating vertexLabel of file", "create_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "creating edgeLabel of file", "create_edge_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "upgrading vertexLabel of file", "update_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpAddNew_011_Model");
    EXPECT_TRUE(ret);

    // 再次编辑并查询diff、subtree
    EditDataAndCheck();

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F25");

    // 对 container 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH,
        &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F8");

    int32_t valueInt32 = 100;
    GmcNodeT *tContainerAdd = NULL;
    ret = GmcYangEditChildNode(g_containerT2Node, "ContainerTwoAdd", GMC_OPERATION_REPLACE_GRAPH, &tContainerAdd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexProperty(tContainerAdd, valueInt32, true, 1, true, 1, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F0");
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F8");

    GmcNodeT *tCaseOneAdd = NULL;
    ret = GmcYangEditChildNode(g_choiceCaseNode, "CaseOneAdd", GMC_OPERATION_REPLACE_GRAPH, &tCaseOneAdd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexProperty(tCaseOneAdd, valueInt32, true, 1, true, 1, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
   for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);

        ret = GmcSetIndexKeyValue(g_stmt_sync_T1List, 1, GMC_DATATYPE_STRING, &fieldStr, strlen(fieldStr));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_T1List, g_keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        GmcNodeT *tListContainAdd = NULL;
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, "ContainerAdd", GMC_OPERATION_REPLACE_GRAPH, &tListContainAdd);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetVertexProperty(tListContainAdd, valueInt32, true, 1, true, 1, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MAX_ELEMENTS;
    dataLef.expectedErrMsg = "violated max-elements clause";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffUpFile/diffAddNew_011.json", NULL);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpAddNew_011");

    // 提交事务
    TransCommit(g_conn_async);

    // 删除配置项yangUpgradeDirPath
    system("sh delCfgItem.sh \"yangUpgradeDirPath=\"");

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F25");

    // 对 container 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH,
        &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F8");

    valueInt32 = 101;
    tContainerAdd = NULL;
    ret = GmcYangEditChildNode(g_containerT2Node, "ContainerTwoAdd", GMC_OPERATION_REPLACE_GRAPH, &tContainerAdd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexProperty(tContainerAdd, valueInt32, true, 1, true, 1, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F0");
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F8");

    tCaseOneAdd = NULL;
    ret = GmcYangEditChildNode(g_choiceCaseNode, "CaseOneAdd", GMC_OPERATION_REPLACE_GRAPH, &tCaseOneAdd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexProperty(tCaseOneAdd, valueInt32, true, 1, true, 1, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
   for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);

        ret = GmcSetIndexKeyValue(g_stmt_sync_T1List, 1, GMC_DATATYPE_STRING, &fieldStr, strlen(fieldStr));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_T1List, g_keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        valueInt32 = 200;
        GmcNodeT *tListContainAdd = NULL;
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, "ContainerAdd", GMC_OPERATION_REPLACE_GRAPH, &tListContainAdd);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetVertexProperty(tListContainAdd, valueInt32, true, 1, true, 1, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    memset(&dataLef, 0, sizeof(dataLef));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MAX_ELEMENTS;
    dataLef.expectedErrMsg = "violated max-elements clause";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne";

    checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    memset(&data, 0, sizeof(AsyncUserDataT));
    SetDiffFile("diffUpFile/diffAddNew_011_2.json", NULL);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpAddNew_011_2");

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 012.yang表，新增contain在root的非尾部，预期失败  -->  规格修改，预期改为成功
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangUpAddNew, Yang_116_yangUpAddNew_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    CreateVertexAndEditData();

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    EXPECT_TRUE(ret);

   AW_FUN_Log(LOG_INFO, ">>>>>> yang update.\n\n");

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("addNew12");

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查启动日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "RecoveryPath", "116_YangUpdate/update/addNew12");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "creating vertexLabel of file", "create_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "creating edgeLabel of file", "create_edge_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "upgrading vertexLabel of file", "update_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);

    // 删除配置项yangUpgradeDirPath
    system("sh delCfgItem.sh \"yangUpgradeDirPath=\"");

    // 重启
    ret = restartMyDb(0, GMERR_OK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    EXPECT_TRUE(ret);

    // 再次编辑并查询diff、subtree
    EditDataAndCheck();

    AW_FUN_Log(LOG_STEP, "END");
}
 
/*****************************************************************************
 * Description  : 013.yang表，新增contain在contain->containe的非尾部，预期失败  -->  规格修改，预期改为成功
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangUpAddNew, Yang_116_yangUpAddNew_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    CreateVertexAndEditData();

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    EXPECT_TRUE(ret);

   AW_FUN_Log(LOG_INFO, ">>>>>> yang update.\n\n");

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("addNew13");

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查启动日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "RecoveryPath", "116_YangUpdate/update/addNew13");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "creating vertexLabel of file", "create_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "creating edgeLabel of file", "create_edge_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "upgrading vertexLabel of file", "update_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);

    // 删除配置项yangUpgradeDirPath
    system("sh delCfgItem.sh \"yangUpgradeDirPath=\"");

    // 重启
    ret = restartMyDb(0, GMERR_OK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    EXPECT_TRUE(ret);

    // 再次编辑并查询diff、subtree
    EditDataAndCheck();

    AW_FUN_Log(LOG_STEP, "END");
}
 
/*****************************************************************************
 * Description  : 014.yang表，新增contain在choice-case非尾部，预期失败  -->  规格修改，预期改为成功
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangUpAddNew, Yang_116_yangUpAddNew_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    CreateVertexAndEditData();

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    EXPECT_TRUE(ret);

   AW_FUN_Log(LOG_INFO, ">>>>>> yang update.\n\n");

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("addNew14");

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查启动日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "RecoveryPath", "116_YangUpdate/update/addNew14");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "creating vertexLabel of file", "create_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "creating edgeLabel of file", "create_edge_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "upgrading vertexLabel of file", "update_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);

    // 删除配置项yangUpgradeDirPath
    system("sh delCfgItem.sh \"yangUpgradeDirPath=\"");

    // 重启
    ret = restartMyDb(0, GMERR_OK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    EXPECT_TRUE(ret);

    // 再次编辑并查询diff、subtree
    EditDataAndCheck();

    AW_FUN_Log(LOG_STEP, "END");
}
 
/*****************************************************************************
 * Description  : 015.yang表，新增contain在list的非尾部，预期失败  -->  规格修改，预期改为成功
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangUpAddNew, Yang_116_yangUpAddNew_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    CreateVertexAndEditData();

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    EXPECT_TRUE(ret);

   AW_FUN_Log(LOG_INFO, ">>>>>> yang update.\n\n");

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("addNew15");

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查启动日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "RecoveryPath", "116_YangUpdate/update/addNew15");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "creating vertexLabel of file", "create_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "creating edgeLabel of file", "create_edge_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "upgrading vertexLabel of file", "update_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);

    // 删除配置项yangUpgradeDirPath
    system("sh delCfgItem.sh \"yangUpgradeDirPath=\"");

    // 重启
    ret = restartMyDb(0, GMERR_OK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    EXPECT_TRUE(ret);

    // 再次编辑并查询diff、subtree
    EditDataAndCheck();

    AW_FUN_Log(LOG_STEP, "END");
}
 
/*****************************************************************************
 * Description  : 016.yang表，新增choice在container、choice-case、list尾部，预期成功
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangUpAddNew, Yang_116_yangUpAddNew_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    CreateVertexAndEditData();

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    EXPECT_TRUE(ret);

   AW_FUN_Log(LOG_INFO, ">>>>>> yang update.\n\n");

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("addNew16");

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查启动日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "RecoveryPath", "116_YangUpdate/update/addNew16");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "creating vertexLabel of file", "create_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "creating edgeLabel of file", "create_edge_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "upgrading vertexLabel of file", "update_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpAddNew_016_Model");
    EXPECT_TRUE(ret);

    // 再次编辑并查询diff、subtree
    EditDataAndCheck();

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F25");

    // 对 container 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH,
        &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F8");

    int32_t valueInt32 = 100;
    GmcNodeT *tContainerAdd = NULL;
    ret = GmcYangEditChildNode(g_containerT2Node, "ContainerTwoAdd", GMC_OPERATION_REPLACE_GRAPH, &tContainerAdd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexProperty(tContainerAdd, valueInt32, true, 1, true, 1, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 choice case 子节点做replace操作  ChoiceAdd
    GmcNodeT *tChoiceAdd = NULL;
    GmcNodeT *tChoiceAddCase = NULL;
    ret = GmcYangEditChildNode(g_containerT2Node, "ChoiceAdd", GMC_OPERATION_REPLACE_GRAPH, &tChoiceAdd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(tChoiceAdd, "CaseOne", GMC_OPERATION_REPLACE_GRAPH, &tChoiceAddCase);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexProperty_Fx(tChoiceAddCase, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F0");

    // 对 choice case 子节点做replace操作  Choice
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F0");
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F8");

    GmcNodeT *tCaseOneAdd = NULL;
    ret = GmcYangEditChildNode(g_choiceCaseNode, "CaseOneAdd", GMC_OPERATION_REPLACE_GRAPH, &tCaseOneAdd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexProperty(tCaseOneAdd, valueInt32, true, 1, true, 1, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
   for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);

        ret = GmcSetIndexKeyValue(g_stmt_sync_T1List, 1, GMC_DATATYPE_STRING, &fieldStr, strlen(fieldStr));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_T1List, g_keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        GmcNodeT *tListContainAdd = NULL;
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, "ContainerAdd", GMC_OPERATION_REPLACE_GRAPH, &tListContainAdd);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetVertexProperty(tListContainAdd, valueInt32, true, 1, true, 1, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对 choice case 子节点做replace操作  ChoiceAdd
        GmcNodeT *tListChoiceAdd = NULL;
        GmcNodeT *tListChoiceAddCase = NULL;
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, "ChoiceAdd", GMC_OPERATION_REPLACE_GRAPH, &tListChoiceAdd);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(tListChoiceAdd, "CaseOne", GMC_OPERATION_REPLACE_GRAPH, &tListChoiceAddCase);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetVertexProperty_Fx(tListChoiceAddCase, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F0");

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MAX_ELEMENTS;
    dataLef.expectedErrMsg = "violated max-elements clause";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffUpFile/diffAddNew_016.json", NULL);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpAddNew_016");

    // 提交事务
    TransCommit(g_conn_async);

    // 删除配置项yangUpgradeDirPath
    system("sh delCfgItem.sh \"yangUpgradeDirPath=\"");

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F25");

    // 对 container 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH,
        &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F8");

    valueInt32 = 101;
    tContainerAdd = NULL;
    ret = GmcYangEditChildNode(g_containerT2Node, "ContainerTwoAdd", GMC_OPERATION_REPLACE_GRAPH, &tContainerAdd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexProperty(tContainerAdd, valueInt32, true, 1, true, 1, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 choice case 子节点做replace操作  ChoiceAdd
    tChoiceAdd = NULL;
    tChoiceAddCase = NULL;
    ret = GmcYangEditChildNode(g_containerT2Node, "ChoiceAdd", GMC_OPERATION_REPLACE_GRAPH, &tChoiceAdd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(tChoiceAdd, "CaseOne", GMC_OPERATION_REPLACE_GRAPH, &tChoiceAddCase);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexProperty_Fx(tChoiceAddCase, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F0");

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F0");
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F8");

    tCaseOneAdd = NULL;
    ret = GmcYangEditChildNode(g_choiceCaseNode, "CaseOneAdd", GMC_OPERATION_REPLACE_GRAPH, &tCaseOneAdd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexProperty(tCaseOneAdd, valueInt32, true, 1, true, 1, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
   for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);

        ret = GmcSetIndexKeyValue(g_stmt_sync_T1List, 1, GMC_DATATYPE_STRING, &fieldStr, strlen(fieldStr));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_T1List, g_keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        valueInt32 = 200;
        GmcNodeT *tListContainAdd = NULL;
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, "ContainerAdd", GMC_OPERATION_REPLACE_GRAPH, &tListContainAdd);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetVertexProperty(tListContainAdd, valueInt32, true, 1, true, 1, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        fieldValue = 200;
        // 对 choice case 子节点做replace操作  ChoiceAdd
        GmcNodeT *tListChoiceAdd = NULL;
        GmcNodeT *tListChoiceAddCase = NULL;
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, "ChoiceAdd", GMC_OPERATION_REPLACE_GRAPH, &tListChoiceAdd);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(tListChoiceAdd, "CaseOne", GMC_OPERATION_REPLACE_GRAPH, &tListChoiceAddCase);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetVertexProperty_Fx(tListChoiceAddCase, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F0");

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    memset(&dataLef, 0, sizeof(dataLef));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MAX_ELEMENTS;
    dataLef.expectedErrMsg = "violated max-elements clause";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne";

    checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    memset(&data, 0, sizeof(AsyncUserDataT));
    SetDiffFile("diffUpFile/diffAddNew_016_2.json", NULL);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpAddNew_016_2");

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 017.yang表，新增choice在root的非尾部，预期失败  -->  规格修改，预期改为成功
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangUpAddNew, Yang_116_yangUpAddNew_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    CreateVertexAndEditData();

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    EXPECT_TRUE(ret);

   AW_FUN_Log(LOG_INFO, ">>>>>> yang update.\n\n");

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("addNew17");

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查启动日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "RecoveryPath", "116_YangUpdate/update/addNew17");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "creating vertexLabel of file", "create_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "creating edgeLabel of file", "create_edge_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "upgrading vertexLabel of file", "update_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);

    // 删除配置项yangUpgradeDirPath
    system("sh delCfgItem.sh \"yangUpgradeDirPath=\"");

    // 重启
    ret = restartMyDb(0, GMERR_OK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    EXPECT_TRUE(ret);

    // 再次编辑并查询diff、subtree
    EditDataAndCheck();

    AW_FUN_Log(LOG_STEP, "END");
}
 
/*****************************************************************************
 * Description  : 018.yang表，新增choice在contain->containe的非尾部，预期失败  -->  规格修改，预期改为成功
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangUpAddNew, Yang_116_yangUpAddNew_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    CreateVertexAndEditData();

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    EXPECT_TRUE(ret);

   AW_FUN_Log(LOG_INFO, ">>>>>> yang update.\n\n");

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("addNew18");

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查启动日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "RecoveryPath", "116_YangUpdate/update/addNew18");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "creating vertexLabel of file", "create_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "creating edgeLabel of file", "create_edge_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "upgrading vertexLabel of file", "update_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);

    // 删除配置项yangUpgradeDirPath
    system("sh delCfgItem.sh \"yangUpgradeDirPath=\"");

    // 重启
    ret = restartMyDb(0, GMERR_OK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    EXPECT_TRUE(ret);

    // 再次编辑并查询diff、subtree
    EditDataAndCheck();

    AW_FUN_Log(LOG_STEP, "END");
}
 
/*****************************************************************************
 * Description  : 019.yang表，新增choice在choice-case非尾部，预期失败  -->  规格修改，预期改为成功
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangUpAddNew, Yang_116_yangUpAddNew_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    CreateVertexAndEditData();

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    EXPECT_TRUE(ret);

   AW_FUN_Log(LOG_INFO, ">>>>>> yang update.\n\n");

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("addNew19");

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查启动日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "RecoveryPath", "116_YangUpdate/update/addNew19");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "creating vertexLabel of file", "create_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "creating edgeLabel of file", "create_edge_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "upgrading vertexLabel of file", "update_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    // 删除配置项yangUpgradeDirPath
    system("sh delCfgItem.sh \"yangUpgradeDirPath=\"");

    // 重启
    ret = restartMyDb(0, GMERR_OK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    EXPECT_TRUE(ret);

    // 再次编辑并查询diff、subtree
    EditDataAndCheck();

    AW_FUN_Log(LOG_STEP, "END");
}
 
/*****************************************************************************
 * Description  : 020.yang表，新增choice在list的非尾部，预期失败  -->  规格修改，预期改为成功
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangUpAddNew, Yang_116_yangUpAddNew_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    CreateVertexAndEditData();

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    EXPECT_TRUE(ret);

   AW_FUN_Log(LOG_INFO, ">>>>>> yang update.\n\n");

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("addNew20");

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查启动日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "RecoveryPath", "116_YangUpdate/update/addNew20");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "creating vertexLabel of file", "create_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "creating edgeLabel of file", "create_edge_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "upgrading vertexLabel of file", "update_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);

    // 删除配置项yangUpgradeDirPath
    system("sh delCfgItem.sh \"yangUpgradeDirPath=\"");

    // 重启
    ret = restartMyDb(0, GMERR_OK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    EXPECT_TRUE(ret);

    // 再次编辑并查询diff、subtree
    EditDataAndCheck();

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 021.yang表，新增case在choice、list->choice尾部，预期成功
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification :  checked
 * *****************************************************************************/
TEST_F(yangUpAddNew, Yang_116_yangUpAddNew_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    CreateVertexAndEditData();

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    EXPECT_TRUE(ret);

   AW_FUN_Log(LOG_INFO, ">>>>>> yang update.\n\n");

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("addNew21");

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查启动日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "RecoveryPath", "116_YangUpdate/update/addNew21");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "creating vertexLabel of file", "create_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "creating edgeLabel of file", "create_edge_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "upgrading vertexLabel of file", "update_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpAddNew_021_Model");
    EXPECT_TRUE(ret);

    // 再次编辑并查询diff、subtree
    EditDataAndCheck();

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F25");

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, "CaseAdd", GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F0");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
   for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);

        ret = GmcSetIndexKeyValue(g_stmt_sync_T1List, 1, GMC_DATATYPE_STRING, &fieldStr, strlen(fieldStr));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_T1List, g_keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 ListchoiceCase 子节点做replace操作
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, "CaseAdd", GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_Fx(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F0");

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MAX_ELEMENTS;
    dataLef.expectedErrMsg = "violated max-elements clause";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffUpFile/diffAddNew_021.json", NULL);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpAddNew_021");

    // 提交事务
    TransCommit(g_conn_async);

    // 删除配置项yangUpgradeDirPath
    system("sh delCfgItem.sh \"yangUpgradeDirPath=\"");

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 200;

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, "CaseAdd", GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F0");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
   for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);

        ret = GmcSetIndexKeyValue(g_stmt_sync_T1List, 1, GMC_DATATYPE_STRING, &fieldStr, strlen(fieldStr));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_T1List, g_keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 ListchoiceCase 子节点做replace操作
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, "CaseAdd", GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        fieldValue = 200;
        testYangSetVertexProperty_Fx(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F0");

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    memset(&dataLef, 0, sizeof(dataLef));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MAX_ELEMENTS;
    dataLef.expectedErrMsg = "violated max-elements clause";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne";

    checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    memset(&data, 0, sizeof(AsyncUserDataT));
    SetDiffFile("diffUpFile/diffAddNew_021_2.json", NULL);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpAddNew_021_2");

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 022.yang表，新增case在choice非尾部，预期失败  -->  规格修改，预期改为成功
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification :  checked
 * *****************************************************************************/
TEST_F(yangUpAddNew, Yang_116_yangUpAddNew_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    CreateVertexAndEditData();

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    EXPECT_TRUE(ret);

   AW_FUN_Log(LOG_INFO, ">>>>>> yang update.\n\n");

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("addNew22");

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查启动日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "RecoveryPath", "116_YangUpdate/update/addNew22");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "creating vertexLabel of file", "create_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "creating edgeLabel of file", "create_edge_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "upgrading vertexLabel of file", "update_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);

    // 删除配置项yangUpgradeDirPath
    system("sh delCfgItem.sh \"yangUpgradeDirPath=\"");

    // 重启
    ret = restartMyDb(0, GMERR_OK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    EXPECT_TRUE(ret);

    // 再次编辑并查询diff、subtree
    EditDataAndCheck();

    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 023.yang表，新增case在list->choice非尾部，预期失败  -->  规格修改，预期改为成功
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification :  checked
 * *****************************************************************************/
TEST_F(yangUpAddNew, Yang_116_yangUpAddNew_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    CreateVertexAndEditData();

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    EXPECT_TRUE(ret);

   AW_FUN_Log(LOG_INFO, ">>>>>> yang update.\n\n");

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("addNew23");

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查启动日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "RecoveryPath", "116_YangUpdate/update/addNew23");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "creating vertexLabel of file", "create_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "creating edgeLabel of file", "create_edge_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "upgrading vertexLabel of file", "update_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);

    // 删除配置项yangUpgradeDirPath
    system("sh delCfgItem.sh \"yangUpgradeDirPath=\"");

    // 重启
    ret = restartMyDb(0, GMERR_OK);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    EXPECT_TRUE(ret);

    // 再次编辑并查询diff、subtree
    EditDataAndCheck();

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 024.yang表，新增多个list在root contain，list下，预期成功
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 11
 * *****************************************************************************/
TEST_F(yangUpAddNew, Yang_116_yangUpAddNew_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    CreateVertexAndEditData();

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    EXPECT_TRUE(ret);

   AW_FUN_Log(LOG_INFO, ">>>>>> yang update.\n\n");

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("addNew24");

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查启动日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "RecoveryPath", "116_YangUpdate/update/addNew24");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "creating vertexLabel of file", "create_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "creating edgeLabel of file", "create_edge_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "upgrading vertexLabel of file", "update_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpAddNew_024_Model");
    EXPECT_TRUE(ret);

    // 再次编辑并查询diff、subtree
    EditDataAndCheck();

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F25");

    // 对 container 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH,
        &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F8");

    int32_t valueInt32 = 100;
    GmcNodeT *tContainerAdd = NULL;
    ret = GmcYangEditChildNode(g_containerT2Node, "ContainerTwoAdd", GMC_OPERATION_REPLACE_GRAPH, &tContainerAdd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexProperty(tContainerAdd, valueInt32, true, 1, true, 1, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 choice case 子节点做replace操作  ChoiceAdd
    GmcNodeT *tChoiceAdd = NULL;
    GmcNodeT *tChoiceAddCase = NULL;
    ret = GmcYangEditChildNode(g_containerT2Node, "ChoiceAdd", GMC_OPERATION_REPLACE_GRAPH, &tChoiceAdd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(tChoiceAdd, "CaseOne", GMC_OPERATION_REPLACE_GRAPH, &tChoiceAddCase);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexProperty_Fx(tChoiceAddCase, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F0");

    // 对 choice case 子节点做replace操作  Choice
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F0");
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F8");

    GmcNodeT *tCaseOneAdd = NULL;
    ret = GmcYangEditChildNode(g_choiceCaseNode, "CaseOneAdd", GMC_OPERATION_REPLACE_GRAPH, &tCaseOneAdd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexProperty(tCaseOneAdd, valueInt32, true, 1, true, 1, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname ListSix
   for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, "ListSix", GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListSix
        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F0");

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

   // 这里需要prepar list的labelname
   for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);

        ret = GmcSetIndexKeyValue(g_stmt_sync_T1List, 1, GMC_DATATYPE_STRING, &fieldStr, strlen(fieldStr));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_T1List, g_keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        GmcNodeT *tListContainAdd = NULL;
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, "ContainerAdd", GMC_OPERATION_REPLACE_GRAPH, &tListContainAdd);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetVertexProperty(tListContainAdd, valueInt32, true, 1, true, 1, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对 choice case 子节点做replace操作  ChoiceAdd
        GmcNodeT *tListChoiceAdd = NULL;
        GmcNodeT *tListChoiceAddCase = NULL;
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, "ChoiceAdd", GMC_OPERATION_REPLACE_GRAPH, &tListChoiceAdd);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(tListChoiceAdd, "CaseOne", GMC_OPERATION_REPLACE_GRAPH, &tListChoiceAddCase);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetVertexProperty_Fx(tListChoiceAddCase, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F0");

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        // 这里需要prepar list的labelname ListEight
        for (int j = 0; j < 3; j++) {
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2List, "ListEight", GMC_OPERATION_REPLACE_GRAPH);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT1Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 对子节点字段 做replace操作  --ListEight
            fieldValue = j;
            testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F0");

            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            nDmlCnt++;
        }
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MAX_ELEMENTS;
    dataLef.expectedErrMsg = "violated max-elements clause";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffUpFile/diffAddNew_024.json", NULL);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpAddNew_024");

    // 提交事务
    TransCommit(g_conn_async);

    // 删除配置项yangUpgradeDirPath
    system("sh delCfgItem.sh \"yangUpgradeDirPath=\"");

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F25");

    // 对 container 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH,
        &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F8");

    valueInt32 = 101;
    tContainerAdd = NULL;
    ret = GmcYangEditChildNode(g_containerT2Node, "ContainerTwoAdd", GMC_OPERATION_REPLACE_GRAPH, &tContainerAdd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexProperty(tContainerAdd, valueInt32, true, 1, true, 1, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 choice case 子节点做replace操作  ChoiceAdd
    tChoiceAdd = NULL;
    tChoiceAddCase = NULL;
    ret = GmcYangEditChildNode(g_containerT2Node, "ChoiceAdd", GMC_OPERATION_REPLACE_GRAPH, &tChoiceAdd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(tChoiceAdd, "CaseOne", GMC_OPERATION_REPLACE_GRAPH, &tChoiceAddCase);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexProperty_Fx(tChoiceAddCase, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F0");

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F0");
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F8");

    tCaseOneAdd = NULL;
    ret = GmcYangEditChildNode(g_choiceCaseNode, "CaseOneAdd", GMC_OPERATION_REPLACE_GRAPH, &tCaseOneAdd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexProperty(tCaseOneAdd, valueInt32, true, 1, true, 1, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname ListFive
   for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, "ListFive", GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListFive
        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F0");

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

   // 这里需要prepar list的labelname
   for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);

        ret = GmcSetIndexKeyValue(g_stmt_sync_T1List, 1, GMC_DATATYPE_STRING, &fieldStr, strlen(fieldStr));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_T1List, g_keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        valueInt32 = 200;
        GmcNodeT *tListContainAdd = NULL;
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, "ContainerAdd", GMC_OPERATION_REPLACE_GRAPH, &tListContainAdd);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetVertexProperty(tListContainAdd, valueInt32, true, 1, true, 1, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        fieldValue = 200;
        // 对 choice case 子节点做replace操作  ChoiceAdd
        GmcNodeT *tListChoiceAdd = NULL;
        GmcNodeT *tListChoiceAddCase = NULL;
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, "ChoiceAdd", GMC_OPERATION_REPLACE_GRAPH, &tListChoiceAdd);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(tListChoiceAdd, "CaseOne", GMC_OPERATION_REPLACE_GRAPH, &tListChoiceAddCase);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetVertexProperty_Fx(tListChoiceAddCase, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F0");

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        // 这里需要prepar list的labelname ListNigh
        for (int j = 0; j < 3; j++) {
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2List, "ListNigh", GMC_OPERATION_REPLACE_GRAPH);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT1Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 对子节点字段 做replace操作  --ListEight
            fieldValue = j;
            testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F0");

            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            nDmlCnt++;
        }

    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    memset(&dataLef, 0, sizeof(dataLef));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MAX_ELEMENTS;
    dataLef.expectedErrMsg = "violated max-elements clause";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne";

    checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    memset(&data, 0, sizeof(AsyncUserDataT));
    SetDiffFile("diffUpFile/diffAddNew_024_2.json", NULL);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpAddNew_024_2");

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 025.yang表，新增多个leaf-list在root contain，list下，预期成功
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 16
 * *****************************************************************************/
TEST_F(yangUpAddNew, Yang_116_yangUpAddNew_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    CreateVertexAndEditData();

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpBase_Model");
    EXPECT_TRUE(ret);

   AW_FUN_Log(LOG_INFO, ">>>>>> yang update.\n\n");

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("addNew25");

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查启动日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "RecoveryPath", "116_YangUpdate/update/addNew25");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "creating vertexLabel of file", "create_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "creating edgeLabel of file", "create_edge_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);
    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "upgrading vertexLabel of file", "update_vertex_0.gmjson");
    AW_MACRO_ASSERT_EQ_BOOL(true, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpBase");
    TransCommit(g_conn_async);

    // check Mode json
    ret = CheckVertexModelJson(g_namespace1, "ContainerOne", "Yang_116_yangUpAddNew_025_Model");
    EXPECT_TRUE(ret);

    // 再次编辑并查询diff、subtree
    EditDataAndCheck();

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F25");

    // 对 container 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH,
        &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F8");

    int32_t valueInt32 = 100;
    GmcNodeT *tContainerAdd = NULL;
    ret = GmcYangEditChildNode(g_containerT2Node, "ContainerTwoAdd", GMC_OPERATION_REPLACE_GRAPH, &tContainerAdd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexProperty(tContainerAdd, valueInt32, true, 1, true, 1, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 choice case 子节点做replace操作  ChoiceAdd
    GmcNodeT *tChoiceAdd = NULL;
    GmcNodeT *tChoiceAddCase = NULL;
    ret = GmcYangEditChildNode(g_containerT2Node, "ChoiceAdd", GMC_OPERATION_REPLACE_GRAPH, &tChoiceAdd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(tChoiceAdd, "CaseOne", GMC_OPERATION_REPLACE_GRAPH, &tChoiceAddCase);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexProperty_Fx(tChoiceAddCase, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F0");

    // 对 choice case 子节点做replace操作  Choice
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F0");
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F8");

    GmcNodeT *tCaseOneAdd = NULL;
    ret = GmcYangEditChildNode(g_choiceCaseNode, "CaseOneAdd", GMC_OPERATION_REPLACE_GRAPH, &tCaseOneAdd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexProperty(tCaseOneAdd, valueInt32, true, 1, true, 1, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname LeafListFour
   for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, "LeafListFour", GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --LeafListFour
        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F0");

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

   // 这里需要prepar list的labelname
   for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);

        ret = GmcSetIndexKeyValue(g_stmt_sync_T1List, 1, GMC_DATATYPE_STRING, &fieldStr, strlen(fieldStr));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_T1List, g_keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        GmcNodeT *tListContainAdd = NULL;
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, "ContainerAdd", GMC_OPERATION_REPLACE_GRAPH, &tListContainAdd);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetVertexProperty(tListContainAdd, valueInt32, true, 1, true, 1, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对 choice case 子节点做replace操作  ChoiceAdd
        GmcNodeT *tListChoiceAdd = NULL;
        GmcNodeT *tListChoiceAddCase = NULL;
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, "ChoiceAdd", GMC_OPERATION_REPLACE_GRAPH, &tListChoiceAdd);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(tListChoiceAdd, "CaseOne", GMC_OPERATION_REPLACE_GRAPH, &tListChoiceAddCase);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetVertexProperty_Fx(tListChoiceAddCase, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F0");

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        // 这里需要prepar list的labelname LeafListNigh
        for (int j = 0; j < 3; j++) {
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2List, "LeafListNigh", GMC_OPERATION_REPLACE_GRAPH);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT1Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 对子节点字段 做replace操作  --LeafListNigh
            fieldValue = j;
            testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F0");

            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            nDmlCnt++;
        }
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MAX_ELEMENTS;
    dataLef.expectedErrMsg = "violated max-elements clause";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffUpFile/diffAddNew_025.json", NULL);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpAddNew_025");

    // 提交事务
    TransCommit(g_conn_async);

    // 删除配置项yangUpgradeDirPath
    system("sh delCfgItem.sh \"yangUpgradeDirPath=\"");

    // 按需刷盘并重启
    ret = flushAndRestartDb();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = UserTestNamespace(g_namespace1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F25");

    // 对 container 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH,
        &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F8");

    valueInt32 = 101;
    tContainerAdd = NULL;
    ret = GmcYangEditChildNode(g_containerT2Node, "ContainerTwoAdd", GMC_OPERATION_REPLACE_GRAPH, &tContainerAdd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexProperty(tContainerAdd, valueInt32, true, 1, true, 1, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 choice case 子节点做replace操作  ChoiceAdd
    tChoiceAdd = NULL;
    tChoiceAddCase = NULL;
    ret = GmcYangEditChildNode(g_containerT2Node, "ChoiceAdd", GMC_OPERATION_REPLACE_GRAPH, &tChoiceAdd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(tChoiceAdd, "CaseOne", GMC_OPERATION_REPLACE_GRAPH, &tChoiceAddCase);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexProperty_Fx(tChoiceAddCase, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F0");

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F0");
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F8");

    tCaseOneAdd = NULL;
    ret = GmcYangEditChildNode(g_choiceCaseNode, "CaseOneAdd", GMC_OPERATION_REPLACE_GRAPH, &tCaseOneAdd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexProperty(tCaseOneAdd, valueInt32, true, 1, true, 1, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname LeafListThree
   for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, "LeafListThree", GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --LeafListThree
        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F0");

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

   // 这里需要prepar list的labelname
   for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);

        ret = GmcSetIndexKeyValue(g_stmt_sync_T1List, 1, GMC_DATATYPE_STRING, &fieldStr, strlen(fieldStr));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_T1List, g_keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        valueInt32 = 200;
        GmcNodeT *tListContainAdd = NULL;
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, "ContainerAdd", GMC_OPERATION_REPLACE_GRAPH, &tListContainAdd);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetVertexProperty(tListContainAdd, valueInt32, true, 1, true, 1, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        fieldValue = 200;
        // 对 choice case 子节点做replace操作  ChoiceAdd
        GmcNodeT *tListChoiceAdd = NULL;
        GmcNodeT *tListChoiceAddCase = NULL;
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, "ChoiceAdd", GMC_OPERATION_REPLACE_GRAPH, &tListChoiceAdd);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(tListChoiceAdd, "CaseOne", GMC_OPERATION_REPLACE_GRAPH, &tListChoiceAddCase);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetVertexProperty_Fx(tListChoiceAddCase, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F0");

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        // 这里需要prepar list的labelname LeafListEight
        for (int j = 0; j < 3; j++) {
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2List, "LeafListEight", GMC_OPERATION_REPLACE_GRAPH);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT1Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 对子节点字段 做replace操作  --LeafListEight
            fieldValue = j;
            testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE, "F0");

            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            nDmlCnt++;
        }
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    memset(&dataLef, 0, sizeof(dataLef));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MAX_ELEMENTS;
    dataLef.expectedErrMsg = "violated max-elements clause";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne";

    checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    memset(&data, 0, sizeof(AsyncUserDataT));
    SetDiffFile("diffUpFile/diffAddNew_025_2.json", NULL);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_116_yangUpAddNew_025_2");

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

