/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#include "RCA_COMMON.h"
#include "../../../reliability/try.h"
#include "pstTool.h"
#include "updateTool.h"
#include "tools.h"

class expand_data_type : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void expand_data_type::SetUpTestCase()
{
    // 按需持久化，启动服务
    char dbFilePath[1024] = {0};
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        printf("get env PWD fail.\n");
    }
    (void)sprintf(dbFilePath, "%s/gmdb", pwdDir);
    (void)Rmdir(dbFilePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, mkdir(dbFilePath, S_IRUSR | S_IWUSR));
    
    int ret = ChangeGmserverCfg((char *)"recover", NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeGmserverCfg((char *)"dataFileDirPath", dbFilePath));
    ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"yangAutoIndex", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 配置 public 默认事务类型
    ret = ChangeGmserverCfg((char *)"defaultTransactionType", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"defaultIsolationLevel", (char *)"2");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void expand_data_type::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    ret = ChangeGmserverCfg((char *)"recover", NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void expand_data_type::SetUp()
{
    int ret = 0;
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    ASSERT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务

    const char *namespace1 = "yangUpAddNew";
    const char *namespaceUserName = "abc";

    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观
    TryDropNameSpace(g_stmt_async, namespace1);
    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void expand_data_type::TearDown()
{
    const char *namespace1 = "yangUpAddNew";
    TryDropNameSpace(g_stmt_async, namespace1);

    int ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

const char *g_namespace1 = "yangUpAddNew";
/*****************************************************************************
 * Description  : 001.union中含类型uint32，写数据，扩展为union含类型uint64
 * Input        : None
 * Output       : None
 * Author       : 李文海
 * Modification : 
 * *****************************************************************************/
TEST_F(expand_data_type, Yang_116_007_union_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmt_async, "schemaPstList/vertexUnionExpand1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheck();

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;
    
    GmcStmtT *stmtRoot = NULL;
    GmcNodeT *nodeRoot = NULL;
    ret = GmcAllocStmt(g_conn_async, &stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmtRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmtRoot, &nodeRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置root节点字段的值
    uint32_t rootF1Val = 100;
    ret = testYangSetField(nodeRoot, GMC_DATATYPE_UINT32, &rootF1Val, sizeof(uint32_t), "root_F1",
                           GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t unionVal = 1;
    ret = testYangSetField(nodeRoot, GMC_DATATYPE_UINT32, &unionVal, sizeof(uint32_t), "root_F2",
                           GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmtRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;
    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);
    // 提交事务
    TransCommit(g_conn_async);

    // 修改配置项 yangUpgradeDirPath
    ModifyUpdatePathCfg("addNewUnion1");

    // 按需刷盘并重启失败
    ret = flushAndRestartDbFail();
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
    // 检查日志
    ret = AW_CHECK_LOG_EXIST(SERVER, 1, "unspport alterType");
    AW_MACRO_EXPECT_EQ_BOOL(true, ret);
}
