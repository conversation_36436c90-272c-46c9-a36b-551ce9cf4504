/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#include "RCA_COMMON.h"
#include "../../../reliability/try.h"

class leafRefContain : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void leafRefContain::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/start.sh");
    int ret = testEnvInit();
    ASSERT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void leafRefContain::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}

void leafRefContain::SetUp()
{
    int ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务

    const char *namespace1 = "leafRefContain";
    const char *namespaceUserName = "abc";

    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观
    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcClearNamespaceAsync(g_stmt_async, namespace1, ClearNSCallbak, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // alloc all stmt
    TestYangAllocAllstmt();
}

void leafRefContain::TearDown()
{
    int ret = 0;
    const char *namespace1 = "leafRefContain";
    ret = GmcClearNamespaceAsync(g_stmt_async, namespace1, ClearNSCallbak, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
 
    ret = GmcDropNamespaceAsync(g_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 释放all stmt
    TestYangFreeAllstmt();

    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


void TestCheckValidateModelAsync(GmcStmtT *stmt)
{
    // 模型校验
    YangValidateUserDataT checkData = {0};
    int ret = GmcYangValidateModelAsync(stmt, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(true, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(0, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));
}

// *typedef void (*GmcYangValidateDoneT)(void *userData, GmcValidateResT validateRes, int32_t status, const char *errMsg);*/
void AsyncValidateLeafRefCb(void *userData, GmcValidateResT validateRes, int32_t status, const char *errMsg)
{
    if (userData) {
        YangValidateUserDataT *uData = (YangValidateUserDataT *)userData;
        uData->status = status;
        if ((status != GMERR_OK) && (errMsg != NULL)) {
            printf("YangValidate errMsg: %s\n", errMsg);
        }
        uData->validateRes = validateRes.validateRes;
        uData->failCount = validateRes.failCount;

        printf(">>> validateRes: %d\n", validateRes.validateRes);
        printf(">>> failCount: %u\n", validateRes.failCount);

        if (uData->isValidErrorPathInfo) {
            GmcErrorPathInfoT msg;
            ASSERT_EQ(GMERR_OK, GmcYangGetErrorPathInfo(&msg));

            // 结果检查
            printf("--- errcode: %d\n", msg.errorCode);
            printf("--- errorClauseIndex: %u\n", msg.errorClauseIndex);
            printf("--- errorMsg: %s\n", msg.errorMsg);
            printf("--- errorPath: %s\n", msg.errorPath);
            EXPECT_EQ(uData->expectedErrCode, msg.errorCode);
            EXPECT_EQ(uData->expectedErrClauseIndex, msg.errorClauseIndex);
            EXPECT_STREQ(uData->expectedErrMsg, msg.errorMsg);
            EXPECT_STREQ(uData->expectedErrPath, msg.errorPath);
            ASSERT_NO_FATAL_FAILURE(GmcYangFreeErrorPathInfo());
        }

        uData->recvNum++;
    }
}
// yang set T0层f0
void testYangSetVertexProperty_Fx(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE optype, const char *name)
{
    int ret = 0;
    uint32_t f0Value = i;
    ret = testYangSetField(node, GMC_DATATYPE_UINT32, &f0Value, sizeof(uint32_t), name, optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 * Description  : 001.container节点，leafref定义的字段父节点when校验失败，不进行leafref校验
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(leafRefContain, Yang_068_leafRefContain_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 container NP 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ContainerTwo 
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作  -- ContainerTwo 
    int32_t f1fieldValue = 100;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作  -- ContainerTwo 
    bool f4 = true;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container P 子节点做replace操作  -- ContainerThree 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ContainerThree 
    testYangSetVertexProperty_F0(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 -- ContainerThree 
    ret = testYangSetField(g_containerT3Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作 -- ContainerThree 
    ret = testYangSetField(g_containerT3Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // leafref校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_BUTT;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath = "";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns leafRefContain -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 002.container节点，leafref定义的字段when校验失败，不进行leafref校验
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(leafRefContain, Yang_068_leafRefContain_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel2.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 container NP 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ContainerTwo 
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作  -- ContainerTwo 
    int32_t f1fieldValue = 100;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作  -- ContainerTwo 
    bool f4 = true;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container P 子节点做replace操作  -- ContainerThree 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ContainerThree 
    testYangSetVertexProperty_F0(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 -- ContainerThree 
    ret = testYangSetField(g_containerT3Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作 -- ContainerThree 
    ret = testYangSetField(g_containerT3Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // leafref校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_BUTT;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath = "";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns leafRefContain -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 003.container节点，有leafref定义字段，有节点must校验失败，不进行leafref校验
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(leafRefContain, Yang_068_leafRefContain_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel3.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 container NP 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ContainerTwo 
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作  -- ContainerTwo 
    int32_t f1fieldValue = 100;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作  -- ContainerTwo 
    bool f4 = true;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container P 子节点做replace操作  -- ContainerThree 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ContainerThree 
    testYangSetVertexProperty_F0(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 -- ContainerThree 
    ret = testYangSetField(g_containerT3Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作 -- ContainerThree 
    ret = testYangSetField(g_containerT3Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // leafref校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MUST;
    dataLef.expectedErrMsg = "violated must clause 0";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = failRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns leafRefContain -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 004.container节点，leafref定义的字段，有字段must校验失败，不进行leafref校验
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(leafRefContain, Yang_068_leafRefContain_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel4.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 container NP 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ContainerTwo 
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作  -- ContainerTwo 
    int32_t f1fieldValue = 100;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作  -- ContainerTwo 
    bool f4 = true;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container P 子节点做replace操作  -- ContainerThree 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ContainerThree 
    testYangSetVertexProperty_F0(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 -- ContainerThree 
    ret = testYangSetField(g_containerT3Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作 -- ContainerThree 
    ret = testYangSetField(g_containerT3Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // leafref校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MUST;
    dataLef.expectedErrMsg = "violated must clause 0";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/F0";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = failRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);
    system("gmsysview subtree -ns leafRefContain -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}
// yang set T0层f0
void testYangSetVertexProperty_F13(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE optype)
{
    int ret = 0;
    uint32_t f0Value = i;
    ret = testYangSetField(node, GMC_DATATYPE_UINT32, &f0Value, sizeof(uint32_t), "F13", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// yang set T0层f0
void testYangSetVertexProperty_F1(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE optype)
{
    int ret = 0;
    uint32_t f0Value = i;
    ret = testYangSetField(node, GMC_DATATYPE_UINT32, &f0Value, sizeof(uint32_t), "F1", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 * Description  : 005.container节点，leafref定义的字段when, must校验成功，leafref校验成功
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(leafRefContain, Yang_068_leafRefContain_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel5.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    

    // 对 container P 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ContainerTwo 
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作  -- ContainerTwo 
    int32_t f1fieldValue = 100;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作  -- ContainerTwo 
    bool f4 = true;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container P 子节点做replace操作  -- ContainerThree 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ContainerThree 
    testYangSetVertexProperty_F0(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 -- ContainerThree 
    ret = testYangSetField(g_containerT3Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作 -- ContainerThree 
    ret = testYangSetField(g_containerT3Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // leafref校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MUST;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath = "";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns leafRefContain -rn ContainerOne -defaultMode REPORT_ALL");
    // 插入不合法数据
    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    fieldValue = 50;
    testYangSetVertexProperty_F13(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 container P 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ContainerTwo 
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作  -- ContainerTwo 
    f1fieldValue = 100;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // leafref校验
    isDataService = true;

    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/F0";

    checkRes = failRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);
    system("gmsysview subtree -ns leafRefContain -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 006.container节点，leafref定义的字段是P节点字段默认值不可见，不插入数据，不进行leafref校验
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(leafRefContain, Yang_068_leafRefContain_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel5.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // leafref校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MUST;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath = "";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns leafRefContain -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 007.container节点，leafref定义的字段是NP节点字段默认值可见，leafref校验成功
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(leafRefContain, Yang_068_leafRefContain_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel6.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // leafref校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/F0";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = failRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns leafRefContain -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 008.container节点，leafref被依赖的字段when校验失败，leafref校验报错
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(leafRefContain, Yang_068_leafRefContain_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel7.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 container P 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ContainerTwo 
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作  -- ContainerTwo 
    int32_t f1fieldValue = 100;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作  -- ContainerTwo 
    bool f4 = true;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // leafref校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/F0";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = failRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns leafRefContain -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}



/*****************************************************************************
 * Description  : 009.container节点，leafref被依赖的字段must校验失败，不进行leafref校验
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(leafRefContain, Yang_068_leafRefContain_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel8.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值   -- ContainerOne
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 container P 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ContainerTwo 
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作  -- ContainerTwo 
    int32_t f1fieldValue = 100;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作  -- ContainerTwo 
    bool f4 = true;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // leafref校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MUST;
    dataLef.expectedErrMsg = "violated must clause 0";
    dataLef.expectedErrPath = "/ContainerOne/F13";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = failRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns leafRefContain -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}



/*****************************************************************************
 * Description  : 010.container节点，leafref被依赖的字段是P节点字段默认值不可见，leafref校验报错
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(leafRefContain, Yang_068_leafRefContain_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel9.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 container P 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ContainerTwo 
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作  -- ContainerTwo 
    int32_t f1fieldValue = 100;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作  -- ContainerTwo 
    bool f4 = true;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // leafref校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/F0";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = failRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns leafRefContain -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 011.container节点，leafref被依赖的字段是NP节点字段默认值可见，leafref校验成功
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(leafRefContain, Yang_068_leafRefContain_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel10.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 container P 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ContainerTwo 
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作  -- ContainerTwo 
    int32_t f1fieldValue = 100;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作  -- ContainerTwo 
    bool f4 = true;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // leafref校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/F0";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns leafRefContain -rn ContainerOne -defaultMode REPORT_ALL");

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 container P 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ContainerTwo 
    fieldValue = 50;
    testYangSetVertexProperty_F0(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作  -- ContainerTwo 
    f1fieldValue = 100;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // leafref校验
    isDataService = true;

    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/F0";

    checkRes = failRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns leafRefContain -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 012.container节点，leafref被依赖的字段是P节点字段，有插入数据，leafref校验成功
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(leafRefContain, Yang_068_leafRefContain_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel11.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 container P 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ContainerTwo 
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作  -- ContainerTwo 
    int32_t f1fieldValue = 100;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作  -- ContainerTwo 
    bool f4 = true;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container P 子节点做replace操作  -- ContainerThree 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ContainerThree 
    testYangSetVertexProperty_F0(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 -- ContainerThree 
    ret = testYangSetField(g_containerT3Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // leafref校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/F0";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns leafRefContain -rn ContainerOne -defaultMode REPORT_ALL");

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 container P 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ContainerTwo 
    fieldValue = 50;
    testYangSetVertexProperty_F0(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作  -- ContainerTwo 
    f1fieldValue = 100;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container P 子节点做replace操作  -- ContainerThree 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ContainerThree 
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 -- ContainerThree 
    ret = testYangSetField(g_containerT3Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // leafref校验
    isDataService = true;

    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/F0";

    checkRes = failRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns leafRefContain -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 013.container节点，leafref被依赖的字段是NP节点字段，有插入数据，leafref校验成功
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(leafRefContain, Yang_068_leafRefContain_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel12.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 container P 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ContainerTwo 
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作  -- ContainerTwo 
    int32_t f1fieldValue = 100;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作  -- ContainerTwo 
    bool f4 = true;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container P 子节点做replace操作  -- ContainerThree 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ContainerThree 
    testYangSetVertexProperty_F0(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 -- ContainerThree 
    ret = testYangSetField(g_containerT3Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // leafref校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/F0";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns leafRefContain -rn ContainerOne -defaultMode REPORT_ALL");

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 container P 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ContainerTwo 
    fieldValue = 50;
    testYangSetVertexProperty_F0(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作  -- ContainerTwo 
    f1fieldValue = 100;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container P 子节点做replace操作  -- ContainerThree 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ContainerThree 
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 -- ContainerThree 
    ret = testYangSetField(g_containerT3Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // leafref校验
    isDataService = true;

    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/F0";

    checkRes = failRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns leafRefContain -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 014.container节点，leafref被依赖的字段是case字段默认值不可见，leafref校验报错
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(leafRefContain, Yang_068_leafRefContain_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel13.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 container P 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ContainerTwo 
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作  -- ContainerTwo 
    int32_t f1fieldValue = 100;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作  -- ContainerTwo 
    bool f4 = true;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container P 子节点做replace操作  -- ContainerThree 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ContainerThree 
    testYangSetVertexProperty_F0(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 -- ContainerThree 
    ret = testYangSetField(g_containerT3Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // leafref校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/F0";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = failRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns leafRefContain -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 015.container节点，leafref被依赖的字段是case字段默认值可见，leafref校验成功
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(leafRefContain, Yang_068_leafRefContain_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel14.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 container P 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ContainerTwo 
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作  -- ContainerTwo 
    int32_t f1fieldValue = 100;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作  -- ContainerTwo 
    bool f4 = true;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container P 子节点做replace操作  -- ContainerThree 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ContainerThree 
    testYangSetVertexProperty_F0(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 -- ContainerThree 
    ret = testYangSetField(g_containerT3Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // leafref校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/F0";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = failRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns leafRefContain -rn ContainerOne -defaultMode REPORT_ALL");

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 container P 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ContainerTwo 
    fieldValue = 3;
    testYangSetVertexProperty_F0(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作  -- ContainerTwo 
    f1fieldValue = 100;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作  -- ContainerTwo 
    f4 = true;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container P 子节点做replace操作  -- ContainerThree 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ContainerThree 
    testYangSetVertexProperty_F0(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 -- ContainerThree 
    ret = testYangSetField(g_containerT3Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // leafref校验
    isDataService = true;

    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/F0";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns leafRefContain -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 016.container节点，leafref被依赖的字段是case字段有插入数据，leafref校验成功
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(leafRefContain, Yang_068_leafRefContain_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel14.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 container P 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ContainerTwo 
    fieldValue = 30;
    testYangSetVertexProperty_F0(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作  -- ContainerTwo 
    int32_t f1fieldValue = 100;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作  -- ContainerTwo 
    bool f4 = true;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container P 子节点做replace操作  -- ContainerThree 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ContainerThree 
    testYangSetVertexProperty_F0(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 -- ContainerThree 
    ret = testYangSetField(g_containerT3Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    fieldValue = 35;
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // leafref校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/F0";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = failRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns leafRefContain -rn ContainerOne -defaultMode REPORT_ALL");

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 container P 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ContainerTwo 
    fieldValue = 60;
    testYangSetVertexProperty_F0(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作  -- ContainerTwo 
    f1fieldValue = 100;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作  -- ContainerTwo 
    f4 = true;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container P 子节点做replace操作  -- ContainerThree 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ContainerThree 
    testYangSetVertexProperty_F0(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 -- ContainerThree 
    ret = testYangSetField(g_containerT3Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    fieldValue = 60;
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // leafref校验
    isDataService = true;

    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/F0";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns leafRefContain -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 017.container节点，leafref被依赖的字段是list字段默认值不可见，leafref校验报错
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(leafRefContain, Yang_068_leafRefContain_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel15.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 container P 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ContainerTwo 
    fieldValue = 30;
    testYangSetVertexProperty_F0(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作  -- ContainerTwo 
    int32_t f1fieldValue = 100;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作  -- ContainerTwo 
    bool f4 = true;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container P 子节点做replace操作  -- ContainerThree 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ContainerThree 
    testYangSetVertexProperty_F0(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 -- ContainerThree 
    ret = testYangSetField(g_containerT3Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    fieldValue = 35;
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // leafref校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/F0";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = failRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns leafRefContain -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 018.container节点，leafref被依赖的字段是list字段默认值不可见，leafref校验报错
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(leafRefContain, Yang_068_leafRefContain_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel15.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 container P 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ContainerTwo 
    fieldValue = 30;
    testYangSetVertexProperty_F0(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作  -- ContainerTwo 
    int32_t f1fieldValue = 100;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作  -- ContainerTwo 
    bool f4 = true;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container P 子节点做replace操作  -- ContainerThree 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ContainerThree 
    testYangSetVertexProperty_F0(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 -- ContainerThree 
    ret = testYangSetField(g_containerT3Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    fieldValue = 35;
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 这里需要prepar list的labelname -- ListOne
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ListOne
    fieldValue = 35;
    testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 2, 2);

    // leafref校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/F0";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = failRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns leafRefContain -rn ContainerOne -defaultMode REPORT_ALL");


    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 container P 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ContainerTwo 
    fieldValue = 4;
    testYangSetVertexProperty_F0(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作  -- ContainerTwo 
    f1fieldValue = 100;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 这里需要prepar list的labelname -- ListOne
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ListOne
    fieldValue = 35;
    testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 2, 2);

    // leafref校验
    isDataService = true;

    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/F0";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns leafRefContain -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 019.container节点，leafref被依赖的字段是同一父节点（list）字段，xpath路径从父节点开始，leafref校验成功
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(leafRefContain, Yang_068_leafRefContain_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel16.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 这里需要prepar list的labelname -- ListOne
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ListOne
    fieldValue = 35;
    testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 container NP 子节点做replace操作 -- ListContainerOne
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listContainerNodeName1, GMC_OPERATION_REPLACE_GRAPH, &g_listContainerNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ListContainerOne F0
    fieldValue = 36;
    testYangSetVertexProperty_F0(g_listContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 -- ListContainerOne
    int32_t f1fieldValue = 100;
    ret = testYangSetField(g_listContainerNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 这里需要prepar list的labelname -- ListOne
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ListOne
    fieldValue = 36;
    testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_F1(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 container NP 子节点做replace操作 -- ListContainerthree
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listContainerNodeName1, GMC_OPERATION_REPLACE_GRAPH, &g_listContainerNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ListContainerthree
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_listContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    fieldValue = 100;
    ret = testYangSetField(g_listContainerNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 3, 3);

    // leafref校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/ContainerOne/ListOne[F0=35]/ListContainerOne/F0";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = failRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns leafRefContain -rn ContainerOne -defaultMode REPORT_ALL");

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 这里需要prepar list的labelname -- ListOne
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ListOne
    fieldValue = 35;
    testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 container NP 子节点做replace操作 -- ListContainerOne
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listContainerNodeName1, GMC_OPERATION_REPLACE_GRAPH, &g_listContainerNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ListContainerOne F0
    fieldValue = 4;
    testYangSetVertexProperty_F0(g_listContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 -- ListContainerOne
    f1fieldValue = 100;
    ret = testYangSetField(g_listContainerNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 这里需要prepar list的labelname -- ListOne
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ListOne
    fieldValue = 36;
    testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_F1(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 container NP 子节点做replace操作 -- ListContainerOne
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listContainerNodeName1, GMC_OPERATION_REPLACE_GRAPH, &g_listContainerNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ListContainerOne
    fieldValue = 36;
    testYangSetVertexProperty_F0(g_listContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    fieldValue = 30;
    ret = testYangSetField(g_listContainerNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 3, 3);

    // leafref校验
    isDataService = true;

    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/ContainerOne/ListOne[F0=35]/ListContainerOne/F0";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns leafRefContain -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}



/*****************************************************************************
 * Description  : 020.container节点，leafref被依赖的字段是同一父节点（list）字段，xpath路径从父节点的上层节点开始，leafref校验成功
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(leafRefContain, Yang_068_leafRefContain_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel17.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 这里需要prepar list的labelname -- ListOne
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ListOne
    fieldValue = 35;
    testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 container NP 子节点做replace操作 -- ListContainerOne
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listContainerNodeName1, GMC_OPERATION_REPLACE_GRAPH, &g_listContainerNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ListContainerOne F0
    fieldValue = 36;
    testYangSetVertexProperty_F0(g_listContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 -- ListContainerOne
    int32_t f1fieldValue = 100;
    ret = testYangSetField(g_listContainerNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 这里需要prepar list的labelname -- ListOne
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ListOne
    fieldValue = 36;
    testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_F1(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 container NP 子节点做replace操作 -- ListContainerthree
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listContainerNodeName1, GMC_OPERATION_REPLACE_GRAPH, &g_listContainerNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ListContainerthree
    fieldValue = 4;
    testYangSetVertexProperty_F0(g_listContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    fieldValue = 100;
    ret = testYangSetField(g_listContainerNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 3, 3);

    // leafref校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/ContainerOne/ListOne[F0=35]/ListContainerOne/F0";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns leafRefContain -rn ContainerOne -defaultMode REPORT_ALL");


    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 这里需要prepar list的labelname -- ListOne
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ListOne
    fieldValue = 35;
    testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 container NP 子节点做replace操作 -- ListContainerOne
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listContainerNodeName1, GMC_OPERATION_REPLACE_GRAPH, &g_listContainerNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ListContainerOne F0
    fieldValue = 36;
    testYangSetVertexProperty_F0(g_listContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 -- ListContainerOne
    f1fieldValue = 100;
    ret = testYangSetField(g_listContainerNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 这里需要prepar list的labelname -- ListOne
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ListOne
    fieldValue = 36;
    testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_F1(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 container NP 子节点做replace操作 -- ListContainerOne
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listContainerNodeName1, GMC_OPERATION_REPLACE_GRAPH, &g_listContainerNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ListContainerOne
    fieldValue = 37;
    testYangSetVertexProperty_F0(g_listContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    fieldValue = 30;
    ret = testYangSetField(g_listContainerNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 3, 3);

    // leafref校验
    isDataService = true;

    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/ContainerOne/ListOne[F0=36]/ListContainerOne/F0";

    checkRes = failRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns leafRefContain -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}



/*****************************************************************************
 * Description  : 021.container节点，leafref被依赖的字段是leaf-list字段默认值不可见，leafref校验报错
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(leafRefContain, Yang_068_leafRefContain_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel18.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 这里需要prepar list的labelname -- ListOne
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ListOne
    fieldValue = 35;
    testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 container NP 子节点做replace操作 -- ListContainerOne
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listContainerNodeName1, GMC_OPERATION_REPLACE_GRAPH, &g_listContainerNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ListContainerOne F0
    fieldValue = 4;
    testYangSetVertexProperty_F0(g_listContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 -- ListContainerOne
    int32_t f1fieldValue = 100;
    ret = testYangSetField(g_listContainerNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 这里需要prepar list的labelname -- ListOne
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ListOne
    fieldValue = 36;
    testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_F1(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 container NP 子节点做replace操作 -- ListContainerthree
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listContainerNodeName1, GMC_OPERATION_REPLACE_GRAPH, &g_listContainerNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ListContainerthree
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_listContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    fieldValue = 100;
    ret = testYangSetField(g_listContainerNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 3, 3);

    // leafref校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/ContainerOne/ListOne[F0=35]/ListContainerOne/F0";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = failRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns leafRefContain -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 022.container节点，leafref被依赖的字段是leaf-list字段默认值可见，leafref校验报错
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(leafRefContain, Yang_068_leafRefContain_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel18.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 30;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 这里需要prepar list的labelname -- ListOne
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ListOne
    fieldValue = 35;
    testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 container NP 子节点做replace操作 -- ListContainerOne
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listContainerNodeName1, GMC_OPERATION_REPLACE_GRAPH, &g_listContainerNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ListContainerOne F0
    fieldValue = 4;
    testYangSetVertexProperty_F0(g_listContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 -- ListContainerOne
    int32_t f1fieldValue = 100;
    ret = testYangSetField(g_listContainerNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 这里需要prepar list的labelname -- ListOne
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ListOne
    fieldValue = 36;
    testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_F1(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 container NP 子节点做replace操作 -- ListContainerthree
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listContainerNodeName1, GMC_OPERATION_REPLACE_GRAPH, &g_listContainerNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ListContainerthree
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_listContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    fieldValue = 100;
    ret = testYangSetField(g_listContainerNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 3, 3);

    // leafref校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/ContainerOne/ListOne[F0=36]/ListContainerOne/F0";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = failRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns leafRefContain -rn ContainerOne -defaultMode REPORT_ALL");

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 30;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 这里需要prepar list的labelname -- ListOne
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ListOne
    fieldValue = 35;
    testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 container NP 子节点做replace操作 -- ListContainerOne
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listContainerNodeName1, GMC_OPERATION_REPLACE_GRAPH, &g_listContainerNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ListContainerOne F0
    fieldValue = 4;
    testYangSetVertexProperty_F0(g_listContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 -- ListContainerOne
    f1fieldValue = 100;
    ret = testYangSetField(g_listContainerNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 这里需要prepar list的labelname -- ListOne
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ListOne
    fieldValue = 36;
    testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_F1(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 container NP 子节点做replace操作 -- ListContainerOne
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listContainerNodeName1, GMC_OPERATION_REPLACE_GRAPH, &g_listContainerNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ListContainerOne
    fieldValue = 9;
    testYangSetVertexProperty_F0(g_listContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    fieldValue = 30;
    ret = testYangSetField(g_listContainerNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 3, 3);

    // leafref校验
    isDataService = true;

    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/ContainerOne/ListOne[F0=35]/ListContainerOne/F0";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns leafRefContain -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}


//023.container节点，leafref被依赖的字段是同一父节点（leaf-list）字段，xpath路径从父节点开始，leafref校验成功
//024.container节点，leafref被依赖的字段是同一父节点（leaf-list）字段，xpath路径从父节点开始，leafref校验成功
// 不存在此种场景；


/*****************************************************************************
 * Description  : 023.container节点，leafref定义多级跳A->B, B->C，C->D, leafref校验成功
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(leafRefContain, Yang_068_leafRefContain_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel19.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 设置根的属性值 做merge操作 -- ContainerOne 
    fieldValue = 100;
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_UINT32, &fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    // 设置根的属性值 做merge操作 -- ContainerOne 
    fieldValue = 50;
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_UINT32, &fieldValue, sizeof(int32_t), "F2", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    // 设置根的属性值 做merge操作 -- ContainerOne 
    fieldValue = 100;
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_UINT32, &fieldValue, sizeof(int32_t), "F3", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 这里需要prepar list的labelname -- ListOne
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ListOne
    fieldValue = 35;
    testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 container NP 子节点做replace操作 -- ListContainerOne
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listContainerNodeName1, GMC_OPERATION_REPLACE_GRAPH, &g_listContainerNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ListContainerOne F0
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_listContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 -- ListContainerOne 
    int32_t f1fieldValue = 100;
    ret = testYangSetField(g_listContainerNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 2, 2);

    // leafref校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/ContainerOne/F1";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = failRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns leafRefContain -rn ContainerOne -defaultMode REPORT_ALL");


    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);


    // 设置根的属性值 做merge操作 -- ContainerOne 
    fieldValue = 100;
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_UINT32, &fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    // 设置根的属性值 做merge操作 -- ContainerOne 
    fieldValue = 100;
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_UINT32, &fieldValue, sizeof(int32_t), "F2", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    // 设置根的属性值 做merge操作 -- ContainerOne 
    fieldValue = 100;
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_UINT32, &fieldValue, sizeof(int32_t), "F3", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 这里需要prepar list的labelname -- ListOne
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ListOne
    fieldValue = 35;
    testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 container NP 子节点做replace操作 -- ListContainerOne
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listContainerNodeName1, GMC_OPERATION_REPLACE_GRAPH, &g_listContainerNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ListContainerOne F0
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_listContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 -- ListContainerOne
    f1fieldValue = 100;
    ret = testYangSetField(g_listContainerNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 这里需要prepar list的labelname -- ListOne
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ListOne
    fieldValue = 36;
    testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_F1(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 container NP 子节点做replace操作 -- ListContainerOne
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listContainerNodeName1, GMC_OPERATION_REPLACE_GRAPH, &g_listContainerNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ListContainerOne
    fieldValue = 9;
    testYangSetVertexProperty_F0(g_listContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    fieldValue = 30;
    ret = testYangSetField(g_listContainerNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 3, 3);

    // leafref校验
    isDataService = true;

    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/ContainerOne/ListOne[F0=36]/ListContainerOne/F0";

    checkRes = failRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns leafRefContain -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 024.container节点，leafref定义多层级多级跳, 循环leafref校验
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(leafRefContain, Yang_068_leafRefContain_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel20.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F2");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F3");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F4");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F5");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F6");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F7");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F8");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F9");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F10");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F11");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 这里需要prepar list的labelname -- ListOne
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ListOne
    fieldValue = 35;
    testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 container NP 子节点做replace操作 -- ListContainerOne
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listContainerNodeName1, GMC_OPERATION_REPLACE_GRAPH, &g_listContainerNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ListContainerOne F0
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_listContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 -- ListContainerOne 
    int32_t f1fieldValue = 100;
    ret = testYangSetField(g_listContainerNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    // 这里需要prepar list的labelname -- ListOne
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ListOne F0
    fieldValue = 36;
    testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 container NP 子节点做replace操作 -- ListContainerOne
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listContainerNodeName1, GMC_OPERATION_REPLACE_GRAPH, &g_listContainerNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ListContainerOne F0
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_listContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 -- ListContainerOne F1
    f1fieldValue = 100;
    ret = testYangSetField(g_listContainerNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 3, 3);

    // leafref校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/ContainerOne/F1";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns leafRefContain -rn ContainerOne -defaultMode REPORT_ALL");


    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F2");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F3");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F4");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F5");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F6");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F7");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F8");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F9");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F10");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F11");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 这里需要prepar list的labelname -- ListOne
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ListOne
    fieldValue = 35;
    testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 container NP 子节点做replace操作 -- ListContainerOne
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listContainerNodeName1, GMC_OPERATION_REPLACE_GRAPH, &g_listContainerNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ListContainerOne F0
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_listContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 -- ListContainerOne 
    f1fieldValue = 100;
    ret = testYangSetField(g_listContainerNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 这里需要prepar list的labelname -- ListOne
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ListOne F0
    fieldValue = 36;
    testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 container NP 子节点做replace操作 -- ListContainerOne
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listContainerNodeName1, GMC_OPERATION_REPLACE_GRAPH, &g_listContainerNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ListContainerOne F0
    fieldValue = 36;
    testYangSetVertexProperty_F0(g_listContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 -- ListContainerOne F1
    f1fieldValue = 100;
    ret = testYangSetField(g_listContainerNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 3, 3);

    // leafref校验
    isDataService = true;

    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/ContainerOne/ListOne[F0=36]/ListContainerOne/F0";

    checkRes = failRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns leafRefContain -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 025.container节点，leafref定义字段和被依赖字段不是同一类型
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(leafRefContain, Yang_068_leafRefContain_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel21.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmt_async, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 模型校验失败
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(false, checkData.validateRes);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 026.container节点，leafref定义在所有支持的类型字段字段上，leafref校验成功
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(leafRefContain, Yang_068_leafRefContain_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel22.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对 container NP 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ContainerTwo 
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作  -- ContainerTwo 
    int32_t f1fieldValue = 100;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作  -- ContainerTwo 
    bool f4 = true;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container P 子节点做replace操作  -- ContainerThree 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ContainerThree 
    testYangSetVertexProperty_F0(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 -- ContainerThree 
    ret = testYangSetField(g_containerT3Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作 -- ContainerThree 
    ret = testYangSetField(g_containerT3Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // leafref校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_BUTT;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath = "";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns leafRefContain -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}

