/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: 040_LeafList
 * Author: hanyang
 * Create: 2023-1-18
 */
#include "LeafList.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
GmcConnT *g_conn_async = NULL;
GmcStmtT *g_stmt_async = NULL;
GmcStmtT *g_stmt_root = NULL;
GmcStmtT *g_stmt_con = NULL;
GmcStmtT *g_stmt_list = NULL;
GmcStmtT *g_stmt_leaf = NULL;
GmcStmtT *g_stmt_choice = NULL;
GmcStmtT *g_stmt_case = NULL;

GmcNodeT *g_rootNode = NULL;
GmcNodeT *g_conNode = NULL;
GmcNodeT *g_listNode = NULL;
GmcNodeT *g_leafNode = NULL;
GmcNodeT *g_choiceNode = NULL;
GmcNodeT *g_caseNode = NULL;

class LeafList : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void LeafList::SetUpTestCase()
{
    int ret;
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void LeafList::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

void LeafList::SetUp()
{
    int ret;
    AsyncUserDataT data = {0};

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_con);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_choice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_case);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步创建namespace
    GmcDropNamespace(g_stmt, g_namespace);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = g_namespace;
    nspCfg.tablespaceName = NULL;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 创建Vertex表
    TestCreateLabelAll(g_stmt_async);

    AW_CHECK_LOG_BEGIN();
}

void LeafList::TearDown()
{
    int ret;
    uint32_t i = 0;
    AsyncUserDataT data = {0};
    AW_CHECK_LOG_END();

    // 删除Vertex和Edge表
    TestDropLabelAll(g_stmt_async);

    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, g_namespace, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    GmcFreeStmt(g_stmt_root);
    GmcFreeStmt(g_stmt_con);
    GmcFreeStmt(g_stmt_list);
    GmcFreeStmt(g_stmt_leaf);
    GmcFreeStmt(g_stmt_choice);
    GmcFreeStmt(g_stmt_case);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_conn_async = NULL;
    g_stmt_async = NULL;
    g_stmt_root = NULL;
    g_stmt_con = NULL;
    g_stmt_list = NULL;
    g_stmt_leaf = NULL;
    g_stmt_choice = NULL;
    g_stmt_case = NULL;

    g_rootNode = NULL;
    g_conNode = NULL;
    g_listNode = NULL;
    g_leafNode = NULL;
    g_choiceNode = NULL;
    g_caseNode = NULL;
}

/* Yang模型示例，全打散和部分打散
                          root(con)
                             |
   ┌------------┬------------┼------------┬------------┬------------┬------------┐
   |            |            |            |            |            |            |
 leaf_1       leaf_2       leaf_3      leaf_4        con_5        list_6      choice_7
           (localhash)      (str)      (fixed)         |            |            |
                                                       |            |            |
                                                     leaf_5       leaf_6       case_7
                                                                                 |
                                                                               leaf_7
*/
/*****************************************************************************
 Description  : 001.部分打散，root下创建leaf-list，执行不同的六原语操作，默认位置
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, Yang_040_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert root***********************************/
    fieldValue = 100;
    TestInsertRootTree(g_conn_async, "root_tree", fieldValue);

    /***************************insert***********************************/
    TestInsertLeafTree(g_conn_async, "root_tree", "leaf_tree_1");

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_001_01");

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************merge***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_1", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i + 100;
        PID = 1;
        TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_leafNode = NULL;
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_001_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************replace***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_1", GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i + 200;
        TestYangSetNodeProperty_PK(g_leafNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_leafNode = NULL;
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_001_03");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************delete***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_1", GMC_OPERATION_DELETE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i + 100;
        PID = 1;
        TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_leafNode = NULL;
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_001_04");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************remove***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_1", GMC_OPERATION_REMOVE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i + 200;
        PID = 1;
        TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_leafNode = NULL;
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_001_05");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 002.部分打散，root下创建leaf-list，执行不同的六原语操作，指定位置
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, Yang_040_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t keyValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    GmcYangListLocatorT listProp;
    GmcPropValueT refKey;

    /***************************insert root***********************************/
    fieldValue = 100;
    TestInsertRootTree(g_conn_async, "root_tree", fieldValue);

    /***************************insert, GMC_YANG_LIST_POSITION_FIRST***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点, 9-0顺序
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置插入位置
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_FIRST, NULL);
        ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        UninitListProperty(&listProp);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_leafNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_leafNode = NULL;
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_002_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************insert, GMC_YANG_LIST_POSITION_BEFORE***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点，在3前面插入100
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    keyValue = 3;
    InitRefKeys(&refKey, 1, &keyValue);
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_BEFORE, &refKey);
    ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_leafNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_leafNode = NULL;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_002_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************insert, GMC_YANG_LIST_POSITION_AFTER***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点，在3后面插入200
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    keyValue = 3;
    InitRefKeys(&refKey, 1, &keyValue);
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_AFTER, &refKey);
    ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodeProperty_PK(g_leafNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_leafNode = NULL;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_002_03");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 003.部分打散，root下创建leaf-list，移动已有元素的位置
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, Yang_040_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t keyValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    GmcYangListLocatorT listProp;
    GmcPropValueT refKey;

    /***************************insert root***********************************/
    fieldValue = 100;
    TestInsertRootTree(g_conn_async, "root_tree", fieldValue);

    /***************************insert***********************************/
    TestInsertLeafTree(g_conn_async, "root_tree", "leaf_tree_1");

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_003_01");

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************移动已有元素位置***********************************/
    /***************************GMC_YANG_LIST_POSITION_FIRST***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点，5挪到第一个
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_1", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_FIRST, NULL);
    ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    fieldValue = 5;
    TestYangSetNodeProperty_PK(g_leafNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_leafNode = NULL;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_003_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************GMC_YANG_LIST_POSITION_BEFORE***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点，5挪到3前面
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_1", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    keyValue = 3;
    InitRefKeys(&refKey, 1, &keyValue);
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_BEFORE, &refKey);
    ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    fieldValue = 5;
    TestYangSetNodeProperty_PK(g_leafNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_leafNode = NULL;


    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_003_03");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************GMC_YANG_LIST_POSITION_AFTER***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点，5挪到3后面
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_1", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    keyValue = 3;
    InitRefKeys(&refKey, 1, &keyValue);
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_AFTER, &refKey);
    ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    fieldValue = 5;
    TestYangSetNodeProperty_PK(g_leafNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_leafNode = NULL;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_003_04");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 004.部分打散，root下创建leaf-list，执行不同的六原语操作，查询diff数据
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, Yang_040_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert root***********************************/
    fieldValue = 100;
    TestInsertRootTree(g_conn_async, "root_tree", fieldValue);

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_leafNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_leafNode = NULL;
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);

    // 查询diff
    testFetchAndDeparseDiff(g_stmt_leaf, batch, expectDiff004, data);

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 005.部分打散，root下创建leaf-list，移动已有元素的位置，查询diff数据
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, Yang_040_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t keyValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    GmcYangListLocatorT listProp;
    GmcPropValueT refKey;

    /***************************insert root***********************************/
    fieldValue = 100;
    TestInsertRootTree(g_conn_async, "root_tree", fieldValue);

    /***************************insert***********************************/
    TestInsertLeafTree(g_conn_async, "root_tree", "leaf_tree_1");

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_005_01");

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************移动已有元素位置***********************************/
    /***************************GMC_YANG_LIST_POSITION_FIRST***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_1", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_FIRST, NULL);
    ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    fieldValue = 5;
    TestYangSetNodeProperty_PK(g_leafNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_leafNode = NULL;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);

    // 查询diff
    testFetchAndDeparseDiff(g_stmt_leaf, batch, expectDiff005, data);

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_005_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 006. 部分打散，root下创建leaf-list，包括list唯一索引，执行六原语操作，subtree查询操作结果
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, Yang_040_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert root***********************************/
    fieldValue = 100;
    TestInsertRootTree(g_conn_async, "root_tree", fieldValue);

    /***************************insert***********************************/
    TestInsertLeafTree(g_conn_async, "root_tree", "leaf_tree_2");

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_006_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************merge***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_2", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i + 100;
        PID = 1;
        TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_leafNode = NULL;
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_006_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************replace***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_2", GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i + 200;
        TestYangSetNodeProperty_PK(g_leafNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_leafNode = NULL;
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_006_03");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************delete***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_2", GMC_OPERATION_DELETE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i + 100;
        PID = 1;
        TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_leafNode = NULL;
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_006_04");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************remove***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_2", GMC_OPERATION_REMOVE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i + 200;
        PID = 1;
        TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_leafNode = NULL;
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_006_05");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 007.部分打散，root-con下创建leaf-list，执行不同的六原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, Yang_040_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert root***********************************/
    fieldValue = 100;
    TestInsertRootTree(g_conn_async, "root_tree", fieldValue);

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_tree_5", GMC_OPERATION_INSERT, &g_conNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_conNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_5", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_leafNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_leafNode = NULL;
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_007_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************merge***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_tree_5", GMC_OPERATION_NONE, &g_conNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_5", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i + 100;
        PID = 1;
        TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_leafNode = NULL;
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_007_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************delete***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_tree_5", GMC_OPERATION_NONE, &g_conNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_5", GMC_OPERATION_DELETE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i + 100;
        PID = 1;
        TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_leafNode = NULL;
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_007_03");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 008.部分打散，root-list下创建leaf-list，执行不同的六原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, Yang_040_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert root***********************************/
    fieldValue = 100;
    TestInsertRootTree(g_conn_async, "root_tree", fieldValue);

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, "list_tree_6", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_listNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_listNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_6", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_list, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_leafNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_leafNode = NULL;
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(12, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(12, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_008_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************merge***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, "list_tree_6", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_listNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_list, fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_6", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_list, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i + 100;
        PID = 1;
        TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_leafNode = NULL;
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(12, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(12, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_008_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************delete***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, "list_tree_6", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_listNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_list, fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_6", GMC_OPERATION_DELETE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_list, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i + 100;
        PID = 1;
        TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_leafNode = NULL;
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(12, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(12, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_008_03");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 009.部分打散，root-choice-case下创建leaf-list，执行不同的六原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, Yang_040_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert root***********************************/
    fieldValue = 100;
    TestInsertRootTree(g_conn_async, "root_tree", fieldValue);

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "choice_tree_7", GMC_OPERATION_INSERT, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = GmcYangEditChildNode(g_choiceNode, "case_tree_7", GMC_OPERATION_INSERT, &g_caseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_caseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_7", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_leafNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_leafNode = NULL;
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_009_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************merge***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "choice_tree_7", GMC_OPERATION_NONE, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = GmcYangEditChildNode(g_choiceNode, "case_tree_7", GMC_OPERATION_NONE, &g_caseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_7", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i + 100;
        PID = 1;
        TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_leafNode = NULL;
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_009_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************delete***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "choice_tree_7", GMC_OPERATION_NONE, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = GmcYangEditChildNode(g_choiceNode, "case_tree_7", GMC_OPERATION_NONE, &g_caseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_7", GMC_OPERATION_DELETE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i + 100;
        PID = 1;
        TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_leafNode = NULL;
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_009_03");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 010.部分打散，多层不同的节点下创建leaf-list，执行不同的六原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, Yang_040_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert root***********************************/
    fieldValue = 100;
    TestInsertRootTree(g_conn_async, "root_tree", fieldValue);

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child con节点
    ret = GmcYangEditChildNode(g_rootNode, "con_tree_5", GMC_OPERATION_INSERT, &g_conNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_conNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child choice节点
    ret = GmcYangEditChildNode(g_rootNode, "choice_tree_7", GMC_OPERATION_INSERT, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child case节点
    ret = GmcYangEditChildNode(g_choiceNode, "case_tree_7", GMC_OPERATION_INSERT, &g_caseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_caseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_leafNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_leafNode = NULL;
    }

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_5", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_leafNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_leafNode = NULL;
    }

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, "list_tree_6", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_listNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_listNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_6", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_list, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_leafNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_leafNode = NULL;
    }

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_7", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_leafNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_leafNode = NULL;
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(42, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(42, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_010_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 011.部分打散，多层不同的节点下创建leaf-list，六原语操作执行失败，查询errorpath
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, Yang_040_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert root***********************************/
    fieldValue = 100;
    TestInsertRootTree(g_conn_async, "root_tree", fieldValue);

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child con节点
    ret = GmcYangEditChildNode(g_rootNode, "con_tree_5", GMC_OPERATION_INSERT, &g_conNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_conNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child choice节点
    ret = GmcYangEditChildNode(g_rootNode, "choice_tree_7", GMC_OPERATION_INSERT, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child case节点
    ret = GmcYangEditChildNode(g_choiceNode, "case_tree_7", GMC_OPERATION_INSERT, &g_caseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_caseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_leafNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_leafNode = NULL;
    }

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_5", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_leafNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_leafNode = NULL;
    }

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, "list_tree_6", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_listNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_listNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_6", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_list, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_leafNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_leafNode = NULL;
    }

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_7", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_leafNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_leafNode = NULL;
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(42, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(42, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************error-path root-leaflist***********************************/
    AsyncUserDataT data_batch = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    TestYangSetNodeProperty_PK(g_leafNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_leafNode = NULL;

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_CREATE;
    data_batch.expectedErrMsg = "target exists";
    data_batch.expectedErrPath = "/root_tree/leaf_tree_1[F0=1]";

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, data_batch.status);

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /**********************error-path root--con--leaflist******************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child con节点
    ret = GmcYangEditChildNode(g_rootNode, "con_tree_5", GMC_OPERATION_NONE, &g_conNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_5", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    TestYangSetNodeProperty_PK(g_leafNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_leafNode = NULL;

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_CREATE;
    data_batch.expectedErrMsg = "target exists";
    data_batch.expectedErrPath = "/root_tree/con_tree_5/leaf_tree_5[F0=1]";

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, data_batch.status);

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /**********************error-path root--list--leaflist******************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, "list_tree_6", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_listNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_list, fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_6", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_list, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    TestYangSetNodeProperty_PK(g_leafNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_leafNode = NULL;

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_CREATE;
    data_batch.expectedErrMsg = "target exists";
    data_batch.expectedErrPath = "/root_tree/list_tree_6[F0=100]/leaf_tree_6[F0=1]";

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, data_batch.status);

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /**********************error-path root--choice--case--leaflist******************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child choice节点
    ret = GmcYangEditChildNode(g_rootNode, "choice_tree_7", GMC_OPERATION_NONE, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child case节点
    ret = GmcYangEditChildNode(g_choiceNode, "case_tree_7", GMC_OPERATION_NONE, &g_caseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_7", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    TestYangSetNodeProperty_PK(g_leafNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_leafNode = NULL;

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_CREATE;
    data_batch.expectedErrMsg = "target exists";
    data_batch.expectedErrPath = "/root_tree/choice_tree_7/case_tree_7/leaf_tree_7[F0=1]";

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, data_batch.status);
    AddWhiteList(GMERR_PRIMARY_KEY_VIOLATION);

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 012.全打散，root下创建leaf-list，执行不同的六原语操作，默认位置
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, DISABLED_Yang_040_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert root***********************************/
    fieldValue = 100;
    TestInsertRootTree(g_conn_async, "root_graph", fieldValue);

    /***************************insert***********************************/
    TestInsertLeafTree(g_conn_async, "root_graph", "leaf_graph_1");

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_graph", "Yang_040_Func_012_01");

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************merge***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_1", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i + 100;
        PID = 1;
        TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_graph", "Yang_040_Func_012_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************replace***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_1", GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i + 200;
        TestYangSetVertexProperty_PK(g_stmt_leaf, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_graph", "Yang_040_Func_012_03");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************delete***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_1", GMC_OPERATION_DELETE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i + 100;
        PID = 1;
        TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_graph", "Yang_040_Func_012_04");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************remove***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_1", GMC_OPERATION_REMOVE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i + 200;
        PID = 1;
        TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_graph", "Yang_040_Func_012_05");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 013.全打散，root下创建leaf-list，执行不同的六原语操作，指定位置
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, DISABLED_Yang_040_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t keyValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    GmcYangListLocatorT listProp;
    GmcPropValueT refKey;

    /***************************insert root***********************************/
    fieldValue = 100;
    TestInsertRootGraph(g_conn_async, "root_graph", fieldValue);

    /***************************insert, GMC_YANG_LIST_POSITION_FIRST***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置插入位置
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_FIRST, NULL);
        ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        UninitListProperty(&listProp);

        // 设置属性值
        fieldValue = i;
        TestYangSetVertexProperty_PK(g_stmt_leaf, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_graph", "Yang_040_Func_013_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************insert, GMC_YANG_LIST_POSITION_BEFORE***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    keyValue = 3;
    InitRefKeys(&refKey, 1, &keyValue);
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_BEFORE, &refKey);
    ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    fieldValue = 100;
    TestYangSetVertexProperty_PK(g_stmt_leaf, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_graph", "Yang_040_Func_013_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************insert, GMC_YANG_LIST_POSITION_AFTER***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    keyValue = 3;
    InitRefKeys(&refKey, 1, &keyValue);
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_AFTER, &refKey);
    ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    fieldValue = 200;
    TestYangSetVertexProperty_PK(g_stmt_leaf, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_graph", "Yang_040_Func_013_03");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 014.全打散，root下创建leaf-list，移动已有元素的位置
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, DISABLED_Yang_040_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t keyValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    GmcYangListLocatorT listProp;
    GmcPropValueT refKey;

    /***************************insert root***********************************/
    fieldValue = 100;
    TestInsertRootGraph(g_conn_async, "root_graph", fieldValue);

    /***************************insert***********************************/
    TestInsertLeafTree(g_conn_async, "root_graph", "leaf_graph_1");

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_graph", "Yang_040_Func_014_01");

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************移动已有元素位置***********************************/
    /***************************GMC_YANG_LIST_POSITION_FIRST***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_1", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_FIRST, NULL);
    ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    fieldValue = 5;
    TestYangSetVertexProperty_PK(g_stmt_leaf, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_graph", "Yang_040_Func_014_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************GMC_YANG_LIST_POSITION_BEFORE***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_1", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    keyValue = 3;
    InitRefKeys(&refKey, 1, &keyValue);
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_BEFORE, &refKey);
    ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    fieldValue = 5;
    TestYangSetVertexProperty_PK(g_stmt_leaf, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_graph", "Yang_040_Func_014_03");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************GMC_YANG_LIST_POSITION_AFTER***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_1", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    keyValue = 3;
    InitRefKeys(&refKey, 1, &keyValue);
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_AFTER, &refKey);
    ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    fieldValue = 5;
    TestYangSetVertexProperty_PK(g_stmt_leaf, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_graph", "Yang_040_Func_014_04");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 015.全打散，root下创建leaf-list，执行不同的六原语操作，查询diff数据
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, DISABLED_Yang_040_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert root***********************************/
    fieldValue = 100;
    TestInsertRootGraph(g_conn_async, "root_graph", fieldValue);

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetVertexProperty_PK(g_stmt_leaf, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);

    // 查询diff
    testFetchAndDeparseDiff(g_stmt_leaf, batch, expectDiff015, data);

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 016.全打散，root下创建leaf-list，移动已有元素的位置，查询diff数据
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, DISABLED_Yang_040_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t keyValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    GmcYangListLocatorT listProp;
    GmcPropValueT refKey;

    /***************************insert root***********************************/
    fieldValue = 100;
    TestInsertRootGraph(g_conn_async, "root_graph", fieldValue);

    /***************************insert***********************************/
    TestInsertLeafTree(g_conn_async, "root_graph", "leaf_graph_1");

    /***************************移动已有元素位置***********************************/
    /***************************GMC_YANG_LIST_POSITION_FIRST***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_1", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_FIRST, NULL);
    ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    fieldValue = 5;
    TestYangSetVertexProperty_PK(g_stmt_leaf, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);

    // 查询diff
    testFetchAndDeparseDiff(g_stmt_leaf, batch, expectDiff016, data);

    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 017. 全打散，root下创建leaf-list，包括list唯一索引，执行六原语操作，subtree查询操作结果
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, DISABLED_Yang_040_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert root***********************************/
    fieldValue = 100;
    TestInsertRootGraph(g_conn_async, "root_graph", fieldValue);

    /***************************insert***********************************/
    TestInsertLeafTree(g_conn_async, "root_graph", "leaf_graph_2");

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_graph", "Yang_040_Func_017_01");

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************merge***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_2", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i + 100;
        PID = 1;
        TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_graph", "Yang_040_Func_017_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************replace***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_2", GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i + 200;
        TestYangSetVertexProperty_PK(g_stmt_leaf, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_graph", "Yang_040_Func_017_03");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************delete***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_2", GMC_OPERATION_DELETE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i + 100;
        PID = 1;
        TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_graph", "Yang_040_Func_017_04");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************remove***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_2", GMC_OPERATION_REMOVE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i + 200;
        PID = 1;
        TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_graph", "Yang_040_Func_017_05");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 018.全打散，root-con下创建leaf-list，执行不同的六原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, DISABLED_Yang_040_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert root***********************************/
    fieldValue = 100;
    TestInsertRootGraph(g_conn_async, "root_graph", fieldValue);

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child con节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_con, "con_graph_5", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_con);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetVertexProperty(g_stmt_con, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_con);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child leaf节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_5", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_con, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetVertexProperty_PK(g_stmt_leaf, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(12, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(12, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_graph", "Yang_040_Func_018_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************merge***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child con节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_con, "con_graph_5", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_con);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_con);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_5", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_con, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i + 100;
        PID = 1;
        TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(12, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(12, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_graph", "Yang_040_Func_018_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************delete***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child con节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_con, "con_graph_5", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_con);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_con);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_5", GMC_OPERATION_DELETE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_con, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i + 100;
        PID = 1;
        TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(12, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(12, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_graph", "Yang_040_Func_018_03");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 019.全打散，root-list下创建leaf-list，执行不同的六原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, DISABLED_Yang_040_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert root***********************************/
    fieldValue = 100;
    TestInsertRootGraph(g_conn_async, "root_graph", fieldValue);

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, "list_graph_6", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetVertexProperty(g_stmt_list, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_6", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_list, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetVertexProperty_PK(g_stmt_leaf, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(12, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(12, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_graph", "Yang_040_Func_019_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************merge***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, "list_graph_6", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_list, fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_6", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_list, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i + 100;
        PID = 1;
        TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(12, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(12, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_graph", "Yang_040_Func_019_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************delete***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, "list_graph_6", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_list, fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_6", GMC_OPERATION_DELETE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_list, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i + 100;
        PID = 1;
        TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(12, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(12, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_graph", "Yang_040_Func_019_03");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 020.全打散，root-choice-case下创建leaf-list，执行不同的六原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, DISABLED_Yang_040_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert root***********************************/
    fieldValue = 100;
    TestInsertRootGraph(g_conn_async, "root_graph", fieldValue);

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child choice节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_choice, "choice_graph_7", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_choice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_choice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child case节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_case, "case_graph_7", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_choice, g_stmt_case);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetVertexProperty(g_stmt_case, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_case);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_7", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_case, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetVertexProperty_PK(g_stmt_leaf, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(13, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(13, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_graph", "Yang_040_Func_020_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************merge***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child choice节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_choice, "choice_graph_7", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_choice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_choice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child case节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_case, "case_graph_7", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_choice, g_stmt_case);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_case);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_7", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_case, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i + 100;
        PID = 1;
        TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(13, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(13, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_graph", "Yang_040_Func_020_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************delete***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child choice节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_choice, "choice_graph_7", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_choice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_choice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child case节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_case, "case_graph_7", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_choice, g_stmt_case);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_case);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_7", GMC_OPERATION_DELETE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_case, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i + 100;
        PID = 1;
        TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(13, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(13, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_graph", "Yang_040_Func_020_03");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 021.全打散，多层不同的节点下创建leaf-list，执行不同的六原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, DISABLED_Yang_040_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert root***********************************/
    fieldValue = 100;
    TestInsertRootGraph(g_conn_async, "root_graph", fieldValue);

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetVertexProperty_PK(g_stmt_leaf, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child con节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_con, "con_graph_5", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_con);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetVertexProperty(g_stmt_con, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_con);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_5", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_con, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetVertexProperty_PK(g_stmt_leaf, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, "list_graph_6", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetVertexProperty(g_stmt_list, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_6", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_list, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetVertexProperty_PK(g_stmt_leaf, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child choice节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_choice, "choice_graph_7", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_choice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_choice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child case节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_case, "case_graph_7", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_choice, g_stmt_case);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetVertexProperty(g_stmt_case, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_case);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_7", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_case, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetVertexProperty_PK(g_stmt_leaf, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(45, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(45, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_graph", "Yang_040_Func_021_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 022.全打散，多层不同的节点下创建leaf-list，六原语操作执行失败，查询errorpath
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, DISABLED_Yang_040_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert root***********************************/
    fieldValue = 100;
    TestInsertRootGraph(g_conn_async, "root_graph", fieldValue);

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetVertexProperty_PK(g_stmt_leaf, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child con节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_con, "con_graph_5", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_con);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetVertexProperty(g_stmt_con, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_con);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_5", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_con, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetVertexProperty_PK(g_stmt_leaf, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, "list_graph_6", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetVertexProperty(g_stmt_list, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_6", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_list, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetVertexProperty_PK(g_stmt_leaf, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child choice节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_choice, "choice_graph_7", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_choice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_choice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child case节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_case, "case_graph_7", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_choice, g_stmt_case);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetVertexProperty(g_stmt_case, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_case);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_7", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_case, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetVertexProperty_PK(g_stmt_leaf, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(45, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(45, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************error-path root-leaflist***********************************/
    AsyncUserDataT data_batch = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    TestYangSetVertexProperty_PK(g_stmt_leaf, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_CREATE;
    data_batch.expectedErrMsg = "target exists";
    data_batch.expectedErrPath = "/root_graph/leaf_graph_1[F0=1]";

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, data_batch.status);

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /**********************error-path root--con--leaflist******************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child con节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_con, "con_graph_5", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_con);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_con);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_5", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_con, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    TestYangSetVertexProperty_PK(g_stmt_leaf, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_CREATE;
    data_batch.expectedErrMsg = "target exists";
    data_batch.expectedErrPath = "/root_graph/con_graph_5/leaf_graph_5[F0=1]";

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, data_batch.status);

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /**********************error-path root--list--leaflist******************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, "list_graph_6", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_list, fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_6", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_list, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    TestYangSetVertexProperty_PK(g_stmt_leaf, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_CREATE;
    data_batch.expectedErrMsg = "target exists";
    data_batch.expectedErrPath = "/root_graph/list_graph_6[F0=100]/leaf_graph_6[F0=1]";

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, data_batch.status);

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /**********************error-path root--choice--case--leaflist******************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child choice节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_choice, "choice_graph_7", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_choice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_choice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child case节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_case, "case_graph_7", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_choice, g_stmt_case);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_case);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_7", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_case, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    TestYangSetVertexProperty_PK(g_stmt_leaf, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data_batch.isValidErrorPathInfo = true;
    data_batch.expectedErrorCode = GMC_VIOLATES_CREATE;
    data_batch.expectedErrMsg = "target exists";
    data_batch.expectedErrPath = "/root_graph/choice_graph_7/case_graph_7/leaf_graph_7[F0=1]";

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data_batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, data_batch.status);
    AddWhiteList(GMERR_PRIMARY_KEY_VIOLATION);

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 023. leaf-list的字段类型为string类型，执行六原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, Yang_040_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert root***********************************/
    fieldValue = 100;
    TestInsertRootTree(g_conn_async, "root_tree", fieldValue);

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_3", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK_String(g_leafNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_leafNode = NULL;
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_023_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************merge***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    char stringValue[10] = {0};
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_3", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i + 100;
        PID = 1;
        ret = snprintf(stringValue, 8, "str%03d", fieldValue);
        AW_MACRO_EXPECT_NE_INT(0, ret);
        ret = GmcSetIndexKeyValue(g_stmt_leaf, 1, GMC_DATATYPE_STRING, stringValue, (strlen(stringValue)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_leafNode = NULL;
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_023_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************replace***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_3", GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i + 200;
        TestYangSetNodeProperty_PK_String(g_leafNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_leafNode = NULL;
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_023_03");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************delete***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_3", GMC_OPERATION_DELETE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i + 100;
        PID = 1;
        ret = snprintf(stringValue, 8, "str%03d", fieldValue);
        AW_MACRO_EXPECT_NE_INT(0, ret);
        ret = GmcSetIndexKeyValue(g_stmt_leaf, 1, GMC_DATATYPE_STRING, stringValue, (strlen(stringValue)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_leafNode = NULL;
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_023_04");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************remove***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_3", GMC_OPERATION_REMOVE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i + 200;
        PID = 1;
        ret = snprintf(stringValue, 8, "str%03d", fieldValue);
        AW_MACRO_EXPECT_NE_INT(0, ret);
        ret = GmcSetIndexKeyValue(g_stmt_leaf, 1, GMC_DATATYPE_STRING, stringValue, (strlen(stringValue)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_leafNode = NULL;
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_023_05");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 024. leaf-list的字段类型为fixed类型，执行六原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, Yang_040_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert root***********************************/
    fieldValue = 100;
    TestInsertRootTree(g_conn_async, "root_tree", fieldValue);

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_4", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK_Fixed(g_leafNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_leafNode = NULL;
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_024_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************merge***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    char stringValue[10] = {0};
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_4", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i + 100;
        PID = 1;
        ret = snprintf(stringValue, 8, "str%03d", fieldValue);
        AW_MACRO_EXPECT_NE_INT(0, ret);
        ret = GmcSetIndexKeyValue(g_stmt_leaf, 1, GMC_DATATYPE_FIXED, stringValue, 7);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_leafNode = NULL;
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_024_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************replace***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_4", GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i + 200;
        TestYangSetNodeProperty_PK_Fixed(g_leafNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_leafNode = NULL;
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_024_03");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************delete***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_4", GMC_OPERATION_DELETE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i + 100;
        PID = 1;
        ret = snprintf(stringValue, 8, "str%03d", fieldValue);
        AW_MACRO_EXPECT_NE_INT(0, ret);
        ret = GmcSetIndexKeyValue(g_stmt_leaf, 1, GMC_DATATYPE_FIXED, stringValue, 7);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_leafNode = NULL;
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_024_04");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************remove***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_4", GMC_OPERATION_REMOVE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i + 200;
        PID = 1;
        ret = snprintf(stringValue, 8, "str%03d", fieldValue);
        AW_MACRO_EXPECT_NE_INT(0, ret);
        ret = GmcSetIndexKeyValue(g_stmt_leaf, 1, GMC_DATATYPE_FIXED, stringValue, 7);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_leafNode = NULL;
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_024_05");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 025.多线程并发leaf-list六原语操作
 Author       : hanyang
*****************************************************************************/
void *Thread_025_01(void *args)
{
    AW_FUN_Log(LOG_STEP, "==============[1] Yang INSERT start==================\n\n");
    int ret;
    uint32_t fieldValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    GmcConnT *conn_async = NULL;
    GmcStmtT *stmt_root = NULL;
    GmcStmtT *stmt_leaf = NULL;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *childNode = NULL;
    AsyncUserDataT data = {0};

    ret = testGmcConnect(&conn_async, &stmt_root, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn_async, &stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespaceAsync(stmt_root, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_root, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_leaf, "leaf_tree_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt_root, stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_leaf, &childNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(childNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        childNode = NULL;
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = GmcTransCommitAsync(conn_async, trans_commit_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (data.status == GMERR_RESTRICT_VIOLATION) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, data.status);
        AW_FUN_Log(LOG_INFO, "==============[1] Yang INSERT trans unsucc.==================\n\n");
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_FUN_Log(LOG_INFO, "==============[1] Yang INSERT trans succ.==================\n\n");
    }
    AW_FUN_Log(LOG_STEP, "==============[1] Yang INSERT end==================\n\n");

    GmcFreeStmt(stmt_leaf);
    ret = testGmcDisconnect(conn_async, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void *Thread_025_02(void *args)
{
    AW_FUN_Log(LOG_STEP, "==============[2] Yang MERGE start==================\n\n");
    int ret;
    uint32_t fieldValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    GmcConnT *conn_async = NULL;
    GmcStmtT *stmt_root = NULL;
    GmcStmtT *stmt_leaf = NULL;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *childNode = NULL;
    AsyncUserDataT data = {0};

    ret = testGmcConnect(&conn_async, &stmt_root, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn_async, &stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespaceAsync(stmt_root, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_root, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_leaf, "leaf_tree_1", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt_root, stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_leaf, &childNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i + 100;
        PID = 1;
        TestSetKeyNameAndValue(stmt_leaf, fieldValue, PID, true);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        childNode = NULL;
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = GmcTransCommitAsync(conn_async, trans_commit_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (data.status == GMERR_RESTRICT_VIOLATION) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, data.status);
        AW_FUN_Log(LOG_INFO, "==============[2] Yang MERGE trans unsucc.==================\n\n");
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_FUN_Log(LOG_INFO, "==============[2] Yang MERGE trans succ.==================\n\n");
    }
    AW_FUN_Log(LOG_STEP, "==============[2] Yang MERGE end==================\n\n");

    GmcFreeStmt(stmt_leaf);
    ret = testGmcDisconnect(conn_async, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void *Thread_025_03(void *args)
{
    AW_FUN_Log(LOG_STEP, "==============[3] Yang REPLACE start==================\n\n");
    int ret;
    uint32_t fieldValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    GmcConnT *conn_async = NULL;
    GmcStmtT *stmt_root = NULL;
    GmcStmtT *stmt_leaf = NULL;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *childNode = NULL;
    AsyncUserDataT data = {0};

    ret = testGmcConnect(&conn_async, &stmt_root, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn_async, &stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespaceAsync(stmt_root, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_root, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_leaf, "leaf_tree_1", GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt_root, stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_leaf, &childNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i + 200;
        TestYangSetNodeProperty_PK(childNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        childNode = NULL;
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = GmcTransCommitAsync(conn_async, trans_commit_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (data.status == GMERR_RESTRICT_VIOLATION) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, data.status);
        AW_FUN_Log(LOG_INFO, "==============[3] Yang REPLACE trans unsucc.==================\n\n");
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_FUN_Log(LOG_INFO, "==============[3] Yang REPLACE trans succ.==================\n\n");
    }
    AW_FUN_Log(LOG_STEP, "==============[3] Yang REPLACE end==================\n\n");

    GmcFreeStmt(stmt_leaf);
    ret = testGmcDisconnect(conn_async, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// main
TEST_F(LeafList, Yang_040_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************insert***********************************/
    AW_FUN_Log(LOG_STEP, "insert root.");
    fieldValue = 100;
    TestInsertRootTree(g_conn_async, "root_tree", fieldValue);

    /***************************六原语***********************************/
    // 3线程并发
    pthread_t Thread[3] = {0};

    pthread_create(&Thread[0], NULL, Thread_025_01, NULL);
    pthread_create(&Thread[1], NULL, Thread_025_02, NULL);
    pthread_create(&Thread[2], NULL, Thread_025_03, NULL);

    pthread_join(Thread[0], NULL);
    pthread_join(Thread[1], NULL);
    pthread_join(Thread[2], NULL);
    AddWhiteList(GMERR_DATA_EXCEPTION);
    AddWhiteList(GMERR_RESTRICT_VIOLATION);
    AddWhiteList(GMERR_SYNTAX_ERROR);

    // 查询校验值, 乐观加可重复读事务下，同一ns下只有一个事务能提交成功，结果不可控

    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 026. leaf-list有3个字段，建表失败
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, Yang_040_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = {0};

    readJanssonFile("schema_file/leaflist_3fields.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTree, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
    AddWhiteList(GMERR_INVALID_TABLE_DEFINITION);
}

/*****************************************************************************
 Description  : 027. leaf-list下的字段为bitmap类型，建表失败
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, Yang_040_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = {0};

    readJanssonFile("schema_file/leaflist_bitmap.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTree, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
    AddWhiteList(GMERR_INVALID_PROPERTY);
}

/*****************************************************************************
 Description  : 028. 部分打散， leaf-list下有container类型子节点，建表失败
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, Yang_040_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = {0};

    readJanssonFile("schema_file/leaflist_con.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTree, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
    AddWhiteList(GMERR_INVALID_TABLE_DEFINITION);
}

/*****************************************************************************
 Description  : 029. 部分打散，leaf-list下有choice-case类型子节点，建表失败
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, Yang_040_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = {0};

    readJanssonFile("schema_file/leaflist_choice_case.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTree, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
    AddWhiteList(GMERR_INVALID_TABLE_DEFINITION);
}

/*****************************************************************************
 Description  : 030. 部分打散，leaf-list下有list类型子节点，建边失败
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, Yang_040_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = {0};

    readJanssonFile("schema_file/leaflist_list.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTree, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile("schema_file/leaflist_list_edge.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTree, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
    AddWhiteList(GMERR_INVALID_TABLE_DEFINITION);

    // 删除vertex表
    ret = GmcDropVertexLabelAsync(g_stmt_async, "root", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(g_stmt_async, "leaf_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(g_stmt_async, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
}

/*****************************************************************************
 Description  : 031. 部分打散，leaf-list下有leaf-list类型子节点，建边失败
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, Yang_040_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = {0};

    readJanssonFile("schema_file/leaflist_leaflist.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTree, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile("schema_file/leaflist_leaflist_edge.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTree, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
    AddWhiteList(GMERR_INVALID_TABLE_DEFINITION);

    // 删除vertex表
    ret = GmcDropVertexLabelAsync(g_stmt_async, "root", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(g_stmt_async, "leaf_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(g_stmt_async, "leaf_2", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
}

/*****************************************************************************
 Description  : 032. 全打散， leaf-list下有container类型子节点，建边失败
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, DISABLED_Yang_040_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = {0};

    readJanssonFile("schema_file/leaflist_con_graph.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigGraph, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile("schema_file/leaflist_con_edge_graph.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vLabelSchema, g_msConfigGraph, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
    AddWhiteList(GMERR_INVALID_TABLE_DEFINITION);

    // 删除vertex表
    ret = GmcDropVertexLabelAsync(g_stmt_async, "root", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(g_stmt_async, "leaf_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(g_stmt_async, "con_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
}

/*****************************************************************************
 Description  : 033. 全打散，leaf-list下有choice-case类型子节点，建边失败
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, DISABLED_Yang_040_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = {0};

    readJanssonFile("schema_file/leaflist_choice_case_graph.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigGraph, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile("schema_file/leaflist_choice_case_edge_graph.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vLabelSchema, g_msConfigGraph, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
    AddWhiteList(GMERR_INVALID_TABLE_DEFINITION);

    // 删除vertex表
    ret = GmcDropVertexLabelAsync(g_stmt_async, "root", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(g_stmt_async, "leaf_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(g_stmt_async, "choice_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(g_stmt_async, "case_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
}

/*****************************************************************************
 Description  : 034. 全打散，leaf-list下有list类型子节点，建边失败
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, DISABLED_Yang_040_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = {0};

    readJanssonFile("schema_file/leaflist_list_graph.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigGraph, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile("schema_file/leaflist_list_edge_graph.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vLabelSchema, g_msConfigGraph, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
    AddWhiteList(GMERR_INVALID_TABLE_DEFINITION);

    // 删除vertex表
    ret = GmcDropVertexLabelAsync(g_stmt_async, "root", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(g_stmt_async, "leaf_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(g_stmt_async, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
}

/*****************************************************************************
 Description  : 035. 全打散，leaf-list下有leaf-list类型子节点，建边失败
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, DISABLED_Yang_040_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = {0};

    readJanssonFile("schema_file/leaflist_leaflist_graph.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigGraph, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile("schema_file/leaflist_leaflist_edge_graph.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vLabelSchema, g_msConfigGraph, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
    AddWhiteList(GMERR_INVALID_TABLE_DEFINITION);

    // 删除vertex表
    ret = GmcDropVertexLabelAsync(g_stmt_async, "root", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(g_stmt_async, "leaf_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(g_stmt_async, "leaf_2", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
}

/*****************************************************************************
 Description  : 036. 不指定min和max值，创建leaf-list，执行不同的六原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, Yang_040_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 创建表
    TestCreateLabelNoMinMax(g_stmt_async);

    /***************************insert***********************************/
    fieldValue = 100;
    TestInsertRootTree(g_conn_async, "root", fieldValue);
    TestInsertLeafTree(g_conn_async, "root", "leaf_1");

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root", "Yang_040_Func_036_01");

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************merge***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_1", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i + 100;
        PID = 1;
        TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_leafNode = NULL;
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root", "Yang_040_Func_036_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除表
    TestDropLabelNoMinMax(g_stmt_async);
}

/*****************************************************************************
 Description  : 037. 指定min和max值都为0，创建leaf-list，执行不同的六原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, Yang_040_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 创建表
    TestCreateLabelMinMax0(g_stmt_async);

    /***************************insert***********************************/
    fieldValue = 100;
    TestInsertRootTree(g_conn_async, "root", fieldValue);
    TestInsertLeafTree(g_conn_async, "root", "leaf_1");

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root", "Yang_040_Func_037_01");

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************merge***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_1", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i + 100;
        PID = 1;
        TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_leafNode = NULL;
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root", "Yang_040_Func_037_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除表
    TestDropLabelMinMax0(g_stmt_async);
}

/*****************************************************************************
 Description  : 038. 指定min的值大于max的值，创建leaf-list表失败
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, Yang_040_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = {0};

    readJanssonFile("schema_file/leaflist_min_bigger_than_max.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTree, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
    AddWhiteList(GMERR_INVALID_TABLE_DEFINITION);
}

/*****************************************************************************
 Description  : 039. 指定min和max的值为string类型，创建leaf-list表失败
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, Yang_040_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = {0};

    readJanssonFile("schema_file/leaflist_min_max_string.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTree, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATATYPE_MISMATCH, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
    AddWhiteList(GMERR_DATATYPE_MISMATCH);
}

/*****************************************************************************
 Description  : 040. 部分打散，leaf-list作为树模型下的节点，建表失败
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, Yang_040_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = {0};

    readJanssonFile("schema_file/leaflist_node.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTree, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATATYPE_MISMATCH, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
    AddWhiteList(GMERR_DATATYPE_MISMATCH);
}

/*****************************************************************************
 Description  : 041.部分打散，root下创建leaf-list，执行merge操作，指定位置
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, Yang_040_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t keyValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    GmcYangListLocatorT listProp;
    GmcPropValueT refKey;

    /***************************insert root***********************************/
    fieldValue = 100;
    TestInsertRootTree(g_conn_async, "root_tree", fieldValue);

    /***************************merge, GMC_YANG_LIST_POSITION_FIRST***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点, 9-0顺序
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_1", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置插入位置
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_FIRST, NULL);
        ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        UninitListProperty(&listProp);

        // 设置属性值
        fieldValue = i;
        PID = 1;
        TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_leafNode = NULL;
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_002_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************merge, GMC_YANG_LIST_POSITION_BEFORE***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点，在3前面插入100
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    keyValue = 3;
    InitRefKeys(&refKey, 1, &keyValue);
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_BEFORE, &refKey);
    ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    fieldValue = 100;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_leafNode = NULL;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_002_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************merge, GMC_YANG_LIST_POSITION_AFTER***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点，在3后面插入200
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    keyValue = 3;
    InitRefKeys(&refKey, 1, &keyValue);
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_AFTER, &refKey);
    ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    fieldValue = 200;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_leafNode = NULL;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_002_03");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 042.部分打散，root下创建leaf-list，执行merge 更新操作，指定位置
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, Yang_040_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t keyValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    GmcYangListLocatorT listProp;
    GmcPropValueT refKey;

    /***************************insert root***********************************/
    fieldValue = 100;
    TestInsertRootTree(g_conn_async, "root_tree", fieldValue);
    TestInsertLeafTree(g_conn_async, "root_tree", "leaf_tree_1");

    /***************************merge, GMC_YANG_LIST_POSITION_FIRST***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点, 5挪到最前面
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_FIRST, NULL);
    ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    fieldValue = 5;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_leafNode = NULL;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_042_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************merge, GMC_YANG_LIST_POSITION_BEFORE***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点，7挪到3前面
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    keyValue = 3;
    InitRefKeys(&refKey, 1, &keyValue);
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_BEFORE, &refKey);
    ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    fieldValue = 7;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_leafNode = NULL;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_042_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************merge, GMC_YANG_LIST_POSITION_AFTER***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点，2挪到3后面
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    keyValue = 3;
    InitRefKeys(&refKey, 1, &keyValue);
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_AFTER, &refKey);
    ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    fieldValue = 2;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_leafNode = NULL;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_042_03");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 043.部分打散，root下创建leaf-list，执行replace操作，指定位置
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, Yang_040_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t keyValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    GmcYangListLocatorT listProp;
    GmcPropValueT refKey;

    /***************************insert root***********************************/
    fieldValue = 100;
    TestInsertRootTree(g_conn_async, "root_tree", fieldValue);

    /***************************replace, GMC_YANG_LIST_POSITION_FIRST***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点, 9-0顺序
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_1", GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置插入位置
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_FIRST, NULL);
        ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        UninitListProperty(&listProp);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_leafNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_leafNode = NULL;
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_002_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************replace, GMC_YANG_LIST_POSITION_BEFORE***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点，在3前面插入100
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_1", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    keyValue = 3;
    InitRefKeys(&refKey, 1, &keyValue);
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_BEFORE, &refKey);
    ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_leafNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_leafNode = NULL;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_002_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************replace, GMC_YANG_LIST_POSITION_AFTER***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点，在3后面插入200
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_1", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    keyValue = 3;
    InitRefKeys(&refKey, 1, &keyValue);
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_AFTER, &refKey);
    ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodeProperty_PK(g_leafNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_leafNode = NULL;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_002_03");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 044.部分打散，root下创建leaf-list，执行replace 更新操作，指定位置
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, Yang_040_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t keyValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    GmcYangListLocatorT listProp;
    GmcPropValueT refKey;

    /***************************insert root***********************************/
    fieldValue = 100;
    TestInsertRootTree(g_conn_async, "root_tree", fieldValue);
    TestInsertLeafTree(g_conn_async, "root_tree", "leaf_tree_1");

    /***************************replace, GMC_YANG_LIST_POSITION_FIRST***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点, 5挪到最前面
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_1", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_FIRST, NULL);
    ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    fieldValue = 5;
    TestYangSetNodeProperty_PK(g_leafNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_leafNode = NULL;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_044_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************replace, GMC_YANG_LIST_POSITION_BEFORE***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点，7挪到3前面
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_1", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    keyValue = 3;
    InitRefKeys(&refKey, 1, &keyValue);
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_BEFORE, &refKey);
    ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    fieldValue = 7;
    TestYangSetNodeProperty_PK(g_leafNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_leafNode = NULL;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_044_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************replace, GMC_YANG_LIST_POSITION_AFTER***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点，2挪到3后面
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_1", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    keyValue = 3;
    InitRefKeys(&refKey, 1, &keyValue);
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_AFTER, &refKey);
    ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    fieldValue = 2;
    TestYangSetNodeProperty_PK(g_leafNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_leafNode = NULL;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_044_03");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 045.部分打散，root下创建leaf-list，执行delete操作，指定位置
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, Yang_040_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t keyValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    GmcYangListLocatorT listProp;
    GmcPropValueT refKey;

    /***************************insert root***********************************/
    fieldValue = 100;
    TestInsertRootTree(g_conn_async, "root_tree", fieldValue);
    TestInsertLeafTree(g_conn_async, "root_tree", "leaf_tree_1");

    /***************************delete, GMC_YANG_LIST_POSITION_FIRST***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点, 删除5
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_1", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_FIRST, NULL);
    ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    fieldValue = 5;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_leafNode = NULL;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_045_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************delete, GMC_YANG_LIST_POSITION_BEFORE***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点，删除8
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_1", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    keyValue = 3;
    InitRefKeys(&refKey, 1, &keyValue);
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_BEFORE, &refKey);
    ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    fieldValue = 8;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_leafNode = NULL;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_045_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************delete, GMC_YANG_LIST_POSITION_AFTER***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点，删除1
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_1", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    keyValue = 3;
    InitRefKeys(&refKey, 1, &keyValue);
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_AFTER, &refKey);
    ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    fieldValue = 1;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_leafNode = NULL;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_045_03");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 046.部分打散，root下创建leaf-list，执行remove操作，指定位置
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, Yang_040_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t keyValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    GmcYangListLocatorT listProp;
    GmcPropValueT refKey;

    /***************************insert root***********************************/
    fieldValue = 100;
    TestInsertRootTree(g_conn_async, "root_tree", fieldValue);
    TestInsertLeafTree(g_conn_async, "root_tree", "leaf_tree_1");

    /***************************remove, GMC_YANG_LIST_POSITION_FIRST***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点, 删除5
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_1", GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_FIRST, NULL);
    ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    fieldValue = 5;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_leafNode = NULL;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_045_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************remove, GMC_YANG_LIST_POSITION_BEFORE***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点，删除8
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_1", GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    keyValue = 3;
    InitRefKeys(&refKey, 1, &keyValue);
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_BEFORE, &refKey);
    ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    fieldValue = 8;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_leafNode = NULL;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_045_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************remove, GMC_YANG_LIST_POSITION_AFTER***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点，删除1
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_1", GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    keyValue = 3;
    InitRefKeys(&refKey, 1, &keyValue);
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_AFTER, &refKey);
    ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    fieldValue = 1;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_leafNode = NULL;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_045_03");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 047.全打散，root下创建leaf-list，执行merge操作，指定位置
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, DISABLED_Yang_040_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t keyValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    GmcYangListLocatorT listProp;
    GmcPropValueT refKey;

    /***************************insert root***********************************/
    fieldValue = 100;
    TestInsertRootGraph(g_conn_async, "root_graph", fieldValue);

    /***************************merge, GMC_YANG_LIST_POSITION_FIRST***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_1", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置插入位置
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_FIRST, NULL);
        ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        UninitListProperty(&listProp);

        // 设置属性值
        fieldValue = i;
        PID = 1;
        TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_graph", "Yang_040_Func_013_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************merge, GMC_YANG_LIST_POSITION_BEFORE***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    keyValue = 3;
    InitRefKeys(&refKey, 1, &keyValue);
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_BEFORE, &refKey);
    ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    fieldValue = 100;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_graph", "Yang_040_Func_013_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************merge, GMC_YANG_LIST_POSITION_AFTER***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    keyValue = 3;
    InitRefKeys(&refKey, 1, &keyValue);
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_AFTER, &refKey);
    ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    fieldValue = 200;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_graph", "Yang_040_Func_013_03");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 048.全打散，root下创建leaf-list，执行merge 更新操作，指定位置
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, DISABLED_Yang_040_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t keyValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    GmcYangListLocatorT listProp;
    GmcPropValueT refKey;

    /***************************insert root***********************************/
    fieldValue = 100;
    TestInsertRootGraph(g_conn_async, "root_graph", fieldValue);
    TestInsertLeafGraph(g_conn_async, "root_graph", "leaf_graph_1");

    /***************************merge, GMC_YANG_LIST_POSITION_FIRST***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点, 5挪到最前面
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_FIRST, NULL);
    ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    fieldValue = 5;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_graph", "Yang_040_Func_048_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************merge, GMC_YANG_LIST_POSITION_BEFORE***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点，7挪到3前面
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    keyValue = 3;
    InitRefKeys(&refKey, 1, &keyValue);
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_BEFORE, &refKey);
    ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    fieldValue = 7;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_graph", "Yang_040_Func_048_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************merge, GMC_YANG_LIST_POSITION_AFTER***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点，2挪到3后面
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    keyValue = 3;
    InitRefKeys(&refKey, 1, &keyValue);
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_AFTER, &refKey);
    ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    fieldValue = 2;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_graph", "Yang_040_Func_048_03");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 049.全打散，root下创建leaf-list，执行replace操作，指定位置
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, DISABLED_Yang_040_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t keyValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    GmcYangListLocatorT listProp;
    GmcPropValueT refKey;

    /***************************insert root***********************************/
    fieldValue = 100;
    TestInsertRootGraph(g_conn_async, "root_graph", fieldValue);

    /***************************replace, GMC_YANG_LIST_POSITION_FIRST***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_1", GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置插入位置
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_FIRST, NULL);
        ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        UninitListProperty(&listProp);

        // 设置属性值
        fieldValue = i;
        TestYangSetVertexProperty_PK(g_stmt_leaf, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_graph", "Yang_040_Func_013_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************replace, GMC_YANG_LIST_POSITION_BEFORE***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_1", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    keyValue = 3;
    InitRefKeys(&refKey, 1, &keyValue);
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_BEFORE, &refKey);
    ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    fieldValue = 100;
    TestYangSetVertexProperty_PK(g_stmt_leaf, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_graph", "Yang_040_Func_013_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************replace, GMC_YANG_LIST_POSITION_AFTER***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_1", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    keyValue = 3;
    InitRefKeys(&refKey, 1, &keyValue);
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_AFTER, &refKey);
    ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    fieldValue = 200;
    TestYangSetVertexProperty_PK(g_stmt_leaf, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_graph", "Yang_040_Func_013_03");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 050.全打散，root下创建leaf-list，执行replace 更新操作，指定位置
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, DISABLED_Yang_040_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t keyValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    GmcYangListLocatorT listProp;
    GmcPropValueT refKey;

    /***************************insert root***********************************/
    fieldValue = 100;
    TestInsertRootGraph(g_conn_async, "root_graph", fieldValue);
    TestInsertLeafGraph(g_conn_async, "root_graph", "leaf_graph_1");

    /***************************replace, GMC_YANG_LIST_POSITION_FIRST***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点, 5挪到最前面
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_1", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_FIRST, NULL);
    ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    fieldValue = 5;
    TestYangSetVertexProperty_PK(g_stmt_leaf, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_graph", "Yang_040_Func_050_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************replace, GMC_YANG_LIST_POSITION_BEFORE***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点，7挪到3前面
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_1", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    keyValue = 3;
    InitRefKeys(&refKey, 1, &keyValue);
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_BEFORE, &refKey);
    ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    fieldValue = 7;
    TestYangSetVertexProperty_PK(g_stmt_leaf, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_graph", "Yang_040_Func_050_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************replace, GMC_YANG_LIST_POSITION_AFTER***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点，2挪到3后面
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_1", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    keyValue = 3;
    InitRefKeys(&refKey, 1, &keyValue);
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_AFTER, &refKey);
    ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    fieldValue = 2;
    TestYangSetVertexProperty_PK(g_stmt_leaf, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_graph", "Yang_040_Func_050_03");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 051.全打散，root下创建leaf-list，执行delete操作，指定位置
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, DISABLED_Yang_040_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t keyValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    GmcYangListLocatorT listProp;
    GmcPropValueT refKey;

    /***************************insert root***********************************/
    fieldValue = 100;
    TestInsertRootGraph(g_conn_async, "root_graph", fieldValue);
    TestInsertLeafGraph(g_conn_async, "root_graph", "leaf_graph_1");

    /***************************delete, GMC_YANG_LIST_POSITION_FIRST***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点, 删除5
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_1", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_FIRST, NULL);
    ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    fieldValue = 5;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_graph", "Yang_040_Func_051_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************delete, GMC_YANG_LIST_POSITION_BEFORE***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点，删除8
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_1", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    keyValue = 3;
    InitRefKeys(&refKey, 1, &keyValue);
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_BEFORE, &refKey);
    ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    fieldValue = 8;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_graph", "Yang_040_Func_051_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************delete, GMC_YANG_LIST_POSITION_AFTER***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点，删除1
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_1", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    keyValue = 3;
    InitRefKeys(&refKey, 1, &keyValue);
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_AFTER, &refKey);
    ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    fieldValue = 1;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_graph", "Yang_040_Func_051_03");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 052.全打散，root下创建leaf-list，执行remove操作，指定位置
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, DISABLED_Yang_040_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t keyValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    GmcYangListLocatorT listProp;
    GmcPropValueT refKey;

    /***************************insert root***********************************/
    fieldValue = 100;
    TestInsertRootGraph(g_conn_async, "root_graph", fieldValue);
    TestInsertLeafGraph(g_conn_async, "root_graph", "leaf_graph_1");

    /***************************remove, GMC_YANG_LIST_POSITION_FIRST***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点, 删除5
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_1", GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_FIRST, NULL);
    ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    fieldValue = 5;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_graph", "Yang_040_Func_051_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************remove, GMC_YANG_LIST_POSITION_BEFORE***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点，删除8
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_1", GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    keyValue = 3;
    InitRefKeys(&refKey, 1, &keyValue);
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_BEFORE, &refKey);
    ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    fieldValue = 8;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_graph", "Yang_040_Func_051_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************remove, GMC_YANG_LIST_POSITION_AFTER***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_graph", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点，删除1
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_graph_1", GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    keyValue = 3;
    InitRefKeys(&refKey, 1, &keyValue);
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_AFTER, &refKey);
    ret = GmcYangSetListLocator(g_stmt_leaf, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    fieldValue = 1;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_graph", "Yang_040_Func_051_03");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 053.string类型的leaflist字段，写入字母相同，大小写不同的内容
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, Yang_040_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert root***********************************/
    fieldValue = 100;
    TestInsertRootTree(g_conn_async, "root_tree", fieldValue);

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_3", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    char stringValue1[10] = "strAAA";
    ret = testYangSetNodeField(g_leafNode, GMC_DATATYPE_STRING, stringValue1, (strlen(stringValue1)), "F0",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_leafNode = NULL;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_053_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************merge***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    char stringValue[10] = {0};
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_3", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    char stringValue2[10] = "strAAa";
    ret = GmcSetIndexKeyValue(g_stmt_leaf, 1, GMC_DATATYPE_STRING, stringValue2, (strlen(stringValue2)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_leafNode = NULL;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_053_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************replace***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_3", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    char stringValue3[10] = "strAaa";
    ret = testYangSetNodeField(g_leafNode, GMC_DATATYPE_STRING, stringValue3, (strlen(stringValue3)), "F0",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_leafNode = NULL;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_053_03");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************delete***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_3", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    char stringValue4[10] = "strAAa";
    ret = GmcSetIndexKeyValue(g_stmt_leaf, 1, GMC_DATATYPE_STRING, stringValue4, (strlen(stringValue4)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_leafNode = NULL;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_053_04");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************remove***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_3", GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    char stringValue5[10] = "strAaa";
    ret = GmcSetIndexKeyValue(g_stmt_leaf, 1, GMC_DATATYPE_STRING, stringValue5, (strlen(stringValue5)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_leafNode = NULL;


    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_053_05");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 054.leaflist设置默认值，且nullable为false，五原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, Yang_040_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert root***********************************/
    fieldValue = 100;
    TestInsertRootTree(g_conn_async, "root_tree", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_054_01", GMC_DEFAULT_FILTER_REPORT_ALL);

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    /***************************五原语操作，create/merge/replace***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_8", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_leafNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_leafNode = NULL;

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_8", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodeProperty_PK(g_leafNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_leafNode = NULL;

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_8", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 300;
    TestYangSetNodeProperty_PK(g_leafNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_leafNode = NULL;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(4, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(4, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_054_02", GMC_DEFAULT_FILTER_REPORT_ALL);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************五原语操作，delete/remove***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_8", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    GmcPropValueT propValue;
    memcpy(propValue.propertyName, "F0", (strlen("F0") + 1));
    propValue.type = GMC_DATATYPE_UINT32;
    propValue.value = &fieldValue;
    propValue.size = sizeof(uint32_t);
    ret = GmcYangSetNodeProperty(g_leafNode, &propValue, GMC_YANG_PROPERTY_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    g_leafNode = NULL;

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_8", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 300;
    memcpy(propValue.propertyName, "F0", (strlen("F0") + 1));
    propValue.type = GMC_DATATYPE_UINT32;
    propValue.value = &fieldValue;
    propValue.size = sizeof(uint32_t);
    ret = GmcYangSetNodeProperty(g_leafNode, &propValue, GMC_YANG_PROPERTY_OPERATION_REMOVE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    AddWhiteList(GMERR_DATA_EXCEPTION);
    AddWhiteList(GMERR_SYNTAX_ERROR);
    g_leafNode = NULL;

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_8", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_leafNode = NULL;

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_8", GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 300;
    TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_leafNode = NULL;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_054_03", GMC_DEFAULT_FILTER_REPORT_ALL);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 055.leaflist存在数据，执行none原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, Yang_040_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t keyValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    GmcYangListLocatorT listProp;
    GmcPropValueT refKey;

    /***************************insert root***********************************/
    fieldValue = 100;
    TestInsertRootTree(g_conn_async, "root_tree", fieldValue);
    TestInsertLeafTree(g_conn_async, "root_tree", "leaf_tree_1");

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_001_01");

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************none***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_1", GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i + 100;
        PID = 1;
        TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_leafNode = NULL;
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_001_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 056.leaflist不存在数据，执行none原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, Yang_040_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t keyValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    GmcYangListLocatorT listProp;
    GmcPropValueT refKey;

    /***************************insert root***********************************/
    fieldValue = 100;
    TestInsertRootTree(g_conn_async, "root_tree", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_056_01");

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************none***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_8", GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i + 100;
        PID = 1;
        TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_leaf);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_leafNode = NULL;
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_056_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 057.有默认值，将leaflist字段置为NULL
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, Yang_040_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t keyValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    GmcYangListLocatorT listProp;
    GmcPropValueT refKey;

    /***************************insert root***********************************/
    fieldValue = 100;
    TestInsertRootTree(g_conn_async, "root_tree", fieldValue);

    /***************************none***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_8", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    ret = testYangSetNodeField(g_leafNode, GMC_DATATYPE_NULL, &fieldValue, sizeof(uint32_t), "F0",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    AddWhiteList(GMERR_DATA_EXCEPTION);
    g_leafNode = NULL;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_057_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 058.无默认值，将leaflist字段置为NULL
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, Yang_040_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t keyValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    GmcYangListLocatorT listProp;
    GmcPropValueT refKey;

    /***************************insert root***********************************/
    fieldValue = 100;
    TestInsertRootTree(g_conn_async, "root_tree", fieldValue);

    /***************************none***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    ret = testYangSetNodeField(g_leafNode, GMC_DATATYPE_NULL, &fieldValue, sizeof(uint32_t), "F0",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    AddWhiteList(GMERR_DATA_EXCEPTION);
    g_leafNode = NULL;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_057_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 059.有默认值，将leaflist字段删除
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, Yang_040_059)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t keyValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    GmcYangListLocatorT listProp;
    GmcPropValueT refKey;

    /***************************insert root***********************************/
    fieldValue = 100;
    TestInsertRootTree(g_conn_async, "root_tree", fieldValue);

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_8", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_leafNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_leafNode = NULL;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_059_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************删除字段***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_8", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    ret = testYangSetNodeField(g_leafNode, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F0",
        GMC_YANG_PROPERTY_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    AddWhiteList(GMERR_DATA_EXCEPTION);
    g_leafNode = NULL;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_059_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 060.无默认值，将leaflist字段删除
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, Yang_040_060)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t keyValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    GmcYangListLocatorT listProp;
    GmcPropValueT refKey;

    /***************************insert root***********************************/
    fieldValue = 100;
    TestInsertRootTree(g_conn_async, "root_tree", fieldValue);

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_leafNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_leafNode = NULL;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_060_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************删除字段***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    ret = testYangSetNodeField(g_leafNode, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F0",
        GMC_YANG_PROPERTY_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    AddWhiteList(GMERR_DATA_EXCEPTION);
    g_leafNode = NULL;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_060_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 061.leaflist中写入数据，subtree查询时指定leaflist中的字段为NULL
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, Yang_040_061)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert ***********************************/
    fieldValue = 100;
    TestInsertRootTree(g_conn_async, "root_tree", fieldValue);
    TestInsertLeafTree(g_conn_async, "root_tree", "leaf_tree_8");
    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree 查询, 指定leaflist中的字段为NULL
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root_tree", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "leaf_tree_8", GMC_OPERATION_SUBTREE_FILTER, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetNodeField(g_leafNode, GMC_DATATYPE_NULL, NULL, sizeof(uint32_t),
        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // report-all查询
    testSubtreeFilterIF(g_stmt_async, g_rootNode, "Yang_040_Func_061_01", "root_tree",
        GMC_DEFAULT_FILTER_REPORT_ALL);

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 062.leaflist中写入数据，subtree查询时指定leaflist中的字段为某个值
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, Yang_040_062)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert ***********************************/
    fieldValue = 100;
    TestInsertRootTree(g_conn_async, "root_tree", fieldValue);
    TestInsertLeafTree(g_conn_async, "root_tree", "leaf_tree_8");
    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree 查询, 指定leaflist中的字段为1
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root_tree", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "leaf_tree_8", GMC_OPERATION_SUBTREE_FILTER, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    fieldValue = 1;
    ret = testYangSetNodeField(g_leafNode, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // report-all查询
    testSubtreeFilterIF(g_stmt_async, g_rootNode, "Yang_040_Func_062_01", "root_tree",
        GMC_DEFAULT_FILTER_REPORT_ALL);

    // subtree 查询, 指定leaflist中的字段为666
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root_tree", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "leaf_tree_8", GMC_OPERATION_SUBTREE_FILTER, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    fieldValue = 666;
    ret = testYangSetNodeField(g_leafNode, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // report-all查询
    testSubtreeFilterIF(g_stmt_async, g_rootNode, "Yang_040_Func_062_02", "root_tree",
        GMC_DEFAULT_FILTER_REPORT_ALL);

    // subtree 查询, 指定leaflist中的字段为666
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root_tree", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "leaf_tree_8", GMC_OPERATION_SUBTREE_FILTER, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    fieldValue = 666;
    ret = testYangSetNodeField(g_leafNode, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // trim查询
    testSubtreeFilterIF(g_stmt_async, g_rootNode, "Yang_040_Func_062_03", "root_tree",
        GMC_DEFAULT_FILTER_TRIM);

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 063.leaflist中写入数据，subtree查询时指定两个leaflsit字段“与”关系
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, Yang_040_063)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert ***********************************/
    fieldValue = 100;
    TestInsertRootTree(g_conn_async, "root_tree", fieldValue);
    TestInsertLeafTree(g_conn_async, "root_tree", "leaf_tree_1");
    TestInsertLeafTree(g_conn_async, "root_tree", "leaf_tree_8");
    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree 查询, 指定leaflist中的字段与条件
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root_tree", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "leaf_tree_8", GMC_OPERATION_SUBTREE_FILTER, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    fieldValue = 1;
    ret = testYangSetNodeField(g_leafNode, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "leaf_tree_1", GMC_OPERATION_SUBTREE_FILTER, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    fieldValue = 3;
    ret = testYangSetNodeField(g_leafNode, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // report-all查询
    testSubtreeFilterIF(g_stmt_async, g_rootNode, "Yang_040_Func_063_01", "root_tree",
        GMC_DEFAULT_FILTER_REPORT_ALL);

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 064.leaflist中写入数据，subtree查询时指定两个leaflsit字段“或”关系
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, Yang_040_064)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert ***********************************/
    fieldValue = 100;
    TestInsertRootTree(g_conn_async, "root_tree", fieldValue);
    TestInsertLeafTree(g_conn_async, "root_tree", "leaf_tree_8");
    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree 查询, 指定leaflist中的字段或条件
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root_tree", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "leaf_tree_8", GMC_OPERATION_SUBTREE_FILTER, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    fieldValue = 1;
    ret = testYangSetNodeField(g_leafNode, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "leaf_tree_8", GMC_OPERATION_SUBTREE_FILTER, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    fieldValue = 3;
    ret = testYangSetNodeField(g_leafNode, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "leaf_tree_8", GMC_OPERATION_SUBTREE_FILTER, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    fieldValue = 777;
    ret = testYangSetNodeField(g_leafNode, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "leaf_tree_8", GMC_OPERATION_SUBTREE_FILTER, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    fieldValue = 888;
    ret = testYangSetNodeField(g_leafNode, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // report-all查询
    testSubtreeFilterIF(g_stmt_async, g_rootNode, "Yang_040_Func_064_01", "root_tree",
        GMC_DEFAULT_FILTER_REPORT_ALL);

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 065.leaflist中写入数据，subtree查询时指定leaflist为NULL
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, Yang_040_065)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert ***********************************/
    fieldValue = 100;
    TestInsertRootTree(g_conn_async, "root_tree", fieldValue);
    TestInsertLeafTree(g_conn_async, "root_tree", "leaf_tree_8");
    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree 查询, 指定leaflist中的字段为NULL
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root_tree", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "leaf_tree_8", GMC_OPERATION_SUBTREE_FILTER, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // report-all查询
    testSubtreeFilterIF(g_stmt_async, g_rootNode, "Yang_040_Func_061_01", "root_tree",
        GMC_DEFAULT_FILTER_REPORT_ALL);

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 066.一次批操作中，对同一个value先后执行create、merge、replace操作，操作成功
 Author       : hanyang
*****************************************************************************/
TEST_F(LeafList, Yang_040_066)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t keyValue;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert root***********************************/
    fieldValue = 100;
    TestInsertRootTree(g_conn_async, "root_tree", fieldValue);

    /***************************create、merge、replace***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_tree", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点 insert
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 3;
    TestYangSetNodeProperty_PK(g_leafNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_leafNode = NULL;

    // 设置child节点 merge
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 3;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_leaf, fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_leafNode = NULL;

    // 设置child节点 replace
    ret = testGmcPrepareStmtByLabelName(g_stmt_leaf, "leaf_tree_1", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_leaf, &g_leafNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 3;
    TestYangSetNodeProperty_PK(g_leafNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_leaf);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_leafNode = NULL;

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(4, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(4, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    testSubtreeFilter(g_stmt_async, "root_tree", "Yang_040_Func_066_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
