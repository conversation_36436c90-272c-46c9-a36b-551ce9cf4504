/*
02 yang支持枚举 DML操作
001 container节点，Insert写入枚举字段，diff查询，subtree查询
002 container节点，Merge写入和Merge更新枚举字段，diff查询，subtree查询
003 container节点，Replace写入和Replace更新枚举字段，diff查询，subtree查询
004 container节点，Delete 枚举字段，diff查询，subtree查询
005 container节点，Remove 枚举字段，diff查询，subtree查询
006 枚举字段作为list主键，Insert数据，diff查询，subtree查询
007 枚举字段作为list主键，Merge数据，diff查询，subtree查询
008 枚举字段作为list主键，Replace数据，diff查询，subtree查询
009 枚举字段作为list主键，Delete数据，diff查询，subtree查询
010 枚举字段作为list主键，Remove数据，diff查询，subtree查询
011 枚举字段作为list主键，改变list节点元素顺序，diff查询，subtree查询
012 枚举字段作为list主键，多个元素Insert和merge相同数据，验证唯一性，同时查询ErrorPath
013 枚举字段作为list唯一localhash，多个元素Insert和merge相同数据，验证唯一性，同时查询ErrorPath
014 两个枚举字段的enumrate相同，都作为list的主键，写入相同的值
015 枚举字段作为leaflist主键，Insert数据，diff查询，subtree查询
016 枚举字段作为leaflist主键，Merge数据，diff查询，subtree查询
017 枚举字段作为leaflist主键，Replace数据，diff查询，subtree查询
018 枚举字段作为leaflist主键，Delete数据，diff查询，subtree查询
019 枚举字段作为leaflist主键，Remove数据，diff查询，subtree查询
020 全打散建表，含有多个枚举字段，DML操作正常，diff查询，subtree查询
021 container节点，Insert写入枚举字段，写入value值，diff查询，subtree查询
022 枚举字段作为list主键，Insert数据，写入value值，diff查询，subtree查询
023 枚举字段作为list主键，Merge数据，写入value值，diff查询，subtree查询
024 枚举字段作为leaflist主键，Insert数据，写入value值，diff查询，subtree查询
025 并发操作写入Enum字段数据
026 Enum字段作为list主键，Insert数据条数超过enumerate数量
027 Enum字段作为leaflist主键，Insert个数等于enumerate数量，插入新数据报错

03 yang支持枚举subtree操作
028 NP节点，枚举字段有默认值，subtree查询，写入和默认值相同的数据，subtree查询，写入和默认值不同的数据，subtree查询，删除字段数据，subtree查询
029 P节点，枚举字段有默认值，subtree查询，写入和默认值相同的数据，subtree查询，写入和默认值不同的数据，subtree查询，删除字段数据，subtree查询
030 leaflist节点，枚举字段有默认值，subtree查询，写入和默认值相同的数据，subtree查询，写入和默认值不同的数据，subtree查询，删除写入的数据，subtree查询
031 NP节点，枚举字段有默认值，when校验后默认值可见，subtree查询，when校验后默认值不可见，subtree查询
032 P节点，枚举字段有默认值，when校验后默认值可见，subtree查询，when校验后默认值不可见，subtree查询
033 leaflist节点，枚举字段有默认值，when校验后默认值可见，subtree查询，when校验后默认值不可见，subtree查询
034 设置subtree查询条件时，写入枚举字段的value，操作成功

04 yang支持枚举ErrPath
035 list节点，枚举字段为主键删除不存在的节点，查询ErrorPath
036 case节点，枚举字段为case下的字段，删除不存在的字段值，查询ErrorPath
037 不符合must校验，查询ErrorPath
038 不符合leafref校验，查询ErrorPath

05 yang支持枚举 DFX
039 subtree子树过滤查询YangTreeschema模型，返回结果正确

06 yang支持枚举 XPath
040 枚举字段作为xpath条件，与字符串比较，进行xpath校验
041 枚举字段作为xpath条件，与数字比较，进行xpath校验，模型校验报错
*/
#include "YangSupportEnum.h"
#include "DiffResult.h"

class YangEnumFunc : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void YangEnumFunc::SetUpTestCase()
{
    int ret;
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void YangEnumFunc::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

void YangEnumFunc::SetUp()
{
    int ret;
    AsyncUserDataT data = {0};

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t i = 0;
    for (i = 0; i < 10; i++) {
        ret = GmcAllocStmt(g_conn_async, &g_stmt_list[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 异步创建namespace
    (void)snprintf(g_cmd, MAX_CMD_SIZE, "%s/gmsysview -q V\\$CATA_NAMESPACE_INFO | grep 'NAMESPACE_NAME'", g_toolPath);
    ret = executeCommand(g_cmd, "NAMESPACE_NAME: NamespaceABC076");
    if (ret == GMERR_OK) {
        AW_FUN_Log(LOG_DEBUG, "NamespaceABC076有残留");
        ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 删除vertex表
        ret = GmcClearNamespaceAsync(g_stmt_async, g_namespace, drop_namespace_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 异步删除namespace
        ret = GmcDropNamespaceAsync(g_stmt_async, g_namespace, drop_namespace_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        memset(&data, 0, sizeof(AsyncUserDataT));
    }
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = g_namespace;
    nspCfg.tablespaceName = NULL;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcClearNamespaceAsync(g_stmt_async, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 创建表
    TestCreateLabel(g_stmt_async);

    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
}

void YangEnumFunc::TearDown()
{
    int ret;
    uint32_t i = 0;
    AsyncUserDataT data = {0};
    AddWhiteList(GMERR_NO_DATA);
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    AW_CHECK_LOG_END();

    // 删除表
    ret = GmcClearNamespaceAsync(g_stmt_async, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, g_namespace, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    GmcFreeStmt(g_stmt_root);
    for (i = 0; i < 10; i++) {
        GmcFreeStmt(g_stmt_list[i]);
    }
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_conn_async = NULL;
    g_stmt_async = NULL;
    g_stmt_root = NULL;
    for (i = 0; i < 10; i++) {
        g_stmt_list[i] = NULL;
    }
    g_rootNode = NULL;
    for (i = 0; i < 10; i++) {
        g_childNode[i] = NULL;
    }
}

// 001 container节点，Insert写入枚举字段，diff查询，subtree查询
TEST_F(YangEnumFunc, Yang_076_Func_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[1], "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_childNode[1], "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[1], "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff021, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_1", "Yang_076_Func_001_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test stop.");

}

// 002 container节点，Merge写入和Merge更新枚举字段，diff查询，subtree查询
TEST_F(YangEnumFunc, Yang_076_Func_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************merge insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_MERGE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    TestYangSetIDName(g_childNode[1], "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_MERGE);
    idValue = -2;
    TestYangSetIDValue(g_childNode[1], "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    TestYangSetIDName(g_childNode[1], "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff021, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_1", "Yang_076_Func_001_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************merge update***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetIDName(g_rootNode, "ID1", "level2", strlen("level2"), GMC_YANG_PROPERTY_OPERATION_MERGE);
    idValue = -3;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    TestYangSetIDName(g_rootNode, "ID3", "level-1", strlen("level-1"), GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetIDName(g_childNode[1], "ID1", "level2", strlen("level2"), GMC_YANG_PROPERTY_OPERATION_MERGE);
    idValue = -3;
    TestYangSetIDValue(g_childNode[1], "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    TestYangSetIDName(g_childNode[1], "ID3", "level-1", strlen("level-1"), GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff022, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_1", "Yang_076_Func_002_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test stop.");
}

// 003 container节点，Replace写入和Replace更新枚举字段，diff查询，subtree查询
TEST_F(YangEnumFunc, Yang_076_Func_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************replace insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_REPLACE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    TestYangSetIDName(g_childNode[1], "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_REPLACE);
    idValue = -2;
    TestYangSetIDValue(g_childNode[1], "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    TestYangSetIDName(g_childNode[1], "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff021, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_1", "Yang_076_Func_001_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************replace update***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值，ID3不写数据，Replace后变为默认值
    TestYangSetIDName(g_rootNode, "ID1", "level2", strlen("level2"), GMC_YANG_PROPERTY_OPERATION_REPLACE);
    idValue = -3;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值，ID3不写数据，Replace后变为默认值
    TestYangSetIDName(g_childNode[1], "ID1", "level2", strlen("level2"), GMC_YANG_PROPERTY_OPERATION_REPLACE);
    idValue = -3;
    TestYangSetIDValue(g_childNode[1], "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff023, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_1", "Yang_076_Func_003_01");
 
    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   AW_FUN_Log(LOG_STEP, "test stop.");
}

// 004 container节点，Delete 枚举字段，diff查询，subtree查询
TEST_F(YangEnumFunc, Yang_076_Func_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[1], "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_childNode[1], "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[1], "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff021, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_1", "Yang_076_Func_001_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************delete***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_DELETE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_DELETE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_DELETE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetIDName(g_childNode[1], "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_DELETE);
    idValue = -2;
    TestYangSetIDValue(g_childNode[1], "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_DELETE);
    TestYangSetIDName(g_childNode[1], "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_DELETE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff024, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_1", "Yang_076_Func_004_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test stop.");
}

// 005 container节点，Remove 枚举字段，diff查询，subtree查询
TEST_F(YangEnumFunc, Yang_076_Func_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[1], "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_childNode[1], "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[1], "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff021, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_1", "Yang_076_Func_001_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************remove***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_REMOVE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_REMOVE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_REMOVE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetIDName(g_childNode[1], "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_REMOVE);
    idValue = -2;
    TestYangSetIDValue(g_childNode[1], "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_REMOVE);
    TestYangSetIDName(g_childNode[1], "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_REMOVE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff024, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_1", "Yang_076_Func_004_01");
 
    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   AW_FUN_Log(LOG_STEP, "test stop.");
}

// 006 枚举字段作为list主键，Insert数据，diff查询，subtree查询
TEST_F(YangEnumFunc, Yang_076_Func_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[1], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[1], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[1], "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值，只有PKID2写入不同的值
        TestYangSetIDName(g_childNode[2], "PKID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        idValue = i;
        TestYangSetIDValue(g_childNode[2], "PKID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[2], "PKID3", "level-1", strlen("level-1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff026, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_2", "Yang_076_Func_006_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test stop.");
}

// 007 枚举字段作为list主键，Merge数据，diff查询，subtree查询
TEST_F(YangEnumFunc, Yang_076_Func_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************merge insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_MERGE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_1", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        char idName[10] = {0};
        ret = snprintf(idName, 10, "level%d", i);
        TestSetKeyNameAndValueIDName1(g_stmt_list[1], (const char *)idName, strlen(idName));
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
        TestYangSetIDName(g_childNode[1], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_MERGE);
        TestYangSetIDName(g_childNode[1], "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_MERGE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2_2", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值，只有PKID2写入不同的值
        char idName[10] = {0};
        ret = snprintf(idName, 10, "level%d", i);
        TestSetKeyNameAndValueIDName3(g_stmt_list[2], "level1", strlen("level1"), (const char *)idName, strlen(idName),
            "level-1", strlen("level-1"));

        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff026, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_2", "Yang_076_Func_006_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************merge update***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetIDName(g_rootNode, "ID1", "level2", strlen("level2"), GMC_YANG_PROPERTY_OPERATION_MERGE);
    idValue = -3;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    TestYangSetIDName(g_rootNode, "ID3", "level-1", strlen("level-1"), GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_1", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        char idName[10] = {0};
        ret = snprintf(idName, 10, "level%d", i);
        TestSetKeyNameAndValueIDName1(g_stmt_list[1], (const char *)idName, strlen(idName));

        fieldValue = 200;
        TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
        TestYangSetIDName(g_childNode[1], "ID2", "level3", strlen("level3"), GMC_YANG_PROPERTY_OPERATION_MERGE);
        TestYangSetIDName(g_childNode[1], "ID3", "level3", strlen("level3"), GMC_YANG_PROPERTY_OPERATION_MERGE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2_2", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值，只有PKID2写入不同的值
        char idName[10] = {0};
        ret = snprintf(idName, 10, "level%d", i);
        TestSetKeyNameAndValueIDName3(g_stmt_list[2], "level1", strlen("level1"), (const char *)idName, strlen(idName),
            "level-1", strlen("level-1"));

        fieldValue = 200;
        TestYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff027, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_2", "Yang_076_Func_007_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test stop.");
}

// 008 枚举字段作为list主键，Replace数据，diff查询，subtree查询
TEST_F(YangEnumFunc, Yang_076_Func_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************replace insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_REPLACE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_1", GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[1], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        TestYangSetIDName(g_childNode[1], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_REPLACE);
        TestYangSetIDName(g_childNode[1], "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2_2", GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值，只有PKID2写入不同的值
        TestYangSetIDName(g_childNode[2], "PKID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_REPLACE);
        idValue = i;
        TestYangSetIDValue(g_childNode[2], "PKID2", idValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        TestYangSetIDName(g_childNode[2], "PKID3", "level-1", strlen("level-1"), GMC_YANG_PROPERTY_OPERATION_REPLACE);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff026, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_2", "Yang_076_Func_006_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************replace update***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_1", GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[1], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        fieldValue = 200;
        TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        TestYangSetIDName(g_childNode[1], "ID2", "level3", strlen("level3"), GMC_YANG_PROPERTY_OPERATION_REPLACE);
        TestYangSetIDName(g_childNode[1], "ID3", "level3", strlen("level3"), GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2_2", GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值，只有PKID2写入不同的值
        TestYangSetIDName(g_childNode[2], "PKID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_REPLACE);
        idValue = i;
        TestYangSetIDValue(g_childNode[2], "PKID2", idValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        TestYangSetIDName(g_childNode[2], "PKID3", "level-1", strlen("level-1"), GMC_YANG_PROPERTY_OPERATION_REPLACE);
        fieldValue = 200;
        TestYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff028, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_2", "Yang_076_Func_008_01");
 
    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   AW_FUN_Log(LOG_STEP, "test stop.");
}

// 009 枚举字段作为list主键，Delete数据，diff查询，subtree查询
TEST_F(YangEnumFunc, Yang_076_Func_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[1], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[1], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[1], "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值，只有PKID2写入不同的值
        TestYangSetIDName(g_childNode[2], "PKID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        idValue = i;
        TestYangSetIDValue(g_childNode[2], "PKID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[2], "PKID3", "level-1", strlen("level-1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff026, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_2", "Yang_076_Func_006_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************delete***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 1; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_1", GMC_OPERATION_DELETE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestSetKeyNameAndValueIDValue1(g_stmt_list[1], idValue);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 1; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2_2", GMC_OPERATION_DELETE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值，只有PKID2写入不同的值
        idValue = i;
        TestSetKeyNameAndValueIDValue3(g_stmt_list[2], 1, idValue, -1);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(5, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(5, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff029, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_2", "Yang_076_Func_009_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test stop.");
}

// 010 枚举字段作为list主键，Remove数据，diff查询，subtree查询
TEST_F(YangEnumFunc, Yang_076_Func_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[1], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[1], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[1], "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值，只有PKID2写入不同的值
        TestYangSetIDName(g_childNode[2], "PKID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        idValue = i;
        TestYangSetIDValue(g_childNode[2], "PKID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[2], "PKID3", "level-1", strlen("level-1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff026, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_2", "Yang_076_Func_006_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************remove***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 1; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_1", GMC_OPERATION_REMOVE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestSetKeyNameAndValueIDValue1(g_stmt_list[1], idValue);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 1; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2_2", GMC_OPERATION_REMOVE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值，只有PKID2写入不同的值
        idValue = i;
        TestSetKeyNameAndValueIDValue3(g_stmt_list[2], 1, idValue, -1);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(5, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(5, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff029, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_2", "Yang_076_Func_009_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test stop.");
}

// 011 枚举字段作为list主键，改变list节点元素顺序，diff查询，subtree查询
TEST_F(YangEnumFunc, Yang_076_Func_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    GmcYangListLocatorT listProp;
    GmcPropValueT refKey;

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -3; i < 4; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[1], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[1], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[1], "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(8, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(8, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_2", "Yang_076_Func_011_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************merge 改变顺序,GMC_YANG_LIST_POSITION_FIRST***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点,0挪到第一个
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_FIRST, NULL);
    ret = GmcYangSetListLocator(g_stmt_list[1], &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    idValue = 0;
    TestSetKeyNameAndValueIDName1(g_stmt_list[1], "level0", strlen("level0"));

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    AW_FUN_Log(LOG_INFO, "diff 查询1");
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff031_01, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_2", "Yang_076_Func_011_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcResetStmt(g_stmt_list[1]);
    /***************************merge 改变顺序,GMC_YANG_LIST_POSITION_BEFORE***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点,3挪到-1前面
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    idValue = -1;
    GmcAttributePropertyT attrProperty;
    attrProperty.type = GMC_ATTRIBUTE_VALUE;
    attrProperty.size = sizeof(int32_t);
    attrProperty.value = &idValue;
    refKey.propertyId = 1;

    refKey.propertyName[0] = '\0';
    refKey.type = GMC_DATATYPE_ENUM;
    refKey.size = sizeof(attrProperty);
    refKey.value = (void *)&attrProperty;

    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_BEFORE, &refKey);
    ret = GmcYangSetListLocator(g_stmt_list[1], &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    idValue = 3;
    TestSetKeyNameAndValueIDValue1(g_stmt_list[1], idValue);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    AW_FUN_Log(LOG_INFO, "diff 查询2");
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff031_02, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_2", "Yang_076_Func_011_03");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************merge 改变顺序,GMC_YANG_LIST_POSITION_AFTER***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点,-2挪到2后面
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    idValue = 2;
    attrProperty.value = &idValue;
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_AFTER, &refKey);
    ret = GmcYangSetListLocator(g_stmt_list[1], &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 设置属性值
    idValue = -2;
    TestSetKeyNameAndValueIDValue1(g_stmt_list[1], idValue);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    AW_FUN_Log(LOG_INFO, "diff 查询3");
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff031_03, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_2", "Yang_076_Func_011_04");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   AW_FUN_Log(LOG_STEP, "test stop.");
}

// 012 枚举字段作为list主键，多个元素Insert和merge相同数据，验证唯一性，同时查询ErrorPath
TEST_F(YangEnumFunc, Yang_076_Func_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert数据***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[1], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[1], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[1], "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值，只有PKID2写入不同的值
        TestYangSetIDName(g_childNode[2], "PKID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        idValue = i;
        TestYangSetIDValue(g_childNode[2], "PKID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[2], "PKID3", "level-1", strlen("level-1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************insert 相同数据，1个主键***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值，只有PKID2写入不同的值
    idValue = -1;
    TestYangSetIDValue(g_childNode[1], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 200;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data.isValidErrorPathInfo = true;
    data.expectedErrorCode = GMC_VIOLATES_CREATE;
    data.expectedErrMsg = "target exists";
    data.expectedErrPath = "/root_2/list_2_1[PKID1=\"level-1\"]";

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, data.status);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_2", "Yang_076_Func_006_01");
    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************insert 相同数据，3个主键***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值，只有PKID2写入不同的值
    TestYangSetIDName(g_childNode[2], "PKID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -1;
    TestYangSetIDValue(g_childNode[2], "PKID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[2], "PKID3", "level-1", strlen("level-1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 200;
    TestYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data.isValidErrorPathInfo = true;
    data.expectedErrorCode = GMC_VIOLATES_CREATE;
    data.expectedErrMsg = "target exists";
    data.expectedErrPath = "/root_2/list_2_2[PKID1=\"level1\",PKID2=\"level-1\",PKID3=\"level-1\"]";

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, data.status);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_2", "Yang_076_Func_006_01");
    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_UNIQUE_VIOLATION);
    AddWhiteList(GMERR_PRIMARY_KEY_VIOLATION);
    AW_FUN_Log(LOG_STEP, "test stop.");
}

// 013 枚举字段作为list唯一localhash，多个元素Insert和merge相同数据，验证唯一性，同时查询ErrorPath
TEST_F(YangEnumFunc, Yang_076_Func_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert数据***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_3", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值，PKID1[level1] ID2写入level1
    idValue = 1;
    TestYangSetIDValue(g_childNode[1], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[1], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_3", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值，PKID1[level2] ID2写入level2
    idValue = 2;
    TestYangSetIDValue(g_childNode[1], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[1], "ID2", "level2", strlen("level2"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************insert 相同数据***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_3", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值，PKID1[level3] ID2写入level1
    idValue = 3;
    TestYangSetIDValue(g_childNode[1], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[1], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data.isValidErrorPathInfo = true;
    data.expectedErrorCode = GMC_VIOLATES_UNIQUE;
    data.expectedErrMsg = "not satisfy field unique";
    data.expectedErrPath = "/root_2/list_2_3[PKID1=\"level3\"]";

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNIQUE_VIOLATION, data.status);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_2", "Yang_076_Func_013_01");
    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    /***************************merge 相同数据***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2_3", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值，PKID1[level2] ID2改为level1
    TestSetKeyNameAndValueIDName1(g_stmt_list[2], "level2", strlen("level2"));
    TestYangSetIDName(g_childNode[2], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data.isValidErrorPathInfo = true;
    data.expectedErrorCode = GMC_VIOLATES_UNIQUE;
    data.expectedErrMsg = "not satisfy field unique";
    data.expectedErrPath = "/root_2/list_2_3[PKID1=\"level2\"]";

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNIQUE_VIOLATION, data.status);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_2", "Yang_076_Func_013_01");
    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AddWhiteList(GMERR_UNIQUE_VIOLATION);
    AddWhiteList(GMERR_PRIMARY_KEY_VIOLATION);
    AW_FUN_Log(LOG_STEP, "test stop.");
}

// 014 两个枚举字段的enumrate相同，都作为list的主键，写入相同的值
TEST_F(YangEnumFunc, Yang_076_Func_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -3; i < 4; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        char idName[10] = {0};
        ret = snprintf(idName, 10, "level%d", i);
        TestYangSetIDName(
            g_childNode[2], "PKID1", (const char *)idName, strlen(idName), GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(
            g_childNode[2], "PKID2", (const char *)idName, strlen(idName), GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(
            g_childNode[2], "PKID3", (const char *)idName, strlen(idName), GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(8, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(8, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff034, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_2", "Yang_076_Func_014_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test stop.");
}

// 015 枚举字段作为leaflist主键，Insert数据，diff查询，subtree查询
TEST_F(YangEnumFunc, Yang_076_Func_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_3", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_3_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        char idName[10] = {0};
        ret = snprintf(idName, 10, "level%d", i);
        TestYangSetIDName(
            g_childNode[1], "PKID1", (const char *)idName, strlen(idName), GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "leaflist_3_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        char idName[10] = {0};
        ret = snprintf(idName, 10, "level%d", i);
        TestYangSetIDName(
            g_childNode[2], "PKID1", (const char *)idName, strlen(idName), GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff035, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_3", "Yang_076_Func_015_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test stop.");
}

// 016 枚举字段作为leaflist主键，Merge数据，diff查询，subtree查询
TEST_F(YangEnumFunc, Yang_076_Func_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_3", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_MERGE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_3_1", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        char idName[10] = {0};
        ret = snprintf(idName, 10, "level%d", i);
        TestSetKeyNameAndValueIDName1(g_stmt_list[1], (const char *)idName, strlen(idName));

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "leaflist_3_2", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        char idName[10] = {0};
        ret = snprintf(idName, 10, "level%d", i);
        TestSetKeyNameAndValueIDName1(g_stmt_list[2], (const char *)idName, strlen(idName));

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff035, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_3", "Yang_076_Func_015_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test stop.");
}

// 017 枚举字段作为leaflist主键，Replace数据，diff查询，subtree查询
TEST_F(YangEnumFunc, Yang_076_Func_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_3", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_REPLACE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_3_1", GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        char idName[10] = {0};
        ret = snprintf(idName, 10, "level%d", i);
        TestYangSetIDName(
            g_childNode[1], "PKID1", (const char *)idName, strlen(idName), GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "leaflist_3_2", GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        char idName[10] = {0};
        ret = snprintf(idName, 10, "level%d", i);
        TestYangSetIDName(
            g_childNode[2], "PKID1", (const char *)idName, strlen(idName), GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff035, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_3", "Yang_076_Func_015_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test stop.");
}

// 018 枚举字段作为leaflist主键，Delete数据，diff查询，subtree查询
TEST_F(YangEnumFunc, Yang_076_Func_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_3", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_3_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        char idName[10] = {0};
        ret = snprintf(idName, 10, "level%d", i);
        TestYangSetIDName(
            g_childNode[1], "PKID1", (const char *)idName, strlen(idName), GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "leaflist_3_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        char idName[10] = {0};
        ret = snprintf(idName, 10, "level%d", i);
        TestYangSetIDName(
            g_childNode[2], "PKID1", (const char *)idName, strlen(idName), GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_3", "Yang_076_Func_015_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************delete***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_3", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 1; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_3_1", GMC_OPERATION_DELETE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestSetKeyNameAndValueIDValue1(g_stmt_list[1], idValue);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 1; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "leaflist_3_2", GMC_OPERATION_DELETE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestSetKeyNameAndValueIDValue1(g_stmt_list[2], idValue);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(5, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(5, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff038, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_3", "Yang_076_Func_018_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test stop.");
}

// 019 枚举字段作为leaflist主键，Remove数据，diff查询，subtree查询
TEST_F(YangEnumFunc, Yang_076_Func_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_3", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_3_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        char idName[10] = {0};
        ret = snprintf(idName, 10, "level%d", i);
        TestYangSetIDName(
            g_childNode[1], "PKID1", (const char *)idName, strlen(idName), GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "leaflist_3_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        char idName[10] = {0};
        ret = snprintf(idName, 10, "level%d", i);
        TestYangSetIDName(
            g_childNode[2], "PKID1", (const char *)idName, strlen(idName), GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_3", "Yang_076_Func_015_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************delete***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_3", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 1; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_3_1", GMC_OPERATION_REMOVE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestSetKeyNameAndValueIDValue1(g_stmt_list[1], idValue);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 1; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "leaflist_3_2", GMC_OPERATION_REMOVE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestSetKeyNameAndValueIDValue1(g_stmt_list[2], idValue);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(5, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(5, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff038, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_3", "Yang_076_Func_018_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test stop.");
}
// 支持全打散，用例下架 Yang_076_Func_020：DTS2024022611136

// 021 container节点，Insert写入枚举字段，写入value值，diff查询，subtree查询
TEST_F(YangEnumFunc, Yang_076_Func_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = 1;
    TestYangSetIDValue(g_rootNode, "ID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = 0;
    TestYangSetIDValue(g_rootNode, "ID3", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = 1;
    TestYangSetIDValue(g_childNode[1], "ID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_childNode[1], "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = 0;
    TestYangSetIDValue(g_childNode[1], "ID3", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff021, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_1", "Yang_076_Func_001_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test stop.");
}

// 022 枚举字段作为list主键，Insert数据，写入value值，diff查询，subtree查询
TEST_F(YangEnumFunc, Yang_076_Func_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = 1;
    TestYangSetIDValue(g_rootNode, "ID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = 0;
    TestYangSetIDValue(g_rootNode, "ID3", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[1], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        idValue = 1;
        TestYangSetIDValue(g_childNode[1], "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDValue(g_childNode[1], "ID3", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值，只有PKID2写入不同的值
        idValue = 1;
        TestYangSetIDValue(g_childNode[2], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        idValue = i;
        TestYangSetIDValue(g_childNode[2], "PKID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        idValue = -1;
        TestYangSetIDValue(g_childNode[2], "PKID3", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff026, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_2", "Yang_076_Func_006_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test stop.");
}

// 023 枚举字段作为list主键，Merge数据，写入value值，diff查询，subtree查询
TEST_F(YangEnumFunc, Yang_076_Func_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************merge insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_MERGE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_1", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestSetKeyNameAndValueIDValue1(g_stmt_list[1], idValue);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
        TestYangSetIDName(g_childNode[1], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_MERGE);
        TestYangSetIDName(g_childNode[1], "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_MERGE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2_2", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值，只有PKID2写入不同的值
        idValue = i;
        TestSetKeyNameAndValueIDValue3(g_stmt_list[2], 1, idValue, -1);

        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff026, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_2", "Yang_076_Func_006_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************merge update***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetIDName(g_rootNode, "ID1", "level2", strlen("level2"), GMC_YANG_PROPERTY_OPERATION_MERGE);
    idValue = -3;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    TestYangSetIDName(g_rootNode, "ID3", "level-1", strlen("level-1"), GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_1", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestSetKeyNameAndValueIDValue1(g_stmt_list[1], idValue);

        fieldValue = 200;
        TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
        TestYangSetIDName(g_childNode[1], "ID2", "level3", strlen("level3"), GMC_YANG_PROPERTY_OPERATION_MERGE);
        TestYangSetIDName(g_childNode[1], "ID3", "level3", strlen("level3"), GMC_YANG_PROPERTY_OPERATION_MERGE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2_2", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值，只有PKID2写入不同的值
        idValue = i;
        TestSetKeyNameAndValueIDValue3(g_stmt_list[2], 1, idValue, -1);

        fieldValue = 200;
        TestYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff027, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_2", "Yang_076_Func_007_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test stop.");
}

// 024 枚举字段作为leaflist主键，Insert数据，写入value值，diff查询，subtree查询
TEST_F(YangEnumFunc, Yang_076_Func_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_3", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_3_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[1], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "leaflist_3_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[2], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_root, batch, expectDiff035, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_3", "Yang_076_Func_015_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   AW_FUN_Log(LOG_STEP, "test stop.");
}

void *Thread_045_01(void *args)
{
    AW_FUN_Log(LOG_STEP, "==============[1] Yang CREATE start==================\n\n");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    GmcConnT *conn_async = NULL;
    GmcStmtT *stmt_root = NULL;
    GmcStmtT *stmt_child = NULL;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *childNode = NULL;
    AsyncUserDataT data = {0};

    uint32_t index = 1;
    ret = testGmcConnect(&conn_async, &stmt_root, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn_async, &stmt_child);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespaceAsync(stmt_root, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt_root, "root_2", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_root, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -3; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_child, "list_2_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt_root, stmt_child);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_child, &childNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(childNode, "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = 100;
        TestYangSetNodeProperty(childNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(childNode, "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(childNode, "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_child);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    // 插入时有可能主键冲突，会失败
    if (data.status != GMERR_OK) {
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));
        AW_FUN_Log(LOG_INFO, "==============[1] Yang CREATE trans fail.==================\n\n");

        // 回滚事务
        ret = TestTransRollBackAsync(conn_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));
        AW_FUN_Log(LOG_INFO, "==============[1] Yang CREATE trans succ.==================\n\n");

        // 提交事务
        ret = GmcTransCommitAsync(conn_async, trans_commit_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    GmcFreeStmt(stmt_child);
    ret = testGmcDisconnect(conn_async, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "==============[1] Yang CREATE end==================\n\n");
    return NULL;
}

void *Thread_045_02(void *args)
{
    AW_FUN_Log(LOG_STEP, "==============[2] Yang MERGE start==================\n\n");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    GmcConnT *conn_async = NULL;
    GmcStmtT *stmt_root = NULL;
    GmcStmtT *stmt_child = NULL;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *childNode = NULL;
    AsyncUserDataT data = {0};

    uint32_t index = 2;
    ret = testGmcConnect(&conn_async, &stmt_root, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn_async, &stmt_child);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespaceAsync(stmt_root, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt_root, "root_2", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_root, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -3; i < 4; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_child, "list_2_1", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt_root, stmt_child);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_child, &childNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        char idName[10] = {0};
        ret = snprintf(idName, 10, "level%d", i);
        TestSetKeyNameAndValueIDName1(stmt_child, (const char *)idName, strlen(idName));
        fieldValue = 100;
        TestYangSetNodeProperty(childNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
        TestYangSetIDName(childNode, "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_MERGE);
        TestYangSetIDName(childNode, "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_MERGE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_child);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(8, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(8, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = GmcTransCommitAsync(conn_async, trans_commit_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (data.status == GMERR_RESTRICT_VIOLATION) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, data.status);
        AW_FUN_Log(LOG_INFO, "==============[2] Yang MERGE trans fail.==================\n\n");
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_FUN_Log(LOG_INFO, "==============[2] Yang MERGE trans succ.==================\n\n");
    }
    AW_FUN_Log(LOG_STEP, "==============[2] Yang MERGE end==================\n\n");

    GmcFreeStmt(stmt_child);
    ret = testGmcDisconnect(conn_async, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

void *Thread_045_03(void *args)
{
    AW_FUN_Log(LOG_STEP, "==============[3] Yang REPLACE start==================\n\n");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    GmcConnT *conn_async = NULL;
    GmcStmtT *stmt_root = NULL;
    GmcStmtT *stmt_child = NULL;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *childNode = NULL;
    AsyncUserDataT data = {0};

    uint32_t index = 3;
    ret = testGmcConnect(&conn_async, &stmt_root, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn_async, &stmt_child);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespaceAsync(stmt_root, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt_root, "root_2", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_root, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -3; i < 4; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_child, "list_2_1", GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt_root, stmt_child);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_child, &childNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(childNode, "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        fieldValue = 100;
        TestYangSetNodeProperty(childNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        TestYangSetIDName(childNode, "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_REPLACE);
        TestYangSetIDName(childNode, "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_child);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(8, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(8, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = GmcTransCommitAsync(conn_async, trans_commit_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (data.status == GMERR_RESTRICT_VIOLATION) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, data.status);
        AW_FUN_Log(LOG_INFO, "==============[3] Yang REPLACE trans fail.==================\n\n");
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_FUN_Log(LOG_INFO, "==============[3] Yang REPLACE trans succ.==================\n\n");
    }
    AW_FUN_Log(LOG_STEP, "==============[3] Yang REPLACE end==================\n\n");

    GmcFreeStmt(stmt_child);
    ret = testGmcDisconnect(conn_async, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

// 025 并发操作写入Enum字段数据
TEST_F(YangEnumFunc, Yang_076_Func_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_UNIQUE_VIOLATION);
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 3线程并发
    pthread_t Thread[3] = {0};

    pthread_create(&Thread[0], NULL, Thread_045_01, NULL);
    pthread_create(&Thread[1], NULL, Thread_045_02, NULL);
    pthread_create(&Thread[2], NULL, Thread_045_03, NULL);

    pthread_join(Thread[0], NULL);
    pthread_join(Thread[1], NULL);
    pthread_join(Thread[2], NULL);

    AddWhiteList(GMERR_RESTRICT_VIOLATION);
    AW_FUN_Log(LOG_STEP, "test stop.");
}

// 026 Enum字段作为list主键，Insert数据条数超过enumerate数量
TEST_F(YangEnumFunc, Yang_076_Func_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    GmcYangListLocatorT listProp;
    GmcPropValueT refKey;

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -3; i < 4; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[1], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[1], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[1], "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点，超过enumerate个数，不存在的enumerate
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    idValue = 4;
    GmcAttributePropertyT attrProperty;
    attrProperty.type = GMC_ATTRIBUTE_VALUE;
    attrProperty.size = sizeof(int32_t);
    attrProperty.value = &idValue;

    GmcPropValueT propValue;
    memcpy(propValue.propertyName, "PKID1", (strlen("PKID1") + 1));
    propValue.type = GMC_DATATYPE_ENUM;
    propValue.value = (void *)&attrProperty;
    propValue.size = sizeof(attrProperty);
    ret = GmcYangSetNodeProperty(g_childNode[1], &propValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_DATA_EXCEPTION);
    AW_FUN_Log(LOG_STEP, "test stop.");
}

// 027 Enum字段作为leaflist主键，Insert个数等于enumerate数量，插入新数据报错
TEST_F(YangEnumFunc, Yang_076_Func_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    GmcYangListLocatorT listProp;
    GmcPropValueT refKey;

    /***************************insert***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_4", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -3; i < 4; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_4", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        char idName[10] = {0};
        ret = snprintf(idName, 10, "level%d", i);
        TestYangSetIDName(
            g_childNode[1], "PKID1", (const char *)idName, strlen(idName), GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点，超过enumerate个数，不存在的enumerate
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_4", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    idValue = 4;
    GmcAttributePropertyT attrProperty;
    attrProperty.type = GMC_ATTRIBUTE_VALUE;
    attrProperty.size = sizeof(int32_t);
    attrProperty.value = &idValue;

    GmcPropValueT propValue;
    memcpy(propValue.propertyName, "PKID1", (strlen("PKID1") + 1));
    propValue.type = GMC_DATATYPE_ENUM;
    propValue.value = (void *)&attrProperty;
    propValue.size = sizeof(attrProperty);
    ret = GmcYangSetNodeProperty(g_childNode[1], &propValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_DATA_EXCEPTION);
    AW_FUN_Log(LOG_STEP, "test stop.");
}

// 028
// NP节点，枚举字段有默认值，subtree查询，写入和默认值相同的数据，subtree查询，写入和默认值不同的数据，subtree查询，删除字段数据，subtree查询
TEST_F(YangEnumFunc, Yang_076_Func_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************查询默认值***********************************/
    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_6", "Yang_076_Func_028_01");
    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************写入和默认值相同的数据，subtree查询***********************************/
    /***************************写入和默认值不同的数据，subtree查询***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_6", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_6_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    // ID1没有默认值，ID2写入和默认值相同值，ID3写入和默认值不同值，ID4有默认值，不写入
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[1], "ID1", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[1], "ID2", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[1], "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_6", "Yang_076_Func_028_02", GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);
    TestSubtreeFilterObjAll(g_stmt_async, "root_6", "Yang_076_Func_028_03", GMC_DEFAULT_FILTER_TRIM);
    TestSubtreeFilterObjAll(g_stmt_async, "root_6", "Yang_076_Func_028_04", GMC_DEFAULT_FILTER_EXPLICIT);

    // subtree查询，内容过滤
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root_6", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_6_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char idName[10] = "level0";
    ret = TestYangSetFieldID(g_childNode[1], GMC_DATATYPE_ENUM, idName, strlen(idName), "ID1",
        GMC_YANG_PROPERTY_OPERATION_CREATE, GMC_ATTRIBUTE_NAME);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(
        g_stmt_async, g_rootNode, "Yang_076_Func_028_05", "root_6", GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************删除字段数据，subtree查询***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_6", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_6_1", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值, 删除ID1和ID3
    TestYangSetIDName(g_childNode[1], "ID1", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_DELETE);
    TestYangSetIDName(g_childNode[1], "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_DELETE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_6", "Yang_076_Func_028_06", GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);
    TestSubtreeFilterObjAll(g_stmt_async, "root_6", "Yang_076_Func_028_07", GMC_DEFAULT_FILTER_TRIM);
    TestSubtreeFilterObjAll(g_stmt_async, "root_6", "Yang_076_Func_028_08", GMC_DEFAULT_FILTER_EXPLICIT);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test stop.");
}

// 029
// P节点，枚举字段有默认值，subtree查询，写入和默认值相同的数据，subtree查询，写入和默认值不同的数据，subtree查询，删除字段数据，subtree查询
TEST_F(YangEnumFunc, Yang_076_Func_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************查询默认值***********************************/
    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_6", "Yang_076_Func_029_01");
    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************写入和默认值相同的数据，subtree查询***********************************/
    /***************************写入和默认值不同的数据，subtree查询***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_6", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_6_2", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    // ID1没有默认值，ID2写入和默认值相同值，ID3写入和默认值不同值，ID4有默认值，不写入
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[1], "ID1", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[1], "ID2", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[1], "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_6", "Yang_076_Func_029_02", GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);
    TestSubtreeFilterObjAll(g_stmt_async, "root_6", "Yang_076_Func_029_03", GMC_DEFAULT_FILTER_TRIM);
    TestSubtreeFilterObjAll(g_stmt_async, "root_6", "Yang_076_Func_029_04", GMC_DEFAULT_FILTER_EXPLICIT);

    // subtree查询，内容过滤
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root_6", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_6_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char idName[10] = "level0";
    ret = TestYangSetFieldID(g_childNode[1], GMC_DATATYPE_ENUM, idName, strlen(idName), "ID1",
        GMC_YANG_PROPERTY_OPERATION_CREATE, GMC_ATTRIBUTE_NAME);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(
        g_stmt_async, g_rootNode, "Yang_076_Func_029_05", "root_6", GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************删除字段数据，subtree查询***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_6", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_6_2", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值, 删除ID1和ID3
    TestYangSetIDName(g_childNode[1], "ID1", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_DELETE);
    TestYangSetIDName(g_childNode[1], "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_DELETE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_6", "Yang_076_Func_029_06", GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);
    TestSubtreeFilterObjAll(g_stmt_async, "root_6", "Yang_076_Func_029_07", GMC_DEFAULT_FILTER_TRIM);
    TestSubtreeFilterObjAll(g_stmt_async, "root_6", "Yang_076_Func_029_08", GMC_DEFAULT_FILTER_EXPLICIT);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test stop.");
}

// 030
// leaflist节点，枚举字段有默认值，subtree查询，写入和默认值相同的数据，subtree查询，写入和默认值不同的数据，subtree查询，删除写入的数据，subtree查询
TEST_F(YangEnumFunc, Yang_076_Func_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************查询默认值***********************************/
    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_3", "Yang_076_Func_030_01");
    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************写入和默认值相同的数据，subtree查询***********************************/
    /***************************写入和默认值不同的数据，subtree查询***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_3", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -3; i < 0; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_3_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        char idName[10] = {0};
        ret = snprintf(idName, 10, "level%d", i);
        TestYangSetIDName(
            g_childNode[1], "PKID1", (const char *)idName, strlen(idName), GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(4, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(4, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_3", "Yang_076_Func_030_02", GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);
    TestSubtreeFilterObjAll(g_stmt_async, "root_3", "Yang_076_Func_030_03", GMC_DEFAULT_FILTER_TRIM);
    TestSubtreeFilterObjAll(g_stmt_async, "root_3", "Yang_076_Func_030_04", GMC_DEFAULT_FILTER_EXPLICIT);

    // subtree查询，内容过滤
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root_3", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "leaflist_3_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char idName[10] = "level0";
    ret = TestYangSetFieldID(g_childNode[1], GMC_DATATYPE_ENUM, idName, strlen(idName), "PKID1",
        GMC_YANG_PROPERTY_OPERATION_CREATE, GMC_ATTRIBUTE_NAME);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "leaflist_3_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char idName1[10] = "level-1";
    ret = TestYangSetFieldID(g_childNode[1], GMC_DATATYPE_ENUM, idName1, strlen(idName1), "PKID1",
        GMC_YANG_PROPERTY_OPERATION_CREATE, GMC_ATTRIBUTE_NAME);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(
        g_stmt_async, g_rootNode, "Yang_076_Func_030_05", "root_3", GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************删除字段数据，subtree查询***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_3", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -3; i < -1; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_3_2", GMC_OPERATION_DELETE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestSetKeyNameAndValueIDValue1(g_stmt_list[1], idValue);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_3", "Yang_076_Func_030_06", GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);
    TestSubtreeFilterObjAll(g_stmt_async, "root_3", "Yang_076_Func_030_07", GMC_DEFAULT_FILTER_TRIM);
    TestSubtreeFilterObjAll(g_stmt_async, "root_3", "Yang_076_Func_030_08", GMC_DEFAULT_FILTER_EXPLICIT);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test stop.");
}

// 031 NP节点，枚举字段有默认值，when校验后默认值可见，subtree查询，when校验后默认值不可见，subtree查询
TEST_F(YangEnumFunc, Yang_076_Func_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async, GMC_YANG_VALIDATION_WHEN_FORCE);

    /*
    依赖关系                              | 写入数据                             | 预期结果
    con_7_1/ID1(/root_7/F0 = 100)         | con_7_1/ID1(/root_7/F0 = 100)           | con_7_1/ID1(默认值可见)
    con_7_1/ID2(/root_7/con_7_1/ID2 = 'level0') | con_7_1/ID2(/root_7/con_7_1/ID2 = 默认值'level0')  |
    con_7_1/ID2(默认值可见)
    */
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_7", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_7", "Yang_076_Func_031_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************修改when条件***********************************/
    /*
    依赖关系                              | 写入数据                             | 预期结果
    con_7_1/ID1(/root_7/F0 = 100)         | con_7_1/ID1(/root_7/F0 = 200)           | con_7_1/ID1(默认值不可见)
    con_7_1/ID2(/root_7/con_7_1/ID2 = 'level0') | con_7_1/ID2(/root_7/con_7_1/ID2 = 'level1')  |
    con_7_1/ID2(写入数据不可见，默认值可见)
    */
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_7", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_7_1", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    TestYangSetIDName(g_childNode[1], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_7", "Yang_076_Func_031_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test stop.");
}

// 032 P节点，枚举字段有默认值，when校验后默认值可见，subtree查询，when校验后默认值不可见，subtree查询
TEST_F(YangEnumFunc, Yang_076_Func_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async, GMC_YANG_VALIDATION_WHEN_FORCE);

    /*
    依赖关系                              | 写入数据                             | 预期结果
    con_7_2/ID1(/root_7/F0 = 100)         | con_7_2/ID1(/root_7/F0 = 100)           | con_7_2/ID1(P节点都不可见)
    con_7_2/ID2(/root_7/con_7_2/ID2 = 'level1') | con_7_2/ID2(/root_7/con_7_2/ID2 = 默认值'level0')  |
    con_7_2/ID2(P节点都不可见)
    */
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_7", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_7", "Yang_076_Func_032_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************修改when条件***********************************/
    /*
    依赖关系                              | 写入数据                             | 预期结果
    con_7_2/ID1(/root_7/F0 = 100)         | con_7_2/ID1(/root_7/F0 = 200)           | con_7_2/ID1(默认值不可见)
    con_7_2/ID2(/root_7/con_7_2/ID2 = 'level1') | con_7_2/ID2(/root_7/con_7_2/ID2 = 'level1')  |
    con_7_2/ID2(写入数据可见)
    */
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_7", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_7_2", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    TestYangSetIDName(g_childNode[1], "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_MERGE);
    TestYangSetIDName(g_childNode[1], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_7", "Yang_076_Func_032_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test stop.");
}

// 033 leaflist节点，枚举字段有默认值，when校验后默认值可见，subtree查询，when校验后默认值不可见，subtree查询
TEST_F(YangEnumFunc, Yang_076_Func_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async, GMC_YANG_VALIDATION_WHEN_FORCE);

    /*
    依赖关系                              | 写入数据                             | 预期结果
    leaflist_7_1(/root_7/F0 = 100)         | leaflist_7_1(/root_7/F0 = 100)           | leaflist_7_1(默认值可见)
    leaflist_7_2(/root_7/leaflist_7_2[PKID1='level1']/PKID1 = 'level1') | leaflist_7_2(PKID1默认值'level0''level-3')  |
    leaflist_7_2(节点都不可见)
    */
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_7", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_7", "Yang_076_Func_033_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************修改when条件***********************************/
    /*
    依赖关系                              | 写入数据                             | 预期结果
    leaflist_7_1(/root_7/F0 = 100)         | leaflist_7_1(/root_7/F0 = 200)           |
    leaflist_7_1(节点不可见，默认值不可见) leaflist_7_2(/root_7/leaflist_7_2[PKID1='level1']/PKID1 = 'level1') |
    leaflist_7_2(PKID1='level1')  | leaflist_7_2(节点可见)
    */
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_7", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_7_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        char idName[10] = {0};
        ret = snprintf(idName, 10, "level%d", i);
        TestYangSetIDName(
            g_childNode[1], "PKID1", (const char *)idName, strlen(idName), GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "leaflist_7_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        char idName[10] = {0};
        ret = snprintf(idName, 10, "level%d", i);
        TestYangSetIDName(
            g_childNode[2], "PKID1", (const char *)idName, strlen(idName), GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_7", "Yang_076_Func_033_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test stop.");
}

// 034 设置subtree查询条件时，写入枚举字段的value，报错
TEST_F(YangEnumFunc, Yang_076_Func_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_6", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_6_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    // ID1没有默认值，ID2写入和默认值相同值，ID3写入和默认值不同值，ID4有默认值，不写入
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[1], "ID1", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[1], "ID2", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[1], "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_6", "Yang_076_Func_034_01", GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);
    TestSubtreeFilterObjAll(g_stmt_async, "root_6", "Yang_076_Func_034_02", GMC_DEFAULT_FILTER_TRIM);
    TestSubtreeFilterObjAll(g_stmt_async, "root_6", "Yang_076_Func_034_03", GMC_DEFAULT_FILTER_EXPLICIT);

    // subtree查询，内容过滤
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root_6", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_6_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    idValue = 0;
    ret = TestYangSetFieldID(g_childNode[1], GMC_DATATYPE_ENUM, &idValue, sizeof(int32_t), "ID1",
        GMC_YANG_PROPERTY_OPERATION_CREATE, GMC_ATTRIBUTE_VALUE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(
        g_stmt_async, g_rootNode, "Yang_076_Func_034_04", "root_6", GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test stop.");
}

// 035 list节点，枚举字段为主键删除不存在的节点，查询ErrorPath
TEST_F(YangEnumFunc, Yang_076_Func_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert数据***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int32_t i = -1; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        idValue = i;
        TestYangSetIDValue(g_childNode[1], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = 100;
        TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[1], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDName(g_childNode[1], "ID3", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(4, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(4, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************删除不存在的数据***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_2_1", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    idValue = 2;
    TestSetKeyNameAndValueIDValue1(g_stmt_list[1], idValue);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data.isValidErrorPathInfo = true;
    data.expectedErrorCode = GMC_VIOLATES_DELETE;
    data.expectedErrMsg = "target not exists";
    data.expectedErrPath = "/root_2/list_2_1[PKID1=\"level2\"]";

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_DATA_EXCEPTION);
    AW_FUN_Log(LOG_STEP, "test stop.");
}

// 036 case节点，枚举字段为case下的字段，删除不存在的字段值，查询ErrorPath
TEST_F(YangEnumFunc, Yang_076_Func_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert数据***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = -2;
    TestYangSetIDValue(g_rootNode, "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_rootNode, "ID3", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "choice_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = GmcYangEditChildNode(g_childNode[1], "case_1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[2], "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************删除不存在的数据***********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "choice_1", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = GmcYangEditChildNode(g_childNode[1], "case_1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDName(g_childNode[2], "ID1", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // error path
    data.isValidErrorPathInfo = true;
    data.expectedErrorCode = GMC_VIOLATES_CREATE;
    data.expectedErrMsg = "target exists";
    data.expectedErrPath = "/root_1/choice_1/case_1";

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, data.status);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_SYNTAX_ERROR);
    AW_FUN_Log(LOG_STEP, "test stop.");
}

// 037 不符合must校验，查询ErrorPath
TEST_F(YangEnumFunc, Yang_076_Func_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 模型校验
    ModelCheck(g_stmt_async);

    /*
    依赖关系                              | 写入数据                             | 预期结果
    con_7_3/ID1(/root_7/F0 = 300)         | con_7_3/ID1(/root_7/F0 = 100)           | con_7_3/ID1(字段不可见)
    */
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_7", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_7_3", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    TestYangSetIDName(g_childNode[1], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test stop.");
}

// 038 不符合leafref校验，查询ErrorPath
TEST_F(YangEnumFunc, Yang_076_Func_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 模型校验和数据校验
    ModelCheck(g_stmt_async);

    /*
    依赖关系                              | 写入数据                             | 预期结果
    con_7_3/ID2(/root_7/con_7_1/ID2)         | con_7_3/ID2(/root_7/con_7_1/ID2默认值不等)           |
    con_7_3/ID2(字段不可见)
    */
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_7", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 300;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_7_1", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 300;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    TestYangSetIDName(g_childNode[1], "ID2", "level0", strlen("level0"), GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_7_3", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    TestYangSetIDName(g_childNode[2], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    bool isDataService = true;

    std::atomic_uint32_t step{0};
    GmcValidateResT validateRes{.validateRes = false, .failCount = 1};
    ValidateParam param = {
        .step = &step,
        .exceptStatus = GMERR_OK,
        .validateRes = validateRes,
        .isValidateErrorPath = true,
        .expectedErrCode = GMC_VIOLATES_LEAFREF,
        .expectedErrClauseIndex = 0,
        .expectedErrMsg = "violated leaf-ref clause 0",
        .expectedErrPath = "/root_7/con_7_3/ID2",
    };
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_LEAFREF, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_root, &cfg, AsyncValidateCb, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitValidateAsyncRecv(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, param.exceptStatus);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test stop.");
}

// 039 subtree子树过滤查询YangTreeschema模型，返回结果正确
TEST_F(YangEnumFunc, Yang_076_Func_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 视图校验 Xpath 合法性
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s", g_toolPath, g_namespace,
        "root_4", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    ret = executeCommand(command, "root_4", "ID1", "enum", "level3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(command, 0, sizeof(command));
    AW_FUN_Log(LOG_STEP, "test stop.");
}

// 040 枚举字段作为xpath条件，与字符串比较，进行xpath校验
TEST_F(YangEnumFunc, Yang_076_Func_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async, GMC_YANG_VALIDATION_WHEN_FORCE);

    /*
    依赖关系                              | 写入数据                             | 预期结果
    con_7_1/ID1(/root_7/F0 = 100)         | con_7_1/ID1(/root_7/F0 = 100)           | con_7_1/ID1(默认值可见)
    con_7_1/ID2(/root_7/con_7_1/ID2 = 'level0') | con_7_1/ID2(/root_7/con_7_1/ID2 = 默认值'level0')  |
    con_7_1/ID2(默认值可见)
    */
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_7", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_7", "Yang_076_Func_031_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************修改when条件***********************************/
    /*
    依赖关系                              | 写入数据                             | 预期结果
    con_7_1/ID1(/root_7/F0 = 100)         | con_7_1/ID1(/root_7/F0 = 200)           | con_7_1/ID1(默认值不可见)
    con_7_1/ID2(/root_7/con_7_1/ID2 = 'level0') | con_7_1/ID2(/root_7/con_7_1/ID2 = 'level1')  |
    con_7_1/ID2(写入数据不可见，默认值可见)
    */
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_7", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_7_1", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    TestYangSetIDName(g_childNode[1], "ID2", "level1", strlen("level1"), GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_7", "Yang_076_Func_031_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test stop.");
}

// 041 枚举字段作为xpath条件，与数字比较，进行xpath校验，模型校验报错
TEST_F(YangEnumFunc, Yang_076_Func_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    GmcConnT *conn_async = NULL;
    GmcStmtT *stmt_async = NULL;
    ret = testGmcConnect(&conn_async, &stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = "ns78";
    nspCfg.tablespaceName = NULL;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观

    ret = GmcCreateNamespaceWithCfgAsync(stmt_async, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcUseNamespaceAsync(stmt_async, "ns78", use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 创建表
    TestCreateLabel11(stmt_async);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(stmt_async, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(false, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(1, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 删除表
    TestDropLabel11(stmt_async);

    // 异步删除namespace
    ret = GmcDropNamespaceAsync(stmt_async, "ns78", drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = testGmcDisconnect(conn_async, stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_SYNTAX_ERROR);
    AddWhiteList(GMERR_DATA_EXCEPTION);
    AW_FUN_Log(LOG_STEP, "test stop.");
}

// 042 枚举和identity类型做为subtree结果转换时，因重名导致转换失败(问题单联调用例补充：DTS2024050704571)
TEST_F(YangEnumFunc, Yang_076_Func_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    int32_t idValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 创建表
    char *vLabelSchema = NULL;
    readJanssonFile("./schemaFile/yang_042.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile("./schemaFile/edge_042.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_042", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    uint32_t valueF0 = 100;
    ret = TestYangSetField(
        g_rootNode, GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点，list_042_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_042_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    idValue = 1;
    TestYangSetIDValue(g_childNode[1], "F0", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点，list_042_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_042_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    idValue = 11;
    TestYangSetIDValue(g_childNode[2], "F0", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_042", "Yang_076_Func_042_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除表
    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcClearNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    AW_FUN_Log(LOG_STEP, "test stop.");
}

template <typename DataTypePtr>
void LltInitPropValue(GmcPropValueT *propValue, const char *name, GmcDataTypeE type, DataTypePtr value, uint32_t size)
{
    if (propValue == NULL) {
        return;
    }
    strcpy_s(propValue->propertyName, strlen(name) + 1, name);
    propValue->type = type;
    propValue->size = size;
    propValue->value = (void *)value;
}

/*
						hw_root
						||
						con_t1
						||
---------------------  con_t2 -------------------
			  ||		    ||            ||
			choice_t3	choice_t32      node_list
			||			 	||
		 case_t41		    case_t42
	     //		\\
	 hw_list	con_t5
*/
// 043
// subtree查询时使用使用GmcYangGetChoiceCasePath获取choicecase路径并进行编辑subtree条件编辑查询(问题单联调用例补充：DTS2024051602634)
TEST_F(YangEnumFunc, Yang_076_Func_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 创建表
    char *vLabelSchema = NULL;
    readJanssonFile("./schemaFile/yang_choice_case.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile("./schemaFile/yang_list_edge.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL, *nodeConT1 = NULL, *nodeConT2 = NULL, *nodeChoiceT3 = NULL;
    GmcNodeT *nodeCaseT41 = NULL, *nodeConT5 = NULL, *nodeCaseT42 = NULL, *nodeList = NULL;
    GmcNodeT *nodeChoiceT32 = NULL, *nodeList2 = NULL;
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "hw_root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 编辑子节点con_t1
    ret = GmcYangEditChildNode(rootNode, "con_t1", GMC_OPERATION_INSERT, &nodeConT1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 编辑子节点con_t2
    ret = GmcYangEditChildNode(nodeConT1, "con_t2", GMC_OPERATION_INSERT, &nodeConT2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 编辑子节点choice_t32
    ret = GmcYangEditChildNode(nodeConT2, "choice_t32", GMC_OPERATION_INSERT, &nodeChoiceT32);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 编辑子节点case_t42
    ret = GmcYangEditChildNode(nodeChoiceT32, "case_t42", GMC_OPERATION_INSERT, &nodeCaseT42);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int16_t caseValF0 = 22;
    ret = testYangSetNodePropertyNew(
        nodeCaseT42, GMC_DATATYPE_INT16, &caseValF0, sizeof(caseValF0), "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 编辑子节点choice_t3
    ret = GmcYangEditChildNode(nodeConT2, "choice_t3", GMC_OPERATION_INSERT, &nodeChoiceT3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 编辑子节点case_t41
    ret = GmcYangEditChildNode(nodeChoiceT3, "case_t41", GMC_OPERATION_INSERT, &nodeCaseT41);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char f0value[8] = "string";
    ret = testYangSetNodePropertyNew(
        nodeCaseT41, GMC_DATATYPE_STRING, &f0value, (strlen(f0value)), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 编辑子节点con_t5
    ret = GmcYangEditChildNode(nodeCaseT41, "con_t5", GMC_OPERATION_INSERT, &nodeConT5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int16_t conValF0 = 11;
    ret = testYangSetNodePropertyNew(
        nodeConT5, GMC_DATATYPE_INT16, &conValF0, sizeof(conValF0), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 编辑case节点下的list，hw_list
    for (uint32_t i = 1; i < 4; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "hw_list", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &nodeList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodePropertyNew(
            nodeList, GMC_DATATYPE_UINT32, &i, sizeof(i), "index", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char str[8] = "abcd";
        ret = testYangSetNodePropertyNew(
            nodeList, GMC_DATATYPE_STRING, &str, (strlen(str)), "emu-name", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t frameVal = i * 10;
        ret = testYangSetNodePropertyNew(
            nodeList, GMC_DATATYPE_UINT32, &frameVal, sizeof(frameVal), "frame-id", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 编辑con_t2下的node_list
    for (uint32_t i = 10; i < 15; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "node_list", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &nodeList2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodePropertyNew(
            nodeList2, GMC_DATATYPE_UINT32, &i, sizeof(i), "index", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char str[8] = "abcd";
        ret = testYangSetNodePropertyNew(
            nodeList2, GMC_DATATYPE_STRING, &str, (strlen(str)), "emu-name", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t frameVal = i * 10;
        ret = testYangSetNodePropertyNew(
            nodeList2, GMC_DATATYPE_UINT32, &frameVal, sizeof(frameVal), "frame-id", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(9, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(9, data.succNum);
    GmcBatchDestroy(batch);
    batch = NULL;
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    rootNode = NULL;
    nodeConT1 = NULL;
    nodeConT2 = NULL;
    nodeChoiceT3 = NULL;
    nodeCaseT41 = NULL;
    nodeConT5 = NULL;
    nodeCaseT42 = NULL;
    nodeList = NULL;
    nodeList2 = NULL;
    nodeChoiceT32 = NULL;

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(g_stmt_async, "hw_root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcYangChildElementTypeE childType[] = {
        GMC_YANG_TYPE_NODE,    // 表示操作 case 下的 node
        GMC_YANG_TYPE_VERTEX,  // 表示操作 case 下的 vertex
        GMC_YANG_TYPE_FIELD    // 表示操作 case 下的字段
    };

    char path[1024] = {0};
    GmcLostPathInfoT pathInfo = {.path = path, .totalPathLen = 1024, .actualPathLen = 0};
    for (uint32_t i = 0; i < sizeof(childType) / sizeof(childType[0]); i++) {
        ret = GmcYangGetChoiceCasePath(rootNode, "hw_root", childType[i], &pathInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    }

    // 编辑subtree查询条件，子节点不包含choicecase路径  hw-list ==>con_t1
    ret = GmcYangEditChildNode(rootNode, "con_t1", GMC_OPERATION_SUBTREE_FILTER, &nodeConT1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < sizeof(childType) / sizeof(childType[0]); i++) {
        ret = GmcYangGetChoiceCasePath(nodeConT1, "con_t1", childType[i], &pathInfo);
        EXPECT_NE(GMERR_OK, ret);
    }

    ret = GmcYangEditChildNode(nodeConT1, "con_t2", GMC_OPERATION_SUBTREE_FILTER, &nodeConT2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // con_t1 ==》 con_t2
    for (uint32_t i = 0; i < sizeof(childType) / sizeof(childType[0]); i++) {
        ret = GmcYangGetChoiceCasePath(nodeConT2, "con_t2", childType[i], &pathInfo);
        EXPECT_NE(GMERR_OK, ret);
    }

    // con_t2 ==》 node_list
    ret = GmcYangEditChildNode(nodeConT2, "node_list", GMC_OPERATION_SUBTREE_FILTER, &nodeList2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < sizeof(childType) / sizeof(childType[0]); i++) {
        ret = GmcYangGetChoiceCasePath(nodeList2, "node_list", childType[i], &pathInfo);
        EXPECT_NE(GMERR_OK, ret);
    }

    // 编辑subtree查询条件，子节点包含choicecase路径
    // 获取choice-case下的node的路径
    ret = GmcYangGetChoiceCasePath(nodeConT2, "con_t5", GMC_YANG_TYPE_NODE, &pathInfo);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STREQ("choice_t3/case_t41", pathInfo.path);

    // 获取choice-case下的字段的路径
    ret = GmcYangGetChoiceCasePath(nodeConT2, "F1", GMC_YANG_TYPE_FIELD, &pathInfo);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STREQ("choice_t32/case_t42", pathInfo.path);

    ret = GmcYangGetChoiceCasePath(nodeConT2, "F0", GMC_YANG_TYPE_FIELD, &pathInfo);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STREQ("choice_t3/case_t41", pathInfo.path);

    // 获取choice-case下的vertex的路径
    ret = GmcYangGetChoiceCasePath(nodeConT2, "hw_list", GMC_YANG_TYPE_VERTEX, &pathInfo);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STREQ("choice_t3/case_t41", pathInfo.path);

    GmcNodeT *hwenvParent = NULL;
    ret = GmcYangEditChildNode(nodeConT2, pathInfo.path, GMC_OPERATION_SUBTREE_FILTER, &hwenvParent);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *hwenv = NULL;
    EXPECT_EQ(GMERR_OK, GmcYangEditChildNode(hwenvParent, "hw_list", GMC_OPERATION_SUBTREE_FILTER, &hwenv));
    GmcPropValueT prop = {0};
    LltInitPropValue(&prop, "index", GMC_DATATYPE_NULL, NULL, 0);
    ret = GmcYangSetNodeProperty(hwenv, &prop, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = rootNode},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = GMC_DEFAULT_FILTER_EXPLICIT,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "hw_root", "Yang_076_Func_043_01");
    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除表
    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcClearNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    hwenvParent = NULL;
    hwenv = NULL;
    rootNode = NULL;
    nodeConT1 = NULL;
    nodeConT2 = NULL;
    nodeList2 = NULL;
    AW_FUN_Log(LOG_STEP, "test stop.");
}
