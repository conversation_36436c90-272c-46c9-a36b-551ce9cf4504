
/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#ifndef SUBTREE_COMMON_H
#define SUBTREE_COMMON_H
extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "jansson.h"

GmcConnT *g_conn_sync = NULL, *g_conn_async = NULL;
GmcStmtT *g_stmt_sync = NULL, *g_stmt_async = NULL;
// 模型树样式             container
//                      /    |     \
//            container    list    choice
//                       /   |    \     \
//            container    list   choice    case
//                                 /    \
//                               case1  case2
char *g_vertexschema = NULL, *g_edgeschema = NULL;
const char *g_vertexLabelT0 = "SubT0Con";
const char *g_vertexLabelT1 = "SubT1List";
const char *g_vertexLabelT1container = "SubT1container";
const char *g_vertexLabelT1choice = "SubT1choice";
const char *g_vertexLabelT1choicecase = "SubT1choiceCase";
const char *g_vertexLabelT2Con = "SubT2Con";
const char *g_vertexLabelT2List = "SubT2List";
const char *g_vertexLabelT2choice = "SubT2Choice";
const char *g_vertexLabelT3case1 = "SubT2ChoiceCase1";
const char *g_vertexLabelT3case2 = "SubT2ChoiceCase2";
const char *g_edgeLabeT0T1 = "SubT0ConSubT1List";
const char *g_edgeLabeT0T1container = "SubT0ConSubT1container";
const char *g_edgeLabeT0T1choice = "SubT0ConSubT1choice";
const char *g_edgeLabelT1ChoiceT2case1 = "SubT1choiceSubT1choiceCase";
const char *g_edgeLabeT1T2Con = "SubT1ListSubT2Con";
const char *g_edgeLabeT1T2List = "SubT1ListSubT2List";
const char *g_edgeLabetT1T2Choice = "SubT1ListSubT2Choice";
const char *g_edgeLabelT2ChoiceT3case1 = "SubT2ChoiceSubT2ChoiceCase1";
const char *g_edgeLabelT2ChoiceT3case2 = "SubT2ChoiceSubT2ChoiceCase2";
const char *g_labelconfig = "{\"max_record_count\" : 10000, \"isFastReadUncommitted\":0, \"auto_increment\":1}";
const char *g_labelCcehConfig = R"(
{
"hash_type":"cceh"
}
)";
const char *g_keyName = "PK";
// 申请各个表用的stmt
GmcStmtT *g_stmt_sync_T0 = NULL;
GmcStmtT *g_stmt_sync_T1List = NULL;
GmcStmtT *g_stmt_sync_T1Con = NULL;
GmcStmtT *g_stmt_sync_T1choice = NULL;
GmcStmtT *g_stmt_sync_T1choicecase = NULL;
GmcStmtT *g_stmt_sync_T2container = NULL;
GmcStmtT *g_stmt_sync_T2List = NULL;
GmcStmtT *g_stmt_sync_T2choice = NULL;
GmcStmtT *g_stmt_sync_T2choicecase1 = NULL;
GmcStmtT *g_stmt_sync_T2choicecase2 = NULL;

// 写数据到内存满用的普通vertex表
const char *g_vertexLabel = "Yang_007_115_labelOOM";
void TestYangAllocAllstmt()
{
    int ret = GmcAllocStmt(g_conn_async, &g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_sync_T1Con);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_sync_T1choice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_sync_T1choicecase);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_sync_T2container);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_sync_T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_sync_T2choice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_sync_T2choicecase1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_sync_T2choicecase2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void TestYangFreeAllstmt()
{
    GmcFreeStmt(g_stmt_sync_T0);
    GmcFreeStmt(g_stmt_sync_T1List);
    GmcFreeStmt(g_stmt_sync_T1Con);
    GmcFreeStmt(g_stmt_sync_T1choice);
    GmcFreeStmt(g_stmt_sync_T1choicecase);
    GmcFreeStmt(g_stmt_sync_T2container);
    GmcFreeStmt(g_stmt_sync_T2List);
    GmcFreeStmt(g_stmt_sync_T2choice);
    GmcFreeStmt(g_stmt_sync_T2choicecase1);
    GmcFreeStmt(g_stmt_sync_T2choicecase2);
}
bool testYangJsonIsEqualInner(const json_t *jsonA, const json_t *jsonB);
bool testYangJsonIsEqualReal(const json_t *valueA, const json_t *valueB)
{
    double doubleA = json_real_value(valueA);
    double doubleB = json_real_value(valueB);
    if (fabs(doubleA - doubleB) < 1e-6) {
        return true;
    }
    return false;
}
bool testYangJsonIsEqualField(const json_t *valueA, const json_t *valueB)
{
    if (json_typeof(valueA) == JSON_STRING) {
        return strcmp(json_string_value(valueA), json_string_value(valueB)) == 0;
    }
    if (json_typeof(valueA) == JSON_INTEGER) {
        return json_integer_value(valueA) == json_integer_value(valueB);
    }
    if (json_typeof(valueA) == JSON_REAL) {
        return testYangJsonIsEqualReal(valueA, valueB);
    }
    return true;
}
bool testYangJsonIsEqualArray(const json_t *valueA, const json_t *valueB)
{
    bool isEqual = true;
    uint32_t sizeA = (uint32_t)json_array_size(valueA);
    uint32_t sizeB = (uint32_t)json_array_size(valueB);
    if (sizeA != sizeB) {
        return false;
    }
    for (uint32_t i = 0; i < sizeA; ++i) {
        json_t *itemA = json_array_get(valueA, i);
        json_t *itemB = json_array_get(valueB, i);
        if (json_typeof(itemA) == JSON_OBJECT) {
            isEqual = testYangJsonIsEqualInner(itemA, itemB);
        } else {
            isEqual = testYangJsonIsEqualField(itemA, itemB);
        }
        if (!isEqual) {
            return false;
        }
    }
    return true;
}
bool testYangJsonIsEqualInner(const json_t *jsonA, const json_t *jsonB)
{
    bool isEqual = true;
    void *itA = json_object_iter((json_t *)jsonA);
    void *itB = json_object_iter((json_t *)jsonB);

    if (itA) {
        const char *keyA = json_object_iter_key(itA);

        while ((strcmp(keyA, "ID") == 0) || (strcmp(keyA, "PID") == 0)) {
            itA = json_object_iter_next((json_t *)jsonA, itA);
            if (itA == NULL) {
                return true;
            }
            keyA = json_object_iter_key(itA);  // 规避方案，subtree查出来的ID和PID不对用户体现，不参与结果比较
        }
    }
    if (itB) {
        const char *keyB = json_object_iter_key(itB);

        while ((strcmp(keyB, "ID") == 0) || (strcmp(keyB, "PID") == 0)) {
            itB = json_object_iter_next((json_t *)jsonA, itB);
            if (itB == NULL) {
                return true;
            }
            keyB = json_object_iter_key(itB);  // 规避方案，subtree查出来的ID和PID不对用户体现，不参与结果比较
        }
    }

    while (itA && itB) {
        const char *keyA = json_object_iter_key(itA);
        json_t *valueA = json_object_iter_value(itA);
        const char *keyB = json_object_iter_key(itB);
        json_t *valueB = json_object_iter_value(itB);

        if ((json_typeof(valueA) != json_typeof(valueB)) || (strcmp(keyA, keyB) != 0)) {
            return false;
        }
        if (json_typeof(valueA) == JSON_OBJECT) {
            isEqual = testYangJsonIsEqualInner(valueA, valueB);
        } else if (json_typeof(valueA) == JSON_ARRAY) {
            isEqual = testYangJsonIsEqualArray(valueA, valueB);
        } else {
            isEqual = testYangJsonIsEqualField(valueA, valueB);
        }
        if (!isEqual) {
            return false;
        }
        itA = json_object_iter_next((json_t *)jsonA, itA);
        itB = json_object_iter_next((json_t *)jsonB, itB);
    }

    return itA == itB;
}
bool testYangJsonIsEqual(const char *json1, const char *json2)
{
    json_error_t jsonError;
    json_t *jsonA = json_loads(json1, JSON_REJECT_DUPLICATES, &jsonError);
    json_t *jsonB = json_loads(json2, JSON_REJECT_DUPLICATES, &jsonError);
    bool isEqual = testYangJsonIsEqualInner(jsonA, jsonB);
    json_decref(jsonA);
    json_decref(jsonB);
    return isEqual;
}
// userData结构
struct subtreeFilterCbParam {
    int step;
    int32_t expectStatus;          // 预期的操作状态
    const char *expectReplyJson;  // 预期返回的subtree查询结果, json字符串
};
// userData ：用户数据 replyJson ：服务端返回的子树 json status ：服务器端操作处理结果  errMsg ：错误信息
void AsyncSubtreeFilterCb(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    subtreeFilterCbParam *param = (subtreeFilterCbParam *)(userData);
    param->expectStatus = status;
    // 将replyJson 与预期json比较
    if (param->expectReplyJson != NULL) {
        const char **jsonReply = NULL;
        bool isEnd = false;
        uint32_t count = 0;
        ASSERT_EQ(GMERR_OK, GmcYangFetchJsonRetDeparse(fetchRet, &isEnd, &jsonReply, &count));
        ASSERT_TRUE(isEnd);
        ASSERT_EQ(1, count);
        ASSERT_TRUE(jsonReply != NULL);
        bool jsonEqual = testYangJsonIsEqual((const char*)jsonReply[0], param->expectReplyJson);
        AW_MACRO_EXPECT_EQ_INT((uint32_t)true, (uint32_t)jsonEqual);
        GmcYangFreeFetchRet(fetchRet);
        fetchRet = NULL;
    }
    param->step++;
    if (GMERR_OK != status) {
        printf("[err] status is %d  errMsg  is %s   \n ", status, errMsg);
        return ;
    }
}
int testWaitAsyncSubtreeRecv(void *userData, int expRecvNum = 1, int timeout = -1, bool isAutoReset = true)
{
    int waitCnt = 0;
    struct timeval start;
    struct timeval end;
    unsigned long duration;

    gettimeofday(&start, NULL);
    subtreeFilterCbParam *userdata1 = (subtreeFilterCbParam *)userData;
    while (userdata1->step != expRecvNum) {
        usleep(10);
        waitCnt++;
        if (timeout > 0 && waitCnt >= timeout) {
            gettimeofday(&end, NULL);
            duration = 1000000 * (end.tv_sec - start.tv_sec) + end.tv_usec - start.tv_usec;
            printf("[INFO] Recv Timeout %lf ", (double)duration / 1000000);
            return -1;  // 接收超时
        }
    }
    return 0;
}
// 判断期待json 和 查询返回json是否相等
static void SubtreeFilter(
    GmcStmtT *stmt, const char *rootName, const char *subtreeJson, const char *replyJson, uint32_t jsonFlag)
{
    GmcSubtreeFilterItemT filter = {
        filter.rootName = rootName,
        filter.subtree.json = subtreeJson,
        filter.jsonFlag =  jsonFlag,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_DEFAULT_FILTER_TRIM,
        .filter = &filter,
    };
    GmcFetchRetT *fetchRet = NULL;
    ASSERT_EQ(GMERR_OK, GmcYangSubtreeFilterExecute(stmt, &filters, &fetchRet));
    bool isEnd = false;
    uint32_t count = 0;
    const char **jsonReply = NULL;
    ASSERT_EQ(GMERR_OK, GmcYangFetchJsonRetDeparse(fetchRet, &isEnd, &jsonReply, &count));
    ASSERT_TRUE(isEnd);
    ASSERT_EQ(1, count);
    ASSERT_TRUE(jsonReply != NULL);
    ASSERT_TRUE(testYangJsonIsEqual(jsonReply[0], replyJson));
    GmcYangFreeFetchRet(fetchRet);
}
int testTransStartAsync(GmcConnT *conn, GmcTxConfigT Config)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransStartAsync(conn, &Config, trans_start_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        return ret;
    }
}
int testTransCommitAsync(GmcConnT *conn)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        return ret;
    }
}
void testSetKeyNameAndValue(GmcStmtT *stmt, uint32_t keyvalue, uint32_t PID = 0, bool islist = false)
{
    int ret;
    if (PID != 0) {
        if (islist) {
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &keyvalue, sizeof(uint32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    ret = GmcSetIndexKeyName(stmt, g_keyName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// yang set 字段
int testYangSetField(GmcStmtT *stmt, GmcDataTypeE type, void *value, uint32_t size,
    const char *fieldname, GmcYangPropOpTypeE optype)
{
    int ret = 0;

    GmcPropValueT propValue;
    memcpy(propValue.propertyName, fieldname, (strlen(fieldname) + 1));
    propValue.type = type;
    propValue.value = value;
    propValue.size = size;
    ret = GmcYangSetVertexProperty(stmt, &propValue, optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }

    return ret;
}
// yang set T0层主键
void testYangSetVertexProperty_root_F0(GmcStmtT *stmt, uint32_t i, GmcYangPropOpTypeE optype)
{
    int ret = 0;
    uint32_t PK_value = i;
    ret = testYangSetField(stmt, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t), "F0", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// yang set T0层一般字段
void testYangSetVertexProperty(GmcStmtT *stmt, int32_t f1, bool f2, double f3, bool f4, float f5,
    GmcYangPropOpTypeE optype)
{
    int ret = 0;

    ret = testYangSetField(stmt, GMC_DATATYPE_INT32, &f1, sizeof(int32_t), "F1", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_BOOL, &f2, sizeof(f2), "F2", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_DOUBLE, &f3, sizeof(f3), "F3", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_BOOL, &f4, sizeof(f4), "F4", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_FLOAT, &f5, sizeof(f5), "F5", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char f6value[8] = "string";
    ret = testYangSetField(stmt, GMC_DATATYPE_STRING, &f6value, (strlen(f6value)), "F6", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// yang set T1层 list 主键
void testYangSetVertexProperty_T1List_PK(GmcStmtT *stmt,  uint32_t f0, GmcYangPropOpTypeE optype)
{
    int ret = 0;
    ret = testYangSetField(stmt, GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t), "F0", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// yang set T1层 list 一般字段
void testYangSetVertexProperty_T1List(GmcStmtT *stmt, int32_t f1, bool f2, double f3, bool f4, float f5,
    GmcYangPropOpTypeE optype)
{
    int ret = 0;

    ret = testYangSetField(stmt, GMC_DATATYPE_INT32, &f1, sizeof(int32_t), "F1", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_BOOL, &f2, sizeof(f2), "F2", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_DOUBLE, &f3, sizeof(f3), "F3", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_BOOL, &f4, sizeof(f4), "F4", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_FLOAT, &f5, sizeof(f5), "F5", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char f6value[8] = "string";
    ret = testYangSetField(stmt, GMC_DATATYPE_STRING, &f6value, (strlen(f6value)), "F6", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// yang set T1层 container F0
void testYangSetVertexProperty_T1Con_F0(GmcStmtT *stmt,  uint32_t f0, GmcYangPropOpTypeE optype)
{
    int ret = 0;
    ret = testYangSetField(stmt, GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t), "F0", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// yang set T1层 container 一般字段
void testYangSetVertexProperty_T1Con(GmcStmtT *stmt, int32_t f1, bool f2, double f3, bool f4, float f5,
    GmcYangPropOpTypeE optype)
{
    int ret = 0;
    ret = testYangSetField(stmt, GMC_DATATYPE_INT32, &f1, sizeof(int32_t), "F1", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_BOOL, &f2, sizeof(f2), "F2", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_DOUBLE, &f3, sizeof(f3), "F3", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_BOOL, &f4, sizeof(f4), "F4", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_FLOAT, &f5, sizeof(f5), "F5", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char f6value[8] = "string";
    ret = testYangSetField(stmt, GMC_DATATYPE_STRING, &f6value, (strlen(f6value)), "F6", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// yang set T1层 choice F0
void testYangSetVertexProperty_T1choice_F0(GmcStmtT *stmt,  uint32_t f0, GmcYangPropOpTypeE optype)
{
    int ret = 0;
    ret = testYangSetField(stmt, GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t), "F0", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// yang set T1层 choice  一般字段
void testYangSetVertexProperty_T1choice(GmcStmtT *stmt, int32_t f1, bool f2, double f3, bool f4, float f5,
    GmcYangPropOpTypeE optype)
{
    int ret = 0;
    ret = testYangSetField(stmt, GMC_DATATYPE_INT32, &f1, sizeof(int32_t), "F1", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_BOOL, &f2, sizeof(f2), "F2", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_DOUBLE, &f3, sizeof(f3), "F3", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_BOOL, &f4, sizeof(f4), "F4", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_FLOAT, &f5, sizeof(f5), "F5", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char f6value[8] = "string";
    ret = testYangSetField(stmt, GMC_DATATYPE_STRING, &f6value, (strlen(f6value)), "F6", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// yang set T1层 choice case F0
void testYangSetVertexProperty_T1choicecase_F0(GmcStmtT *stmt,  uint32_t f0, GmcYangPropOpTypeE optype)
{
    int ret = 0;
    ret = testYangSetField(stmt, GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t), "F0", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// yang set T1层 choice case 一般字段
void testYangSetVertexProperty_T1choicecase(GmcStmtT *stmt, int32_t f1, bool f2, double f3, bool f4, float f5,
    GmcYangPropOpTypeE optype)
{
    int ret = 0;
    ret = testYangSetField(stmt, GMC_DATATYPE_INT32, &f1, sizeof(int32_t), "F1", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_BOOL, &f2, sizeof(f2), "F2", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_DOUBLE, &f3, sizeof(f3), "F3", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_BOOL, &f4, sizeof(f4), "F4", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_FLOAT, &f5, sizeof(f5), "F5", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char f6value[8] = "string";
    ret = testYangSetField(stmt, GMC_DATATYPE_STRING, &f6value, (strlen(f6value)), "F6", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// yang set T2层 container F0
void testYangSetVertexProperty_T2container_F0(GmcStmtT *stmt,  uint32_t f0, GmcYangPropOpTypeE optype)
{
    int ret = 0;
    ret = testYangSetField(stmt, GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t), "F0", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// yang set T2层 container 一般字段
void testYangSetVertexProperty_T2container(GmcStmtT *stmt, int32_t f1, bool f2, double f3, bool f4, float f5,
    GmcYangPropOpTypeE optype)
{
    int ret = 0;
    ret = testYangSetField(stmt, GMC_DATATYPE_INT32, &f1, sizeof(int32_t), "F1", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_BOOL, &f2, sizeof(f2), "F2", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_DOUBLE, &f3, sizeof(f3), "F3", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_BOOL, &f4, sizeof(f4), "F4", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_FLOAT, &f5, sizeof(f5), "F5", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char f6value[8] = "string";
    ret = testYangSetField(stmt, GMC_DATATYPE_STRING, &f6value, (strlen(f6value)), "F6", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// yang set T2层 list 主键
void testYangSetVertexProperty_T2List_PK(GmcStmtT *stmt,  uint32_t f0, GmcYangPropOpTypeE optype)
{
    int ret = 0;
    ret = testYangSetField(stmt, GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t), "F0", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// yang set T2层 list 一般字段
void testYangSetVertexProperty_T2List(GmcStmtT *stmt, int32_t f1, bool f2, double f3, bool f4, float f5,
    GmcYangPropOpTypeE optype)
{
    int ret = 0;

    ret = testYangSetField(stmt, GMC_DATATYPE_INT32, &f1, sizeof(int32_t), "F1", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_BOOL, &f2, sizeof(f2), "F2", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_DOUBLE, &f3, sizeof(f3), "F3", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_BOOL, &f4, sizeof(f4), "F4", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_FLOAT, &f5, sizeof(f5), "F5", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char f6value[8] = "string";
    ret = testYangSetField(stmt, GMC_DATATYPE_STRING, &f6value, (strlen(f6value)), "F6", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// yang set T2层 choice F0
void testYangSetVertexProperty_T2choice_F0(GmcStmtT *stmt,  uint32_t f0, GmcYangPropOpTypeE optype)
{
    int ret = 0;
    ret = testYangSetField(stmt, GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t), "F0", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// yang set T2层 choice 一般字段
void testYangSetVertexProperty_T2choice(GmcStmtT *stmt, int32_t f1, bool f2, double f3, bool f4, float f5,
    GmcYangPropOpTypeE optype)
{
    int ret = 0;

    ret = testYangSetField(stmt, GMC_DATATYPE_INT32, &f1, sizeof(int32_t), "F1", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_BOOL, &f2, sizeof(f2), "F2", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_DOUBLE, &f3, sizeof(f3), "F3", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_BOOL, &f4, sizeof(f4), "F4", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_FLOAT, &f5, sizeof(f5), "F5", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char f6value[8] = "string";
    ret = testYangSetField(stmt, GMC_DATATYPE_STRING, &f6value, (strlen(f6value)), "F6", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// yang set T2层 choice case1 F0
void testYangSetVertexProperty_T2choicecase1_F0(GmcStmtT *stmt,  uint32_t f0, GmcYangPropOpTypeE optype)
{
    int ret = 0;
    ret = testYangSetField(stmt, GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t), "F0", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// yang set T2层 choice case1 一般字段
void testYangSetVertexProperty_T2choicecase1(GmcStmtT *stmt, int32_t f1, bool f2, double f3, bool f4, float f5,
    GmcYangPropOpTypeE optype)
{
    int ret = 0;

    ret = testYangSetField(stmt, GMC_DATATYPE_INT32, &f1, sizeof(int32_t), "F1", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_BOOL, &f2, sizeof(f2), "F2", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_DOUBLE, &f3, sizeof(f3), "F3", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_BOOL, &f4, sizeof(f4), "F4", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_FLOAT, &f5, sizeof(f5), "F5", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char f6value[8] = "string";
    ret = testYangSetField(stmt, GMC_DATATYPE_STRING, &f6value, (strlen(f6value)), "F6", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 模型树直接预置 数据
void testYangPresetAllDate(GmcConnT *conn)
{
    int ret ;
    // 开启事务
    GmcTxConfigT TrxConfig = {0};
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务
    // 启动事务
    ret = testTransStartAsync(conn, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 根节点 set 字段值
    uint32_t pkvalue = 1;
    // stmt句柄 yang操作类型 属性操作类型 属性名称 属性类型 属性值 属相长度
    testYangSetVertexProperty_root_F0(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t f1 = 1;
    bool f2 = true;
    double f3 = 1.0;
    bool f4 = 1;
    float f5 = 1;
    char *f6 = "string";
    testYangSetVertexProperty(g_stmt_sync_T0, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置child节点 g_stmt_sync_T1List  T1层list
    for (uint32_t i = 1; i < 11; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // list 主键的值 F0 从1-10
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_sync_T1List, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_sync_T1List, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置child节点 g_stmt_sync_T2container T1层 list下面的 container (T2层 container)
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2container, g_vertexLabelT2Con, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2container);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t T2container_F0value = 1;
        testYangSetVertexProperty_T2container_F0(g_stmt_sync_T2container, T2container_F0value,
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T2container(g_stmt_sync_T2container, f1, f2, f3, f4, f5,
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2container);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置child节点 g_stmt_sync_T2List T1层 list下面的 list (T2层 list)
        for (uint32_t m = 1; m < 11; m++) {
            // list 主键的值 F0 从1-10
            uint32_t t2Listpkvalue = m;
            int32_t t2ListF1 = 1;
            bool t2ListF2 = true;
            double t2ListF3 = 1;
            bool t2ListF4 = 1;
            float t2ListF5 = 1;
            char *t2ListF6 = "string";
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2List, g_vertexLabelT2List, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            testYangSetVertexProperty_T2List_PK(g_stmt_sync_T2List, t2Listpkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
            testYangSetVertexProperty_T2List(g_stmt_sync_T2List, t2ListF1, t2ListF2, t2ListF3, t2ListF4, t2ListF5,
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        // 设置child节点 g_stmt_sync_T2choice T1层 list下面的 choice (T2层 choice)
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2choice, g_vertexLabelT2choice, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2choice);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2choice);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置child节点 g_stmt_sync_T2choice T1层 list下面的 choice case1 (T2层 choice case1)
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2choicecase1, g_vertexLabelT3case1, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T2choice, g_stmt_sync_T2choicecase1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t t2choicecase1F0value = i;
        testYangSetVertexProperty_T2choicecase1_F0(g_stmt_sync_T2choicecase1, t2choicecase1F0value,
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T2choicecase1(g_stmt_sync_T2choicecase1, f1, f2, f3, f4, f5,
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2choicecase1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 设置child节点 g_stmt_sync_T1Con T1层container
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1Con, g_vertexLabelT1container, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1Con);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t t1ConF0value = 1;
    testYangSetVertexProperty_T1Con_F0(g_stmt_sync_T1Con, t1ConF0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_sync_T1Con, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1Con);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置child节点 g_stmt_sync_T1choice T1层choice
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1choice, g_vertexLabelT1choice, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1choice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t t1choiceF0value = 1;
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1choice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置child节点 g_stmt_sync_T1choice case T1层choice case
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1choicecase, g_vertexLabelT1choicecase, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T1choice, g_stmt_sync_T1choicecase);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t T1choicecase_F0value = 1;
    testYangSetVertexProperty_T1choicecase_F0(g_stmt_sync_T1choicecase, T1choicecase_F0value,
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choicecase(g_stmt_sync_T1choicecase, f1, f2, f3, f4, f5,
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1choicecase);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void InitListProperty(
    GmcYangListLocatorT *listProperty, GmcYangListPositionE position, GmcPropValueT *referenceKey)
{
    if (listProperty == NULL) {
        return;
    }
    listProperty->position = position;
    if (referenceKey != NULL) {
        listProperty->refKeyFields = (GmcPropValueT **)malloc(sizeof(GmcPropValueT *));
        listProperty->refKeyFields[0] = referenceKey;
        listProperty->refKeyFieldsCount = 1;
    } else {
        listProperty->refKeyFields = NULL;
        listProperty->refKeyFieldsCount = 0;
    }
}

void UninitListProperty(GmcYangListLocatorT *listProperty)
{
    if (listProperty->refKeyFields == NULL) {
        return;
    }
    free(listProperty->refKeyFields);
    listProperty->refKeyFields = NULL;
}

// 模型树中的表完成merge操作
void testYangMergeAllDate(GmcConnT *conn)
{
    int ret ;
    // 开启事务
    GmcTxConfigT TrxConfig = {0};
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务
    // 启动事务
    ret = testTransStartAsync(conn, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // merge操作
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, g_vertexLabelT0, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    uint32_t  keyvalue = 1;
    int32_t f1 = 2;
    bool f2 = true;
    double f3 = 2;
    bool f4 = 2;
    float f5 = 2;
    // 这里只设置了 F0的值 和keyname 因为是根节点
    testSetKeyNameAndValue(g_stmt_sync_T0, keyvalue);
    testYangSetVertexProperty(g_stmt_sync_T0, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_MERGE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置child节点 g_stmt_sync_T1List  T1层list
    for (int32_t i = 1; i < 11; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t  keyvalue = i;
        // 设置list 的索引  PID恒定为1 根container
        // 这里只设置了 F0的值 和keyname
        testSetKeyNameAndValue(g_stmt_sync_T1List, keyvalue, 1, true);
        testYangSetVertexProperty_T1List(g_stmt_sync_T1List, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_MERGE);
        GmcYangListLocatorT listProp;
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_REMAIN, NULL);
        ASSERT_EQ(GMERR_OK, GmcYangSetListLocator(g_stmt_sync_T1List, &listProp));
        UninitListProperty(&listProp);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置child节点 g_stmt_sync_T2container T1层 list下面的 container (T2层 container)
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2container, g_vertexLabelT2Con, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2container);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置 container 的索引 不需要设置set key value
        // 这里只设置了 keyname 不会设置key value
        testSetKeyNameAndValue(g_stmt_sync_T2container, keyvalue, i);
        uint32_t T2container_F0value = 2;
        testYangSetVertexProperty_T2container_F0(g_stmt_sync_T2container, T2container_F0value,
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        testYangSetVertexProperty_T2container(g_stmt_sync_T2container, f1, f2, f3, f4, f5,
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2container);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置child节点 g_stmt_sync_T2List T1层 list下面的 list (T2层 list)
        for (uint32_t m = 1; m < 11; m++) {
            // list 主键的值 F0 从1-10
            uint32_t T2List_pkvalue = m;
            int32_t T2ListF1 = 2;
            bool T2ListF2 = true;
            double T2ListF3 = 2;
            bool T2ListF4 = 2;
            float T2ListF5 = 2;
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2List, g_vertexLabelT2List, GMC_OPERATION_MERGE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 设置list 的索引  PID为list的ID
            // 这里设置了 keyvalue f0 和 keyname
            testSetKeyNameAndValue(g_stmt_sync_T2List, T2List_pkvalue, 1, true);
            testYangSetVertexProperty_T2List(g_stmt_sync_T2List, T2ListF1, T2ListF2, T2ListF3, T2ListF4, T2ListF5,
                GMC_YANG_PROPERTY_OPERATION_MERGE);
            GmcYangListLocatorT listProp1;
            InitListProperty(&listProp1, GMC_YANG_LIST_POSITION_REMAIN, NULL);
            ASSERT_EQ(GMERR_OK, GmcYangSetListLocator(g_stmt_sync_T2List, &listProp1));
            UninitListProperty(&listProp);
            ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        // 设置child节点 g_stmt_sync_T2choice T1层 list下面的 choice (T2层 choice)
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2choice, g_vertexLabelT2choice, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2choice);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2choice);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置child节点 g_stmt_sync_T2choice T1层 list下面的 choice case1 (T2层 choice case1)
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2choicecase1, g_vertexLabelT3case1, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T2choice, g_stmt_sync_T2choicecase1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t T2choicecase1_pkvalue = i;
        testSetKeyNameAndValue(g_stmt_sync_T2choicecase1, T2choicecase1_pkvalue, i);
        uint32_t T2choicecase1_F0value = 2;
        testYangSetVertexProperty_T2choicecase1_F0(g_stmt_sync_T2choicecase1, T2choicecase1_F0value,
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        testYangSetVertexProperty_T2choicecase1(g_stmt_sync_T2choicecase1, f1, f2, f3, f4, f5,
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2choicecase1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 设置child节点 g_stmt_sync_T1Con T1层container
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1Con, g_vertexLabelT1container, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1Con);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t T1Con_F0value = 2;
    testSetKeyNameAndValue(g_stmt_sync_T1Con, T1Con_F0value, 1);
    testYangSetVertexProperty_T1Con_F0(g_stmt_sync_T1Con, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_MERGE);
    testYangSetVertexProperty_T1Con(g_stmt_sync_T1Con, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_MERGE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1Con);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置child节点 g_stmt_sync_T1choice T1层choice
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1choice, g_vertexLabelT1choice, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1choice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t T1choice_F0value = 2;
    testSetKeyNameAndValue(g_stmt_sync_T1choice, T1choice_F0value, 1);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1choice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置child节点 g_stmt_sync_T1choice case T1层choice case
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1choicecase, g_vertexLabelT1choicecase, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T1choice, g_stmt_sync_T1choicecase);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t T1choicecase_F0value = 2;
    testSetKeyNameAndValue(g_stmt_sync_T1choicecase, T1choicecase_F0value, 1);
    testYangSetVertexProperty_T1choicecase_F0(g_stmt_sync_T1choicecase, T1choicecase_F0value,
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    testYangSetVertexProperty_T1choicecase(g_stmt_sync_T1choicecase, f1, f2, f3, f4, f5,
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1choicecase);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 模型树中的表完成replace操作
void testYangReplaceAllDate(GmcConnT *conn)
{
    int ret ;
    // 开启事务
    GmcTxConfigT TrxConfig = {0};
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务
    // 启动事务
    ret = testTransStartAsync(conn, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // merge操作
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    uint32_t  keyvalue = 1;
    int32_t f1 = 3;
    bool f2 = true;
    double f3 = 3;
    bool f4 = 3;
    float f5 = 3;
    testYangSetVertexProperty_root_F0(g_stmt_sync_T0, keyvalue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty(g_stmt_sync_T0, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置child节点 g_stmt_sync_T1List  T1层list
    for (uint32_t i = 1; i < 11; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // list 主键的值 F0 从1-10
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_sync_T1List, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        testYangSetVertexProperty_T1List(g_stmt_sync_T1List, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置child节点 g_stmt_sync_T2container T1层 list下面的 container (T2层 container)
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2container, g_vertexLabelT2Con, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2container);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t T2container_F0value = 1;
        testYangSetVertexProperty_T2container_F0(g_stmt_sync_T2container, T2container_F0value,
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        testYangSetVertexProperty_T2container(g_stmt_sync_T2container, f1, f2, f3, f4, f5,
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2container);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置child节点 g_stmt_sync_T2List T1层 list下面的 list (T2层 list)
        for (uint32_t m = 1; m < 11; m++) {
            // list 主键的值 F0 从1-10
            uint32_t T2List_pkvalue = m;
            int32_t t2ListF1 = 3;
            bool t2ListF2 = true;
            double t2ListF3 = 3;
            bool t2ListF4 = 3;
            float t2ListF5 = 3;
            char *t2ListF6 = "string";
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2List, g_vertexLabelT2List, GMC_OPERATION_REPLACE_GRAPH);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            testYangSetVertexProperty_T2List_PK(g_stmt_sync_T2List, T2List_pkvalue,
                GMC_YANG_PROPERTY_OPERATION_REPLACE);
            testYangSetVertexProperty_T2List(g_stmt_sync_T2List, t2ListF1, t2ListF2, t2ListF3, t2ListF4, t2ListF5,
                GMC_YANG_PROPERTY_OPERATION_REPLACE);
            ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        // 设置child节点 g_stmt_sync_T2choice T1层 list下面的 choice (T2层 choice)
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2choice, g_vertexLabelT2choice, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2choice);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2choice);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置child节点 g_stmt_sync_T2choice T1层 list下面的 choice case1 (T2层 choice case1)
        ret =
            testGmcPrepareStmtByLabelName(g_stmt_sync_T2choicecase1, g_vertexLabelT3case1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T2choice, g_stmt_sync_T2choicecase1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t T2choicecase1_F0value = i;
        testYangSetVertexProperty_T2choicecase1_F0(g_stmt_sync_T2choicecase1, T2choicecase1_F0value,
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        testYangSetVertexProperty_T2choicecase1(g_stmt_sync_T2choicecase1, f1, f2, f3, f4, f5,
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2choicecase1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 设置child节点 g_stmt_sync_T1Con T1层container
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1Con, g_vertexLabelT1container, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1Con);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t T1Con_F0value = 1;
    testSetKeyNameAndValue(g_stmt_sync_T1Con, T1Con_F0value);
    testYangSetVertexProperty_T1Con_F0(g_stmt_sync_T1Con, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_T1Con(g_stmt_sync_T1Con, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1Con);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置child节点 g_stmt_sync_T1choice T1层choice
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1choice, g_vertexLabelT1choice, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1choice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t T1choice_F0value = 1;
    testSetKeyNameAndValue(g_stmt_sync_T1choice, T1choice_F0value);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1choice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置child节点 g_stmt_sync_T1choice case T1层choice case
    ret =
        testGmcPrepareStmtByLabelName(g_stmt_sync_T1choicecase, g_vertexLabelT1choicecase, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T1choice, g_stmt_sync_T1choicecase);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t T1choicecase_F0value = 1;
    testSetKeyNameAndValue(g_stmt_sync_T1choicecase, T1choicecase_F0value);
    testYangSetVertexProperty_T1choicecase_F0(g_stmt_sync_T1choicecase, T1choicecase_F0value,
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_T1choicecase(g_stmt_sync_T1choicecase, f1, f2, f3, f4, f5,
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1choicecase);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 模型树中的表完成merge操作
void testYangMergePropertyFivePrimitives(GmcConnT *conn)
{
    int ret ;
    // 开启事务
    GmcTxConfigT TrxConfig = {0};
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务
    // 启动事务
    ret = testTransStartAsync(conn, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // merge操作
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, g_vertexLabelT0, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    uint32_t  keyvalue = 1;
    int32_t f1 = 2;
    bool f2 = true;
    double f3 = 2;
    bool f4 = 2;
    float f5 = 2;
    // 这里只设置了 F0的值 和keyname因为是根节点
    testSetKeyNameAndValue(g_stmt_sync_T0, keyvalue);
    ret = testYangSetField(g_stmt_sync_T0, GMC_DATATYPE_INT32, &f1, sizeof(int32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_stmt_sync_T0, GMC_DATATYPE_BOOL, &f2, sizeof(f2), "F2",
        GMC_YANG_PROPERTY_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int8_t f9 = 8;
    ret = testYangSetField(g_stmt_sync_T0, GMC_DATATYPE_INT8, &f9, sizeof(int8_t), "F9",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t f10 = 10;
    ret = testYangSetField(g_stmt_sync_T0, GMC_DATATYPE_UINT8, &f10, sizeof(uint8_t), "F10",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置child节点 g_stmt_sync_T1Con T1层container
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1Con, g_vertexLabelT1container, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1Con);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t T1Con_F0value = 2;
    testSetKeyNameAndValue(g_stmt_sync_T1Con, T1Con_F0value, 1);
    testYangSetVertexProperty_T1Con_F0(g_stmt_sync_T1Con, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_MERGE);
    testYangSetVertexProperty_T1Con(g_stmt_sync_T1Con, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_MERGE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1Con);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 模型树直接预置 数据
void testYangPresetAllDate_OptimiTrans(GmcConnT *conn)
{
    int ret ;
    // 写数据
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 根节点 set 字段值
    uint32_t pkvalue = 1;
    // stmt句柄 yang操作类型 属性操作类型 属性名称 属性类型 属性值 属相长度
    testYangSetVertexProperty_root_F0(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t f1 = 1;
    bool f2 = true;
    double f3 = 1;
    bool f4 = 1;
    float f5 = 1;
    char *f6 = "string";
    testYangSetVertexProperty(g_stmt_sync_T0, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置child节点 g_stmt_sync_T1List  T1层list
    for (uint32_t i = 1; i < 11; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // list 主键的值 F0 从1-10
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_sync_T1List, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_sync_T1List, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置child节点 g_stmt_sync_T2container T1层 list下面的 container (T2层 container)
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2container, g_vertexLabelT2Con, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2container);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t T2container_F0value = 1;
        testYangSetVertexProperty_T2container_F0(g_stmt_sync_T2container, T2container_F0value,
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T2container(g_stmt_sync_T2container, f1, f2, f3, f4, f5,
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2container);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置child节点 g_stmt_sync_T2List T1层 list下面的 list (T2层 list)
        for (uint32_t m = 1; m < 11; m++) {
            // list 主键的值 F0 从1-10
            uint32_t t2Listpkvalue = m;
            int32_t t2ListF1 = 1;
            bool t2ListF2 = true;
            double t2ListF3 = 1;
            bool t2ListF4 = 1;
            float t2ListF5 = 1;
            char *t2ListF6 = "string";
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2List, g_vertexLabelT2List, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            testYangSetVertexProperty_T2List_PK(g_stmt_sync_T2List, t2Listpkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
            testYangSetVertexProperty_T2List(g_stmt_sync_T2List, t2ListF1, t2ListF2, t2ListF3, t2ListF4, t2ListF5,
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        // 设置child节点 g_stmt_sync_T2choice T1层 list下面的 choice (T2层 choice)
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2choice, g_vertexLabelT2choice, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2choice);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2choice);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置child节点 g_stmt_sync_T2choice T1层 list下面的 choice case1 (T2层 choice case1)
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2choicecase1, g_vertexLabelT3case1, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T2choice, g_stmt_sync_T2choicecase1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t t2choicecase1F0value = i;
        testYangSetVertexProperty_T2choicecase1_F0(g_stmt_sync_T2choicecase1, t2choicecase1F0value,
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T2choicecase1(g_stmt_sync_T2choicecase1, f1, f2, f3, f4, f5,
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2choicecase1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 设置child节点 g_stmt_sync_T1Con T1层container
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1Con, g_vertexLabelT1container, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1Con);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t t1ConF0value = 1;
    testYangSetVertexProperty_T1Con_F0(g_stmt_sync_T1Con, t1ConF0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_sync_T1Con, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1Con);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置child节点 g_stmt_sync_T1choice T1层choice
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1choice, g_vertexLabelT1choice, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1choice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t t1choiceF0value = 1;
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1choice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置child节点 g_stmt_sync_T1choice case T1层choice case
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1choicecase, g_vertexLabelT1choicecase, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T1choice, g_stmt_sync_T1choicecase);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t T1choicecase_F0value = 1;
    testYangSetVertexProperty_T1choicecase_F0(g_stmt_sync_T1choicecase, T1choicecase_F0value,
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choicecase(g_stmt_sync_T1choicecase, f1, f2, f3, f4, f5,
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1choicecase);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
}
// 模型树中的表完成merge操作
void testYangMergeAllDate_OptimiTrans(GmcConnT *conn)
{
    int ret ;
    // merge操作
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, g_vertexLabelT0, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    uint32_t  keyvalue = 1;
    int32_t f1 = 2;
    bool f2 = true;
    double f3 = 2;
    bool f4 = 2;
    float f5 = 2;
    // 这里只设置了 F0的值 和keyname 因为是根节点
    testSetKeyNameAndValue(g_stmt_sync_T0, keyvalue);
    testYangSetVertexProperty(g_stmt_sync_T0, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_MERGE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置child节点 g_stmt_sync_T1List  T1层list
    for (int32_t i = 1; i < 11; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t  keyvalue = i;
        // 设置list 的索引  PID恒定为1 根container
        // 这里只设置了 F0的值 和keyname
        testSetKeyNameAndValue(g_stmt_sync_T1List, keyvalue, 1, true);
        testYangSetVertexProperty_T1List(g_stmt_sync_T1List, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_MERGE);
        GmcYangListLocatorT listProp;
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_REMAIN, NULL);
        ASSERT_EQ(GMERR_OK, GmcYangSetListLocator(g_stmt_sync_T1List, &listProp));
        UninitListProperty(&listProp);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置child节点 g_stmt_sync_T2container T1层 list下面的 container (T2层 container)
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2container, g_vertexLabelT2Con, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2container);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置 container 的索引 不需要设置set key value
        // 这里只设置了 keyname 不会设置key value
        testSetKeyNameAndValue(g_stmt_sync_T2container, keyvalue, i);
        uint32_t T2container_F0value = 2;
        testYangSetVertexProperty_T2container_F0(g_stmt_sync_T2container, T2container_F0value,
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        testYangSetVertexProperty_T2container(g_stmt_sync_T2container, f1, f2, f3, f4, f5,
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2container);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置child节点 g_stmt_sync_T2List T1层 list下面的 list (T2层 list)
        for (uint32_t m = 1; m < 11; m++) {
            // list 主键的值 F0 从1-10
            uint32_t T2List_pkvalue = m;
            int32_t T2ListF1 = 2;
            bool T2ListF2 = true;
            double T2ListF3 = 2;
            bool T2ListF4 = 2;
            float T2ListF5 = 2;
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2List, g_vertexLabelT2List, GMC_OPERATION_MERGE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 设置list 的索引  PID为list的ID
            // 这里设置了 keyvalue f0 和 keyname
            testSetKeyNameAndValue(g_stmt_sync_T2List, T2List_pkvalue, 1, true);
            testYangSetVertexProperty_T2List(g_stmt_sync_T2List, T2ListF1, T2ListF2, T2ListF3, T2ListF4, T2ListF5,
                GMC_YANG_PROPERTY_OPERATION_MERGE);
            GmcYangListLocatorT listProp1;
            InitListProperty(&listProp1, GMC_YANG_LIST_POSITION_REMAIN, NULL);
            ASSERT_EQ(GMERR_OK, GmcYangSetListLocator(g_stmt_sync_T2List, &listProp1));
            UninitListProperty(&listProp);
            ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        // 设置child节点 g_stmt_sync_T2choice T1层 list下面的 choice (T2层 choice)
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2choice, g_vertexLabelT2choice, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2choice);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2choice);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置child节点 g_stmt_sync_T2choice T1层 list下面的 choice case1 (T2层 choice case1)
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2choicecase1, g_vertexLabelT3case1, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T2choice, g_stmt_sync_T2choicecase1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t T2choicecase1_pkvalue = i;
        testSetKeyNameAndValue(g_stmt_sync_T2choicecase1, T2choicecase1_pkvalue, i);
        uint32_t T2choicecase1_F0value = 2;
        testYangSetVertexProperty_T2choicecase1_F0(g_stmt_sync_T2choicecase1, T2choicecase1_F0value,
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        testYangSetVertexProperty_T2choicecase1(g_stmt_sync_T2choicecase1, f1, f2, f3, f4, f5,
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2choicecase1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 设置child节点 g_stmt_sync_T1Con T1层container
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1Con, g_vertexLabelT1container, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1Con);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t T1Con_F0value = 2;
    testSetKeyNameAndValue(g_stmt_sync_T1Con, T1Con_F0value, 1);
    testYangSetVertexProperty_T1Con_F0(g_stmt_sync_T1Con, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_MERGE);
    testYangSetVertexProperty_T1Con(g_stmt_sync_T1Con, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_MERGE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1Con);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置child节点 g_stmt_sync_T1choice T1层choice
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1choice, g_vertexLabelT1choice, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1choice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t T1choice_F0value = 2;
    testSetKeyNameAndValue(g_stmt_sync_T1choice, T1choice_F0value, 1);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1choice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置child节点 g_stmt_sync_T1choice case T1层choice case
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1choicecase, g_vertexLabelT1choicecase, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T1choice, g_stmt_sync_T1choicecase);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t T1choicecase_F0value = 2;
    testSetKeyNameAndValue(g_stmt_sync_T1choicecase, T1choicecase_F0value, 1);
    testYangSetVertexProperty_T1choicecase_F0(g_stmt_sync_T1choicecase, T1choicecase_F0value,
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    testYangSetVertexProperty_T1choicecase(g_stmt_sync_T1choicecase, f1, f2, f3, f4, f5,
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1choicecase);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
}
// 模型树中的表完成replace操作
void testYangReplaceAllDate_OptimiTrans(GmcConnT *conn)
{
    int ret ;
    // merge操作
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    uint32_t  keyvalue = 1;
    int32_t f1 = 3;
    bool f2 = true;
    double f3 = 3;
    bool f4 = 3;
    float f5 = 3;
    testYangSetVertexProperty_root_F0(g_stmt_sync_T0, keyvalue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty(g_stmt_sync_T0, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置child节点 g_stmt_sync_T1List  T1层list
    for (uint32_t i = 1; i < 11; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // list 主键的值 F0 从1-10
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_sync_T1List, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        testYangSetVertexProperty_T1List(g_stmt_sync_T1List, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置child节点 g_stmt_sync_T2container T1层 list下面的 container (T2层 container)
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2container, g_vertexLabelT2Con, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2container);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t T2container_F0value = 1;
        testYangSetVertexProperty_T2container_F0(g_stmt_sync_T2container, T2container_F0value,
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        testYangSetVertexProperty_T2container(g_stmt_sync_T2container, f1, f2, f3, f4, f5,
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2container);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置child节点 g_stmt_sync_T2List T1层 list下面的 list (T2层 list)
        for (uint32_t m = 1; m < 11; m++) {
            // list 主键的值 F0 从1-10
            uint32_t T2List_pkvalue = m;
            int32_t t2ListF1 = 3;
            bool t2ListF2 = true;
            double t2ListF3 = 3;
            bool t2ListF4 = 3;
            float t2ListF5 = 3;
            char *t2ListF6 = "string";
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2List, g_vertexLabelT2List, GMC_OPERATION_REPLACE_GRAPH);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            testYangSetVertexProperty_T2List_PK(g_stmt_sync_T2List, T2List_pkvalue,
                GMC_YANG_PROPERTY_OPERATION_REPLACE);
            testYangSetVertexProperty_T2List(g_stmt_sync_T2List, t2ListF1, t2ListF2, t2ListF3, t2ListF4, t2ListF5,
                GMC_YANG_PROPERTY_OPERATION_REPLACE);
            ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        // 设置child节点 g_stmt_sync_T2choice T1层 list下面的 choice (T2层 choice)
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2choice, g_vertexLabelT2choice, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2choice);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2choice);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置child节点 g_stmt_sync_T2choice T1层 list下面的 choice case1 (T2层 choice case1)
        ret =
            testGmcPrepareStmtByLabelName(g_stmt_sync_T2choicecase1, g_vertexLabelT3case1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T2choice, g_stmt_sync_T2choicecase1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t T2choicecase1_F0value = i;
        testYangSetVertexProperty_T2choicecase1_F0(g_stmt_sync_T2choicecase1, T2choicecase1_F0value,
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        testYangSetVertexProperty_T2choicecase1(g_stmt_sync_T2choicecase1, f1, f2, f3, f4, f5,
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2choicecase1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 设置child节点 g_stmt_sync_T1Con T1层container
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1Con, g_vertexLabelT1container, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1Con);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t T1Con_F0value = 1;
    testSetKeyNameAndValue(g_stmt_sync_T1Con, T1Con_F0value);
    testYangSetVertexProperty_T1Con_F0(g_stmt_sync_T1Con, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_T1Con(g_stmt_sync_T1Con, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1Con);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置child节点 g_stmt_sync_T1choice T1层choice
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1choice, g_vertexLabelT1choice, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1choice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t T1choice_F0value = 1;
    testSetKeyNameAndValue(g_stmt_sync_T1choice, T1choice_F0value);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1choice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置child节点 g_stmt_sync_T1choice case T1层choice case
    ret =
        testGmcPrepareStmtByLabelName(g_stmt_sync_T1choicecase, g_vertexLabelT1choicecase, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T1choice, g_stmt_sync_T1choicecase);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t T1choicecase_F0value = 1;
    testSetKeyNameAndValue(g_stmt_sync_T1choicecase, T1choicecase_F0value);
    testYangSetVertexProperty_T1choicecase_F0(g_stmt_sync_T1choicecase, T1choicecase_F0value,
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_T1choicecase(g_stmt_sync_T1choicecase, f1, f2, f3, f4, f5,
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1choicecase);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
}
#endif
