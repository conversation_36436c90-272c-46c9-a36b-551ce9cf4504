#include "tools.h"

class get_id_and_type_interface : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void get_id_and_type_interface::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/start.sh");
}

void get_id_and_type_interface::TearDownTestCase()
{}

void get_id_and_type_interface::SetUp()
{
    int ret;
    AsyncUserDataT data = {0};
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步建连
    ret = testGmcConnect(&g_connAsync, &g_stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步创建namespace
    TryDropNameSpace(g_stmtAsync, g_namespace);
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = g_namespace;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观
    ret = GmcCreateNamespaceWithCfgAsync(g_stmtAsync, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = GmcUseNamespaceAsync(g_stmtAsync, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    AllocAllStmts();

    // 生成后天目录
    system("rm -rf dynFolder;mkdir dynFolder");

    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    AddWhiteList(GMERR_NO_DATA);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
}

void get_id_and_type_interface::TearDown()
{
    int ret;
    AsyncUserDataT data = {0};
    // 异步删除namespace
    TryDropNameSpace(g_stmtAsync, g_namespace);
    // 断连
    ret = testGmcDisconnect(g_connAsync, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();
    GmcDetachAllShmSeg();
    testEnvClean();
}

// 001.GmcYangGetPropTypeByName第一个参数传NULL
TEST_F(get_id_and_type_interface, Yang_085_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_UNIQUE_VIOLATION);
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/all_type.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck();

    uint32_t idRootF0 = 0;
    uint32_t typeRootF0 = 0;
    ret = GmcYangGetPropTypeByName(NULL, "root_F0", &idRootF0, &typeRootF0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
}

// 002.GmcYangGetPropTypeByName第二个参数传NULL
TEST_F(get_id_and_type_interface, Yang_085_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_UNIQUE_VIOLATION);
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/all_type.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck();

    uint32_t getID = 0;
    uint32_t getType = 0;
    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 取rootNode
    ret = testGmcPrepareStmtByLabelName(g_stmtConRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtConRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtConRoot, &g_nodeConRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangGetPropTypeByName(g_nodeConRoot, NULL, &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    // 取PNode
    ret = GmcYangEditChildNode(g_nodeConRoot, "P_container", GMC_OPERATION_INSERT, &g_nodeConP);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangGetPropTypeByName(g_nodeConP, NULL, &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    // 取NPNode
    ret = GmcYangEditChildNode(g_nodeConRoot, "NP_container", GMC_OPERATION_INSERT, &g_nodeConNP);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangGetPropTypeByName(g_nodeConNP, NULL, &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    // 取choiceNode
    ret = GmcYangEditChildNode(g_nodeConRoot, "choice_1", GMC_OPERATION_INSERT, &g_nodeConChoice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangGetPropTypeByName(g_nodeConChoice, NULL, &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    // 取case1Node
    ret = GmcYangEditChildNode(g_nodeConChoice, "case_1_1", GMC_OPERATION_INSERT, &g_nodeConCase1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangGetPropTypeByName(g_nodeConCase1, NULL, &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    // 取case2Node
    ret = GmcYangEditChildNode(g_nodeConChoice, "case_1_2", GMC_OPERATION_INSERT, &g_nodeConCase2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangGetPropTypeByName(g_nodeConCase2, NULL, &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, g_stmtConRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取listNode
    ret = testGmcPrepareStmtByLabelName(g_stmtListRoot, "list_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmtConRoot, g_stmtListRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtListRoot, &g_nodeListRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangGetPropTypeByName(g_nodeListRoot, NULL, &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    // 主键赋值
    uint32_t valueConCase1N2 = 105;
    ret = SetNodeProperty(g_nodeListRoot, GMC_DATATYPE_UINT32, &valueConCase1N2, sizeof(uint32_t), "root_F0",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 取PNode
    ret = GmcYangEditChildNode(g_nodeListRoot, "P_container", GMC_OPERATION_INSERT, &g_nodeListP);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangGetPropTypeByName(g_nodeListP, NULL, &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    // 取NPNode
    ret = GmcYangEditChildNode(g_nodeListRoot, "NP_container", GMC_OPERATION_INSERT, &g_nodeListNP);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangGetPropTypeByName(g_nodeListNP, NULL, &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    // 取choiceNode
    ret = GmcYangEditChildNode(g_nodeListRoot, "choice_1", GMC_OPERATION_INSERT, &g_nodeListChoice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangGetPropTypeByName(g_nodeListChoice, NULL, &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    // 取case1Node
    ret = GmcYangEditChildNode(g_nodeListChoice, "case_1_1", GMC_OPERATION_INSERT, &g_nodeListCase1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangGetPropTypeByName(g_nodeListCase1, NULL, &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    // 取case2Node
    ret = GmcYangEditChildNode(g_nodeListChoice, "case_1_2", GMC_OPERATION_INSERT, &g_nodeListCase2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangGetPropTypeByName(g_nodeListCase2, NULL, &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, g_stmtListRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取leaflist[]Node
    for (int i = 0; i < LEAF_LIST_TYPE_CNT; i++) {
        string labelNameStr = "leaf-list_label_" + to_string(i + 1);
        const char *labelName = labelNameStr.c_str();
        ret = testGmcPrepareStmtByLabelName(g_stmtLeafListRoot[i], labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtConRoot, g_stmtLeafListRoot[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtLeafListRoot[i], &g_nodeLeafListRoot[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangGetPropTypeByName(g_nodeLeafListRoot[i], NULL, &getID, &getType);
        AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
        // 设置leaflist主键
        SetLeafListKeyValue(g_nodeLeafListRoot[i], i, i);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtLeafListRoot[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2 + LEAF_LIST_TYPE_CNT, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2 + LEAF_LIST_TYPE_CNT, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 003.GmcYangGetPropTypeByName前两个参数传NULL
TEST_F(get_id_and_type_interface, Yang_085_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_UNIQUE_VIOLATION);
    int ret = 0;
    uint32_t idRootF0 = 0;
    uint32_t typeRootF0 = 0;
    ret = GmcYangGetPropTypeByName(NULL, NULL, &idRootF0, &typeRootF0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
}

// 004.GmcYangGetPropTypeByName传不存在的字段名
TEST_F(get_id_and_type_interface, Yang_085_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_INVALID_PROPERTY);
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/all_type.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck();

    uint32_t getID = 0;
    uint32_t getType = 0;
    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 取rootNode
    ret = testGmcPrepareStmtByLabelName(g_stmtConRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtConRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtConRoot, &g_nodeConRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangGetPropTypeByName(g_nodeConRoot, "unexist_name", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    // 取PNode
    ret = GmcYangEditChildNode(g_nodeConRoot, "P_container", GMC_OPERATION_INSERT, &g_nodeConP);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangGetPropTypeByName(g_nodeConP, "unexist_name", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    // 取NPNode
    ret = GmcYangEditChildNode(g_nodeConRoot, "NP_container", GMC_OPERATION_INSERT, &g_nodeConNP);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangGetPropTypeByName(g_nodeConNP, "unexist_name", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    // 取choiceNode
    ret = GmcYangEditChildNode(g_nodeConRoot, "choice_1", GMC_OPERATION_INSERT, &g_nodeConChoice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangGetPropTypeByName(g_nodeConChoice, "unexist_name", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    // 取case1Node
    ret = GmcYangEditChildNode(g_nodeConChoice, "case_1_1", GMC_OPERATION_INSERT, &g_nodeConCase1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangGetPropTypeByName(g_nodeConCase1, "unexist_name", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    // 取case2Node
    ret = GmcYangEditChildNode(g_nodeConChoice, "case_1_2", GMC_OPERATION_INSERT, &g_nodeConCase2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangGetPropTypeByName(g_nodeConCase2, "unexist_name", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, g_stmtConRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取listNode
    ret = testGmcPrepareStmtByLabelName(g_stmtListRoot, "list_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmtConRoot, g_stmtListRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtListRoot, &g_nodeListRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangGetPropTypeByName(g_nodeListRoot, "unexist_name", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    // 主键赋值
    uint32_t valueConCase1N2 = 105;
    ret = SetNodeProperty(g_nodeListRoot, GMC_DATATYPE_UINT32, &valueConCase1N2, sizeof(uint32_t), "root_F0",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 取PNode
    ret = GmcYangEditChildNode(g_nodeListRoot, "P_container", GMC_OPERATION_INSERT, &g_nodeListP);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangGetPropTypeByName(g_nodeListP, "unexist_name", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    // 取NPNode
    ret = GmcYangEditChildNode(g_nodeListRoot, "NP_container", GMC_OPERATION_INSERT, &g_nodeListNP);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangGetPropTypeByName(g_nodeListNP, "unexist_name", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    // 取choiceNode
    ret = GmcYangEditChildNode(g_nodeListRoot, "choice_1", GMC_OPERATION_INSERT, &g_nodeListChoice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangGetPropTypeByName(g_nodeListChoice, "unexist_name", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    // 取case1Node
    ret = GmcYangEditChildNode(g_nodeListChoice, "case_1_1", GMC_OPERATION_INSERT, &g_nodeListCase1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangGetPropTypeByName(g_nodeListCase1, "unexist_name", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    // 取case2Node
    ret = GmcYangEditChildNode(g_nodeListChoice, "case_1_2", GMC_OPERATION_INSERT, &g_nodeListCase2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangGetPropTypeByName(g_nodeListCase2, "unexist_name", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, g_stmtListRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取leaflist[]Node
    for (int i = 0; i < LEAF_LIST_TYPE_CNT; i++) {
        string labelNameStr = "leaf-list_label_" + to_string(i + 1);
        const char *labelName = labelNameStr.c_str();
        ret = testGmcPrepareStmtByLabelName(g_stmtLeafListRoot[i], labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtConRoot, g_stmtLeafListRoot[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtLeafListRoot[i], &g_nodeLeafListRoot[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangGetPropTypeByName(g_nodeLeafListRoot[i], "unexist_name", &getID, &getType);
        AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
        // 设置leaflist主键
        SetLeafListKeyValue(g_nodeLeafListRoot[i], i, i);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtLeafListRoot[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2 + LEAF_LIST_TYPE_CNT, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2 + LEAF_LIST_TYPE_CNT, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
