#include "tools.h"

class yang_vertex_scan_normal : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void yang_vertex_scan_normal::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/start.sh");
}

void yang_vertex_scan_normal::TearDownTestCase()
{}

void yang_vertex_scan_normal::SetUp()
{
    int ret;
    AsyncUserDataT data = {0};
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步建连
    ret = testGmcConnect(&g_connAsync, &g_stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步创建namespace
    TryDropNameSpace(g_stmtAsync, g_namespace);
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = g_namespace;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观
    ret = GmcCreateNamespaceWithCfgAsync(g_stmtAsync, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = GmcUseNamespaceAsync(g_stmtAsync, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    AllocAllStmts();

    // 生成后天目录
    system("rm -rf dynFolder;mkdir dynFolder");

    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    AddWhiteList(GMERR_NO_DATA);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
}

void yang_vertex_scan_normal::TearDown()
{
    int ret;
    AsyncUserDataT data = {0};
    // 异步删除namespace
    TryDropNameSpace(g_stmtAsync, g_namespace);
    // 断连
    ret = testGmcDisconnect(g_connAsync, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();
    GmcDetachAllShmSeg();
    testEnvClean();
}

// 005.零散的yang模型，scan默认值
TEST_F(yang_vertex_scan_normal, Yang_085_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/all_node.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 默认值是读不到的
    ret = Scan(stmt, "main_label");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = Scan(stmt, "list_label");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = Scan(stmt, "leaf-list_label_1");
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 006.yang模型，scan默认值
TEST_F(yang_vertex_scan_normal, Yang_085_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/all_node.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_2.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck();

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 默认值是读不到的
    ret = Scan(stmt, "main_label");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = Scan(stmt, "list_label");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = Scan(stmt, "leaf-list_label_1");
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 007.DML操作，提交事务后，用set primarykey value、name scan表中的数据
TEST_F(yang_vertex_scan_normal, Yang_085_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/all_node.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_2.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck();

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写数据
    SetAllNodeProperty();
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/3_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keys[8] = {0};
    uint32_t unexistKeys[8] = {9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999};
    keys[0] = 1;
    ret = ScanByKeyValue(stmt, "main_label", "pk", 1, keys, "root_F1", 100);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "main_label", "pk", 1, keys, "root_F3", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "main_label", "pk", 1, unexistKeys, "root_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByKeyValue(stmt, "P_container", "main_label", "pk", 1, keys, "P_F1", 101);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "P_container", "main_label", "pk", 1, keys, "P_F2", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "P_container", "main_label", "pk", 1, unexistKeys, "P_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "main_label", "pk", 1, keys, "NP_F1", 102);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "main_label", "pk", 1, keys, "NP_F2", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "main_label", "pk", 1, unexistKeys, "NP_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "main_label", "pk", 1, keys, "case_1_1_F1", 103);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "main_label", "pk", 1, keys, "case_1_1_F2", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "main_label", "pk", 1, unexistKeys, "case_1_1_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    keys[1] = 104;
    ret = ScanByKeyValue(stmt, "list_label", "pk", 2, keys, "root_F1", 104);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "list_label", "pk", 2, keys, "root_F3", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "list_label", "pk", 2, unexistKeys, "root_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByKeyValue(stmt, "P_container", "list_label", "pk", 2, keys, "P_F1", 105);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "P_container", "list_label", "pk", 2, keys, "P_F2", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "P_container", "list_label", "pk", 2, unexistKeys, "P_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "list_label", "pk", 2, keys, "NP_F1", 106);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "list_label", "pk", 2, keys, "NP_F2", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "list_label", "pk", 2, unexistKeys, "NP_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "list_label", "pk", 2, keys, "case_1_1_F1", 107);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "list_label", "pk", 2, keys, "case_1_1_F2", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "list_label", "pk", 2, unexistKeys, "case_1_1_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    keys[1] = 108;
    ret = ScanByKeyValue(stmt, "leaf-list_label_1", "pk", 2, keys, "root_F1", 108);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "leaf-list_label_1", "pk", 2, unexistKeys, "root_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 008.DML操作，提交事务后，用set localkey value、name scan表中的数据
TEST_F(yang_vertex_scan_normal, Yang_085_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/all_node.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_2.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck();

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写数据
    SetAllNodeProperty();
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/3_reply.json");

    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keys[8] = {0};
    uint32_t unexistKeys[8] = {9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999};
    keys[0] = 100;
    ret = ScanByKeyValue(stmt, "main_label", "lk", 1, keys, "root_F1", 100);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "main_label", "lk", 1, keys, "root_F3", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "main_label", "lk", 1, unexistKeys, "root_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByKeyValue(stmt, "P_container", "main_label", "lk", 1, keys, "P_F1", 101);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "P_container", "main_label", "lk", 1, keys, "P_F2", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "P_container", "main_label", "lk", 1, unexistKeys, "P_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "main_label", "lk", 1, keys, "NP_F1", 102);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "main_label", "lk", 1, keys, "NP_F2", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "main_label", "lk", 1, unexistKeys, "NP_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "main_label", "lk", 1, keys, "case_1_1_F1", 103);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "main_label", "lk", 1, keys, "case_1_1_F2", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "main_label", "lk", 1, unexistKeys, "case_1_1_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    keys[0] = 104;
    ret = ScanByKeyValue(stmt, "list_label", "lk", 1, keys, "root_F1", 104);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "list_label", "lk", 1, keys, "root_F3", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "list_label", "lk", 1, unexistKeys, "root_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByKeyValue(stmt, "P_container", "list_label", "lk", 1, keys, "P_F1", 105);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "P_container", "list_label", "lk", 1, keys, "P_F2", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "P_container", "list_label", "lk", 1, unexistKeys, "P_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "list_label", "lk", 1, keys, "NP_F1", 106);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "list_label", "lk", 1, keys, "NP_F2", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "list_label", "lk", 1, unexistKeys, "NP_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "list_label", "lk", 1, keys, "case_1_1_F1", 107);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "list_label", "lk", 1, keys, "case_1_1_F2", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "list_label", "lk", 1, unexistKeys, "case_1_1_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    keys[0] = 108;
    ret = ScanByKeyValue(stmt, "leaf-list_label_1", "lk", 1, keys, "root_F1", 108);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "leaf-list_label_1", "lk", 1, unexistKeys, "root_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 009.DML操作，提交事务前后，用set localhashkey value、name scan表中的数据
TEST_F(yang_vertex_scan_normal, Yang_085_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/all_node.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_2.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck();

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写数据
    SetAllNodeProperty();
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/3_reply.json");

    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keys[8] = {0};
    uint32_t unexistKeys[8] = {9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999};
    keys[0] = 101;
    ret = ScanByKeyValue(stmt, "main_label", "lhk", 1, keys, "root_F1", 100);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "main_label", "lhk", 1, keys, "root_F3", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "main_label", "lhk", 1, unexistKeys, "root_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByKeyValue(stmt, "P_container", "main_label", "lhk", 1, keys, "P_F1", 101);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "P_container", "main_label", "lhk", 1, keys, "P_F2", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "P_container", "main_label", "lhk", 1, unexistKeys, "P_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "main_label", "lhk", 1, keys, "NP_F1", 102);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "main_label", "lhk", 1, keys, "NP_F2", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "main_label", "lhk", 1, unexistKeys, "NP_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "main_label", "lhk", 1, keys, "case_1_1_F1", 103);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "main_label", "lhk", 1, keys, "case_1_1_F2", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "main_label", "lhk", 1, unexistKeys, "case_1_1_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    keys[0] = 105;
    ret = ScanByKeyValue(stmt, "list_label", "lhk", 1, keys, "root_F1", 104);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "list_label", "lhk", 1, keys, "root_F3", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "list_label", "lhk", 1, unexistKeys, "root_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByKeyValue(stmt, "P_container", "list_label", "lhk", 1, keys, "P_F1", 105);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "P_container", "list_label", "lhk", 1, keys, "P_F2", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "P_container", "list_label", "lhk", 1, unexistKeys, "P_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "list_label", "lhk", 1, keys, "NP_F1", 106);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "list_label", "lhk", 1, keys, "NP_F2", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "list_label", "lhk", 1, unexistKeys, "NP_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "list_label", "lhk", 1, keys, "case_1_1_F1", 107);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "list_label", "lhk", 1, keys, "case_1_1_F2", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "list_label", "lhk", 1, unexistKeys, "case_1_1_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    keys[0] = 108;
    ret = ScanByKeyValue(stmt, "leaf-list_label_1", "lhk", 1, keys, "root_F1", 108);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "leaf-list_label_1", "lhk", 1, unexistKeys, "root_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 010.DML操作，提交事务前后，用set localkey range、name scan表中的数据
TEST_F(yang_vertex_scan_normal, Yang_085_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/all_node.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_2.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck();

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写数据
    SetAllNodeProperty();
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/3_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    unsigned int arrLen = 1;
    uint32_t leftValue = 106;
    uint32_t rightValue = 110;
    GmcPropValueT leftVals[arrLen];
    GmcPropValueT rightVals[arrLen];
    leftVals[0].size = sizeof(uint32_t);
    leftVals[0].type = GMC_DATATYPE_UINT32;
    leftVals[0].value = &leftValue;
    rightVals[0].size = sizeof(uint32_t);
    rightVals[0].type = GMC_DATATYPE_UINT32;
    rightVals[0].value = &rightValue;

    GmcRangeItemT items1[arrLen];
    items1[0].lValue = NULL;
    items1[0].rValue = NULL;
    items1[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items1[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items1[0].order = GMC_ORDER_ASC;

    GmcRangeItemT items2[arrLen];
    items2[0].lValue = &leftVals[0];
    items2[0].rValue = &rightVals[0];
    items2[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items2[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items2[0].order = GMC_ORDER_ASC;

    GmcRangeItemT items3[arrLen];
    items3[0].lValue = &leftVals[0];
    items3[0].rValue = &rightVals[0];
    items3[0].lFlag = GMC_COMPARE_RANGE_OPEN;
    items3[0].rFlag = GMC_COMPARE_RANGE_OPEN;
    items3[0].order = GMC_ORDER_ASC;

    GmcRangeItemT items4[arrLen];
    items4[0].lValue = &leftVals[0];
    items4[0].rValue = &rightVals[0];
    items4[0].lFlag = GMC_COMPARE_RANGE_OPEN;
    items4[0].rFlag = GMC_COMPARE_RANGE_OPEN;
    items4[0].order = GMC_ORDER_DESC;

    uint32_t unexistLeftValue = 8998;
    uint32_t unexistRightValue = 9999;
    GmcPropValueT unexistLeftVals[arrLen];
    GmcPropValueT unexistRightVals[arrLen];
    unexistLeftVals[0].size = sizeof(uint32_t);
    unexistLeftVals[0].type = GMC_DATATYPE_UINT32;
    unexistLeftVals[0].value = &unexistLeftValue;
    unexistRightVals[0].size = sizeof(uint32_t);
    unexistRightVals[0].type = GMC_DATATYPE_UINT32;
    unexistRightVals[0].value = &unexistRightValue;
    GmcRangeItemT unexistItems[arrLen];
    unexistItems[0].lValue = &unexistLeftVals[0];
    unexistItems[0].rValue = &unexistRightVals[0];
    unexistItems[0].lFlag = GMC_COMPARE_RANGE_OPEN;
    unexistItems[0].rFlag = GMC_COMPARE_RANGE_OPEN;
    unexistItems[0].order = GMC_ORDER_ASC;

    uint32_t expectConRootF2Values[1] = {101};
    ret = ScanByRange(stmt, "main_label", items1, arrLen, "root_F2", expectConRootF2Values);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    uint32_t expectNoValue[10] = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
    ret = ScanByRange(stmt, "main_label", items1, arrLen, "root_F3", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByRange(stmt, "main_label", unexistItems, arrLen, "root_F2", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    uint32_t expectConPValues[1] = {101};
    ret = ScanByRange(stmt, "P_container", "main_label", items1, arrLen, "P_F1", expectConPValues);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByRange(stmt, "P_container", "main_label", items1, arrLen, "P_F2", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByRange(stmt, "P_container", "main_label", unexistItems, arrLen, "P_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    uint32_t expectConNPValues[1] = {102};
    ret = ScanByRange(stmt, "NP_container", "main_label", items1, arrLen, "NP_F1", expectConNPValues);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByRange(stmt, "NP_container", "main_label", items1, arrLen, "NP_F2", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByRange(stmt, "NP_container", "main_label", unexistItems, arrLen, "NP_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    uint32_t expectConCase1Values[1] = {103};
    ret = ScanByRange(stmt, "choice_1/case_1_1", "main_label", items1, arrLen, "case_1_1_F1", expectConCase1Values);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByRange(stmt, "choice_1/case_1_1", "main_label", items1, arrLen, "case_1_1_F2", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret =
        ScanByRange(stmt, "choice_1/case_1_1", "main_label", unexistItems, arrLen, "case_1_1_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    uint32_t expectListRootF2Values1[10] = {105, 106, 107, 108, 109, 110, 111, 112, 113, 114};
    ret = ScanByRange(stmt, "list_label", items1, arrLen, "root_F2", expectListRootF2Values1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    uint32_t expectListRootF2Values2[5] = {107, 108, 109, 110, 111};
    ret = ScanByRange(stmt, "list_label", items2, arrLen, "root_F2", expectListRootF2Values2);
    AW_MACRO_EXPECT_EQ_INT(5, ret);
    uint32_t expectListRootF2Values3[3] = {108, 109, 110};
    ret = ScanByRange(stmt, "list_label", items3, arrLen, "root_F2", expectListRootF2Values3);
    AW_MACRO_EXPECT_EQ_INT(3, ret);
    uint32_t expectListRootF2Values4[3] = {110, 109, 108};
    ret = ScanByRange(stmt, "list_label", items4, arrLen, "root_F2", expectListRootF2Values4);
    AW_MACRO_EXPECT_EQ_INT(3, ret);
    ret = ScanByRange(stmt, "list_label", items1, arrLen, "root_F3", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    ret = ScanByRange(stmt, "list_label", unexistItems, arrLen, "root_F2", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    uint32_t expectListPValues1[10] = {105, 106, 107, 108, 109, 110, 111, 112, 113, 114};
    ret = ScanByRange(stmt, "P_container", "list_label", items1, arrLen, "P_F1", expectListPValues1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    uint32_t expectListPValues2[5] = {107, 108, 109, 110, 111};
    ret = ScanByRange(stmt, "P_container", "list_label", items2, arrLen, "P_F1", expectListPValues2);
    AW_MACRO_EXPECT_EQ_INT(5, ret);
    uint32_t expectListPValues3[3] = {108, 109, 110};
    ret = ScanByRange(stmt, "P_container", "list_label", items3, arrLen, "P_F1", expectListPValues3);
    AW_MACRO_EXPECT_EQ_INT(3, ret);
    uint32_t expectListPValues4[3] = {110, 109, 108};
    ret = ScanByRange(stmt, "P_container", "list_label", items4, arrLen, "P_F1", expectListPValues4);
    AW_MACRO_EXPECT_EQ_INT(3, ret);
    ret = ScanByRange(stmt, "P_container", "list_label", items1, arrLen, "P_F2", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    ret = ScanByRange(stmt, "P_container", "list_label", unexistItems, arrLen, "P_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    uint32_t expectListNPValues1[10] = {106, 107, 108, 109, 110, 111, 112, 113, 114, 115};
    ret = ScanByRange(stmt, "NP_container", "list_label", items1, arrLen, "NP_F1", expectListNPValues1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    uint32_t expectListNPValues2[5] = {108, 109, 110, 111, 112};
    ret = ScanByRange(stmt, "NP_container", "list_label", items2, arrLen, "NP_F1", expectListNPValues2);
    AW_MACRO_EXPECT_EQ_INT(5, ret);
    uint32_t expectListNPValues3[3] = {109, 110, 111};
    ret = ScanByRange(stmt, "NP_container", "list_label", items3, arrLen, "NP_F1", expectListNPValues3);
    AW_MACRO_EXPECT_EQ_INT(3, ret);
    uint32_t expectListNPValues4[3] = {111, 110, 109};
    ret = ScanByRange(stmt, "NP_container", "list_label", items4, arrLen, "NP_F1", expectListNPValues4);
    AW_MACRO_EXPECT_EQ_INT(3, ret);
    ret = ScanByRange(stmt, "NP_container", "list_label", items1, arrLen, "NP_F2", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    ret = ScanByRange(stmt, "NP_container", "list_label", unexistItems, arrLen, "NP_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    uint32_t expectListCase1Values1[10] = {107, 108, 109, 110, 111, 112, 113, 114, 115, 116};
    ret = ScanByRange(stmt, "choice_1/case_1_1", "list_label", items1, arrLen, "case_1_1_F1", expectListCase1Values1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    uint32_t expectListCase1Values2[5] = {109, 110, 111, 112, 113};
    ret = ScanByRange(stmt, "choice_1/case_1_1", "list_label", items2, arrLen, "case_1_1_F1", expectListCase1Values2);
    AW_MACRO_EXPECT_EQ_INT(5, ret);
    uint32_t expectListCase1Values3[3] = {110, 111, 112};
    ret = ScanByRange(stmt, "choice_1/case_1_1", "list_label", items3, arrLen, "case_1_1_F1", expectListCase1Values3);
    AW_MACRO_EXPECT_EQ_INT(3, ret);
    uint32_t expectListCase1Values4[3] = {112, 111, 110};
    ret = ScanByRange(stmt, "choice_1/case_1_1", "list_label", items4, arrLen, "case_1_1_F1", expectListCase1Values4);
    AW_MACRO_EXPECT_EQ_INT(3, ret);
    ret = ScanByRange(stmt, "choice_1/case_1_1", "list_label", items1, arrLen, "case_1_1_F2", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    ret =
        ScanByRange(stmt, "choice_1/case_1_1", "list_label", unexistItems, arrLen, "case_1_1_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    uint32_t expectLeafListValues1[10] = {108, 109, 110, 111, 112, 113, 114, 115, 116, 117};
    ret = ScanByRange(stmt, "leaf-list_label_1", items1, arrLen, "root_F1", expectLeafListValues1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    uint32_t expectLeafListValues2[3] = {108, 109, 110};
    ret = ScanByRange(stmt, "leaf-list_label_1", items2, arrLen, "root_F1", expectLeafListValues2);
    AW_MACRO_EXPECT_EQ_INT(3, ret);
    uint32_t expectLeafListValues3[2] = {108, 109};
    ret = ScanByRange(stmt, "leaf-list_label_1", items3, arrLen, "root_F1", expectLeafListValues3);
    AW_MACRO_EXPECT_EQ_INT(2, ret);
    uint32_t expectLeafListValues4[2] = {109, 108};
    ret = ScanByRange(stmt, "leaf-list_label_1", items4, arrLen, "root_F1", expectLeafListValues4);
    AW_MACRO_EXPECT_EQ_INT(2, ret);
    ret = ScanByRange(stmt, "leaf-list_label_1", unexistItems, arrLen, "root_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 011.DML操作，提交事务后，用SetFilter表达式 scan表中的数据
TEST_F(yang_vertex_scan_normal, Yang_085_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/all_node.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_2.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck();

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写数据
    SetAllNodeProperty();
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/3_reply.json");

    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t expectValues1[1] = {100};
    ret = ScanByFilter(stmt, "main_label", "root_F4 = 102", "root_F1", expectValues1);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    uint32_t expectValues2[1] = {101};
    ret = ScanByFilter(stmt, "P_container", "main_label", "root_F4 = 102", "P_F1", expectValues2);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    uint32_t expectValues3[1] = {102};
    ret = ScanByFilter(stmt, "NP_container", "main_label", "root_F4 = 102", "NP_F1", expectValues3);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    uint32_t expectValues4[1] = {103};
    ret = ScanByFilter(stmt, "choice_1/case_1_1", "main_label", "root_F4 = 102", "case_1_1_F1", expectValues4);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    uint32_t expectNoValue[10] = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
    ret = ScanByFilter(stmt, "main_label", "root_F4 = 102", "root_F3", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(stmt, "main_label", "root_F4 = 103", "root_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = WrongFilter(stmt, "main_label", "list_label.root_F4 = 106");
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = WrongFilter(stmt, "main_label", "main_label.list_label/root_F4 = 106");
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_NAME, ret);
    ret = ScanByFilter(stmt, "main_label", "main_label.P_container/P_F4 = 102", "root_F1", expectValues1);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(stmt, "P_container", "main_label", "main_label.P_container/P_F4 = 102", "P_F1", expectValues2);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(stmt, "NP_container", "main_label", "main_label.P_container/P_F4 = 102", "NP_F1", expectValues3);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(
        stmt, "choice_1/case_1_1", "main_label", "main_label.P_container/P_F4 = 102", "case_1_1_F1", expectValues4);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(stmt, "main_label", "main_label.P_container/P_F4 = 102", "root_F3", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(stmt, "main_label", "main_label.P_container/P_F4 = 103", "root_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByFilter(stmt, "main_label", "main_label.NP_container/NP_F4 = 103", "root_F1", expectValues1);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(stmt, "P_container", "main_label", "main_label.NP_container/NP_F4 = 103", "P_F1", expectValues2);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret =
        ScanByFilter(stmt, "NP_container", "main_label", "main_label.NP_container/NP_F4 = 103", "NP_F1", expectValues3);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(
        stmt, "choice_1/case_1_1", "main_label", "main_label.NP_container/NP_F4 = 103", "case_1_1_F1", expectValues4);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(stmt, "main_label", "main_label.NP_container/NP_F4 = 103", "root_F3", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(stmt, "main_label", "main_label.NP_container/NP_F4 = 104", "root_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByFilter(stmt, "main_label", "main_label.case_1_1/case_1_1_F4 = 104", "root_F1", expectValues1);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret =
        ScanByFilter(stmt, "P_container", "main_label", "main_label.case_1_1/case_1_1_F4 = 104", "P_F1", expectValues2);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(
        stmt, "NP_container", "main_label", "main_label.case_1_1/case_1_1_F4 = 104", "NP_F1", expectValues3);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(
        stmt, "choice_1/case_1_1", "main_label", "main_label.case_1_1/case_1_1_F4 = 104", "case_1_1_F1", expectValues4);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(stmt, "main_label", "main_label.case_1_1/case_1_1_F4 = 104", "root_F3", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(stmt, "main_label", "main_label.case_1_1/case_1_1_F4 = 105", "root_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    uint32_t expectValues5[4] = {106, 107, 108, 109};
    ret = ScanByFilter(stmt, "list_label", "root_F4 > 107 and root_F4 <= 111", "root_F1", expectValues5);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    uint32_t expectValues6[4] = {107, 108, 109, 110};
    ret = ScanByFilter(stmt, "P_container", "list_label", "root_F4 > 107 and root_F4 <= 111", "P_F1", expectValues6);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    uint32_t expectValues7[4] = {108, 109, 110, 111};
    ret = ScanByFilter(stmt, "NP_container", "list_label", "root_F4 > 107 and root_F4 <= 111", "NP_F1", expectValues7);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    uint32_t expectValues8[4] = {109, 110, 111, 112};
    ret = ScanByFilter(
        stmt, "choice_1/case_1_1", "list_label", "root_F4 > 107 and root_F4 <= 111", "case_1_1_F1", expectValues8);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByFilter(stmt, "list_label", "root_F4 > 107 and root_F4 <= 111", "root_F3", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByFilter(stmt, "list_label", "root_F4 > 8999 and root_F4 <= 9999", "root_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByFilter(stmt, "list_label", "list_label.P_container/P_F4 > 107 and list_label.P_container/P_F4 <= 111",
        "root_F1", expectValues5);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByFilter(stmt, "P_container", "list_label",
        "list_label.P_container/P_F4 > 107 and list_label.P_container/P_F4 <= 111", "P_F1", expectValues6);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByFilter(stmt, "NP_container", "list_label",
        "list_label.P_container/P_F4 > 107 and list_label.P_container/P_F4 <= 111", "NP_F1", expectValues7);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByFilter(stmt, "choice_1/case_1_1", "list_label",
        "list_label.P_container/P_F4 > 107 and list_label.P_container/P_F4 <= 111", "case_1_1_F1", expectValues8);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByFilter(stmt, "list_label", "list_label.P_container/P_F4 > 107 and list_label.P_container/P_F4 <= 111",
        "root_F3", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByFilter(stmt, "list_label", "list_label.P_container/P_F4 > 8999 and list_label.P_container/P_F4 <= 9999",
        "root_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    uint32_t expectValues9[4] = {105, 106, 107, 108};
    ret = ScanByFilter(stmt, "list_label",
        "list_label.NP_container/NP_F4 > 107 and list_label.NP_container/NP_F4 <= 111", "root_F1", expectValues9);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    uint32_t expectValues10[4] = {106, 107, 108, 109};
    ret = ScanByFilter(stmt, "P_container", "list_label",
        "list_label.NP_container/NP_F4 > 107 and list_label.NP_container/NP_F4 <= 111", "P_F1", expectValues10);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    uint32_t expectValues11[4] = {107, 108, 109, 110};
    ret = ScanByFilter(stmt, "NP_container", "list_label",
        "list_label.NP_container/NP_F4 > 107 and list_label.NP_container/NP_F4 <= 111", "NP_F1", expectValues11);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    uint32_t expectValues12[4] = {108, 109, 110, 111};
    ret = ScanByFilter(stmt, "choice_1/case_1_1", "list_label",
        "list_label.NP_container/NP_F4 > 107 and list_label.NP_container/NP_F4 <= 111", "case_1_1_F1", expectValues12);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByFilter(stmt, "list_label",
        "list_label.NP_container/NP_F4 > 107 and list_label.NP_container/NP_F4 <= 111", "root_F3", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByFilter(stmt, "list_label",
        "list_label.NP_container/NP_F4 > 8999 and list_label.NP_container/NP_F4 <= 9999", "root_F1", expectNoValue,
        true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    uint32_t expectValues13[4] = {104, 105, 106, 107};
    ret = ScanByFilter(stmt, "list_label",
        "list_label.choice_1/case_1_1/case_1_1_F4 > 107 "
        "and list_label.choice_1/case_1_1/case_1_1_F4 <= 111",
        "root_F1", expectValues13);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    uint32_t expectValues14[4] = {105, 106, 107, 108};
    ret = ScanByFilter(stmt, "P_container", "list_label",
        "list_label.choice_1/case_1_1/case_1_1_F4 > 107 "
        "and list_label.choice_1/case_1_1/case_1_1_F4 <= 111",
        "P_F1", expectValues14);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    uint32_t expectValues15[4] = {106, 107, 108, 109};
    ret = ScanByFilter(stmt, "NP_container", "list_label",
        "list_label.choice_1/case_1_1/case_1_1_F4 > 107 "
        "and list_label.choice_1/case_1_1/case_1_1_F4 <= 111",
        "NP_F1", expectValues15);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    uint32_t expectValues16[4] = {107, 108, 109, 110};
    ret = ScanByFilter(stmt, "choice_1/case_1_1", "list_label",
        "list_label.choice_1/case_1_1/case_1_1_F4 > 107 "
        "and list_label.choice_1/case_1_1/case_1_1_F4 <= 111",
        "case_1_1_F1", expectValues16);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByFilter(stmt, "list_label",
        "list_label.choice_1/case_1_1/case_1_1_F4 > 107 "
        "and list_label.choice_1/case_1_1/case_1_1_F4 <= 111",
        "root_F3", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByFilter(stmt, "list_label",
        "list_label.choice_1/case_1_1/case_1_1_F4 > 8999 "
        "and list_label.choice_1/case_1_1/case_1_1_F4 <= 9999",
        "root_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    uint32_t expectValues17[5] = {111, 112, 113, 114, 115};
    ret = ScanByFilter(stmt, "leaf-list_label_1", "root_F1 > 110 and root_F1 <= 115", "root_F1", expectValues17);
    AW_MACRO_EXPECT_EQ_INT(5, ret);
    ret = ScanByFilter(stmt, "leaf-list_label_1", "root_F1 > 8999 and root_F1 <= 9999", "root_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 012.关闭diff，DML操作导致when删，用set primarykey value、name scan表中的数据
TEST_F(yang_vertex_scan_normal, Yang_085_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_ARRAY_SUBSCRIPT_ERROR);
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/all_node_with_when.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_2.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck();

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写数据
    SetAllNodeProperty();
    SetConRootAndIncreValid("root_F5", 103);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_1.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 各种字段违背约束
    SetConRootAndIncreValid("when_1", 1001);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_2.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keys[8] = {0};
    keys[0] = 1;
    ret = ScanByKeyValue(stmt, "main_label", "pk", 1, keys, "root_F5", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "P_container", "main_label", "pk", 1, keys, "P_F1", 101);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "P_container", "main_label", "pk", 1, keys, "P_F4", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "main_label", "pk", 1, keys, "NP_F1", 102);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "main_label", "pk", 1, keys, "NP_F4", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "main_label", "pk", 1, keys, "case_1_1_F1", 103);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "main_label", "pk", 1, keys, "case_1_1_F4", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    keys[1] = 104;
    ret = ScanByKeyValue(stmt, "list_label", "pk", 2, keys, "root_F1", 104);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "list_label", "pk", 2, keys, "root_F4", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "P_container", "list_label", "pk", 2, keys, "P_F1", 105);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "P_container", "list_label", "pk", 2, keys, "P_F4", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "list_label", "pk", 2, keys, "NP_F1", 106);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "list_label", "pk", 2, keys, "NP_F4", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "list_label", "pk", 2, keys, "case_1_1_F1", 107);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "list_label", "pk", 2, keys, "case_1_1_F4", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // case节点违背约束
    SetConRootAndIncreValid("when_2", 1001);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_3.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 因为choice下没有数据，所以choice也为空，getnode时nodePath传“空的父节点/空的子节点”会报1004001
    ScanNoElement(stmt, "choice_1/case_1_1", "main_label", "pk", 1, keys);
    ScanNoElement(stmt, "choice_1/case_1_1", "list_label", "pk", 2, keys);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // case节点违背约束
    SetConRootAndIncreValid("when_3", 1001);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_4.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ScanNodeNoElement(stmt, "P_container", "main_label", "pk", 1, keys, "P_F1");
    ScanNodeNoElement(stmt, "NP_container", "main_label", "pk", 1, keys, "NP_F1");
    ScanNodeNoElement(stmt, "P_container", "list_label", "pk", 2, keys, "P_F1");
    ScanNodeNoElement(stmt, "NP_container", "list_label", "pk", 2, keys, "NP_F1");

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // case节点违背约束
    SetConRootAndIncreValid("when_4", 1001);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_5.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanByKeyValue(stmt, "list_label", "pk", 2, keys, "root_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    keys[1] = 108;
    ret = ScanByKeyValue(stmt, "leaf-list_label_1", "pk", 2, keys, "root_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 013.关闭diff，DML操作导致when删，用set localkey value、name scan表中的数据
TEST_F(yang_vertex_scan_normal, Yang_085_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_ARRAY_SUBSCRIPT_ERROR);
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/all_node_with_when.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_2.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck();

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写数据
    SetAllNodeProperty();
    SetConRootAndIncreValid("root_F5", 103);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_1.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写数据
    SetConRootAndIncreValid("when_1", 1001);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_2.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t conlks[1] = {100};
    uint32_t listlks[1] = {104};
    ret = ScanByKeyValue(stmt, "main_label", "lk", 1, conlks, "root_F5", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "P_container", "main_label", "lk", 1, conlks, "P_F1", 101);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "P_container", "main_label", "lk", 1, conlks, "P_F4", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "main_label", "lk", 1, conlks, "NP_F1", 102);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "main_label", "lk", 1, conlks, "NP_F4", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "main_label", "lk", 1, conlks, "case_1_1_F1", 103);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "main_label", "lk", 1, conlks, "case_1_1_F4", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "list_label", "lk", 1, listlks, "root_F1", 104);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "list_label", "lk", 1, listlks, "root_F4", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "P_container", "list_label", "lk", 1, listlks, "P_F1", 105);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "P_container", "list_label", "lk", 1, listlks, "P_F4", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "list_label", "lk", 1, listlks, "NP_F1", 106);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "list_label", "lk", 1, listlks, "NP_F4", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "list_label", "lk", 1, listlks, "case_1_1_F1", 107);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "list_label", "lk", 1, listlks, "case_1_1_F4", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // case节点违背约束
    SetConRootAndIncreValid("when_2", 1001);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_3.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 因为choice下没有数据，所以choice也为空，getnode时nodePath传“空的父节点/空的子节点”会报1004001
    ScanNoElement(stmt, "choice_1/case_1_1", "main_label", "lk", 1, conlks);
    ScanNoElement(stmt, "choice_1/case_1_1", "list_label", "lk", 1, listlks);
    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // case节点违背约束
    SetConRootAndIncreValid("when_3", 1001);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_4.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ScanNodeNoElement(stmt, "P_container", "main_label", "lk", 1, conlks, "P_F1");
    ScanNodeNoElement(stmt, "NP_container", "main_label", "lk", 1, conlks, "NP_F1");
    ScanNodeNoElement(stmt, "P_container", "list_label", "lk", 1, listlks, "P_F1");
    ScanNodeNoElement(stmt, "NP_container", "list_label", "lk", 1, listlks, "NP_F1");

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // case节点违背约束
    SetConRootAndIncreValid("when_4", 1001);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_5.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanByKeyValue(stmt, "list_label", "lk", 1, listlks, "root_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    uint32_t leafListKeys[1] = {108};
    ret = ScanByKeyValue(stmt, "leaf-list_label_1", "lk", 1, leafListKeys, "root_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 014.关闭diff，DML操作导致when删，用set localhashkey value、name scan表中的数据
TEST_F(yang_vertex_scan_normal, Yang_085_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_ARRAY_SUBSCRIPT_ERROR);
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/all_node_with_when.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_2.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck();

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写数据
    SetAllNodeProperty();
    SetConRootAndIncreValid("root_F5", 103);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_1.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写数据
    SetConRootAndIncreValid("when_1", 1001);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_2.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t conlhks[1] = {101};
    uint32_t listlhks[1] = {105};
    ret = ScanByKeyValue(stmt, "main_label", "lhk", 1, conlhks, "root_F5", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "P_container", "main_label", "lhk", 1, conlhks, "P_F1", 101);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "P_container", "main_label", "lhk", 1, conlhks, "P_F4", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "main_label", "lhk", 1, conlhks, "NP_F1", 102);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "main_label", "lhk", 1, conlhks, "NP_F4", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "main_label", "lhk", 1, conlhks, "case_1_1_F1", 103);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "main_label", "lhk", 1, conlhks, "case_1_1_F4", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "list_label", "lhk", 1, listlhks, "root_F1", 104);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "list_label", "lhk", 1, listlhks, "root_F4", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "P_container", "list_label", "lhk", 1, listlhks, "P_F1", 105);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "P_container", "list_label", "lhk", 1, listlhks, "P_F4", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "list_label", "lhk", 1, listlhks, "NP_F1", 106);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "list_label", "lhk", 1, listlhks, "NP_F4", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "list_label", "lhk", 1, listlhks, "case_1_1_F1", 107);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "list_label", "lhk", 1, listlhks, "case_1_1_F4", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // case节点违背约束
    SetConRootAndIncreValid("when_2", 1001);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_3.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 因为choice下没有数据，所以choice也为空，getnode时nodePath传“空的父节点/空的子节点”会报1004001
    ScanNoElement(stmt, "choice_1/case_1_1", "main_label", "lhk", 1, conlhks);
    ScanNoElement(stmt, "choice_1/case_1_1", "list_label", "lhk", 1, listlhks);
    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // case节点违背约束
    SetConRootAndIncreValid("when_3", 1001);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_4.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ScanNodeNoElement(stmt, "P_container", "main_label", "lhk", 1, conlhks, "P_F1");
    ScanNodeNoElement(stmt, "NP_container", "main_label", "lhk", 1, conlhks, "NP_F1");
    ScanNodeNoElement(stmt, "P_container", "list_label", "lhk", 1, listlhks, "P_F1");
    ScanNodeNoElement(stmt, "NP_container", "list_label", "lhk", 1, listlhks, "NP_F1");

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // case节点违背约束
    SetConRootAndIncreValid("when_4", 1001);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_5.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanByKeyValue(stmt, "list_label", "lhk", 1, listlhks, "root_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    uint32_t leafListKeys[1] = {108};
    ret = ScanByKeyValue(stmt, "leaf-list_label_1", "lhk", 1, leafListKeys, "root_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 015.关闭diff，DML操作导致when删，用set localkey range、name scan表中的数据
TEST_F(yang_vertex_scan_normal, Yang_085_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_ARRAY_SUBSCRIPT_ERROR);
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/all_node_with_when.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_2.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck();

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写数据
    SetAllNodeProperty();
    SetConRootAndIncreValid("root_F5", 103);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_1.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写数据
    SetConRootAndIncreValid("when_1", 1001);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_2.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    unsigned int arrLen = 1;
    uint32_t leftValue = 106;
    uint32_t rightValue = 110;
    GmcPropValueT leftVals[arrLen];
    GmcPropValueT rightVals[arrLen];
    leftVals[0].size = sizeof(uint32_t);
    leftVals[0].type = GMC_DATATYPE_UINT32;
    leftVals[0].value = &leftValue;
    rightVals[0].size = sizeof(uint32_t);
    rightVals[0].type = GMC_DATATYPE_UINT32;
    rightVals[0].value = &rightValue;

    GmcRangeItemT items1[arrLen];
    items1[0].lValue = NULL;
    items1[0].rValue = NULL;
    items1[0].lFlag = GMC_COMPARE_RANGE_OPEN;
    items1[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items1[0].order = GMC_ORDER_ASC;

    GmcRangeItemT items2[arrLen];
    items2[0].lValue = &leftVals[0];
    items2[0].rValue = &rightVals[0];
    items2[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items2[0].rFlag = GMC_COMPARE_RANGE_OPEN;
    items2[0].order = GMC_ORDER_DESC;

    uint32_t expectRootValues1[10] = {104, 105, 106, 107, 108, 109, 110, 111, 112, 113};
    ret = ScanByRange(stmt, "list_label", items1, arrLen, "root_F1", expectRootValues1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    uint32_t expectRootValues2[4] = {109, 108, 107, 106};
    ret = ScanByRange(stmt, "list_label", items2, arrLen, "root_F1", expectRootValues2);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    uint32_t expectNoValue[10] = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
    ret = ScanByRange(stmt, "list_label", items1, arrLen, "root_F4", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    uint32_t expectPValues1[10] = {105, 106, 107, 108, 109, 110, 111, 112, 113, 114};
    ret = ScanByRange(stmt, "P_container", "list_label", items1, arrLen, "P_F1", expectPValues1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    uint32_t expectPValues2[4] = {110, 109, 108, 107};
    ret = ScanByRange(stmt, "P_container", "list_label", items2, arrLen, "P_F1", expectPValues2);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByRange(stmt, "P_container", "list_label", items1, arrLen, "P_F4", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    uint32_t expectNPValues1[10] = {106, 107, 108, 109, 110, 111, 112, 113, 114, 115};
    ret = ScanByRange(stmt, "NP_container", "list_label", items1, arrLen, "NP_F1", expectNPValues1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    uint32_t expectNPValues2[4] = {111, 110, 109, 108};
    ret = ScanByRange(stmt, "NP_container", "list_label", items2, arrLen, "NP_F1", expectNPValues2);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByRange(stmt, "NP_container", "list_label", items1, arrLen, "NP_F4", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    uint32_t expectCaseValues1[10] = {107, 108, 109, 110, 111, 112, 113, 114, 115, 116};
    ret = ScanByRange(stmt, "choice_1/case_1_1", "list_label", items1, arrLen, "case_1_1_F1", expectCaseValues1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    uint32_t expectCaseValues2[4] = {112, 111, 110, 109};
    ret = ScanByRange(stmt, "choice_1/case_1_1", "list_label", items2, arrLen, "case_1_1_F1", expectCaseValues2);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByRange(stmt, "choice_1/case_1_1", "list_label", items1, arrLen, "case_1_1_F4", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // case节点违背约束
    SetConRootAndIncreValid("when_2", 1001);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_3.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 因为choice下没有数据，所以choice也为空，getnode时nodePath传“空的父节点/空的子节点”会报1004001
    ScanNoElement(stmt, "choice_1/case_1_1", "list_label", items1, arrLen);
    ScanNoElement(stmt, "choice_1/case_1_1", "list_label", items2, arrLen);
    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // case节点违背约束
    SetConRootAndIncreValid("when_3", 1001);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_4.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ScanNodeNoElement(stmt, "P_container", "list_label", items1, arrLen, "P_F1");
    ScanNodeNoElement(stmt, "P_container", "list_label", items2, arrLen, "P_F1");
    ScanNodeNoElement(stmt, "NP_container", "list_label", items1, arrLen, "NP_F1");
    ScanNodeNoElement(stmt, "NP_container", "list_label", items2, arrLen, "NP_F1");
    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // case节点违背约束
    SetConRootAndIncreValid("when_4", 1001);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_5.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ScanByRange(stmt, "list_label", items1, arrLen, "root_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByRange(stmt, "list_label", items2, arrLen, "root_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByRange(stmt, "leaf-list_label_1", items1, arrLen, "root_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByRange(stmt, "leaf-list_label_1", items2, arrLen, "root_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 016.关闭diff，DML操作导致when删，用SetFilter scan表中的数据
TEST_F(yang_vertex_scan_normal, Yang_085_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/all_node_with_when.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_2.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck();

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写数据
    SetAllNodeProperty();
    SetConRootAndIncreValid("root_F5", 103);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_1.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写数据
    SetConRootAndIncreValid("when_1", 1001);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_2.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t expectNoValues[10] = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
    ret = ScanByFilter(stmt, "main_label", "root_F4 = 102", "root_F5", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    uint32_t expectConPValues[1] = {101};
    ret = ScanByFilter(stmt, "P_container", "main_label", "root_F4 = 102", "P_F1", expectConPValues);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(stmt, "P_container", "main_label", "root_F4 = 102", "P_F4", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    uint32_t expectConNPValues[1] = {102};
    ret = ScanByFilter(stmt, "NP_container", "main_label", "root_F4 = 102", "NP_F1", expectConNPValues);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(stmt, "NP_container", "main_label", "root_F4 = 102", "NP_F4", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    uint32_t expectConCaseValues[1] = {103};
    ret = ScanByFilter(stmt, "choice_1/case_1_1", "main_label", "root_F4 = 102", "case_1_1_F1", expectConCaseValues);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(stmt, "choice_1/case_1_1", "main_label", "root_F4 = 102", "case_1_1_F4", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret =
        ScanByFilter(stmt, "P_container", "main_label", "main_label.P_container/P_F1 = 101", "P_F1", expectConPValues);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(
        stmt, "P_container", "main_label", "main_label.P_container/P_F1 = 101", "P_F4", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(
        stmt, "NP_container", "main_label", "main_label.P_container/P_F1 = 101", "NP_F1", expectConNPValues);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(
        stmt, "NP_container", "main_label", "main_label.P_container/P_F1 = 101", "NP_F4", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(stmt, "choice_1/case_1_1", "main_label", "main_label.P_container/P_F1 = 101", "case_1_1_F1",
        expectConCaseValues);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(stmt, "choice_1/case_1_1", "main_label", "main_label.P_container/P_F1 = 101", "case_1_1_F4",
        expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(
        stmt, "P_container", "main_label", "main_label.NP_container/NP_F1 = 102", "P_F1", expectConPValues);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(
        stmt, "P_container", "main_label", "main_label.NP_container/NP_F1 = 102", "P_F4", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(
        stmt, "NP_container", "main_label", "main_label.NP_container/NP_F1 = 102", "NP_F1", expectConNPValues);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(
        stmt, "NP_container", "main_label", "main_label.NP_container/NP_F1 = 102", "NP_F4", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(stmt, "choice_1/case_1_1", "main_label", "main_label.NP_container/NP_F1 = 102", "case_1_1_F1",
        expectConCaseValues);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(stmt, "choice_1/case_1_1", "main_label", "main_label.NP_container/NP_F1 = 102", "case_1_1_F4",
        expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(
        stmt, "P_container", "main_label", "main_label.choice_1/case_1_1/case_1_1_F1 = 103", "P_F1", expectConPValues);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(stmt, "P_container", "main_label", "main_label.choice_1/case_1_1/case_1_1_F1 = 103", "P_F4",
        expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(stmt, "NP_container", "main_label", "main_label.choice_1/case_1_1/case_1_1_F1 = 103", "NP_F1",
        expectConNPValues);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(stmt, "NP_container", "main_label", "main_label.choice_1/case_1_1/case_1_1_F1 = 103", "NP_F4",
        expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(stmt, "choice_1/case_1_1", "main_label", "main_label.choice_1/case_1_1/case_1_1_F1 = 103",
        "case_1_1_F1", expectConCaseValues);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(stmt, "choice_1/case_1_1", "main_label", "main_label.choice_1/case_1_1/case_1_1_F1 = 103",
        "case_1_1_F4", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    // 表达式中带有被when删的字段
    ret = ScanByFilter(stmt, "main_label", "root_F5 = 103", "root_F1", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByFilter(stmt, "main_label", "main_label.P_container/P_F4 = 102", "root_F1", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByFilter(stmt, "main_label", "main_label.NP_container/NP_F4 = 103", "root_F1", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByFilter(
        stmt, "main_label", "main_label.choice_1/case_1_1/case_1_1_F4 = 104", "root_F1", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = ScanByFilter(stmt, "list_label", "root_F1 > 105 and root_F1 <= 109", "root_F4", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    uint32_t expectListPValues[4] = {107, 108, 109, 110};
    ret =
        ScanByFilter(stmt, "P_container", "list_label", "root_F1 > 105 and root_F1 <= 109", "P_F1", expectListPValues);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByFilter(
        stmt, "P_container", "list_label", "root_F1 > 105 and root_F1 <= 109", "P_F4", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    uint32_t expectListNPValues[4] = {108, 109, 110, 111};
    ret = ScanByFilter(
        stmt, "NP_container", "list_label", "root_F1 > 105 and root_F1 <= 109", "NP_F1", expectListNPValues);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByFilter(
        stmt, "NP_container", "list_label", "root_F1 > 105 and root_F1 <= 109", "NP_F4", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    uint32_t expectListCaseValues[4] = {109, 110, 111, 112};
    ret = ScanByFilter(stmt, "choice_1/case_1_1", "list_label", "root_F1 > 105 and root_F1 <= 109", "case_1_1_F1",
        expectListCaseValues);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByFilter(stmt, "choice_1/case_1_1", "list_label", "root_F1 > 105 and root_F1 <= 109", "case_1_1_F4",
        expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByFilter(stmt, "list_label", "list_label.P_container/P_F1 > 105 and list_label.P_container/P_F1 <= 109",
        "root_F4", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    uint32_t expectListPValuesByP[4] = {106, 107, 108, 109};
    ret = ScanByFilter(stmt, "P_container", "list_label",
        "list_label.P_container/P_F1 > 105 "
        "and list_label.P_container/P_F1 <= 109",
        "P_F1", expectListPValuesByP);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByFilter(stmt, "P_container", "list_label",
        "list_label.P_container/P_F1 > 105 "
        "and list_label.P_container/P_F1 <= 109",
        "P_F4", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    uint32_t expectListNPValuesByP[4] = {107, 108, 109, 110};
    ret = ScanByFilter(stmt, "NP_container", "list_label",
        "list_label.P_container/P_F1 > 105 "
        "and list_label.P_container/P_F1 <= 109",
        "NP_F1", expectListNPValuesByP);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByFilter(stmt, "NP_container", "list_label",
        "list_label.P_container/P_F1 > 105 "
        "and list_label.P_container/P_F1 <= 109",
        "NP_F4", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    uint32_t expectListCaseValuesByP[4] = {108, 109, 110, 111};
    ret = ScanByFilter(stmt, "choice_1/case_1_1", "list_label",
        "list_label.P_container/P_F1 > 105 "
        "and list_label.P_container/P_F1 <= 109",
        "case_1_1_F1", expectListCaseValuesByP);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByFilter(stmt, "choice_1/case_1_1", "list_label",
        "list_label.P_container/P_F1 > 105 "
        "and list_label.P_container/P_F1 <= 109",
        "case_1_1_F4", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByFilter(stmt, "list_label",
        "list_label.NP_container/NP_F1 > 105 "
        "and list_label.NP_container/NP_F1 <= 109",
        "root_F4", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    uint32_t expectListPValuesByNP[4] = {105, 106, 107, 108};
    ret = ScanByFilter(stmt, "P_container", "list_label",
        "list_label.NP_container/NP_F1 > 105 "
        "and list_label.NP_container/NP_F1 <= 109",
        "P_F1", expectListPValuesByNP);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByFilter(stmt, "P_container", "list_label",
        "list_label.NP_container/NP_F1 > 105 "
        "and list_label.NP_container/NP_F1 <= 109",
        "P_F4", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    uint32_t expectListNPValuesByNP[4] = {106, 107, 108, 109};
    ret = ScanByFilter(stmt, "NP_container", "list_label",
        "list_label.NP_container/NP_F1 > 105 "
        "and list_label.NP_container/NP_F1 <= 109",
        "NP_F1", expectListNPValuesByNP);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByFilter(stmt, "NP_container", "list_label",
        "list_label.NP_container/NP_F1 > 105 "
        "and list_label.NP_container/NP_F1 <= 109",
        "NP_F4", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    uint32_t expectListCaseValuesByNP[4] = {107, 108, 109, 110};
    ret = ScanByFilter(stmt, "choice_1/case_1_1", "list_label",
        "list_label.NP_container/NP_F1 > 105 "
        "and list_label.NP_container/NP_F1 <= 109",
        "case_1_1_F1", expectListCaseValuesByNP);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByFilter(stmt, "choice_1/case_1_1", "list_label",
        "list_label.NP_container/NP_F1 > 105 "
        "and list_label.NP_container/NP_F1 <= 109",
        "case_1_1_F4", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByFilter(stmt, "list_label",
        "list_label.choice_1/case_1_1/case_1_1_F1 > 105 "
        "and list_label.choice_1/case_1_1/case_1_1_F1 <= 109",
        "root_F4", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(3, ret);
    uint32_t expectListPValuesByCase[3] = {105, 106, 107};
    ret = ScanByFilter(stmt, "P_container", "list_label",
        "list_label.choice_1/case_1_1/case_1_1_F1 > 105 "
        "and list_label.choice_1/case_1_1/case_1_1_F1 <= 109",
        "P_F1", expectListPValuesByCase);
    AW_MACRO_EXPECT_EQ_INT(3, ret);
    ret = ScanByFilter(stmt, "P_container", "list_label",
        "list_label.choice_1/case_1_1/case_1_1_F1 > 105 "
        "and list_label.choice_1/case_1_1/case_1_1_F1 <= 109",
        "P_F4", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(3, ret);
    uint32_t expectListNPValuesByCase[3] = {106, 107, 108};
    ret = ScanByFilter(stmt, "NP_container", "list_label",
        "list_label.choice_1/case_1_1/case_1_1_F1 > 105 "
        "and list_label.choice_1/case_1_1/case_1_1_F1 <= 109",
        "NP_F1", expectListNPValuesByCase);
    AW_MACRO_EXPECT_EQ_INT(3, ret);
    ret = ScanByFilter(stmt, "NP_container", "list_label",
        "list_label.choice_1/case_1_1/case_1_1_F1 > 105 "
        "and list_label.choice_1/case_1_1/case_1_1_F1 <= 109",
        "NP_F4", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(3, ret);
    uint32_t expectListCaseValuesByCase[3] = {107, 108, 109};
    ret = ScanByFilter(stmt, "choice_1/case_1_1", "list_label",
        "list_label.choice_1/case_1_1/case_1_1_F1 > 105 "
        "and list_label.choice_1/case_1_1/case_1_1_F1 <= 109",
        "case_1_1_F1", expectListCaseValuesByCase);
    AW_MACRO_EXPECT_EQ_INT(3, ret);
    ret = ScanByFilter(stmt, "choice_1/case_1_1", "list_label",
        "list_label.choice_1/case_1_1/case_1_1_F1 > 105 "
        "and list_label.choice_1/case_1_1/case_1_1_F1 <= 109",
        "case_1_1_F4", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(3, ret);
    // 表达式中带有被when删的字段 DTS2024042507438
    ret = WrongFilter(stmt, "list_label", "main_label.P_container/P_F4 > 106");
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = ScanByFilter(stmt, "list_label", "root_F4 > 106", "root_F1", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByFilter(stmt, "list_label", "list_label.P_container/P_F4 > 106", "root_F1", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByFilter(stmt, "list_label", "list_label.NP_container/NP_F4 > 107", "root_F1", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByFilter(
        stmt, "list_label", "list_label.choice_1/case_1_1/case_1_1_F4 > 108", "root_F1", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // case节点违背约束
    SetConRootAndIncreValid("when_2", 1001);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_3.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // case节点违背约束
    SetConRootAndIncreValid("when_3", 1001);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_4.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // case节点违背约束
    SetConRootAndIncreValid("when_4", 1001);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_5.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 017.开启diff，DML操作导致when删，用set primarykey value、name scan表中的数据
TEST_F(yang_vertex_scan_normal, Yang_085_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_UNEXPECTED_NULL_VALUE);
    AddWhiteList(GMERR_ARRAY_SUBSCRIPT_ERROR);
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/all_node_with_when.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_2.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck();

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写数据
    SetAllNodeProperty(GMC_YANG_DIFF_DELAY_READ_ON);
    SetConRootAndIncreValid("root_F5", 103, expectDiff13S1);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_1.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 各种字段违背约束
    SetConRootAndIncreValid("when_1", 1001, expectDiff13S2);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_2.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keys[8] = {0};
    keys[0] = 1;
    ret = ScanByKeyValue(stmt, "main_label", "pk", 1, keys, "root_F5", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "P_container", "main_label", "pk", 1, keys, "P_F1", 101);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "P_container", "main_label", "pk", 1, keys, "P_F4", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "main_label", "pk", 1, keys, "NP_F1", 102);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "main_label", "pk", 1, keys, "NP_F4", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "main_label", "pk", 1, keys, "case_1_1_F1", 103);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "main_label", "pk", 1, keys, "case_1_1_F4", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    keys[1] = 104;
    ret = ScanByKeyValue(stmt, "list_label", "pk", 2, keys, "root_F1", 104);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "list_label", "pk", 2, keys, "root_F4", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "P_container", "list_label", "pk", 2, keys, "P_F1", 105);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "P_container", "list_label", "pk", 2, keys, "P_F4", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "list_label", "pk", 2, keys, "NP_F1", 106);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "list_label", "pk", 2, keys, "NP_F4", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "list_label", "pk", 2, keys, "case_1_1_F1", 107);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "list_label", "pk", 2, keys, "case_1_1_F4", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // case节点违背约束
    SetConRootAndIncreValid("when_2", 1001, expectDiff13S3);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_3.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 因为choice下没有数据，所以choice也为空，getnode时nodePath传“空的父节点/空的子节点”会报1004001
    ScanNoElement(stmt, "choice_1/case_1_1", "main_label", "pk", 1, keys);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // case节点违背约束
    SetConRootAndIncreValid("when_3", 1001, expectDiff13S4);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_4.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ScanNodeNoElement(stmt, "P_container", "main_label", "pk", 1, keys, "P_F1");
    ScanNodeNoElement(stmt, "NP_container", "main_label", "pk", 1, keys, "NP_F1");
    ScanNodeNoElement(stmt, "P_container", "list_label", "pk", 2, keys, "P_F1");
    ScanNodeNoElement(stmt, "NP_container", "list_label", "pk", 2, keys, "NP_F1");

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // case节点违背约束
    SetConRootAndIncreValid("when_4", 1001, expectDiff13S5);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_5.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanByKeyValue(stmt, "list_label", "pk", 2, keys, "root_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    keys[1] = 108;
    ret = ScanByKeyValue(stmt, "leaf-list_label_1", "pk", 2, keys, "root_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 018.开启diff，DML操作导致when删，用set localkey value、name scan表中的数据
TEST_F(yang_vertex_scan_normal, Yang_085_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_UNEXPECTED_NULL_VALUE);
    AddWhiteList(GMERR_ARRAY_SUBSCRIPT_ERROR);
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/all_node_with_when.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_2.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck();

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写数据
    SetAllNodeProperty(GMC_YANG_DIFF_DELAY_READ_ON);
    SetConRootAndIncreValid("root_F5", 103, expectDiff13S1);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_1.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写数据
    SetConRootAndIncreValid("when_1", 1001, expectDiff13S2);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_2.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t conlks[1] = {100};
    uint32_t listlks[1] = {104};
    ret = ScanByKeyValue(stmt, "main_label", "lk", 1, conlks, "root_F5", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "P_container", "main_label", "lk", 1, conlks, "P_F1", 101);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "P_container", "main_label", "lk", 1, conlks, "P_F4", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "main_label", "lk", 1, conlks, "NP_F1", 102);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "main_label", "lk", 1, conlks, "NP_F4", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "main_label", "lk", 1, conlks, "case_1_1_F1", 103);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "main_label", "lk", 1, conlks, "case_1_1_F4", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "list_label", "lk", 1, listlks, "root_F1", 104);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "list_label", "lk", 1, listlks, "root_F4", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "P_container", "list_label", "lk", 1, listlks, "P_F1", 105);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "P_container", "list_label", "lk", 1, listlks, "P_F4", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "list_label", "lk", 1, listlks, "NP_F1", 106);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "list_label", "lk", 1, listlks, "NP_F4", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "list_label", "lk", 1, listlks, "case_1_1_F1", 107);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "list_label", "lk", 1, listlks, "case_1_1_F4", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // case节点违背约束
    SetConRootAndIncreValid("when_2", 1001, expectDiff13S3);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_3.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 因为choice下没有数据，所以choice也为空，getnode时nodePath传“空的父节点/空的子节点”会报1004001
    ScanNoElement(stmt, "choice_1/case_1_1", "main_label", "lk", 1, conlks);
    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // case节点违背约束
    SetConRootAndIncreValid("when_3", 1001, expectDiff13S4);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_4.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ScanNodeNoElement(stmt, "P_container", "main_label", "lk", 1, conlks, "P_F1");
    ScanNodeNoElement(stmt, "NP_container", "main_label", "lk", 1, conlks, "NP_F1");
    ScanNodeNoElement(stmt, "P_container", "list_label", "lk", 1, listlks, "P_F1");
    ScanNodeNoElement(stmt, "NP_container", "list_label", "lk", 1, listlks, "NP_F1");

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // case节点违背约束
    SetConRootAndIncreValid("when_4", 1001, expectDiff13S5);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_5.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanByKeyValue(stmt, "list_label", "lk", 1, listlks, "root_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    uint32_t leafListKeys[1] = {108};
    ret = ScanByKeyValue(stmt, "leaf-list_label_1", "lk", 1, leafListKeys, "root_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 019.开启diff，DML操作导致when删，用set localhashkey value、name scan表中的数据
TEST_F(yang_vertex_scan_normal, Yang_085_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_UNEXPECTED_NULL_VALUE);
    AddWhiteList(GMERR_ARRAY_SUBSCRIPT_ERROR);
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/all_node_with_when.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_2.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck();

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写数据
    SetAllNodeProperty(GMC_YANG_DIFF_DELAY_READ_ON);
    SetConRootAndIncreValid("root_F5", 103, expectDiff13S1);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_1.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写数据
    SetConRootAndIncreValid("when_1", 1001, expectDiff13S2);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_2.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t conlhks[1] = {101};
    uint32_t listlhks[1] = {105};
    ret = ScanByKeyValue(stmt, "main_label", "lhk", 1, conlhks, "root_F5", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "P_container", "main_label", "lhk", 1, conlhks, "P_F1", 101);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "P_container", "main_label", "lhk", 1, conlhks, "P_F4", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "main_label", "lhk", 1, conlhks, "NP_F1", 102);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "main_label", "lhk", 1, conlhks, "NP_F4", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "main_label", "lhk", 1, conlhks, "case_1_1_F1", 103);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "main_label", "lhk", 1, conlhks, "case_1_1_F4", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "list_label", "lhk", 1, listlhks, "root_F1", 104);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "list_label", "lhk", 1, listlhks, "root_F4", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "P_container", "list_label", "lhk", 1, listlhks, "P_F1", 105);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "P_container", "list_label", "lhk", 1, listlhks, "P_F4", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "list_label", "lhk", 1, listlhks, "NP_F1", 106);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "list_label", "lhk", 1, listlhks, "NP_F4", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "list_label", "lhk", 1, listlhks, "case_1_1_F1", 107);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "list_label", "lhk", 1, listlhks, "case_1_1_F4", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // case节点违背约束
    SetConRootAndIncreValid("when_2", 1001, expectDiff13S3);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_3.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 因为choice下没有数据，所以choice也为空，getnode时nodePath传“空的父节点/空的子节点”会报1004001
    ScanNoElement(stmt, "choice_1/case_1_1", "main_label", "lhk", 1, conlhks);
    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // case节点违背约束
    SetConRootAndIncreValid("when_3", 1001, expectDiff13S4);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_4.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ScanNodeNoElement(stmt, "P_container", "main_label", "lhk", 1, conlhks, "P_F1");
    ScanNodeNoElement(stmt, "NP_container", "main_label", "lhk", 1, conlhks, "NP_F1");
    ScanNodeNoElement(stmt, "P_container", "list_label", "lhk", 1, listlhks, "P_F1");
    ScanNodeNoElement(stmt, "NP_container", "list_label", "lhk", 1, listlhks, "NP_F1");

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // case节点违背约束
    SetConRootAndIncreValid("when_4", 1001, expectDiff13S5);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_5.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ScanByKeyValue(stmt, "list_label", "lhk", 1, listlhks, "root_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    uint32_t leafListKeys[1] = {108};
    ret = ScanByKeyValue(stmt, "leaf-list_label_1", "lhk", 1, leafListKeys, "root_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 020.开启diff，DML操作导致when删，用set localkey range、name scan表中的数据
TEST_F(yang_vertex_scan_normal, Yang_085_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_UNEXPECTED_NULL_VALUE);
    AddWhiteList(GMERR_ARRAY_SUBSCRIPT_ERROR);
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/all_node_with_when.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_2.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck();

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写数据
    SetAllNodeProperty(GMC_YANG_DIFF_DELAY_READ_ON);
    SetConRootAndIncreValid("root_F5", 103, expectDiff13S1);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_1.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写数据
    SetConRootAndIncreValid("when_1", 1001, expectDiff13S2);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_2.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    unsigned int arrLen = 1;
    uint32_t leftValue = 106;
    uint32_t rightValue = 110;
    GmcPropValueT leftVals[arrLen];
    GmcPropValueT rightVals[arrLen];
    leftVals[0].size = sizeof(uint32_t);
    leftVals[0].type = GMC_DATATYPE_UINT32;
    leftVals[0].value = &leftValue;
    rightVals[0].size = sizeof(uint32_t);
    rightVals[0].type = GMC_DATATYPE_UINT32;
    rightVals[0].value = &rightValue;

    GmcRangeItemT items1[arrLen];
    items1[0].lValue = NULL;
    items1[0].rValue = NULL;
    items1[0].lFlag = GMC_COMPARE_RANGE_OPEN;
    items1[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items1[0].order = GMC_ORDER_ASC;

    GmcRangeItemT items2[arrLen];
    items2[0].lValue = &leftVals[0];
    items2[0].rValue = &rightVals[0];
    items2[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items2[0].rFlag = GMC_COMPARE_RANGE_OPEN;
    items2[0].order = GMC_ORDER_DESC;

    uint32_t expectRootValues1[10] = {104, 105, 106, 107, 108, 109, 110, 111, 112, 113};
    ret = ScanByRange(stmt, "list_label", items1, arrLen, "root_F1", expectRootValues1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    uint32_t expectRootValues2[4] = {109, 108, 107, 106};
    ret = ScanByRange(stmt, "list_label", items2, arrLen, "root_F1", expectRootValues2);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    uint32_t expectNoValue[10] = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
    ret = ScanByRange(stmt, "list_label", items1, arrLen, "root_F4", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    uint32_t expectPValues1[10] = {105, 106, 107, 108, 109, 110, 111, 112, 113, 114};
    ret = ScanByRange(stmt, "P_container", "list_label", items1, arrLen, "P_F1", expectPValues1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    uint32_t expectPValues2[4] = {110, 109, 108, 107};
    ret = ScanByRange(stmt, "P_container", "list_label", items2, arrLen, "P_F1", expectPValues2);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByRange(stmt, "P_container", "list_label", items1, arrLen, "P_F4", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    uint32_t expectNPValues1[10] = {106, 107, 108, 109, 110, 111, 112, 113, 114, 115};
    ret = ScanByRange(stmt, "NP_container", "list_label", items1, arrLen, "NP_F1", expectNPValues1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    uint32_t expectNPValues2[4] = {111, 110, 109, 108};
    ret = ScanByRange(stmt, "NP_container", "list_label", items2, arrLen, "NP_F1", expectNPValues2);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByRange(stmt, "NP_container", "list_label", items1, arrLen, "NP_F4", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    uint32_t expectCaseValues1[10] = {107, 108, 109, 110, 111, 112, 113, 114, 115, 116};
    ret = ScanByRange(stmt, "choice_1/case_1_1", "list_label", items1, arrLen, "case_1_1_F1", expectCaseValues1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    uint32_t expectCaseValues2[4] = {112, 111, 110, 109};
    ret = ScanByRange(stmt, "choice_1/case_1_1", "list_label", items2, arrLen, "case_1_1_F1", expectCaseValues2);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByRange(stmt, "choice_1/case_1_1", "list_label", items1, arrLen, "case_1_1_F4", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // case节点违背约束
    SetConRootAndIncreValid("when_2", 1001, expectDiff13S3);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_3.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 因为choice下没有数据，所以choice也为空，getnode时nodePath传“空的父节点/空的子节点”会报1004001
    ScanNoElement(stmt, "choice_1/case_1_1", "main_label", items1, arrLen);
    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // case节点违背约束
    SetConRootAndIncreValid("when_3", 1001, expectDiff13S4);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_4.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ScanNodeNoElement(stmt, "P_container", "list_label", items1, arrLen, "P_F1");
    ScanNodeNoElement(stmt, "P_container", "list_label", items2, arrLen, "P_F1");
    ScanNodeNoElement(stmt, "NP_container", "list_label", items1, arrLen, "NP_F1");
    ScanNodeNoElement(stmt, "NP_container", "list_label", items2, arrLen, "NP_F1");
    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // case节点违背约束
    SetConRootAndIncreValid("when_4", 1001, expectDiff13S5);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_5.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ScanByRange(stmt, "list_label", items1, arrLen, "root_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByRange(stmt, "list_label", items2, arrLen, "root_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByRange(stmt, "leaf-list_label_1", items1, arrLen, "root_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByRange(stmt, "leaf-list_label_1", items2, arrLen, "root_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 021.开启diff，DML操作导致when删，用SetFilter scan表中的数据
TEST_F(yang_vertex_scan_normal, Yang_085_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_UNEXPECTED_NULL_VALUE);
    AddWhiteList(GMERR_ARRAY_SUBSCRIPT_ERROR);
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/all_node_with_when.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_2.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck();

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写数据
    SetAllNodeProperty(GMC_YANG_DIFF_DELAY_READ_ON);
    SetConRootAndIncreValid("root_F5", 103, expectDiff13S1);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_1.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写数据
    SetConRootAndIncreValid("when_1", 1001, expectDiff13S2);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_2.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t expectNoValues[10] = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
    ret = ScanByFilter(stmt, "main_label", "root_F4 = 102", "root_F5", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    uint32_t expectConPValues[1] = {101};
    ret = ScanByFilter(stmt, "P_container", "main_label", "root_F4 = 102", "P_F1", expectConPValues);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(stmt, "P_container", "main_label", "root_F4 = 102", "P_F4", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    uint32_t expectConNPValues[1] = {102};
    ret = ScanByFilter(stmt, "NP_container", "main_label", "root_F4 = 102", "NP_F1", expectConNPValues);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(stmt, "NP_container", "main_label", "root_F4 = 102", "NP_F4", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    uint32_t expectConCaseValues[1] = {103};
    ret = ScanByFilter(stmt, "choice_1/case_1_1", "main_label", "root_F4 = 102", "case_1_1_F1", expectConCaseValues);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(stmt, "choice_1/case_1_1", "main_label", "root_F4 = 102", "case_1_1_F4", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret =
        ScanByFilter(stmt, "P_container", "main_label", "main_label.P_container/P_F1 = 101", "P_F1", expectConPValues);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(
        stmt, "P_container", "main_label", "main_label.P_container/P_F1 = 101", "P_F4", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(
        stmt, "NP_container", "main_label", "main_label.P_container/P_F1 = 101", "NP_F1", expectConNPValues);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(
        stmt, "NP_container", "main_label", "main_label.P_container/P_F1 = 101", "NP_F4", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(stmt, "choice_1/case_1_1", "main_label", "main_label.P_container/P_F1 = 101", "case_1_1_F1",
        expectConCaseValues);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(stmt, "choice_1/case_1_1", "main_label", "main_label.P_container/P_F1 = 101", "case_1_1_F4",
        expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(
        stmt, "P_container", "main_label", "main_label.NP_container/NP_F1 = 102", "P_F1", expectConPValues);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(
        stmt, "P_container", "main_label", "main_label.NP_container/NP_F1 = 102", "P_F4", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(
        stmt, "NP_container", "main_label", "main_label.NP_container/NP_F1 = 102", "NP_F1", expectConNPValues);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(
        stmt, "NP_container", "main_label", "main_label.NP_container/NP_F1 = 102", "NP_F4", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(stmt, "choice_1/case_1_1", "main_label", "main_label.NP_container/NP_F1 = 102", "case_1_1_F1",
        expectConCaseValues);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(stmt, "choice_1/case_1_1", "main_label", "main_label.NP_container/NP_F1 = 102", "case_1_1_F4",
        expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(
        stmt, "P_container", "main_label", "main_label.choice_1/case_1_1/case_1_1_F1 = 103", "P_F1", expectConPValues);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(stmt, "P_container", "main_label", "main_label.choice_1/case_1_1/case_1_1_F1 = 103", "P_F4",
        expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(stmt, "NP_container", "main_label", "main_label.choice_1/case_1_1/case_1_1_F1 = 103", "NP_F1",
        expectConNPValues);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(stmt, "NP_container", "main_label", "main_label.choice_1/case_1_1/case_1_1_F1 = 103", "NP_F4",
        expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(stmt, "choice_1/case_1_1", "main_label", "main_label.choice_1/case_1_1/case_1_1_F1 = 103",
        "case_1_1_F1", expectConCaseValues);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(stmt, "choice_1/case_1_1", "main_label", "main_label.choice_1/case_1_1/case_1_1_F1 = 103",
        "case_1_1_F4", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    // 表达式中带有被when删的字段
    ret = ScanByFilter(stmt, "main_label", "root_F5 = 103", "root_F1", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByFilter(stmt, "main_label", "main_label.P_container/P_F4 = 102", "root_F1", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByFilter(stmt, "main_label", "main_label.NP_container/NP_F4 = 103", "root_F1", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByFilter(
        stmt, "main_label", "main_label.choice_1/case_1_1/case_1_1_F4 = 104", "root_F1", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = ScanByFilter(stmt, "list_label", "root_F1 > 105 and root_F1 <= 109", "root_F4", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    uint32_t expectListPValues[4] = {107, 108, 109, 110};
    ret =
        ScanByFilter(stmt, "P_container", "list_label", "root_F1 > 105 and root_F1 <= 109", "P_F1", expectListPValues);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByFilter(
        stmt, "P_container", "list_label", "root_F1 > 105 and root_F1 <= 109", "P_F4", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    uint32_t expectListNPValues[4] = {108, 109, 110, 111};
    ret = ScanByFilter(
        stmt, "NP_container", "list_label", "root_F1 > 105 and root_F1 <= 109", "NP_F1", expectListNPValues);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByFilter(
        stmt, "NP_container", "list_label", "root_F1 > 105 and root_F1 <= 109", "NP_F4", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    uint32_t expectListCaseValues[4] = {109, 110, 111, 112};
    ret = ScanByFilter(stmt, "choice_1/case_1_1", "list_label", "root_F1 > 105 and root_F1 <= 109", "case_1_1_F1",
        expectListCaseValues);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByFilter(stmt, "choice_1/case_1_1", "list_label", "root_F1 > 105 and root_F1 <= 109", "case_1_1_F4",
        expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByFilter(stmt, "list_label", "list_label.P_container/P_F1 > 105 and list_label.P_container/P_F1 <= 109",
        "root_F4", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    uint32_t expectListPValuesByP[4] = {106, 107, 108, 109};
    ret = ScanByFilter(stmt, "P_container", "list_label",
        "list_label.P_container/P_F1 > 105 "
        "and list_label.P_container/P_F1 <= 109",
        "P_F1", expectListPValuesByP);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByFilter(stmt, "P_container", "list_label",
        "list_label.P_container/P_F1 > 105 "
        "and list_label.P_container/P_F1 <= 109",
        "P_F4", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    uint32_t expectListNPValuesByP[4] = {107, 108, 109, 110};
    ret = ScanByFilter(stmt, "NP_container", "list_label",
        "list_label.P_container/P_F1 > 105 "
        "and list_label.P_container/P_F1 <= 109",
        "NP_F1", expectListNPValuesByP);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByFilter(stmt, "NP_container", "list_label",
        "list_label.P_container/P_F1 > 105 "
        "and list_label.P_container/P_F1 <= 109",
        "NP_F4", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    uint32_t expectListCaseValuesByP[4] = {108, 109, 110, 111};
    ret = ScanByFilter(stmt, "choice_1/case_1_1", "list_label",
        "list_label.P_container/P_F1 > 105 "
        "and list_label.P_container/P_F1 <= 109",
        "case_1_1_F1", expectListCaseValuesByP);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByFilter(stmt, "choice_1/case_1_1", "list_label",
        "list_label.P_container/P_F1 > 105 "
        "and list_label.P_container/P_F1 <= 109",
        "case_1_1_F4", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByFilter(stmt, "list_label",
        "list_label.NP_container/NP_F1 > 105 "
        "and list_label.NP_container/NP_F1 <= 109",
        "root_F4", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    uint32_t expectListPValuesByNP[4] = {105, 106, 107, 108};
    ret = ScanByFilter(stmt, "P_container", "list_label",
        "list_label.NP_container/NP_F1 > 105 "
        "and list_label.NP_container/NP_F1 <= 109",
        "P_F1", expectListPValuesByNP);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByFilter(stmt, "P_container", "list_label",
        "list_label.NP_container/NP_F1 > 105 "
        "and list_label.NP_container/NP_F1 <= 109",
        "P_F4", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    uint32_t expectListNPValuesByNP[4] = {106, 107, 108, 109};
    ret = ScanByFilter(stmt, "NP_container", "list_label",
        "list_label.NP_container/NP_F1 > 105 "
        "and list_label.NP_container/NP_F1 <= 109",
        "NP_F1", expectListNPValuesByNP);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByFilter(stmt, "NP_container", "list_label",
        "list_label.NP_container/NP_F1 > 105 "
        "and list_label.NP_container/NP_F1 <= 109",
        "NP_F4", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    uint32_t expectListCaseValuesByNP[4] = {107, 108, 109, 110};
    ret = ScanByFilter(stmt, "choice_1/case_1_1", "list_label",
        "list_label.NP_container/NP_F1 > 105 "
        "and list_label.NP_container/NP_F1 <= 109",
        "case_1_1_F1", expectListCaseValuesByNP);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByFilter(stmt, "choice_1/case_1_1", "list_label",
        "list_label.NP_container/NP_F1 > 105 "
        "and list_label.NP_container/NP_F1 <= 109",
        "case_1_1_F4", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByFilter(stmt, "list_label",
        "list_label.choice_1/case_1_1/case_1_1_F1 > 105 "
        "and list_label.choice_1/case_1_1/case_1_1_F1 <= 109",
        "root_F4", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(3, ret);
    uint32_t expectListPValuesByCase[3] = {105, 106, 107};
    ret = ScanByFilter(stmt, "P_container", "list_label",
        "list_label.choice_1/case_1_1/case_1_1_F1 > 105 "
        "and list_label.choice_1/case_1_1/case_1_1_F1 <= 109",
        "P_F1", expectListPValuesByCase);
    AW_MACRO_EXPECT_EQ_INT(3, ret);
    ret = ScanByFilter(stmt, "P_container", "list_label",
        "list_label.choice_1/case_1_1/case_1_1_F1 > 105 "
        "and list_label.choice_1/case_1_1/case_1_1_F1 <= 109",
        "P_F4", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(3, ret);
    uint32_t expectListNPValuesByCase[3] = {106, 107, 108};
    ret = ScanByFilter(stmt, "NP_container", "list_label",
        "list_label.choice_1/case_1_1/case_1_1_F1 > 105 "
        "and list_label.choice_1/case_1_1/case_1_1_F1 <= 109",
        "NP_F1", expectListNPValuesByCase);
    AW_MACRO_EXPECT_EQ_INT(3, ret);
    ret = ScanByFilter(stmt, "NP_container", "list_label",
        "list_label.choice_1/case_1_1/case_1_1_F1 > 105 "
        "and list_label.choice_1/case_1_1/case_1_1_F1 <= 109",
        "NP_F4", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(3, ret);
    uint32_t expectListCaseValuesByCase[3] = {107, 108, 109};
    ret = ScanByFilter(stmt, "choice_1/case_1_1", "list_label",
        "list_label.choice_1/case_1_1/case_1_1_F1 > 105 "
        "and list_label.choice_1/case_1_1/case_1_1_F1 <= 109",
        "case_1_1_F1", expectListCaseValuesByCase);
    AW_MACRO_EXPECT_EQ_INT(3, ret);
    ret = ScanByFilter(stmt, "choice_1/case_1_1", "list_label",
        "list_label.choice_1/case_1_1/case_1_1_F1 > 105 "
        "and list_label.choice_1/case_1_1/case_1_1_F1 <= 109",
        "case_1_1_F4", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(3, ret);
    // 表达式中带有被when删的字段
    ret = ScanByFilter(stmt, "list_label", "root_F4 > 106", "root_F1", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByFilter(stmt, "list_label", "list_label.P_container/P_F4 > 106", "root_F1", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByFilter(stmt, "list_label", "list_label.NP_container/NP_F4 > 107", "root_F1", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByFilter(
        stmt, "list_label", "list_label.choice_1/case_1_1/case_1_1_F4 > 108", "root_F1", expectNoValues, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    // DTS2024042507438
    ret = WrongFilter(stmt, "list_label", "main_label.root_F4 > 106");
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // case节点违背约束
    SetConRootAndIncreValid("when_2", 1001, expectDiff13S3);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_3.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // case节点违背约束
    SetConRootAndIncreValid("when_3", 1001, expectDiff13S4);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_4.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // case节点违背约束
    SetConRootAndIncreValid("when_4", 1001, expectDiff13S5);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/8_reply_5.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 022.各类型的yang模型，DML操作写其他case数据导致原case数据被删，用set primarykey value、name scan表中的数据
TEST_F(yang_vertex_scan_normal, Yang_085_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_ARRAY_SUBSCRIPT_ERROR);
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/all_node.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_2.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck();

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写数据
    SetAllNodeProperty();
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/3_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写另一case
    SetAnotherCase();
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/17_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keys[8] = {0};
    keys[0] = 1;
    ret = ScanByKeyValue(stmt, "choice_1/case_1_2", "main_label", "pk", 1, keys, "case_1_2_F1", 104);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_2", "main_label", "pk", 1, keys, "case_1_2_F4", 105);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    keys[1] = 104;
    ret = ScanByKeyValue(stmt, "choice_1/case_1_2", "list_label", "pk", 2, keys, "case_1_2_F1", 108);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_2", "list_label", "pk", 2, keys, "case_1_2_F4", 109);
    AW_MACRO_EXPECT_EQ_INT(1, ret);

    ScanNodeNoElement(stmt, "choice_1/case_1_1", "main_label", "pk", 1, keys, "case_1_1_F1");
    ScanNodeNoElement(stmt, "choice_1/case_1_1", "list_label", "pk", 2, keys, "case_1_1_F1");

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 023.各类型的yang模型，DML操作写其他case数据导致原case数据被删，用set local value、name scan 旧的case
TEST_F(yang_vertex_scan_normal, Yang_085_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_ARRAY_SUBSCRIPT_ERROR);
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/all_node.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_2.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck();

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写数据
    SetAllNodeProperty();
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/3_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写另一case
    SetAnotherCase();
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/17_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keys[8] = {0};
    keys[0] = 100;
    ret = ScanByKeyValue(stmt, "choice_1/case_1_2", "main_label", "lk", 1, keys, "case_1_2_F1", 104);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_2", "main_label", "lk", 1, keys, "case_1_2_F4", 105);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    keys[0] = 104;
    ret = ScanByKeyValue(stmt, "choice_1/case_1_2", "list_label", "lk", 1, keys, "case_1_2_F1", 108);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_2", "list_label", "lk", 1, keys, "case_1_2_F4", 109);
    AW_MACRO_EXPECT_EQ_INT(1, ret);

    keys[0] = 100;
    ScanNodeNoElement(stmt, "choice_1/case_1_1", "main_label", "lk", 1, keys, "case_1_1_F1");
    keys[0] = 104;
    ScanNodeNoElement(stmt, "choice_1/case_1_1", "list_label", "lk", 1, keys, "case_1_1_F1");

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 024.各类型的yang模型，DML操作写其他case数据导致原case数据被删，用set localhash value、name scan 旧的case
TEST_F(yang_vertex_scan_normal, Yang_085_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_ARRAY_SUBSCRIPT_ERROR);
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/all_node.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_2.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck();

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写数据
    SetAllNodeProperty();
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/3_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写另一case
    SetAnotherCase();
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/17_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keys[8] = {0};
    keys[0] = 101;
    ret = ScanByKeyValue(stmt, "choice_1/case_1_2", "main_label", "lhk", 1, keys, "case_1_2_F1", 104);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_2", "main_label", "lhk", 1, keys, "case_1_2_F4", 105);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    keys[0] = 105;
    ret = ScanByKeyValue(stmt, "choice_1/case_1_2", "list_label", "lhk", 1, keys, "case_1_2_F1", 108);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_2", "list_label", "lhk", 1, keys, "case_1_2_F4", 109);
    AW_MACRO_EXPECT_EQ_INT(1, ret);

    keys[0] = 101;
    ScanNodeNoElement(stmt, "choice_1/case_1_1", "main_label", "lhk", 1, keys, "case_1_1_F1");
    keys[0] = 105;
    ScanNodeNoElement(stmt, "choice_1/case_1_1", "list_label", "lhk", 1, keys, "case_1_1_F1");

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 025.各类型的yang模型，DML操作写其他case数据导致原case数据被删，用set local range、name scan 旧的case
TEST_F(yang_vertex_scan_normal, Yang_085_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_ARRAY_SUBSCRIPT_ERROR);
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/all_node.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_2.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck();

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写数据
    SetAllNodeProperty();
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/3_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写另一case
    SetAnotherCase();
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/17_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    unsigned int arrLen = 1;
    GmcRangeItemT items[arrLen];
    items[0].lValue = NULL;
    items[0].rValue = NULL;
    items[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].order = GMC_ORDER_ASC;

    uint32_t expectRootValues[1] = {104};
    ret = ScanByRange(stmt, "choice_1/case_1_2", "main_label", items, arrLen, "case_1_2_F1", expectRootValues);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    uint32_t expectListValues[10] = {108, 109, 110, 111, 112, 113, 114, 115, 116, 117};
    ret = ScanByRange(stmt, "choice_1/case_1_2", "list_label", items, arrLen, "case_1_2_F1", expectListValues);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    ret = ScanRangeNodeNoElement(stmt, "choice_1/case_1_1", "main_label", items, arrLen, "case_1_1_F1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    ret = ScanRangeNodeNoElement(stmt, "choice_1/case_1_1", "list_label", items, arrLen, "case_1_1_F1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 026.各类型的yang模型，DML操作写其他case数据导致原case数据被删，用Setfilter表达式 scan 旧的case
TEST_F(yang_vertex_scan_normal, Yang_085_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_ARRAY_SUBSCRIPT_ERROR);
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/all_node.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_2.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck();

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写数据
    SetAllNodeProperty();
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/3_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写另一case
    SetAnotherCase();
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/17_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t expectValues1[1] = {104};
    ret = ScanByFilter(stmt, "choice_1/case_1_2", "main_label", "root_F1 = 100", "case_1_2_F1", expectValues1);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    uint32_t expectValues2[1] = {100};
    ret = ScanByFilter(stmt, "main_label", "main_label.choice_1/case_1_2/case_1_2_F1 = 104", "root_F1", expectValues2);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    uint32_t expectValues3[10] = {108, 109, 110, 111, 112, 113, 114, 115, 116, 117};
    ret = ScanByFilter(stmt, "choice_1/case_1_2", "list_label", "root_F1 > 0", "case_1_2_F1", expectValues3);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    uint32_t expectValues4[10] = {104, 105, 106, 107, 108, 109, 110, 111, 112, 113};
    ret = ScanByFilter(stmt, "list_label", "list_label.choice_1/case_1_2/case_1_2_F1 > 0", "root_F1", expectValues4);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    ret = ScanFilterNoNodeElement(stmt, "choice_1/case_1_1", "main_label", "root_F1 = 100", "case_1_1_F1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    ret = ScanFilterNoNodeElement(stmt, "choice_1/case_1_1", "list_label", "root_F1 > 0", "case_1_2_F1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);

    uint32_t expectNoValue[1] = {0};
    ret = ScanByFilter(
        stmt, "main_label", "main_label.choice_1/case_1_1/case_1_1_F1 > 0", "root_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByFilter(
        stmt, "list_label", "list_label.choice_1/case_1_1/case_1_1_F1 > 0", "root_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 027.list表localkey不唯一，DML写冲突数据，set localkey value、name scan表中的数据
TEST_F(yang_vertex_scan_normal, Yang_085_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/all_node_ique_index.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_3.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck();

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写索引冲突的数据
    SetIQUE();
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/22_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keys[1] = {105};
    uint32_t unexistKeys[1] = {9999};
    uint32_t expectNoValue[4] = {0, 0, 0, 0};
    uint32_t expectRootValues[4] = {104, 109, 114, 119};
    ret = ScanAllByKeyValue(stmt, "list_label", "lk", 1, keys, "root_F1", expectRootValues);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanAllByKeyValue(stmt, "list_label", "lk", 1, keys, "root_F4", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanAllByKeyValue(stmt, "list_label", "lk", 1, unexistKeys, "root_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    uint32_t expectPValues[4] = {105, 110, 115, 120};
    ret = ScanAllByKeyValue(stmt, "P_container", "list_label", "lk", 1, keys, "P_F1", expectPValues);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanAllByKeyValue(stmt, "P_container", "list_label", "lk", 1, keys, "P_F2", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanAllByKeyValue(stmt, "P_container", "list_label", "lk", 1, unexistKeys, "P_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    uint32_t expectNPValues[4] = {106, 111, 116, 121};
    ret = ScanAllByKeyValue(stmt, "NP_container", "list_label", "lk", 1, keys, "NP_F1", expectNPValues);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanAllByKeyValue(stmt, "NP_container", "list_label", "lk", 1, keys, "NP_F2", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanAllByKeyValue(stmt, "NP_container", "list_label", "lk", 1, unexistKeys, "NP_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    uint32_t expectCaseValues[4] = {107, 112, 117, 122};
    ret = ScanAllByKeyValue(stmt, "choice_1/case_1_1", "list_label", "lk", 1, keys, "case_1_1_F1", expectCaseValues);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanAllByKeyValue(stmt, "choice_1/case_1_1", "list_label", "lk", 1, keys, "case_1_1_F2", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanAllByKeyValue(
        stmt, "choice_1/case_1_1", "list_label", "lk", 1, unexistKeys, "case_1_1_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 028.list表localkey不唯一，DML写冲突数据，set localhashkey value、name scan表中的数据
TEST_F(yang_vertex_scan_normal, Yang_085_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/all_node_ique_index.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_3.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck();

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写索引冲突的数据
    SetIQUE();
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/22_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keys[1] = {106};
    uint32_t unexistKeys[1] = {9999};
    uint32_t expectNoValue[5] = {0, 0, 0, 0, 0};
    uint32_t expectRootValues[5] = {104, 108, 112, 116, 120};
    ret = ScanAllByKeyValue(stmt, "list_label", "lhk", 1, keys, "root_F1", expectRootValues);
    AW_MACRO_EXPECT_EQ_INT(5, ret);
    ret = ScanAllByKeyValue(stmt, "list_label", "lhk", 1, keys, "root_F4", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(5, ret);
    ret = ScanAllByKeyValue(stmt, "list_label", "lhk", 1, unexistKeys, "root_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    uint32_t expectPValues[5] = {105, 109, 113, 117, 121};
    ret = ScanAllByKeyValue(stmt, "P_container", "list_label", "lhk", 1, keys, "P_F1", expectPValues);
    AW_MACRO_EXPECT_EQ_INT(5, ret);
    ret = ScanAllByKeyValue(stmt, "P_container", "list_label", "lhk", 1, keys, "P_F2", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(5, ret);
    ret = ScanAllByKeyValue(stmt, "P_container", "list_label", "lhk", 1, unexistKeys, "P_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    uint32_t expectNPValues[5] = {106, 110, 114, 118, 122};
    ret = ScanAllByKeyValue(stmt, "NP_container", "list_label", "lhk", 1, keys, "NP_F1", expectNPValues);
    AW_MACRO_EXPECT_EQ_INT(5, ret);
    ret = ScanAllByKeyValue(stmt, "NP_container", "list_label", "lhk", 1, keys, "NP_F2", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(5, ret);
    ret = ScanAllByKeyValue(stmt, "NP_container", "list_label", "lhk", 1, unexistKeys, "NP_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    uint32_t expectCaseValues[5] = {107, 111, 115, 119, 123};
    ret = ScanAllByKeyValue(stmt, "choice_1/case_1_1", "list_label", "lhk", 1, keys, "case_1_1_F1", expectCaseValues);
    AW_MACRO_EXPECT_EQ_INT(5, ret);
    ret =
        ScanAllByKeyValue(stmt, "choice_1/case_1_1", "list_label", "lhk", 1, keys, "case_1_1_F2", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(5, ret);
    ret = ScanAllByKeyValue(
        stmt, "choice_1/case_1_1", "list_label", "lhk", 1, unexistKeys, "case_1_1_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 029.list表localkey不唯一，DML写冲突数据，set localkey range、name scan表中的数据
TEST_F(yang_vertex_scan_normal, Yang_085_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/all_node_ique_index.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_3.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck();

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写索引冲突的数据
    SetIQUE();
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/22_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    unsigned int arrLen = 1;
    uint32_t leftValue = 106;
    uint32_t rightValue = 108;
    GmcPropValueT leftVals[arrLen];
    GmcPropValueT rightVals[arrLen];
    leftVals[0].size = sizeof(uint32_t);
    leftVals[0].type = GMC_DATATYPE_UINT32;
    leftVals[0].value = &leftValue;
    rightVals[0].size = sizeof(uint32_t);
    rightVals[0].type = GMC_DATATYPE_UINT32;
    rightVals[0].value = &rightValue;

    GmcRangeItemT items1[arrLen];
    items1[0].lValue = NULL;
    items1[0].rValue = NULL;
    items1[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items1[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items1[0].order = GMC_ORDER_ASC;

    GmcRangeItemT items2[arrLen];
    items2[0].lValue = &leftVals[0];
    items2[0].rValue = &rightVals[0];
    items2[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items2[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items2[0].order = GMC_ORDER_ASC;

    GmcRangeItemT items3[arrLen];
    items3[0].lValue = &leftVals[0];
    items3[0].rValue = &rightVals[0];
    items3[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items3[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items3[0].order = GMC_ORDER_DESC;

    uint32_t unexistLeftValue = 8998;
    uint32_t unexistRightValue = 9999;
    GmcPropValueT unexistLeftVals[arrLen];
    GmcPropValueT unexistRightVals[arrLen];
    unexistLeftVals[0].size = sizeof(uint32_t);
    unexistLeftVals[0].type = GMC_DATATYPE_UINT32;
    unexistLeftVals[0].value = &unexistLeftValue;
    unexistRightVals[0].size = sizeof(uint32_t);
    unexistRightVals[0].type = GMC_DATATYPE_UINT32;
    unexistRightVals[0].value = &unexistRightValue;
    GmcRangeItemT unexistItems[arrLen];
    unexistItems[0].lValue = &unexistLeftVals[0];
    unexistItems[0].rValue = &unexistRightVals[0];
    unexistItems[0].lFlag = GMC_COMPARE_RANGE_OPEN;
    unexistItems[0].rFlag = GMC_COMPARE_RANGE_OPEN;
    unexistItems[0].order = GMC_ORDER_ASC;

    uint32_t expectRootValues1[20] = {
        104, 109, 114, 119, 105, 110, 115, 120, 106, 111, 116, 121, 107, 112, 117, 122, 108, 113, 118, 123};
    ret = ScanByRange(stmt, "list_label", items1, arrLen, "root_F1", expectRootValues1);
    AW_MACRO_EXPECT_EQ_INT(20, ret);
    uint32_t expectRootValues2[12] = {105, 110, 115, 120, 106, 111, 116, 121, 107, 112, 117, 122};
    ret = ScanByRange(stmt, "list_label", items2, arrLen, "root_F1", expectRootValues2);
    AW_MACRO_EXPECT_EQ_INT(12, ret);
    uint32_t expectRootValues3[12] = {122, 117, 112, 107, 121, 116, 111, 106, 120, 115, 110, 105};
    ret = ScanByRange(stmt, "list_label", items3, arrLen, "root_F1", expectRootValues3);
    AW_MACRO_EXPECT_EQ_INT(12, ret);
    uint32_t expectNoValue[20] = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
    ret = ScanByRange(stmt, "list_label", items1, arrLen, "root_F4", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(20, ret);
    ret = ScanByRange(stmt, "list_label", unexistItems, arrLen, "root_F4", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    uint32_t expectPValues1[20] = {
        105, 110, 115, 120, 106, 111, 116, 121, 107, 112, 117, 122, 108, 113, 118, 123, 109, 114, 119, 124};
    ret = ScanByRange(stmt, "P_container", "list_label", items1, arrLen, "P_F1", expectPValues1);
    AW_MACRO_EXPECT_EQ_INT(20, ret);
    uint32_t expectPValues2[12] = {106, 111, 116, 121, 107, 112, 117, 122, 108, 113, 118, 123};
    ret = ScanByRange(stmt, "P_container", "list_label", items2, arrLen, "P_F1", expectPValues2);
    AW_MACRO_EXPECT_EQ_INT(12, ret);
    uint32_t expectPValues3[12] = {123, 118, 113, 108, 122, 117, 112, 107, 121, 116, 111, 106};
    ret = ScanByRange(stmt, "P_container", "list_label", items3, arrLen, "P_F1", expectPValues3);
    AW_MACRO_EXPECT_EQ_INT(12, ret);
    ret = ScanByRange(stmt, "P_container", "list_label", items1, arrLen, "P_F2", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(20, ret);
    ret = ScanByRange(stmt, "P_container", "list_label", unexistItems, arrLen, "P_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    uint32_t expectNPValues1[20] = {
        106, 111, 116, 121, 107, 112, 117, 122, 108, 113, 118, 123, 109, 114, 119, 124, 110, 115, 120, 125};
    ret = ScanByRange(stmt, "NP_container", "list_label", items1, arrLen, "NP_F1", expectNPValues1);
    AW_MACRO_EXPECT_EQ_INT(20, ret);
    uint32_t expectNPValues2[12] = {107, 112, 117, 122, 108, 113, 118, 123, 109, 114, 119, 124};
    ret = ScanByRange(stmt, "NP_container", "list_label", items2, arrLen, "NP_F1", expectNPValues2);
    AW_MACRO_EXPECT_EQ_INT(12, ret);
    uint32_t expectNPValues3[12] = {124, 119, 114, 109, 123, 118, 113, 108, 122, 117, 112, 107};
    ret = ScanByRange(stmt, "NP_container", "list_label", items3, arrLen, "NP_F1", expectNPValues3);
    AW_MACRO_EXPECT_EQ_INT(12, ret);
    ret = ScanByRange(stmt, "NP_container", "list_label", items1, arrLen, "NP_F2", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(20, ret);
    ret = ScanByRange(stmt, "NP_container", "list_label", unexistItems, arrLen, "NP_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    uint32_t expectCaseValues1[20] = {
        107, 112, 117, 122, 108, 113, 118, 123, 109, 114, 119, 124, 110, 115, 120, 125, 111, 116, 121, 126};
    ret = ScanByRange(stmt, "choice_1/case_1_1", "list_label", items1, arrLen, "case_1_1_F1", expectCaseValues1);
    AW_MACRO_EXPECT_EQ_INT(20, ret);
    uint32_t expectCaseValues2[12] = {108, 113, 118, 123, 109, 114, 119, 124, 110, 115, 120, 125};
    ret = ScanByRange(stmt, "choice_1/case_1_1", "list_label", items2, arrLen, "case_1_1_F1", expectCaseValues2);
    AW_MACRO_EXPECT_EQ_INT(12, ret);
    uint32_t expectCaseValues3[12] = {125, 120, 115, 110, 124, 119, 114, 109, 123, 118, 113, 108};
    ret = ScanByRange(stmt, "choice_1/case_1_1", "list_label", items3, arrLen, "case_1_1_F1", expectCaseValues3);
    AW_MACRO_EXPECT_EQ_INT(12, ret);
    ret = ScanByRange(stmt, "choice_1/case_1_1", "list_label", items1, arrLen, "case_1_1_F2", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(20, ret);
    ret =
        ScanByRange(stmt, "choice_1/case_1_1", "list_label", unexistItems, arrLen, "case_1_1_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 030.建含max-elements=1的list表和leaflist表的yang模型，用仅一个字段的primarykey SetkeyValue、SetKeyName，scan表中数据
TEST_F(yang_vertex_scan_normal, Yang_085_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AsyncUserDataT data = {0};
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/all_node_one_key.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_3.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写数据
    SetAllNodePropertyOneKey();
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/36_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keys[8] = {0};
    uint32_t unexistKeys[8] = {9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999};
    keys[0] = 1;
    ret = ScanByKeyValue(stmt, "P_container", "list_label", "pk", 1, keys, "P_F1", 105);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "P_container", "list_label", "pk", 1, keys, "P_F2", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "P_container", "list_label", "pk", 1, unexistKeys, "P_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "list_label", "pk", 1, keys, "NP_F1", 106);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "list_label", "pk", 1, keys, "NP_F2", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "list_label", "pk", 1, unexistKeys, "NP_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "list_label", "pk", 1, keys, "case_1_1_F1", 107);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "list_label", "pk", 1, keys, "case_1_1_F2", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "list_label", "pk", 1, unexistKeys, "case_1_1_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 031.yang支持的各类索引（primary key、local key、localhash
// key）索引字段达到上限（32）（每个索引字段的数据类型是各种各样的）， 用这个primary key set key value、set key
// name查询各个节点下的字段
TEST_F(yang_vertex_scan_normal, Yang_085_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AsyncUserDataT data = {0};
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/all_node_32_key.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_4.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写数据
    SetAllNodeProperty32Key();
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/37_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keys[32] = {0};
    uint32_t unexistKeys[32] = {9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999,
        9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999};
    keys[0] = 1;
    for (uint32_t i = 1; i < 32; i++) {
        keys[i] = 104;
    }
    ret = ScanByKeyValue32(stmt, "list_label", "pk", 32, keys, "root_F1", 104);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue32(stmt, "list_label", "pk", 32, keys, "root_F35", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue32(stmt, "list_label", "pk", 32, unexistKeys, "root_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByKeyValue32(stmt, "P_container", "list_label", "pk", 32, keys, "P_F1", 109);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue32(stmt, "P_container", "list_label", "pk", 32, keys, "P_F2", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue32(stmt, "P_container", "list_label", "pk", 32, unexistKeys, "P_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByKeyValue32(stmt, "NP_container", "list_label", "pk", 32, keys, "NP_F1", 110);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue32(stmt, "NP_container", "list_label", "pk", 32, keys, "NP_F2", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue32(stmt, "NP_container", "list_label", "pk", 32, unexistKeys, "NP_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByKeyValue32(stmt, "choice_1/case_1_1", "list_label", "pk", 32, keys, "case_1_1_F1", 111);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue32(stmt, "choice_1/case_1_1", "list_label", "pk", 32, keys, "case_1_1_F2", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue32(stmt, "choice_1/case_1_1", "list_label", "pk", 32, unexistKeys, "case_1_1_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 032.yang支持的各类索引（primary key、local key、localhash
// key）索引字段达到上限（32）（每个索引字段的数据类型是各种各样的）， 用这个local key set key value、set key
// name查询各个节点下的字段
TEST_F(yang_vertex_scan_normal, Yang_085_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AsyncUserDataT data = {0};
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/all_node_32_key.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_4.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写数据
    SetAllNodeProperty32Key();
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/37_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keys[32] = {0};
    uint32_t unexistKeys[32] = {9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999,
        9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999};

    for (uint32_t i = 0; i < 32; i++) {
        keys[i] = 100;
    }
    ret = ScanByKeyValue(stmt, "main_label", "lk", 32, keys, "root_F1", 100);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "main_label", "lk", 32, keys, "root_F33", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "main_label", "lk", 32, unexistKeys, "root_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByKeyValue(stmt, "P_container", "main_label", "lk", 32, keys, "P_F1", 101);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "P_container", "main_label", "lk", 32, keys, "P_F2", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "P_container", "main_label", "lk", 32, unexistKeys, "P_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "main_label", "lk", 32, keys, "NP_F1", 102);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "main_label", "lk", 32, keys, "NP_F2", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "main_label", "lk", 32, unexistKeys, "NP_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "main_label", "lk", 32, keys, "case_1_1_F1", 103);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "main_label", "lk", 32, keys, "case_1_1_F2", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "main_label", "lk", 32, unexistKeys, "case_1_1_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    for (uint32_t i = 0; i < 32; i++) {
        keys[i] = 104;
    }
    ret = ScanByKeyValue(stmt, "list_label", "lk", 32, keys, "root_F1", 104);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "list_label", "lk", 32, keys, "root_F35", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "list_label", "lk", 32, unexistKeys, "root_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByKeyValue(stmt, "P_container", "list_label", "lk", 32, keys, "P_F1", 109);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "P_container", "list_label", "lk", 32, keys, "P_F2", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "P_container", "list_label", "lk", 32, unexistKeys, "P_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "list_label", "lk", 32, keys, "NP_F1", 110);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "list_label", "lk", 32, keys, "NP_F2", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "list_label", "lk", 32, unexistKeys, "NP_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "list_label", "lk", 32, keys, "case_1_1_F1", 111);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "list_label", "lk", 32, keys, "case_1_1_F2", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "list_label", "lk", 32, unexistKeys, "case_1_1_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 033.yang支持的各类索引（primary key、local key、localhash
// key）索引字段达到上限（32）（每个索引字段的数据类型是各种各样的）， 用这个localhash key set key value、set key
// name查询各个节点下的字段
TEST_F(yang_vertex_scan_normal, Yang_085_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AsyncUserDataT data = {0};
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/all_node_32_key.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_4.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写数据
    SetAllNodeProperty32Key();
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/37_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keys[32] = {0};
    uint32_t unexistKeys[32] = {9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999,
        9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999};

    for (uint32_t i = 0; i < 32; i++) {
        keys[i] = 100;
    }
    ret = ScanByKeyValue(stmt, "main_label", "lhk", 32, keys, "root_F1", 100);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "main_label", "lhk", 32, keys, "root_F33", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "main_label", "lhk", 32, unexistKeys, "root_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByKeyValue(stmt, "P_container", "main_label", "lhk", 32, keys, "P_F1", 101);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "P_container", "main_label", "lhk", 32, keys, "P_F2", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "P_container", "main_label", "lhk", 32, unexistKeys, "P_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "main_label", "lhk", 32, keys, "NP_F1", 102);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "main_label", "lhk", 32, keys, "NP_F2", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "main_label", "lhk", 32, unexistKeys, "NP_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "main_label", "lhk", 32, keys, "case_1_1_F1", 103);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "main_label", "lhk", 32, keys, "case_1_1_F2", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "main_label", "lhk", 32, unexistKeys, "case_1_1_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    for (uint32_t i = 0; i < 32; i++) {
        keys[i] = 104;
    }
    ret = ScanByKeyValue(stmt, "list_label", "lhk", 32, keys, "root_F1", 104);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "list_label", "lhk", 32, keys, "root_F35", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "list_label", "lhk", 32, unexistKeys, "root_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByKeyValue(stmt, "P_container", "list_label", "lhk", 32, keys, "P_F1", 109);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "P_container", "list_label", "lhk", 32, keys, "P_F2", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "P_container", "list_label", "lhk", 32, unexistKeys, "P_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "list_label", "lhk", 32, keys, "NP_F1", 110);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "list_label", "lhk", 32, keys, "NP_F2", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "NP_container", "list_label", "lhk", 32, unexistKeys, "NP_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "list_label", "lhk", 32, keys, "case_1_1_F1", 111);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "list_label", "lhk", 32, keys, "case_1_1_F2", 0, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValue(stmt, "choice_1/case_1_1", "list_label", "lhk", 32, unexistKeys, "case_1_1_F1", 0, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 034.yang支持的各类索引（primary key、local key、localhash
// key）索引字段达到上限（32）（每个索引字段的数据类型是各种各样的）， 用这个local key set key range、set key
// name查询各个节点下的字段
TEST_F(yang_vertex_scan_normal, Yang_085_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AsyncUserDataT data = {0};
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/all_node_32_key.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_4.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写数据
    SetAllNodeProperty32Key();
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/37_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    unsigned int arrLen = 32;
    uint32_t leftValue = 100;
    uint32_t rightValue = 105;
    GmcPropValueT leftVals[arrLen];
    GmcPropValueT rightVals[arrLen];
    for (uint32_t i = 0; i < arrLen; i++) {
        leftVals[i].size = sizeof(uint32_t);
        leftVals[i].type = GMC_DATATYPE_UINT32;
        leftVals[i].value = &leftValue;
        rightVals[i].size = sizeof(uint32_t);
        rightVals[i].type = GMC_DATATYPE_UINT32;
        rightVals[i].value = &rightValue;
    }

    GmcRangeItemT items1[arrLen];
    for (uint32_t i = 0; i < arrLen; i++) {
        items1[i].lValue = NULL;
        items1[i].rValue = NULL;
        items1[i].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items1[i].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items1[i].order = GMC_ORDER_ASC;
    }

    GmcRangeItemT items2[arrLen];
    for (uint32_t i = 0; i < arrLen; i++) {
        items2[i].lValue = &leftVals[i];
        items2[i].rValue = &rightVals[i];
        items2[i].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items2[i].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items2[i].order = GMC_ORDER_ASC;
    }

    GmcRangeItemT items3[arrLen];
    for (uint32_t i = 0; i < arrLen; i++) {
        items3[i].lValue = &leftVals[i];
        items3[i].rValue = &rightVals[i];
        items3[i].lFlag = GMC_COMPARE_RANGE_OPEN;
        items3[i].rFlag = GMC_COMPARE_RANGE_OPEN;
        items3[i].order = GMC_ORDER_ASC;
    }

    GmcRangeItemT items4[arrLen];
    for (uint32_t i = 0; i < arrLen; i++) {
        items4[i].lValue = &leftVals[i];
        items4[i].rValue = &rightVals[i];
        items4[i].lFlag = GMC_COMPARE_RANGE_OPEN;
        items4[i].rFlag = GMC_COMPARE_RANGE_OPEN;
        items4[i].order = GMC_ORDER_DESC;
    }

    uint32_t unexistLeftValue = 8998;
    uint32_t unexistRightValue = 9999;
    GmcPropValueT unexistLeftVals[arrLen];
    GmcPropValueT unexistRightVals[arrLen];
    for (uint32_t i = 0; i < arrLen; i++) {
        unexistLeftVals[i].size = sizeof(uint32_t);
        unexistLeftVals[i].type = GMC_DATATYPE_UINT32;
        unexistLeftVals[i].value = &unexistLeftValue;
        unexistRightVals[i].size = sizeof(uint32_t);
        unexistRightVals[i].type = GMC_DATATYPE_UINT32;
        unexistRightVals[i].value = &unexistRightValue;
    }
    GmcRangeItemT unexistItems[arrLen];
    for (uint32_t i = 0; i < arrLen; i++) {
        unexistItems[i].lValue = &unexistLeftVals[i];
        unexistItems[i].rValue = &unexistRightVals[i];
        unexistItems[i].lFlag = GMC_COMPARE_RANGE_OPEN;
        unexistItems[i].rFlag = GMC_COMPARE_RANGE_OPEN;
        unexistItems[i].order = GMC_ORDER_ASC;
    }

    uint32_t expectConRootF2Values[1] = {100};
    ret = ScanByRange(stmt, "main_label", items1, arrLen, "root_F2", expectConRootF2Values);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    uint32_t expectNoValue[10] = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
    ret = ScanByRange(stmt, "main_label", items1, arrLen, "root_F33", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByRange(stmt, "main_label", unexistItems, arrLen, "root_F2", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    uint32_t expectConPValues[1] = {101};
    ret = ScanByRange(stmt, "P_container", "main_label", items1, arrLen, "P_F1", expectConPValues);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByRange(stmt, "P_container", "main_label", items1, arrLen, "P_F2", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByRange(stmt, "P_container", "main_label", unexistItems, arrLen, "P_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    uint32_t expectConNPValues[1] = {102};
    ret = ScanByRange(stmt, "NP_container", "main_label", items1, arrLen, "NP_F1", expectConNPValues);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByRange(stmt, "NP_container", "main_label", items1, arrLen, "NP_F2", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByRange(stmt, "NP_container", "main_label", unexistItems, arrLen, "NP_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    uint32_t expectConCase1Values[1] = {103};
    ret = ScanByRange(stmt, "choice_1/case_1_1", "main_label", items1, arrLen, "case_1_1_F1", expectConCase1Values);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByRange(stmt, "choice_1/case_1_1", "main_label", items1, arrLen, "case_1_1_F2", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret =
        ScanByRange(stmt, "choice_1/case_1_1", "main_label", unexistItems, arrLen, "case_1_1_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    uint32_t expectListRootF2Values1[10] = {100, 101, 102, 103, 104, 105, 106, 107, 108, 109};
    ret = ScanByRange(stmt, "list_label", items1, arrLen, "root_F2", expectListRootF2Values1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    uint32_t expectListRootF2Values2[6] = {100, 101, 102, 103, 104, 105};
    ret = ScanByRange(stmt, "list_label", items2, arrLen, "root_F2", expectListRootF2Values2);
    AW_MACRO_EXPECT_EQ_INT(6, ret);
    uint32_t expectListRootF2Values3[4] = {101, 102, 103, 104};
    ret = ScanByRange(stmt, "list_label", items3, arrLen, "root_F2", expectListRootF2Values3);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    uint32_t expectListRootF2Values4[4] = {104, 103, 102, 101};
    ret = ScanByRange(stmt, "list_label", items4, arrLen, "root_F2", expectListRootF2Values4);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByRange(stmt, "list_label", items1, arrLen, "root_F35", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    ret = ScanByRange(stmt, "list_label", unexistItems, arrLen, "root_F2", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    uint32_t expectListPValues1[10] = {105, 106, 107, 108, 109, 110, 111, 112, 113, 114};
    ret = ScanByRange(stmt, "P_container", "list_label", items1, arrLen, "P_F1", expectListPValues1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    uint32_t expectListPValues2[6] = {105, 106, 107, 108, 109, 110};
    ret = ScanByRange(stmt, "P_container", "list_label", items2, arrLen, "P_F1", expectListPValues2);
    AW_MACRO_EXPECT_EQ_INT(6, ret);
    uint32_t expectListPValues3[4] = {106, 107, 108, 109};
    ret = ScanByRange(stmt, "P_container", "list_label", items3, arrLen, "P_F1", expectListPValues3);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    uint32_t expectListPValues4[4] = {109, 108, 107, 106};
    ret = ScanByRange(stmt, "P_container", "list_label", items4, arrLen, "P_F1", expectListPValues4);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByRange(stmt, "P_container", "list_label", items1, arrLen, "P_F2", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    ret = ScanByRange(stmt, "P_container", "list_label", unexistItems, arrLen, "P_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    uint32_t expectListNPValues1[10] = {106, 107, 108, 109, 110, 111, 112, 113, 114, 115};
    ret = ScanByRange(stmt, "NP_container", "list_label", items1, arrLen, "NP_F1", expectListNPValues1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    uint32_t expectListNPValues2[6] = {106, 107, 108, 109, 110, 111};
    ret = ScanByRange(stmt, "NP_container", "list_label", items2, arrLen, "NP_F1", expectListNPValues2);
    AW_MACRO_EXPECT_EQ_INT(6, ret);
    uint32_t expectListNPValues3[4] = {107, 108, 109, 110};
    ret = ScanByRange(stmt, "NP_container", "list_label", items3, arrLen, "NP_F1", expectListNPValues3);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    uint32_t expectListNPValues4[4] = {110, 109, 108, 107};
    ret = ScanByRange(stmt, "NP_container", "list_label", items4, arrLen, "NP_F1", expectListNPValues4);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByRange(stmt, "NP_container", "list_label", items1, arrLen, "NP_F2", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    ret = ScanByRange(stmt, "NP_container", "list_label", unexistItems, arrLen, "NP_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    uint32_t expectListCase1Values1[10] = {107, 108, 109, 110, 111, 112, 113, 114, 115, 116};
    ret = ScanByRange(stmt, "choice_1/case_1_1", "list_label", items1, arrLen, "case_1_1_F1", expectListCase1Values1);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    uint32_t expectListCase1Values2[6] = {107, 108, 109, 110, 111, 112};
    ret = ScanByRange(stmt, "choice_1/case_1_1", "list_label", items2, arrLen, "case_1_1_F1", expectListCase1Values2);
    AW_MACRO_EXPECT_EQ_INT(6, ret);
    uint32_t expectListCase1Values3[4] = {108, 109, 110, 111};
    ret = ScanByRange(stmt, "choice_1/case_1_1", "list_label", items3, arrLen, "case_1_1_F1", expectListCase1Values3);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    uint32_t expectListCase1Values4[4] = {111, 110, 109, 108};
    ret = ScanByRange(stmt, "choice_1/case_1_1", "list_label", items4, arrLen, "case_1_1_F1", expectListCase1Values4);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByRange(stmt, "choice_1/case_1_1", "list_label", items1, arrLen, "case_1_1_F2", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    ret =
        ScanByRange(stmt, "choice_1/case_1_1", "list_label", unexistItems, arrLen, "case_1_1_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 035.yang支持的各类索引（primary key、local key、localhash
// key）索引字段达到上限（32）（每个索引字段的数据类型是各种各样的）， 不用或不全用这些key SetFilter查询各个节点下的字段
TEST_F(yang_vertex_scan_normal, Yang_085_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AsyncUserDataT data = {0};
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/all_node_32_key.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_4.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写数据
    SetAllNodeProperty32Key();
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/37_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t expectValues1[1] = {100};
    ret = ScanByFilter(stmt, "main_label", "root_F4 = 100", "root_F1", expectValues1);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    uint32_t expectValues2[1] = {101};
    ret = ScanByFilter(stmt, "P_container", "main_label", "root_F4 = 100", "P_F1", expectValues2);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    uint32_t expectValues3[1] = {102};
    ret = ScanByFilter(stmt, "NP_container", "main_label", "root_F4 = 100", "NP_F1", expectValues3);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    uint32_t expectValues4[1] = {103};
    ret = ScanByFilter(stmt, "choice_1/case_1_1", "main_label", "root_F4 = 100", "case_1_1_F1", expectValues4);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    uint32_t expectNoValue[10] = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
    ret = ScanByFilter(stmt, "main_label", "root_F4 = 100", "root_F33", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(stmt, "main_label", "root_F4 = 200", "root_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = WrongFilter(stmt, "main_label", "list_label.root_F4 = 106");
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = WrongFilter(stmt, "main_label", "main_label.list_label/root_F4 = 106");
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_NAME, ret);
    ret = ScanByFilter(stmt, "main_label", "main_label.P_container/P_F4 = 102", "root_F1", expectValues1);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(stmt, "P_container", "main_label", "main_label.P_container/P_F4 = 102", "P_F1", expectValues2);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(stmt, "NP_container", "main_label", "main_label.P_container/P_F4 = 102", "NP_F1", expectValues3);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(
        stmt, "choice_1/case_1_1", "main_label", "main_label.P_container/P_F4 = 102", "case_1_1_F1", expectValues4);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(stmt, "main_label", "main_label.P_container/P_F4 = 102", "root_F33", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(stmt, "main_label", "main_label.P_container/P_F4 = 103", "root_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByFilter(stmt, "main_label", "main_label.NP_container/NP_F4 = 103", "root_F1", expectValues1);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(stmt, "P_container", "main_label", "main_label.NP_container/NP_F4 = 103", "P_F1", expectValues2);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret =
        ScanByFilter(stmt, "NP_container", "main_label", "main_label.NP_container/NP_F4 = 103", "NP_F1", expectValues3);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(
        stmt, "choice_1/case_1_1", "main_label", "main_label.NP_container/NP_F4 = 103", "case_1_1_F1", expectValues4);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(stmt, "main_label", "main_label.NP_container/NP_F4 = 103", "root_F33", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(stmt, "main_label", "main_label.NP_container/NP_F4 = 104", "root_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ScanByFilter(stmt, "main_label", "main_label.case_1_1/case_1_1_F4 = 104", "root_F1", expectValues1);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret =
        ScanByFilter(stmt, "P_container", "main_label", "main_label.case_1_1/case_1_1_F4 = 104", "P_F1", expectValues2);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(
        stmt, "NP_container", "main_label", "main_label.case_1_1/case_1_1_F4 = 104", "NP_F1", expectValues3);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(
        stmt, "choice_1/case_1_1", "main_label", "main_label.case_1_1/case_1_1_F4 = 104", "case_1_1_F1", expectValues4);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(stmt, "main_label", "main_label.case_1_1/case_1_1_F4 = 104", "root_F33", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByFilter(stmt, "main_label", "main_label.case_1_1/case_1_1_F4 = 105", "root_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    uint32_t expectValues5[2] = {108, 109};
    ret = ScanByFilter(stmt, "list_label", "root_F4 > 107 and root_F4 <= 111", "root_F1", expectValues5);
    AW_MACRO_EXPECT_EQ_INT(2, ret);
    uint32_t expectValues6[2] = {113, 114};
    ret = ScanByFilter(stmt, "P_container", "list_label", "root_F4 > 107 and root_F4 <= 111", "P_F1", expectValues6);
    AW_MACRO_EXPECT_EQ_INT(2, ret);
    uint32_t expectValues7[2] = {114, 115};
    ret = ScanByFilter(stmt, "NP_container", "list_label", "root_F4 > 107 and root_F4 <= 111", "NP_F1", expectValues7);
    AW_MACRO_EXPECT_EQ_INT(2, ret);
    uint32_t expectValues8[2] = {115, 116};
    ret = ScanByFilter(
        stmt, "choice_1/case_1_1", "list_label", "root_F4 > 107 and root_F4 <= 111", "case_1_1_F1", expectValues8);
    AW_MACRO_EXPECT_EQ_INT(2, ret);
    ret = ScanByFilter(stmt, "list_label", "root_F4 > 107 and root_F4 <= 111", "root_F35", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(2, ret);
    ret = ScanByFilter(stmt, "list_label", "root_F4 > 8999 and root_F4 <= 9999", "root_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = ScanByFilter(stmt, "list_label", "list_label.P_container/P_F4 > 113 and list_label.P_container/P_F4 <= 116",
        "root_F1", expectValues5);
    AW_MACRO_EXPECT_EQ_INT(2, ret);
    ret = ScanByFilter(stmt, "P_container", "list_label",
        "list_label.P_container/P_F4 > 113 and list_label.P_container/P_F4 <= 116", "P_F1", expectValues6);
    AW_MACRO_EXPECT_EQ_INT(2, ret);
    ret = ScanByFilter(stmt, "NP_container", "list_label",
        "list_label.P_container/P_F4 > 113 and list_label.P_container/P_F4 <= 116", "NP_F1", expectValues7);
    AW_MACRO_EXPECT_EQ_INT(2, ret);
    ret = ScanByFilter(stmt, "choice_1/case_1_1", "list_label",
        "list_label.P_container/P_F4 > 113 and list_label.P_container/P_F4 <= 116", "case_1_1_F1", expectValues8);
    AW_MACRO_EXPECT_EQ_INT(2, ret);
    ret = ScanByFilter(stmt, "list_label", "list_label.P_container/P_F4 > 113 and list_label.P_container/P_F4 <= 116",
        "root_F35", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(2, ret);
    ret = ScanByFilter(stmt, "list_label",
        "list_label.P_container/P_F4 > 8999 "
        "and list_label.P_container/P_F4 <= 9999",
        "root_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    uint32_t expectValues9[4] = {101, 102, 103, 104};
    ret = ScanByFilter(stmt, "list_label",
        "list_label.NP_container/NP_F4 > 107 "
        "and list_label.NP_container/NP_F4 <= 111",
        "root_F1", expectValues9);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    uint32_t expectValues10[4] = {106, 107, 108, 109};
    ret = ScanByFilter(stmt, "P_container", "list_label",
        "list_label.NP_container/NP_F4 > 107 and list_label.NP_container/NP_F4 <= 111", "P_F1", expectValues10);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    uint32_t expectValues11[4] = {107, 108, 109, 110};
    ret = ScanByFilter(stmt, "NP_container", "list_label",
        "list_label.NP_container/NP_F4 > 107 and list_label.NP_container/NP_F4 <= 111", "NP_F1", expectValues11);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    uint32_t expectValues12[4] = {108, 109, 110, 111};
    ret = ScanByFilter(stmt, "choice_1/case_1_1", "list_label",
        "list_label.NP_container/NP_F4 > 107 and list_label.NP_container/NP_F4 <= 111", "case_1_1_F1", expectValues12);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByFilter(stmt, "list_label",
        "list_label.NP_container/NP_F4 > 107 "
        "and list_label.NP_container/NP_F4 <= 111",
        "root_F35", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByFilter(stmt, "list_label",
        "list_label.NP_container/NP_F4 > 8999 "
        "and list_label.NP_container/NP_F4 <= 9999",
        "root_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    uint32_t expectValues13[4] = {100, 101, 102, 103};
    ret = ScanByFilter(stmt, "list_label",
        "list_label.choice_1/case_1_1/case_1_1_F4 > 107 "
        "and list_label.choice_1/case_1_1/case_1_1_F4 <= 111",
        "root_F1", expectValues13);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    uint32_t expectValues14[4] = {105, 106, 107, 108};
    ret = ScanByFilter(stmt, "P_container", "list_label",
        "list_label.choice_1/case_1_1/case_1_1_F4 > 107 "
        "and list_label.choice_1/case_1_1/case_1_1_F4 <= 111",
        "P_F1", expectValues14);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    uint32_t expectValues15[4] = {106, 107, 108, 109};
    ret = ScanByFilter(stmt, "NP_container", "list_label",
        "list_label.choice_1/case_1_1/case_1_1_F4 > 107 "
        "and list_label.choice_1/case_1_1/case_1_1_F4 <= 111",
        "NP_F1", expectValues15);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    uint32_t expectValues16[4] = {107, 108, 109, 110};
    ret = ScanByFilter(stmt, "choice_1/case_1_1", "list_label",
        "list_label.choice_1/case_1_1/case_1_1_F4 > 107 "
        "and list_label.choice_1/case_1_1/case_1_1_F4 <= 111",
        "case_1_1_F1", expectValues16);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByFilter(stmt, "list_label",
        "list_label.choice_1/case_1_1/case_1_1_F4 > 107 "
        "and list_label.choice_1/case_1_1/case_1_1_F4 <= 111",
        "root_F35", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(4, ret);
    ret = ScanByFilter(stmt, "list_label",
        "list_label.choice_1/case_1_1/case_1_1_F4 > 8999 "
        "and list_label.choice_1/case_1_1/case_1_1_F4 <= 9999",
        "root_F1", expectNoValue, true);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_INVALID_NAME);
}

// 036.yang支持的各类索引（primary key、local key、localhash key），
// 用这个primary key set key value、set key name update、merge、delete，直接报错
TEST_F(yang_vertex_scan_normal, Yang_085_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int indexCnt = 0;
    AsyncUserDataT data = {0};
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/all_node.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_2.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck();

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写数据
    SetAllNodeProperty();
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/3_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // GMC_OPERATION_MERGE, 主键是允许merge的
    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keys[8] = {0};
    keys[0] = 1;

    ret = GmcPrepareStmtByLabelName(stmt, "main_label", GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = GmcPrepareStmtByLabelName(stmt, "main_label", GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    ret = singeMergeByKeyValueFail(stmt, "main_label", "pk", 1, keys, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);

    ret = batchMergeByKeyValueOk(conn, stmt, "main_label", "pk", 1, keys, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransRollBack(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchMergeByKeyValueOk(conn, stmt, "main_label", "pk", 1, keys, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // yang操作必须开启事务否则报错
    ret = batchMergeByKeyValueOk(conn, stmt, "main_label", "pk", 1, keys, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANS_MODE_MISMATCH, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    AddWhiteList(GMERR_TRANS_MODE_MISMATCH);
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
}

// 037.yang支持的各类索引（primary key、local key、localhash key），
// 用这个local key set key value、set key name update、merge、delete，直接报错（1个大用例或3个小用例）
TEST_F(yang_vertex_scan_normal, Yang_085_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int indexCnt = 0;
    AsyncUserDataT data = {0};
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/all_node.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_2.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck();

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写数据
    SetAllNodeProperty();
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/3_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // GMC_OPERATION_MERGE
    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keys[8] = {0};
    keys[0] = 1;

    ret = GmcPrepareStmtByLabelName(stmt, "main_label", GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = GmcPrepareStmtByLabelName(stmt, "main_label", GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = singeMergeByKeyValueFail(stmt, "main_label", "lk", 1, keys, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = batchRootMergeByKeyValFail(conn, stmt, "main_label", "lk", 1, keys, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    ret = GmcTransRollBack(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // GMC_OPERATION_DELETE_GRAPH
    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ScanByKeyValueFail(stmt, "main_label", "lk", 1, keys, GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = GmcTransRollBack(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // GMC_OPERATION_REMOVE_GRAPH
    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ScanByKeyValueFail(stmt, "main_label", "lk", 1, keys, GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = GmcTransRollBack(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // GMC_OPERATION_NONE
    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ScanByKeyValueFail(stmt, "main_label", "lk", 1, keys, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = GmcTransRollBack(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // GMC_OPERATION_MERGE
    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    keys[0] = 104;

    ret = GmcPrepareStmtByLabelName(stmt, "list_label", GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = GmcPrepareStmtByLabelName(stmt, "list_label", GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = singeMergeByKeyValueFail(stmt, "list_label", "lk", 1, keys, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = batchMergeByKeyValueFail(conn, stmt, "list_label", "lk", 1, keys, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    ret = GmcTransRollBack(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // GMC_OPERATION_DELETE_GRAPH
    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ScanByKeyValueFail(stmt, "list_label", "lk", 1, keys, GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = GmcTransRollBack(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // GMC_OPERATION_REMOVE_GRAPH
    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ScanByKeyValueFail(stmt, "list_label", "lk", 1, keys, GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = GmcTransRollBack(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // GMC_OPERATION_NONE
    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ScanByKeyValueFail(stmt, "list_label", "lk", 1, keys, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = GmcTransRollBack(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // GMC_OPERATION_MERGE
    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    keys[0] = 108;

    ret = GmcPrepareStmtByLabelName(stmt, "leaf-list_label_1", GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = GmcPrepareStmtByLabelName(stmt, "leaf-list_label_1", GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = singeMergeByKeyValueFail(stmt, "leaf-list_label_1", "lk", 1, keys, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = batchMergeByKeyValueFail(conn, stmt, "leaf-list_label_1", "lk", 1, keys, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    ret = GmcTransRollBack(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // GMC_OPERATION_DELETE_GRAPH
    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ScanByKeyValueFail(stmt, "leaf-list_label_1", "lk", 1, keys, GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = GmcTransRollBack(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // GMC_OPERATION_REMOVE_GRAPH
    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ScanByKeyValueFail(stmt, "leaf-list_label_1", "lk", 1, keys, GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = GmcTransRollBack(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // GMC_OPERATION_NONE
    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ScanByKeyValueFail(stmt, "leaf-list_label_1", "lk", 1, keys, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = GmcTransRollBack(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    AddWhiteList(GMERR_DATA_EXCEPTION);
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
}

// 038.yang支持的各类索引（primary key、local key、localhash key），
// 用这个localhash key set key value、set key name update、merge、delete，直接报错（1个大用例或3个小用例）
TEST_F(yang_vertex_scan_normal, Yang_085_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int indexCnt = 0;
    AsyncUserDataT data = {0};
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/all_node.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_2.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck();

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写数据
    SetAllNodeProperty();
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/3_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // GMC_OPERATION_MERGE
    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keys[8] = {0};
    keys[0] = 1;

    ret = GmcPrepareStmtByLabelName(stmt, "main_label", GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = GmcPrepareStmtByLabelName(stmt, "main_label", GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = singeMergeByKeyValueFail(stmt, "main_label", "lhk", 1, keys, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = batchRootMergeByKeyValFail(conn, stmt, "main_label", "lhk", 1, keys, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    ret = GmcTransRollBack(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // GMC_OPERATION_DELETE_GRAPH
    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ScanByKeyValueFail(stmt, "main_label", "lhk", 1, keys, GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = GmcTransRollBack(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // GMC_OPERATION_REMOVE_GRAPH
    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ScanByKeyValueFail(stmt, "main_label", "lhk", 1, keys, GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = GmcTransRollBack(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // GMC_OPERATION_NONE
    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ScanByKeyValueFail(stmt, "main_label", "lhk", 1, keys, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = GmcTransRollBack(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // GMC_OPERATION_MERGE
    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    keys[0] = 104;

    ret = GmcPrepareStmtByLabelName(stmt, "list_label", GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = GmcPrepareStmtByLabelName(stmt, "list_label", GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = singeMergeByKeyValueFail(stmt, "list_label", "lhk", 1, keys, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = batchMergeByKeyValueFail(conn, stmt, "list_label", "lhk", 1, keys, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    ret = GmcTransRollBack(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // GMC_OPERATION_DELETE_GRAPH
    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ScanByKeyValueFail(stmt, "list_label", "lhk", 1, keys, GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = GmcTransRollBack(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // GMC_OPERATION_REMOVE_GRAPH
    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ScanByKeyValueFail(stmt, "list_label", "lhk", 1, keys, GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = GmcTransRollBack(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // GMC_OPERATION_NONE
    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ScanByKeyValueFail(stmt, "list_label", "lhk", 1, keys, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = GmcTransRollBack(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // GMC_OPERATION_MERGE
    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    keys[0] = 108;

    ret = GmcPrepareStmtByLabelName(stmt, "leaf-list_label_1", GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = GmcPrepareStmtByLabelName(stmt, "leaf-list_label_1", GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = singeMergeByKeyValueFail(stmt, "leaf-list_label_1", "lhk", 1, keys, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = batchMergeByKeyValueFail(conn, stmt, "leaf-list_label_1", "lhk", 1, keys, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    ret = GmcTransRollBack(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // GMC_OPERATION_DELETE_GRAPH
    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ScanByKeyValueFail(stmt, "leaf-list_label_1", "lhk", 1, keys, GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = GmcTransRollBack(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // GMC_OPERATION_REMOVE_GRAPH
    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ScanByKeyValueFail(stmt, "leaf-list_label_1", "lhk", 1, keys, GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = GmcTransRollBack(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // GMC_OPERATION_NONE
    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ScanByKeyValueFail(stmt, "leaf-list_label_1", "lhk", 1, keys, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = GmcTransRollBack(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    AddWhiteList(GMERR_DATA_EXCEPTION);
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    AddWhiteList(GMERR_MEMORY_OPERATE_FAILED);
}

// 039.yang支持的各类索引（primary key、local key、localhash key），
// 用这个local key set key range、set key name update、merge、delete，直接报错（1个大用例或3个小用例）
TEST_F(yang_vertex_scan_normal, Yang_085_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AsyncUserDataT data = {0};
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/all_node.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_2.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck();

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写数据
    SetAllNodeProperty();
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/3_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    unsigned int arrLen = 1;
    uint32_t leftValue = 106;
    uint32_t rightValue = 110;
    GmcPropValueT leftVals[arrLen];
    GmcPropValueT rightVals[arrLen];
    leftVals[0].size = sizeof(uint32_t);
    leftVals[0].type = GMC_DATATYPE_UINT32;
    leftVals[0].value = &leftValue;
    rightVals[0].size = sizeof(uint32_t);
    rightVals[0].type = GMC_DATATYPE_UINT32;
    rightVals[0].value = &rightValue;

    GmcRangeItemT items1[arrLen];
    items1[0].lValue = NULL;
    items1[0].rValue = NULL;
    items1[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items1[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items1[0].order = GMC_ORDER_ASC;

    GmcRangeItemT items2[arrLen];
    items2[0].lValue = &leftVals[0];
    items2[0].rValue = &rightVals[0];
    items2[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items2[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items2[0].order = GMC_ORDER_ASC;

    GmcRangeItemT items3[arrLen];
    items3[0].lValue = &leftVals[0];
    items3[0].rValue = &rightVals[0];
    items3[0].lFlag = GMC_COMPARE_RANGE_OPEN;
    items3[0].rFlag = GMC_COMPARE_RANGE_OPEN;
    items3[0].order = GMC_ORDER_ASC;

    GmcRangeItemT items4[arrLen];
    items4[0].lValue = &leftVals[0];
    items4[0].rValue = &rightVals[0];
    items4[0].lFlag = GMC_COMPARE_RANGE_OPEN;
    items4[0].rFlag = GMC_COMPARE_RANGE_OPEN;
    items4[0].order = GMC_ORDER_DESC;

    uint32_t unexistLeftValue = 8998;
    uint32_t unexistRightValue = 9999;
    GmcPropValueT unexistLeftVals[arrLen];
    GmcPropValueT unexistRightVals[arrLen];
    unexistLeftVals[0].size = sizeof(uint32_t);
    unexistLeftVals[0].type = GMC_DATATYPE_UINT32;
    unexistLeftVals[0].value = &unexistLeftValue;
    unexistRightVals[0].size = sizeof(uint32_t);
    unexistRightVals[0].type = GMC_DATATYPE_UINT32;
    unexistRightVals[0].value = &unexistRightValue;
    GmcRangeItemT unexistItems[arrLen];
    unexistItems[0].lValue = &unexistLeftVals[0];
    unexistItems[0].rValue = &unexistRightVals[0];
    unexistItems[0].lFlag = GMC_COMPARE_RANGE_OPEN;
    unexistItems[0].rFlag = GMC_COMPARE_RANGE_OPEN;
    unexistItems[0].order = GMC_ORDER_ASC;

    uint32_t expectConRootF2Values[1] = {101};
    ret = GmcPrepareStmtByLabelName(stmt, "main_label", GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = GmcPrepareStmtByLabelName(stmt, "main_label", GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    ret = ScanByRangeFail(stmt, "main_label", items1, arrLen, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
}

// 040.yang支持的各类索引（primary key、local key、localhash key），
// 用SetFilter update、merge、delete，直接报错（1个大用例或3个小用例）
TEST_F(yang_vertex_scan_normal, Yang_085_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AsyncUserDataT data = {0};
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/all_node.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_2.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck();

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写数据
    SetAllNodeProperty();
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/3_reply.json");

    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, "main_label", GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = GmcPrepareStmtByLabelName(stmt, "main_label", GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    uint32_t expectValues1[1] = {100};
    ret = ScanByFilterFail(stmt, "main_label", "root_F4 = 102", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
}

// 041.相关接口测试
TEST_F(yang_vertex_scan_normal, Yang_085_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int indexCnt = 0;
    AsyncUserDataT data = {0};
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/all_node.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_2.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck();

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // yangDML写数据
    SetAllNodeProperty();
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/3_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcResetVertex(stmt, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_WRONG_STMT_OBJECT, ret);

    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keys[8] = {0};
    uint32_t unexistKeys[8] = {9999, 9999, 9999, 9999, 9999, 9999, 9999, 9999};
    keys[0] = 1;
    ret = ScanByKeyValueID(stmt, "main_label", "pk", 1, keys, 1, 100);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ScanByKeyValueID(stmt, "P_container", "main_label", "pk", 1, keys, 0, 101);
    AW_MACRO_EXPECT_EQ_INT(1, ret);

    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // GmcGetPropTypeByKeyNamePropOrder
    int i;
    char fieldName[128];
    uint32_t propType;
    const char *keyName = "pk";
    uint32_t labelId = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, "main_label", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcKeyNameAndOrderT keyNameAndOrder = {keyName, labelId};
    ret = GmcGetPropTypeByKeyNamePropOrder(stmt, &keyNameAndOrder, fieldName, 128, &propType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_UINT32, propType);

    // GmcGetPropTypeByNodeNamePropId
    ret = testGmcPrepareStmtByLabelName(stmt, "main_label", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeNameAndPropIdT NodeNameAndPropId = {"P_container", 0};
    ret = GmcGetPropTypeByNodeNamePropId(stmt, &NodeNameAndPropId, fieldName, 128, &propType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_UINT32, propType);

    // GmcGetPropTypeByNodeNamePropName
    ret = testGmcPrepareStmtByLabelName(stmt, "main_label", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t fieldId;
    ret = GmcGetPropTypeByNodeNamePropName(stmt, "P_container", "P_F1", &fieldId, &propType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_UINT32, propType);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_WRONG_STMT_OBJECT);
}

// 042.建各类型的各数据类型的yang模型，用DML操作获取到每个节点的node，传入GmcYangGetPropTypeByName获取每个字段的ID和type
TEST_F(yang_vertex_scan_normal, Yang_085_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/all_type.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck();

    uint32_t getID = 0;
    uint32_t getType = 0;
    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 取rootNode
    ret = testGmcPrepareStmtByLabelName(g_stmtConRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtConRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtConRoot, &g_nodeConRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 当前迭代不校验ID
    ret = GmcYangGetPropTypeByName(g_nodeConRoot, "ID", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_UINT32, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConRoot, "root_F0", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_UINT32, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConRoot, "root_F1", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_CHAR, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConRoot, "root_F2", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_UCHAR, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConRoot, "root_F3", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_INT8, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConRoot, "root_F4", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_UINT8, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConRoot, "root_F5", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_INT16, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConRoot, "root_F6", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_UINT16, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConRoot, "root_F7", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_INT32, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConRoot, "root_F8", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_INT64, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConRoot, "root_F9", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_UINT64, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConRoot, "root_F10", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_BOOL, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConRoot, "root_F11", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_FLOAT, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConRoot, "root_F12", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_DOUBLE, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConRoot, "root_F13", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_TIME, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConRoot, "root_F14", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_STRING, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConRoot, "root_F15", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_BYTES, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConRoot, "root_F16", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_FIXED, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConRoot, "root_F17", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_BITMAP, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConRoot, "root_I1", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_IDENTITY, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConRoot, "root_E1", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_ENUM, getType);
    // 取PNode
    ret = GmcYangEditChildNode(g_nodeConRoot, "P_container", GMC_OPERATION_INSERT, &g_nodeConP);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangGetPropTypeByName(g_nodeConP, "P_F0", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_UINT32, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConP, "P_F1", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_CHAR, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConP, "P_F2", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_UCHAR, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConP, "P_F3", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_INT8, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConP, "P_F4", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_UINT8, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConP, "P_F5", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_INT16, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConP, "P_F6", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_UINT16, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConP, "P_F7", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_INT32, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConP, "P_F8", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_INT64, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConP, "P_F9", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_UINT64, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConP, "P_F10", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_BOOL, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConP, "P_F11", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_FLOAT, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConP, "P_F12", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_DOUBLE, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConP, "P_F13", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_TIME, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConP, "P_F14", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_STRING, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConP, "P_F15", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_BYTES, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConP, "P_F16", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_FIXED, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConP, "P_F17", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_BITMAP, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConP, "P_I1", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_IDENTITY, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConP, "P_E1", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_ENUM, getType);
    // 取NPNode
    ret = GmcYangEditChildNode(g_nodeConRoot, "NP_container", GMC_OPERATION_INSERT, &g_nodeConNP);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangGetPropTypeByName(g_nodeConNP, "NP_F0", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_UINT32, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConNP, "NP_F1", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_CHAR, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConNP, "NP_F2", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_UCHAR, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConNP, "NP_F3", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_INT8, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConNP, "NP_F4", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_UINT8, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConNP, "NP_F5", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_INT16, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConNP, "NP_F6", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_UINT16, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConNP, "NP_F7", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_INT32, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConNP, "NP_F8", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_INT64, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConNP, "NP_F9", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_UINT64, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConNP, "NP_F10", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_BOOL, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConNP, "NP_F11", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_FLOAT, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConNP, "NP_F12", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_DOUBLE, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConNP, "NP_F13", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_TIME, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConNP, "NP_F14", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_STRING, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConNP, "NP_F15", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_BYTES, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConNP, "NP_F16", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_FIXED, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConNP, "NP_F17", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_BITMAP, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConNP, "NP_I1", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_IDENTITY, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConNP, "NP_E1", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_ENUM, getType);
    // 取choiceNode
    ret = GmcYangEditChildNode(g_nodeConRoot, "choice_1", GMC_OPERATION_INSERT, &g_nodeConChoice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 取case1Node
    ret = GmcYangEditChildNode(g_nodeConChoice, "case_1_1", GMC_OPERATION_INSERT, &g_nodeConCase1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 取case2Node
    ret = GmcYangEditChildNode(g_nodeConChoice, "case_1_2", GMC_OPERATION_INSERT, &g_nodeConCase2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 取过case2再用case1getType也是可以的
    ret = GmcYangGetPropTypeByName(g_nodeConCase1, "case_1_1_F0", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_UINT32, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConCase1, "case_1_1_F1", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_CHAR, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConCase1, "case_1_1_F2", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_UCHAR, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConCase1, "case_1_1_F3", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_INT8, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConCase1, "case_1_1_F4", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_UINT8, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConCase1, "case_1_1_F5", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_INT16, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConCase1, "case_1_1_F6", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_UINT16, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConCase1, "case_1_1_F7", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_INT32, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConCase1, "case_1_1_F8", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_INT64, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConCase1, "case_1_1_F9", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_UINT64, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConCase1, "case_1_1_F10", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_BOOL, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConCase1, "case_1_1_F11", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_FLOAT, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConCase1, "case_1_1_F12", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_DOUBLE, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConCase1, "case_1_1_F13", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_TIME, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConCase1, "case_1_1_F14", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_STRING, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConCase1, "case_1_1_F15", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_BYTES, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConCase1, "case_1_1_F16", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_FIXED, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConCase1, "case_1_1_F17", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_BITMAP, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConCase1, "case_1_1_I1", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_IDENTITY, getType);
    ret = GmcYangGetPropTypeByName(g_nodeConCase1, "case_1_1_E1", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_ENUM, getType);
    // 添加批操作
    ret = GmcBatchAddDML(batch, g_stmtConRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取listNode
    ret = testGmcPrepareStmtByLabelName(g_stmtListRoot, "list_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmtConRoot, g_stmtListRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtListRoot, &g_nodeListRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangGetPropTypeByName(g_nodeListRoot, "ID", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_UINT32, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListRoot, "root_F0", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_UINT32, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListRoot, "root_F1", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_CHAR, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListRoot, "root_F2", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_UCHAR, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListRoot, "root_F3", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_INT8, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListRoot, "root_F4", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_UINT8, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListRoot, "root_F5", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_INT16, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListRoot, "root_F6", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_UINT16, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListRoot, "root_F7", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_INT32, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListRoot, "root_F8", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_INT64, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListRoot, "root_F9", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_UINT64, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListRoot, "root_F10", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_BOOL, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListRoot, "root_F11", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_FLOAT, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListRoot, "root_F12", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_DOUBLE, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListRoot, "root_F13", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_TIME, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListRoot, "root_F14", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_STRING, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListRoot, "root_F15", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_BYTES, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListRoot, "root_F16", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_FIXED, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListRoot, "root_F17", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_BITMAP, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListRoot, "root_I1", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_IDENTITY, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListRoot, "root_E1", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_ENUM, getType);
    // 主键赋值
    uint32_t valueConCase1N2 = 105;
    ret = SetNodeProperty(g_nodeListRoot, GMC_DATATYPE_UINT32, &valueConCase1N2, sizeof(uint32_t), "root_F0",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 取PNode
    ret = GmcYangEditChildNode(g_nodeListRoot, "P_container", GMC_OPERATION_INSERT, &g_nodeListP);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangGetPropTypeByName(g_nodeListP, "P_F0", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_UINT32, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListP, "P_F1", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_CHAR, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListP, "P_F2", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_UCHAR, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListP, "P_F3", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_INT8, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListP, "P_F4", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_UINT8, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListP, "P_F5", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_INT16, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListP, "P_F6", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_UINT16, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListP, "P_F7", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_INT32, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListP, "P_F8", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_INT64, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListP, "P_F9", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_UINT64, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListP, "P_F10", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_BOOL, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListP, "P_F11", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_FLOAT, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListP, "P_F12", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_DOUBLE, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListP, "P_F13", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_TIME, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListP, "P_F14", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_STRING, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListP, "P_F15", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_BYTES, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListP, "P_F16", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_FIXED, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListP, "P_F17", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_BITMAP, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListP, "P_I1", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_IDENTITY, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListP, "P_E1", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_ENUM, getType);
    // 取NPNode
    ret = GmcYangEditChildNode(g_nodeListRoot, "NP_container", GMC_OPERATION_INSERT, &g_nodeListNP);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangGetPropTypeByName(g_nodeListNP, "NP_F0", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_UINT32, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListNP, "NP_F1", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_CHAR, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListNP, "NP_F2", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_UCHAR, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListNP, "NP_F3", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_INT8, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListNP, "NP_F4", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_UINT8, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListNP, "NP_F5", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_INT16, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListNP, "NP_F6", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_UINT16, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListNP, "NP_F7", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_INT32, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListNP, "NP_F8", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_INT64, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListNP, "NP_F9", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_UINT64, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListNP, "NP_F10", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_BOOL, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListNP, "NP_F11", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_FLOAT, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListNP, "NP_F12", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_DOUBLE, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListNP, "NP_F13", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_TIME, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListNP, "NP_F14", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_STRING, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListNP, "NP_F15", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_BYTES, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListNP, "NP_F16", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_FIXED, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListNP, "NP_F17", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_BITMAP, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListNP, "NP_I1", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_IDENTITY, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListNP, "NP_E1", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_ENUM, getType);
    // 取choiceNode
    ret = GmcYangEditChildNode(g_nodeListRoot, "choice_1", GMC_OPERATION_INSERT, &g_nodeListChoice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 取case1Node
    ret = GmcYangEditChildNode(g_nodeListChoice, "case_1_1", GMC_OPERATION_INSERT, &g_nodeListCase1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangGetPropTypeByName(g_nodeListCase1, "case_1_1_F0", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_UINT32, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListCase1, "case_1_1_F1", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_CHAR, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListCase1, "case_1_1_F2", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_UCHAR, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListCase1, "case_1_1_F3", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_INT8, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListCase1, "case_1_1_F4", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_UINT8, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListCase1, "case_1_1_F5", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_INT16, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListCase1, "case_1_1_F6", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_UINT16, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListCase1, "case_1_1_F7", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_INT32, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListCase1, "case_1_1_F8", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_INT64, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListCase1, "case_1_1_F9", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_UINT64, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListCase1, "case_1_1_F10", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_BOOL, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListCase1, "case_1_1_F11", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_FLOAT, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListCase1, "case_1_1_F12", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_DOUBLE, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListCase1, "case_1_1_F13", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_TIME, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListCase1, "case_1_1_F14", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_STRING, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListCase1, "case_1_1_F15", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_BYTES, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListCase1, "case_1_1_F16", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_FIXED, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListCase1, "case_1_1_F17", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_BITMAP, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListCase1, "case_1_1_I1", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_IDENTITY, getType);
    ret = GmcYangGetPropTypeByName(g_nodeListCase1, "case_1_1_E1", &getID, &getType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_DATATYPE_ENUM, getType);
    // 取case2Node
    ret = GmcYangEditChildNode(g_nodeListChoice, "case_1_2", GMC_OPERATION_INSERT, &g_nodeListCase2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加批操作
    ret = GmcBatchAddDML(batch, g_stmtListRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t expectTypeTurn[LEAF_LIST_TYPE_CNT] = {GMC_DATATYPE_UINT32, GMC_DATATYPE_CHAR, GMC_DATATYPE_UCHAR,
        GMC_DATATYPE_INT8, GMC_DATATYPE_UINT8, GMC_DATATYPE_INT16, GMC_DATATYPE_UINT16, GMC_DATATYPE_INT32,
        GMC_DATATYPE_INT64, GMC_DATATYPE_UINT64, GMC_DATATYPE_BOOL, GMC_DATATYPE_TIME, GMC_DATATYPE_STRING,
        GMC_DATATYPE_BYTES, GMC_DATATYPE_FIXED, GMC_DATATYPE_IDENTITY, GMC_DATATYPE_ENUM};
    // 取leaflist[]Node
    for (int i = 0; i < LEAF_LIST_TYPE_CNT; i++) {
        string labelNameStr = "leaf-list_label_" + to_string(i + 1);
        const char *labelName = labelNameStr.c_str();
        ret = testGmcPrepareStmtByLabelName(g_stmtLeafListRoot[i], labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtConRoot, g_stmtLeafListRoot[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtLeafListRoot[i], &g_nodeLeafListRoot[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangGetPropTypeByName(g_nodeLeafListRoot[i], "root_F0", &getID, &getType);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectTypeTurn[i], getType);
        // 设置leaflist主键
        SetLeafListKeyValue(g_nodeLeafListRoot[i], i, i);
        // 添加批操作
        ret = GmcBatchAddDML(batch, g_stmtLeafListRoot[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2 + LEAF_LIST_TYPE_CNT, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2 + LEAF_LIST_TYPE_CNT, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 046.DTS2024051614442
TEST_F(yang_vertex_scan_normal, Yang_085_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AsyncUserDataT data = {0};
    const char *labelConfig = R"(
{
    "max_record_count":1,
    "isFastReadUncommitted":0,
    "auto_increment":1,
    "yang_model":1
})";
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/all_node.gmjson", labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgelabel_2.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 模型校验
    ModelCheck(g_stmtAsync);

    // 启动事务1
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步建连
    GmcConnT *connAsync = NULL;
    GmcStmtT *stmtAsync = NULL;
    ret = testGmcConnect(&connAsync, &stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespaceAsync(stmtAsync, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 启动事务2
    ret = TestTransStartAsync(connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtConRoot, "main_label", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtConRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtConRoot, &g_nodeConRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置root_F2的值
    uint32_t valueRootF2 = 105;
    ret = SetNodeProperty(g_nodeConRoot, GMC_DATATYPE_UINT32, &valueRootF2, sizeof(uint32_t),
                            "root_F2", GMC_YANG_PROPERTY_OPERATION_MERGE);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtConRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);

    // 设置批处理batch参数
    GmcBatchT *batch2 = NULL;
    ret = TestBatchPrepare(connAsync, &batch2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    GmcResetStmt(g_stmtConRoot);
    ret = testGmcPrepareStmtByLabelName(g_stmtConRoot, "main_label", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch2, g_stmtConRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtConRoot, &g_nodeConRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置root_F2的值
    ret = SetNodeProperty(g_nodeConRoot, GMC_DATATYPE_UINT32, &valueRootF2, sizeof(uint32_t),
                            "root_F2", GMC_YANG_PROPERTY_OPERATION_MERGE);
    // 添加DML操作
    ret = GmcBatchAddDML(batch2, g_stmtConRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 批处理提交
    ret = GmcBatchExecuteAsync(batch2, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);

    GmcBatchDestroy(batch);
    GmcBatchDestroy(batch2);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务1
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 回滚事务2
    ret = TestTransRollBackAsync(connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(connAsync, stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
