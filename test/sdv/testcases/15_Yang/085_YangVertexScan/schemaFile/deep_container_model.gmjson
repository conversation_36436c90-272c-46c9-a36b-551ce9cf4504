[    
    {
        "type":"container",
        "name":"Deep_1",
        "fields":[
            {"name":"ID", "type":"uint32", "nullable":false},
            {"name":"LKF", "type":"uint32", "nullable":true, "default":1},
            {"name":"LHK<PERSON>", "type":"uint32", "nullable":true, "default":2},
            {"name":"F_1", "type":"uint32", "nullable":true, "default":1},
            #(0)#
        ],
        "keys":[
            {
                "node":"Deep_1",
                "name":"pk",
                "fields":["ID"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            },
            {
                "node":"Deep_1",
                "name":"lk",
                "fields":["LKF"],
                "index":{"type":"local"},
                "constraints":{"unique":true}
            },
            {
                "node":"Deep_1",
                "name":"lhk",
                "fields":["LHKF"],
                "index":{"type":"localhash"},
                "constraints":{"unique":true}
            }
        ]
    }
]
