/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#include "RCA_COMMON.h"
#include "../../../reliability/try.h"

class mandtyContain : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void mandtyContain::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/start.sh");
    int ret = testEnvInit();
    ASSERT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void mandtyContain::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}

void mandtyContain::SetUp()
{
    int ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务

    const char *namespace1 = "mandtyContain";
    const char *namespaceUserName = "abc";

    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观
    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcClearNamespaceAsync(g_stmt_async, namespace1, ClearNSCallbak, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // alloc all stmt
    TestYangAllocAllstmt();
}

void mandtyContain::TearDown()
{
    int ret = 0;
    const char *namespace1 = "mandtyContain";
    ret = GmcClearNamespaceAsync(g_stmt_async, namespace1, ClearNSCallbak, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcDropNamespaceAsync(g_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 释放all stmt
    TestYangFreeAllstmt();

    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


void TestCheckValidateModelAsync(GmcStmtT *stmt)
{
    // 模型校验
    YangValidateUserDataT checkData = {0};
    int ret = GmcYangValidateModelAsync(stmt, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(true, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(0, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));
}

// *typedef void (*GmcYangValidateDoneT)(void *userData, GmcValidateResT validateRes, int32_t status, const char *errMsg);*/
void AsyncValidateLeafRefCb(void *userData, GmcValidateResT validateRes, int32_t status, const char *errMsg)
{
    if (userData) {
        YangValidateUserDataT *uData = (YangValidateUserDataT *)userData;
        uData->status = status;
        if ((status != GMERR_OK) && (errMsg != NULL)) {
            printf("YangValidate errMsg: %s\n", errMsg);
        }
        uData->validateRes = validateRes.validateRes;
        uData->failCount = validateRes.failCount;

        printf(">>> validateRes: %d\n", validateRes.validateRes);
        printf(">>> failCount: %u\n", validateRes.failCount);

        if (uData->isValidErrorPathInfo) {
            GmcErrorPathInfoT msg;
            ASSERT_EQ(GMERR_OK, GmcYangGetErrorPathInfo(&msg));

            // 结果检查
            printf("--- errcode: %d\n", msg.errorCode);
            printf("--- errorClauseIndex: %u\n", msg.errorClauseIndex);
            printf("--- errorMsg: %s\n", msg.errorMsg);
            printf("--- errorPath: %s\n", msg.errorPath);
            EXPECT_EQ(uData->expectedErrCode, msg.errorCode);
            EXPECT_EQ(uData->expectedErrClauseIndex, msg.errorClauseIndex);
            EXPECT_STREQ(uData->expectedErrMsg, msg.errorMsg);
            EXPECT_STREQ(uData->expectedErrPath, msg.errorPath);
            ASSERT_NO_FATAL_FAILURE(GmcYangFreeErrorPathInfo());
        }

        uData->recvNum++;
    }
}
// yang set T0层f0
void testYangSetVertexProperty_Fx(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE optype, const char *name)
{
    int ret = 0;
    uint32_t f0Value = i;
    ret = testYangSetField(node, GMC_DATATYPE_UINT32, &f0Value, sizeof(uint32_t), name, optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 * Description  : 001.conatain 节点，部分leaf定义mandatory
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(mandtyContain, Yang_072_mandtyContain_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F13");

    // 对 container NP 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ContainerTwo 
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作  -- ContainerTwo 
    int32_t f1fieldValue = 100;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作  -- ContainerTwo 
    bool f4 = true;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container P 子节点做replace操作  -- ContainerThree 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ContainerThree 
    testYangSetVertexProperty_F0(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 -- ContainerThree 
    ret = testYangSetField(g_containerT3Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作 -- ContainerThree 
    ret = testYangSetField(g_containerT3Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // all校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/F2";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = failRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);
    system("gmsysview subtree -ns mandtyContain -rn ContainerOne -defaultMode REPORT_ALL");

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    bool boolValue = true;
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_BOOL, &boolValue, sizeof(boolValue), "F2",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // all校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/F2";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 002.conatain节点嵌套多层NP contain，最后一层节点有mandatory leaf
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(mandtyContain, Yang_072_mandtyContain_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel2.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F13");

    // 对 container NP 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ContainerTwo 
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作  -- ContainerTwo 
    int32_t f1fieldValue = 100;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作  -- ContainerTwo 
    bool f4 = true;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container P 子节点做replace操作  -- ContainerThree 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ContainerThree 
    testYangSetVertexProperty_F0(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 -- ContainerThree 
    ret = testYangSetField(g_containerT3Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作 -- ContainerThree 
    ret = testYangSetField(g_containerT3Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // all校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/ContainerTwo_2/ContainerTwo_3/ContainerTwo_4/ContainerTwo_5/F0";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = failRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // 对 container NP 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_NONE, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *g_containerT2Node_2 = NULL;
    ret = GmcYangEditChildNode(g_containerT2Node, "ContainerTwo_2", GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *g_containerT2Node_3 = NULL;
    ret = GmcYangEditChildNode(g_containerT2Node_2, "ContainerTwo_3", GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node_3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *g_containerT2Node_4 = NULL;
    ret = GmcYangEditChildNode(g_containerT2Node_3, "ContainerTwo_4", GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node_4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexProperty_F0(g_containerT2Node_4, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    GmcNodeT *g_containerT2Node_5 = NULL;
    ret = GmcYangEditChildNode(g_containerT2Node_4, "ContainerTwo_5", GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node_5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    fieldValue = 100;
    testYangSetVertexProperty_F0(g_containerT2Node_5, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // all校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/ContainerTwo_2/ContainerTwo_3/ContainerTwo_4/ContainerTwo_5/F0";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);
    system("gmsysview subtree -ns mandtyContain -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}



/*****************************************************************************
 * Description  : 003.conatain节点嵌套多层contain，中间层有P节点，其他是NP，最后一层节点有mandatory leaf
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(mandtyContain, Yang_072_mandtyContain_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel3.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F13");

    // 对 container NP 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ContainerTwo 
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作  -- ContainerTwo 
    int32_t f1fieldValue = 100;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作  -- ContainerTwo 
    bool f4 = true;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container P 子节点做replace操作  -- ContainerThree 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ContainerThree 
    testYangSetVertexProperty_F0(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 -- ContainerThree 
    ret = testYangSetField(g_containerT3Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作 -- ContainerThree 
    ret = testYangSetField(g_containerT3Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // all校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/ContainerTwo_2/ContainerTwo_3/ContainerTwo_4/ContainerTwo_5/F0";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // 对 container NP 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_NONE, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *g_containerT2Node_2 = NULL;
    ret = GmcYangEditChildNode(g_containerT2Node, "ContainerTwo_2", GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *g_containerT2Node_3 = NULL;
    ret = GmcYangEditChildNode(g_containerT2Node_2, "ContainerTwo_3", GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node_3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *g_containerT2Node_4 = NULL;
    ret = GmcYangEditChildNode(g_containerT2Node_3, "ContainerTwo_4", GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node_4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexProperty_F0(g_containerT2Node_4, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    GmcNodeT *g_containerT2Node_5 = NULL;
    ret = GmcYangEditChildNode(g_containerT2Node_4, "ContainerTwo_5", GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node_5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    fieldValue = 100;
    testYangSetVertexProperty_F0(g_containerT2Node_5, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // all校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath =
        "/ContainerOne/ContainerTwo/ContainerTwo_2/ContainerTwo_3/ContainerTwo_4/ContainerTwo_5/ContainerTwo_6/F0";

    checkRes = failRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);


    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // 对 container NP 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_NONE, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_containerT2Node, "ContainerTwo_2", GMC_OPERATION_NONE, &g_containerT2Node_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_containerT2Node_2, "ContainerTwo_3", GMC_OPERATION_NONE, &g_containerT2Node_3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_containerT2Node_3, "ContainerTwo_4", GMC_OPERATION_NONE, &g_containerT2Node_4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_containerT2Node_4, "ContainerTwo_5", GMC_OPERATION_NONE, &g_containerT2Node_5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *g_containerT2Node_6 = NULL;
    ret = GmcYangEditChildNode(g_containerT2Node_5, "ContainerTwo_6", GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node_6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    fieldValue = 100;
    testYangSetVertexProperty_F0(g_containerT2Node_6, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // all校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath =
        "/ContainerOne/ContainerTwo/ContainerTwo_2/ContainerTwo_3/ContainerTwo_4/ContainerTwo_5/ContainerTwo_6/F0";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns mandtyContain -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 004.conatain节点嵌套多层contain，最后一层是P节点，其他是NP，最后一层节点有mandatory leaf
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(mandtyContain, Yang_072_mandtyContain_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel4.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F13");

    // 对 container NP 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ContainerTwo 
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作  -- ContainerTwo 
    int32_t f1fieldValue = 100;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作  -- ContainerTwo 
    bool f4 = true;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container P 子节点做replace操作  -- ContainerThree 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ContainerThree 
    testYangSetVertexProperty_F0(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 -- ContainerThree 
    ret = testYangSetField(g_containerT3Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作 -- ContainerThree 
    ret = testYangSetField(g_containerT3Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // all校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/ContainerTwo_2/ContainerTwo_3/ContainerTwo_4/ContainerTwo_5/F0";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // 对 container NP 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_NONE, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *g_containerT2Node_2 = NULL;
    ret = GmcYangEditChildNode(g_containerT2Node, "ContainerTwo_2", GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *g_containerT2Node_3 = NULL;
    ret = GmcYangEditChildNode(g_containerT2Node_2, "ContainerTwo_3", GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node_3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *g_containerT2Node_4 = NULL;
    ret = GmcYangEditChildNode(g_containerT2Node_3, "ContainerTwo_4", GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node_4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexProperty_F0(g_containerT2Node_4, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    GmcNodeT *g_containerT2Node_5 = NULL;
    ret = GmcYangEditChildNode(g_containerT2Node_4, "ContainerTwo_5", GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node_5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    fieldValue = 100;
    testYangSetVertexProperty_F0(g_containerT2Node_5, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // all校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath ="";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // 对 container NP 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_NONE, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_containerT2Node, "ContainerTwo_2", GMC_OPERATION_NONE, &g_containerT2Node_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_containerT2Node_2, "ContainerTwo_3", GMC_OPERATION_NONE, &g_containerT2Node_3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_containerT2Node_3, "ContainerTwo_4", GMC_OPERATION_NONE, &g_containerT2Node_4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_containerT2Node_4, "ContainerTwo_5", GMC_OPERATION_NONE, &g_containerT2Node_5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *g_containerT2Node_6 = NULL;
    ret = GmcYangEditChildNode(g_containerT2Node_5, "ContainerTwo_6", GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node_6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    fieldValue = 100;
    testYangSetVertexProperty_F0(g_containerT2Node_6, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // all校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath ="";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns mandtyContain -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 005.conatain P节点，定义在list下，contain子节点有mandatory leaf
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(mandtyContain, Yang_072_mandtyContain_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel5.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F13");

    // 对 container NP 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ContainerTwo 
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作  -- ContainerTwo 
    int32_t f1fieldValue = 100;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作  -- ContainerTwo 
    bool f4 = true;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container P 子节点做replace操作  -- ContainerThree 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ContainerThree 
    testYangSetVertexProperty_F0(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 -- ContainerThree 
    ret = testYangSetField(g_containerT3Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作 -- ContainerThree 
    ret = testYangSetField(g_containerT3Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // all校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/ContainerTwo_2/ContainerTwo_3/ContainerTwo_4/ContainerTwo_5/F0";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int nDmlCnt = 1;
    for (int i = 150; i < 155; i++) {
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作  --ListOne

        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // all校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath ="";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    nDmlCnt = 1;
    for (int i = 150; i < 155; i++) {
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对 container P 子节点做replace操作 -- ListContainerOne
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listContainerNodeName1, GMC_OPERATION_REPLACE_GRAPH, &g_listContainerNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作 -- ListContainerOne F0
        if (i >151) {
            testYangSetVertexProperty_F0(g_listContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        }

        fieldValue = 36;
        testYangSetVertexProperty_Fx(g_listContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // all校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath ="/ContainerOne/ListOne[F0=150]/ListContainerOne/F0";

    checkRes = failRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);


    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    nDmlCnt = 1;
    for (int i = 150; i < 155; i++) {
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对 container NP 子节点做replace操作 -- ListContainerOne
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listContainerNodeName1, GMC_OPERATION_REPLACE_GRAPH, &g_listContainerNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        testYangSetVertexProperty_F0(g_listContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做replace操作 -- ListContainerOne F0
        fieldValue = 36;
        testYangSetVertexProperty_Fx(g_listContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F0");

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // all校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath ="/ContainerOne/ListOne[F0=150]/ListContainerOne/F0";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns mandtyContain -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}



/*****************************************************************************
 * Description  : 006.conatain NP节点，定义在list下，contain子节点有mandatory leaf
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(mandtyContain, Yang_072_mandtyContain_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel6.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F13");

    // 对 container NP 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ContainerTwo 
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作  -- ContainerTwo 
    int32_t f1fieldValue = 100;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作  -- ContainerTwo 
    bool f4 = true;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container P 子节点做replace操作  -- ContainerThree 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ContainerThree 
    testYangSetVertexProperty_F0(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 -- ContainerThree 
    ret = testYangSetField(g_containerT3Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作 -- ContainerThree 
    ret = testYangSetField(g_containerT3Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*
        /ListOne/ListContainerOne(NP)/F1 定义 mandatory | listOne未写数据 | all校验正常
    */
    // all校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/ContainerTwo_2/ContainerTwo_3/ContainerTwo_4/ContainerTwo_5/F0";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int nDmlCnt = 1;
    for (int i = 150; i < 155; i++) {
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作  --ListOne

        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    /*
        /ListOne/ListContainerOne(NP)/F1 定义 mandatory | listOne/F0写入数据, ListContainerOne未写数据 | all校验报错
    */

    // all校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath ="/ContainerOne/ListOne[F0=150]/ListContainerOne/F1";

    checkRes = failRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);


    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    nDmlCnt = 1;
    for (int i = 150; i < 155; i++) {
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对 container P 子节点做replace操作 -- ListContainerOne
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listContainerNodeName1, GMC_OPERATION_REPLACE_GRAPH, &g_listContainerNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作 -- ListContainerOne F0
        testYangSetVertexProperty_F0(g_listContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        if (i >151) {
            fieldValue = 36;
            testYangSetVertexProperty_Fx(g_listContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
        }

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    /*
        /ListOne/ListContainerOne(NP)/F1 定义 mandatory | listOne/F0写入数据, 部分record ListContainerOne未写数据 | all校验报错
    */

    // all校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath ="/ContainerOne/ListOne[F0=150]/ListContainerOne/F1";

    checkRes = failRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    nDmlCnt = 1;
    for (int i = 150; i < 152; i++) {
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对 container NP 子节点做replace操作 -- ListContainerOne
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listContainerNodeName1, GMC_OPERATION_REPLACE_GRAPH, &g_listContainerNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作 -- ListContainerOne F1
        fieldValue = 36;
        testYangSetVertexProperty_Fx(g_listContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    /*
        /ListOne/ListContainerOne(NP)/F1 定义 mandatory | listOne/F0写入数据, 全部record ListContainerOne未写数据 | all校验正常
    */

    // all校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath ="/ContainerOne/ListOne[F0=150]/ListContainerOne/F0";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns mandtyContain -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}



/*****************************************************************************
 * Description  : 007.conatain P节点，定义在choice的default case下，contain子节点有mandatory leaf
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(mandtyContain, Yang_072_mandtyContain_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel7.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F13");

    // 对 container NP 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ContainerTwo 
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作  -- ContainerTwo 
    int32_t f1fieldValue = 100;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作  -- ContainerTwo 
    bool f4 = true;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container P 子节点做replace操作  -- ContainerThree 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ContainerThree 
    testYangSetVertexProperty_F0(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 -- ContainerThree 
    ret = testYangSetField(g_containerT3Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作 -- ContainerThree 
    ret = testYangSetField(g_containerT3Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*1
        /Choice/CaseOne/CaseContainerOne(P)/F0 定义 mandatory | Choice未写数据 | all校验正常
    */

    // all校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/ContainerTwo_2/ContainerTwo_3/ContainerTwo_4/ContainerTwo_5/F0";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);


    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);

    // 对 choice case 子节点做replace操作 --CaseOne
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 --CaseOne
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 --CaseOne
    ret = testYangSetField(g_choiceCaseNode, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作 --CaseOne
    ret = testYangSetField(g_choiceCaseNode, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*2
        /Choice/CaseOne/CaseContainerOne(P)/F0 定义 mandatory | CaseOne写数据,CaseContainerOne未写数据 | all校验正常
    */

    // all校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath ="/ContainerOne/ListOne[F0=150]/ListContainerOne/F1";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    // 对 choice case 子节点做replace操作 --CaseOne
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_NONE, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_NONE, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // -- CaseContainerOne
    ret = GmcYangEditChildNode(g_choiceCaseNode, g_caseContainerNodeName1, GMC_OPERATION_REPLACE_GRAPH, &g_caseContainerNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_caseContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*3
        /Choice/CaseOne/CaseContainerOne(P)/F0 定义 mandatory | CaseOne写数据,CaseContainerOne.F1写数据 | all校验报错
    */

    // all校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath ="/ContainerOne/Choice/CaseOne/CaseContainerOne/F0";

    checkRes = failRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    // 对 choice case 子节点做replace操作 --CaseOne
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_NONE, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_NONE, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_choiceCaseNode, g_caseContainerNodeName1, GMC_OPERATION_NONE, &g_caseContainerNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_caseContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F0");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*4
        /Choice/CaseOne/CaseContainerOne(P)/F0 定义 mandatory | CaseOne写数据,CaseContainerOne.F0写数据 | all校验正常
    */

    // all校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath ="/ContainerOne/ListOne[F0=150]/ListContainerOne/F0";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns mandtyContain -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 008.conatain P节点，定义在choice的 非default case(case2)下，contain子节点有mandatory leaf
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(mandtyContain, Yang_072_mandtyContain_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel8.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F13");

    // 对 container NP 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ContainerTwo 
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作  -- ContainerTwo 
    int32_t f1fieldValue = 100;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作  -- ContainerTwo 
    bool f4 = true;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container P 子节点做replace操作  -- ContainerThree 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ContainerThree 
    testYangSetVertexProperty_F0(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 -- ContainerThree 
    ret = testYangSetField(g_containerT3Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作 -- ContainerThree 
    ret = testYangSetField(g_containerT3Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*1
        /Choice/CaseOne/CaseContainerOne(P)/F0 定义 mandatory | Choice未写数据 | all校验正常
    */

    // all校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/ContainerTwo_2/ContainerTwo_3/ContainerTwo_4/ContainerTwo_5/F0";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);

    // 对 choice case 子节点做replace操作 --CaseOne
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 --CaseOne
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 --CaseOne
    ret = testYangSetField(g_choiceCaseNode, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作 --CaseOne
    ret = testYangSetField(g_choiceCaseNode, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*2
        /Choice/CaseOne/CaseContainerOne(P)/F0 定义 mandatory | CaseOne写数据,CaseContainerOne未写数据 | all校验正常
    */

    // all校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath ="/ContainerOne/ListOne[F0=150]/ListContainerOne/F1";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    // 对 choice case 子节点做replace操作 --CaseOne
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_NONE, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_NONE, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_choiceCaseNode, g_caseContainerNodeName1, GMC_OPERATION_REPLACE_GRAPH, &g_caseContainerNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_caseContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*3
        /Choice/CaseOne/CaseContainerOne(P)/F0 定义 mandatory | CaseOne写数据,CaseContainerOne.F1写数据 | all校验报错
    */

    // all校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath ="/ContainerOne/Choice/CaseOne/CaseContainerOne/F0";

    checkRes = failRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    // 对 choice case 子节点做replace操作 --CaseOne
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_NONE, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_NONE, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_choiceCaseNode, g_caseContainerNodeName1, GMC_OPERATION_NONE, &g_caseContainerNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_caseContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F0");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*4
        /Choice/CaseOne/CaseContainerOne(P)/F0 定义 mandatory | CaseOne写数据,CaseContainerOne.F0写数据 | all校验正常
    */

    // all校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath ="/ContainerOne/ListOne[F0=150]/ListContainerOne/F0";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns mandtyContain -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 009.conatain NP节点，定义在choice的 非default case(case2)下，contain子节点有mandatory leaf
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(mandtyContain, Yang_072_mandtyContain_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel9.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F13");

    // 对 container NP 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ContainerTwo 
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作  -- ContainerTwo 
    int32_t f1fieldValue = 100;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作  -- ContainerTwo 
    bool f4 = true;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container P 子节点做replace操作  -- ContainerThree 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ContainerThree 
    testYangSetVertexProperty_F0(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 -- ContainerThree 
    ret = testYangSetField(g_containerT3Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作 -- ContainerThree 
    ret = testYangSetField(g_containerT3Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*1
        /Choice/CaseOne(非default)/CaseContainerOne(NP)/F0 定义 mandatory | Choice未写数据 | all校验正常
    */

    // all校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/ContainerTwo_2/ContainerTwo_3/ContainerTwo_4/ContainerTwo_5/F0";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);

    // 对 choice case 子节点做replace操作 --CaseOne
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 --CaseOne
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 --CaseOne
    ret = testYangSetField(g_choiceCaseNode, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作 --CaseOne
    ret = testYangSetField(g_choiceCaseNode, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*2
        /Choice/CaseOne(非default)/CaseContainerOne(NP)/F0 定义 mandatory| CaseOne写数据,CaseContainerOne未写数据 | all校验报错
    */

    // all校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath ="/ContainerOne/Choice/CaseOne/CaseContainerOne/F0";

    checkRes = failRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    // 对 choice case 子节点做replace操作 --CaseOne
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_NONE, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_NONE, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_choiceCaseNode, g_caseContainerNodeName1, GMC_OPERATION_REPLACE_GRAPH, &g_caseContainerNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_caseContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*3
        /Choice/CaseOne(非default)/CaseContainerOne(NP)/F0 定义 mandatory| CaseOne写数据,CaseContainerOne.F1写数据 | all校验报错
    */

    // all校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath ="/ContainerOne/Choice/CaseOne/CaseContainerOne/F0";

    checkRes = failRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    // 对 choice case 子节点做replace操作 --CaseOne
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_NONE, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_NONE, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_choiceCaseNode, g_caseContainerNodeName1, GMC_OPERATION_NONE, &g_caseContainerNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_caseContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F0");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*4
        /Choice/CaseOne(非default)/CaseContainerOne(NP)/F0 定义 mandatory| CaseOne写数据,CaseContainerOne.F0写数据 | all校验正常
    */

    // all校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath ="/ContainerOne/ListOne[F0=150]/ListContainerOne/F0";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns mandtyContain -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 010.conatain节点，leaf定义mandatory，定义多个when：条件依赖其他节点
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(mandtyContain, Yang_072_mandtyContain_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel10.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 container NP 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ContainerTwo 
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作  -- ContainerTwo 
    int32_t f1fieldValue = 100;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作  -- ContainerTwo 
    bool f4 = true;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container P 子节点做replace操作  -- ContainerThree 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ContainerThree 
    testYangSetVertexProperty_F0(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 -- ContainerThree 
    ret = testYangSetField(g_containerT3Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作 -- ContainerThree 
    ret = testYangSetField(g_containerT3Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*1
        /ContainerOne/F13 定义mandatory 、定义when ../F8 = 100 、定义 when ../F9 = 100 、定义 when ../F10 = 100、
            定义 when ../F11 = 100        | F13未写数据,F8,F9,F10,F11未写数据 | all校验正常,mandatory校验正常
    */

    // mandatory校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/F13";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_MANDATORY, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // all校验
    YangValidateUserDataT dataAll = {0};
    dataAll.status = GMERR_OK;
    dataAll.isValidErrorPathInfo = false;
    dataAll.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataAll.expectedErrMsg = "mandatory verify no field";
    dataAll.expectedErrPath = "/ContainerOne/F13";

    checkRes = sucRes;
    GmcValidateConfigT cfgAll = {.type = GMC_YANG_VALIDATION_MANDATORY, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfgAll, AsyncValidateLeafRefCb, &dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataAll.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataAll.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataAll.failCount);
    memset(&dataAll, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F8");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F9");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F10");
    fieldValue = 110;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F11");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*2
        /ContainerOne/F13 定义mandatory 、定义when ../F8 = 100 、定义 when ../F9 = 100 、定义 when ../F10 = 100、
            定义 when ../F11 = 100        | F13未写数据,F8,F9,F10 =100 F11=110 | all校验正常
    */

    // mandatory校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/F2";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // all校验
    memset(&dataAll, 0, sizeof(YangValidateUserDataT));
    dataAll.status = GMERR_OK;
    dataAll.isValidErrorPathInfo = false;
    dataAll.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataAll.expectedErrMsg = "mandatory verify no field";
    dataAll.expectedErrPath = "/ContainerOne/F2";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfgAll, AsyncValidateLeafRefCb, &dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataAll.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataAll.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataAll.failCount);
    memset(&dataAll, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);


    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F11");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*3
        /ContainerOne/F13 定义mandatory 、定义when ../F8 = 100 、定义 when ../F9 = 100 、定义 when ../F10 = 100、
            定义 when ../F11 = 100 | F13未写数据,F8,F9,F10 =100 F11=100 | all校验报错
    */

    // all校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/F13";

    checkRes = failRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // all校验
    memset(&dataAll, 0, sizeof(YangValidateUserDataT));
    dataAll.status = GMERR_OK;
    dataAll.isValidErrorPathInfo = true;
    dataAll.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataAll.expectedErrMsg = "mandatory verify no field";
    dataAll.expectedErrPath = "/ContainerOne/F13";

    checkRes = failRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfgAll, AsyncValidateLeafRefCb, &dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataAll.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataAll.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataAll.failCount);
    memset(&dataAll, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);


    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 10;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F13");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*3
        /ContainerOne/F13 定义mandatory 、定义when ../F8 = 100 、定义 when ../F9 = 100 、定义 when ../F10 = 100、
            定义 when ../F11 = 100 | F13写数据,F8,F9,F10 =100 F11=100 | all校验正常
    */

    // mandatory校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/F13";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // all校验
    memset(&dataAll, 0, sizeof(YangValidateUserDataT));
    dataAll.status = GMERR_OK;
    dataAll.isValidErrorPathInfo = false;
    dataAll.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataAll.expectedErrMsg = "mandatory verify no field";
    dataAll.expectedErrPath = "/ContainerOne/F13";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfgAll, AsyncValidateLeafRefCb, &dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataAll.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataAll.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataAll.failCount);
    memset(&dataAll, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);
    system("gmsysview subtree -ns mandtyContain -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 011.conatain节点，leaf定义mandatory，定义多个when：条件依赖自身节点
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(mandtyContain, Yang_072_mandtyContain_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel11.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 container NP 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ContainerTwo 
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作  -- ContainerTwo 
    int32_t f1fieldValue = 100;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作  -- ContainerTwo 
    bool f4 = true;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container P 子节点做replace操作  -- ContainerThree 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 -- ContainerThree 
    testYangSetVertexProperty_F0(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 -- ContainerThree 
    ret = testYangSetField(g_containerT3Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作 -- ContainerThree 
    ret = testYangSetField(g_containerT3Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*1
        /ContainerOne/F13 定义mandatory 、定义when current() = 100  | F13未写数据 | all校验正常
    */

    // mandatory校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/F13";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // all校验
    YangValidateUserDataT dataAll = {0};
    dataAll.status = GMERR_OK;
    dataAll.isValidErrorPathInfo = false;
    dataAll.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataAll.expectedErrMsg = "mandatory verify no field";
    dataAll.expectedErrPath = "/ContainerOne/F13";

    checkRes = sucRes;
    GmcValidateConfigT cfgAll = {.type = GMC_YANG_VALIDATION_MANDATORY, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfgAll, AsyncValidateLeafRefCb, &dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataAll.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataAll.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataAll.failCount);
    memset(&dataAll, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 120;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F13");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*2
        /ContainerOne/F13 定义mandatory 、定义when current() = 100  | F13=120 | all校验正常
    */

    // mandatory校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/F2";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // all校验
    memset(&dataAll, 0, sizeof(YangValidateUserDataT));
    dataAll.status = GMERR_OK;
    dataAll.isValidErrorPathInfo = false;
    dataAll.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataAll.expectedErrMsg = "mandatory verify no field";
    dataAll.expectedErrPath = "/ContainerOne/F13";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfgAll, AsyncValidateLeafRefCb, &dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataAll.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataAll.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataAll.failCount);
    memset(&dataAll, 0, sizeof(YangValidateUserDataT));


    // 提交事务
    TransCommit(g_conn_async);


    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F11");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*3
        /ContainerOne/F13 定义mandatory 、定义when current() = 100  | F13=100 | all校验正常
    */

    // mandatory校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/F13";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // all校验
    memset(&dataAll, 0, sizeof(YangValidateUserDataT));
    dataAll.status = GMERR_OK;
    dataAll.isValidErrorPathInfo = false;
    dataAll.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataAll.expectedErrMsg = "mandatory verify no field";
    dataAll.expectedErrPath = "/ContainerOne/F13";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfgAll, AsyncValidateLeafRefCb, &dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataAll.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataAll.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataAll.failCount);
    memset(&dataAll, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns mandtyContain -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 012.conatain节点嵌套多层NP contain，最后一层节点有mandatory leaf，第一层有when
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(mandtyContain, Yang_072_mandtyContain_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel12.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*1
        /ContainerTwo/ContainerTwo_2/ContainerTwo_3/ContainerTwo_4/ContainerTwo_5/F0 定义mandatory
        /ContainerTwo 定义when ../F13 = 100  | F13默认值110 | all校验正常
    */

    // all校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/ContainerTwo_2/ContainerTwo_3/ContainerTwo_4/ContainerTwo_5/F0";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F13");

   // 对 container NP 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *g_containerT2Node_2 = NULL;
    ret = GmcYangEditChildNode(g_containerT2Node, "ContainerTwo_2", GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *g_containerT2Node_3 = NULL;
    ret = GmcYangEditChildNode(g_containerT2Node_2, "ContainerTwo_3", GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node_3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *g_containerT2Node_4 = NULL;
    ret = GmcYangEditChildNode(g_containerT2Node_3, "ContainerTwo_4", GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node_4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexProperty_F0(g_containerT2Node_4, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*2
        /ContainerTwo/ContainerTwo_2/ContainerTwo_3/ContainerTwo_4/ContainerTwo_5/F0 定义mandatory
        /ContainerTwo 定义when ../F13 = 100  ContainerTwo_5.F0 未写值| F13=100 | all校验报错
    */

    // all校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/ContainerTwo_2/ContainerTwo_3/ContainerTwo_4/ContainerTwo_5/F0";

    checkRes = failRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F13");

   // 对 container NP 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_NONE, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_containerT2Node, "ContainerTwo_2", GMC_OPERATION_NONE, &g_containerT2Node_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_containerT2Node_2, "ContainerTwo_3", GMC_OPERATION_NONE, &g_containerT2Node_3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_containerT2Node_3, "ContainerTwo_4", GMC_OPERATION_NONE, &g_containerT2Node_4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *g_containerT2Node_5 = NULL;
    ret = GmcYangEditChildNode(g_containerT2Node_4, "ContainerTwo_5", GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node_5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    fieldValue = 130;
    testYangSetVertexProperty_F0(g_containerT2Node_5, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*3
        /ContainerTwo/ContainerTwo_2/ContainerTwo_3/ContainerTwo_4/ContainerTwo_5/F0 定义mandatory
        /ContainerTwo 定义when ../F13 = 100  ContainerTwo_5.F0 写值| F13=100 | all校验正常
    */

    // all校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/ContainerTwo_2/ContainerTwo_3/ContainerTwo_4/ContainerTwo_5/F0";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns mandtyContain -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}



/*****************************************************************************
 * Description  : 013.conatain节点嵌套多层contain，最后一层是P节点，其他是NP，最后一层节点有mandatory leaf，第一层有when
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(mandtyContain, Yang_072_mandtyContain_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel13.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*1
        /ContainerTwo/ContainerTwo_2/ContainerTwo_3/ContainerTwo_4/ContainerTwo_5(P点)/F0 定义mandatory
        /ContainerTwo 定义when ../F13 = 100  | F13默认值100 | all校验正常
    */

    // all校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/ContainerTwo_2/ContainerTwo_3/ContainerTwo_4/ContainerTwo_5/F0";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F13");

   // 对 container NP 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *g_containerT2Node_2 = NULL;
    ret = GmcYangEditChildNode(g_containerT2Node, "ContainerTwo_2", GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *g_containerT2Node_3 = NULL;
    ret = GmcYangEditChildNode(g_containerT2Node_2, "ContainerTwo_3", GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node_3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *g_containerT2Node_4 = NULL;
    ret = GmcYangEditChildNode(g_containerT2Node_3, "ContainerTwo_4", GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node_4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexProperty_F0(g_containerT2Node_4, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*2
        /ContainerTwo/ContainerTwo_2/ContainerTwo_3/ContainerTwo_4/ContainerTwo_5（P点）/F0 定义mandatory
        /ContainerTwo 定义when ../F13 = 100  ContainerTwo_5（P点） 未写值| F13=100 | all校验正常
    */

    // all校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/ContainerTwo_2/ContainerTwo_3/ContainerTwo_4/ContainerTwo_5/F0";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F13");

   // 对 container NP 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_NONE, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_containerT2Node, "ContainerTwo_2", GMC_OPERATION_NONE, &g_containerT2Node_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_containerT2Node_2, "ContainerTwo_3", GMC_OPERATION_NONE, &g_containerT2Node_3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_containerT2Node_3, "ContainerTwo_4", GMC_OPERATION_NONE, &g_containerT2Node_4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *g_containerT2Node_5 = NULL;
    ret = GmcYangEditChildNode(g_containerT2Node_4, "ContainerTwo_5", GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node_5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    fieldValue = 130;
    testYangSetVertexProperty_Fx(g_containerT2Node_5, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*3
        /ContainerTwo/ContainerTwo_2/ContainerTwo_3/ContainerTwo_4/ContainerTwo_5/F0 定义mandatory
        /ContainerTwo 定义when ../F13 = 100  ContainerTwo_5.F0 未写值| F13=100 | all校验报错
    */

    // all校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/ContainerTwo_2/ContainerTwo_3/ContainerTwo_4/ContainerTwo_5/F0";

    checkRes = failRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // 对 container NP 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_NONE, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_containerT2Node, "ContainerTwo_2", GMC_OPERATION_NONE, &g_containerT2Node_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_containerT2Node_2, "ContainerTwo_3", GMC_OPERATION_NONE, &g_containerT2Node_3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_containerT2Node_3, "ContainerTwo_4", GMC_OPERATION_NONE, &g_containerT2Node_4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_containerT2Node_4, "ContainerTwo_5", GMC_OPERATION_NONE, &g_containerT2Node_5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    fieldValue = 130;
    testYangSetVertexProperty_F0(g_containerT2Node_5, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*4
        /ContainerTwo/ContainerTwo_2/ContainerTwo_3/ContainerTwo_4/ContainerTwo_5/F0 定义mandatory
        /ContainerTwo 定义when ../F13 = 100  ContainerTwo_5.F0 写值| F13=100 | all校验正常
    */

    // all校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/ContainerTwo_2/ContainerTwo_3/ContainerTwo_4/ContainerTwo_5/F0";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns mandtyContain -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}



/*****************************************************************************
 * Description  : 014.conatain NP节点，定义在list下，contain子节点有mandatory leaf，list上定义when
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(mandtyContain, Yang_072_mandtyContain_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel14.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    fieldValue = 110;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F13");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int nDmlCnt = 1;
    for (int i = 150; i < 155; i++) {
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作  --ListOne

        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对 container P 子节点做replace操作 -- ListContainerOne
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listContainerNodeName1, GMC_OPERATION_REPLACE_GRAPH, &g_listContainerNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作 -- ListContainerOne F0
        if (i >151) {
            testYangSetVertexProperty_F0(g_listContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        }

        fieldValue = 36;
        testYangSetVertexProperty_Fx(g_listContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    /*
        /ListOne/ListContainerOne/F0 定义mandatroy、ListONe定义when /ContainerOne/F13 = 100
            | ListOne[150].ListContainerOne.F0 未赋值、 /ContainerOne/F13 = 110 | all校验正常
    */

    // all校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/ContainerTwo_2/ContainerTwo_3/ContainerTwo_4/ContainerTwo_5/F0";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F13");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    nDmlCnt = 1;
    for (int i = 150; i < 155; i++) {
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作  --ListOne

        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对 container P 子节点做replace操作 -- ListContainerOne
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listContainerNodeName1, GMC_OPERATION_REPLACE_GRAPH, &g_listContainerNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作 -- ListContainerOne F0
        if (i >151) {
            testYangSetVertexProperty_F0(g_listContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        }

        fieldValue = 36;
        testYangSetVertexProperty_Fx(g_listContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    /*
        /ListOne/ListContainerOne/F0 定义mandatroy、ListONe定义when /ContainerOne/F13 = 100
            | ListOne[150].ListContainerOne.F0 未赋值、 /ContainerOne/F13 = 100 | all校验报错
    */

    // all校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath ="/ContainerOne/ListOne[F0=150]/ListContainerOne/F0";

    checkRes = failRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    nDmlCnt = 1;
    for (int i = 150; i < 155; i++) {
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对 container P 子节点做replace操作 -- ListContainerOne
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listContainerNodeName1, GMC_OPERATION_REPLACE_GRAPH, &g_listContainerNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作 -- ListContainerOne F0
        testYangSetVertexProperty_F0(g_listContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    /*
        /ListOne/ListContainerOne/F0 定义mandatroy、ListONe定义when /ContainerOne/F13 = 100
            | ListOne[all].ListContainerOne.F0 赋值、 /ContainerOne/F13 = 100 | all校验正常
    */

    // all校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath ="/ContainerOne/ListOne[F0=150]/ListContainerOne/F0";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns mandtyContain -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 015.conatain P节点，定义在choice的 非default case(case2)下，contain子节点有mandatory leaf，choice上定义when
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(mandtyContain, Yang_072_mandtyContain_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel15.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    fieldValue = 110;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F13");

    // 对 choice case 子节点做replace操作 --CaseOne
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 --CaseOne
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 --CaseOne
    int32_t f1fieldValue = 100;
    ret = testYangSetField(g_choiceCaseNode, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作 --CaseOne
    bool f4 = true;
    ret = testYangSetField(g_choiceCaseNode, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作 --CaseOne.CaseContainerOne
    ret = GmcYangEditChildNode(g_choiceCaseNode, g_caseContainerNodeName1, GMC_OPERATION_REPLACE_GRAPH, &g_caseContainerNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_caseContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*1
        /Choice/CaseOne/CaseContainerOne(P)/F0 定义 mandatory 、Choice定义when /ContainerOne/F13 = 100
            /Choice/CaseOne/CaseContainerOne(P)/F1写数据(F0未写)、 /ContainerOne/F13 = 110 | mandatory校验报错，all校验正常
    */

    // mandatory校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/Choice/CaseOne/CaseContainerOne/F0";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = failRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_MANDATORY, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // all校验
    dataLef.isValidErrorPathInfo = false;
    checkRes = sucRes;
    GmcValidateConfigT cfgAll = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfgAll, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);

    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F13");

    // 对 choice case 子节点做replace操作 --CaseOne
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 --CaseOne
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 --CaseOne
    f1fieldValue = 100;
    ret = testYangSetField(g_choiceCaseNode, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作 --CaseOne
    f4 = true;
    ret = testYangSetField(g_choiceCaseNode, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作 --CaseOne.CaseContainerOne
    ret = GmcYangEditChildNode(g_choiceCaseNode, g_caseContainerNodeName1, GMC_OPERATION_REPLACE_GRAPH, &g_caseContainerNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_caseContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*2
        /Choice/CaseOne/CaseContainerOne(P)/F0 定义 mandatory 、Choice定义when /ContainerOne/F13 = 100
            /Choice/CaseOne/CaseContainerOne(P)/F1写数据(F0未写)、 /ContainerOne/F13 = 100 | mandatory校验报错,all校验报错
    */

    // mandatory校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath ="/ContainerOne/Choice/CaseOne/CaseContainerOne/F0";

    checkRes = failRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);

    // all校验
    ret = GmcYangValidateAsync(g_stmt_async, &cfgAll, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 choice case 子节点做replace操作 --CaseOne
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_NONE, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_NONE, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_choiceCaseNode, g_caseContainerNodeName1, GMC_OPERATION_NONE, &g_caseContainerNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_caseContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*3
        /Choice/CaseOne/CaseContainerOne(P)/F0 定义 mandatory 、Choice定义when /ContainerOne/F13 = 100
            /Choice/CaseOne/CaseContainerOne(P)/F0写数据、 /ContainerOne/F13 = 100 | mandatory校验正常,all校验正常
    */

    // mandatory校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath ="/ContainerOne/Choice/CaseOne/CaseContainerOne/F0";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // all校验
    dataLef.isValidErrorPathInfo = false;
    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfgAll, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns mandtyContain -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}

int dataMandatoryCheck(uint32_t checkType, YangValidateUserDataT &dataLef)
{
    // mandatory校验
    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    if (dataLef.isValidErrorPathInfo) {
        checkRes = failRes;
    }

    GmcValidateConfigT cfg = {.type = checkType, .cfgJson = NULL};
    int ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    if (GMERR_OK != ret) {
        return -1;
    }

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    
    if (GMERR_OK != dataLef.status || checkRes.validateRes, dataLef.validateRes || checkRes.failCount, dataLef.failCount) {
        return -1;
    }

    return 0;
}

/*****************************************************************************
 * Description  : 016.conatain NP节点，定义在choice的 非default case(case2)下，contain子节点有mandatory leaf,case2上定义when
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(mandtyContain, Yang_072_mandtyContain_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel16.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    fieldValue = 110;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F13");

    // 对 choice case 子节点做replace操作 --CaseOne
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 --CaseOne
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 --CaseOne
    int32_t f1fieldValue = 100;
    ret = testYangSetField(g_choiceCaseNode, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作 --CaseOne
    bool f4 = true;
    ret = testYangSetField(g_choiceCaseNode, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*1
        /Choice/CaseOne/CaseContainerOne(NP)/F0 定义 mandatory 、CaseOne定义when /ContainerOne/F13 = 100
            /Choice/CaseOne/CaseContainerOne(NP)未写数据、 /ContainerOne/F13 = 110 | mandatory校验报错，all校验正常
    */

    // mandatory校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/Choice/CaseOne/CaseContainerOne/F0";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = failRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_MANDATORY, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // all校验
    dataLef.isValidErrorPathInfo = false;
    checkRes = sucRes;
    GmcValidateConfigT cfgAll = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfgAll, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);

    fieldValue = 130;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F13");

    // 对 choice case 子节点做replace操作 --CaseOne
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 --CaseOne
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 --CaseOne
    f1fieldValue = 100;
    ret = testYangSetField(g_choiceCaseNode, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作 --CaseOne
    f4 = true;
    ret = testYangSetField(g_choiceCaseNode, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作 --CaseOne.CaseContainerOne
    ret = GmcYangEditChildNode(g_choiceCaseNode, g_caseContainerNodeName1, GMC_OPERATION_REPLACE_GRAPH, &g_caseContainerNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_caseContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*2
        /Choice/CaseOne/CaseContainerOne(P)/F0 定义 mandatory 、Choice定义when /ContainerOne/F13 = 100
            /Choice/CaseOne/CaseContainerOne(P)/F1写数据(F0未写)、 /ContainerOne/F13 = 130 | mandatory校验报错,all校验正常
    */

    // mandatory校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath ="/ContainerOne/Choice/CaseOne/CaseContainerOne/F0";

    checkRes = failRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);

    // all校验
    dataLef.isValidErrorPathInfo = false;
    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfgAll, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F13");

    // 对 choice case 子节点做replace操作 --CaseOne
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 --CaseOne
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 --CaseOne
    f1fieldValue = 100;
    ret = testYangSetField(g_choiceCaseNode, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作 --CaseOne.CaseContainerOne
    ret = GmcYangEditChildNode(g_choiceCaseNode, g_caseContainerNodeName1, GMC_OPERATION_REPLACE_GRAPH, &g_caseContainerNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_caseContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*3
        /Choice/CaseOne/CaseContainerOne(P)/F0 定义 mandatory 、Choice定义when /ContainerOne/F13 = 100
            /Choice/CaseOne/CaseContainerOne(P)/F1写数据(F0未写)、 /ContainerOne/F13 = 100 | mandatory校验报错,all校验报错
    */

    // mandatory校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath ="/ContainerOne/Choice/CaseOne/CaseContainerOne/F0";

    checkRes = failRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);

    // all校验
    ret = GmcYangValidateAsync(g_stmt_async, &cfgAll, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);


    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 choice case 子节点做replace操作 --CaseOne
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 --CaseOne
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 对子节点字段 做merge操作 --CaseOne
    f1fieldValue = 100;
    ret = testYangSetField(g_choiceCaseNode, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作 --CaseOne.CaseContainerOne
    ret = GmcYangEditChildNode(g_choiceCaseNode, g_caseContainerNodeName1, GMC_OPERATION_REPLACE_GRAPH, &g_caseContainerNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_caseContainerNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F0");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*3
        /Choice/CaseOne/CaseContainerOne(P)/F0 定义 mandatory 、Choice定义when /ContainerOne/F13 = 100
            /Choice/CaseOne/CaseContainerOne(P)/F1写数据(F0未写)、 /ContainerOne/F13 = 100 | mandatory校验报错,all校验报错
    */

    // mandatory校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath ="/ContainerOne/Choice/CaseOne/CaseContainerOne/F0";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);

    // all校验
    ret = GmcYangValidateAsync(g_stmt_async, &cfgAll, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns mandtyContain -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}

