/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#include "RCA_COMMON.h"
#include "../../../reliability/try.h"

class mandtyCase : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void mandtyCase::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/start.sh");
    int ret = testEnvInit();
    ASSERT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void mandtyCase::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}

void mandtyCase::SetUp()
{
    int ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务

    const char *namespace1 = "mandtyCase";
    const char *namespaceUserName = "abc";

    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观
    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));


    // alloc all stmt
    TestYangAllocAllstmt();
}

void mandtyCase::TearDown()
{
    const char *namespace1 = "mandtyCase";
    TryDropNameSpace(g_stmt_async, namespace1);

    // 释放all stmt
    TestYangFreeAllstmt();

    int ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestCheckValidateModelAsync(GmcStmtT *stmt)
{
    // 模型校验
    YangValidateUserDataT checkData = {0};
    int ret = GmcYangValidateModelAsync(stmt, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(true, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(0, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));
}


void AsyncValidateLeafRefCb(void *userData, GmcValidateResT validateRes, int32_t status, const char *errMsg)
{
    if (userData) {
        YangValidateUserDataT *uData = (YangValidateUserDataT *)userData;
        uData->status = status;
        if ((status != GMERR_OK) && (errMsg != NULL)) {
            printf("YangValidate errMsg: %s\n", errMsg);
        }
        uData->validateRes = validateRes.validateRes;
        uData->failCount = validateRes.failCount;

        printf(">>> validateRes: %d\n", validateRes.validateRes);
        printf(">>> failCount: %u\n", validateRes.failCount);

        if (uData->isValidErrorPathInfo) {
            GmcErrorPathInfoT msg;
            ASSERT_EQ(GMERR_OK, GmcYangGetErrorPathInfo(&msg));

            // 结果检查
            printf("--- errcode: %d\n", msg.errorCode);
            printf("--- errorClauseIndex: %u\n", msg.errorClauseIndex);
            printf("--- errorMsg: %s\n", msg.errorMsg);
            printf("--- errorPath: %s\n", msg.errorPath);
            EXPECT_EQ(uData->expectedErrCode, msg.errorCode);
            EXPECT_EQ(uData->expectedErrClauseIndex, msg.errorClauseIndex);
            EXPECT_STREQ(uData->expectedErrMsg, msg.errorMsg);
            EXPECT_STREQ(uData->expectedErrPath, msg.errorPath);
            ASSERT_NO_FATAL_FAILURE(GmcYangFreeErrorPathInfo());
        }

        uData->recvNum++;
    }
}
// yang set T0层f0
void testYangSetVertexProperty_Fx(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE optype, const char *name)
{
    int ret = 0;
    uint32_t f0Value = i;
    ret = testYangSetField(node, GMC_DATATYPE_UINT32, &f0Value, sizeof(uint32_t), name, optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 * Description  : 001.choice-case节点，无default case, leaf定义mandatory
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(mandtyCase, Yang_072_mandtyCase_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaCase/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaCase/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);


    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // mandatory校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_BUTT;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath = "";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 choice case 子节点做replace操作 --CaseOne
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做merge操作 --CaseOne
    int32_t f1fieldValue = 100;
    ret = testYangSetField(g_choiceCaseNode, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作 --CaseOne
    bool f4 = true;
    ret = testYangSetField(g_choiceCaseNode, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // mandatory校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/Choice/CaseOne/F0";

    checkRes = failRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 choice case 子节点做replace操作 --CaseOne
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_NONE, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_NONE, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 --CaseOne
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // mandatory校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/Choice/CaseOne/F0";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);


    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 choice case 子节点做replace操作 --CaseTwo
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, "CaseTwo", GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 --CaseTwo.F1
    f1fieldValue = 100;
    ret = testYangSetField(g_choiceCaseNode, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // mandatory校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/Choice/CaseTwo/F0";

    checkRes = failRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);


    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 choice case 子节点做replace操作 --CaseTwo
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, "CaseTwo", GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 --CaseTwo.F0
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // mandatory校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/Choice/CaseTwo/F0";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns mandtyCase -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}



/*****************************************************************************
 * Description  : 002.choice-case节点，有default case1, case2 leaf定义mandatory
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(mandtyCase, Yang_072_mandtyCase_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaCase/SubTreeVertexLabel2.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaCase/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // mandatory校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_BUTT;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath = "";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 choice case 子节点做replace操作 --CaseOne
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做merge操作 --CaseOne
    int32_t f1fieldValue = 100;
    ret = testYangSetField(g_choiceCaseNode, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段做create操作 --CaseOne
    bool f4 = true;
    ret = testYangSetField(g_choiceCaseNode, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // mandatory校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/Choice/CaseOne/F0";

    checkRes = failRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 choice case 子节点做replace操作 --CaseOne
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_NONE, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_NONE, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 --CaseOne
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // mandatory校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/Choice/CaseOne/F0";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns mandtyCase -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}



/*****************************************************************************
 * Description  : 003.choice-case节点，无default case, case的leaf定义mandatory，父节点是P节点
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(mandtyCase, Yang_072_mandtyCase_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaCase/SubTreeVertexLabel3.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaCase/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);


    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*
        p节点不写入数据 | 校验正常
    */

    // mandatory校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_BUTT;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath = "";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container P 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ContainerTwo 
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*
        p节点写入数据,choice子节点（无default case）未写数据 | 校验正常
    */

    // mandatory校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/Choice/CaseOne/F0";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container P 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_NONE, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 choice case 子节点做replace操作 --CaseOne
    ret = GmcYangEditChildNode(g_containerT2Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 --CaseOne
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*
        p节点写入数据,choice子节点CaseOne写数据,mandatory leaf未写数据 | 校验报错
    */

    // mandatory校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/Choice/CaseOne/F0";

    checkRes = failRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container P 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_NONE, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 choice case 子节点做replace操作 --CaseOne
    ret = GmcYangEditChildNode(g_containerT2Node, g_choiceNodeName, GMC_OPERATION_NONE, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_NONE, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 --CaseOne
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F0");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*
        p节点写入数据,choice子节点CaseOne写数据,mandatory leaf写入数据 | 校验报错
    */

    // mandatory校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/Choice/CaseOne/F0";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container P 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_NONE, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 choice case 子节点做replace操作 --CaseTwo
    ret = GmcYangEditChildNode(g_containerT2Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, "CaseTwo", GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 --CaseTwo.F1
    int32_t f1fieldValue = 100;
    ret = testYangSetField(g_choiceCaseNode, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*
        p节点写入数据,choice子节点CaseTwo写数据,mandatory leaf未写入数据 | 校验报错
    */

    // mandatory校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/Choice/CaseTwo/F0";

    checkRes = failRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);


    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container P 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_NONE, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 choice case 子节点做replace操作 --CaseTwo
    ret = GmcYangEditChildNode(g_containerT2Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, "CaseTwo", GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 --CaseTwo.F0
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F0");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*
        p节点写入数据,choice子节点CaseTwo写数据,mandatory leaf写入数据 | 校验正常
    */

    // mandatory校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/Choice/CaseTwo/F0";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns mandtyCase -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 004.choice-case节点，有default case1, case2 leaf定义mandatory，父节点是NP节点
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(mandtyCase, Yang_072_mandtyCase_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaCase/SubTreeVertexLabel4.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaCase/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);


    // 对 container NP 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ContainerTwo 
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);


    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*1
        np节点不写入数据,choice不写数据，显示default case | 校验正常
    */

    // mandatory校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_BUTT;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath = "";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container NP 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_NONE, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    // 对 choice case 子节点做replace操作 --CaseTwo
    ret = GmcYangEditChildNode(g_containerT2Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, "CaseTwo", GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 --CaseTwo.F1
    int32_t f1fieldValue = 100;
    ret = testYangSetField(g_choiceCaseNode, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);


    /*2
        np节点写入数据,choice.caseTwo写数据，mandatory leaf 未写数据 | 校验报错
    */

    // mandatory校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/Choice/CaseTwo/F0";

    checkRes = failRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);


    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container NP 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_NONE, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    // 对 choice case 子节点做replace操作 --CaseTwo
    ret = GmcYangEditChildNode(g_containerT2Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, "CaseTwo", GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 --CaseTwo.F0
    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F0");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*3
        np节点写入数据,choice.caseTwo写数据，mandatory leaf 写数据 | 校验正常
    */

    // mandatory校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/Choice/CaseTwo/F0";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns mandtyCase -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}



/*****************************************************************************
 * Description  : 005.choice-case节点，无default case, leaf定义mandatory，并定义多个when条件
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(mandtyCase, Yang_072_mandtyCase_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaCase/SubTreeVertexLabel5.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaCase/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    AW_FUN_Log(LOG_INFO, "\n>> trans 1:\n");
    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);


    // 对 container P 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ContainerTwo 
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);


    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*1
       choice节点不写入数据 | 校验正常
    */

    // mandatory校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_BUTT;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath = "";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_MANDATORY, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_INFO, "\n>> trans 2:\n");
    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container P 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_NONE, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 choice case 子节点做replace操作 --CaseOne
    ret = GmcYangEditChildNode(g_containerT2Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 --CaseOne
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*2
        p节点写入数据,choice子节点CaseOne.F0定义mandatory，未写数据，when不满足 | mandatory校验正常,all校验正常删除
    */

    // mandatory校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/Choice/CaseOne/F0";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // all校验
    YangValidateUserDataT dataAll = {0};
    dataAll.status = GMERR_OK;
    dataAll.isValidErrorPathInfo = false;
    dataAll.expectedErrCode = GMC_VIOLATES_BUTT;
    dataAll.expectedErrMsg = "";
    dataAll.expectedErrPath = "";

    checkRes = sucRes;
    GmcValidateConfigT cfgAll = {.type = GMC_YANG_VALIDATION_ALL , .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfgAll, AsyncValidateLeafRefCb, &dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataAll.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataAll.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataAll.failCount);
    memset(&dataAll, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_INFO, "\n>> trans 3:\n");
    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对root节点写数据
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F2");

    // 对 container P 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_NONE, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 choice case 子节点做replace操作 --CaseOne
    ret = GmcYangEditChildNode(g_containerT2Node, g_choiceNodeName, GMC_OPERATION_NONE, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_NONE, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 --CaseOne
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*3
        p节点写入数据,choice子节点CaseOne.F0定义mandatory和when，未写数据，when满足 | mandatory校验报错,all校验报错
    */

    // mandatory校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/Choice/CaseOne/F0";

    checkRes = failRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // all校验
    memset(&dataAll, 0, sizeof(YangValidateUserDataT));
    dataAll.status = GMERR_OK;
    dataAll.isValidErrorPathInfo = true;
    dataAll.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataAll.expectedErrMsg = "mandatory verify no field";
    dataAll.expectedErrPath = "/ContainerOne/ContainerTwo/Choice/CaseOne/F0";

    checkRes = failRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfgAll, AsyncValidateLeafRefCb, &dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataAll.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataAll.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataAll.failCount);
    memset(&dataAll, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_INFO, "\n>> trans 4:\n");
    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container P 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_NONE, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 choice case 子节点做replace操作 --CaseOne
    ret = GmcYangEditChildNode(g_containerT2Node, g_choiceNodeName, GMC_OPERATION_NONE, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_NONE, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 --CaseOne
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F0");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*4
        p节点写入数据,choice子节点CaseOne.F0定义mandatory和when，写数据，when满足 | mandatory校验正常,all校验正常
    */

    // mandatory校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/Choice/CaseOne/F0";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // all校验
    memset(&dataAll, 0, sizeof(YangValidateUserDataT));
    dataAll.status = GMERR_OK;
    dataAll.isValidErrorPathInfo = false;
    dataAll.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataAll.expectedErrMsg = "mandatory verify no field";
    dataAll.expectedErrPath = "/ContainerOne/ContainerTwo/Choice/CaseOne/F0";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfgAll, AsyncValidateLeafRefCb, &dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataAll.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataAll.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataAll.failCount);
    memset(&dataAll, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);
 
    AW_FUN_Log(LOG_INFO, "\n>> trans 5:\n");
    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对root节点写数据
    fieldValue = 110;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 container P 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_NONE, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 choice case 子节点做replace操作 --CaseTwo
    ret = GmcYangEditChildNode(g_containerT2Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, "CaseTwo", GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 --CaseTwo.F1
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*5
        p节点写入数据,choice子节点CaseTwo.F0定义mandatory和when，未写数据，when不满足 | mandatory校验正常,all校验正常删除
    */

    // mandatory校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/Choice/CaseOne/F0";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // all校验
    memset(&dataAll, 0, sizeof(YangValidateUserDataT));
    dataAll.status = GMERR_OK;
    dataAll.isValidErrorPathInfo = false;
    dataAll.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataAll.expectedErrMsg = "mandatory verify no field";
    dataAll.expectedErrPath = "/ContainerOne/ContainerTwo/Choice/CaseOne/F0";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfgAll, AsyncValidateLeafRefCb, &dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataAll.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataAll.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataAll.failCount);
    memset(&dataAll, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_INFO, "\n>> trans 6:\n");
    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对root节点写数据
    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 container P 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_NONE, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 choice case 子节点做replace操作 --CaseTwo
    ret = GmcYangEditChildNode(g_containerT2Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, "CaseTwo", GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 --CaseTwo.F1
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*5
        p节点写入数据,choice子节点CaseTwo.F0定义mandatory和when，未写数据，when满足 | mandatory校验报错,all校验报错
    */

    // mandatory校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/Choice/CaseTwo/F0";

    checkRes = failRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // all校验
    memset(&dataAll, 0, sizeof(YangValidateUserDataT));
    dataAll.status = GMERR_OK;
    dataAll.isValidErrorPathInfo = true;
    dataAll.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataAll.expectedErrMsg = "mandatory verify no field";
    dataAll.expectedErrPath = "/ContainerOne/ContainerTwo/Choice/CaseTwo/F0";

    checkRes = failRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfgAll, AsyncValidateLeafRefCb, &dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataAll.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataAll.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataAll.failCount);
    memset(&dataAll, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);


    AW_FUN_Log(LOG_INFO, "\n>> trans 7:\n");
    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    // 对 container P 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_NONE, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 choice case 子节点做replace操作 --CaseTwo
    ret = GmcYangEditChildNode(g_containerT2Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, "CaseTwo", GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 --CaseTwo.F0
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F0");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*5
        p节点写入数据,choice子节点CaseTwo.F0定义mandatory和when，写数据，when满足 | mandatory校验正常,all校验正常
    */

    // mandatory校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/Choice/CaseTwo/F0";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // all校验
    memset(&dataAll, 0, sizeof(YangValidateUserDataT));
    dataAll.status = GMERR_OK;
    dataAll.isValidErrorPathInfo = false;
    dataAll.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataAll.expectedErrMsg = "mandatory verify no field";
    dataAll.expectedErrPath = "/ContainerOne/ContainerTwo/Choice/CaseTwo/F0";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfgAll, AsyncValidateLeafRefCb, &dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataAll.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataAll.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataAll.failCount);
    memset(&dataAll, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns mandtyCase -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 006.choice-case节点，无default case, leaf定义mandatory，case上定义多个when条件
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(mandtyCase, Yang_072_mandtyCase_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaCase/SubTreeVertexLabel6.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaCase/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    AW_FUN_Log(LOG_INFO, "\n>> trans 1:\n");
    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);


    // 对 container P 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ContainerTwo 
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);


    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*1
       choice节点不写入数据 | 校验正常
    */

    // mandatory校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_BUTT;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath = "";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_MANDATORY, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_INFO, "\n>> trans 2:\n");
    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container P 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_NONE, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 choice case 子节点做replace操作 --CaseOne
    ret = GmcYangEditChildNode(g_containerT2Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 --CaseOne
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*2
        p节点写入数据,choice子节点CaseOne.F0定义mandatory，未写数据（其他leaf写数据），CaseOne上when不满足 | mandatory校验报错,all校验正常删除
    */

    // mandatory校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/Choice/CaseOne/F0";

    checkRes = failRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // all校验
    YangValidateUserDataT dataAll = {0};
    dataAll.status = GMERR_OK;
    dataAll.isValidErrorPathInfo = false;
    dataAll.expectedErrCode = GMC_VIOLATES_BUTT;
    dataAll.expectedErrMsg = "";
    dataAll.expectedErrPath = "";

    checkRes = sucRes;
    GmcValidateConfigT cfgAll = {.type = GMC_YANG_VALIDATION_ALL , .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfgAll, AsyncValidateLeafRefCb, &dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataAll.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataAll.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataAll.failCount);
    memset(&dataAll, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_INFO, "\n>> trans 3:\n");
    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对root节点写数据
    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F2");

    // 对 container P 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_NONE, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 choice case 子节点做replace操作 --CaseOne
    ret = GmcYangEditChildNode(g_containerT2Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 --CaseOne
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*3
        p节点写入数据,choice子节点CaseOne.F0定义mandatory未写数据（其他leaf写数据），CaseOne上when满足 | mandatory校验报错,all校验报错
    */

    // mandatory校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/Choice/CaseOne/F0";

    checkRes = failRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // all校验
    memset(&dataAll, 0, sizeof(YangValidateUserDataT));
    dataAll.status = GMERR_OK;
    dataAll.isValidErrorPathInfo = true;
    dataAll.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataAll.expectedErrMsg = "mandatory verify no field";
    dataAll.expectedErrPath = "/ContainerOne/ContainerTwo/Choice/CaseOne/F0";

    checkRes = failRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfgAll, AsyncValidateLeafRefCb, &dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataAll.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataAll.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataAll.failCount);
    memset(&dataAll, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_INFO, "\n>> trans 4:\n");
    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container P 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_NONE, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 choice case 子节点做replace操作 --CaseOne
    ret = GmcYangEditChildNode(g_containerT2Node, g_choiceNodeName, GMC_OPERATION_NONE, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_NONE, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 --CaseOne
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F0");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*4
        p节点写入数据,choice子节点CaseOne.F0定义mandatory，写数据，caseOne上when满足 | mandatory校验正常,all校验正常
    */

    // mandatory校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/Choice/CaseOne/F0";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // all校验
    memset(&dataAll, 0, sizeof(YangValidateUserDataT));
    dataAll.status = GMERR_OK;
    dataAll.isValidErrorPathInfo = false;
    dataAll.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataAll.expectedErrMsg = "mandatory verify no field";
    dataAll.expectedErrPath = "/ContainerOne/ContainerTwo/Choice/CaseOne/F0";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfgAll, AsyncValidateLeafRefCb, &dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataAll.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataAll.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataAll.failCount);
    memset(&dataAll, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_INFO, "\n>> trans 5:\n");
    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对root节点写数据
    fieldValue = 110;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 container P 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_NONE, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 choice case 子节点做replace操作 --CaseTwo
    ret = GmcYangEditChildNode(g_containerT2Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, "CaseTwo", GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 --CaseTwo.F1
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*5
        p节点写入数据,choice子节点CaseTwo.F0定义mandatory和when，未写数据，CaseTwo上when不满足 | mandatory校验报错,all校验正常删除
    */

    // mandatory校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/Choice/CaseTwo/F0";

    checkRes = failRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // all校验
    memset(&dataAll, 0, sizeof(YangValidateUserDataT));
    dataAll.status = GMERR_OK;
    dataAll.isValidErrorPathInfo = false;
    dataAll.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataAll.expectedErrMsg = "mandatory verify no field";
    dataAll.expectedErrPath = "/ContainerOne/ContainerTwo/Choice/CaseOne/F0";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfgAll, AsyncValidateLeafRefCb, &dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataAll.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataAll.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataAll.failCount);
    memset(&dataAll, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_INFO, "\n>> trans 6:\n");
    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对root节点写数据
    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 container P 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_NONE, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 choice case 子节点做replace操作 --CaseTwo
    ret = GmcYangEditChildNode(g_containerT2Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, "CaseTwo", GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 --CaseTwo.F1
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*5
        p节点写入数据,choice子节点CaseTwo.F0定义mandatory和when，未写数据，CaseTwo上when满足 | mandatory校验报错,all校验报错
    */

    // mandatory校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/Choice/CaseTwo/F0";

    checkRes = failRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // all校验
    memset(&dataAll, 0, sizeof(YangValidateUserDataT));
    dataAll.status = GMERR_OK;
    dataAll.isValidErrorPathInfo = true;
    dataAll.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataAll.expectedErrMsg = "mandatory verify no field";
    dataAll.expectedErrPath = "/ContainerOne/ContainerTwo/Choice/CaseTwo/F0";

    checkRes = failRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfgAll, AsyncValidateLeafRefCb, &dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataAll.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataAll.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataAll.failCount);
    memset(&dataAll, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);


    AW_FUN_Log(LOG_INFO, "\n>> trans 7:\n");
    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    // 对 container P 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_NONE, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 choice case 子节点做replace操作 --CaseTwo
    ret = GmcYangEditChildNode(g_containerT2Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, "CaseTwo", GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 --CaseTwo.F0
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F0");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*5
        p节点写入数据,choice子节点CaseTwo.F0定义mandatory和when，写数据，CaseTwo上when满足 | mandatory校验正常,all校验正常
    */

    // mandatory校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/Choice/CaseTwo/F0";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // all校验
    memset(&dataAll, 0, sizeof(YangValidateUserDataT));
    dataAll.status = GMERR_OK;
    dataAll.isValidErrorPathInfo = false;
    dataAll.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataAll.expectedErrMsg = "mandatory verify no field";
    dataAll.expectedErrPath = "/ContainerOne/ContainerTwo/Choice/CaseTwo/F0";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfgAll, AsyncValidateLeafRefCb, &dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataAll.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataAll.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataAll.failCount);
    memset(&dataAll, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns mandtyCase -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 007.choice-case节点，无default case, leaf定义mandatory，choice上定义多个when条件
 * Input        : None
 * Output       : None
 * Notes        : container
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(mandtyCase, Yang_072_mandtyCase_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaCase/SubTreeVertexLabel7.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaCase/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    AW_FUN_Log(LOG_INFO, "\n>> trans 1:\n");
    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);


    // 对 container P 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  -- ContainerTwo 
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);


    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*1
       choice节点不写入数据 | 校验正常
    */

    // mandatory校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_BUTT;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath = "";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_MANDATORY, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_INFO, "\n>> trans 2:\n");
    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container P 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_NONE, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 choice case 子节点做replace操作 --CaseOne
    ret = GmcYangEditChildNode(g_containerT2Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 --CaseOne
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*2
        p节点写入数据,choice子节点CaseOne.F0定义mandatory，未写数据（其他leaf写数据），choice上when不满足 | mandatory校验报错,all校验正常删除
    */

    // mandatory校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/Choice/CaseOne/F0";

    checkRes = failRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // all校验
    YangValidateUserDataT dataAll = {0};
    dataAll.status = GMERR_OK;
    dataAll.isValidErrorPathInfo = false;
    dataAll.expectedErrCode = GMC_VIOLATES_BUTT;
    dataAll.expectedErrMsg = "";
    dataAll.expectedErrPath = "";

    checkRes = sucRes;
    GmcValidateConfigT cfgAll = {.type = GMC_YANG_VALIDATION_ALL , .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfgAll, AsyncValidateLeafRefCb, &dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataAll.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataAll.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataAll.failCount);
    memset(&dataAll, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_INFO, "\n>> trans 3:\n");
    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对root节点写数据
    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F2");

    // 对 container P 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_NONE, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 choice case 子节点做replace操作 --CaseOne
    ret = GmcYangEditChildNode(g_containerT2Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 --CaseOne
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*3
        p节点写入数据,choice子节点CaseOne.F0定义mandatory未写数据（其他leaf写数据），choice上when满足 | mandatory校验报错,all校验报错
    */

    // mandatory校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/Choice/CaseOne/F0";

    checkRes = failRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // all校验
    memset(&dataAll, 0, sizeof(YangValidateUserDataT));
    dataAll.status = GMERR_OK;
    dataAll.isValidErrorPathInfo = true;
    dataAll.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataAll.expectedErrMsg = "mandatory verify no field";
    dataAll.expectedErrPath = "/ContainerOne/ContainerTwo/Choice/CaseOne/F0";

    checkRes = failRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfgAll, AsyncValidateLeafRefCb, &dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataAll.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataAll.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataAll.failCount);
    memset(&dataAll, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_INFO, "\n>> trans 4:\n");
    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container P 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_NONE, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 choice case 子节点做replace操作 --CaseOne
    ret = GmcYangEditChildNode(g_containerT2Node, g_choiceNodeName, GMC_OPERATION_NONE, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_NONE, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 --CaseOne
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F0");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*4
        p节点写入数据,choice子节点CaseOne.F0定义mandatory，写数据，choice上when满足 | mandatory校验正常,all校验正常
    */

    // mandatory校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/Choice/CaseOne/F0";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // all校验
    memset(&dataAll, 0, sizeof(YangValidateUserDataT));
    dataAll.status = GMERR_OK;
    dataAll.isValidErrorPathInfo = false;
    dataAll.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataAll.expectedErrMsg = "mandatory verify no field";
    dataAll.expectedErrPath = "/ContainerOne/ContainerTwo/Choice/CaseOne/F0";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfgAll, AsyncValidateLeafRefCb, &dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataAll.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataAll.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataAll.failCount);
    memset(&dataAll, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_INFO, "\n>> trans 5:\n");
    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对root节点写数据
    fieldValue = 110;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 container P 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_NONE, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 choice case 子节点做replace操作 --CaseTwo
    ret = GmcYangEditChildNode(g_containerT2Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, "CaseTwo", GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 --CaseTwo.F1
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*5
        p节点写入数据,choice子节点CaseTwo.F0定义mandatory，未写数据，choice上when不满足 | mandatory校验报错,all校验正常删除
    */

    // mandatory校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/Choice/CaseTwo/F0";

    checkRes = failRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // all校验
    memset(&dataAll, 0, sizeof(YangValidateUserDataT));
    dataAll.status = GMERR_OK;
    dataAll.isValidErrorPathInfo = false;
    dataAll.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataAll.expectedErrMsg = "mandatory verify no field";
    dataAll.expectedErrPath = "/ContainerOne/ContainerTwo/Choice/CaseOne/F0";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfgAll, AsyncValidateLeafRefCb, &dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataAll.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataAll.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataAll.failCount);
    memset(&dataAll, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_INFO, "\n>> trans 6:\n");
    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对root节点写数据
    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 container P 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_NONE, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 choice case 子节点做replace操作 --CaseTwo
    ret = GmcYangEditChildNode(g_containerT2Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, "CaseTwo", GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 --CaseTwo.F1
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*5
        p节点写入数据,choice子节点CaseTwo.F0定义mandatory和when，未写数据，choice上when满足 | mandatory校验报错,all校验报错
    */

    // mandatory校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/Choice/CaseTwo/F0";

    checkRes = failRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // all校验
    memset(&dataAll, 0, sizeof(YangValidateUserDataT));
    dataAll.status = GMERR_OK;
    dataAll.isValidErrorPathInfo = true;
    dataAll.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataAll.expectedErrMsg = "mandatory verify no field";
    dataAll.expectedErrPath = "/ContainerOne/ContainerTwo/Choice/CaseTwo/F0";

    checkRes = failRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfgAll, AsyncValidateLeafRefCb, &dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataAll.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataAll.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataAll.failCount);
    memset(&dataAll, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_INFO, "\n>> trans 7:\n");
    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理,关闭diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    // 对 container P 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_NONE, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 choice case 子节点做replace操作 --CaseTwo
    ret = GmcYangEditChildNode(g_containerT2Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, "CaseTwo", GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作 --CaseTwo.F0
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F0");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*5
        p节点写入数据,choice子节点CaseTwo.F0定义mandatory和when，写数据，CaseTwo上when满足 | mandatory校验正常,all校验正常
    */

    // mandatory校验
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataLef.expectedErrMsg = "mandatory verify no field";
    dataLef.expectedErrPath = "/ContainerOne/ContainerTwo/Choice/CaseTwo/F0";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // all校验
    memset(&dataAll, 0, sizeof(YangValidateUserDataT));
    dataAll.status = GMERR_OK;
    dataAll.isValidErrorPathInfo = false;
    dataAll.expectedErrCode = GMC_VIOLATES_MANDATORY_LEAF;
    dataAll.expectedErrMsg = "mandatory verify no field";
    dataAll.expectedErrPath = "/ContainerOne/ContainerTwo/Choice/CaseTwo/F0";

    checkRes = sucRes;
    ret = GmcYangValidateAsync(g_stmt_async, &cfgAll, AsyncValidateLeafRefCb, &dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataAll);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataAll.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataAll.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataAll.failCount);
    memset(&dataAll, 0, sizeof(YangValidateUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns mandtyCase -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}

