/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: 114_SubtreeANDCondition
 * Author: hanyang
 * Create: 2025-01-23
 */
#include "ANDCondition.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
GmcConnT *g_conn_async = NULL;
GmcStmtT *g_stmt_async = NULL;
GmcStmtT *g_stmt_root = NULL;
GmcStmtT *g_stmt_list[100] = {0};
GmcNodeT *g_rootNode = NULL;
GmcNodeT *g_childNode[100] = {0};

class SubtreeAndCondition_test : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void SubtreeAndCondition_test::SetUpTestCase()
{
    int ret;
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void SubtreeAndCondition_test::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

void SubtreeAndCondition_test::SetUp()
{
    int ret;
    AsyncUserDataT data = {0};

    ret = TestYangGmcConnect(&g_conn, &g_stmt, GMC_CONN_TYPE_SYNC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t i = 0;
    for (i = 0; i < 100; i++) {
        ret = GmcAllocStmt(g_conn_async, &g_stmt_list[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 异步创建namespace
    GmcDropNamespace(g_stmt, g_namespace);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = g_namespace;
    nspCfg.tablespaceName = NULL;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 创建表
    TestCreateLabel(g_stmt_async);

    AW_CHECK_LOG_BEGIN();
}

void SubtreeAndCondition_test::TearDown()
{
    int ret;
    uint32_t i = 0;
    AsyncUserDataT data = {0};
    AddWhiteList(GMERR_NO_DATA);
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    AddWhiteList(GMERR_SYNTAX_ERROR);
    AddWhiteList(GMERR_DATA_EXCEPTION);
    AW_CHECK_LOG_END();

    // 删除表
    TestDropLabel(g_stmt_async);

    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, g_namespace, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    GmcFreeStmt(g_stmt_root);
    for (i = 0; i < 100; i++) {
        GmcFreeStmt(g_stmt_list[i]);
    }
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_conn_async = NULL;
    g_stmt_async = NULL;
    g_stmt_root = NULL;
    for (i = 0; i < 100; i++) {
        g_stmt_list[i] = NULL;
    }
    g_rootNode = NULL;
    for (i = 0; i < 100; i++) {
        g_childNode[i] = NULL;
    }
}

/*
leaflist_1--uint32
leaflist_2--uint32，有默认值
leaflist_3--uint32
*/

/*****************************************************************************
 Description  : 001.ret = GmcYangSubtreeSetAndCondition接口参数空指针
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeAndCondition_test, Yang_114_Func_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    ret = GmcYangSubtreeSetAndCondition(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    AddWhiteList(GMERR_NULL_VALUE_NOT_ALLOWED);
}

/*****************************************************************************
 Description  : 002.GmcYangEditChildNode的optype不是subtree查询类型
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeAndCondition_test, Yang_114_Func_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 3; i++) {
        // leaflist_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(4, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(4, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询，内容过滤
    AW_FUN_Log(LOG_INFO, "Subtree Filter Content.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    AddWhiteList(GMERR_SYNTAX_ERROR);
    AddWhiteList(GMERR_NULL_VALUE_NOT_ALLOWED);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 003.新接口传入类型为root的node
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeAndCondition_test, Yang_114_Func_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询，内容过滤
    AW_FUN_Log(LOG_INFO, "Subtree Filter Content.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangSubtreeSetAndCondition(g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 004.新接口传入类型为list的node
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeAndCondition_test, Yang_114_Func_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询，内容过滤
    AW_FUN_Log(LOG_INFO, "Subtree Filter Content.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "list_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t f0 = 1;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 005.新接口传入类型为container的node
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeAndCondition_test, Yang_114_Func_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询，内容过滤
    AW_FUN_Log(LOG_INFO, "Subtree Filter Content.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t f0 = 1;
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t),
        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 006.新接口传入类型为choice，case的node
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeAndCondition_test, Yang_114_Func_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询，内容过滤
    AW_FUN_Log(LOG_INFO, "Subtree Filter Content.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "list_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "choice_1_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[3], "case_1_1_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t f0 = 1;
    ret = TestYangSetField(g_childNode[4], GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t),
        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 007.同一leaflist条件编辑同时包含与条件和或条件
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeAndCondition_test, Yang_114_Func_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 3; i++) {
        // leaflist_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(4, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(4, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询，内容过滤
    AW_FUN_Log(LOG_INFO, "Subtree Filter Content.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t f0 = 1;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    f0 = 3;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    f0 = 5;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = NULL;
    filter.subtree.obj = g_rootNode;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_OBJ;
    filters.filter = &filter;

    char replyPath[1024] = {0};
    ret = snprintf(replyPath, 1024, "SubtreeReplyJson/%s_Reply.json", "Yang_114_Func_007_01");
    AW_MACRO_EXPECT_NE_INT(0, ret);
    char *replyJson = NULL;
    readJanssonFile(replyPath, &replyJson);
    ASSERT_NE((void *)NULL, replyJson);
    std::vector<std::string> reply(1);
    reply[0] = replyJson;

    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_root,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };

    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_root, &filters, NULL, AsyncFetchRetCb, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    free(replyJson);
    replyJson = NULL;
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  :008.设置查询条件时，新接口ret = GmcYangSubtreeSetAndCondition和GmcYangSetNodeProperty顺序互换，
                普通subtree查询和encode查询
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeAndCondition_test, Yang_114_Func_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 10; i++) {
        // leaflist_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询，内容过滤
    AW_FUN_Log(LOG_INFO, "Subtree Filter Content.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t f0 = 1;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    f0 = 3;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_008_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 009.GmcYangEditChildNode后，重复调用新接口设置与条件
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeAndCondition_test, Yang_114_Func_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 10; i++) {
        // leaflist_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询，内容过滤
    AW_FUN_Log(LOG_INFO, "Subtree Filter Content.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t f0 = 1;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    f0 = 3;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_008_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 010.GmcYangEditChildNode后，直接调用新接口，没有GmcYangSetNodeProperty设置值
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeAndCondition_test, Yang_114_Func_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 3; i++) {
        // leaflist_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(4, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(4, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询，内容过滤
    AW_FUN_Log(LOG_INFO, "Subtree Filter Content.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_010_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 011.leaflist中存在多个用户设置值，设置subtree查询与条件，数据满足条件，
                普通subtree查询和encode查询，4种默认值过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeAndCondition_test, Yang_114_Func_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 10; i++) {
        // leaflist_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Report All Tagged.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 7;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_011_01", GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Report All.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 7;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_011_01", GMC_DEFAULT_FILTER_REPORT_ALL);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Explicit.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 7;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_011_01", GMC_DEFAULT_FILTER_EXPLICIT);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Trim.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 7;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_011_01", GMC_DEFAULT_FILTER_TRIM);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Encode.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 7;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterEncode(g_stmt_root, g_rootNode, "Yang_114_Func_011_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 012.leaflist中存在多个用户设置值，设置subtree查询与条件，数据部分满足条件，
                查询成功，返回部分数据普通subtree查询和encode查询，4种默认值过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeAndCondition_test, Yang_114_Func_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 10; i++) {
        if (i == 3) {
            continue;
        }

        // leaflist_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(10, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(10, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Report All Tagged.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 7;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_012_01", GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Report All.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 7;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_012_01", GMC_DEFAULT_FILTER_REPORT_ALL);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Explicit.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 7;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_012_01", GMC_DEFAULT_FILTER_EXPLICIT);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Trim.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 7;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_012_01", GMC_DEFAULT_FILTER_TRIM);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Encode.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 7;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterEncode(g_stmt_root, g_rootNode, "Yang_114_Func_012_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 013.leaflist中存在多个默认值，设置subtree查询与条件，数据满足条件，
                查询成功，返回数据普通subtree查询和encode查询，4种默认值过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeAndCondition_test, Yang_114_Func_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Report All Tagged.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 8;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_013_01", GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Report All.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 8;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_013_02", GMC_DEFAULT_FILTER_REPORT_ALL);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Explicit.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 8;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_013_03", GMC_DEFAULT_FILTER_EXPLICIT);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Trim.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 8;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_013_04", GMC_DEFAULT_FILTER_TRIM);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Encode.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 8;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterEncode(g_stmt_root, g_rootNode, "Yang_114_Func_013_05");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 014.leaflist中存在多个默认值，设置subtree查询与条件，数据部分满足条件，
                普通subtree查询和encode查询，4种默认值过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeAndCondition_test, Yang_114_Func_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Report All Tagged.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_014_01", GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Report All.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_014_01", GMC_DEFAULT_FILTER_REPORT_ALL);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Explicit.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_014_01", GMC_DEFAULT_FILTER_EXPLICIT);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Trim.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_014_01", GMC_DEFAULT_FILTER_TRIM);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Encode.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterEncode(g_stmt_root, g_rootNode, "Yang_114_Func_014_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 015.leaflist中存在多个默认值和用户设置值，设置subtree查询与条件，
                数据满足条件，普通subtree查询和encode查询，4种默认值过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeAndCondition_test, Yang_114_Func_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 6; i++) {
        // leaflist_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Report All Tagged.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 8;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_015_01", GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Report All.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 8;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_015_02", GMC_DEFAULT_FILTER_REPORT_ALL);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Explicit.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 8;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_015_03", GMC_DEFAULT_FILTER_EXPLICIT);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Trim.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 8;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_015_04", GMC_DEFAULT_FILTER_TRIM);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Encode.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 8;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterEncode(g_stmt_root, g_rootNode, "Yang_114_Func_015_05");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 016.leaflist中存在多个默认值和用户设置值，设置subtree查询与条件，
                数据部分满足条件，普通subtree查询和encode查询，4种默认值过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeAndCondition_test, Yang_114_Func_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 6; i++) {
        // leaflist_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Report All Tagged.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 18;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_016_01", GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Report All.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 18;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_016_01", GMC_DEFAULT_FILTER_REPORT_ALL);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Explicit.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 18;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_016_01", GMC_DEFAULT_FILTER_EXPLICIT);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Trim.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 18;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_016_01", GMC_DEFAULT_FILTER_TRIM);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Encode.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 18;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterEncode(g_stmt_root, g_rootNode, "Yang_114_Func_016_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 017.设置20个与条件，写入数据满足条件，普通subtree查询和encode查询，4种默认值过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeAndCondition_test, Yang_114_Func_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 100; i++) {
        // leaflist_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(101, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(101, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Report All Tagged.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 50; i <= 68; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 90;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_017_01", GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Report All.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 50; i <= 68; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 90;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_017_01", GMC_DEFAULT_FILTER_REPORT_ALL);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Explicit.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 50; i <= 68; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 90;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_017_01", GMC_DEFAULT_FILTER_EXPLICIT);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Trim.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 50; i <= 68; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 90;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_017_01", GMC_DEFAULT_FILTER_TRIM);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Encode.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 50; i <= 68; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 90;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterEncode(g_stmt_root, g_rootNode, "Yang_114_Func_017_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 018.设置20个与条件，写入数据部分满足条件，普通subtree查询和encode查询，4种默认值过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeAndCondition_test, Yang_114_Func_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 100; i++) {
        // leaflist_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(101, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(101, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Report All Tagged.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 50; i <= 68; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 900;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_018_01", GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Report All.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 50; i <= 68; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 900;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_018_01", GMC_DEFAULT_FILTER_REPORT_ALL);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Explicit.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 50; i <= 68; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 900;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_018_01", GMC_DEFAULT_FILTER_EXPLICIT);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Trim.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 50; i <= 68; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 900;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_018_01", GMC_DEFAULT_FILTER_TRIM);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Encode.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 50; i <= 68; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 900;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterEncode(g_stmt_root, g_rootNode, "Yang_114_Func_018_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 019.设置20个与条件，写入数据全部不满足条件，普通subtree查询和encode查询，4种默认值过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeAndCondition_test, Yang_114_Func_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 100; i++) {
        // leaflist_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(101, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(101, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Report All Tagged.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 500; i <= 520; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_019_01", GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Report All.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 500; i <= 520; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_019_01", GMC_DEFAULT_FILTER_REPORT_ALL);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Explicit.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 500; i <= 520; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_019_01", GMC_DEFAULT_FILTER_EXPLICIT);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Trim.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 500; i <= 520; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_019_01", GMC_DEFAULT_FILTER_TRIM);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Encode.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 500; i <= 520; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestSubtreeFilterEncode(g_stmt_root, g_rootNode, "Yang_114_Func_019_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 020.多个leaflist查询，全部设置与条件，写入数据满足条件，
                普通subtree查询和encode查询，4种默认值过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeAndCondition_test, Yang_114_Func_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 10; i++) {
        // leaflist_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 10; i++) {
        // leaflist_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 10; i++) {
        // leaflist_3
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_3", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(31, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(31, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Report All Tagged.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 1; i <= 3; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 1; i <= 3; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 1; i <= 3; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_3", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_020_01", GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Report All.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 1; i <= 3; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 1; i <= 3; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 1; i <= 3; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_3", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_020_02", GMC_DEFAULT_FILTER_REPORT_ALL);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Explicit.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 1; i <= 3; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 1; i <= 3; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 1; i <= 3; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_3", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_020_03", GMC_DEFAULT_FILTER_EXPLICIT);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Trim.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 1; i <= 3; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 1; i <= 3; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 1; i <= 3; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_3", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_020_04", GMC_DEFAULT_FILTER_TRIM);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Encode.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 1; i <= 3; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 1; i <= 3; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 1; i <= 3; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_3", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestSubtreeFilterEncode(g_stmt_root, g_rootNode, "Yang_114_Func_020_05");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 021.多个leaflist查询，全部设置与条件，写入数据部分满足条件，
                普通subtree查询和encode查询，4种默认值过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeAndCondition_test, Yang_114_Func_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 10; i++) {
        // leaflist_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 10; i++) {
        // leaflist_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 10; i++) {
        // leaflist_3
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_3", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(31, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(31, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Report All Tagged.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 9; i <= 11; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 9; i <= 11; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 9; i <= 11; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_3", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_021_01", GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Report All.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 9; i <= 11; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 9; i <= 11; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 9; i <= 11; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_3", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_021_01", GMC_DEFAULT_FILTER_REPORT_ALL);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Explicit.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 9; i <= 11; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 9; i <= 11; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 9; i <= 11; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_3", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_021_01", GMC_DEFAULT_FILTER_EXPLICIT);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Trim.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 9; i <= 11; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 9; i <= 11; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 9; i <= 11; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_3", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_021_01", GMC_DEFAULT_FILTER_TRIM);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Encode.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 9; i <= 11; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 9; i <= 11; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 9; i <= 11; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_3", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestSubtreeFilterEncode(g_stmt_root, g_rootNode, "Yang_114_Func_021_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 022.多个leaflist查询，部分设置与条件，部分设置或条件，
                写入数据满足条件，普通subtree查询和encode查询，4种默认值过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeAndCondition_test, Yang_114_Func_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 10; i++) {
        // leaflist_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 10; i++) {
        // leaflist_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 10; i++) {
        // leaflist_3
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_3", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(31, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(31, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Report All Tagged.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 4; i <= 5; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 4; i <= 5; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 4; i <= 5; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_3", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_022_01", GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Report All.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 4; i <= 5; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 4; i <= 5; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 4; i <= 5; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_3", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_022_02", GMC_DEFAULT_FILTER_REPORT_ALL);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Explicit.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 4; i <= 5; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 4; i <= 5; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 4; i <= 5; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_3", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_022_03", GMC_DEFAULT_FILTER_EXPLICIT);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Trim.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 4; i <= 5; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 4; i <= 5; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 4; i <= 5; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_3", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_022_04", GMC_DEFAULT_FILTER_TRIM);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Encode.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 4; i <= 5; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 4; i <= 5; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 4; i <= 5; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_3", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestSubtreeFilterEncode(g_stmt_root, g_rootNode, "Yang_114_Func_022_05");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 023.多个leaflist查询，部分设置与条件，部分设置或条件，
                写入数据部分满足条件，普通subtree查询和encode查询，4种默认值过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeAndCondition_test, Yang_114_Func_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 5; i++) {
        // leaflist_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 5; i++) {
        // leaflist_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 5; i++) {
        // leaflist_3
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_3", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(16, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(16, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Report All Tagged.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 4; i <= 5; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 4; i <= 5; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 4; i <= 5; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_3", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_023_01", GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Report All.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 4; i <= 5; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 4; i <= 5; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 4; i <= 5; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_3", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_023_02", GMC_DEFAULT_FILTER_REPORT_ALL);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Explicit.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 4; i <= 5; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 4; i <= 5; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 4; i <= 5; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_3", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_023_03", GMC_DEFAULT_FILTER_EXPLICIT);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Trim.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 4; i <= 5; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 4; i <= 5; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 4; i <= 5; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_3", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_023_04", GMC_DEFAULT_FILTER_TRIM);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Encode.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 4; i <= 5; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 4; i <= 5; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 4; i <= 5; i++) {
        ret = GmcYangEditChildNode(g_childNode[1], "leaflist_3", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
            "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestSubtreeFilterEncode(g_stmt_root, g_rootNode, "Yang_114_Func_023_05");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 024.leaflist中存在1W+条数据，设置与条件，写入数据满足条件，
                普通subtree查询和encode查询，4种默认值过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeAndCondition_test, Yang_114_Func_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 10000; i++) {
        // leaflist_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(10001, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(10001, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Report All Tagged.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5000;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 9990;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_024_01", GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Report All.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5000;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 9990;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_024_01", GMC_DEFAULT_FILTER_REPORT_ALL);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Explicit.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5000;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 9990;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_024_01", GMC_DEFAULT_FILTER_EXPLICIT);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Trim.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5000;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 9990;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_024_01", GMC_DEFAULT_FILTER_TRIM);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Encode.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5000;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 9990;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterEncode(g_stmt_root, g_rootNode, "Yang_114_Func_024_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 025.leaflist中存在1W+条数据，设置与条件，写入数据部分满足条件，
                普通subtree查询和encode查询，4种默认值过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeAndCondition_test, Yang_114_Func_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 10000; i++) {
        // leaflist_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(10001, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(10001, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Report All Tagged.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5000;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 10000;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_025_01", GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Report All.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5000;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 10000;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_025_01", GMC_DEFAULT_FILTER_REPORT_ALL);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Explicit.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5000;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 10000;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_025_01", GMC_DEFAULT_FILTER_EXPLICIT);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Trim.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5000;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 10000;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterObj(g_stmt_root, g_rootNode, "Yang_114_Func_025_01", GMC_DEFAULT_FILTER_TRIM);

    // subtree查询
    AW_FUN_Log(LOG_INFO, "Subtree Filter Encode.");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5000;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "leaflist_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 10000;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSubtreeSetAndCondition(g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSubtreeFilterEncode(g_stmt_root, g_rootNode, "Yang_114_Func_025_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
