/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#include "RCA_COMMON.h"
#include "../../../reliability/try.h"
#include "aliasTool.h"

class acrossTreeVal : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void acrossTreeVal::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"yangAutoIndex=1\"");
    system("sh $TEST_HOME/tools/start.sh -f");
    int ret = testEnvInit();
    ASSERT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void acrossTreeVal::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

const char *g_nspName01 = "acrossTreeVal";
const char *g_nspName02 = "acrossTreeVal02";

void acrossTreeVal::SetUp()
{
    int ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务

    const char *namespace1 = "acrossTreeVal";
    const char *namespaceUserName = "abc";

    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观
    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // alloc all stmt
    TestYangAllocAllstmt();
}

void acrossTreeVal::TearDown()
{
    const char *namespace1 = "acrossTreeVal";
    TryDropNameSpace(g_stmt_async, namespace1);

    // 释放all stmt
    TestYangFreeAllstmt();

    int ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


void TestCheckValidateModelAsync(GmcStmtT *stmt)
{
    // 模型校验
    YangValidateUserDataT checkData = {0};
    int ret = GmcYangValidateModelAsync(stmt, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(true, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(0, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));
}

// *typedef void (*GmcYangValidateDoneT)(void *userData, GmcValidateResT validateRes, int32_t status, const char *errMsg);*/
void AsyncValidateLeafRefCb(void *userData, GmcValidateResT validateRes, int32_t status, const char *errMsg)
{
    if (userData) {
        YangValidateUserDataT *uData = (YangValidateUserDataT *)userData;
        uData->status = status;
        if ((status != GMERR_OK) && (errMsg != NULL)) {
            printf("YangValidate errMsg: %s\n", errMsg);
        }
        uData->validateRes = validateRes.validateRes;
        uData->failCount = validateRes.failCount;

        printf(">>> validateRes: %d\n", validateRes.validateRes);
        printf(">>> failCount: %u\n", validateRes.failCount);

        if (uData->isValidErrorPathInfo) {
            GmcErrorPathInfoT msg;
            ASSERT_EQ(GMERR_OK, GmcYangGetErrorPathInfo(&msg));

            // 结果检查
            printf("--- errcode: %d\n", msg.errorCode);
            printf("--- errorClauseIndex: %u\n", msg.errorClauseIndex);
            printf("--- errorMsg: %s\n", msg.errorMsg);
            printf("--- errorPath: %s\n", msg.errorPath);
            EXPECT_EQ(uData->expectedErrCode, msg.errorCode);
            EXPECT_EQ(uData->expectedErrClauseIndex, msg.errorClauseIndex);
            EXPECT_STREQ(uData->expectedErrMsg, msg.errorMsg);
            EXPECT_STREQ(uData->expectedErrPath, msg.errorPath);
            ASSERT_NO_FATAL_FAILURE(GmcYangFreeErrorPathInfo());
        }

        uData->recvNum++;
    }
}

char *g_diffreply01 = NULL;
char *g_diffreply02 = NULL;
std::vector<std::string> g_diffreply(2);
void SetDiffFile(const char *diffPath1, const char *diffPath2)
{
    g_diffreply.clear();
    if (diffPath1 != NULL) {
        g_diffreply01 = NULL;
        readJanssonFile(diffPath1, &g_diffreply01);
        g_diffreply.push_back(g_diffreply01);
    }
    if (diffPath2 != NULL) {
        g_diffreply02 = NULL;
        readJanssonFile(diffPath2, &g_diffreply02);
        g_diffreply.push_back(g_diffreply02);
    }
}
void AddDiffFile(const char *diffPath)
{
    if (diffPath != NULL) {
        char * diffreply01 = NULL;
        readJanssonFile(diffPath, &diffreply01);
        g_diffreply.push_back(diffreply01);
        free(diffreply01);
    }
}
void ReleaseDiffFile()
{
    if (g_diffreply01) {
        free(g_diffreply01);
        g_diffreply01 = NULL;
    }

    if (g_diffreply02) {
        free(g_diffreply02);
        g_diffreply02 = NULL;
    }
}

void ClearDiffFile()
{
    g_diffreply.clear();
}


/*****************************************************************************
 * Description  : 001.定义树root1(NP)、root2(NP)，root1定义带默认值字段F1、F2、F3定义when、must、leafref校验依赖root2，数据校验合法
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(acrossTreeVal, Yang_097_acrossTreeVal_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabel.gmjson", "schemaBasicFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabelB.gmjson", "schemaBasicFunc/SubTreeEdgelLabelB.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 不编辑数据
    GmcBatchT *batch = NULL;

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_BUTT;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath = "";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff 预期为空
    AsyncUserDataT data = {0};
    std::vector<std::string> diffreply(1);
    diffreply[0] = "";
    testFetchAndDeparseDiff(g_stmt_async, batch, diffreply, data, GMERR_DATA_EXCEPTION);

    // 回滚事务，因为diff查询报错，会导致事务commit失败
    TransRollback(g_conn_async);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_001");
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 002.定义树root1(NP)、root2(NP)，root1定义带默认值字段F1、F2、F3定义when、must、leafref校验依赖root2，when、must、leafref条件不满足
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(acrossTreeVal, Yang_097_acrossTreeVal_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabel2.gmjson", "schemaBasicFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabelB.gmjson", "schemaBasicFunc/SubTreeEdgelLabelB.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

#ifndef FEATURE_PERSISTENCE
    // create savepoint
    CreateSavepoint(g_conn_async, g_savepointName);
#endif

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne_B", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F5");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F6");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F7");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MUST;
    dataLef.expectedErrMsg = "violated must clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/F6";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffBasicFunc/diff002_B.json", "diffBasicFunc/diff002.json");
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_002");

#ifndef FEATURE_PERSISTENCE
    // Rollback savepoint  diff为空
    RollbackSavepoint(g_conn_async, g_savepointName);
    ClearDiffFile();
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_002_Sp");
#endif
    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 003.定义树root1(NP)、root2(NP)，root1定义带默认值字段F1、F2、F3定义when、must、leafref校验依赖root2，when、leafref条件不满足，must条件满足
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(acrossTreeVal, Yang_097_acrossTreeVal_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabel2.gmjson", "schemaBasicFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabelB.gmjson", "schemaBasicFunc/SubTreeEdgelLabelB.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

#ifndef FEATURE_PERSISTENCE
    // create savepoint
    CreateSavepoint(g_conn_async, g_savepointName);
#endif

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne_B", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    fieldValue = 555;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F5");
    fieldValue = 666;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F6");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F7");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/F7";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffBasicFunc/diff003.json", NULL);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_003");

#ifndef FEATURE_PERSISTENCE
    // Rollback savepoint  diff为空
    RollbackSavepoint(g_conn_async, g_savepointName);
    ClearDiffFile();
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_002_Sp");
#endif

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 004.定义树root1(NP)、root2(NP)，root1定义带默认值字段F1、F2、F3定义when、must、leafref校验依赖root2，when、must、leafref条件不满足, GMC_YANG_VALIDATION_ALL校验
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(acrossTreeVal, Yang_097_acrossTreeVal_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabel2.gmjson", "schemaBasicFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabelB.gmjson", "schemaBasicFunc/SubTreeEdgelLabelB.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

#ifndef FEATURE_PERSISTENCE
    // create savepoint
    CreateSavepoint(g_conn_async, g_savepointName);
#endif

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F5");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_WHEN;
    dataLef.expectedErrMsg = "violated when clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/F5";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffBasicFunc/diff004.json", NULL);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_004");

#ifndef FEATURE_PERSISTENCE
    // Rollback savepoint  diff为空
    RollbackSavepoint(g_conn_async, g_savepointName);
    ClearDiffFile();
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_002_Sp");
#endif

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 005.定义树root1(NP)、root2(NP)，root1定义带默认值字段F1定义when校验依赖root2，when删除后，新开事务，when满足条件
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(acrossTreeVal, Yang_097_acrossTreeVal_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabel2.gmjson", "schemaBasicFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabelB.gmjson", "schemaBasicFunc/SubTreeEdgelLabelB.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MUST;
    dataLef.expectedErrMsg = "violated must clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/F6";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_005");

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

#ifndef FEATURE_PERSISTENCE
    // create savepoint
    CreateSavepoint(g_conn_async, g_savepointName);
#endif

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne_B", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    fieldValue = 5555;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F5");
    fieldValue = 6666;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F6");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F7");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    memset(&dataLef, 0, sizeof(dataLef));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/F7";

    checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffBasicFunc/diff005_B.json", "diffBasicFunc/diff005.json");
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_005_2");

#ifndef FEATURE_PERSISTENCE
    // Rollback savepoint  diff为空
    RollbackSavepoint(g_conn_async, g_savepointName);
    ClearDiffFile();
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_005_Sp");
#endif

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 006.定义树root1(NP)、root2(NP)，root1定义带default case上定义when校验依赖root2，when删除后，新开事务，when满足条件
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(acrossTreeVal, Yang_097_acrossTreeVal_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabel6.gmjson", "schemaBasicFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabelB.gmjson", "schemaBasicFunc/SubTreeEdgelLabelB.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MUST;
    dataLef.expectedErrMsg = "violated must clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/F6";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_006");

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

#ifndef FEATURE_PERSISTENCE
    // create savepoint
    CreateSavepoint(g_conn_async, g_savepointName);
#endif

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne_B", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    fieldValue = 55;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F5");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    memset(&dataLef, 0, sizeof(dataLef));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/F7";

    checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffBasicFunc/diff006_B.json", "diffBasicFunc/diff006.json");
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_006_2");

#ifndef FEATURE_PERSISTENCE
    // Rollback savepoint  diff为空
    RollbackSavepoint(g_conn_async, g_savepointName);
    ClearDiffFile();
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_006");
#endif

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 007.定义树root1(NP)、root2(NP)，root1定义带default case上默认值字段定义when校验依赖root2，when删除后，新开事务，when满足条件
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(acrossTreeVal, Yang_097_acrossTreeVal_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabel7.gmjson", "schemaBasicFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabelB.gmjson", "schemaBasicFunc/SubTreeEdgelLabelB.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MUST;
    dataLef.expectedErrMsg = "violated must clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/F6";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_007");

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

#ifndef FEATURE_PERSISTENCE
    // create savepoint
    CreateSavepoint(g_conn_async, g_savepointName);
#endif

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne_B", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    fieldValue = 55;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F5");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    memset(&dataLef, 0, sizeof(dataLef));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/F7";

    checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffBasicFunc/diff007_B.json", "diffBasicFunc/diff007.json");
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_007_2");

#ifndef FEATURE_PERSISTENCE
    // Rollback savepoint  diff为空
    RollbackSavepoint(g_conn_async, g_savepointName);
    ClearDiffFile();
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_007");
#endif

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 008.定义树root1(P)、root2(NP)，root1定义带默认值字段F1定义when校验依赖root2，不创建root1,并且when条件不满足
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(acrossTreeVal, Yang_097_acrossTreeVal_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabel8.gmjson", "schemaBasicFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabelB.gmjson", "schemaBasicFunc/SubTreeEdgelLabelB.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 不编辑数据
    GmcBatchT *batch = NULL;

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_BUTT;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath = "";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 事务提交
    TransCommit(g_conn_async);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_008");
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 009.定义树root1(P)、root2(NP)，root1定义带默认值字段F1定义when校验依赖root2，创建root1不编辑F1，when条件不满足
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(acrossTreeVal, Yang_097_acrossTreeVal_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabel8.gmjson", "schemaBasicFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabelB.gmjson", "schemaBasicFunc/SubTreeEdgelLabelB.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/F7";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffBasicFunc/diff009.json", NULL);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_009");

    // 事务提交
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 010.定义树root1(P)、root2(NP)，root1定义带默认值字段F1定义when校验依赖root2，创建root1不编辑F1，when条件满足
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(acrossTreeVal, Yang_097_acrossTreeVal_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabel8.gmjson", "schemaBasicFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabelB.gmjson", "schemaBasicFunc/SubTreeEdgelLabelB.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

#ifndef FEATURE_PERSISTENCE
    // create savepoint
    CreateSavepoint(g_conn_async, g_savepointName);
#endif

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne_B", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    fieldValue = 5555;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F5");
    fieldValue = 7777;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F7");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/F7";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffBasicFunc/diff010.json", "diffBasicFunc/diff010_B.json");
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_010");

#ifndef FEATURE_PERSISTENCE
    // Rollback savepoint  diff为空
    RollbackSavepoint(g_conn_async, g_savepointName);
    ClearDiffFile();
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_010_Sp");
#endif

    // 事务提交
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 011.定义树root1(P)、root2(NP)，root1定义带默认值字段F1定义when校验依赖root2，创建root1编辑F1，when条件不满足
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(acrossTreeVal, Yang_097_acrossTreeVal_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabel8.gmjson", "schemaBasicFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabelB.gmjson", "schemaBasicFunc/SubTreeEdgelLabelB.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

#ifndef FEATURE_PERSISTENCE
    // create savepoint
    CreateSavepoint(g_conn_async, g_savepointName);
#endif

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    fieldValue = 300;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F5");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne_B", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F5");
    fieldValue = 7777;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F7");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/F7";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffBasicFunc/diff011.json", "diffBasicFunc/diff011_B.json");
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_011");

#ifndef FEATURE_PERSISTENCE
    // Rollback savepoint  diff为空
    RollbackSavepoint(g_conn_async, g_savepointName);
    ClearDiffFile();
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj，为空
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_010_Sp");
#endif

    // 事务提交
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 012.定义树root1(P)、root2(NP)，root1定义带默认值字段F1定义when校验依赖root2，创建root1编辑F1，when条件满足
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(acrossTreeVal, Yang_097_acrossTreeVal_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabel8.gmjson", "schemaBasicFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabelB.gmjson", "schemaBasicFunc/SubTreeEdgelLabelB.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

#ifndef FEATURE_PERSISTENCE
    // create savepoint
    CreateSavepoint(g_conn_async, g_savepointName);
#endif

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    fieldValue = 300;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F5");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne_B", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    fieldValue = 300;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F5");
    fieldValue = 7777;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F7");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/F7";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffBasicFunc/diff012.json", "diffBasicFunc/diff012_B.json");
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_012");

#ifndef FEATURE_PERSISTENCE
    // Rollback savepoint  diff为空
    RollbackSavepoint(g_conn_async, g_savepointName);
    ClearDiffFile();
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj，为空
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_010_Sp");
#endif

    // 事务提交
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 013.定义树root1(P)、root2(NP)，root1定义带默认值字段F1定义when校验依赖root2，预置创建root1不编辑F1，事务1里when删除F1，事务2里编辑root2使when条件满足
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(acrossTreeVal, Yang_097_acrossTreeVal_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabel8.gmjson", "schemaBasicFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabelB.gmjson", "schemaBasicFunc/SubTreeEdgelLabelB.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/F7";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 事务提交
    TransCommit(g_conn_async);

    // subtree查询 obj，为空
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_013");
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);
    nDmlCnt = 0;

#ifndef FEATURE_PERSISTENCE
    // create savepoint
    CreateSavepoint(g_conn_async, g_savepointName);
#endif

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne_B", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    fieldValue = 5555;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F5");
    fieldValue = 7777;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F7");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    memset(&dataLef, 0, sizeof(dataLef));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/F7";

    checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffBasicFunc/diff013.json", "diffBasicFunc/diff013_B.json");
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_013_2");

#ifndef FEATURE_PERSISTENCE
    // Rollback savepoint  diff为空
    RollbackSavepoint(g_conn_async, g_savepointName);
    ClearDiffFile();
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj，为空
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_013");
#endif

    // 事务提交
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 014.定义树root1(P)、root2(NP)，root1定义带默认值字段F1定义when校验依赖root2，事务1里创建root1编辑F1，事务2里编辑root2使when条件满足
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(acrossTreeVal, Yang_097_acrossTreeVal_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabel8.gmjson", "schemaBasicFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabelB.gmjson", "schemaBasicFunc/SubTreeEdgelLabelB.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F5");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F6");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F7");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 事务提交
    TransCommit(g_conn_async);

    // subtree查询 obj，为空
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_014");
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);
    nDmlCnt = 0;

#ifndef FEATURE_PERSISTENCE
    // create savepoint
    CreateSavepoint(g_conn_async, g_savepointName);
#endif

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne_B", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F5");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F6");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F7");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/F7";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffBasicFunc/diff014.json", NULL);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_014");

#ifndef FEATURE_PERSISTENCE
    // Rollback savepoint  diff为空
    RollbackSavepoint(g_conn_async, g_savepointName);
    ClearDiffFile();
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj，为空
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_014");
#endif

    // 事务提交
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 015.定义树root1(P)、root2(NP)，root1定义带默认值字段F1定义when校验依赖root2，事务1里创建root1编辑F1，事务2里编辑root2使when条件不满足
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(acrossTreeVal, Yang_097_acrossTreeVal_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabel8.gmjson", "schemaBasicFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabelB.gmjson", "schemaBasicFunc/SubTreeEdgelLabelB.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F5");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F6");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F7");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 事务提交
    TransCommit(g_conn_async);

    // subtree查询 obj，为空
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_015");
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);
    nDmlCnt = 0;

#ifndef FEATURE_PERSISTENCE
    // create savepoint
    CreateSavepoint(g_conn_async, g_savepointName);
#endif

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne_B", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 800;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F5");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F6");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F7");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MUST;
    dataLef.expectedErrMsg = "violated must clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/F6";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffBasicFunc/diff015.json", "diffBasicFunc/diff015_B.json");
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_015_2");

#ifndef FEATURE_PERSISTENCE
    // Rollback savepoint  diff为空
    RollbackSavepoint(g_conn_async, g_savepointName);
    ClearDiffFile();
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj，为空
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_015");
#endif

    // 事务提交
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 016.定义树root1(NP)、root2(P)，root1定义带默认值字段F1定义when校验依赖root2，不预置数据，数据校验
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(acrossTreeVal, Yang_097_acrossTreeVal_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabel16.gmjson", "schemaBasicFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabelB16.gmjson", "schemaBasicFunc/SubTreeEdgelLabelB.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/F7";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_016");

    // 事务提交
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 017.定义树root1(NP)、root2(P)，root1定义带默认值字段F1定义when校验依赖root2，编辑root2，使when条件满足
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(acrossTreeVal, Yang_097_acrossTreeVal_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabel16.gmjson", "schemaBasicFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabelB16.gmjson", "schemaBasicFunc/SubTreeEdgelLabelB.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

#ifndef FEATURE_PERSISTENCE
    // create savepoint
    CreateSavepoint(g_conn_async, g_savepointName);
#endif

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne_B", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 5555;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F5");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F6");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F7");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/F7";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffBasicFunc/diff017_B.json", NULL);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_017");

#ifndef FEATURE_PERSISTENCE
    // Rollback savepoint  diff为空
    RollbackSavepoint(g_conn_async, g_savepointName);
    ClearDiffFile();
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj，为空
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_017");
#endif

    // 事务提交
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 018.定义树root1(NP)、root2(P)，root1定义带默认值字段F1定义when校验依赖root2，事务1里创建root1编辑F1，事务2里when条件不满足
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(acrossTreeVal, Yang_097_acrossTreeVal_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabel16.gmjson", "schemaBasicFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabelB16.gmjson", "schemaBasicFunc/SubTreeEdgelLabelB.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F5");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 事务提交
    TransCommit(g_conn_async);

    // subtree查询 obj，为空
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_018");
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);
    nDmlCnt = 0;

#ifndef FEATURE_PERSISTENCE
    // create savepoint
    CreateSavepoint(g_conn_async, g_savepointName);
#endif

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne_B", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 200;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F5");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/F7";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffBasicFunc/diff018.json", "diffBasicFunc/diff018_B.json");
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_018_2");

#ifndef FEATURE_PERSISTENCE
    // Rollback savepoint  diff为空
    RollbackSavepoint(g_conn_async, g_savepointName);
    ClearDiffFile();
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj，为空
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_018");
#endif

    // 事务提交
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 019.定义树root1(NP)、root2(P)，root1定义带默认值字段F1定义when校验依赖root2，事务1里创建创建root1编辑F1，事务2里when条件满足
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(acrossTreeVal, Yang_097_acrossTreeVal_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabel16.gmjson", "schemaBasicFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabelB16.gmjson", "schemaBasicFunc/SubTreeEdgelLabelB.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F5");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 事务提交
    TransCommit(g_conn_async);

    // subtree查询 obj，为空
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_019");
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);
    nDmlCnt = 0;

#ifndef FEATURE_PERSISTENCE
    // create savepoint
    CreateSavepoint(g_conn_async, g_savepointName);
#endif

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne_B", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F5");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/F7";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffBasicFunc/diff019.json", NULL);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_019");

#ifndef FEATURE_PERSISTENCE
    // Rollback savepoint  diff为空
    RollbackSavepoint(g_conn_async, g_savepointName);
    ClearDiffFile();
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj，为空
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_019");
#endif

    // 事务提交
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 020.定义树root1(NP)、root2(P)，root1定义带default case上定义when校验依赖root2，when删除后，新开事务，when满足条件
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(acrossTreeVal, Yang_097_acrossTreeVal_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabel16.gmjson", "schemaBasicFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabelB16.gmjson", "schemaBasicFunc/SubTreeEdgelLabelB.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MUST;
    dataLef.expectedErrMsg = "violated must clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/F6";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_020");

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

#ifndef FEATURE_PERSISTENCE
    // create savepoint
    CreateSavepoint(g_conn_async, g_savepointName);
#endif

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne_B", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 5555;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F5");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    memset(&dataLef, 0, sizeof(dataLef));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/F7";

    checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffBasicFunc/diff020_B.json", "diffBasicFunc/diff020.json");
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_020_2");

#ifndef FEATURE_PERSISTENCE
    // Rollback savepoint  diff为空
    RollbackSavepoint(g_conn_async, g_savepointName);
    ClearDiffFile();
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_020");
#endif

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 021.定义树root1(NP)、root2(P)，root1定义带default case上默认值字段定义when校验依赖root2，when删除后，新开事务，when满足条件
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(acrossTreeVal, Yang_097_acrossTreeVal_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabel21.gmjson", "schemaBasicFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabelB16.gmjson", "schemaBasicFunc/SubTreeEdgelLabelB.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MUST;
    dataLef.expectedErrMsg = "violated must clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/F6";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_021");

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

#ifndef FEATURE_PERSISTENCE
    // create savepoint
    CreateSavepoint(g_conn_async, g_savepointName);
#endif

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne_B", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 300;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F5");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    memset(&dataLef, 0, sizeof(dataLef));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/F7";

    checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffBasicFunc/diff021_B.json", "diffBasicFunc/diff021.json");
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_021_2");

#ifndef FEATURE_PERSISTENCE
    // Rollback savepoint  diff为空
    RollbackSavepoint(g_conn_async, g_savepointName);
    ClearDiffFile();
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_021");
#endif

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 022.定义树root1(NP)、root2(P)，root1定义带default case上默认值字段定义when校验依赖root2，when满足后，新开事务，when不满足条件
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(acrossTreeVal, Yang_097_acrossTreeVal_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabel22.gmjson", "schemaBasicFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabelB16.gmjson", "schemaBasicFunc/SubTreeEdgelLabelB.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceCaseNode, g_caseContainerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_caseContainerNode2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_caseContainerNode2, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_022");

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);
    nDmlCnt = 0;

#ifndef FEATURE_PERSISTENCE
    // create savepoint
    CreateSavepoint(g_conn_async, g_savepointName);
#endif

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne_B", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 800;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F5");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MUST;
    dataLef.expectedErrMsg = "violated must clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne[F0=\"str00101\"]/F6";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffBasicFunc/diff022_B.json", "diffBasicFunc/diff022.json");
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_022_2");

#ifndef FEATURE_PERSISTENCE
    // Rollback savepoint  diff为空
    RollbackSavepoint(g_conn_async, g_savepointName);
    ClearDiffFile();
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_022");
#endif

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 017.定义树root1(NP)、root2(P)，root1定义带默认值字段F1定义when校验依赖root2，编辑root2，使when条件满足
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(acrossTreeVal, Yang_097_acrossTreeVal_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabel16.gmjson", "schemaBasicFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabelB16.gmjson", "schemaBasicFunc/SubTreeEdgelLabelB.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

#ifndef FEATURE_PERSISTENCE
    // create savepoint
    CreateSavepoint(g_conn_async, g_savepointName);
#endif

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne_B", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 500;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F5");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F6");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F7");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/F7";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffBasicFunc/diff023_B.json", "diffBasicFunc/diff023.json");
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_023");

#ifndef FEATURE_PERSISTENCE
    // Rollback savepoint  diff为空
    RollbackSavepoint(g_conn_async, g_savepointName);
    ClearDiffFile();
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj，为空
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_023_Sp");
#endif

    // 事务提交
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 024.定义树root1(P)、root2(P)，root1定义带默认值字段F1定义when校验依赖root2，不预置数据，数据校验
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(acrossTreeVal, Yang_097_acrossTreeVal_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabel23.gmjson", "schemaBasicFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabelB16.gmjson", "schemaBasicFunc/SubTreeEdgelLabelB.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/F7";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_024");

    // 事务提交
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 025.定义树root1(P)、root2(P)，root1定义带默认值字段F1定义when校验依赖root2，预置创建root1不编辑F1，数据校验
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(acrossTreeVal, Yang_097_acrossTreeVal_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabel23.gmjson", "schemaBasicFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabelB16.gmjson", "schemaBasicFunc/SubTreeEdgelLabelB.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_025");

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);
    nDmlCnt = 0;

#ifndef FEATURE_PERSISTENCE
    // create savepoint
    CreateSavepoint(g_conn_async, g_savepointName);
#endif

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MUST;
    dataLef.expectedErrMsg = "violated must clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne[F0=\"str00101\"]/F6";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // subtree查询 obj，没有开启diff，无法查询diff
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_025_2");

#ifndef FEATURE_PERSISTENCE
    // Rollback savepoint  diff为空，无法查询diff,报错1004000
    AsyncUserDataT data = {0};
    RollbackSavepoint(g_conn_async, g_savepointName);

    // subtree查询 obj，没有开启diff
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_025_Sp");
#endif

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 026.定义树root1(P)、root2(P)，root1定义带默认值字段F1定义when校验依赖root2，预置创建root1、root2不编辑默认值字段，when条件满足
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(acrossTreeVal, Yang_097_acrossTreeVal_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabel25.gmjson", "schemaBasicFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabelB16.gmjson", "schemaBasicFunc/SubTreeEdgelLabelB.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne_B", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_026");

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);
    nDmlCnt = 0;

#ifndef FEATURE_PERSISTENCE
    // create savepoint
    CreateSavepoint(g_conn_async, g_savepointName);
#endif

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MUST;
    dataLef.expectedErrMsg = "violated must clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne[F0=\"str00101\"]/F6";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // subtree查询 obj，没有开启diff，无法查询diff
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_026");

#ifndef FEATURE_PERSISTENCE
    // Rollback savepoint  diff为空，无法查询diff,报错1004000
    AsyncUserDataT data = {0};
    RollbackSavepoint(g_conn_async, g_savepointName);

    // subtree查询 obj，没有开启diff
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_026");
#endif

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 027.定义树root1(P)、root2(P)，root1定义带默认值字段F1定义when校验依赖root2，创建root2，when条件不满足
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(acrossTreeVal, Yang_097_acrossTreeVal_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabel23.gmjson", "schemaBasicFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabelB16.gmjson", "schemaBasicFunc/SubTreeEdgelLabelB.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

#ifndef FEATURE_PERSISTENCE
    // create savepoint
    CreateSavepoint(g_conn_async, g_savepointName);
#endif

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne_B", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MUST;
    dataLef.expectedErrMsg = "violated must clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne[F0=\"str00101\"]/F6";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffBasicFunc/diff027_B.json", NULL);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj，开启diff
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_027");
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne_B", "Yang_097_acrossTreeVal_027_B");

#ifndef FEATURE_PERSISTENCE
    // Rollback savepoint,diff为空
    RollbackSavepoint(g_conn_async, g_savepointName);
    ClearDiffFile();
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj,均为空
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_027");
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne_B", "Yang_097_acrossTreeVal_027");
#endif

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOne -defaultMode REPORT_ALL");
    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOne_B -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 028.定义树root1(P)、root2(P)，root1定义带默认值字段F1定义when校验依赖root2，创建root2，when条件满足
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(acrossTreeVal, Yang_097_acrossTreeVal_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabel23.gmjson", "schemaBasicFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabelB16.gmjson", "schemaBasicFunc/SubTreeEdgelLabelB.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

#ifndef FEATURE_PERSISTENCE
    // create savepoint
    CreateSavepoint(g_conn_async, g_savepointName);
#endif

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne_B", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 5555;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F5");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MUST;
    dataLef.expectedErrMsg = "violated must clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne[F0=\"str00101\"]/F6";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffBasicFunc/diff028_B.json", NULL);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj，开启diff
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_028");
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne_B", "Yang_097_acrossTreeVal_028_B");

#ifndef FEATURE_PERSISTENCE
    // Rollback savepoint,diff为空
    RollbackSavepoint(g_conn_async, g_savepointName);
    ClearDiffFile();
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj,均为空
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_028");
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne_B", "Yang_097_acrossTreeVal_028");
#endif

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOne -defaultMode REPORT_ALL");
    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOne_B -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 029.定义树root1(P)、root2(P)，root1定义带默认值字段F1定义when校验依赖root2，预置创建root1编辑F1，数据校验
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(acrossTreeVal, Yang_097_acrossTreeVal_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabel23.gmjson", "schemaBasicFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabelB16.gmjson", "schemaBasicFunc/SubTreeEdgelLabelB.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F5");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne_B", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_029");

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);
    nDmlCnt = 0;

#ifndef FEATURE_PERSISTENCE
    // create savepoint
    CreateSavepoint(g_conn_async, g_savepointName);
#endif

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MUST;
    dataLef.expectedErrMsg = "violated must clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne[F0=\"str00101\"]/F6";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // subtree查询 obj，没有开启diff，无法查询diff
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_029_2");

#ifndef FEATURE_PERSISTENCE
    // Rollback savepoint  diff为空，无法查询diff,报错1004000
    AsyncUserDataT data = {0};
    RollbackSavepoint(g_conn_async, g_savepointName);

    // subtree查询 obj，没有开启diff
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_029");
#endif

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 030.定义树root1(P)、root2(P)，root1定义带默认值字段F1定义when校验依赖root2，预置创建root1编辑F1，创建root2，when条件满足
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(acrossTreeVal, Yang_097_acrossTreeVal_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabel23.gmjson", "schemaBasicFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabelB16.gmjson", "schemaBasicFunc/SubTreeEdgelLabelB.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F5");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne_B", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F5");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_030");

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);
    nDmlCnt = 0;

#ifndef FEATURE_PERSISTENCE
    // create savepoint
    CreateSavepoint(g_conn_async, g_savepointName);
#endif

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MUST;
    dataLef.expectedErrMsg = "violated must clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne[F0=\"str00101\"]/F6";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // subtree查询 obj，没有开启diff，无法查询diff
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_030");

#ifndef FEATURE_PERSISTENCE
    // Rollback savepoint  diff为空，无法查询diff,报错1004000
    AsyncUserDataT data = {0};
    RollbackSavepoint(g_conn_async, g_savepointName);

    // subtree查询 obj，没有开启diff
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_030");
#endif

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 031.定义树root1(P)、root2(P)，root1定义带默认值字段F1定义when校验依赖root2，预置创建root1不编辑F1，事务1里when删除F1，事务2when条件满足
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(acrossTreeVal, Yang_097_acrossTreeVal_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabel23.gmjson", "schemaBasicFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabelB16.gmjson", "schemaBasicFunc/SubTreeEdgelLabelB.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne_B", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MUST;
    dataLef.expectedErrMsg = "violated must clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne[F0=\"str00101\"]/F6";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // subtree查询 obj,when删除后查询为空
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_031");

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);
    nDmlCnt = 0;

#ifndef FEATURE_PERSISTENCE
    // create savepoint
    CreateSavepoint(g_conn_async, g_savepointName);
#endif

    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne_B", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 5555;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F5");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    memset(&dataLef, 0, sizeof(dataLef));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MUST;
    dataLef.expectedErrMsg = "violated must clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne[F0=\"str00101\"]/F6";

    checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffBasicFunc/diff031_B.json", "diffBasicFunc/diff031.json");
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_031_2");

#ifndef FEATURE_PERSISTENCE
    // Rollback savepoint  diff为空
    RollbackSavepoint(g_conn_async, g_savepointName);
    ClearDiffFile();
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_031");
#endif

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 032.定义树root1(NP)-leaflist、root2(NP)-leaflist，root1-leaflist字段F1定义默认值，F1定义when校验依赖root2-leaflist，不预置数据，when条件满足
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(acrossTreeVal, Yang_097_acrossTreeVal_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabel32.gmjson", "schemaBasicFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabelB32.gmjson", "schemaBasicFunc/SubTreeEdgelLabelB.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/F7";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_032");

    // 事务提交
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 033.定义树root1(NP)-leaflist、root2(NP)-leaflist，root1-leaflist字段F1定义默认值，F1定义when校验依赖root2-leaflist，不预置数据，when条件不满足
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(acrossTreeVal, Yang_097_acrossTreeVal_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabel32.gmjson", "schemaBasicFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabelB33.gmjson", "schemaBasicFunc/SubTreeEdgelLabelB.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/F7";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_033");

    // 事务提交
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 034.定义树root1(NP)-leaflist、root2(NP)-leaflist，root1-leaflist字段F1定义默认值，F1定义when校验依赖root2-leaflist，事务1里root1的leaflist被when删除，事务2里when条件满足
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(acrossTreeVal, Yang_097_acrossTreeVal_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabel32.gmjson", "schemaBasicFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabelB33.gmjson", "schemaBasicFunc/SubTreeEdgelLabelB.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/F7";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

#ifndef FEATURE_PERSISTENCE
    // create savepoint
    CreateSavepoint(g_conn_async, g_savepointName);
#endif

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_034");

#ifndef FEATURE_PERSISTENCE
    // Rollback savepoint 
    RollbackSavepoint(g_conn_async, g_savepointName);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_034");
#endif

    // 事务提交
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

#ifndef FEATURE_PERSISTENCE
    // create savepoint
    CreateSavepoint(g_conn_async, g_savepointName);
#endif

    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne_B", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 这里需要prepar list的labelname
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_LeafList, "LeafList_B", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_LeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_LeafList, &g_vertexLabelT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    uint32_t fieldValue = 555;
    testYangSetVertexProperty_F0(g_vertexLabelT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_LeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    memset(&dataLef, 0, sizeof(dataLef));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MUST;
    dataLef.expectedErrMsg = "violated must clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne[F0=\"str00101\"]/F6";

    checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffBasicFunc/diff034_B.json", "diffBasicFunc/diff034.json");
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_034_2");

#ifndef FEATURE_PERSISTENCE
    // Rollback savepoint  diff为空
    RollbackSavepoint(g_conn_async, g_savepointName);
    ClearDiffFile();
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_034");
#endif

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 035.定义树root1(NP)-list、root2(NP)-list，root1-list字段F1定义默认值，F1定义when校验依赖root2-list，不预置数据，数据校验
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(acrossTreeVal, Yang_097_acrossTreeVal_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabel35.gmjson", "schemaBasicFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabelB35.gmjson", "schemaBasicFunc/SubTreeEdgelLabelB.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

#ifndef FEATURE_PERSISTENCE
    // create savepoint
    CreateSavepoint(g_conn_async, g_savepointName);
#endif

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne_B", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 102; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, "ListOne_B", GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/F7";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffBasicFunc/diff035_B.json", NULL);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_035");

#ifndef FEATURE_PERSISTENCE
    // Rollback savepoint  diff为空
    RollbackSavepoint(g_conn_async, g_savepointName);
    ClearDiffFile();
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_035");
#endif

    // 事务提交
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 036.定义树root1(NP)-list、root2(NP)-list，root1-list字段F1定义默认值，F1定义when校验依赖root2-list，预置创建root1-list、root2-list，不编辑默认值字段，when条件不满足
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(acrossTreeVal, Yang_097_acrossTreeVal_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabel35.gmjson", "schemaBasicFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabelB35.gmjson", "schemaBasicFunc/SubTreeEdgelLabelB.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 102; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, "ListOne", GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne_B", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 102; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, "ListOne_B", GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_036");

    // 事务提交
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);
    nDmlCnt = 0;

#ifndef FEATURE_PERSISTENCE
    // create savepoint
    CreateSavepoint(g_conn_async, g_savepointName);
#endif

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne_B", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 102; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, "ListOne_B", GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t fieldValue = i + 100;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/F7";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffBasicFunc/diff036_B.json", "diffBasicFunc/diff036.json");
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_036_2");

#ifndef FEATURE_PERSISTENCE
    // Rollback savepoint  diff为空
    RollbackSavepoint(g_conn_async, g_savepointName);
    ClearDiffFile();
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_036");
#endif

    // 事务提交
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 037.定义树root1(NP)-list-leaflist、root2(NP)-list-leaflist，root1-list-leaflist字段F1定义默认值，F1定义when校验依赖root2-list-leaflist，预置创建root1-list、root2-list，不编辑默认值字段，when条件不满足
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(acrossTreeVal, Yang_097_acrossTreeVal_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabel37.gmjson", "schemaBasicFunc/SubTreeEdgelLabel37.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabelB37.gmjson", "schemaBasicFunc/SubTreeEdgelLabelB37.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 102; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, "ListOne", GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne_B", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 102; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, "ListOne_B", GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_037");
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne_B", "Yang_097_acrossTreeVal_037_B");
    // 事务提交
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);
    nDmlCnt = 0;

#ifndef FEATURE_PERSISTENCE
    // create savepoint
    CreateSavepoint(g_conn_async, g_savepointName);
#endif

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/F7";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_037_2");

#ifndef FEATURE_PERSISTENCE
    // Rollback savepoint 
    RollbackSavepoint(g_conn_async, g_savepointName);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_037");
#endif

    // 事务提交
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOne_B -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 038.定义树root1(NP)-list-leaflist、root2(NP)-list-leaflist，root1-list-leaflist字段F1定义默认值，F1定义when校验依赖root2-list-leaflist，预置创建root1-list、root2-list，不编辑默认值字段，when条件满足
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(acrossTreeVal, Yang_097_acrossTreeVal_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabel37.gmjson", "schemaBasicFunc/SubTreeEdgelLabel37.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabelB37.gmjson", "schemaBasicFunc/SubTreeEdgelLabelB37.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 102; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, "ListOne", GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne_B", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 102; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, "ListOne_B", GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_038");
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne_B", "Yang_097_acrossTreeVal_038_B");

    // 事务提交
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);
    nDmlCnt = 0;

#ifndef FEATURE_PERSISTENCE
    // create savepoint
    CreateSavepoint(g_conn_async, g_savepointName);
#endif

   // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne_B", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 这里需要prepar list的labelname
    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, "ListOne_B", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  --ListOne
    char fieldStr[15] = {0};
    snprintf(fieldStr, sizeof(fieldStr), "str00%d", 55);
    ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 这里需要prepar list的labelname
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_LeafList, "LeafList_B", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_LeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_LeafList, &g_vertexLabelT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    uint32_t fieldValue = 55;
    testYangSetVertexProperty_F0(g_vertexLabelT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_LeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/F7";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_038");

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffBasicFunc/diff038_B.json", NULL);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_038");

#ifndef FEATURE_PERSISTENCE
    // Rollback savepoint  diff为空
    RollbackSavepoint(g_conn_async, g_savepointName);
    ClearDiffFile();
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_097_acrossTreeVal_038");
#endif

    // 事务提交
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOne_B -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 039.定义10颗树root1-root10上定义when、must、leafref、依赖root11字段，when、must、leafref条件均满足
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(acrossTreeVal, Yang_097_acrossTreeVal_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabel37.gmjson", "schemaBasicFunc/SubTreeEdgelLabel37.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabelB37.gmjson", "schemaBasicFunc/SubTreeEdgelLabelB37.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char tVertexName[128] = {0};
    char tEdgeName[128] = {0};
    AW_FUN_Log(LOG_INFO, ">>> create 11 vertex and edge.");
    for (int i = 1; i <= 11; i++) {
        (void)snprintf(tVertexName, 128, "multi_vertexlabel10/SubTreeVertexLabel_%d.gmjson", i);
        (void)snprintf(tEdgeName, 128, "multi_vertexlabel10/SubTreeEdgelLabel_%d.gmjson", i);
        ret = CreateVertexAndEdge(tVertexName, tEdgeName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/F7";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOneB_1", "Yang_097_acrossTreeVal_039");
    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOneB_10", "Yang_097_acrossTreeVal_039");

    // 事务提交
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOneB_8 -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 040.定义10颗树root1-root10上定义when、must、leafref、依赖root11字段，when、must、leafref条件均不满足
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(acrossTreeVal, Yang_097_acrossTreeVal_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabel37.gmjson", "schemaBasicFunc/SubTreeEdgelLabel37.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabelB37.gmjson", "schemaBasicFunc/SubTreeEdgelLabelB37.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char tVertexName[128] = {0};
    char tEdgeName[128] = {0};
    AW_FUN_Log(LOG_INFO, ">>> create 11 vertex and edge.");
    for (int i = 1; i <= 11; i++) {
        (void)snprintf(tVertexName, 128, "multi_vertexlabel10/SubTreeVertexLabel_%d.gmjson", i);
        (void)snprintf(tEdgeName, 128, "multi_vertexlabel10/SubTreeEdgelLabel_%d.gmjson", i);
        ret = CreateVertexAndEdge(tVertexName, tEdgeName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

#ifndef FEATURE_PERSISTENCE
    // create savepoint
    CreateSavepoint(g_conn_async, g_savepointName);
#endif

   // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOneB_11", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t fieldValue = 55;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F2");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F3");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F4");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MUST;
    dataLef.expectedErrMsg = "violated must clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOneB_1/F2";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOneB_8", "Yang_097_acrossTreeVal_040");

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffBasicFunc/diff040_B1.json", NULL);
    for (int i = 2; i < 12; i++) {
        char fieldPath[128] = {0};
        snprintf(fieldPath, sizeof(fieldPath), "diffBasicFunc/diff040_B%d.json", i);
        AddDiffFile(fieldPath);
    }
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

#ifndef FEATURE_PERSISTENCE
    // Rollback savepoint  diff为空
    RollbackSavepoint(g_conn_async, g_savepointName);
    ClearDiffFile();
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOneB_8", "Yang_097_acrossTreeVal_040_Sp");
#endif

    // 事务提交
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOneB_8 -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 041.定义10颗树root1-root10上定义when、must、leafref、依赖root11字段，when、leafref条件均不满足，must满足
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(acrossTreeVal, Yang_097_acrossTreeVal_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabel37.gmjson", "schemaBasicFunc/SubTreeEdgelLabel37.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabelB37.gmjson", "schemaBasicFunc/SubTreeEdgelLabelB37.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char tVertexName[128] = {0};
    char tEdgeName[128] = {0};
    AW_FUN_Log(LOG_INFO, ">>> create 11 vertex and edge.");
    for (int i = 1; i <= 11; i++) {
        (void)snprintf(tVertexName, 128, "multi_vertexlabel10/SubTreeVertexLabel_%d.gmjson", i);
        (void)snprintf(tEdgeName, 128, "multi_vertexlabel10/SubTreeEdgelLabel_%d.gmjson", i);
        ret = CreateVertexAndEdge(tVertexName, tEdgeName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

#ifndef FEATURE_PERSISTENCE
    // create savepoint
    CreateSavepoint(g_conn_async, g_savepointName);
#endif

   // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOneB_11", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t fieldValue = 101;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F2");
    fieldValue = 55;
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F3");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOneB_1/F1";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOneB_8", "Yang_097_acrossTreeVal_040");

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffBasicFunc/diff041_B1.json", NULL);
    for (int i = 2; i < 12; i++) {
        char fieldPath[128] = {0};
        snprintf(fieldPath, sizeof(fieldPath), "diffBasicFunc/diff041_B%d.json", i);
        AddDiffFile(fieldPath);
    }
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

#ifndef FEATURE_PERSISTENCE
    // Rollback savepoint  diff为空
    RollbackSavepoint(g_conn_async, g_savepointName);
    ClearDiffFile();
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOneB_8", "Yang_097_acrossTreeVal_040_Sp");
#endif

    // 事务提交
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOneB_11 -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 042.定义10颗树root1-root10,when、must、leafref、循环依赖，when、must、leafref条件均不满足
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(acrossTreeVal, Yang_097_acrossTreeVal_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabel37.gmjson", "schemaBasicFunc/SubTreeEdgelLabel37.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabelB37.gmjson", "schemaBasicFunc/SubTreeEdgelLabelB37.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char tVertexName[128] = {0};
    char tEdgeName[128] = {0};
    AW_FUN_Log(LOG_INFO, ">>> create 11 vertex and edge.");
    for (int i = 1; i <= 10; i++) {
        (void)snprintf(tVertexName, 128, "multi_vertexlabel10cycle/SubTreeVertexLabel_%d.gmjson", i);
        (void)snprintf(tEdgeName, 128, "multi_vertexlabel10cycle/SubTreeEdgelLabel_%d.gmjson", i);
        ret = CreateVertexAndEdge(tVertexName, tEdgeName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

#ifndef FEATURE_PERSISTENCE
    // create savepoint
    CreateSavepoint(g_conn_async, g_savepointName);
#endif

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MUST;
    dataLef.expectedErrMsg = "violated must clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOneB_1/F2";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOneB_1", "Yang_097_acrossTreeVal_042_B1");
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOneB_10", "Yang_097_acrossTreeVal_042_B10");

#ifndef FEATURE_PERSISTENCE
    // Rollback savepoint  diff为空
    RollbackSavepoint(g_conn_async, g_savepointName);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOneB_1", "Yang_097_acrossTreeVal_042_B1_Sp");
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOneB_10", "Yang_097_acrossTreeVal_042_B10_Sp");
#endif

    // 事务提交
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOneB_10 -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 043.定义10颗树root1-root10,when、must、leafref、循环依赖，when、leafref条件均不满足,must满足
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(acrossTreeVal, Yang_097_acrossTreeVal_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabel37.gmjson", "schemaBasicFunc/SubTreeEdgelLabel37.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabelB37.gmjson", "schemaBasicFunc/SubTreeEdgelLabelB37.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char tVertexName[128] = {0};
    char tEdgeName[128] = {0};
    AW_FUN_Log(LOG_INFO, ">>> create 11 vertex and edge.");
    for (int i = 1; i <= 10; i++) {
        (void)snprintf(tVertexName, 128, "multi_vertexlabel10cycle/SubTreeVertexLabel_%d.gmjson", i);
        (void)snprintf(tEdgeName, 128, "multi_vertexlabel10cycle/SubTreeEdgelLabel_%d.gmjson", i);
        ret = CreateVertexAndEdge(tVertexName, tEdgeName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

#ifndef FEATURE_PERSISTENCE
    // create savepoint
    CreateSavepoint(g_conn_async, g_savepointName);
#endif

   // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 1; i < 11; i++) {
        memset(tVertexName, 0, sizeof(tVertexName));
        (void)snprintf(tVertexName, 128, "ContainerOneB_%d", i);

        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, tVertexName, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t fieldValue = 200 + i;
        testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
        fieldValue = 201;
        testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F2");
        testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F3");

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOneB_1/F1";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffBasicFunc/diff043_B1.json", NULL);
    for (int i = 2; i < 11; i++) {
        char fieldPath[128] = {0};
        snprintf(fieldPath, sizeof(fieldPath), "diffBasicFunc/diff043_B%d.json", i);
        AddDiffFile(fieldPath);
    }
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOneB_1", "Yang_097_acrossTreeVal_043_B1");
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOneB_10", "Yang_097_acrossTreeVal_043_B10");

#ifndef FEATURE_PERSISTENCE
    // Rollback savepoint  diff为空
    RollbackSavepoint(g_conn_async, g_savepointName);
    ClearDiffFile();
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffreply, data);
    ReleaseDiffFile();

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOneB_1", "Yang_097_acrossTreeVal_042_B1_Sp");
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOneB_10", "Yang_097_acrossTreeVal_042_B10_Sp");
#endif

    // 事务提交
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOneB_10 -defaultMode REPORT_ALL");
    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOneB_1 -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 044.定义10颗树root1-root10,when、must、leafref、循环依赖，when、must满足、leafref条件均满足，全量数据校验
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(acrossTreeVal, Yang_097_acrossTreeVal_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabel37.gmjson", "schemaBasicFunc/SubTreeEdgelLabel37.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabelB37.gmjson", "schemaBasicFunc/SubTreeEdgelLabelB37.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char tVertexName[128] = {0};
    char tEdgeName[128] = {0};
    AW_FUN_Log(LOG_INFO, ">>> create 11 vertex and edge.");
    for (int i = 1; i <= 10; i++) {
        (void)snprintf(tVertexName, 128, "multi_vertexlabel10cycle/SubTreeVertexLabel_%d.gmjson", i);
        (void)snprintf(tEdgeName, 128, "multi_vertexlabel10cycle/SubTreeEdgelLabel_%d.gmjson", i);
        ret = CreateVertexAndEdge(tVertexName, tEdgeName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

#ifndef FEATURE_PERSISTENCE
    // create savepoint
    CreateSavepoint(g_conn_async, g_savepointName);
#endif

   // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 1; i < 11; i++) {
        memset(tVertexName, 0, sizeof(tVertexName));
        (void)snprintf(tVertexName, 128, "ContainerOneB_%d", i);

        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, tVertexName, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t fieldValue = 200;
        testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
        testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F2");
        testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F3");

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOneB_1/F1";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOneB_1", "Yang_097_acrossTreeVal_044_B1");
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOneB_10", "Yang_097_acrossTreeVal_044_B10");

#ifndef FEATURE_PERSISTENCE
    // Rollback savepoint  diff为空
    RollbackSavepoint(g_conn_async, g_savepointName);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOneB_1", "Yang_097_acrossTreeVal_044_B1_Sp");
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOneB_10", "Yang_097_acrossTreeVal_044_B10_Sp");
#endif

    // 事务提交
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOneB_10 -defaultMode REPORT_ALL");
    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOneB_1 -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 045.定义10颗树root1-root10上定义when、must、leafref、依赖root11字段，全量数据校验，when失败，must成功、learref失败
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(acrossTreeVal, Yang_097_acrossTreeVal_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabel37.gmjson", "schemaBasicFunc/SubTreeEdgelLabel37.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabelB37.gmjson", "schemaBasicFunc/SubTreeEdgelLabelB37.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char tVertexName[128] = {0};
    char tEdgeName[128] = {0};
    AW_FUN_Log(LOG_INFO, ">>> create 11 vertex and edge.");
    for (int i = 1; i <= 10; i++) {
        (void)snprintf(tVertexName, 128, "multi_vertexlabel10cycle/SubTreeVertexLabel_%d.gmjson", i);
        (void)snprintf(tEdgeName, 128, "multi_vertexlabel10cycle/SubTreeEdgelLabel_%d.gmjson", i);
        ret = CreateVertexAndEdge(tVertexName, tEdgeName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

#ifndef FEATURE_PERSISTENCE
    // create savepoint
    CreateSavepoint(g_conn_async, g_savepointName);
#endif

   // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 1; i < 11; i++) {
        memset(tVertexName, 0, sizeof(tVertexName));
        (void)snprintf(tVertexName, 128, "ContainerOneB_%d", i);

        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, tVertexName, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t fieldValue = 200 + i;
        testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
        testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F3");
        fieldValue = 200;
        testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F2");

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOneB_1/F1";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOneB_1", "Yang_097_acrossTreeVal_045_B1");
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOneB_10", "Yang_097_acrossTreeVal_045_B10");

#ifndef FEATURE_PERSISTENCE
    // Rollback savepoint  diff为空
    RollbackSavepoint(g_conn_async, g_savepointName);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOneB_1", "Yang_097_acrossTreeVal_045_B1_Sp");
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOneB_10", "Yang_097_acrossTreeVal_045_B10_Sp");
#endif

    // 事务提交
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOneB_10 -defaultMode REPORT_ALL");
    system("gmsysview subtree -ns acrossTreeVal -rn ContainerOneB_1 -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 046.定义10颗树root1-root10上定义when、must、leafref、依赖root11字段，导入导出
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(acrossTreeVal, Yang_097_acrossTreeVal_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabel37.gmjson", "schemaBasicFunc/SubTreeEdgelLabel37.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = CreateVertexAndEdge(
        "schemaBasicFunc/SubTreeVertexLabelB37.gmjson", "schemaBasicFunc/SubTreeEdgelLabelB37.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char tVertexName[128] = {0};
    char tEdgeName[128] = {0};
    AW_FUN_Log(LOG_INFO, ">>> create 11 vertex and edge.");
    for (int i = 1; i <= 10; i++) {
        (void)snprintf(tVertexName, 128, "multi_vertexlabel10cycle/SubTreeVertexLabel_%d.gmjson", i);
        (void)snprintf(tEdgeName, 128, "multi_vertexlabel10cycle/SubTreeEdgelLabel_%d.gmjson", i);
        ret = CreateVertexAndEdge(tVertexName, tEdgeName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOneB_1", "Yang_097_acrossTreeVal_045_B1_Sp");
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOneB_10", "Yang_097_acrossTreeVal_045_B10_Sp");
    TransCommit(g_conn_async);

#ifndef FEATURE_PERSISTENCE  //光启不支持导入导出
    system("mkdir -p /root/data/");

    // 导出之前切换到需要导出的nsp
    ret = useNameSpaceAsync(g_stmt_async, g_nspName01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};

    // 导出配置设置
    GmcPersistenceConfigT exportConfig =
        GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_NONE, FILEPATH, NULL);
    // 普通算法，只含点表的导入导出
    ret = GmcYangExportDataAsync(g_stmt_async, &exportConfig, ConfigExportCallback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    testClearNsp(g_stmt_async, g_nspName01);
    dropNameSpaceAsync(g_stmt_async, g_nspName01);
    createNameSpaceAsync(g_stmt_async, g_nspName02, "abc");

    AW_FUN_Log(LOG_INFO, "导入另一个nsp");
    ret = useNameSpaceAsync(g_stmt_async, g_nspName02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangImportDataAsync(g_stmt_async, &exportConfig, ConfigImportCallback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOneB_1", "Yang_097_acrossTreeVal_045_B1_Sp");
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOneB_10", "Yang_097_acrossTreeVal_045_B10_Sp");
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // create savepoint
    CreateSavepoint(g_conn_async, g_savepointName);

   // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 1; i < 11; i++) {
        memset(tVertexName, 0, sizeof(tVertexName));
        (void)snprintf(tVertexName, 128, "ContainerOneB_%d", i);

        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, tVertexName, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t fieldValue = 200 + i;
        testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
        testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F3");
        fieldValue = 200;
        testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F2");

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOneB_1/F1";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOneB_1", "Yang_097_acrossTreeVal_045_B1");
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOneB_10", "Yang_097_acrossTreeVal_045_B10");

    // Rollback savepoint  diff为空
    RollbackSavepoint(g_conn_async, g_savepointName);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOneB_1", "Yang_097_acrossTreeVal_045_B1_Sp");
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOneB_10", "Yang_097_acrossTreeVal_045_B10_Sp");

    // 事务提交
    TransCommit(g_conn_async);

    testClearNsp(g_stmt_async, g_nspName02);
    dropNameSpaceAsync(g_stmt_async, g_nspName02);

    system("rm -rf /root/data/");
#endif

    AW_FUN_Log(LOG_STEP, "END");
}
