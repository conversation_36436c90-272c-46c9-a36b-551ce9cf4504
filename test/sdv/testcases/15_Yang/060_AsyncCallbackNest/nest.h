/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#ifndef NEST_H
#define NEST_H

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <string.h>
#include <string>
#include <atomic>
#include <vector>
#include <iostream>
#include <cmath>
#include <chrono>
#include <sys/time.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/epoll.h>
#include <pthread.h>
#include <assert.h>
#include <errno.h>
#include <stdint.h>
#include <typeinfo>
#include <limits.h>
#include "gtest/gtest.h"

#include "t_rd_sn.h"
#include "t_rd_tree.h"
#include "t_datacom_lite.h"
#include "jansson.h"

using namespace std;
GmcConnT *g_conn_sync = NULL;
GmcStmtT *g_stmt_sync = NULL;
GmcConnT *g_conn_async = NULL;
GmcStmtT *g_stmt_async = NULL;
GmcConnT *g_conn_async1 = NULL;
GmcStmtT *g_stmt_async1 = NULL;
GmcConnT *g_conn[10] = {0};
GmcStmtT *g_stmt[10] = {0};
#define MAX_NEST_COUNT 4
#define MAX_NEST_COUNT1 3
#ifdef ENV_RTOSV2X
    #define MAX_NEST_COUNT2 4
#else
    #define MAX_NEST_COUNT2 9
#endif
#define MAX_NEST_COUNT3 1023
#define MAX_NEST_COUNT4 99
#if !defined(RUN_INDEPENDENT)
#define MAX_NEST_COUNT5 1000
#define MAX_NEST_COUNT6 512
uint32_t g_num = 120;
#else
#define MAX_NEST_COUNT5 10000
#define MAX_NEST_COUNT6 2000
uint32_t g_num = 500;
#endif

const char *g_msConfig = "{\"max_record_count\" : 10000000}";
const char *g_labelconfig = "{\"max_record_count\" : 2000000, \"isFastReadUncommitted\":0, \"auto_increment\":1,"
    "\"yang_model\":1}";
GmcTxConfigT g_trxConfig;
static int g_epollFd;         // epollFd
static int g_connFd;          // connFd
static int g_timeoutTimerFd;  // timerFd
static bool isClose = false;  // 用于标记超时，强行把connFd转成timerFd传给GmcHandleEvent
static const char *g_vertexLabelSchema =
    R"([{
        "type":"record",
        "name":"TestAsync",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false},
                {"name":"F3", "type":"int32", "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"TestAsync",
                    "name":"TestAsync_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{ "unique":true}
                }
            ]
        }])";
char *vertexLabelJson[5] = {0};
char *g_edgeLabelJson[5] = {0};
void Create_Vertex_Cb_Nest(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (user_data->nestDeep < MAX_NEST_COUNT) {
            user_data->nestDeep++;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcCreateVertexLabelAsync(user_data->stmt,
                vertexLabelJson[user_data->nestDeep], NULL, Create_Vertex_Cb_Nest, user_data));
        }
    }
}
void Create_Vertex_WithName_Cb_Nest(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (status) {
            AW_MACRO_ASSERT_EQ_INT(GMERR_DUPLICATE_TABLE, status);
            GmcAsyncRequestDoneContextT context;
            context.createVertexWithNameCb = create_vertex_label_callback;
            context.userData = user_data;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcCreateVertexLabelWithNameAsync(user_data->stmt,
                vertexLabelJson[0], NULL, "vertex05", &context));
        } else {
            if (user_data->nestDeep < MAX_NEST_COUNT) {
                user_data->nestDeep++;
                char vertexName[20];
                int res = sprintf(vertexName, "vertex0%d", user_data->nestDeep);
                ASSERT_LT(0, res);
                GmcAsyncRequestDoneContextT context;
                context.createVertexWithNameCb = Create_Vertex_WithName_Cb_Nest;
                context.userData = user_data;
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcCreateVertexLabelWithNameAsync(user_data->stmt,
                    vertexLabelJson[user_data->nestDeep], NULL, vertexName, &context));
            }
        }
    }
}
void Create_Vertex_WithName_Cb_Nest1(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        if (status != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_PROGRAM_LIMIT_EXCEEDED, status);
            user_data->recvNum++;
            return;
        }
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        user_data->nestDeep++;
        char vertexName[20];
        sprintf(vertexName, "vertex0%d", user_data->nestDeep);
        GmcAsyncRequestDoneContextT context;
        context.createVertexWithNameCb = Create_Vertex_WithName_Cb_Nest1;
        context.userData = user_data;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcCreateVertexLabelWithNameAsync(user_data->stmt, g_vertexLabelSchema, NULL,
            vertexName, &context));
    }
}
void Drop_Vertex_Cb_Nest(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (user_data->nestDeep < MAX_NEST_COUNT) {
            user_data->nestDeep++;
            char vertexName[20];
            sprintf(vertexName, "Vertex0%d", user_data->nestDeep);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcDropVertexLabelAsync(user_data->stmt, vertexName,
                Drop_Vertex_Cb_Nest, user_data));
        }
    }
}
void Truncate_Vertex_Cb_Nest(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (user_data->nestDeep < MAX_NEST_COUNT) {
            user_data->nestDeep++;
            char vertexName[20];
            sprintf(vertexName, "Vertex0%d", user_data->nestDeep);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcTruncateVertexLabelAsync(user_data->stmt, vertexName,
                Truncate_Vertex_Cb_Nest, user_data));
        }
    }
}
void Replace_Vertex_Cb_Nest(void *userData, uint32_t affectedRows, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (user_data->nestDeep < MAX_NEST_COUNT4) {
            user_data->nestDeep++;
            uint32_t pk = 0 + user_data->nestDeep;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK,
                testGmcPrepareStmtByLabelName(user_data->stmt, "Vertex00", GMC_OPERATION_REPLACE));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK,
                GmcSetVertexProperty(user_data->stmt, "PK", GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t)));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK,
                GmcSetVertexProperty(user_data->stmt, "F0", GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t)));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK,
                GmcSetVertexProperty(user_data->stmt, "F1", GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t)));
            GmcAsyncRequestDoneContextT replaceRequestCtx;
            replaceRequestCtx.replaceCb = Replace_Vertex_Cb_Nest;
            replaceRequestCtx.userData = user_data;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecuteAsync(user_data->stmt, &replaceRequestCtx));
        }
    }
}
void Create_Yang_Vertex_Cb_Nest(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (user_data->nestDeep < MAX_NEST_COUNT1) {
            user_data->nestDeep++;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcCreateVertexLabelAsync(user_data->stmt,
                vertexLabelJson[user_data->nestDeep], g_labelconfig, Create_Yang_Vertex_Cb_Nest, user_data));
        }
    }
}
void Create_Yang_Edge_Cb_Nest(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (user_data->nestDeep < MAX_NEST_COUNT1) {
            user_data->nestDeep++;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcCreateEdgeLabelAsync(user_data->stmt,
                g_edgeLabelJson[user_data->nestDeep], g_labelconfig, Create_Yang_Edge_Cb_Nest, user_data));
        }
    }
}

void Drop_Yang_Edge_Cb_Nest(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (user_data->nestDeep < MAX_NEST_COUNT1) {
            user_data->nestDeep++;
            char edgeName[20];
            sprintf(edgeName, "L%d", user_data->nestDeep);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcDropEdgeLabelAsync(user_data->stmt, edgeName,
                Drop_Yang_Edge_Cb_Nest, user_data));
        }
    }
}
void Create_Kv_Cb_Nest(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (user_data->nestDeep < MAX_NEST_COUNT) {
            user_data->nestDeep++;
            char kvName[20];
            sprintf(kvName, "kv%d", user_data->nestDeep);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcKvCreateTableAsync(user_data->stmt, kvName, NULL,
                Create_Kv_Cb_Nest, user_data));
        }
    }
}
void Drop_Kv_Cb_Nest(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (user_data->nestDeep < MAX_NEST_COUNT) {
            user_data->nestDeep++;
            char kvName[20];
            sprintf(kvName, "kv%d", user_data->nestDeep);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcKvDropTableAsync(user_data->stmt, kvName,
                Drop_Kv_Cb_Nest, user_data));
        }
    }
}

void Truncate_Kv_Cb_Nest(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (user_data->nestDeep < MAX_NEST_COUNT) {
            user_data->nestDeep++;
            char kvName[20];
            sprintf(kvName, "kv%d", user_data->nestDeep);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcKvTruncateTableAsync(user_data->stmt, kvName,
                Truncate_Kv_Cb_Nest, user_data));
        }
    }
}

void Set_KV_Cb_Nest(void *userData, uint32_t affectedRows, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (user_data->nestDeep < MAX_NEST_COUNT4) {
            user_data->nestDeep++;
            GmcKvTupleT kvInfo = {0};
            int32_t value = 100 + user_data->nestDeep;
            char key[32];
            sprintf(key, "zhangsan%d", user_data->nestDeep);
            // 设置k-v值
            kvInfo.key = key;
            kvInfo.keyLen = strlen(key);
            kvInfo.value = &value;
            kvInfo.valueLen = sizeof(int32_t);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcKvPrepareStmtByLabelName(user_data->stmt, "kv0"));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcKvSetAsync(user_data->stmt, &kvInfo, Set_KV_Cb_Nest, user_data));
        }
    }
}

void Remove_KV_Cb_Nest(void *userData, uint32_t affectedRows, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (user_data->nestDeep < MAX_NEST_COUNT4) {
            user_data->nestDeep++;
            char key[32];
            sprintf(key, "zhangsan%d", user_data->nestDeep);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcKvPrepareStmtByLabelName(user_data->stmt, "kv0"));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcKvRemoveAsync(user_data->stmt, key, strlen(key),
                Remove_KV_Cb_Nest, user_data));
        }
    }
}
void Create_Namespace_Cb_Nest(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (user_data->nestDeep < MAX_NEST_COUNT) {
            user_data->nestDeep++;
            char nsName[20];
            char userName[20];
            sprintf(nsName, "test%d", user_data->nestDeep);
            sprintf(userName, "user%d", user_data->nestDeep);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcCreateNamespaceAsync(user_data->stmt, nsName, userName,
                Create_Namespace_Cb_Nest, user_data));
        }
    }
}

void Bind_NsToTsp_Cb_Nest(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (user_data->nestDeep < MAX_NEST_COUNT) {
            user_data->nestDeep++;
            char nsName[20];
            char tsName[20];
            sprintf(nsName, "test%d", user_data->nestDeep);
            sprintf(tsName, "tsp%d", user_data->nestDeep);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBindNamespaceToTableSpaceAsync(user_data->stmt, nsName, tsName,
                Bind_NsToTsp_Cb_Nest, user_data));
        }
    }
}
void Create_Namespace_WithCfg_Cb_Nest(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (user_data->nestDeep < MAX_NEST_COUNT) {
            user_data->nestDeep++;
            char nsName[20];
            sprintf(nsName, "yang%d", user_data->nestDeep);
            GmcNspCfgT yangNspCfg = {};
            yangNspCfg.tablespaceName = NULL;
            yangNspCfg.namespaceName = nsName;
            yangNspCfg.userName = "abc";
            yangNspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcCreateNamespaceWithCfgAsync(user_data->stmt, &yangNspCfg,
                Create_Namespace_WithCfg_Cb_Nest, user_data));
        }
    }
}
void Create_Tablespace_Cb_Nest(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (user_data->nestDeep < MAX_NEST_COUNT) {
            user_data->nestDeep++;
            char tsName[20];
            sprintf(tsName, "tsp%d", user_data->nestDeep);
            GmcTspCfgT tspCfg;
            tspCfg.tablespaceName = tsName;
            tspCfg.initSize = 4;
            tspCfg.stepSize = 4;
            tspCfg.maxSize = 4;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcCreateTablespaceAsync(user_data->stmt, &tspCfg,
                Create_Tablespace_Cb_Nest, user_data));
        }
    }
}

void Drop_Tablespace_Cb_Nest(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (user_data->nestDeep < MAX_NEST_COUNT) {
            user_data->nestDeep++;
            char tsName[20];
            sprintf(tsName, "tsp%d", user_data->nestDeep);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcDropTablespaceAsync(user_data->stmt, tsName,
                Drop_Tablespace_Cb_Nest, user_data));
        }
    }
}

void Use_Namespace_Cb_Nest(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (user_data->nestDeep < MAX_NEST_COUNT) {
            user_data->nestDeep++;
            char nsName[20];
            sprintf(nsName, "yang%d", user_data->nestDeep);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcUseNamespaceAsync(user_data->stmt, nsName,
                Use_Namespace_Cb_Nest, user_data));
        }
    }
}

void Drop_Namespace_Cb_Nest(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (user_data->nestDeep < MAX_NEST_COUNT) {
            user_data->nestDeep++;
            char nsName[20];
            sprintf(nsName, "yang%d", user_data->nestDeep);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcDropNamespaceAsync(user_data->stmt, nsName,
                Drop_Namespace_Cb_Nest, user_data));
        }
    }
}

void Clear_Namespace_Cb_Nest(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (user_data->nestDeep < MAX_NEST_COUNT) {
            user_data->nestDeep++;
            char nsName[20];
            sprintf(nsName, "yang%d", user_data->nestDeep);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcClearNamespaceAsync(user_data->stmt, nsName,
                Clear_Namespace_Cb_Nest, user_data));
        }
    }
}

void Truncate_Namespace_Cb_Nest(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (user_data->nestDeep < MAX_NEST_COUNT) {
            user_data->nestDeep++;
            char nsName[20];
            sprintf(nsName, "yang%d", user_data->nestDeep);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcTruncateNamespaceAsync(user_data->stmt, nsName,
                Truncate_Namespace_Cb_Nest, user_data));
        }
    }
}
static void testInitPropValue(GmcPropValueT *propValue, const char *name, GmcDataTypeE type, void *value, uint32_t size)
{
    if (propValue == NULL) {
        return;
    }
    strcpy_s(propValue->propertyName, strlen(name) + 1, name);
    propValue->type = type;
    propValue->size = size;
    propValue->value = (void *)value;
}
static int testYangSetNodePropertyObj(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    GmcPropValueT propValue = {0};
    uint32_t valueF1 = i;
    char str[1024 * 13] = {0};
    const char *p = "a";
    for (int j = 0; j < 1023 * 13; j++) {
        strcat(str, p);
    }
    testInitPropValue(&propValue, "F1", GMC_DATATYPE_UINT32, &valueF1, sizeof(valueF1));
    ret = GmcYangSetNodeProperty(node, &propValue, opType);
    if (ret) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    testInitPropValue(&propValue, "F2", GMC_DATATYPE_STRING, str, strlen(str));
    ret = GmcYangSetNodeProperty(node, &propValue, opType);
    if (ret) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    return ret;
}
static int testYangSetNodeProperty(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType, bool isList = false)
{
    int ret = 0;
    GmcPropValueT propValue = {0};
    uint32_t valueF0 = i;
    uint32_t valueF1 = i;
    char valueF2[8] = "string";
    if (!isList) {
        testInitPropValue(&propValue, "F0", GMC_DATATYPE_UINT32, &valueF0, sizeof(valueF0));
        ret = GmcYangSetNodeProperty(node, &propValue, opType);
        if (ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            testGmcGetLastError();
            return ret;
        }
    }
    testInitPropValue(&propValue, "F1", GMC_DATATYPE_UINT32, &valueF1, sizeof(valueF1));
    ret = GmcYangSetNodeProperty(node, &propValue, opType);
    if (ret) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    testInitPropValue(&propValue, "F2", GMC_DATATYPE_STRING, valueF2, strlen(valueF2));
    ret = GmcYangSetNodeProperty(node, &propValue, opType);
    if (ret) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    return ret;
}

static int testYangSetNodePropertyPk(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    GmcPropValueT propValue = {0};
    uint32_t valueF0 = i;
    testInitPropValue(&propValue, "F0", GMC_DATATYPE_UINT32, &valueF0, sizeof(valueF0));
    ret = GmcYangSetNodeProperty(node, &propValue, opType);
    if (ret) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testGmcGetLastError();
        return ret;
    }
    return ret;
}

static int testBatchPrepareAndSetDiff(GmcConnT *conn, GmcBatchT **batch, GmcBatchTypeE batchType = GMC_BATCH_YANG,
    GmcYangDiffTypeE diffType = GMC_YANG_DIFF_DELAY_READ_ON)
{
    int ret = 0;
    GmcBatchOptionT batchOption;

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBatchType(&batchOption, batchType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcYangBatchOptionSetDiffType(&batchOption, diffType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchPrepare(conn, &batchOption, batch);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }

    return ret;
}

static void insertFourLabel(GmcConnT *conn, GmcStmtT *stmt)
{
    AsyncUserDataT data = {0};
    int ret = 0;
    GmcStmtT *stmt1 = NULL;
    GmcBatchT *batch = NULL;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *rootNode1 = NULL;
    GmcNodeT *childNode = NULL;
    GmcNodeT *childNode1 = NULL;
    ret = GmcAllocStmt(conn, &stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStartAsync(conn, &g_trxConfig, trans_start_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写入表0
    ret = testGmcPrepareStmtByLabelName(stmt, "root0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    ret = testYangSetNodeProperty(rootNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置node节点
    ret = GmcYangEditChildNode(rootNode, "Con0", GMC_OPERATION_INSERT, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeProperty(childNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list
    for (uint32_t i = 0; i < 5; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt1, "l0", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt1, &rootNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(6, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(6, data.succNum);

    // 写入表1
    ret = testGmcPrepareStmtByLabelName(stmt, "root1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    ret = testYangSetNodeProperty(rootNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置node节点
    ret = GmcYangEditChildNode(rootNode, "Choice1", GMC_OPERATION_INSERT, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(childNode, "Case1", GMC_OPERATION_INSERT, &childNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeProperty(childNode1, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置leaflist
    for (uint32_t i = 0; i < 5; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt1, "l1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt1, &rootNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodePropertyPk(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(6, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(6, data.succNum);
    // 写入表2
    ret = testGmcPrepareStmtByLabelName(stmt, "root2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    ret = testYangSetNodeProperty(rootNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置node节点
    ret = GmcYangEditChildNode(rootNode, "Choice2", GMC_OPERATION_INSERT, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(childNode, "Case2", GMC_OPERATION_INSERT, &childNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeProperty(childNode1, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list
    for (uint32_t i = 0; i < 5; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt1, "l2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt1, &rootNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(6, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(6, data.succNum);

    // 写入表3
    ret = testGmcPrepareStmtByLabelName(stmt, "root3", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    ret = testYangSetNodeProperty(rootNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点
    ret = GmcYangEditChildNode(rootNode, "Con3", GMC_OPERATION_INSERT, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeProperty(childNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置leaflist
    for (uint32_t i = 0; i < 5; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt1, "l3", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt1, &rootNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodePropertyPk(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(6, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(6, data.succNum);
    ret = GmcBatchDestroy(batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

static void testYangPresetLabelAndData(GmcConnT *conn, GmcStmtT *stmt)
{
    char nsName[20];
    int ret;
    AsyncUserDataT data = {0};
    readJanssonFile("schema_file/Yang_Vertex.gmjson", &vertexLabelJson[0]);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, vertexLabelJson[0]);
    for (int i = 1; i < 4; i++) {
        char vertexPath[32];
        ret = sprintf(vertexPath, "schema_file/Yang_Vertex%d.gmjson", i);
        ASSERT_LT(0, ret);
        readJanssonFile(vertexPath, &vertexLabelJson[i]);
        AW_MACRO_ASSERT_NE_INT((void *)NULL, vertexLabelJson[i]);
    }
    readJanssonFile("schema_file/Yang_Edge.gmjson", &g_edgeLabelJson[0]);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, g_edgeLabelJson[0]);
    for (int i = 1; i < 4; i++) {
        char vertexPath[32];
        ret = sprintf(vertexPath, "schema_file/Yang_Edge%d.gmjson", i);
        ASSERT_LT(0, ret);
        readJanssonFile(vertexPath, &g_edgeLabelJson[i]);
        AW_MACRO_ASSERT_NE_INT((void *)NULL, g_edgeLabelJson[i]);
    }
    // 配置ns级别，RR+乐观事务模式
    GmcNspCfgT yangNspCfg = {};
    yangNspCfg.tablespaceName = NULL;
    yangNspCfg.userName = "abc";
    yangNspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};
    for (int i = 0; i < 5; i++) {
        ret = sprintf(nsName, "yang%d", i);
        ASSERT_LT(0, ret);
        yangNspCfg.namespaceName = nsName;
        ret = GmcCreateNamespaceWithCfgAsync(stmt, &yangNspCfg, create_namespace_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecvOneThread(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        ret = GmcUseNamespaceAsync(stmt, nsName, use_namespace_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecvOneThread(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        // 建yang的vertex表
        data.stmt = stmt;
        data.nestDeep = 0;
        ret = GmcCreateVertexLabelAsync(stmt, vertexLabelJson[0], g_labelconfig, Create_Yang_Vertex_Cb_Nest, &data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecvOneThread(&data, 4);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        // 建yang的edge表
        data.stmt = stmt;
        data.nestDeep = 0;
        ret = GmcCreateEdgeLabelAsync(stmt, g_edgeLabelJson[0], g_labelconfig, Create_Yang_Edge_Cb_Nest, &data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecvOneThread(&data, 4);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        insertFourLabel(conn, stmt);
    }
    for (int i = 0; i < 4; i++) {
        free(vertexLabelJson[i]);
        free(g_edgeLabelJson[i]);
    }
}
void Batch_Execute_Cb_Nest(void *userData, GmcBatchRetT *batchRet, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        if (status != GMERR_REQUEST_TIME_OUT) {
            int ret =
                GmcBatchDeparseRet(batchRet, (uint32_t *)&(user_data->totalNum), (uint32_t *)&(user_data->succNum));
            ASSERT_EQ(GMERR_OK, ret);
        }
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (status) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, status);
            GmcNodeT *rootNode = NULL;
            GmcBatchT *batch = NULL;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testBatchPrepareAndSetDiff(user_data->conn, &batch));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testGmcPrepareStmtByLabelName(user_data->stmt, "root0",
                GMC_OPERATION_REMOVE_GRAPH));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangSetRoot(batch, user_data->stmt));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcGetRootNode(user_data->stmt, &rootNode));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBatchAddDML(batch, user_data->stmt));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBatchExecuteAsync(batch, batch_execute_callback, user_data));
        } else {
            if (user_data->nestDeep < MAX_NEST_COUNT) {
                user_data->nestDeep++;
                GmcNodeT *rootNode = NULL;
                GmcNodeT *rootNode1 = NULL;
                GmcNodeT *childNode = NULL;
                GmcBatchT *batch = NULL;
                GmcStmtT *stmt = NULL;
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcAllocStmt(user_data->conn, &stmt));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testBatchPrepareAndSetDiff(user_data->conn, &batch));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testGmcPrepareStmtByLabelName(user_data->stmt, "root0",
                    GMC_OPERATION_MERGE));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangSetRoot(batch, user_data->stmt));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcGetRootNode(user_data->stmt, &rootNode));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangEditChildNode(rootNode, "Con0", GMC_OPERATION_MERGE,
                    &childNode));
                // Add DML操作
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBatchAddDML(batch, user_data->stmt));
                // 设置l1(list)
                for (uint32_t i = 0 + user_data->nestDeep * 50; i < 50 + user_data->nestDeep * 50; i++) {
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testGmcPrepareStmtByLabelName(stmt, "l0", GMC_OPERATION_MERGE));
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangBindChild(batch, user_data->stmt, stmt));
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcGetRootNode(stmt, &rootNode1));
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &i,
                        sizeof(uint32_t)));
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcSetIndexKeyName(stmt, "PK"));
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testYangSetNodeProperty(rootNode1, i,
                        GMC_YANG_PROPERTY_OPERATION_MERGE, true));
                    // Add DML操作
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBatchAddDML(batch, stmt));
                }
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBatchExecuteAsync(batch, Batch_Execute_Cb_Nest, user_data));
            }
        }
    }
}
void Batch_Execute_Cb_Nest1(void *userData, GmcBatchRetT *batchRet, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        if (status != GMERR_REQUEST_TIME_OUT) {
            int ret =
                GmcBatchDeparseRet(batchRet, (uint32_t *)&(user_data->totalNum), (uint32_t *)&(user_data->succNum));
            ASSERT_EQ(GMERR_OK, ret);
        }
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (user_data->nestDeep < MAX_NEST_COUNT) {
            user_data->nestDeep++;
            for (uint32_t i = 0; i < 30; i++) {
                GmcNodeT *rootNode = NULL;
                GmcNodeT *rootNode1 = NULL;
                GmcNodeT *childNode = NULL;
                GmcBatchT *batch = NULL;
                GmcStmtT *stmt = NULL;
                uint32_t pk = i + 30 * user_data->nestDeep;
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcAllocStmt(user_data->conn, &stmt));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testBatchPrepareAndSetDiff(user_data->conn, &batch));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testGmcPrepareStmtByLabelName(user_data->stmt, "root0",
                    GMC_OPERATION_MERGE));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangSetRoot(batch, user_data->stmt));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcGetRootNode(user_data->stmt, &rootNode));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangEditChildNode(rootNode, "Con0", GMC_OPERATION_MERGE,
                    &childNode));
                // Add DML操作
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBatchAddDML(batch, user_data->stmt));
                // 设置l1(list)
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testGmcPrepareStmtByLabelName(stmt, "l0", GMC_OPERATION_MERGE));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangBindChild(batch, user_data->stmt, stmt));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcGetRootNode(stmt, &rootNode1));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &pk,
                    sizeof(uint32_t)));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcSetIndexKeyName(stmt, "PK"));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testYangSetNodeProperty(rootNode1, pk,
                    GMC_YANG_PROPERTY_OPERATION_MERGE, true));
                // Add DML操作
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBatchAddDML(batch, stmt));
                if (i == 0) {
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBatchExecuteAsync(batch, Batch_Execute_Cb_Nest1, user_data));
                } else {
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBatchExecuteAsync(batch, batch_execute_callback, user_data));
                }
                GmcFreeStmt(stmt);
            }
        }
    }
}
void Trans_Start_Cb_Nest(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (user_data->nestDeep < MAX_NEST_COUNT2) {
            user_data->nestDeep++;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcTransStartAsync(g_conn[user_data->nestDeep], &g_trxConfig,
                Trans_Start_Cb_Nest, user_data));
        }
    }
}

void Trans_Commit_Cb_Nest(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (user_data->nestDeep < MAX_NEST_COUNT2) {
            user_data->nestDeep++;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcTransCommitAsync(g_conn[user_data->nestDeep],
                Trans_Commit_Cb_Nest, user_data));
        }
    }
}
void Trans_RollBack_Cb_Nest(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (user_data->nestDeep < MAX_NEST_COUNT2) {
            user_data->nestDeep++;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcTransRollBackAsync(g_conn[user_data->nestDeep],
                Trans_RollBack_Cb_Nest, user_data));
        }
    }
}
void Create_Savepoint_Cb_Nest(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (user_data->nestDeep < MAX_NEST_COUNT3) {
            user_data->nestDeep++;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcTransCreateSavepointAsync(user_data->conn, "sp1",
                Create_Savepoint_Cb_Nest, user_data));
        }
    }
}
void Rollback_Savepoint_Cb_Nest(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (user_data->nestDeep < MAX_NEST_COUNT3) {
            user_data->nestDeep++;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcTransRollBackSavepointAsync(user_data->conn, "sp1",
            Rollback_Savepoint_Cb_Nest, user_data));
        }
    }
}
void Release_Savepoint_Cb_Nest(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (user_data->nestDeep < MAX_NEST_COUNT3) {
            user_data->nestDeep++;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcTransReleaseSavepointAsync(user_data->conn, "sp1",
                Release_Savepoint_Cb_Nest, user_data));
        }
    }
}
void Trans_CheckConflict_Cb_Nest(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (user_data->nestDeep < MAX_NEST_COUNT2) {
            user_data->nestDeep++;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcTransCheckOptimisticTrxConflictAsync(g_conn[user_data->nestDeep],
                Trans_CheckConflict_Cb_Nest, user_data));
        }
    }
}
void CreateRRNameSpaceAndYangLabel(GmcStmtT *stmt)
{
    char *vertexSchema = NULL;
    char *edgeSchema = NULL;
    AsyncUserDataT data = {0};
    // 配置ns级别，RR+乐观事务模式
    GmcNspCfgT yangNspCfg = {};
    yangNspCfg.tablespaceName = NULL;
    yangNspCfg.namespaceName = "yang";
    yangNspCfg.userName = "abc";
    yangNspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};
    int ret = GmcCreateNamespaceWithCfgAsync(stmt, &yangNspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcUseNamespaceAsync(stmt, "yang", use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcClearNamespaceAsync(stmt, "yang", ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 建yang表
    readJanssonFile("schema_file/Yang_Vertex.gmjson", &vertexSchema);
    AW_MACRO_ASSERT_NOTNULL(vertexSchema);
    readJanssonFile("schema_file/Yang_Edge.gmjson", &edgeSchema);
    AW_MACRO_ASSERT_NOTNULL(edgeSchema);
    ret = GmcCreateVertexLabelAsync(stmt, vertexSchema, g_labelconfig, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcCreateEdgeLabelAsync(stmt, edgeSchema, g_labelconfig, create_edge_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    free(vertexSchema);
    free(edgeSchema);
}
void CreateRRNameSpaceAndYangLabel1(GmcStmtT *stmt)
{
    char *vertexSchema = NULL;
    char *edgeSchema = NULL;
    AsyncUserDataT data = {0};
    // 配置ns级别，RR+乐观事务模式
    GmcNspCfgT yangNspCfg = {};
    yangNspCfg.tablespaceName = NULL;
    yangNspCfg.namespaceName = "yang";
    yangNspCfg.userName = "abc";
    yangNspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};
    int ret = GmcCreateNamespaceWithCfgAsync(stmt, &yangNspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcUseNamespaceAsync(stmt, "yang", use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcUseNamespace(g_stmt_sync, "yang");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcClearNamespaceAsync(stmt, "yang", ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 建yang表
    readJanssonFile("schema_file/Yang_Vertex1.gmjson", &vertexSchema);
    AW_MACRO_ASSERT_NOTNULL(vertexSchema);
    readJanssonFile("schema_file/Yang_Edge1.gmjson", &edgeSchema);
    AW_MACRO_ASSERT_NOTNULL(edgeSchema);
    ret = GmcCreateVertexLabelAsync(stmt, vertexSchema, g_labelconfig, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcCreateEdgeLabelAsync(stmt, edgeSchema, g_labelconfig, create_edge_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    free(vertexSchema);
    free(edgeSchema);
}
void DropRRNameSpaceAndYangLabel(GmcStmtT *stmt)
{
    AsyncUserDataT data = {0};
    int ret = 0;
    ret = GmcClearNamespaceAsync(stmt, "yang", ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 删除namesapce
    ret = GmcDropNamespaceAsync(stmt, "yang", drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
}
void DropRRNameSpaceAndYangLabel1(GmcStmtT *stmt)
{
    AsyncUserDataT data = {0};
    int ret = 0;
    ret = GmcClearNamespaceAsync(stmt, "yang", ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 删除namesapce
    ret = GmcDropNamespaceAsync(stmt, "yang", drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
}
void Validate_Cb_Nest(void *userData, GmcValidateResT validateRes, Status status, const char *errMsg)
{
#ifdef FEATURE_YANG_VALIDATION
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->validateRes = validateRes.validateRes;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (user_data->nestDeep < MAX_NEST_COUNT2) {
            user_data->nestDeep++;
            GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_MANDATORY, .cfgJson = NULL};
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangValidateAsync(user_data->stmt, &cfg, Validate_Cb_Nest, user_data));
        }
    }
#endif
}
string GetOpTypeString(GmcDiffOpTypeE op)
{
    switch (op) {
        case GMC_DIFF_OP_CREATE:
            return "create";
        case GMC_DIFF_OP_REMOVE:
            return "remove";
        case GMC_DIFF_OP_UPDATE:
            return "update";
        default:
            return "invalid";
    }
}

string GetValueString(GmcYangNodeValueT *value)
{
    switch (value->type) {
        case GMC_DATATYPE_STRING:
        case GMC_DATATYPE_BYTES:
        case GMC_DATATYPE_FIXED:
            return string("") + (const char *)value->value;
        case GMC_DATATYPE_CHAR:
        case GMC_DATATYPE_UCHAR:
            return "" + to_string(*(const char *)value->value);
            break;
        case GMC_DATATYPE_INT8:
        case GMC_DATATYPE_UINT8:
            return "" + to_string(*(const uint8_t *)value->value);
        case GMC_DATATYPE_INT16:
        case GMC_DATATYPE_UINT16:
            return "" + to_string(*(const uint16_t *)value->value);
        case GMC_DATATYPE_INT32:
        case GMC_DATATYPE_UINT32:
            return "" + to_string(*(const uint32_t *)value->value);
        case GMC_DATATYPE_UINT64:
        case GMC_DATATYPE_INT64:
        case GMC_DATATYPE_TIME:
            return "" + to_string(*(const uint64_t *)value->value);
        case GMC_DATATYPE_BITMAP:
            return "" + to_string(*(const int *)value->value);
        case GMC_DATATYPE_FLOAT:
            return "" + to_string(*(const float *)value->value);
        case GMC_DATATYPE_DOUBLE:
            return "" + to_string(*(const double *)value->value);
        case GMC_DATATYPE_BOOL:
            return "" + to_string(*(const bool *)value->value);
        case GMC_DATATYPE_NULL:
            return string("NULL");
        default:
            return string("NIL:") + to_string(value->type);
    }
}

string GetVertexString(GmcStmtT *stmt, GmcYangNodeT *info, bool isNewData)
{
    GmcYangNodeValueT *propValue = NULL;
    string res = "";
    Status ret;
    GmcDiffOpTypeE opType;
    EXPECT_EQ(GMERR_OK, GmcYangNodeGetDiffOpType(info, &opType));
    uint32_t propNum = 0;
    if ((isNewData && opType == GMC_DIFF_OP_CREATE) || (!isNewData && opType == GMC_DIFF_OP_REMOVE) ||
        opType == GMC_DIFF_OP_UPDATE) {
        ret = GmcYangNodeGetKeyPropNum(info, &propNum);
        if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
            return "";
        }
        EXPECT_EQ(GMERR_OK, ret);
        res += "priKey(";
        for (unsigned int i = 0; i < propNum; i++) {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetKeyPropValue(info, i, &propValue));
            string propNameString = propValue->name;
            res += propNameString + ":" + GetValueString(propValue) + ",";
        }
        if (res[res.size() - 1] == ',') {
            res[res.size() - 1] = ')';
        } else {
            res += ")";
        }
    }
    bool isHasNewPrevNode = false;
    bool isHasOldPrevNode = false;
    ret = GmcYangNodeHasNewPrev(info, &isHasNewPrevNode);
    if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
        return "";
    }
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangNodeHasOldPrev(info, &isHasOldPrevNode);
    if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
        return "";
    }
    EXPECT_EQ(GMERR_OK, ret);
    if ((isNewData && isHasNewPrevNode) || (!isNewData && isHasOldPrevNode) || opType == GMC_DIFF_OP_UPDATE) {
        bool isExist = (isNewData ? isHasNewPrevNode : isHasOldPrevNode);
        if (!isExist) {
            if (res == "") {
                res = "NULL";
            }
            return res;
        }
        res += ", preKey(";
        ret = GmcYangNodeGetKeyPropNum(info, &propNum);
        if (ret != GMERR_OK) {
            if (res == "") {
                res = "NULL";
            }
            return res;
        }
        for (unsigned int i = 0; i < propNum; i++) {
            ret = isNewData ? GmcYangNodeGetNewPrevKey(info, i, &propValue) :
                              GmcYangNodeGetOldPrevKey(info, i, &propValue);
            if (ret == GMERR_INVALID_NAME) {
                continue;
            } else if (ret != GMERR_OK) {
                cout << "error:" << ret << ", " << propValue->name;
            }
            string propNameString = propValue->name;
            res += propNameString + ":" + GetValueString(propValue) + ",";
        }
        if (res[res.size() - 1] == ',') {
            res[res.size() - 1] = ')';
        } else {
            res += ")";
        }
    }
    if (res == "") {
        res = "NULL";
    }
    return res;
}

void GetYangInfoString(GmcStmtT *stmt, GmcYangNodeT *info, string parentFix, string &res)
{
    res = parentFix + ":";
    GmcDiffOpTypeE opType;
    EXPECT_EQ(GMERR_OK, GmcYangNodeGetDiffOpType(info, &opType));
    res += GetOpTypeString((GmcDiffOpTypeE)opType);

    GmcYangNodeTypeE nodeType;
    EXPECT_EQ(GMERR_OK, GmcYangNodeGetType(info, &nodeType));
    if (nodeType == GMC_YANG_FIELD) {
        GmcYangNodeValueT *newValue = NULL;
        GmcYangNodeValueT *oldValue = NULL;
        if (opType == GMC_DIFF_OP_CREATE) {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetNewValue(info, &newValue));
            res += "(" + GetValueString(newValue) + ")";
        } else if (opType == GMC_DIFF_OP_REMOVE) {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetOldValue(info, &oldValue));
            res += "(" + GetValueString(oldValue) + ")";
        } else {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetNewValue(info, &newValue));
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetOldValue(info, &oldValue));
            res += "(" + GetValueString(newValue) + "," + GetValueString(oldValue) + ")";
        }
    } else {
        if (strcmp(GetVertexString(stmt, info, true).c_str(), "") != 0) {
            res += "[(" + GetVertexString(stmt, info, true) + "),";
        }
        if (strcmp(GetVertexString(stmt, info, false).c_str(), "") != 0) {
            res += "(" + GetVertexString(stmt, info, false) + ")]";
        }
    }
}

// 深度遍历生成diff信息字符串
void DFSYangNode(GmcStmtT *stmt, GmcYangNodeT *parent, string prefix, string &resStr)
{
    GmcYangNodeT *child = NULL;
    GmcYangNodeT *prevChild = NULL;
    string res = "";
    string diffStr;
    do {
        ASSERT_EQ(GMERR_OK, GmcYangNodeGetNext(parent, prevChild, &child));
        prevChild = child;
        if (child != NULL) {
            // 打印diff信息
            const char *nodeName;
            ASSERT_EQ(GMERR_OK, GmcYangNodeGetName(child, &nodeName));
            string childName = prefix + nodeName;
            ASSERT_NO_FATAL_FAILURE(GetYangInfoString(stmt, child, childName, diffStr));
            res += diffStr + "\n";
            string childStr;
            DFSYangNode(stmt, child, nodeName + string("."), childStr);
            res += childStr;
        }
    } while (child != NULL);
    resStr += res;
}

// 比较s1 s2两个字符串，如果字符串相同，返回空串，不同返回从不同位置开始的子串
string StrCmp(string &s1, string &s2)
{
    int i = 0;
    for (; s1[i] != 0 && s2[i] != 0; i++) {
        if (s1[i] != s2[i]) {
            return s1.substr(i);
        }
    }

    if (s1[i] == 0 && s2[i] == 0) {
        return string("");
    } else if (s2[i] == 0) {
        return s1.substr(i);
    } else {
        return s2.substr(i);
    }
}
// 不校验diff准确性
void TestCheckYangTree(GmcStmtT *stmt, const GmcYangTreeT **yangTrees, uint32_t idx,
    uint32_t count, vector<string> &expectReply)
{
    GmcYangNodeT *rootInfo = NULL;
    for (uint32_t i = 0; i < count; i++) {
        ASSERT_EQ(GMERR_OK, GmcYangGetRootNode(yangTrees[i], &rootInfo));
        string res;
        const char *rootName;
        ASSERT_EQ(GMERR_OK, GmcYangNodeGetName(rootInfo, &rootName));
        ASSERT_NO_FATAL_FAILURE(GetYangInfoString(stmt, rootInfo, rootName + string(""), res));
        res += "\n";
        ASSERT_NO_FATAL_FAILURE(DFSYangNode(stmt, rootInfo, rootName + string("."), res));
        char fileName[128] = {0};
        int ret = sprintf_s(fileName, sizeof(fileName), "perf_%dDiffTreeInfo", count);
        if (ret == -1) {
            AW_FUN_Log(LOG_INFO, "sprintf_s error\n");
            return;
        }
        FILE *fp = fopen(fileName, "w");
        if (fp == NULL) {
            AW_FUN_Log(LOG_INFO, "fopen error\n");
            return;
        }
        ret = fputs(res.c_str(), fp);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "fputs error\n");
            return;
        }
        ret = fclose(fp);
        if (ret == -1) {
            AW_FUN_Log(LOG_INFO, "fclose error\n");
            return;
        }
        ASSERT_EQ(GMERR_OK, GmcYangFreeTree(yangTrees[i]));
    }
}
void Fetch_Diff_Cb(
    void *userData, GmcFetchRetT *fetchRet, Status status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        uint32_t count = 0;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            userData1->recvNum++;
            uint32_t idx = userData1->lastExpectIdx;
            const GmcYangTreeT **yangTree = NULL;
            ASSERT_EQ(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
            TestCheckYangTree(userData1->stmt, yangTree, idx, count, *userData1->expectDiff);
            // 全部获取完以后 才释放FetchRet
            if (isEnd) {
                GmcYangFreeFetchRet(fetchRet);
                return;
            }
            // 没有获取完diff 重新触发回调函数
            userData1->lastExpectIdx = idx + count;
            ASSERT_EQ(GMERR_OK, GmcYangFetchDiffExecuteAsync(userData1->stmt, NULL, Fetch_Diff_Cb, userData1));
            return;
        } else {
            userData1->recvNum++;
        }
    }
}

void testYangTreeToStr(GmcStmtT *stmt, const GmcYangTreeT *reply, bool isDiff)
{
    string res;
    if (!isDiff) {
        char *replyJson = NULL;
        EXPECT_EQ(GMERR_OK, GmcYangTreeToJson(reply, &replyJson));
    }
}
void CheckTreeReply(const GmcYangTreeT **yangTree, uint32_t count, AsyncUserDataT *param, bool isDiff = false)
{
    uint32_t idx = param->lastExpectIdx;
    for (uint32_t i = 0; i < count; i++) {
        if (yangTree[i] == NULL) {
            continue;
        }
        testYangTreeToStr(param->stmt, yangTree[i], isDiff);
        GmcYangFreeTree(yangTree[i]);
    }
}
void FetchSubtree_Cb(
    void *userData, GmcFetchRetT *fetchRet, Status status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        uint32_t count = 0;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            userData1->recvNum++;
            uint32_t idx = userData1->lastExpectIdx;
            const GmcYangTreeT **yangTree = NULL;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
            CheckTreeReply(yangTree, count, userData1);
            // 全部获取完以后 才释放FetchRet
            if (isEnd) {
                GmcYangFreeFetchRet(fetchRet);
                return;
            }
            // 没有获取完subtree 重新触发回调函数
            userData1->lastExpectIdx = idx + count;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangSubtreeFilterExecuteAsync(userData1->stmt, NULL, fetchRet,
                FetchSubtree_Cb, userData1));
        }
    }
}
int testYangSetField(
    GmcNodeT *node, GmcDataTypeE type, void *value, uint32_t size, const char *fieldname, GmcYangPropOpTypeE optype)
{
    int ret = 0;
    GmcPropValueT propValue;
    memcpy(propValue.propertyName, fieldname, (strlen(fieldname) + 1));
    propValue.type = type;
    propValue.value = value;
    propValue.size = size;
    ret = GmcYangSetNodeProperty(node, &propValue, optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    return ret;
}
void DDL_Nest_DML_Cb2(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (user_data->nestDeep < MAX_NEST_COUNT4) {
            user_data->nestDeep++;
            uint32_t pk = 0 + user_data->nestDeep;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK,
                testGmcPrepareStmtByLabelName(user_data->stmt, "Vertex00", GMC_OPERATION_REPLACE));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK,
                GmcSetVertexProperty(user_data->stmt, "PK", GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t)));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK,
                GmcSetVertexProperty(user_data->stmt, "F0", GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t)));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK,
                GmcSetVertexProperty(user_data->stmt, "F1", GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t)));
            GmcAsyncRequestDoneContextT replaceRequestCtx;
            replaceRequestCtx.replaceCb = Replace_Vertex_Cb_Nest;
            replaceRequestCtx.userData = user_data;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecuteAsync(user_data->stmt, &replaceRequestCtx));
        }
    }
}
void DDL_Nest_DML_Cb1(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (user_data->nestDeep < MAX_NEST_COUNT4) {
            user_data->nestDeep++;
            char *vertexSchema = NULL;
            readJanssonFile("schema_file/Vertex00.gmjson", &vertexSchema);
            AW_MACRO_ASSERT_NOTNULL(vertexSchema);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcCreateVertexLabelAsync(user_data->stmt, vertexSchema, NULL,
                DDL_Nest_DML_Cb2, user_data));
            free(vertexSchema);
        }
    }
}
void DDL_Nest_DML_Cb(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (status) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_DUPLICATE_OBJECT, status);
            uint32_t pk = 200;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK,
                testGmcPrepareStmtByLabelName(user_data->stmt, "Vertex00", GMC_OPERATION_REPLACE));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK,
                GmcSetVertexProperty(user_data->stmt, "PK", GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t)));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK,
                GmcSetVertexProperty(user_data->stmt, "F0", GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t)));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK,
                GmcSetVertexProperty(user_data->stmt, "F1", GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t)));
            GmcAsyncRequestDoneContextT replaceRequestCtx;
            replaceRequestCtx.replaceCb = replace_vertex_callback;
            replaceRequestCtx.userData = user_data;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecuteAsync(user_data->stmt, &replaceRequestCtx));
        } else {
            if (user_data->nestDeep < MAX_NEST_COUNT4) {
                user_data->nestDeep++;
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcUseNamespaceAsync(user_data->stmt, "normal",
                    DDL_Nest_DML_Cb1, user_data));
            }
        }
    }
}
void Kv_DDL_Nest_DML_Cb(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (user_data->nestDeep < MAX_NEST_COUNT4) {
            user_data->nestDeep++;
            GmcKvTupleT kvInfo = {0};
            int32_t value = 100 + user_data->nestDeep;
            char key[32];
            sprintf(key, "zhangsan%d", user_data->nestDeep);
            // 设置k-v值
            kvInfo.key = key;
            kvInfo.keyLen = strlen(key);
            kvInfo.value = &value;
            kvInfo.valueLen = sizeof(int32_t);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcKvPrepareStmtByLabelName(user_data->stmt, "kv0"));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcKvSetAsync(user_data->stmt, &kvInfo, Set_KV_Cb_Nest, user_data));
        }
    }
}
void Kv_DML_Nest_DDL_Cb2(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        char kvName[20] = "kv0";
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcKvDropTableAsync(user_data->stmt, kvName,
            drop_kv_table_callback, user_data));
    }
}
void Kv_DML_Nest_DDL_Cb1(void *userData, uint32_t affectedRows, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        char kvName[20] = "kv0";
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcKvTruncateTableAsync(user_data->stmt, kvName,
            Kv_DML_Nest_DDL_Cb2, user_data));
    }
}
void Kv_DML_Nest_DDL_Cb(void *userData, uint32_t affectedRows, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        GmcKvTupleT kvInfo = {0};
        int32_t value = 1000;
        char key[32] = "zhangsan1000";
        // 设置k-v值
        kvInfo.key = key;
        kvInfo.keyLen = strlen(key);
        kvInfo.value = &value;
        kvInfo.valueLen = sizeof(int32_t);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcKvPrepareStmtByLabelName(user_data->stmt, "kv0"));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcKvSetAsync(user_data->stmt, &kvInfo, Kv_DML_Nest_DDL_Cb1, user_data));
    }
}
void DML_Nest_DDL_Cb2(void *userData, uint32_t affectedRows, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, status);
        user_data->recvNum++;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcDropVertexLabelAsync(user_data->stmt, "Vertex00",
            drop_vertex_label_callback, user_data));
    }
}
void DML_Nest_DDL_Cb1(void *userData, uint32_t affectedRows, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcTruncateVertexLabelAsync(user_data->stmt, "Vertex00",
            drop_vertex_label_callback, user_data));
    }
}
void DML_Nest_DDL_Cb(void *userData, uint32_t affectedRows, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        uint32_t pk = 100;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK,
            testGmcPrepareStmtByLabelName(user_data->stmt, "Vertex00", GMC_OPERATION_REPLACE));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK,
            GmcSetVertexProperty(user_data->stmt, "PK", GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK,
            GmcSetVertexProperty(user_data->stmt, "F0", GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK,
            GmcSetVertexProperty(user_data->stmt, "F1", GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t)));
        GmcAsyncRequestDoneContextT replaceRequestCtx;
        replaceRequestCtx.replaceCb = DML_Nest_DDL_Cb1;
        replaceRequestCtx.userData = user_data;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecuteAsync(user_data->stmt, &replaceRequestCtx));
    }
}
void DCL_Nest_DML_Cb1(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (user_data->nestDeep < MAX_NEST_COUNT) {
            user_data->nestDeep++;
            GmcNodeT *rootNode = NULL;
            GmcNodeT *rootNode1 = NULL;
            GmcNodeT *childNode = NULL;
            GmcBatchT *batch = NULL;
            GmcStmtT *stmt = NULL;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcAllocStmt(user_data->conn, &stmt));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testBatchPrepareAndSetDiff(user_data->conn, &batch));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testGmcPrepareStmtByLabelName(user_data->stmt, "root0",
                GMC_OPERATION_MERGE));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangSetRoot(batch, user_data->stmt));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcGetRootNode(user_data->stmt, &rootNode));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangEditChildNode(rootNode, "Con0", GMC_OPERATION_MERGE,
                &childNode));
            // Add DML操作
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBatchAddDML(batch, user_data->stmt));
            // 设置l1(list)
            for (uint32_t i = 0 + user_data->nestDeep * 50; i < 50 + user_data->nestDeep * 50; i++) {
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testGmcPrepareStmtByLabelName(stmt, "l0",
                    GMC_OPERATION_MERGE));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangBindChild(batch, user_data->stmt, stmt));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcGetRootNode(stmt, &rootNode1));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &i,
                    sizeof(uint32_t)));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcSetIndexKeyName(stmt, "PK"));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testYangSetNodeProperty(rootNode1, i,
                    GMC_YANG_PROPERTY_OPERATION_MERGE, true));
                // Add DML操作
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBatchAddDML(batch, stmt));
            }
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBatchExecuteAsync(batch, Batch_Execute_Cb_Nest, user_data));
        }
    }
}
void DCL_Nest_DML_Cb(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (status) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, status);
            GmcNodeT *rootNode = NULL;
            GmcBatchT *batch = NULL;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testBatchPrepareAndSetDiff(user_data->conn, &batch));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testGmcPrepareStmtByLabelName(user_data->stmt, "root0",
                GMC_OPERATION_REMOVE_GRAPH));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangSetRoot(batch, user_data->stmt));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcGetRootNode(user_data->stmt, &rootNode));
            // Add DML操作
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBatchAddDML(batch, user_data->stmt));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBatchExecuteAsync(batch, batch_execute_callback, user_data));
        } else {
            if (user_data->nestDeep < MAX_NEST_COUNT) {
                user_data->nestDeep++;
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcTransCreateSavepointAsync(user_data->conn, "sp1",
                    DCL_Nest_DML_Cb1, user_data));
            }
        }
    }
}
void DCL_Nest_DDL_Cb3(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcDropNamespaceAsync(user_data->stmt, "yang",
            drop_vertex_label_callback, user_data));
    }
}
void DCL_Nest_DDL_Cb2(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcDropVertexLabelAsync(user_data->stmt, "l0",
            DCL_Nest_DDL_Cb3, user_data));
    }
}
void DCL_Nest_DDL_Cb1(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcDropVertexLabelAsync(user_data->stmt, "root0",
            DCL_Nest_DDL_Cb2, user_data));
    }
}
void DCL_Nest_DDL_Cb(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (status) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, status);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcTruncateNamespaceAsync(user_data->stmt, "yang",
                drop_vertex_label_callback, user_data));
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcDropEdgeLabelAsync(user_data->stmt, "L0",
                DCL_Nest_DDL_Cb1, user_data));
        }
    }
}
void Connect_NewConn_Cb(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        YangConnOptionT connOptions = {0};
        connOptions.isOneThreadEpoll = true;
        connOptions.epollRegType = TEST_EPOLLREG_WITH_USD;
        connOptions.epollRegWithUsDFunc = g_epollRegInfoOneThread;
        connOptions.epollFd = &g_epollDataOneThread.userEpollFd;
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestYangGmcConnect(&conn, &stmt,
            GMC_CONN_TYPE_ASYNC, &connOptions));
    }
}
void Disconnect_NewConn_Cb(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        YangConnOptionT connOptions = {0};
        connOptions.isOneThreadEpoll = true;
        connOptions.epollRegType = TEST_EPOLLREG_WITH_USD;
        connOptions.epollRegWithUsDFunc = g_epollRegInfoOneThread;
        connOptions.epollFd = &g_epollDataOneThread.userEpollFd;
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestYangGmcConnect(&conn, &stmt,
            GMC_CONN_TYPE_ASYNC, &connOptions));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, testGmcDisconnect(conn, stmt));
    }
}
void Init_Cb(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        AW_MACRO_ASSERT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, GmcInit());
    }
}
void UnInit_Cb(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        AW_MACRO_ASSERT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, GmcUnInit());
    }
}
void TimeTaken_Cb(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        sleep(4);
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
    }
}
void TimeTaken_Cb1(void *userData, uint32_t affectedRows, Status status, const char *errMsg)
{
    if (userData) {
        sleep(15);
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        uint32_t pk = 1;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK,
            testGmcPrepareStmtByLabelName(user_data->stmt, "Vertex00", GMC_OPERATION_REPLACE));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK,
            GmcSetVertexProperty(user_data->stmt, "PK", GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK,
            GmcSetVertexProperty(user_data->stmt, "F0", GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK,
            GmcSetVertexProperty(user_data->stmt, "F1", GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t)));
        GmcAsyncRequestDoneContextT replaceRequestCtx;
        replaceRequestCtx.replaceCb = replace_vertex_callback;
        replaceRequestCtx.userData = user_data;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecuteAsync(user_data->stmt, &replaceRequestCtx));
    }
}
void TimeTaken_Cb2(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        uint32_t pk = 0;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK,
            testGmcPrepareStmtByLabelName(user_data->stmt, "Vertex00", GMC_OPERATION_REPLACE));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK,
            GmcSetVertexProperty(user_data->stmt, "PK", GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK,
            GmcSetVertexProperty(user_data->stmt, "F0", GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK,
            GmcSetVertexProperty(user_data->stmt, "F1", GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t)));
        GmcAsyncRequestDoneContextT replaceRequestCtx;
        replaceRequestCtx.replaceCb = TimeTaken_Cb1;
        replaceRequestCtx.userData = user_data;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecuteAsync(user_data->stmt, &replaceRequestCtx));
    }
}
void TimeTaken_Cb3(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        sleep(15);
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        uint32_t pk = 0;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK,
            testGmcPrepareStmtByLabelName(user_data->stmt, "Vertex00", GMC_OPERATION_REPLACE));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK,
            GmcSetVertexProperty(user_data->stmt, "PK", GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK,
            GmcSetVertexProperty(user_data->stmt, "F0", GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK,
            GmcSetVertexProperty(user_data->stmt, "F1", GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t)));
        GmcAsyncRequestDoneContextT replaceRequestCtx;
        replaceRequestCtx.replaceCb = replace_vertex_callback;
        replaceRequestCtx.userData = user_data;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecuteAsync(user_data->stmt, &replaceRequestCtx));
    }
}
void Send_LargePackets_Cb_Nest(void *userData, GmcBatchRetT *batchRet, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        if (status != GMERR_REQUEST_TIME_OUT) {
            int ret =
                GmcBatchDeparseRet(batchRet, (uint32_t *)&(user_data->totalNum), (uint32_t *)&(user_data->succNum));
            ASSERT_EQ(GMERR_OK, ret);
        }
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        user_data->nestDeep++;
        GmcNodeT *rootNode = NULL;
        GmcNodeT *rootNode1 = NULL;
        GmcNodeT *childNode = NULL;
        GmcBatchT *batch = NULL;
        GmcStmtT *stmt = NULL;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcAllocStmt(user_data->conn, &stmt));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testBatchPrepareAndSetDiff(user_data->conn, &batch));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testGmcPrepareStmtByLabelName(user_data->stmt, "root0",
            GMC_OPERATION_MERGE));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangSetRoot(batch, user_data->stmt));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcGetRootNode(user_data->stmt, &rootNode));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangEditChildNode(rootNode, "Con0", GMC_OPERATION_MERGE,
            &childNode));
        // Add DML操作
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBatchAddDML(batch, user_data->stmt));
        // 设置l1(list)
        for (uint32_t i = 50; i < 210; i++) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testGmcPrepareStmtByLabelName(stmt, "l0", GMC_OPERATION_MERGE));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangBindChild(batch, user_data->stmt, stmt));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcGetRootNode(stmt, &rootNode1));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &i,
                sizeof(uint32_t)));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcSetIndexKeyName(stmt, "PK"));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testYangSetNodePropertyObj(rootNode1, i,
                GMC_YANG_PROPERTY_OPERATION_MERGE));
            // Add DML操作
            if (i > 205) {
                AW_MACRO_EXPECT_EQ_INT(1011000, GmcBatchAddDML(batch, stmt));
            } else {
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBatchAddDML(batch, stmt));
            }
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBatchExecuteAsync(batch, batch_execute_callback, user_data));
    }
}
void Ten_Thousand_Cb_Nest(void *userData, GmcBatchRetT *batchRet, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        if (status != GMERR_REQUEST_TIME_OUT) {
            int ret =
                GmcBatchDeparseRet(batchRet, (uint32_t *)&(user_data->totalNum), (uint32_t *)&(user_data->succNum));
            ASSERT_EQ(GMERR_OK, ret);
        }
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (user_data->nestDeep < MAX_NEST_COUNT5) {
            user_data->nestDeep++;
            GmcNodeT *rootNode = NULL;
            GmcNodeT *rootNode1 = NULL;
            GmcNodeT *childNode = NULL;
            GmcBatchT *batch = NULL;
            GmcStmtT *stmt = NULL;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcAllocStmt(user_data->conn, &stmt));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testBatchPrepareAndSetDiff(user_data->conn, &batch));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testGmcPrepareStmtByLabelName(user_data->stmt, "root0",
                GMC_OPERATION_MERGE));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangSetRoot(batch, user_data->stmt));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcGetRootNode(user_data->stmt, &rootNode));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangEditChildNode(rootNode, "Con0", GMC_OPERATION_MERGE,
                &childNode));
            // Add DML操作
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBatchAddDML(batch, user_data->stmt));
            // 设置l1(list)
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testGmcPrepareStmtByLabelName(stmt, "l0", GMC_OPERATION_MERGE));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangBindChild(batch, user_data->stmt, stmt));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcGetRootNode(stmt, &rootNode1));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &user_data->nestDeep,
                sizeof(uint32_t)));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcSetIndexKeyName(stmt, "PK"));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testYangSetNodeProperty(rootNode1, user_data->nestDeep,
                GMC_YANG_PROPERTY_OPERATION_MERGE, true));
            // Add DML操作
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBatchAddDML(batch, stmt));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBatchExecuteAsync(batch, Ten_Thousand_Cb_Nest, user_data));
        }
    }
}
void Sync_Operation_Cb_Nest1(void *userData, uint32_t affectedRows, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        YangConnOptionT connOptions = {0};
        connOptions.isOneThreadEpoll = true;
        connOptions.epollRegType = TEST_EPOLLREG_WITH_USD;
        connOptions.epollRegWithUsDFunc = g_epollRegInfoOneThread;
        connOptions.epollFd = &g_epollDataOneThread.userEpollFd;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestYangGmcConnect(&conn, &stmt,
            GMC_CONN_TYPE_SYNC, &connOptions));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcDropVertexLabel(stmt, "Vertex00"));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, testGmcDisconnect(conn, stmt));
    }
}
void Sync_Operation_Cb_Nest(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        uint32_t pk = 0;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK,
            testGmcPrepareStmtByLabelName(user_data->stmt, "Vertex00", GMC_OPERATION_REPLACE));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK,
            GmcSetVertexProperty(user_data->stmt, "PK", GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK,
            GmcSetVertexProperty(user_data->stmt, "F0", GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK,
            GmcSetVertexProperty(user_data->stmt, "F1", GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t)));
        GmcAsyncRequestDoneContextT replaceRequestCtx;
        replaceRequestCtx.replaceCb = Sync_Operation_Cb_Nest1;
        replaceRequestCtx.userData = user_data;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecuteAsync(user_data->stmt, &replaceRequestCtx));
    }
}
bool isFull = false;
// 嵌套回调卡死
void Async_Queen_Full_Cb_Nest(void *userData, uint32_t affectedRows, Status status, const char *errMsg)
{
    while (!isFull) {
    }
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (!isFull && user_data->nestDeep < MAX_NEST_COUNT2) {
            static uint32_t succdCnt = 0;
            user_data->nestDeep++;
            uint32_t pk = 20000 + user_data->nestDeep;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK,
                testGmcPrepareStmtByLabelName(user_data->stmt, "Vertex00", GMC_OPERATION_REPLACE));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK,
                GmcSetVertexProperty(user_data->stmt, "PK", GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t)));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK,
                GmcSetVertexProperty(user_data->stmt, "F0", GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t)));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK,
                GmcSetVertexProperty(user_data->stmt, "F1", GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t)));
            GmcAsyncRequestDoneContextT replaceRequestCtx;
            replaceRequestCtx.replaceCb = Async_Queen_Full_Cb_Nest;
            replaceRequestCtx.userData = user_data;
            int32_t ret = GmcExecuteAsync(user_data->stmt, &replaceRequestCtx);
            if (ret != GMERR_OK) {
                AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_SEND_BUFFER_FULL, ret);
                user_data->nestDeep = succdCnt;
                isFull = true;
            } else {
                succdCnt++;
            }
        }
    }
}
// 不嵌套回调卡死
void Async_Queen_Full_Cb_Nest1(void *userData, uint32_t affectedRows, Status status, const char *errMsg)
{
    while (!isFull) {
    }
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
    }
}
void Create_Nest_Use_Namespace_Cb(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcUseNamespaceAsync(user_data->stmt, "yang0",
            use_namespace_callback, user_data));
    }
}
void Use_Nest_Start_Trans_Cb(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcTransStartAsync(user_data->conn, &g_trxConfig,
            trans_start_callback, user_data));
    }
}
void Use_Nest_Create_Tablespace_Cb(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        GmcTspCfgT tspCfg;
        tspCfg.tablespaceName = "tsp0";
        tspCfg.initSize = 4;
        tspCfg.stepSize = 4;
        tspCfg.maxSize = 4;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcCreateTablespaceAsync(user_data->stmt, &tspCfg,
            trans_start_callback, user_data));
    }
}
void Use_Nest_Clear_Namespace_Cb(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcClearNamespaceAsync(user_data->stmt, "yang0",
            trans_start_callback, user_data));
    }
}
void CreateTsp_Nest_BindNs_Cb(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBindNamespaceToTableSpaceAsync(user_data->stmt, "yang0", "tsp0",
            trans_start_callback, user_data));
    }
}
void BindNs_Nest_Create_Vertex_Cb(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (user_data->nestDeep < MAX_NEST_COUNT6 / 2) {
            user_data->nestDeep++;
            char gLabelSchema[1024];
            snprintf(gLabelSchema, 1024,
                "[{\"type\":\"record\", \"name\":\"T%d\", \"tablespace\": \"tsp0\", \"fields\":[{\"name\":\"F0\","
                "\"type\":\"int32\"},{\"name\":\"F1\", \"type\":\"int32\"}],"
                "\"keys\":[{\"node\":\"T%d\", \"name\":\"T%d_K0\", \"fields\":[\"F0\"], "
                "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
                user_data->nestDeep, user_data->nestDeep, user_data->nestDeep);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcCreateVertexLabelAsync(user_data->stmt, gLabelSchema, NULL,
                BindNs_Nest_Create_Vertex_Cb, user_data));
        }
    }
}
void CreateEdge_Nest_CreateVertex_Cb(void *userData, Status status, const char *errMsg);
void CreateVertex_Nest_CreateEdge_Cb(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (user_data->nestDeep < MAX_NEST_COUNT6) {
            user_data->nestDeep++;
            char gLabelSchema[1024];
            snprintf(gLabelSchema, 1024,
                "[{\"type\":\"container\",\"name\":\"root%d\",\"presence\":false,\"fields\":["
                "{\"name\":\"ID\", \"type\":\"uint32\", \"nullable\":false},"
                "{\"name\":\"F0\", \"type\":\"uint32\", \"default\":2023},"
                "{\"type\":\"container\",\"presence\":true,\"name\":\"Con%d\",\"fields\":["
                "{\"name\":\"F0\", \"type\":\"uint32\", \"default\":2023}]}],"
                "\"keys\":[{\"node\":\"root%d\",\"name\":\"PK\",\"fields\":[\"ID\"],\"index\":{\"type\":\"primary\"},"
                "\"constraints\":{\"unique\":true}}]},"
                "{\"type\":\"list\",\"name\":\"list%d\",\"fields\":["
                "{\"name\":\"ID\", \"type\":\"uint32\", \"nullable\":false, \"auto_increment\":true},"
                "{\"name\":\"PID\", \"type\":\"uint32\", \"nullable\":false},"
                "{\"name\":\"F0\", \"type\":\"uint32\", \"nullable\":false}],"
                "\"keys\":[{\"node\":\"list%d\",\"name\":\"PK\",\"fields\":[\"PID\", \"F0\"],\"index\":"
                "{\"type\":\"primary\"},\"constraints\":{\"unique\":true}}]}]", user_data->nestDeep,
                user_data->nestDeep, user_data->nestDeep, user_data->nestDeep, user_data->nestDeep);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcCreateVertexLabelAsync(user_data->stmt, gLabelSchema, g_labelconfig,
                CreateEdge_Nest_CreateVertex_Cb, user_data));
        }
    }
}
void CreateEdge_Nest_CreateVertex_Cb(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (user_data->nestDeep < MAX_NEST_COUNT6) {
            user_data->nestDeep++;
            char gLabelSchema[1024];
            snprintf(gLabelSchema, 1024,
                "[{\"name\":\"L%d\",\"source_vertex_label\":\"root%d\",\"dest_vertex_label\":\"list%d\","
                "\"source_node_path\":\"/Con%d\",\"constraint\":{\"operator_type\":\"and\","
                "\"conditions\":[{\"source_property\": \"ID\",\"dest_property\": \"PID\"}]}}]",
                user_data->nestDeep - 1, user_data->nestDeep - 1,
                user_data->nestDeep - 1, user_data->nestDeep - 1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcCreateEdgeLabelAsync(user_data->stmt, gLabelSchema, g_labelconfig,
                CreateVertex_Nest_CreateEdge_Cb, user_data));
        }
    }
}
void TransStart_Nest_Batch_Cb(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (user_data->nestDeep < MAX_NEST_COUNT) {
            user_data->nestDeep++;
            GmcNodeT *rootNode = NULL;
            GmcNodeT *rootNode1 = NULL;
            GmcNodeT *childNode = NULL;
            GmcBatchT *batch = NULL;
            GmcStmtT *stmt = NULL;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcAllocStmt(user_data->conn, &stmt));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testBatchPrepareAndSetDiff(user_data->conn, &batch));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testGmcPrepareStmtByLabelName(user_data->stmt, "root0",
                GMC_OPERATION_MERGE));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangSetRoot(batch, user_data->stmt));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcGetRootNode(user_data->stmt, &rootNode));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangEditChildNode(rootNode, "Con0", GMC_OPERATION_MERGE,
                &childNode));
            // Add DML操作
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBatchAddDML(batch, user_data->stmt));
            // 设置l1(list)
            for (uint32_t i = 0 + user_data->nestDeep * 50; i < 50 + user_data->nestDeep * 50; i++) {
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testGmcPrepareStmtByLabelName(stmt, "l0", GMC_OPERATION_MERGE));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangBindChild(batch, user_data->stmt, stmt));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcGetRootNode(stmt, &rootNode1));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &i,
                    sizeof(uint32_t)));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcSetIndexKeyName(stmt, "PK"));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testYangSetNodeProperty(rootNode1, i,
                    GMC_YANG_PROPERTY_OPERATION_MERGE, true));
                // Add DML操作
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBatchAddDML(batch, stmt));
            }
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBatchExecuteAsync(batch, Batch_Execute_Cb_Nest, user_data));
        }
    }
}
void TransStart_Nest_Rollback_Cb(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcTransRollBackAsync(user_data->conn,
            trans_rollback_callback, user_data));
    }
}
void Rollback_Nest_TransStart_Cb(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcTransStartAsync(user_data->conn, &g_trxConfig,
            trans_start_callback, user_data));
    }
}
void TransStart_Nest_CreateSp_Cb(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcTransCreateSavepointAsync(user_data->conn, "sp0",
            trans_rollback_callback, user_data));
    }
}
void TransStart_Nest_Subtree_Cb(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        GmcNodeT *root = NULL;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK,
            testGmcPrepareStmtByLabelName(user_data->stmt, "root0", GMC_OPERATION_SUBTREE_FILTER));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcGetRootNode(user_data->stmt, &root));
        uint32_t f0 = 2023;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testYangSetField(root, GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t),
            "F0", GMC_YANG_PROPERTY_OPERATION_CREATE));
        GmcSubtreeFilterItemT filter = {
            .rootName = NULL,
            .subtree = {.obj = root},
            .jsonFlag = GMC_JSON_INDENT(4),
            .maxDepth = 0,
            .isLocationFilter = 0,
            .defaultMode = 3,
            .configFlag = 0,
        };
        GmcSubtreeFilterT filters = {
            .filterMode = GMC_FETCH_OBJ,
            .filter = &filter,
        };
        user_data->filterMode = filters.filterMode;
        user_data->lastExpectIdx = 0;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangSubtreeFilterExecuteAsync(user_data->stmt, &filters,
            NULL, FetchSubtree_Cb, user_data));
    }
}
void ReleaseSp_Nest_Rollback_Cb(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (status) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, status);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcTransRollBackSavepointAsync(user_data->conn, "sp1",
                trans_commit_callback, user_data));
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcTransRollBackAsync(user_data->conn,
                trans_commit_callback, user_data));
        }
    }
}
void FetchDiff_Nest_TransCommit_Cb(
    void *userData, GmcFetchRetT *fetchRet, Status status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        uint32_t count = 0;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            userData1->recvNum++;
            uint32_t idx = userData1->lastExpectIdx;
            const GmcYangTreeT **yangTree = NULL;
            ASSERT_EQ(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
            TestCheckYangTree(userData1->stmt, yangTree, idx, count, *userData1->expectDiff);
            // 全部获取完以后 才释放FetchRet
            if (isEnd) {
                GmcYangFreeFetchRet(fetchRet);
                // 提交事务
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcTransCommitAsync(userData1->conn,
                    trans_commit_callback, userData1));
                return;
            }
            // 没有获取完diff 重新触发回调函数
            userData1->lastExpectIdx = idx + count;
            ASSERT_EQ(GMERR_OK, GmcYangFetchDiffExecuteAsync(userData1->stmt, NULL,
                FetchDiff_Nest_TransCommit_Cb, userData1));
        }
    }
}
void FetchDiff_Nest_TransRollback_Cb(
    void *userData, GmcFetchRetT *fetchRet, Status status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        uint32_t count = 0;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            userData1->recvNum++;
            uint32_t idx = userData1->lastExpectIdx;
            const GmcYangTreeT **yangTree = NULL;
            ASSERT_EQ(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
            TestCheckYangTree(userData1->stmt, yangTree, idx, count, *userData1->expectDiff);
            // 全部获取完以后 才释放FetchRet
            if (isEnd) {
                GmcYangFreeFetchRet(fetchRet);
                // 回滚事务
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcTransRollBackAsync(userData1->conn,
                    trans_commit_callback, userData1));
                return;
            }
            // 没有获取完diff 重新触发回调函数
            userData1->lastExpectIdx = idx + count;
            ASSERT_EQ(GMERR_OK, GmcYangFetchDiffExecuteAsync(userData1->stmt, NULL,
                FetchDiff_Nest_TransRollback_Cb, userData1));
        } else {
            userData1->recvNum++;
            AW_MACRO_EXPECT_EQ_INT(GMERR_TRANS_MODE_MISMATCH, status);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcTransRollBackAsync(userData1->conn,
                trans_commit_callback, userData1));
        }
    }
}
void FetchDiff_Nest_ReleaseSp_Cb(
    void *userData, GmcFetchRetT *fetchRet, Status status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        uint32_t count = 0;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            userData1->recvNum++;
            uint32_t idx = userData1->lastExpectIdx;
            const GmcYangTreeT **yangTree = NULL;
            ASSERT_EQ(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
            TestCheckYangTree(userData1->stmt, yangTree, idx, count, *userData1->expectDiff);
            // 全部获取完以后 才释放FetchRet
            if (isEnd) {
                GmcYangFreeFetchRet(fetchRet);
                // 回滚事务
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcTransReleaseSavepointAsync(userData1->conn, "sp1",
                    trans_commit_callback, userData1));
                return;
            }
            // 没有获取完diff 重新触发回调函数
            userData1->lastExpectIdx = idx + count;
            ASSERT_EQ(GMERR_OK, GmcYangFetchDiffExecuteAsync(userData1->stmt, NULL,
                FetchDiff_Nest_ReleaseSp_Cb, userData1));
        }
    }
}
void FetchDiff_Nest_Subtree_Cb(
    void *userData, GmcFetchRetT *fetchRet, Status status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        uint32_t count = 0;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            userData1->recvNum++;
            uint32_t idx = userData1->lastExpectIdx;
            const GmcYangTreeT **yangTree = NULL;
            ASSERT_EQ(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
            TestCheckYangTree(userData1->stmt, yangTree, idx, count, *userData1->expectDiff);
            // 全部获取完以后 才释放FetchRet
            if (isEnd) {
                GmcYangFreeFetchRet(fetchRet);
                sleep(1);
                GmcNodeT *root = NULL;
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK,
                    testGmcPrepareStmtByLabelName(userData1->stmt, "root0", GMC_OPERATION_SUBTREE_FILTER));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcGetRootNode(userData1->stmt, &root));
                uint32_t f0 = 2023;
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testYangSetField(root, GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t),
                    "F0", GMC_YANG_PROPERTY_OPERATION_CREATE));
                GmcSubtreeFilterItemT filter = {
                    .rootName = NULL,
                    .subtree = {.obj = root},
                    .jsonFlag = GMC_JSON_INDENT(4),
                    .maxDepth = 0,
                    .isLocationFilter = 0,
                    .defaultMode = 3,
                    .configFlag = 0,
                };
                GmcSubtreeFilterT filters = {
                    .filterMode = GMC_FETCH_OBJ,
                    .filter = &filter,
                };
                userData1->filterMode = filters.filterMode;
                userData1->lastExpectIdx = 0;
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangSubtreeFilterExecuteAsync(userData1->stmt, &filters,
                    NULL, FetchSubtree_Cb, userData1));
                return;
            }
            // 没有获取完diff 重新触发回调函数
            userData1->lastExpectIdx = idx + count;
            ASSERT_EQ(GMERR_OK, GmcYangFetchDiffExecuteAsync(userData1->stmt, NULL,
                FetchDiff_Nest_Subtree_Cb, userData1));
        } else {
            userData1->recvNum++;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangFetchDiffExecuteAsync(userData1->stmt, NULL,
                Fetch_Diff_Cb, userData1));
        }
    }
}
void Batch_Nest_Subtree_Cb(void *userData, GmcBatchRetT *batchRet, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        if (status != GMERR_REQUEST_TIME_OUT) {
            int ret =
                GmcBatchDeparseRet(batchRet, (uint32_t *)&(user_data->totalNum), (uint32_t *)&(user_data->succNum));
            ASSERT_EQ(GMERR_OK, ret);
        }
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        GmcNodeT *root = NULL;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK,
            testGmcPrepareStmtByLabelName(user_data->stmt, "root0", GMC_OPERATION_SUBTREE_FILTER));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcGetRootNode(user_data->stmt, &root));
        uint32_t f0 = 2023;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testYangSetField(root, GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t),
            "F0", GMC_YANG_PROPERTY_OPERATION_CREATE));
        GmcSubtreeFilterItemT filter = {
            .rootName = NULL,
            .subtree = {.obj = root},
            .jsonFlag = GMC_JSON_INDENT(4),
            .maxDepth = 0,
            .isLocationFilter = 0,
            .defaultMode = 0,
            .configFlag = 0,
        };
        GmcSubtreeFilterT filters = {
            .filterMode = GMC_FETCH_OBJ,
            .filter = &filter,
        };
        user_data->filterMode = filters.filterMode;
        user_data->lastExpectIdx = 0;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangSubtreeFilterExecuteAsync(user_data->stmt, &filters,
            NULL, FetchSubtree_Cb, user_data));
    }
}
void Batch_Nest_Diff_Cb(void *userData, GmcBatchRetT *batchRet, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        if (status != GMERR_REQUEST_TIME_OUT) {
            int ret =
                GmcBatchDeparseRet(batchRet, (uint32_t *)&(user_data->totalNum), (uint32_t *)&(user_data->succNum));
            ASSERT_EQ(GMERR_OK, ret);
        }
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangFetchDiffExecuteAsync(user_data->stmt, NULL,
            Fetch_Diff_Cb, user_data));
    }
}
void Subtree_Nest_Batch_Cb(
    void *userData, GmcFetchRetT *fetchRet, Status status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        uint32_t count = 0;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            userData1->recvNum++;
            uint32_t idx = userData1->lastExpectIdx;
            const GmcYangTreeT **yangTree = NULL;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
            CheckTreeReply(yangTree, count, userData1);
            // 全部获取完以后 才释放FetchRet
            if (isEnd) {
                GmcYangFreeFetchRet(fetchRet);
                GmcNodeT *rootNode = NULL;
                GmcNodeT *rootNode1 = NULL;
                GmcNodeT *childNode = NULL;
                GmcBatchT *batch = NULL;
                GmcStmtT *stmt = NULL;
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcAllocStmt(userData1->conn, &stmt));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testBatchPrepareAndSetDiff(userData1->conn, &batch));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testGmcPrepareStmtByLabelName(userData1->stmt, "root0",
                    GMC_OPERATION_MERGE));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangSetRoot(batch, userData1->stmt));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcGetRootNode(userData1->stmt, &rootNode));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangEditChildNode(rootNode, "Con0", GMC_OPERATION_MERGE,
                    &childNode));
                // Add DML操作
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBatchAddDML(batch, userData1->stmt));
                // 设置l1(list)
                for (uint32_t i = 100; i < 200; i++) {
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testGmcPrepareStmtByLabelName(stmt, "l0", GMC_OPERATION_MERGE));
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangBindChild(batch, userData1->stmt, stmt));
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcGetRootNode(stmt, &rootNode1));
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &i,
                        sizeof(uint32_t)));
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcSetIndexKeyName(stmt, "PK"));
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testYangSetNodePropertyObj(rootNode1, i,
                        GMC_YANG_PROPERTY_OPERATION_MERGE));
                    // Add DML操作
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBatchAddDML(batch, stmt));
                }
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBatchExecuteAsync(batch, batch_execute_callback, userData1));
                return;
            }
            // 没有获取完subtree 重新触发回调函数
            userData1->lastExpectIdx = idx + count;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangSubtreeFilterExecuteAsync(userData1->stmt, NULL, fetchRet,
                FetchSubtree_Cb, userData1));
        }
    }
}

void Subtree_Nest_Diff_Cb(
    void *userData, GmcFetchRetT *fetchRet, Status status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        uint32_t count = 0;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            userData1->recvNum++;
            uint32_t idx = userData1->lastExpectIdx;
            const GmcYangTreeT **yangTree = NULL;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
            CheckTreeReply(yangTree, count, userData1);
            // 全部获取完以后 才释放FetchRet
            if (isEnd) {
                GmcYangFreeFetchRet(fetchRet);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangFetchDiffExecuteAsync(userData1->stmt, NULL,
                    Fetch_Diff_Cb, userData1));
                return;
            }
            // 没有获取完subtree 重新触发回调函数
            userData1->lastExpectIdx = idx + count;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangSubtreeFilterExecuteAsync(userData1->stmt, NULL, fetchRet,
                FetchSubtree_Cb, userData1));
        }
    }
}

void Subtree_Nest_RollBack_Cb(
    void *userData, GmcFetchRetT *fetchRet, Status status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        uint32_t count = 0;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            userData1->recvNum++;
            uint32_t idx = userData1->lastExpectIdx;
            const GmcYangTreeT **yangTree = NULL;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
            CheckTreeReply(yangTree, count, userData1);
            // 全部获取完以后 才释放FetchRet
            if (isEnd) {
                GmcYangFreeFetchRet(fetchRet);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcTransRollBackAsync(userData1->conn,
                    trans_rollback_callback, userData1));
                return;
            }
            // 没有获取完subtree 重新触发回调函数
            userData1->lastExpectIdx = idx + count;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangSubtreeFilterExecuteAsync(userData1->stmt, NULL, fetchRet,
                FetchSubtree_Cb, userData1));
        }
    }
}

void Subtree_Nest_TransStart_Cb(
    void *userData, GmcFetchRetT *fetchRet, Status status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        uint32_t count = 0;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            userData1->recvNum++;
            uint32_t idx = userData1->lastExpectIdx;
            const GmcYangTreeT **yangTree = NULL;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
            CheckTreeReply(yangTree, count, userData1);
            // 全部获取完以后 才释放FetchRet
            if (isEnd) {
                GmcYangFreeFetchRet(fetchRet);
                GmcConnT *conn = NULL;
                GmcStmtT *stmt = NULL;
                YangConnOptionT connOptions = {0};
                connOptions.isOneThreadEpoll = true;
                connOptions.epollRegType = TEST_EPOLLREG_WITH_USD;
                connOptions.epollRegWithUsDFunc = g_epollRegInfoOneThread;
                connOptions.epollFd = &g_epollDataOneThread.userEpollFd;
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestYangGmcConnect(&conn, &stmt,
                    GMC_CONN_TYPE_ASYNC, &connOptions));
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcUseNamespaceAsync(stmt, "yang",
                    trans_rollback_callback, userData1));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcTransStartAsync(conn, &g_trxConfig,
                    trans_rollback_callback, userData1));
                userData1->conn = conn;
                userData1->stmt = stmt;
                return;
            }
            // 没有获取完subtree 重新触发回调函数
            userData1->lastExpectIdx = idx + count;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangSubtreeFilterExecuteAsync(userData1->stmt, NULL, fetchRet,
                FetchSubtree_Cb, userData1));
        }
    }
}
void Trans_Nest_Cb4(void *userData, GmcBatchRetT *batchRet, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        if (status != GMERR_REQUEST_TIME_OUT) {
            int ret =
                GmcBatchDeparseRet(batchRet, (uint32_t *)&(user_data->totalNum), (uint32_t *)&(user_data->succNum));
            ASSERT_EQ(GMERR_OK, ret);
        }
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcTransCommitAsync(user_data->conn,
            trans_commit_callback, user_data));
    }
}
void Trans_Nest_Cb3(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        GmcNodeT *rootNode = NULL;
        GmcNodeT *rootNode1 = NULL;
        GmcNodeT *childNode = NULL;
        GmcBatchT *batch = NULL;
        GmcStmtT *stmt = NULL;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcAllocStmt(user_data->conn, &stmt));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testBatchPrepareAndSetDiff(user_data->conn, &batch));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testGmcPrepareStmtByLabelName(user_data->stmt, "root0",
            GMC_OPERATION_MERGE));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangSetRoot(batch, user_data->stmt));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcGetRootNode(user_data->stmt, &rootNode));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangEditChildNode(rootNode, "Con0", GMC_OPERATION_MERGE,
            &childNode));
        // Add DML操作
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBatchAddDML(batch, user_data->stmt));
        // 设置l1(list)
        for (uint32_t i = 0; i < 100; i++) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testGmcPrepareStmtByLabelName(stmt, "l0", GMC_OPERATION_MERGE));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangBindChild(batch, user_data->stmt, stmt));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcGetRootNode(stmt, &rootNode1));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &i,
                sizeof(uint32_t)));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcSetIndexKeyName(stmt, "PK"));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testYangSetNodePropertyObj(rootNode1, i,
                GMC_YANG_PROPERTY_OPERATION_MERGE));
            // Add DML操作
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBatchAddDML(batch, stmt));
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBatchExecuteAsync(batch, Trans_Nest_Cb4, user_data));
    }
}
void Trans_Nest_Cb2(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcTransCreateSavepointAsync(user_data->conn, "sp1",
            Trans_Nest_Cb3, user_data));
    }
}
void Trans_Nest_Cb1(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcTransStartAsync(user_data->conn, &g_trxConfig,
            Trans_Nest_Cb2, user_data));
    }
}
void Trans_Nest_Cb(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        YangConnOptionT connOptions = {0};
        connOptions.isOneThreadEpoll = true;
        connOptions.epollRegType = TEST_EPOLLREG_WITH_USD;
        connOptions.epollRegWithUsDFunc = g_epollRegInfoOneThread;
        connOptions.epollFd = &g_epollDataOneThread.userEpollFd;
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestYangGmcConnect(&conn, &stmt,
            GMC_CONN_TYPE_ASYNC, &connOptions));
        user_data->conn = conn;
        user_data->stmt = stmt;
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcUseNamespaceAsync(stmt, "yang",
            Trans_Nest_Cb1, user_data));
    }
}
void Update_Nest_Subtree_Cb1(void *userData, GmcBatchRetT *batchRet, Status status, const char *errMsg);
void Subtree_Nest_Update_Cb1(
    void *userData, GmcFetchRetT *fetchRet, Status status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        uint32_t count = 0;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            userData1->recvNum++;
            uint32_t idx = userData1->lastExpectIdx;
            const GmcYangTreeT **yangTree = NULL;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
            CheckTreeReply(yangTree, count, userData1);
            // 全部获取完以后 才释放FetchRet
            if (isEnd) {
                GmcYangFreeFetchRet(fetchRet);
                if (userData1->nestDeep < MAX_NEST_COUNT4) {
                    userData1->nestDeep++;
                    GmcNodeT *rootNode = NULL;
                    GmcNodeT *rootNode1 = NULL;
                    GmcNodeT *childNode = NULL;
                    GmcBatchT *batch = NULL;
                    GmcStmtT *stmt = NULL;
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcAllocStmt(userData1->conn, &stmt));
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testBatchPrepareAndSetDiff(userData1->conn, &batch));
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testGmcPrepareStmtByLabelName(userData1->stmt, "root0",
                        GMC_OPERATION_MERGE));
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangSetRoot(batch, userData1->stmt));
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcGetRootNode(userData1->stmt, &rootNode));
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangEditChildNode(rootNode, "Con0", GMC_OPERATION_MERGE,
                        &childNode));
                    // Add DML操作
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBatchAddDML(batch, userData1->stmt));
                    // 设置l1(list)
                    for (uint32_t i = 0; i < 50; i++) {
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testGmcPrepareStmtByLabelName(stmt, "l0",
                            GMC_OPERATION_MERGE));
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangBindChild(batch, userData1->stmt, stmt));
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcGetRootNode(stmt, &rootNode1));
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &i,
                            sizeof(uint32_t)));
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcSetIndexKeyName(stmt, "PK"));
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testYangSetNodeProperty(rootNode1, i + userData1->nestDeep,
                            GMC_YANG_PROPERTY_OPERATION_MERGE, true));
                        // Add DML操作
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBatchAddDML(batch, stmt));
                    }
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBatchExecuteAsync(batch, Update_Nest_Subtree_Cb1, userData1));
                    GmcFreeStmt(stmt);
                }
                return;
            }
            // 没有获取完subtree 重新触发回调函数
            userData1->lastExpectIdx = idx + count;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangSubtreeFilterExecuteAsync(userData1->stmt, NULL, fetchRet,
                Subtree_Nest_Update_Cb1, userData1));
        }
    }
}
void Update_Nest_Subtree_Cb1(void *userData, GmcBatchRetT *batchRet, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        if (status != GMERR_REQUEST_TIME_OUT) {
            int ret =
                GmcBatchDeparseRet(batchRet, (uint32_t *)&(user_data->totalNum), (uint32_t *)&(user_data->succNum));
            ASSERT_EQ(GMERR_OK, ret);
        }
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (user_data->nestDeep < MAX_NEST_COUNT4) {
            user_data->nestDeep++;
            GmcNodeT *root = NULL;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK,
                testGmcPrepareStmtByLabelName(user_data->stmt, "root0", GMC_OPERATION_SUBTREE_FILTER));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcGetRootNode(user_data->stmt, &root));
            uint32_t f0 = 2023;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testYangSetField(root, GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t),
                "F0", GMC_YANG_PROPERTY_OPERATION_CREATE));
            GmcSubtreeFilterItemT filter = {
                .rootName = NULL,
                .subtree = {.obj = root},
                .jsonFlag = GMC_JSON_INDENT(4),
                .maxDepth = 0,
                .isLocationFilter = 0,
                .defaultMode = 0,
                .configFlag = 0,
            };
            GmcSubtreeFilterT filters = {
                .filterMode = GMC_FETCH_OBJ,
                .filter = &filter,
            };
            user_data->filterMode = filters.filterMode;
            user_data->lastExpectIdx = 0;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangSubtreeFilterExecuteAsync(user_data->stmt, &filters,
                NULL, Subtree_Nest_Update_Cb1, user_data));
        }
    }
}
void *OperationThread1(void *args)
{
    int ret;
    AsyncUserDataT data = {0};
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt1 = NULL;
    GmcBatchT *batch = NULL;
    uint32_t valueF0;
    EpollThreadDataT epollData = {0};
    ret = createEpollOneThread(&epollData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL;
    GmcNodeT *rootNode1 = NULL;
    GmcNodeT *childNode = NULL;
    GmcNodeT *childNode1 = NULL;
    GmcNodeT *childNode2 = NULL;
    YangConnOptionT connOptions = {0};
    connOptions.timeoutMs = 200000;
    connOptions.isOneThreadEpoll = true;
    connOptions.epollRegType = TEST_EPOLLREG_WITH_USD;
    connOptions.epollRegWithUsDFunc = g_epollRegInfoOneThread;
    connOptions.epollFd = &epollData.userEpollFd;
    ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespaceAsync(stmt, "yang", use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data, 1, true, -1, epollData.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    // 启动事务
    ret = GmcAllocStmt(conn, &stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 启动事务
    ret = GmcTransStartAsync(conn, &g_trxConfig, trans_start_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data, 1, true, -1, epollData.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt, "root0", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(rootNode, "Con0", GMC_OPERATION_MERGE, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // Add DML操作
    ret = GmcBatchAddDML(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置l1(list)
    for (uint32_t i = 0; i < 50; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt1, "l0", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt1, &rootNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt1, "PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_MERGE, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 批处理提交,嵌套四次
    data.stmt = stmt;
    data.conn = conn;
    data.nestDeep = 0;
    ret = GmcBatchExecuteAsync(batch, Update_Nest_Subtree_Cb1, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data, 100, true, -1, epollData.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcBatchDestroy(batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = GmcTransCommitAsync(conn, trans_start_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data, 1, true, -1, epollData.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    // 释放异步连接
    GmcFreeStmt(stmt1);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    closeEpollOneThread(&epollData);
}
void Update_Nest_Subtree_Cb2(void *userData, GmcBatchRetT *batchRet, Status status, const char *errMsg);
void Subtree_Nest_Update_Cb2(
    void *userData, GmcFetchRetT *fetchRet, Status status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        uint32_t count = 0;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            userData1->recvNum++;
            uint32_t idx = userData1->lastExpectIdx;
            const GmcYangTreeT **yangTree = NULL;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
            CheckTreeReply(yangTree, count, userData1);
            // 全部获取完以后 才释放FetchRet
            if (isEnd) {
                GmcYangFreeFetchRet(fetchRet);
                if (userData1->nestDeep < MAX_NEST_COUNT4) {
                    userData1->nestDeep++;
                    GmcNodeT *rootNode = NULL;
                    GmcNodeT *rootNode1 = NULL;
                    GmcNodeT *childNode = NULL;
                    GmcNodeT *childNode1 = NULL;
                    GmcBatchT *batch = NULL;
                    GmcStmtT *stmt = NULL;
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcAllocStmt(userData1->conn, &stmt));
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testBatchPrepareAndSetDiff(userData1->conn, &batch));
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testGmcPrepareStmtByLabelName(userData1->stmt, "root1",
                        GMC_OPERATION_MERGE));
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangSetRoot(batch, userData1->stmt));
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcGetRootNode(userData1->stmt, &rootNode));
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangEditChildNode(rootNode, "Choice1", GMC_OPERATION_MERGE,
                        &childNode));
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangEditChildNode(childNode, "Case1", GMC_OPERATION_MERGE,
                        &childNode1));
                    // Add DML操作
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBatchAddDML(batch, userData1->stmt));
                    // 设置l1(list)
                    for (uint32_t i = 0; i < 50; i++) {
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testGmcPrepareStmtByLabelName(stmt, "l1",
                            GMC_OPERATION_MERGE));
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangBindChild(batch, userData1->stmt, stmt));
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcGetRootNode(stmt, &rootNode1));
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &i,
                            sizeof(uint32_t)));
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcSetIndexKeyName(stmt, "PK"));
                        // Add DML操作
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBatchAddDML(batch, stmt));
                    }
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBatchExecuteAsync(batch, Update_Nest_Subtree_Cb2, userData1));
                    GmcFreeStmt(stmt);
                }
                return;
            }
            // 没有获取完subtree 重新触发回调函数
            userData1->lastExpectIdx = idx + count;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangSubtreeFilterExecuteAsync(userData1->stmt, NULL, fetchRet,
                Subtree_Nest_Update_Cb2, userData1));
        }
    }
}
void Update_Nest_Subtree_Cb2(void *userData, GmcBatchRetT *batchRet, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        if (status != GMERR_REQUEST_TIME_OUT) {
            int ret =
                GmcBatchDeparseRet(batchRet, (uint32_t *)&(user_data->totalNum), (uint32_t *)&(user_data->succNum));
            ASSERT_EQ(GMERR_OK, ret);
        }
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (user_data->nestDeep < MAX_NEST_COUNT4) {
            user_data->nestDeep++;
            GmcNodeT *root = NULL;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK,
                testGmcPrepareStmtByLabelName(user_data->stmt, "root1", GMC_OPERATION_SUBTREE_FILTER));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcGetRootNode(user_data->stmt, &root));
            uint32_t f0 = 2023;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testYangSetField(root, GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t),
                "F0", GMC_YANG_PROPERTY_OPERATION_CREATE));
            GmcSubtreeFilterItemT filter = {
                .rootName = NULL,
                .subtree = {.obj = root},
                .jsonFlag = GMC_JSON_INDENT(4),
                .maxDepth = 0,
                .isLocationFilter = 0,
                .defaultMode = 0,
                .configFlag = 0,
            };
            GmcSubtreeFilterT filters = {
                .filterMode = GMC_FETCH_OBJ,
                .filter = &filter,
            };
            user_data->filterMode = filters.filterMode;
            user_data->lastExpectIdx = 0;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangSubtreeFilterExecuteAsync(user_data->stmt, &filters,
                NULL, Subtree_Nest_Update_Cb2, user_data));
        }
    }
}
void *OperationThread2(void *args)
{
    int ret;
    AsyncUserDataT data = {0};
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt1 = NULL;
    GmcBatchT *batch = NULL;
    uint32_t valueF0;
    EpollThreadDataT epollData = {0};
    ret = createEpollOneThread(&epollData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL;
    GmcNodeT *rootNode1 = NULL;
    GmcNodeT *childNode = NULL;
    GmcNodeT *childNode1 = NULL;
    GmcNodeT *childNode2 = NULL;
    YangConnOptionT connOptions = {0};
    connOptions.timeoutMs = 200000;
    connOptions.isOneThreadEpoll = true;
    connOptions.epollRegType = TEST_EPOLLREG_WITH_USD;
    connOptions.epollRegWithUsDFunc = g_epollRegInfoOneThread;
    connOptions.epollFd = &epollData.userEpollFd;
    ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespaceAsync(stmt, "yang", use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data, 1, true, -1, epollData.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    // 启动事务
    ret = GmcAllocStmt(conn, &stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 启动事务
    ret = GmcTransStartAsync(conn, &g_trxConfig, trans_start_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data, 1, true, -1, epollData.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt, "root1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(rootNode, "Choice1", GMC_OPERATION_MERGE, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(childNode, "Case1", GMC_OPERATION_MERGE, &childNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // Add DML操作
    ret = GmcBatchAddDML(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置l1(list)
    for (uint32_t i = 0; i < 50; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt1, "l1", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt1, &rootNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt1, "PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 批处理提交,嵌套四次
    data.stmt = stmt;
    data.conn = conn;
    data.nestDeep = 0;
    ret = GmcBatchExecuteAsync(batch, Update_Nest_Subtree_Cb2, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data, 100, true, -1, epollData.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcBatchDestroy(batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = GmcTransCommitAsync(conn, trans_start_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data, 1, true, -1, epollData.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    // 释放异步连接
    GmcFreeStmt(stmt1);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    closeEpollOneThread(&epollData);
}
void Update_Nest_Subtree_Cb3(void *userData, GmcBatchRetT *batchRet, Status status, const char *errMsg);
void Subtree_Nest_Update_Cb3(
    void *userData, GmcFetchRetT *fetchRet, Status status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        uint32_t count = 0;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            userData1->recvNum++;
            uint32_t idx = userData1->lastExpectIdx;
            const GmcYangTreeT **yangTree = NULL;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
            CheckTreeReply(yangTree, count, userData1);
            // 全部获取完以后 才释放FetchRet
            if (isEnd) {
                GmcYangFreeFetchRet(fetchRet);
                if (userData1->nestDeep < MAX_NEST_COUNT4) {
                    userData1->nestDeep++;
                    GmcNodeT *rootNode = NULL;
                    GmcNodeT *rootNode1 = NULL;
                    GmcNodeT *childNode = NULL;
                    GmcNodeT *childNode1 = NULL;
                    GmcBatchT *batch = NULL;
                    GmcStmtT *stmt = NULL;
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcAllocStmt(userData1->conn, &stmt));
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testBatchPrepareAndSetDiff(userData1->conn, &batch));
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testGmcPrepareStmtByLabelName(userData1->stmt, "root2",
                        GMC_OPERATION_MERGE));
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangSetRoot(batch, userData1->stmt));
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcGetRootNode(userData1->stmt, &rootNode));
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangEditChildNode(rootNode, "Choice2", GMC_OPERATION_MERGE,
                        &childNode));
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangEditChildNode(childNode, "Case2", GMC_OPERATION_MERGE,
                        &childNode1));
                    // Add DML操作
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBatchAddDML(batch, userData1->stmt));
                    // 设置l1(list)
                    for (uint32_t i = 0; i < 50; i++) {
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testGmcPrepareStmtByLabelName(stmt, "l2",
                            GMC_OPERATION_MERGE));
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangBindChild(batch, userData1->stmt, stmt));
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcGetRootNode(stmt, &rootNode1));
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &i,
                            sizeof(uint32_t)));
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcSetIndexKeyName(stmt, "PK"));
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testYangSetNodeProperty(rootNode1, i + userData1->nestDeep,
                            GMC_YANG_PROPERTY_OPERATION_MERGE, true));
                        // Add DML操作
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBatchAddDML(batch, stmt));
                    }
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBatchExecuteAsync(batch, Update_Nest_Subtree_Cb3, userData1));
                    GmcFreeStmt(stmt);
                }
                return;
            }
            // 没有获取完subtree 重新触发回调函数
            userData1->lastExpectIdx = idx + count;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangSubtreeFilterExecuteAsync(userData1->stmt, NULL, fetchRet,
                Subtree_Nest_Update_Cb1, userData1));
        }
    }
}
void Update_Nest_Subtree_Cb3(void *userData, GmcBatchRetT *batchRet, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        if (status != GMERR_REQUEST_TIME_OUT) {
            int ret =
                GmcBatchDeparseRet(batchRet, (uint32_t *)&(user_data->totalNum), (uint32_t *)&(user_data->succNum));
            ASSERT_EQ(GMERR_OK, ret);
        }
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (user_data->nestDeep < MAX_NEST_COUNT4) {
            user_data->nestDeep++;
            GmcNodeT *root = NULL;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK,
                testGmcPrepareStmtByLabelName(user_data->stmt, "root2", GMC_OPERATION_SUBTREE_FILTER));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcGetRootNode(user_data->stmt, &root));
            uint32_t f0 = 2023;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testYangSetField(root, GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t),
                "F0", GMC_YANG_PROPERTY_OPERATION_CREATE));
            GmcSubtreeFilterItemT filter = {
                .rootName = NULL,
                .subtree = {.obj = root},
                .jsonFlag = GMC_JSON_INDENT(4),
                .maxDepth = 0,
                .isLocationFilter = 0,
                .defaultMode = 0,
                .configFlag = 0,
            };
            GmcSubtreeFilterT filters = {
                .filterMode = GMC_FETCH_OBJ,
                .filter = &filter,
            };
            user_data->filterMode = filters.filterMode;
            user_data->lastExpectIdx = 0;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangSubtreeFilterExecuteAsync(user_data->stmt, &filters,
                NULL, Subtree_Nest_Update_Cb3, user_data));
        }
    }
}
void *OperationThread3(void *args)
{
    int ret;
    AsyncUserDataT data = {0};
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt1 = NULL;
    GmcBatchT *batch = NULL;
    uint32_t valueF0;
    EpollThreadDataT epollData = {0};
    ret = createEpollOneThread(&epollData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL;
    GmcNodeT *rootNode1 = NULL;
    GmcNodeT *childNode = NULL;
    GmcNodeT *childNode1 = NULL;
    GmcNodeT *childNode2 = NULL;
    YangConnOptionT connOptions = {0};
    connOptions.timeoutMs = 200000;
    connOptions.isOneThreadEpoll = true;
    connOptions.epollRegType = TEST_EPOLLREG_WITH_USD;
    connOptions.epollRegWithUsDFunc = g_epollRegInfoOneThread;
    connOptions.epollFd = &epollData.userEpollFd;
    ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespaceAsync(stmt, "yang", use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data, 1, true, -1, epollData.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    // 启动事务
    ret = GmcAllocStmt(conn, &stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 启动事务
    ret = GmcTransStartAsync(conn, &g_trxConfig, trans_start_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data, 1, true, -1, epollData.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt, "root2", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(rootNode, "Choice2", GMC_OPERATION_MERGE, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(childNode, "Case2", GMC_OPERATION_MERGE, &childNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // Add DML操作
    ret = GmcBatchAddDML(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置l1(list)
    for (uint32_t i = 0; i < 50; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt1, "l2", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt1, &rootNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt1, "PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_MERGE, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 批处理提交,嵌套四次
    data.stmt = stmt;
    data.conn = conn;
    data.nestDeep = 0;
    ret = GmcBatchExecuteAsync(batch, Update_Nest_Subtree_Cb3, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data, 100, true, -1, epollData.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcBatchDestroy(batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = GmcTransCommitAsync(conn, trans_start_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data, 1, true, -1, epollData.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    // 释放异步连接
    GmcFreeStmt(stmt1);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    closeEpollOneThread(&epollData);
}
void *EpollThreadFunc060(void *args)
{
    const int epollTimeoutMs = 1000;
    struct epoll_event events[2048];
    int *epollFd = (int *)args;

    if (*epollFd < 0) {
        return NULL;
    }

    while (*epollFd >= 0) {
        int32_t fdCount = epoll_wait(*epollFd, events, 2048, epollTimeoutMs);
        while (fdCount > 0) {
            --fdCount;
            if (isClose && events[fdCount].data.fd == g_connFd) {
                AW_FUN_Log(LOG_INFO, "fd response was changed to timerFd, fd is %d\n", g_timeoutTimerFd);
                GmcHandleRWEvent(g_timeoutTimerFd, events[fdCount].events);
                continue;
            }
            GmcHandleRWEvent(events[fdCount].data.fd, events[fdCount].events);
        }
    }
    return NULL;
}
static void CreateAndStartEpollThread(pthread_t *threadId, int *epollFd)
{
    *epollFd = epoll_create(1024);
    ASSERT_LE(0, *epollFd);
    int ret = pthread_create(threadId, NULL, EpollThreadFunc060, epollFd);
    ASSERT_EQ(0, ret);
    g_epollData.createBgThread = true;
}
void CreateAsyncConnectionWithEpollReg(GmcConnT **connAsync, GmcEpollRegWithUserDataT func)
{
    GmcConnOptionsT *connOptions;
    Status ret = GmcConnOptionsCreate(&connOptions);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcConnOptionsSetServerLocator(connOptions, g_connServer);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcConnOptionsSetAsyncTimeoutThreshold(connOptions, 1000);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcConnOptionsSetEpollRegFuncWithUserData(connOptions, func, &g_connFd);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcConnect(GMC_CONN_TYPE_ASYNC, connOptions, connAsync);
    ASSERT_EQ(GMERR_OK, ret);
    GmcConnOptionsDestroy(connOptions);
}
static void StopAndDestroyEpollThread(pthread_t threadId, int *epollFd)
{
    int fd = *epollFd;
    *epollFd = -1;  // tell EpollThreadFunc to exit
    pthread_join(threadId, NULL);
    close(fd);
}
void InvokeAsyncDDLCbTimeOut(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (user_data->nestDeep < MAX_NEST_COUNT1) {
            user_data->nestDeep++;
            GmcAsyncRequestDoneContextT context;
            context.createVertexWithNameCb = InvokeAsyncDDLCbTimeOut;
            context.userData = user_data;
            char vertexLabelName[20] = {0};
            int res = sprintf(vertexLabelName, "vertexLabel%d", user_data->nestDeep);
            ASSERT_LT(0, res);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcCreateVertexLabelWithNameAsync(user_data->stmt, g_vertexLabelSchema,
                NULL, vertexLabelName, &context));
        }
        switch (user_data->recvNum) {
            case 2: {
                // 在第三次完成建表制造超时
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, status);
                isClose = true;
                break;
            }
            case 3: {
                AW_MACRO_EXPECT_EQ_INT(GMERR_REQUEST_TIME_OUT, status);
                isClose = false;
                break;
            }
            default:
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, status);
                return;
        }
    }
}
static int EpollRegWithUserData(int fd, GmcEpollCtlTypeE type, uint32_t events, void *userData)
{
    *(int *)userData = fd;
    AW_FUN_Log(LOG_INFO, "epollfd is %d, epoll reg fd is %d \n", g_epollFd, fd);
    int ret;
    switch (type) {
        case GMC_EPOLL_ADD: {
            struct epoll_event event;
            event.data.fd = fd;
            event.events = events;
            ret = epoll_ctl(g_epollFd, EPOLL_CTL_ADD, fd, &event);
            if (ret != 0) {
                AW_FUN_Log(LOG_INFO, "error is %d, %d, %s \n", ret, errno, dlerror());
            }
            return ret;
        }
        case GMC_EPOLL_DEL:
            return epoll_ctl(g_epollFd, EPOLL_CTL_DEL, fd, NULL);
        default:
            return -1;
    }
}
void DML_Nest_DCL_Cb(void *userData, GmcBatchRetT *batchRet, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        if (status != GMERR_REQUEST_TIME_OUT) {
            int ret =
                GmcBatchDeparseRet(batchRet, (uint32_t *)&(user_data->totalNum), (uint32_t *)&(user_data->succNum));
            ASSERT_EQ(GMERR_OK, ret);
        }
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (status == GMERR_PRIMARY_KEY_VIOLATION) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcTransRollBackAsync(user_data->conn,
            trans_commit_callback, user_data));
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcTransCommitAsync(user_data->conn,
                trans_commit_callback, user_data));
        }
    }
}
void DML_Nest_Connect_Cb(void *userData, GmcBatchRetT *batchRet, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        if (status != GMERR_REQUEST_TIME_OUT) {
            int ret =
                GmcBatchDeparseRet(batchRet, (uint32_t *)&(user_data->totalNum), (uint32_t *)&(user_data->succNum));
            ASSERT_EQ(GMERR_OK, ret);
        }
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        GmcConnT *conn[4];
        GmcStmtT *stmt[4];
        YangConnOptionT connOptions = {0};
        connOptions.isOneThreadEpoll = true;
        connOptions.epollRegType = TEST_EPOLLREG_WITH_USD;
        connOptions.epollRegWithUsDFunc = g_epollRegInfoOneThread;
        connOptions.epollFd = &g_epollDataOneThread.userEpollFd;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestYangGmcConnect(&conn[0], &stmt[0], GMC_CONN_TYPE_ASYNC,
            &connOptions));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestYangGmcConnect(&conn[1], &stmt[1], GMC_CONN_TYPE_SYNC,
            &connOptions));
        connOptions.isLobConn = true;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestYangGmcConnect(&conn[2], &stmt[2], GMC_CONN_TYPE_ASYNC,
            &connOptions));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestYangGmcConnect(&conn[3], &stmt[3], GMC_CONN_TYPE_SYNC,
            &connOptions));
        for (int i = 0; i < 4; i++) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testGmcDisconnect(conn[i], stmt[i]));
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcTransCommitAsync(user_data->conn,
            trans_commit_callback, user_data));
    }
}
void FetchCataCallback(void *userData, int32_t status, const char *errMsg, uint32_t errIndex)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->expSuccNum = errIndex;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                printf("errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                printf("expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
    }
}
void DML_Nest_FetchCata_Cb(void *userData, GmcBatchRetT *batchRet, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        if (status != GMERR_REQUEST_TIME_OUT) {
            int ret =
                GmcBatchDeparseRet(batchRet, (uint32_t *)&(user_data->totalNum), (uint32_t *)&(user_data->succNum));
            ASSERT_EQ(GMERR_OK, ret);
        }
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        const char *vertexNames[2] = {"root0", "l0"};
        const char *edgeNames[1] = {"L0"};
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcPrefetchVertexLabelsAsync(user_data->stmt, vertexNames, 2,
            FetchCataCallback, user_data));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcPrefetchEdgeLabelsAsync(user_data->stmt, edgeNames, 1,
            FetchCataCallback, user_data));
    }
}
void FetchCataNestDmlCb(void *userData, int32_t status, const char *errMsg, uint32_t errIndex)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->expSuccNum = errIndex;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                printf("errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                printf("expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        GmcNodeT *rootNode = NULL;
        GmcNodeT *rootNode1 = NULL;
        GmcNodeT *childNode = NULL;
        GmcBatchT *batch = NULL;
        GmcStmtT *stmt = NULL;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcAllocStmt(user_data->conn, &stmt));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testBatchPrepareAndSetDiff(user_data->conn, &batch));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testGmcPrepareStmtByLabelName(user_data->stmt, "root0",
            GMC_OPERATION_MERGE));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangSetRoot(batch, user_data->stmt));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcGetRootNode(user_data->stmt, &rootNode));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangEditChildNode(rootNode, "Con0", GMC_OPERATION_MERGE,
            &childNode));
        // Add DML操作
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBatchAddDML(batch, user_data->stmt));
        // 设置l1(list)
        for (uint32_t i = 0 + user_data->nestDeep * 50; i < 50 + user_data->nestDeep * 50; i++) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testGmcPrepareStmtByLabelName(stmt, "l0", GMC_OPERATION_MERGE));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangBindChild(batch, user_data->stmt, stmt));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcGetRootNode(stmt, &rootNode1));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &i,
                sizeof(uint32_t)));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcSetIndexKeyName(stmt, "PK"));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testYangSetNodeProperty(rootNode1, i,
                GMC_YANG_PROPERTY_OPERATION_MERGE, true));
            // Add DML操作
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBatchAddDML(batch, stmt));
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBatchExecuteAsync(batch, batch_execute_callback, user_data));
    }
}

void FetchCataNestFetchCataCb(void *userData, int32_t status, const char *errMsg, uint32_t errIndex)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->expSuccNum = errIndex;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                printf("errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                printf("expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        const char *edgeNames[4] = {"L0", "L1", "L2", "L3"};
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcPrefetchEdgeLabelsAsync(user_data->stmt, edgeNames, 4,
            FetchCataCallback, user_data));
    }
}
void DQL_Nest_DDL_Cb(
    void *userData, GmcFetchRetT *fetchRet, Status status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        uint32_t count = 0;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            userData1->recvNum++;
            uint32_t idx = userData1->lastExpectIdx;
            const GmcYangTreeT **yangTree = NULL;
            ASSERT_EQ(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
            TestCheckYangTree(userData1->stmt, yangTree, idx, count, *userData1->expectDiff);
            // 全部获取完以后 才释放FetchRet
            if (isEnd) {
                GmcYangFreeFetchRet(fetchRet);
                GmcNspCfgT yangNspCfg = {};
                yangNspCfg.tablespaceName = NULL;
                yangNspCfg.namespaceName = "yang1";
                yangNspCfg.userName = "abc";
                yangNspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcCreateNamespaceWithCfgAsync(userData1->stmt,
                    &yangNspCfg, create_namespace_callback, userData1));
                return;
            }
            // 没有获取完diff 重新触发回调函数
            userData1->lastExpectIdx = idx + count;
            ASSERT_EQ(GMERR_OK, GmcYangFetchDiffExecuteAsync(userData1->stmt, NULL,
                FetchDiff_Nest_TransCommit_Cb, userData1));
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_TRANS_MODE_MISMATCH, status);
            userData1->recvNum++;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcUseNamespaceAsync(userData1->stmt,
                "yang", create_namespace_callback, userData1));
        }
    }
}
void DQL_Nest_Connect_Cb(
    void *userData, GmcFetchRetT *fetchRet, Status status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        uint32_t count = 0;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            userData1->recvNum++;
            uint32_t idx = userData1->lastExpectIdx;
            const GmcYangTreeT **yangTree = NULL;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
            CheckTreeReply(yangTree, count, userData1);
            // 全部获取完以后 才释放FetchRet
            if (isEnd) {
                GmcYangFreeFetchRet(fetchRet);
                GmcConnT *conn[4];
                GmcStmtT *stmt[4];
                YangConnOptionT connOptions = {0};
                connOptions.isOneThreadEpoll = true;
                connOptions.epollRegType = TEST_EPOLLREG_WITH_USD;
                connOptions.epollRegWithUsDFunc = g_epollRegInfoOneThread;
                connOptions.epollFd = &g_epollDataOneThread.userEpollFd;
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestYangGmcConnect(&conn[0], &stmt[0], GMC_CONN_TYPE_ASYNC,
                    &connOptions));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestYangGmcConnect(&conn[1], &stmt[1], GMC_CONN_TYPE_SYNC,
                    &connOptions));
                connOptions.isLobConn = true;
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestYangGmcConnect(&conn[2], &stmt[2], GMC_CONN_TYPE_ASYNC,
                    &connOptions));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestYangGmcConnect(&conn[3], &stmt[3], GMC_CONN_TYPE_SYNC,
                    &connOptions));
                for (int i = 0; i < 4; i++) {
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testGmcDisconnect(conn[i], stmt[i]));
                }
                return;
            }
            // 没有获取完subtree 重新触发回调函数
            userData1->lastExpectIdx = idx + count;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangSubtreeFilterExecuteAsync(userData1->stmt, NULL, fetchRet,
                FetchSubtree_Cb, userData1));
        }
    }
}
void DQL_Nest_FetchCata_Cb(
    void *userData, GmcFetchRetT *fetchRet, Status status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        uint32_t count = 0;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            userData1->recvNum++;
            uint32_t idx = userData1->lastExpectIdx;
            const GmcYangTreeT **yangTree = NULL;
            ASSERT_EQ(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
            TestCheckYangTree(userData1->stmt, yangTree, idx, count, *userData1->expectDiff);
            // 全部获取完以后 才释放FetchRet
            if (isEnd) {
                GmcYangFreeFetchRet(fetchRet);
                const char *vertexNames[2] = {"root0", "l0"};
                const char *edgeNames[1] = {"L0"};
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcPrefetchVertexLabelsAsync(userData1->stmt, vertexNames, 2,
                    FetchCataCallback, userData1));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcPrefetchEdgeLabelsAsync(userData1->stmt, edgeNames, 1,
                    FetchCataCallback, userData1));
                return;
            }
            // 没有获取完diff 重新触发回调函数
            userData1->lastExpectIdx = idx + count;
            ASSERT_EQ(GMERR_OK, GmcYangFetchDiffExecuteAsync(userData1->stmt, NULL,
                FetchDiff_Nest_Subtree_Cb, userData1));
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_TRANS_MODE_MISMATCH, status);
            userData1->recvNum++;
            const char *vertexNames[2] = {"root0", "l0"};
            const char *edgeNames[1] = {"L0"};
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcPrefetchVertexLabelsAsync(userData1->stmt, vertexNames, 2,
                FetchCataCallback, userData1));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcPrefetchEdgeLabelsAsync(userData1->stmt, edgeNames, 1,
                FetchCataCallback, userData1));
        }
    }
}
void DCL_Nest_DQL_Cb(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangFetchDiffExecuteAsync(user_data->stmt, NULL,
            Fetch_Diff_Cb, user_data));
    }
}
void DCL_Nest_Connect_Cb(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        GmcConnT *conn[4];
        GmcStmtT *stmt[4];
        YangConnOptionT connOptions = {0};
        connOptions.isOneThreadEpoll = true;
        connOptions.epollRegType = TEST_EPOLLREG_WITH_USD;
        connOptions.epollRegWithUsDFunc = g_epollRegInfoOneThread;
        connOptions.epollFd = &g_epollDataOneThread.userEpollFd;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestYangGmcConnect(&conn[0], &stmt[0], GMC_CONN_TYPE_ASYNC,
            &connOptions));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestYangGmcConnect(&conn[1], &stmt[1], GMC_CONN_TYPE_SYNC,
            &connOptions));
        connOptions.isLobConn = true;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestYangGmcConnect(&conn[2], &stmt[2], GMC_CONN_TYPE_ASYNC,
            &connOptions));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestYangGmcConnect(&conn[3], &stmt[3], GMC_CONN_TYPE_SYNC,
            &connOptions));
        for (int i = 0; i < 4; i++) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testGmcDisconnect(conn[i], stmt[i]));
        }
    }
}
void DCL_Nest_FetchCata_Cb(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        const char *vertexNames[2] = {"root0", "l0"};
        const char *edgeNames[1] = {"L0"};
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcPrefetchVertexLabelsAsync(user_data->stmt, vertexNames, 2,
            FetchCataCallback, user_data));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcPrefetchEdgeLabelsAsync(user_data->stmt, edgeNames, 1,
            FetchCataCallback, user_data));
    }
}
void DDL_Nest_DQL_Cb(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangFetchDiffExecuteAsync(user_data->stmt, NULL,
            Fetch_Diff_Cb, user_data));
    }
}
void DDL_Nest_DCL_Cb(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (status) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcTransRollBackAsync(user_data->conn, trans_commit_callback,
                user_data));
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcTransCreateSavepointAsync(user_data->conn, "sp1",
                trans_commit_callback, user_data));
        }
    }
}
void DDL_Nest_Connect_Cb(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        GmcConnT *conn[4];
        GmcStmtT *stmt[4];
        YangConnOptionT connOptions = {0};
        connOptions.isOneThreadEpoll = true;
        connOptions.epollRegType = TEST_EPOLLREG_WITH_USD;
        connOptions.epollRegWithUsDFunc = g_epollRegInfoOneThread;
        connOptions.epollFd = &g_epollDataOneThread.userEpollFd;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestYangGmcConnect(&conn[0], &stmt[0], GMC_CONN_TYPE_ASYNC,
            &connOptions));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestYangGmcConnect(&conn[1], &stmt[1], GMC_CONN_TYPE_SYNC,
            &connOptions));
        connOptions.isLobConn = true;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestYangGmcConnect(&conn[2], &stmt[2], GMC_CONN_TYPE_ASYNC,
            &connOptions));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestYangGmcConnect(&conn[3], &stmt[3], GMC_CONN_TYPE_SYNC,
            &connOptions));
        for (int i = 0; i < 4; i++) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testGmcDisconnect(conn[i], stmt[i]));
        }
    }
}
void DDL_Nest_FetchCata_Cb(void *userData, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        const char *vertexNames[2] = {"root0", "l0"};
        const char *edgeNames[1] = {"L0"};
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcPrefetchVertexLabelsAsync(user_data->stmt, vertexNames, 2,
            FetchCataCallback, user_data));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcPrefetchEdgeLabelsAsync(user_data->stmt, edgeNames, 1,
            FetchCataCallback, user_data));
    }
}
void FetchCataNestDqlCb(void *userData, int32_t status, const char *errMsg, uint32_t errIndex)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->expSuccNum = errIndex;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                printf("errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                printf("expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangFetchDiffExecuteAsync(user_data->stmt, NULL,
            Fetch_Diff_Cb, user_data));
    }
}
void FetchCataNestDclCb(void *userData, int32_t status, const char *errMsg, uint32_t errIndex)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->expSuccNum = errIndex;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                printf("errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                printf("expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (status) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcTransRollBackAsync(user_data->conn,
                trans_commit_callback, user_data));
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcTransCreateSavepointAsync(user_data->conn, "sp1",
                trans_commit_callback, user_data));
        }
    }
}
void FetchCataNestConnectCb(void *userData, int32_t status, const char *errMsg, uint32_t errIndex)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->expSuccNum = errIndex;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                printf("errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                printf("expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        GmcConnT *conn[4];
        GmcStmtT *stmt[4];
        YangConnOptionT connOptions = {0};
        connOptions.isOneThreadEpoll = true;
        connOptions.epollRegType = TEST_EPOLLREG_WITH_USD;
        connOptions.epollRegWithUsDFunc = g_epollRegInfoOneThread;
        connOptions.epollFd = &g_epollDataOneThread.userEpollFd;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestYangGmcConnect(&conn[0], &stmt[0], GMC_CONN_TYPE_ASYNC,
            &connOptions));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestYangGmcConnect(&conn[1], &stmt[1], GMC_CONN_TYPE_SYNC,
            &connOptions));
        connOptions.isLobConn = true;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestYangGmcConnect(&conn[2], &stmt[2], GMC_CONN_TYPE_ASYNC,
            &connOptions));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestYangGmcConnect(&conn[3], &stmt[3], GMC_CONN_TYPE_SYNC,
            &connOptions));
        for (int i = 0; i < 4; i++) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testGmcDisconnect(conn[i], stmt[i]));
        }
    }
}
void FetchCataNestDdlCb(void *userData, int32_t status, const char *errMsg, uint32_t errIndex)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->expSuccNum = errIndex;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                printf("errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                printf("expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (status) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcDropNamespaceAsync(user_data->stmt, "yang",
                drop_vertex_label_callback, user_data));
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcDropEdgeLabelAsync(user_data->stmt, "L0",
                drop_vertex_label_callback, user_data));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcDropVertexLabelAsync(user_data->stmt, "root0",
                drop_vertex_label_callback, user_data));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcDropVertexLabelAsync(user_data->stmt, "l0",
                drop_vertex_label_callback, user_data));
        }
    }
}
void CreateNsTimeOutCb(void *userData, int32_t status, const char *errMsg)
{
    sleep(5);
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if ((user_data->lastError != NULL) && (errMsg != NULL)) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcUseNamespaceAsync(user_data->stmt, "yang",
            drop_vertex_label_callback, user_data));
    }
}
void DQL_Nest_DML_Cb(
    void *userData, GmcFetchRetT *fetchRet, Status status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        uint32_t count = 0;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            userData1->recvNum++;
            uint32_t idx = userData1->lastExpectIdx;
            const GmcYangTreeT **yangTree = NULL;
            ASSERT_EQ(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
            TestCheckYangTree(userData1->stmt, yangTree, idx, count, *userData1->expectDiff);
            // 全部获取完以后 才释放FetchRet
            if (isEnd) {
                GmcYangFreeFetchRet(fetchRet);
                GmcNspCfgT yangNspCfg = {};
                yangNspCfg.tablespaceName = NULL;
                yangNspCfg.namespaceName = "yang1";
                yangNspCfg.userName = "abc";
                yangNspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcCreateNamespaceWithCfgAsync(userData1->stmt,
                    &yangNspCfg, create_namespace_callback, userData1));
                return;
            }
            // 没有获取完diff 重新触发回调函数
            userData1->lastExpectIdx = idx + count;
            ASSERT_EQ(GMERR_OK, GmcYangFetchDiffExecuteAsync(userData1->stmt, NULL,
                FetchDiff_Nest_TransCommit_Cb, userData1));
        } else {
            userData1->recvNum++;
            AW_MACRO_EXPECT_EQ_INT(GMERR_TRANS_MODE_MISMATCH, status);
            GmcNodeT *rootNode = NULL;
            GmcBatchT *batch = NULL;
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testBatchPrepareAndSetDiff(userData1->conn, &batch));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, testGmcPrepareStmtByLabelName(userData1->stmt, "root0",
                GMC_OPERATION_REMOVE_GRAPH));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangSetRoot(batch, userData1->stmt));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcGetRootNode(userData1->stmt, &rootNode));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBatchAddDML(batch, userData1->stmt));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcBatchExecuteAsync(batch, batch_execute_callback, userData1));
        }
    }
}
#endif
