/*
    李文海 lwx1068802
    2023/7/20
    树模型DDL支持xpath解析：基础场景
*/
#include "tools.h"

class xpath_analyse_normal_test : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void xpath_analyse_normal_test::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/start.sh");
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("mkdir actual");
}

void xpath_analyse_normal_test::TearDownTestCase()
{
    system("rm -rf actual");
}

void xpath_analyse_normal_test::SetUp()
{
    int ret;
    AsyncUserDataT data = {0};

    // 异步建连
    ret = testGmcConnect(&g_connAsync, &g_stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步创建namespace
    GmcDropNamespace(g_stmtAsync, g_namespace);
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = g_namespace;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观
    GmcDropNamespace(g_stmtAsync, g_namespace);
    ret = GmcCreateNamespaceWithCfgAsync(g_stmtAsync, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcUseNamespaceAsync(g_stmtAsync, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    AW_CHECK_LOG_BEGIN();
}

void xpath_analyse_normal_test::TearDown()
{
    int ret;
    AsyncUserDataT data = {0};
    AW_CHECK_LOG_END();

    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmtAsync, g_namespace, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 断连
    ret = testGmcDisconnect(g_connAsync, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcDetachAllShmSeg();
	testEnvClean();
}

// 1.所有ID/PID以外的字段为leafref类型且xpath可解析且存在，建表
TEST_F(xpath_analyse_normal_test, Yang_059_001)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_1.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_1.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(true, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(0, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_001\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_001 = NULL, *actual_001 = NULL;
    readJanssonFile("./expect/expect_001", &expect_001);
    EXPECT_NE((void *)NULL, expect_001);
    readJanssonFile("./actual/actual_001", &actual_001);
    EXPECT_NE((void *)NULL, actual_001);

    EXPECT_TRUE(testYangJsonIsEqual(actual_001, expect_001));
    free(expect_001);
    free(actual_001);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 3.所有ID/PID以外的字段为leafref类型且xpath为空串""，建表
TEST_F(xpath_analyse_normal_test, Yang_059_002)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SYNTAX_ERROR);
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_3.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_3.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(false, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(15, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_002\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_002 = NULL, *actual_002 = NULL;
    readJanssonFile("./expect/expect_002", &expect_002);
    EXPECT_NE((void *)NULL, expect_002);
    readJanssonFile("./actual/actual_002", &actual_002);
    EXPECT_NE((void *)NULL, actual_002);

    EXPECT_TRUE(testYangJsonIsEqual(actual_002, expect_002));
    free(expect_002);
    free(actual_002);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 4.所有ID/PID以外的字段为leafref类型且缺省xpath，建表
TEST_F(xpath_analyse_normal_test, Yang_059_003)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_4.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_4.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, data.status);

    free(mainJson);
    free(listJson);
#endif
}

// 5.所有ID/PID以外的字段为leafref类型且xpath全部无法解析（如乱码），建表
TEST_F(xpath_analyse_normal_test, Yang_059_004)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SYNTAX_ERROR);
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_5.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_5.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(false, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(15, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_004\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_004 = NULL, *actual_004 = NULL;
    readJanssonFile("./expect/expect_004", &expect_004);
    EXPECT_NE((void *)NULL, expect_004);
    readJanssonFile("./actual/actual_004", &actual_004);
    EXPECT_NE((void *)NULL, actual_004);

    EXPECT_TRUE(testYangJsonIsEqual(actual_004, expect_004));
    free(expect_004);
    free(actual_004);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 6.1.所有ID/PID以外的字段为leafref类型且xpath可解析且存在，但前/后使用空格、Tab、和各类转义字符（“如\n，\b等”），建表
// 预期转义字符都不会转义，空格、Tab都不影响解析
TEST_F(xpath_analyse_normal_test, Yang_059_005)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SYNTAX_ERROR);
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_6_1.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_6_1.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(false, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(8, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_005\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_005 = NULL, *actual_005 = NULL;
    readJanssonFile("./expect/expect_005", &expect_005);
    EXPECT_NE((void *)NULL, expect_005);
    readJanssonFile("./actual/actual_005", &actual_005);
    EXPECT_NE((void *)NULL, actual_005);

    EXPECT_TRUE(testYangJsonIsEqual(actual_005, expect_005));
    free(expect_005);
    free(actual_005);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 6.2.所有ID/PID以外的字段为leafref类型且xpath可解析且存在，但前/后使用换行、\0，建表
TEST_F(xpath_analyse_normal_test, Yang_059_006)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_INVALID_JSON_CONTENT);
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_6_2.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_JSON_CONTENT, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_6_2.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_JSON_CONTENT, data.status);

    free(mainJson);
    free(listJson);
#endif
}

// 7.所有ID/PID以外的字段为leafref类型，各类节点下xpath部分正确，建表
TEST_F(xpath_analyse_normal_test, Yang_059_007)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SYNTAX_ERROR);
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_7.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_7.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(false, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(5, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_007\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_007 = NULL, *actual_007 = NULL;
    readJanssonFile("./expect/expect_007", &expect_007);
    EXPECT_NE((void *)NULL, expect_007);
    readJanssonFile("./actual/actual_007", &actual_007);
    EXPECT_NE((void *)NULL, actual_007);

    EXPECT_TRUE(testYangJsonIsEqual(actual_007, expect_007));
    free(expect_007);
    free(actual_007);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 8.所有ID/PID以外的字段为leafref类型且xpath路径存在但绝对/相对路径长度达到3k，建表
TEST_F(xpath_analyse_normal_test, Yang_059_008)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SYNTAX_ERROR);
    int ret = 0;
    // 建表
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, g_leafrefMain3K, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcCreateVertexLabelAsync(g_stmtAsync, g_leafrefList3K, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(true, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(0, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_008\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_008 = NULL, *actual_008 = NULL;
    readJanssonFile("./expect/expect_008", &expect_008);
    EXPECT_NE((void *)NULL, expect_008);
    readJanssonFile("./actual/actual_008", &actual_008);
    EXPECT_NE((void *)NULL, actual_008);

    EXPECT_TRUE(testYangJsonIsEqual(actual_008, expect_008));
    free(expect_008);
    free(actual_008);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(edgeJson);
#endif
}

// 9.所有ID/PID以外的字段为leafref类型且xpath路径存在但绝对/相对路径长度超过3k，建表
TEST_F(xpath_analyse_normal_test, Yang_059_009)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_INVALID_PROPERTY);
    int ret = 0;
    // 建表
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, g_leafrefMainOver3K, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, data.status);

    ret = GmcCreateVertexLabelAsync(g_stmtAsync, g_leafrefListOver3K, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, data.status);
#endif
}

// 10.所有ID/PID以外的字段为8条must，choice/case以外的节点为8条must，每条xpath为简单bool值（如true、1>2等），建表
TEST_F(xpath_analyse_normal_test, Yang_059_010)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_10.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_10.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(true, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(0, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_010\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_010 = NULL, *actual_010 = NULL;
    readJanssonFile("./expect/expect_010", &expect_010);
    EXPECT_NE((void *)NULL, expect_010);
    readJanssonFile("./actual/actual_010", &actual_010);
    EXPECT_NE((void *)NULL, actual_010);

    EXPECT_TRUE(testYangJsonIsEqual(actual_010, expect_010));
    free(expect_010);
    free(actual_010);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 11.所有ID/PID以外的字段为8条must，choice/case以外的节点为8条must，每条xpath为非bool值（3、1+2），建表
TEST_F(xpath_analyse_normal_test, Yang_059_011)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SYNTAX_ERROR);
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_11.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_11.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(true, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(0, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_011\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_011 = NULL, *actual_011 = NULL;
    readJanssonFile("./expect/expect_011", &expect_011);
    EXPECT_NE((void *)NULL, expect_011);
    readJanssonFile("./actual/actual_011", &actual_011);
    EXPECT_NE((void *)NULL, actual_011);

    EXPECT_TRUE(testYangJsonIsEqual(actual_011, expect_011));
    free(expect_011);
    free(actual_011);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 12.所有ID/PID以外的字段为8条must，choice/case以外的节点为8条must，每条xpath为空串""，建表
TEST_F(xpath_analyse_normal_test, Yang_059_012)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SYNTAX_ERROR);
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_12.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_12.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(false, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(72, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_012\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_012 = NULL, *actual_012 = NULL;
    readJanssonFile("./expect/expect_012", &expect_012);
    EXPECT_NE((void *)NULL, expect_012);
    readJanssonFile("./actual/actual_012", &actual_012);
    EXPECT_NE((void *)NULL, actual_012);

    EXPECT_TRUE(testYangJsonIsEqual(actual_012, expect_012));
    free(expect_012);
    free(actual_012);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 13.所有ID/PID以外的字段为8条must，choice/case以外的节点为8条must，缺省xpath，建表
TEST_F(xpath_analyse_normal_test, Yang_059_013)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_13.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_13.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, data.status);

    free(mainJson);
    free(listJson);
#endif
}

// 14.所有ID/PID以外的字段为8条must，choice/case以外的节点为8条must，xpath全部不能解析（如乱码），建表
TEST_F(xpath_analyse_normal_test, Yang_059_014)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SYNTAX_ERROR);
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_14.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_14.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(false, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(72, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_014\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_014 = NULL, *actual_014 = NULL;
    readJanssonFile("./expect/expect_014", &expect_014);
    EXPECT_NE((void *)NULL, expect_014);
    readJanssonFile("./actual/actual_014", &actual_014);
    EXPECT_NE((void *)NULL, actual_014);

    EXPECT_TRUE(testYangJsonIsEqual(actual_014, expect_014));
    free(expect_014);
    free(actual_014);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 15.所有ID/PID以外的字段为8条must，choice/case以外的节点为8条must，xpath可解析，但前/后使用空格、Tab、和各类转义字符（“如\n，\b等”），建表
TEST_F(xpath_analyse_normal_test, Yang_059_015)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SYNTAX_ERROR);
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_15.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_15.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(false, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(37, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_015\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_015 = NULL, *actual_015 = NULL;
    readJanssonFile("./expect/expect_015", &expect_015);
    EXPECT_NE((void *)NULL, expect_015);
    readJanssonFile("./actual/actual_015", &actual_015);
    EXPECT_NE((void *)NULL, actual_015);

    EXPECT_TRUE(testYangJsonIsEqual(actual_015, expect_015));
    free(expect_015);
    free(actual_015);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 16.所有ID/PID以外的字段为8条must，choice/case以外的节点为8条must，每条xpath包含各种正确的函数，32个入参，建表
TEST_F(xpath_analyse_normal_test, Yang_059_016)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SYNTAX_ERROR);
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_16.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_16.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(true, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(0, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_016\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_016 = NULL, *actual_016 = NULL;
    readJanssonFile("./expect/expect_016", &expect_016);
    EXPECT_NE((void *)NULL, expect_016);
    readJanssonFile("./actual/actual_016", &actual_016);
    EXPECT_NE((void *)NULL, actual_016);

    EXPECT_TRUE(testYangJsonIsEqual(actual_016, expect_016));
    free(expect_016);
    free(actual_016);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 17.所有ID/PID以外的字段为8条must，choice/case以外的节点为8条must，每条xpath包含各种正确的函数，33个入参，建表
TEST_F(xpath_analyse_normal_test, Yang_059_017)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SYNTAX_ERROR);
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_17.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_17.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(false, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(9, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_017\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_017 = NULL, *actual_017 = NULL;
    readJanssonFile("./expect/expect_017", &expect_017);
    EXPECT_NE((void *)NULL, expect_017);
    readJanssonFile("./actual/actual_017", &actual_017);
    EXPECT_NE((void *)NULL, actual_017);

    EXPECT_TRUE(testYangJsonIsEqual(actual_017, expect_017));
    free(expect_017);
    free(actual_017);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 18.所有ID/PID以外的字段为8条must，choice/case以外的节点为8条must，每条xpath包含不存在的函数名（如undefined（）），建表
TEST_F(xpath_analyse_normal_test, Yang_059_018)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SYNTAX_ERROR);
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_18.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_18.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(false, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(72, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_018\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_018 = NULL, *actual_018 = NULL;
    readJanssonFile("./expect/expect_018", &expect_018);
    EXPECT_NE((void *)NULL, expect_018);
    readJanssonFile("./actual/actual_018", &actual_018);
    EXPECT_NE((void *)NULL, actual_018);

    EXPECT_TRUE(testYangJsonIsEqual(actual_018, expect_018));
    free(expect_018);
    free(actual_018);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 19.所有ID/PID以外的字段为8条must，choice/case以外的节点为8条must，每条xpath包含入参错误的函数（类型错误），建表
TEST_F(xpath_analyse_normal_test, Yang_059_019)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SYNTAX_ERROR);
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_19.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_19.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(true, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(0, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_019\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_019 = NULL, *actual_019 = NULL;
    readJanssonFile("./expect/expect_019", &expect_019);
    EXPECT_NE((void *)NULL, expect_019);
    readJanssonFile("./actual/actual_019", &actual_019);
    EXPECT_NE((void *)NULL, actual_019);

    EXPECT_TRUE(testYangJsonIsEqual(actual_019, expect_019));
    free(expect_019);
    free(actual_019);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 20.所有ID/PID以外的字段为8条must，choice/case以外的节点为8条must，每条xpath函数名和（）中含空格，建表
TEST_F(xpath_analyse_normal_test, Yang_059_020)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SYNTAX_ERROR);
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_20.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_20.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(false, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(72, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_020\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_020 = NULL, *actual_020 = NULL;
    readJanssonFile("./expect/expect_020", &expect_020);
    EXPECT_NE((void *)NULL, expect_020);
    readJanssonFile("./actual/actual_020", &actual_020);
    EXPECT_NE((void *)NULL, actual_020);

    EXPECT_TRUE(testYangJsonIsEqual(actual_020, expect_020));
    free(expect_020);
    free(actual_020);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 21.所有ID/PID以外的字段为8条must，choice/case以外的节点为8条must，每条xpath含不成对的（），建表
TEST_F(xpath_analyse_normal_test, Yang_059_021)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SYNTAX_ERROR);
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_21.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_21.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(false, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(72, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_021\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_021 = NULL, *actual_021 = NULL;
    readJanssonFile("./expect/expect_021", &expect_021);
    EXPECT_NE((void *)NULL, expect_021);
    readJanssonFile("./actual/actual_021", &actual_021);
    EXPECT_NE((void *)NULL, actual_021);

    EXPECT_TRUE(testYangJsonIsEqual(actual_021, expect_021));
    free(expect_021);
    free(actual_021);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 22.所有ID/PID以外的字段为8条must，choice/case以外的节点为8条must，每条xpath含成对但多余的（），建表
TEST_F(xpath_analyse_normal_test, Yang_059_022)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SYNTAX_ERROR);
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_22.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_22.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(true, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(0, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_022\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_022 = NULL, *actual_022 = NULL;
    readJanssonFile("./expect/expect_022", &expect_022);
    EXPECT_NE((void *)NULL, expect_022);
    readJanssonFile("./actual/actual_022", &actual_022);
    EXPECT_NE((void *)NULL, actual_022);

    EXPECT_TRUE(testYangJsonIsEqual(actual_022, expect_022));
    free(expect_022);
    free(actual_022);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 23.所有ID/PID以外的字段为8条must，choice/case以外的节点为8条must，每条xpath含正确的路径类型节点，建表
TEST_F(xpath_analyse_normal_test, Yang_059_023)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SYNTAX_ERROR);
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_23.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_23.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(true, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(0, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_023\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_023 = NULL, *actual_023 = NULL;
    readJanssonFile("./expect/expect_023", &expect_023);
    EXPECT_NE((void *)NULL, expect_023);
    readJanssonFile("./actual/actual_023", &actual_023);
    EXPECT_NE((void *)NULL, actual_023);

    EXPECT_TRUE(testYangJsonIsEqual(actual_023, expect_023));
    free(expect_023);
    free(actual_023);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 24.所有ID/PID以外的字段为8条must，choice/case以外的节点为8条must，含不存在的路径节点类型，建表
TEST_F(xpath_analyse_normal_test, Yang_059_024)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SYNTAX_ERROR);
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_24.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_24.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(false, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(72, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_024\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_024 = NULL, *actual_024 = NULL;
    readJanssonFile("./expect/expect_024", &expect_024);
    EXPECT_NE((void *)NULL, expect_024);
    readJanssonFile("./actual/actual_024", &actual_024);
    EXPECT_NE((void *)NULL, actual_024);

    EXPECT_TRUE(testYangJsonIsEqual(actual_024, expect_024));
    free(expect_024);
    free(actual_024);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 25.所有ID/PID以外的字段为8条must，choice/case以外的节点为8条must，每条xpath含各类正确的运算符，建表
TEST_F(xpath_analyse_normal_test, Yang_059_025)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SYNTAX_ERROR);
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_25.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_25.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(true, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(0, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_025\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_025 = NULL, *actual_025 = NULL;
    readJanssonFile("./expect/expect_025", &expect_025);
    EXPECT_NE((void *)NULL, expect_025);
    readJanssonFile("./actual/actual_025", &actual_025);
    EXPECT_NE((void *)NULL, actual_025);

    EXPECT_TRUE(testYangJsonIsEqual(actual_025, expect_025));
    free(expect_025);
    free(actual_025);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 26.所有ID/PID以外的字段为8条must，choice/case以外的节点为8条must，每条xpath含不存在（如xor）的运算符，建表
TEST_F(xpath_analyse_normal_test, Yang_059_026)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SYNTAX_ERROR);
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_26.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_26.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(false, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(72, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_026\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_026 = NULL, *actual_026 = NULL;
    readJanssonFile("./expect/expect_026", &expect_026);
    EXPECT_NE((void *)NULL, expect_026);
    readJanssonFile("./actual/actual_026", &actual_026);
    EXPECT_NE((void *)NULL, actual_026);

    EXPECT_TRUE(testYangJsonIsEqual(actual_026, expect_026));
    free(expect_026);
    free(actual_026);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 27.所有ID/PID以外的字段为8条must，choice/case以外的节点为8条must，每条xpath含仅数学无意义（div 0或小数取余等）的表达式，建表
TEST_F(xpath_analyse_normal_test, Yang_059_027)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_27.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_27.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(true, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(0, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_027\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_027 = NULL, *actual_027 = NULL;
    readJanssonFile("./expect/expect_027", &expect_027);
    EXPECT_NE((void *)NULL, expect_027);
    readJanssonFile("./actual/actual_027", &actual_027);
    EXPECT_NE((void *)NULL, actual_027);

    EXPECT_TRUE(testYangJsonIsEqual(actual_027, expect_027));
    free(expect_027);
    free(actual_027);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 28.所有ID/PID以外的字段为8条must，choice/case以外的节点为8条must，每条xpath中路径和运算符用一个空格" "隔开，建表
TEST_F(xpath_analyse_normal_test, Yang_059_028)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_28.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_28.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(true, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(0, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_028\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_028 = NULL, *actual_028 = NULL;
    readJanssonFile("./expect/expect_028", &expect_028);
    EXPECT_NE((void *)NULL, expect_028);
    readJanssonFile("./actual/actual_028", &actual_028);
    EXPECT_NE((void *)NULL, actual_028);

    EXPECT_TRUE(testYangJsonIsEqual(actual_028, expect_028));
    free(expect_028);
    free(actual_028);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 29.所有ID/PID以外的字段为8条must，choice/case以外的节点为8条must，每条xpath中路径和运算符不用空格/用多个空格或用Tab隔开，建表
TEST_F(xpath_analyse_normal_test, Yang_059_029)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SYNTAX_ERROR);
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_29.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_29.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(true, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(0, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_029\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_029 = NULL, *actual_029 = NULL;
    readJanssonFile("./expect/expect_029", &expect_029);
    EXPECT_NE((void *)NULL, expect_029);
    readJanssonFile("./actual/actual_029", &actual_029);
    EXPECT_NE((void *)NULL, actual_029);

    EXPECT_TRUE(testYangJsonIsEqual(actual_029, expect_029));
    free(expect_029);
    free(actual_029);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 30.1所有ID/PID以外的字段为8条must，choice/case以外的节点为8条must，xpath部分正确（先对后错），建表
TEST_F(xpath_analyse_normal_test, Yang_059_030)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SYNTAX_ERROR);
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_30_1.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_30_1.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(false, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(55, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_030\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_030 = NULL, *actual_030 = NULL;
    readJanssonFile("./expect/expect_030", &expect_030);
    EXPECT_NE((void *)NULL, expect_030);
    readJanssonFile("./actual/actual_030", &actual_030);
    EXPECT_NE((void *)NULL, actual_030);

    EXPECT_TRUE(testYangJsonIsEqual(actual_030, expect_030));
    free(expect_030);
    free(actual_030);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 30.2所有ID/PID以外的字段为8条must，choice/case以外的节点为8条must，xpath部分正确（先错后对），建表
TEST_F(xpath_analyse_normal_test, Yang_059_031)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SYNTAX_ERROR);
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_30_2.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_30_2.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(false, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(55, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_031\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_031 = NULL, *actual_031 = NULL;
    readJanssonFile("./expect/expect_031", &expect_031);
    EXPECT_NE((void *)NULL, expect_031);
    readJanssonFile("./actual/actual_031", &actual_031);
    EXPECT_NE((void *)NULL, actual_031);

    EXPECT_TRUE(testYangJsonIsEqual(actual_031, expect_031));
    free(expect_031);
    free(actual_031);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 30.3所有ID/PID以外的字段为8条must，choice/case以外的节点为8条must，xpath部分正确（对错交叉），建表
TEST_F(xpath_analyse_normal_test, Yang_059_032)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SYNTAX_ERROR);
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_30_3.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_30_3.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(false, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(55, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_032\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_032 = NULL, *actual_032 = NULL;
    readJanssonFile("./expect/expect_032", &expect_032);
    EXPECT_NE((void *)NULL, expect_032);
    readJanssonFile("./actual/actual_032", &actual_032);
    EXPECT_NE((void *)NULL, actual_032);

    EXPECT_TRUE(testYangJsonIsEqual(actual_032, expect_032));
    free(expect_032);
    free(actual_032);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 31.所有ID/PID以外的字段为8条must，choice/case以外的节点为8条must，xpath达到3k，建表
TEST_F(xpath_analyse_normal_test, Yang_059_033)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 建表
    AsyncUserDataT data = {0};
    string mainJsonStr = MakeMustMainJson(g_xpathBool3K);
    const char *mainJson = mainJsonStr.c_str();
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    
    string listJsonStr = MakeMustListJson(g_xpathBool3K);
    const char *listJson = listJsonStr.c_str();
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(true, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(0, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_033\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_033 = NULL, *actual_033 = NULL;
    readJanssonFile("./expect/expect_033", &expect_033);
    EXPECT_NE((void *)NULL, expect_033);
    readJanssonFile("./actual/actual_033", &actual_033);
    EXPECT_NE((void *)NULL, actual_033);

    EXPECT_TRUE(testYangJsonIsEqual(actual_033, expect_033));
    free(expect_033);
    free(actual_033);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(edgeJson);
#endif
}

// 32.所有ID/PID以外的字段为8条must，choice/case以外的节点为8条must，xpath超过3k，建表
TEST_F(xpath_analyse_normal_test, Yang_059_034)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_INVALID_PROPERTY);
    int ret = 0;
    // 建表
    AsyncUserDataT data = {0};
    string mainJsonStr = MakeMustMainJson(g_xpathBoolOver3K);
    const char *mainJson = mainJsonStr.c_str();
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, data.status);
    
    string listJsonStr = MakeMustListJson(g_xpathBoolOver3K);
    const char *listJson = listJsonStr.c_str();
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, data.status);
#endif
}

// 33.所有ID/PID/list中primary key以外的字段、所有节点为when，每条xpath为简单bool值（如true、1>2等），建表
TEST_F(xpath_analyse_normal_test, Yang_059_035)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_33.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_33.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(true, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(0, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_035\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_035 = NULL, *actual_035 = NULL;
    readJanssonFile("./expect/expect_035", &expect_035);
    EXPECT_NE((void *)NULL, expect_035);
    readJanssonFile("./actual/actual_035", &actual_035);
    EXPECT_NE((void *)NULL, actual_035);

    EXPECT_TRUE(testYangJsonIsEqual(actual_035, expect_035));
    free(expect_035);
    free(actual_035);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 34.所有ID/PID/list中primary key以外的字段、所有节点为when，每条xpath为非bool值（3、1+2），建表
TEST_F(xpath_analyse_normal_test, Yang_059_036)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_34.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_34.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(true, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(0, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_036\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_036 = NULL, *actual_036 = NULL;
    readJanssonFile("./expect/expect_036", &expect_036);
    EXPECT_NE((void *)NULL, expect_036);
    readJanssonFile("./actual/actual_036", &actual_036);
    EXPECT_NE((void *)NULL, actual_036);

    EXPECT_TRUE(testYangJsonIsEqual(actual_036, expect_036));
    free(expect_036);
    free(actual_036);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 35.所有ID/PID/list中primary key以外的字段、所有节点为when，每条xpath为空串""，建表
TEST_F(xpath_analyse_normal_test, Yang_059_037)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SYNTAX_ERROR);
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_35.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_35.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(false, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(20, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_037\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_037 = NULL, *actual_037 = NULL;
    readJanssonFile("./expect/expect_037", &expect_037);
    EXPECT_NE((void *)NULL, expect_037);
    readJanssonFile("./actual/actual_037", &actual_037);
    EXPECT_NE((void *)NULL, actual_037);

    EXPECT_TRUE(testYangJsonIsEqual(actual_037, expect_037));
    free(expect_037);
    free(actual_037);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 36.所有ID/PID/list中primary key以外的字段、所有节点为when，缺省xpath，建表
TEST_F(xpath_analyse_normal_test, Yang_059_038)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_36.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_36.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, data.status);

    free(mainJson);
    free(listJson);
#endif
}

// 37.所有ID/PID/list中primary key以外的字段、所有节点为when，xpath全部不能解析（如乱码），建表
TEST_F(xpath_analyse_normal_test, Yang_059_039)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SYNTAX_ERROR);
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_37.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_37.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(false, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(20, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_039\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_039 = NULL, *actual_039 = NULL;
    readJanssonFile("./expect/expect_039", &expect_039);
    EXPECT_NE((void *)NULL, expect_039);
    readJanssonFile("./actual/actual_039", &actual_039);
    EXPECT_NE((void *)NULL, actual_039);

    EXPECT_TRUE(testYangJsonIsEqual(actual_039, expect_039));
    free(expect_039);
    free(actual_039);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 38.1所有ID/PID/list中primary key以外的字段、所有节点为when，xpath含换行和\0
TEST_F(xpath_analyse_normal_test, Yang_059_040)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_INVALID_JSON_CONTENT);
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_38_1.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_JSON_CONTENT, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_38_1.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_JSON_CONTENT, data.status);

    free(mainJson);
    free(listJson);
#endif
}

// 38.2所有ID/PID/list中primary key以外的字段、所有节点为when，xpath含Tab、空格、\0以外的转义字符
TEST_F(xpath_analyse_normal_test, Yang_059_041)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SYNTAX_ERROR);
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_38_2.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_38_2.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(false, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(20, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_041\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_041 = NULL, *actual_041 = NULL;
    readJanssonFile("./expect/expect_041", &expect_041);
    EXPECT_NE((void *)NULL, expect_041);
    readJanssonFile("./actual/actual_041", &actual_041);
    EXPECT_NE((void *)NULL, actual_041);

    EXPECT_TRUE(testYangJsonIsEqual(actual_041, expect_041));
    free(expect_041);
    free(actual_041);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 39.所有ID/PID/list中primary key以外的字段、所有节点为when，每条xpath包含各种正确的函数，包含32个正确的入参，建表
TEST_F(xpath_analyse_normal_test, Yang_059_042)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_39.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_39.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(true, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(0, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_042\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_042 = NULL, *actual_042 = NULL;
    readJanssonFile("./expect/expect_042", &expect_042);
    EXPECT_NE((void *)NULL, expect_042);
    readJanssonFile("./actual/actual_042", &actual_042);
    EXPECT_NE((void *)NULL, actual_042);

    EXPECT_TRUE(testYangJsonIsEqual(actual_042, expect_042));
    free(expect_042);
    free(actual_042);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 40.所有ID/PID/list中primary key以外的字段、所有节点为when，每条xpath包含各种正确的函数，包含33个正确的入参，建表
TEST_F(xpath_analyse_normal_test, Yang_059_043)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SYNTAX_ERROR);
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_40.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_40.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(false, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(11, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_043\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_043 = NULL, *actual_043 = NULL;
    readJanssonFile("./expect/expect_043", &expect_043);
    EXPECT_NE((void *)NULL, expect_043);
    readJanssonFile("./actual/actual_043", &actual_043);
    EXPECT_NE((void *)NULL, actual_043);

    EXPECT_TRUE(testYangJsonIsEqual(actual_043, expect_043));
    free(expect_043);
    free(actual_043);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 41.所有ID/PID/list中primary key以外的字段、所有节点为when，每条xpath包含不存在的函数名（如undefined()），建表
TEST_F(xpath_analyse_normal_test, Yang_059_044)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SYNTAX_ERROR);
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_41.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_41.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(false, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(20, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_044\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_044 = NULL, *actual_044 = NULL;
    readJanssonFile("./expect/expect_044", &expect_044);
    EXPECT_NE((void *)NULL, expect_044);
    readJanssonFile("./actual/actual_044", &actual_044);
    EXPECT_NE((void *)NULL, actual_044);

    EXPECT_TRUE(testYangJsonIsEqual(actual_044, expect_044));
    free(expect_044);
    free(actual_044);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 42.所有ID/PID/list中primary key以外的字段、所有节点为when，每条xpath包含入参错误的函数（类型错误），建表
TEST_F(xpath_analyse_normal_test, Yang_059_045)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_42.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_42.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(true, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(0, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_045\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_045 = NULL, *actual_045 = NULL;
    readJanssonFile("./expect/expect_045", &expect_045);
    EXPECT_NE((void *)NULL, expect_045);
    readJanssonFile("./actual/actual_045", &actual_045);
    EXPECT_NE((void *)NULL, actual_045);

    EXPECT_TRUE(testYangJsonIsEqual(actual_045, expect_045));
    free(expect_045);
    free(actual_045);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 43.所有ID/PID/list中primary key以外的字段、所有节点为when，每条xpath函数名和（）中含空格，建表
TEST_F(xpath_analyse_normal_test, Yang_059_046)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SYNTAX_ERROR);
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_43.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_43.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(false, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(20, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_046\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_046 = NULL, *actual_046 = NULL;
    readJanssonFile("./expect/expect_046", &expect_046);
    EXPECT_NE((void *)NULL, expect_046);
    readJanssonFile("./actual/actual_046", &actual_046);
    EXPECT_NE((void *)NULL, actual_046);

    EXPECT_TRUE(testYangJsonIsEqual(actual_046, expect_046));
    free(expect_046);
    free(actual_046);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 44.所有ID/PID/list中primary key以外的字段、所有节点为when，每条xpath含不成对的（），建表
TEST_F(xpath_analyse_normal_test, Yang_059_047)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SYNTAX_ERROR);
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_44.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_44.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(false, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(20, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_047\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_047 = NULL, *actual_047 = NULL;
    readJanssonFile("./expect/expect_047", &expect_047);
    EXPECT_NE((void *)NULL, expect_047);
    readJanssonFile("./actual/actual_047", &actual_047);
    EXPECT_NE((void *)NULL, actual_047);

    EXPECT_TRUE(testYangJsonIsEqual(actual_047, expect_047));
    free(expect_047);
    free(actual_047);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 45.所有ID/PID/list中primary key以外的字段、所有节点为when，每条xpath含成对但多余的（），建表
TEST_F(xpath_analyse_normal_test, Yang_059_048)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SYNTAX_ERROR);
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_45.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_45.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(true, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(0, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_048\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_048 = NULL, *actual_048 = NULL;
    readJanssonFile("./expect/expect_048", &expect_048);
    EXPECT_NE((void *)NULL, expect_048);
    readJanssonFile("./actual/actual_048", &actual_048);
    EXPECT_NE((void *)NULL, actual_048);

    EXPECT_TRUE(testYangJsonIsEqual(actual_048, expect_048));
    free(expect_048);
    free(actual_048);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 46.所有ID/PID/list中primary key以外的字段、所有节点为when，每条xpath含正确的路径类型节点，建表
TEST_F(xpath_analyse_normal_test, Yang_059_049)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SYNTAX_ERROR);
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_45.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_45.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(true, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(0, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_049\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_049 = NULL, *actual_049 = NULL;
    readJanssonFile("./expect/expect_049", &expect_049);
    EXPECT_NE((void *)NULL, expect_049);
    readJanssonFile("./actual/actual_049", &actual_049);
    EXPECT_NE((void *)NULL, actual_049);

    EXPECT_TRUE(testYangJsonIsEqual(actual_049, expect_049));
    free(expect_049);
    free(actual_049);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 47.所有ID/PID/list中primary key以外的字段、所有节点为when，每条xpath含不存在的路径类型节点（如unexist-path-type-node），建表
TEST_F(xpath_analyse_normal_test, Yang_059_050)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SYNTAX_ERROR);
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_47.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_47.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(false, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(20, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_050\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_050 = NULL, *actual_050 = NULL;
    readJanssonFile("./expect/expect_050", &expect_050);
    EXPECT_NE((void *)NULL, expect_050);
    readJanssonFile("./actual/actual_050", &actual_050);
    EXPECT_NE((void *)NULL, actual_050);

    EXPECT_TRUE(testYangJsonIsEqual(actual_050, expect_050));
    free(expect_050);
    free(actual_050);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 48.所有ID/PID/list中primary key以外的字段、所有节点为when，每条xpath含各类正确的运算符，建表
TEST_F(xpath_analyse_normal_test, Yang_059_051)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SYNTAX_ERROR);
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_48.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_48.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(true, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(0, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_051\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_051 = NULL, *actual_051 = NULL;
    readJanssonFile("./expect/expect_051", &expect_051);
    EXPECT_NE((void *)NULL, expect_051);
    readJanssonFile("./actual/actual_051", &actual_051);
    EXPECT_NE((void *)NULL, actual_051);

    EXPECT_TRUE(testYangJsonIsEqual(actual_051, expect_051));
    free(expect_051);
    free(actual_051);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 49.所有ID/PID/list中primary key以外的字段、所有节点为when，每条xpath含不存在的运算符，建表
TEST_F(xpath_analyse_normal_test, Yang_059_052)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SYNTAX_ERROR);
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_49.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_49.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(false, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(20, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_052\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_052 = NULL, *actual_052 = NULL;
    readJanssonFile("./expect/expect_052", &expect_052);
    EXPECT_NE((void *)NULL, expect_052);
    readJanssonFile("./actual/actual_052", &actual_052);
    EXPECT_NE((void *)NULL, actual_052);

    EXPECT_TRUE(testYangJsonIsEqual(actual_052, expect_052));
    free(expect_052);
    free(actual_052);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 50.所有ID/PID/list中primary key以外的字段、所有节点为when，每条xpath含仅数学无意义（div 0）的表达式，建表
TEST_F(xpath_analyse_normal_test, Yang_059_053)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SYNTAX_ERROR);
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_50.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_50.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(true, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(0, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_053\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_053 = NULL, *actual_053 = NULL;
    readJanssonFile("./expect/expect_053", &expect_053);
    EXPECT_NE((void *)NULL, expect_053);
    readJanssonFile("./actual/actual_053", &actual_053);
    EXPECT_NE((void *)NULL, actual_053);

    EXPECT_TRUE(testYangJsonIsEqual(actual_053, expect_053));
    free(expect_053);
    free(actual_053);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 51.所有ID/PID/list中primary key以外的字段、所有节点为when，每条xpath中路径和运算符用一个空格" "隔开，建表
TEST_F(xpath_analyse_normal_test, Yang_059_054)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SYNTAX_ERROR);
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_51.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_51.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(true, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(0, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_054\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_054 = NULL, *actual_054 = NULL;
    readJanssonFile("./expect/expect_054", &expect_054);
    EXPECT_NE((void *)NULL, expect_054);
    readJanssonFile("./actual/actual_054", &actual_054);
    EXPECT_NE((void *)NULL, actual_054);

    EXPECT_TRUE(testYangJsonIsEqual(actual_054, expect_054));
    free(expect_054);
    free(actual_054);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 52.所有ID/PID/list中primary key以外的字段、所有节点为when，每条xpath中路径和运算符不用空格/用多个空格或用Tab隔开，建表
TEST_F(xpath_analyse_normal_test, Yang_059_055)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SYNTAX_ERROR);
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_52.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_52.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(false, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(1, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_055\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_055 = NULL, *actual_055 = NULL;
    readJanssonFile("./expect/expect_055", &expect_055);
    EXPECT_NE((void *)NULL, expect_055);
    readJanssonFile("./actual/actual_055", &actual_055);
    EXPECT_NE((void *)NULL, actual_055);

    EXPECT_TRUE(testYangJsonIsEqual(actual_055, expect_055));
    free(expect_055);
    free(actual_055);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 53.所有ID/PID/list中primary key以外的字段、所有节点为when，xpath部分正确，建表
TEST_F(xpath_analyse_normal_test, Yang_059_056)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SYNTAX_ERROR);
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_53.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_53.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(false, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(11, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_056\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_056 = NULL, *actual_056 = NULL;
    readJanssonFile("./expect/expect_056", &expect_056);
    EXPECT_NE((void *)NULL, expect_056);
    readJanssonFile("./actual/actual_056", &actual_056);
    EXPECT_NE((void *)NULL, actual_056);

    EXPECT_TRUE(testYangJsonIsEqual(actual_056, expect_056));
    free(expect_056);
    free(actual_056);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}

// 54.所有ID/PID/list中primary key以外的字段、所有节点为when，xpath达到3k，建表
TEST_F(xpath_analyse_normal_test, Yang_059_057)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 建表
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, g_whenMain3K, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcCreateVertexLabelAsync(g_stmtAsync, g_whenList3K, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(true, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(0, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_057\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_057 = NULL, *actual_057 = NULL;
    readJanssonFile("./expect/expect_057", &expect_057);
    EXPECT_NE((void *)NULL, expect_057);
    readJanssonFile("./actual/actual_057", &actual_057);
    EXPECT_NE((void *)NULL, actual_057);

    EXPECT_TRUE(testYangJsonIsEqual(actual_057, expect_057));
    free(expect_057);
    free(actual_057);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(edgeJson);
#endif
}

// 55.所有ID/PID/list中primary key以外的字段、所有节点为when，xpath超过3k，建表
TEST_F(xpath_analyse_normal_test, Yang_059_058)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_INVALID_PROPERTY);
    int ret = 0;
    // 建表
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, g_whenMainOver3K, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, data.status);

    ret = GmcCreateVertexLabelAsync(g_stmtAsync, g_whenListOver3K, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, data.status);
#endif
}

// 56.所有ID/PID以外的字段为leafref，所有ID/PID以外的字段为8条must，choice/case以外的节点为8条must，
//    所有ID/PID/list中primary key以外的字段、所有节点为when，xpath部分正确，建表
TEST_F(xpath_analyse_normal_test, Yang_059_059)
{
#ifdef FEATURE_YANG_VALIDATION
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_SYNTAX_ERROR);
    int ret = 0;
    // 建表
    char *mainJson = NULL;
    readJanssonFile("schema/main_56.gmjson", &mainJson);
    ASSERT_NE((void *)NULL, mainJson);
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, mainJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *listJson = NULL;
    readJanssonFile("schema/list_56.gmjson", &listJson);
    ASSERT_NE((void *)NULL, listJson);
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, listJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    char *edgeJson = NULL;
    readJanssonFile("schema/edge.edgejson", &edgeJson);
    ASSERT_NE((void *)NULL, edgeJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeJson, g_labelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 模型校验
    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(g_stmtAsync, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(false, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(26, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));

    // 视图校验 Xpath 解析是否正确
    char command[1024];
    snprintf(command, 1024, "%s/gmsysview subtree -ns %s -rn %s -filterMode MODEL -s %s >./actual/actual_059\n",
        g_toolPath, g_namespace, "container_1", g_connServer);
    AW_FUN_Log(LOG_INFO, "%s", command);
    system(command);
    memset(command, 0, sizeof(command));

    // 比较预期与实际是否一致
    char *expect_059 = NULL, *actual_059 = NULL;
    readJanssonFile("./expect/expect_059", &expect_059);
    EXPECT_NE((void *)NULL, expect_059);
    readJanssonFile("./actual/actual_059", &actual_059);
    EXPECT_NE((void *)NULL, actual_059);

    EXPECT_TRUE(testYangJsonIsEqual(actual_059, expect_059));
    free(expect_059);
    free(actual_059);

    // 删表
    ret = GmcDropEdgeLabelAsync(g_stmtAsync, "edge_label", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "container_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmtAsync, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(mainJson);
    free(listJson);
    free(edgeJson);
#endif
}
