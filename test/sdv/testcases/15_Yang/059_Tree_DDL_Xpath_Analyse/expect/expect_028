{"type": "container", "name": "container_1", "clause": [{"type": "must", "formula": "/container_1/con_1_F1 + 1 > 1", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/con_2_F1 - 1 < 2", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/choice_1/case_1_1/case_1_1_F1 * 2 = 3", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/choice_1/case_1_2/case_1_2_F1 div 4 != 5", "status": "valid"}, {"type": "must", "formula": "/container_1/con_1_F1 > 10", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/con_2_F1 < 20", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/choice_1/case_1_1/case_1_1_F1 = 30", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/choice_1/case_1_2/case_1_2_F1 != 40", "status": "valid"}], "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "con_1_F1", "type": "int32", "clause": [{"type": "must", "formula": "/container_1/con_1_F1 + 1 > 1", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/con_2_F1 - 1 < 2", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/choice_1/case_1_1/case_1_1_F1 * 2 = 3", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/choice_1/case_1_2/case_1_2_F1 div 4 != 5", "status": "valid"}, {"type": "must", "formula": "/container_1/con_1_F1 > 10", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/con_2_F1 < 20", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/choice_1/case_1_1/case_1_1_F1 = 30", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/choice_1/case_1_2/case_1_2_F1 != 40", "status": "valid"}]}, {"type": "container", "name": "container_2", "clause": [{"type": "must", "formula": "/container_1/con_1_F1 + 1 > 1", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/con_2_F1 - 1 < 2", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/choice_1/case_1_1/case_1_1_F1 * 2 = 3", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/choice_1/case_1_2/case_1_2_F1 div 4 != 5", "status": "valid"}, {"type": "must", "formula": "/container_1/con_1_F1 > 10", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/con_2_F1 < 20", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/choice_1/case_1_1/case_1_1_F1 = 30", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/choice_1/case_1_2/case_1_2_F1 != 40", "status": "valid"}], "fields": [{"name": "con_2_F1", "type": "int32", "clause": [{"type": "must", "formula": "/container_1/con_1_F1 + 1 > 1", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/con_2_F1 - 1 < 2", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/choice_1/case_1_1/case_1_1_F1 * 2 = 3", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/choice_1/case_1_2/case_1_2_F1 div 4 != 5", "status": "valid"}, {"type": "must", "formula": "/container_1/con_1_F1 > 10", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/con_2_F1 < 20", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/choice_1/case_1_1/case_1_1_F1 = 30", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/choice_1/case_1_2/case_1_2_F1 != 40", "status": "valid"}]}, {"type": "choice", "name": "choice_1", "fields": [{"type": "case", "name": "case_1_1", "fields": [{"name": "case_1_1_F1", "type": "int32", "clause": [{"type": "must", "formula": "/container_1/con_1_F1 + 1 > 1", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/con_2_F1 - 1 < 2", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/choice_1/case_1_1/case_1_1_F1 * 2 = 3", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/choice_1/case_1_2/case_1_2_F1 div 4 != 5", "status": "valid"}, {"type": "must", "formula": "/container_1/con_1_F1 > 10", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/con_2_F1 < 20", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/choice_1/case_1_1/case_1_1_F1 = 30", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/choice_1/case_1_2/case_1_2_F1 != 40", "status": "valid"}]}]}, {"type": "case", "name": "case_1_2", "fields": [{"name": "case_1_2_F1", "type": "int32", "clause": [{"type": "must", "formula": "/container_1/con_1_F1 + 1 > 1", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/con_2_F1 - 1 < 2", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/choice_1/case_1_1/case_1_1_F1 * 2 = 3", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/choice_1/case_1_2/case_1_2_F1 div 4 != 5", "status": "valid"}, {"type": "must", "formula": "/container_1/con_1_F1 > 10", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/con_2_F1 < 20", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/choice_1/case_1_1/case_1_1_F1 = 30", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/choice_1/case_1_2/case_1_2_F1 != 40", "status": "valid"}]}]}]}]}, {"type": "list", "name": "list_1", "clause": [{"type": "must", "formula": "/container_1/con_1_F1 + 1 > 1", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/con_2_F1 - 1 < 2", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/choice_1/case_1_1/case_1_1_F1 * 2 = 3", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/choice_1/case_1_2/case_1_2_F1 div 4 != 5", "status": "valid"}, {"type": "must", "formula": "/container_1/con_1_F1 > 10", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/con_2_F1 < 20", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/choice_1/case_1_1/case_1_1_F1 = 30", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/choice_1/case_1_2/case_1_2_F1 != 40", "status": "valid"}], "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "list_1_F1", "type": "int32", "nullable": false, "clause": [{"type": "must", "formula": "/container_1/con_1_F1 + 1 > 1", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/con_2_F1 - 1 < 2", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/choice_1/case_1_1/case_1_1_F1 * 2 = 3", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/choice_1/case_1_2/case_1_2_F1 div 4 != 5", "status": "valid"}, {"type": "must", "formula": "/container_1/con_1_F1 > 10", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/con_2_F1 < 20", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/choice_1/case_1_1/case_1_1_F1 = 30", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/choice_1/case_1_2/case_1_2_F1 != 40", "status": "valid"}]}, {"name": "list_1_F2", "type": "int32", "nullable": false, "clause": [{"type": "must", "formula": "/container_1/con_1_F1 + 1 > 1", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/con_2_F1 - 1 < 2", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/choice_1/case_1_1/case_1_1_F1 * 2 = 3", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/choice_1/case_1_2/case_1_2_F1 div 4 != 5", "status": "valid"}, {"type": "must", "formula": "/container_1/con_1_F1 > 10", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/con_2_F1 < 20", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/choice_1/case_1_1/case_1_1_F1 = 30", "status": "valid"}, {"type": "must", "formula": "/container_1/container_2/choice_1/case_1_2/case_1_2_F1 != 40", "status": "valid"}]}], "keys": [{"node": "list_1", "name": "pk", "fields": ["PID", "list_1_F1"], "index": {"type": "primary"}, "constrains": {"unique": true}}]}], "keys": [{"node": "container_1", "name": "pk", "fields": ["ID"], "index": {"type": "primary"}, "constrains": {"unique": true}}]}