[{"type": "container", "name": "root_C", "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}, {"type": "container", "name": "con_1_C", "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}]}, {"type": "container", "name": "con_2_C", "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}, {"type": "container", "name": "con_2_1_C", "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}]}]}, {"type": "container", "name": "con_3_C", "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}, {"type": "choice", "name": "choice_3_1_C", "fields": [{"type": "case", "name": "case_3_1_1_C", "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}]}, {"type": "case", "name": "case_3_1_2_C", "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}]}]}]}, {"type": "container", "name": "con_4_C", "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}]}, {"type": "container", "name": "con_5_C", "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}]}, {"type": "choice", "name": "choice_6_C", "fields": [{"type": "case", "name": "case_6_1_C", "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}]}]}, {"type": "choice", "name": "choice_7_C", "fields": [{"type": "case", "name": "case_7_1_C", "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}, {"type": "container", "name": "con_7_1_1_C", "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}]}]}]}, {"type": "choice", "name": "choice_8_C", "fields": [{"type": "case", "name": "case_8_1_C", "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}, {"type": "choice", "name": "choice_8_1_1_C", "fields": [{"type": "case", "name": "case_8_1_1_1_C", "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}]}]}]}]}, {"type": "choice", "name": "choice_9_C", "fields": [{"type": "case", "name": "case_9_1_C", "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}]}, {"type": "case", "name": "case_9_2_C", "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}]}]}, {"type": "choice", "name": "choice_10_C", "fields": [{"type": "case", "name": "case_10_1_C", "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}]}]}], "keys": [{"node": "root_C", "name": "PK", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_4_1_C", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": true, "comment": "绝对路径", "clause": [{"type": "leafref", "formula": "/root_C/con_4_C/list_4_1_C/F1"}]}, {"name": "F1", "type": "uint32", "nullable": true, "comment": "绝对路径", "clause": [{"type": "leafref", "formula": "/root_C/con_4_C/list_4_1_C/F2"}]}, {"name": "F2", "type": "uint32", "nullable": true, "comment": "绝对路径", "clause": [{"type": "leafref", "formula": "/root_C/con_4_C/list_4_1_C/F3"}]}, {"name": "F3", "type": "uint32", "nullable": true, "comment": "绝对路径", "clause": [{"type": "leafref", "formula": "/root_C/con_4_C/list_4_1_C/F4"}]}, {"name": "F4", "type": "uint32", "nullable": true, "comment": "绝对路径", "clause": [{"type": "leafref", "formula": "/root_C/con_4_C/list_4_1_C/F5"}]}, {"name": "F5", "type": "uint32", "nullable": true, "comment": "绝对路径", "clause": [{"type": "leafref", "formula": "/root_C/con_4_C/list_4_1_C/F6"}]}, {"name": "F6", "type": "uint32", "nullable": true, "comment": "绝对路径", "clause": [{"type": "leafref", "formula": "/root_C/con_4_C/list_4_1_C/F7"}]}, {"name": "F7", "type": "uint32", "nullable": true, "comment": "绝对路径", "clause": [{"type": "leafref", "formula": "/root_C/con_4_C/list_4_1_C/F8"}]}, {"name": "F8", "type": "uint32", "nullable": true, "comment": "绝对路径", "clause": [{"type": "leafref", "formula": "/root_C/con_4_C/list_4_1_C/F9"}]}, {"name": "F9", "type": "uint32", "nullable": true, "comment": "绝对路径", "clause": [{"type": "leafref", "formula": "/root_C/choice_9_C/case_9_2_C/list_9_2_1_C/F0"}]}, {"name": "F10", "type": "uint32", "nullable": true}], "keys": [{"fields": ["PID", "PK"], "node": "list_4_1_C", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_9_1_1_C", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}], "keys": [{"fields": ["PID", "PK"], "node": "list_9_1_1_C", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_9_2_1_C", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": true, "comment": "绝对路径", "clause": [{"type": "leafref", "formula": "/root_C/choice_9_C/case_9_2_C/list_9_2_1_C/F1"}]}, {"name": "F1", "type": "uint32", "nullable": true, "comment": "绝对路径", "clause": [{"type": "leafref", "formula": "/root_C/choice_9_C/case_9_2_C/list_9_2_1_C/F2"}]}, {"name": "F2", "type": "uint32", "nullable": true, "comment": "绝对路径", "clause": [{"type": "leafref", "formula": "/root_C/choice_9_C/case_9_2_C/list_9_2_1_C/F3"}]}, {"name": "F3", "type": "uint32", "nullable": true, "comment": "绝对路径", "clause": [{"type": "leafref", "formula": "/root_C/choice_9_C/case_9_2_C/list_9_2_1_C/F4"}]}, {"name": "F4", "type": "uint32", "nullable": true, "comment": "绝对路径", "clause": [{"type": "leafref", "formula": "/root_C/choice_9_C/case_9_2_C/list_9_2_1_C/F5"}]}, {"name": "F5", "type": "uint32", "nullable": true, "comment": "绝对路径", "clause": [{"type": "leafref", "formula": "/root_C/choice_9_C/case_9_2_C/list_9_2_1_C/F6"}]}, {"name": "F6", "type": "uint32", "nullable": true, "comment": "绝对路径", "clause": [{"type": "leafref", "formula": "/root_C/choice_9_C/case_9_2_C/list_9_2_1_C/F7"}]}, {"name": "F7", "type": "uint32", "nullable": true, "comment": "绝对路径", "clause": [{"type": "leafref", "formula": "/root_C/choice_9_C/case_9_2_C/list_9_2_1_C/F8"}]}, {"name": "F8", "type": "uint32", "nullable": true, "comment": "绝对路径", "clause": [{"type": "leafref", "formula": "/root_C/choice_9_C/case_9_2_C/list_9_2_1_C/F9"}]}, {"name": "F9", "type": "uint32", "nullable": true, "comment": "绝对路径", "clause": [{"type": "leafref", "formula": "/root_C/list_12_C/con_12_1_C/F0"}]}, {"name": "F10", "type": "uint32", "nullable": true}], "keys": [{"fields": ["PID", "PK"], "node": "list_9_2_1_C", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_11_C", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}], "keys": [{"fields": ["PID", "PK"], "node": "list_11_C", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_12_C", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}, {"type": "container", "name": "con_12_1_C", "fields": [{"name": "F0", "type": "uint32", "nullable": true, "comment": "绝对路径", "clause": [{"type": "leafref", "formula": "/root_C/list_12_C/con_12_1_C/F1"}]}, {"name": "F1", "type": "uint32", "nullable": true, "comment": "绝对路径", "clause": [{"type": "leafref", "formula": "/root_C/list_12_C/con_12_1_C/F2"}]}, {"name": "F2", "type": "uint32", "nullable": true, "comment": "绝对路径", "clause": [{"type": "leafref", "formula": "/root_C/list_12_C/con_12_1_C/F3"}]}, {"name": "F3", "type": "uint32", "nullable": true, "comment": "绝对路径", "clause": [{"type": "leafref", "formula": "/root_C/list_12_C/con_12_1_C/F4"}]}, {"name": "F4", "type": "uint32", "nullable": true, "comment": "绝对路径", "clause": [{"type": "leafref", "formula": "/root_C/list_12_C/con_12_1_C/F5"}]}, {"name": "F5", "type": "uint32", "nullable": true, "comment": "绝对路径", "clause": [{"type": "leafref", "formula": "/root_C/list_12_C/con_12_1_C/F6"}]}, {"name": "F6", "type": "uint32", "nullable": true, "comment": "绝对路径", "clause": [{"type": "leafref", "formula": "/root_C/list_12_C/con_12_1_C/F7"}]}, {"name": "F7", "type": "uint32", "nullable": true, "comment": "绝对路径", "clause": [{"type": "leafref", "formula": "/root_C/list_12_C/con_12_1_C/F8"}]}, {"name": "F8", "type": "uint32", "nullable": true, "comment": "绝对路径", "clause": [{"type": "leafref", "formula": "/root_C/list_12_C/con_12_1_C/F9"}]}, {"name": "F9", "type": "uint32", "nullable": true, "comment": "绝对路径", "clause": [{"type": "leafref", "formula": "/root_C/list_13_C/F0"}]}, {"name": "F10", "type": "uint32", "nullable": true}]}], "keys": [{"fields": ["PID", "PK"], "node": "list_12_C", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_13_C", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": true, "comment": "绝对路径", "clause": [{"type": "leafref", "formula": "/root_C/list_13_C/F1"}]}, {"name": "F1", "type": "uint32", "nullable": true, "comment": "绝对路径", "clause": [{"type": "leafref", "formula": "/root_C/list_13_C/F2"}]}, {"name": "F2", "type": "uint32", "nullable": true, "comment": "绝对路径", "clause": [{"type": "leafref", "formula": "/root_C/list_13_C/F3"}]}, {"name": "F3", "type": "uint32", "nullable": true, "comment": "绝对路径", "clause": [{"type": "leafref", "formula": "/root_C/list_13_C/F4"}]}, {"name": "F4", "type": "uint32", "nullable": true, "comment": "绝对路径", "clause": [{"type": "leafref", "formula": "/root_C/leaflist_16_C/PK"}]}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}, {"type": "choice", "name": "choice_13_1_C", "fields": [{"type": "case", "name": "case_13_1_1_C", "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}]}, {"type": "case", "name": "case_13_1_2_C", "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}]}]}], "keys": [{"fields": ["PID", "PK"], "node": "list_13_C", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_14_C", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}], "keys": [{"fields": ["PID", "PK"], "node": "list_14_C", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_14_1_C", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}], "keys": [{"fields": ["PID", "PK"], "node": "list_14_1_C", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_15_C", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}], "keys": [{"fields": ["PID", "PK"], "node": "list_15_C", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "leaf-list", "name": "leaflist_5_1_C", "min-elements": 0, "max-elements": 10000, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false}], "keys": [{"fields": ["PID", "PK"], "node": "leaflist_5_1_C", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "leaf-list", "name": "leaflist_10_1_1_C", "min-elements": 0, "max-elements": 10000, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false}], "keys": [{"fields": ["PID", "PK"], "node": "leaflist_10_1_1_C", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "leaf-list", "name": "leaflist_15_1_C", "min-elements": 0, "max-elements": 10000, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false}], "keys": [{"fields": ["PID", "PK"], "node": "leaflist_15_1_C", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "leaf-list", "name": "leaflist_16_C", "min-elements": 0, "max-elements": 10000, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false, "comment": "绝对路径", "clause": [{"type": "leafref", "formula": "/root_C/leaflist_17_C/PK"}]}], "keys": [{"fields": ["PID", "PK"], "node": "leaflist_16_C", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "leaf-list", "name": "leaflist_17_C", "min-elements": 0, "max-elements": 10000, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false, "comment": "绝对路径", "clause": [{"type": "leafref", "formula": "/root_C/leaflist_18_C/PK"}]}], "keys": [{"fields": ["PID", "PK"], "node": "leaflist_17_C", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "leaf-list", "name": "leaflist_18_C", "min-elements": 0, "max-elements": 10000, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false, "comment": "绝对路径", "clause": [{"type": "leafref", "formula": "/root_C/leaflist_19_C/PK"}]}], "keys": [{"fields": ["PID", "PK"], "node": "leaflist_18_C", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "leaf-list", "name": "leaflist_19_C", "min-elements": 0, "max-elements": 10000, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false, "comment": "绝对路径", "clause": [{"type": "leafref", "formula": "/root_C/leaflist_20_C/PK"}]}], "keys": [{"fields": ["PID", "PK"], "node": "leaflist_19_C", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "leaf-list", "name": "leaflist_20_C", "min-elements": 0, "max-elements": 10000, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false, "comment": "绝对路径", "clause": [{"type": "leafref", "formula": "/root_A/F0"}]}], "keys": [{"fields": ["PID", "PK"], "node": "leaflist_20_C", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}]