/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#include "RCA_COMMON.h"
#include "../../../reliability/try.h"
#include "aliasTool.h"

class variableWhen : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void variableWhen::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/start.sh");
    int ret = testEnvInit();
    ASSERT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void variableWhen::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}

void variableWhen::SetUp()
{
    int ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务

    const char *namespace1 = "variableWhen";
    const char *namespaceUserName = "abc";

    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观
    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // alloc all stmt
    TestYangAllocAllstmt();
}

void variableWhen::TearDown()
{
    const char *namespace1 = "variableWhen";
    TryDropNameSpace(g_stmt_async, namespace1);

    // 释放all stmt
    TestYangFreeAllstmt();

    int ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


void TestCheckValidateModelAsync(GmcStmtT *stmt)
{
    // 模型校验
    YangValidateUserDataT checkData = {0};
    int ret = GmcYangValidateModelAsync(stmt, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(true, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(0, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));
}

// *typedef void (*GmcYangValidateDoneT)(void *userData, GmcValidateResT validateRes, int32_t status, const char *errMsg);*/
void AsyncValidateLeafRefCb(void *userData, GmcValidateResT validateRes, int32_t status, const char *errMsg)
{
    if (userData) {
        YangValidateUserDataT *uData = (YangValidateUserDataT *)userData;
        uData->status = status;
        if ((status != GMERR_OK) && (errMsg != NULL)) {
            printf("YangValidate errMsg: %s\n", errMsg);
        }
        uData->validateRes = validateRes.validateRes;
        uData->failCount = validateRes.failCount;

        printf(">>> validateRes: %d\n", validateRes.validateRes);
        printf(">>> failCount: %u\n", validateRes.failCount);

        if (uData->isValidErrorPathInfo) {
            GmcErrorPathInfoT msg;
            ASSERT_EQ(GMERR_OK, GmcYangGetErrorPathInfo(&msg));

            // 结果检查
            printf("--- errcode: %d\n", msg.errorCode);
            printf("--- errorClauseIndex: %u\n", msg.errorClauseIndex);
            printf("--- errorMsg: %s\n", msg.errorMsg);
            printf("--- errorPath: %s\n", msg.errorPath);
            EXPECT_EQ(uData->expectedErrCode, msg.errorCode);
            EXPECT_EQ(uData->expectedErrClauseIndex, msg.errorClauseIndex);
            EXPECT_STREQ(uData->expectedErrMsg, msg.errorMsg);
            EXPECT_STREQ(uData->expectedErrPath, msg.errorPath);
            ASSERT_NO_FATAL_FAILURE(GmcYangFreeErrorPathInfo());
        }

        uData->recvNum++;
    }
}


/*****************************************************************************
 * Description  : 001.when条件current函数的xpath语句包含带参数的谓词(list)，变量类型为string，且为主键字段
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(variableWhen, Yang_082_variableWhen_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaWhen/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaWhen/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    /*
    依赖关系                              | 写入数据                             | 预期结果
    ContainerTwo/F0(/alias_ContainerOne/alias_ListOne[$TARGET_LIST-RECORD]/F1 = current() | ContainerOne/F0 = 100 current=100 |
           ContainerTwo/F0(属性可见) 
    ContainerThree/F0(/alias_ContainerOne/alias_ListOne[$TARGET_LIST-RECORD]/F1 != current() | ContainerOne/F0 = 100 current=100 |
           ContainerThree/F0(属性不可见) 
    */

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 对子节点字段 做replace操作  --ListOne
    char fieldStr[8] = "str001";
    ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 container NP 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 对 container P 子节点做replace操作  -- ContainerThree 
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 这里需要prepar list的labelname
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  --ListTwo
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验 -- 不传参数，报错；
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_BUTT;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath = "";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONSTRAINT_CHECK_VIOLATION, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(false, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(0, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // when校验 -- 传参数
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_BUTT;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath = "";

    checkRes = sucRes;
    cfg.type = GMC_YANG_VALIDATION_ALL_FORCE;
    cfg.cfgJson = R"({"TARGET_LIST-RECORD":  "str001"})";
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    char *diffreply01 = NULL;
    readJanssonFile("diffWhen/diff001.json", &diffreply01);

    std::vector<std::string> diffreply(1);
    diffreply[0] = diffreply01;
    testFetchAndDeparseDiff(g_stmt_async, batch, diffreply, data);
    free(diffreply01);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_082_variableWhen_001");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns variableWhen -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 002.when条件not函数的xpath语句包含带参数的谓词(list)，变量类型为int8，且为主键字段
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(variableWhen, Yang_082_variableWhen_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaWhen/SubTreeVertexLabel2.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaWhen/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    /*
    依赖关系                              | 写入数据                             | 预期结果
    ContainerTwo/F1(not(/alias_ContainerOne/alias_ListOne[F0=$TARGET_LIST-RECORD]/F1 = 100 | F1=100 |
           ContainerTwo/F1(属性不可见) 
    ContainerThree/F1(not(/alias_ContainerOne/alias_ListOne[F0=$TARGET_LIST-RECORD]/F1 != 100 | F1 = 100 |
           ContainerThree/F1(属性可见) 
    */

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 对子节点字段 做replace操作  --ListOne
    int8_t fieldInt8 = 11;
    ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_INT8, &fieldInt8, sizeof(int8_t), "F0",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 container NP 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 对 container P 子节点做replace操作  -- ContainerThree 
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 这里需要prepar list的labelname
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  --ListTwo
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验 -- 输入错误参数名，报错；
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_BUTT;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath = "";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE,
        .cfgJson = R"({"TARGET_LIST-RECORD2222":  "11"})"};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(false, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(0, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // when校验 -- 输入参数；
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_BUTT;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath = "";

    checkRes = sucRes;
    cfg.type = GMC_YANG_VALIDATION_ALL_FORCE,
    cfg.cfgJson = R"({"TARGET_LIST-RECORD": 11})";
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    char *diffreply01 = NULL;
    readJanssonFile("diffWhen/diff002.json", &diffreply01);

    std::vector<std::string> diffreply(1);
    diffreply[0] = diffreply01;
    testFetchAndDeparseDiff(g_stmt_async, batch, diffreply, data);
    free(diffreply01);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_082_variableWhen_002");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns variableWhen -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}



/*****************************************************************************
 * Description  : 003.when条件true函数的xpath语句包含带参数的谓词(list)，变量类型为uint8，且为主键字段
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(variableWhen, Yang_082_variableWhen_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaWhen/SubTreeVertexLabel3.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaWhen/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    /*
    依赖关系                              | 写入数据                             | 预期结果
    ContainerTwo/F3(/alias_ContainerOne/alias_ListOne[F0=$TARGET_LIST-RECORD]/F4 != true() | F4=true |
           (属性不可见) 
    ContainerThree/F3(/alias_ContainerOne/alias_ListOne[F0=$TARGET_LIST-RECORD]/F4 = true() | F4=true |
           (属性可见) 
    */

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 对子节点字段 做replace操作  --ListOne
    uint8_t fieldUint8 = 11;
    ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_UINT8, &fieldUint8, sizeof(uint8_t), "F0",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 container NP 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 对 container P 子节点做replace操作  -- ContainerThree 
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 这里需要prepar list的labelname
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  --ListTwo
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验 -- 输入参数
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_BUTT;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath = "";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE,
        .cfgJson = R"({"TARGET_LIST-RECORD":  11})"};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    char *diffreply01 = NULL;
    readJanssonFile("diffWhen/diff003.json", &diffreply01);

    std::vector<std::string> diffreply(1);
    diffreply[0] = diffreply01;
    testFetchAndDeparseDiff(g_stmt_async, batch, diffreply, data);
    free(diffreply01);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_082_variableWhen_003");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns variableWhen -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 004.when条件false函数的xpath语句包含带参数的谓词(list)，变量类型为int16，且为主键字段
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(variableWhen, Yang_082_variableWhen_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaWhen/SubTreeVertexLabel4.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaWhen/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    /*
    依赖关系                              | 写入数据                             | 预期结果
    ContainerTwo/F4(/alias_ContainerOne/alias_ListOne[F0=$TARGET_LIST-RECORD]/F4 != false() | F4=true |
           ContainerTwo/F4(属性可见) 
    ContainerThree/F4(/alias_ContainerOne/alias_ListOne[F0=$TARGET_LIST-RECORD]/F4 = false() | F4=true |
           ContainerThree/F4(属性不可见) 
    */

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 对子节点字段 做replace操作  --ListOne
    int16_t fieldInt16 = 11;
    ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_INT16, &fieldInt16, sizeof(int16_t), "F0",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 container NP 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 对 container P 子节点做replace操作  -- ContainerThree 
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 这里需要prepar list的labelname
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  --ListTwo
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验 -- 输入参数
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_BUTT;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath = "";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE,
        .cfgJson = R"({"TARGET_LIST-RECORD":  11})"};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    char *diffreply01 = NULL;
    readJanssonFile("diffWhen/diff004.json", &diffreply01);

    std::vector<std::string> diffreply(1);
    diffreply[0] = diffreply01;
    testFetchAndDeparseDiff(g_stmt_async, batch, diffreply, data);
    free(diffreply01);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_082_variableWhen_004");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns variableWhen -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 005.when条件string函数的xpath语句包含带参数的谓词(list)，变量类型为uint16，且为主键字段
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(variableWhen, Yang_082_variableWhen_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaWhen/SubTreeVertexLabel5.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaWhen/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    /*
    依赖关系                              | 写入数据                             | 预期结果
    ContainerTwo/F5(string(/alias_ContainerOne/alias_ListOne[F0=$TARGET_LIST-RECORD]/F1) = '120' | F1=100 |
           ContainerTwo/F5(属性不可见) 
    ContainerThree/F5(string(/alias_ContainerOne/alias_ListOne[F0=$TARGET_LIST-RECORD]/F1) != '120' | F1=100  |
           ContainerThree/F5(属性可见) 
    */

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 对子节点字段 做replace操作  --ListOne
    uint16_t fieldUint16 = 11;
    ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_UINT16, &fieldUint16, sizeof(uint16_t), "F0",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 container NP 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 对 container P 子节点做replace操作  -- ContainerThree 
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 这里需要prepar list的labelname
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  --ListTwo
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验 -- 输入参数
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_BUTT;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath = "";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE,
        .cfgJson = R"({"TARGET_LIST-RECORD":  11})"};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));


    // 获取diff
    AsyncUserDataT data = {0};
    char *diffreply01 = NULL;
    readJanssonFile("diffWhen/diff005.json", &diffreply01);

    std::vector<std::string> diffreply(1);
    diffreply[0] = diffreply01;
    testFetchAndDeparseDiff(g_stmt_async, batch, diffreply, data);
    free(diffreply01);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_082_variableWhen_005");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns variableWhen -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 006.when条件number函数的xpath语句包含带参数的谓词(list)，变量类型为int32，且为主键字段
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(variableWhen, Yang_082_variableWhen_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaWhen/SubTreeVertexLabel6.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaWhen/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    /*
    依赖关系                              | 写入数据                             | 预期结果
    ContainerTwo/F6(number(/alias_ContainerOne/alias_ListOne[F0=$TARGET_LIST-RECORD]/F6) = '1' | F6='1' |
           ContainerTwo/F6(属性可见) 
    ContainerThree/F6(number(/alias_ContainerOne/alias_ListOne[F0=$TARGET_LIST-RECORD]/F6) != '1' | F6='1' |
           ContainerThree/F6(属性不可见) 
    */

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 对子节点字段 做replace操作  --ListOne
    int32_t fieldInt32 = 32;
    ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_INT32, &fieldInt32, sizeof(int32_t), "F0",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 container NP 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 对 container P 子节点做replace操作  -- ContainerThree 
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 这里需要prepar list的labelname
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  --ListTwo
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验 -- 输入参数
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_BUTT;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath = "";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE,
        .cfgJson = R"({"TARGET_LIST-RECORD":  32})"};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));


    // 获取diff
    AsyncUserDataT data = {0};
    char *diffreply01 = NULL;
    readJanssonFile("diffWhen/diff006.json", &diffreply01);

    std::vector<std::string> diffreply(1);
    diffreply[0] = diffreply01;
    testFetchAndDeparseDiff(g_stmt_async, batch, diffreply, data);
    free(diffreply01);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_082_variableWhen_006");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns variableWhen -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 007.when条件translate函数使的xpath语句包含带参数的谓词(list)，变量类型为uint32，且为主键字段
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(variableWhen, Yang_082_variableWhen_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaWhen/SubTreeVertexLabel7.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaWhen/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    /*
    依赖关系                              | 写入数据                             | 预期结果
    ContainerTwo/F7(translate(/alias_ContainerOne/alias_ListOne[F0=$TARGET_LIST-RECORD]/F6, 'a', 'b') = 'bbdbdd'" | F6='aadadd' |
           ContainerTwo/F7(属性可见) 
    ContainerThree/F7(translate(/alias_ContainerOne/alias_ListOne[F0=$TARGET_LIST-RECORD]/F6, 'a', 'b') = 'bbbddd1'"  | F6='aadadd' |
           ContainerThree/F7(属性不可见) 
    */

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 对子节点字段 做replace操作  --ListOne
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    char strValue[8] = "aadadd";
    ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &strValue, (strlen(strValue)), "F6",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container NP 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 对 container P 子节点做replace操作  -- ContainerThree 
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 这里需要prepar list的labelname
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  --ListTwo
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验 -- 输入参数
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_BUTT;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath = "";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE,
        .cfgJson = R"({"TARGET_LIST-RECORD":  100})"};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    char *diffreply01 = NULL;
    readJanssonFile("diffWhen/diff007.json", &diffreply01);

    std::vector<std::string> diffreply(1);
    diffreply[0] = diffreply01;
    testFetchAndDeparseDiff(g_stmt_async, batch, diffreply, data);
    free(diffreply01);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_082_variableWhen_007");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns variableWhen -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 008.when条件starts-with函数的xpath语句包含带参数的谓词(list)，变量类型为int64，且为主键字段
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(variableWhen, Yang_082_variableWhen_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaWhen/SubTreeVertexLabel8.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaWhen/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    /*
    依赖关系                              | 写入数据                             | 预期结果
    ContainerTwo/F8(starts-with(/alias_ContainerOne/alias_ListOne[F0=$TARGET_LIST-RECORD]/F6, 'a')" | F6='aadadd' |
           (属性可见) 
    ContainerThree/F8(starts-with(/alias_ContainerOne/alias_ListOne[F0=$TARGET_LIST-RECORD]/F6, 'b')"  | F6='aadadd' |
           (属性不可见) 
    */

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 对子节点字段 做replace操作  --ListOne
    int64_t fieldInt64 = 32;
    ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_INT64, &fieldInt64, sizeof(int64_t), "F0",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    char strValue[8] = "aadadd";
    ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &strValue, (strlen(strValue)), "F6",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container NP 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 对 container P 子节点做replace操作  -- ContainerThree 
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 这里需要prepar list的labelname
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  --ListTwo
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验 -- 输入参数
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_BUTT;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath = "";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE,
        .cfgJson = R"({"TARGET_LIST-RECORD":  32})"};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    char *diffreply01 = NULL;
    readJanssonFile("diffWhen/diff008.json", &diffreply01);

    std::vector<std::string> diffreply(1);
    diffreply[0] = diffreply01;
    testFetchAndDeparseDiff(g_stmt_async, batch, diffreply, data);
    free(diffreply01);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_082_variableWhen_008");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns variableWhen -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 009.when条件运算符，加法（+）、减法（-）、乘法（*）、除法（div）、取余（mod）的xpath语句包含带参数的谓词(list)，变量类型为bool，且为主键字段
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(variableWhen, Yang_082_variableWhen_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaWhen/SubTreeVertexLabel9.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaWhen/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    /*
    依赖关系                              | 写入数据                             | 预期结果
    ContainerTwo/F9(
                    /alias_ContainerOne/alias_ListOne[F2=$TARGET_LIST-RECORD]/F1 + /alias_ContainerOne/alias_ListOne[F2=$TARGET_LIST-RECORD]/F7 > 100"
                    /alias_ContainerOne/alias_ListOne[F2=$TARGET_LIST-RECORD]/F1 - /alias_ContainerOne/alias_ListOne[F2=$TARGET_LIST-RECORD]/F7 < 100"
                    /alias_ContainerOne/alias_ListOne[F2=$TARGET_LIST-RECORD]/F1 * /alias_ContainerOne/alias_ListOne[F2=$TARGET_LIST-RECORD]/F7 > 100"
                    /alias_ContainerOne/alias_ListOne[F2=$TARGET_LIST-RECORD]/F1 div 100 = 1"
                    /alias_ContainerOne/alias_ListOne[F2=$TARGET_LIST-RECORD]/F1 mod 100 = 0" )
                | F1= 100 F7=10 | (属性可见) 
    ContainerThree/F9(.....alias_ContainerOne/alias_ListOne[F2=$TARGET_LIST-RECORD]/F1 mod 100 = 1)|
                | (属性不可见) 
    */

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 对子节点字段 做replace操作  --ListOne
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    bool boolValue = true;
    ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_BOOL, &boolValue, sizeof(boolValue), "F2",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    fieldValue = 10;
    testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F7");

    char strValue[8] = "aadadd";
    ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &strValue, (strlen(strValue)), "F6",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container NP 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 对 container P 子节点做replace操作  -- ContainerThree 
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 这里需要prepar list的labelname
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  --ListTwo
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验 -- 输入参数
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_BUTT;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath = "";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE,
        .cfgJson = R"({"TARGET_LIST-RECORD":  1000})"};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    char *diffreply01 = NULL;
    readJanssonFile("diffWhen/diff009.json", &diffreply01);

    std::vector<std::string> diffreply(1);
    diffreply[0] = diffreply01;
    testFetchAndDeparseDiff(g_stmt_async, batch, diffreply, data);
    free(diffreply01);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_082_variableWhen_009");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns variableWhen -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 010.when条件，逻辑与（and）、逻辑或（or）的xpath语句包含带参数的谓词(list)变量类型为string，且为主键字段
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(variableWhen, Yang_082_variableWhen_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaWhen/SubTreeVertexLabel10.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaWhen/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    /*
    依赖关系                              | 写入数据                             | 预期结果
    ContainerTwo/F10(
                     /alias_ContainerOne/alias_ListOne[F0=$TARGET_LIST-RECORD]/F1 mod 100 = 0 and /alias_ContainerOne/alias_ListOne[F0=$TARGET_LIST-RECORD]/F1 = 100"
                     /alias_ContainerOne/alias_ListOne[F0=$TARGET_LIST-RECORD]/F1 > 100 or /alias_ContainerOne/alias_ListOne[F0=$TARGET_LIST-RECORD]/F1 < 100"
                | F1= 100 | (属性不可见) 
    ContainerThree/F10(
                     /alias_ContainerOne/alias_ListOne[F0=$TARGET_LIST-RECORD]/F1 mod 100 = 0 and /alias_ContainerOne/alias_ListOne[F0=$TARGET_LIST-RECORD]/F1 = 100"
                     /alias_ContainerOne/alias_ListOne[F0=$TARGET_LIST-RECORD]/F1 > 100 or /alias_ContainerOne/alias_ListOne[F0=$TARGET_LIST-RECORD]/F1 = 100"
                | F1= 100 | (属性可见) 
    */

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 对子节点字段 做replace操作  --ListOne
    char fieldStr[8] = "str001";
    ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool boolValue = true;
    ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_BOOL, &boolValue, sizeof(boolValue), "F2",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 container NP 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 对 container P 子节点做replace操作  -- ContainerThree 
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 这里需要prepar list的labelname
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  --ListTwo
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验 -- 输入参数
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_BUTT;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath = "";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE,
        .cfgJson = R"({"TARGET_LIST-RECORD":  "str001"})"};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    char *diffreply01 = NULL;
    readJanssonFile("diffWhen/diff010.json", &diffreply01);

    std::vector<std::string> diffreply(1);
    diffreply[0] = diffreply01;
    testFetchAndDeparseDiff(g_stmt_async, batch, diffreply, data);
    free(diffreply01);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_082_variableWhen_010");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns variableWhen -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 011.when条件多个xpath语句包含带参数的谓词(list)，变量类型为string，且为主键字段
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(variableWhen, Yang_082_variableWhen_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaWhen/SubTreeVertexLabel11.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaWhen/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
   for (int i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[8] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);

        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        bool boolValue = false;
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_BOOL, &boolValue, sizeof(boolValue), "F2",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = 300 + i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = 100;
        TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = 100;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListTwo
        fieldValue = 100 + i;
        testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    /*
    diff里有record1,record2 会同时校验record1，record2数据;
    传入record.主键值，
    校验根据diff里数据来，取数据根据全局的数据来；
    */

    // when校验 -- 输入参数record2的值；修改str000.F1= 只校验涉及F1的plan (plan里的解析是按全局的数据来)
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_BUTT;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath = "";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE,
        .cfgJson = R"({"TARGET_LIST-RECORD":  "str001"})"};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    char *diffreply01 = NULL;
    readJanssonFile("diffWhen/diff011.json", &diffreply01);

    std::vector<std::string> diffreply(1);
    diffreply[0] = diffreply01;
    testFetchAndDeparseDiff(g_stmt_async, batch, diffreply, data);
    free(diffreply01);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_082_variableWhen_011");

    // 提交事务
    TransCommit(g_conn_async);

    /*
    依赖关系                              | 写入数据                             | 预期结果
    ContainerTwo/F11("/alias_ContainerOne/alias_ListOne[F0=$TARGET_LIST-RECORD]/F1 != 301""
                | diff里：str00.F1= 100 全局:str001.F1=301 | (传参str001  属性不可见)
    ContainerThree/F11("/alias_ContainerOne/alias_ListOne[F0=$TARGET_LIST-RECORD]/F1 = 301""
                | diff里：str00.F1= 100 全局:str001.F1=301 | (传参str001  属性可见) 
    */

    // 创建乐观事务
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
   for (int i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[8] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);

        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        bool boolValue = false;
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_BOOL, &boolValue, sizeof(boolValue), "F2",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = 300 + i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = 100;
        TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = 100;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListTwo
        fieldValue = 100 + i;
        testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_082_variableWhen_011_2");
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 对子节点字段 做replace操作  --ListOne
    char fieldStr[8] = "str000";
    ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool boolValue = true;
    ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_BOOL, &boolValue, sizeof(boolValue), "F2",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 container NP 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 对 container P 子节点做replace操作  -- ContainerThree 
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 这里需要prepar list的labelname
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  --ListTwo
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    /*
    diff里只有record1，只会校验record1
    传入record.主键值
    校验哪个record根据diff里数据来，取数据根据全局的数据来；
    */

    // when校验 -- 输入参数record2的值；修改str000.F1= 只校验涉及F1的plan (plan里的解析是按全局的数据来)
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_BUTT;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath = "";

    checkRes = sucRes;
    GmcValidateConfigT cfg2 = {.type = GMC_YANG_VALIDATION_ALL_FORCE,
        .cfgJson = R"({"TARGET_LIST-RECORD":  "str001"})"};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg2, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    AW_FUN_Log(LOG_INFO, ">> check diff011_2.json\n");
    // 获取diff
    memset(&data, 0, sizeof(data));
    char *diffreply02 = NULL;
    readJanssonFile("diffWhen/diff011_2.json", &diffreply02);

    std::vector<std::string> diffreply2(1);
    diffreply2[0] = diffreply02;
    testFetchAndDeparseDiff(g_stmt_async, batch, diffreply2, data);
    free(diffreply02);

    //subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_082_variableWhen_011_3");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns variableWhen -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 012.when条件xpath语句包含带参数的谓词(list)，变量类型为string，且不为主键字段
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(variableWhen, Yang_082_variableWhen_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaWhen/SubTreeVertexLabel12.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaWhen/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
   for (int i = 0; i < 6; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[8] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);

        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        bool boolValue = false;
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_BOOL, &boolValue, sizeof(boolValue), "F2",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = 300 + i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

        char strValue[8] = {0};
        if (i < 3) {
            snprintf(strValue, sizeof(strValue), "aadadd");
        } else {
            snprintf(strValue, sizeof(strValue), "aacccc");
        }
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &strValue, (strlen(strValue)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = 100;
        TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = 100;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListTwo
        fieldValue = 100 + i;
        testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_082_variableWhen_012");
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
   for (int i = 6; i < 12; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[8] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);

        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        bool boolValue = false;
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_BOOL, &boolValue, sizeof(boolValue), "F2",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = 300 + i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

        char strValue[8] = {0};
        if (i < 9) {
            snprintf(strValue, sizeof(strValue), "aadadd");
        } else {
            snprintf(strValue, sizeof(strValue), "aacccc");
        }
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &strValue, (strlen(strValue)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = 100;
        TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = 100;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListTwo
        fieldValue = 100 + i;
        testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验 -- 输入参数对应多条记录，校验diff里涉及字段的plan (plan里的数据解析按全局的来)
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_BUTT;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath = "";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE,
        .cfgJson = R"({"TARGET_LIST-RECORD":  "aadadd"})"};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    char *diffreply01 = NULL;
    readJanssonFile("diffWhen/diff012.json", &diffreply01);

    std::vector<std::string> diffreply(1);
    diffreply[0] = diffreply01;
    testFetchAndDeparseDiff(g_stmt_async, batch, diffreply, data);
    free(diffreply01);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_082_variableWhen_012_1");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns variableWhen -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 013.when条件xpath语句包含带参数的谓词(list)，变量为ID
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(variableWhen, Yang_082_variableWhen_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaWhen/SubTreeVertexLabel13.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaWhen/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    /*
    依赖关系                              | 写入数据                             | 预期结果
    ContainerTwo/F0(/alias_ContainerOne/alias_ListOne[$TARGET_LIST-RECORD]/F1 = current() | ContainerOne/F0 = 100 current=100 |
           ContainerTwo/F0(属性可见) 
    ContainerThree/F0(/alias_ContainerOne/alias_ListOne[$TARGET_LIST-RECORD]/F1 != current() | ContainerOne/F0 = 100 current=100 |
           ContainerThree/F0(属性不可见) 
    */

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 对子节点字段 做replace操作  --ListOne
    char fieldStr[8] = "str001";
    ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 container NP 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 对 container P 子节点做replace操作  -- ContainerThree 
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 这里需要prepar list的labelname
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  --ListTwo
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验 -- 传参数
    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_BUTT;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath = "";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE,
       .cfgJson = R"({"TARGET_LIST-RECORD":  1})"};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    char *diffreply01 = NULL;
    readJanssonFile("diffWhen/diff013.json", &diffreply01);

    std::vector<std::string> diffreply(1);
    diffreply[0] = diffreply01;
    testFetchAndDeparseDiff(g_stmt_async, batch, diffreply, data);
    free(diffreply01);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_082_variableWhen_013");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns variableWhen -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}



/*****************************************************************************
 * Description  : 014.when条件xpath语句包含带参数的谓词(list)，变量为PID
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(variableWhen, Yang_082_variableWhen_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaWhen/SubTreeVertexLabel14.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaWhen/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    /*
    一个Xpath扫出多条数据，只要其中一条数据满足，该Xpath即满足  （or 语义）
    多个when之间 (and 语义)

    依赖关系                              | 写入数据                             | 预期结果
    ContainerTwo/F0(/alias_ContainerOne/alias_ListOne[$TARGET_LIST-RECORD]/F1 = 300 | ContainerOne/F0 = 100 current=100 |
           ContainerTwo/F0(有一条数据满足，即为满足；属性不可见) 
    ContainerThree/F0(/alias_ContainerOne/ListOne[PID=$TARGET_LIST-RECORD]/F1 != 100 | ContainerOne/F0 = 100 current=100 |
           ContainerThree/F0(有一条数据满足，即为满足；属性可见) 
    */

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);


    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
   for (int i = 0; i < 3; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[8] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);

        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = 100 + i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

        int32_t fieldInt32 = 100;
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_INT32, &fieldInt32, sizeof(int32_t), "F7",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = 100;
        TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = 100;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListTwo
        fieldValue = 100;
        testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验 -- 传参数
    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_BUTT;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath = "";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE,
       .cfgJson = R"({"TARGET_LIST-RECORD":  1})"};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    char *diffreply01 = NULL;
    readJanssonFile("diffWhen/diff014.json", &diffreply01);

    std::vector<std::string> diffreply(1);
    diffreply[0] = diffreply01;
    testFetchAndDeparseDiff(g_stmt_async, batch, diffreply, data);
    free(diffreply01);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_082_variableWhen_014");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns variableWhen -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 015. when条件多个xpath语句包含带参数的谓词(list)，变量类型为string，且为主键字段，在存在record1的基础上，编辑record2，输入record1的主键值，预期只会对record2校验
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(variableWhen, Yang_082_variableWhen_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaWhen/SubTreeVertexLabel15.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaWhen/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
   for (int i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[8] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);

        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        bool boolValue = false;
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_BOOL, &boolValue, sizeof(boolValue), "F2",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = 300 + i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = 100;
        TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = 100;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListTwo
        fieldValue = 100 + i;
        testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_082_variableWhen_015");
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 对子节点字段 做replace操作  --ListOne
    char fieldStr[8] = "str000";
    ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool boolValue = true;
    ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_BOOL, &boolValue, sizeof(boolValue), "F2",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 container NP 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 对 container P 子节点做replace操作  -- ContainerThree 
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 这里需要prepar list的labelname
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  --ListTwo
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    /*
    diff里只有record1，只会校验record1
    传入record.主键值
    校验哪个record根据diff里数据来，取数据根据全局的数据来；
    */

    // when校验 -- 输入参数record2的值；修改str000.F1= 只校验涉及F1的plan (plan里的解析是按全局的数据来)
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_BUTT;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath = "";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE,
        .cfgJson = R"({"TARGET_LIST-RECORD":  "str001"})"};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    char *diffreply01 = NULL;
    readJanssonFile("diffWhen/diff015.json", &diffreply01);

    std::vector<std::string> diffreply(1);
    diffreply[0] = diffreply01;
    testFetchAndDeparseDiff(g_stmt_async, batch, diffreply, data);
    free(diffreply01);

    //subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_082_variableWhen_015_2");

    // 提交事务
    TransCommit(g_conn_async);
 
    system("gmsysview subtree -ns variableWhen -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 016. when条件多个xpath语句包含带参数的谓词(list)，变量类型为string，且为主键字段，在存在record1的基础上，编辑record2，输入record2的主键值，预期只会对record2校验
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(variableWhen, Yang_082_variableWhen_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaWhen/SubTreeVertexLabel15.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaWhen/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
   for (int i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[8] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);

        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        bool boolValue = false;
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_BOOL, &boolValue, sizeof(boolValue), "F2",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = 300 + i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = 100;
        TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = 100;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListTwo
        fieldValue = 100 + i;
        testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // subtree查询 obj
    TransStart(g_conn_async);
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_082_variableWhen_016");
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

   // 这里需要prepar list的labelname
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 对子节点字段 做replace操作  --ListOne
    char fieldStr[8] = "str000";
    ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool boolValue = true;
    ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_BOOL, &boolValue, sizeof(boolValue), "F2",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 container NP 子节点做replace操作  -- ContainerTwo 
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 对 container P 子节点做replace操作  -- ContainerThree 
    ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 这里需要prepar list的labelname
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作  --ListTwo
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    /*
    diff里只有record1，只会校验record1
    传入record.主键值
    校验哪个record根据diff里数据来，取数据根据全局的数据来；
    */

    // when校验 -- 输入参数record2的值；修改str000.F1= 只校验涉及F1的plan (plan里的解析是按全局的数据来)
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_BUTT;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath = "";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE,
        .cfgJson = R"({"TARGET_LIST-RECORD":  "str000"})"};
    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    char *diffreply01 = NULL;
    readJanssonFile("diffWhen/diff016.json", &diffreply01);

    std::vector<std::string> diffreply(1);
    diffreply[0] = diffreply01;
    testFetchAndDeparseDiff(g_stmt_async, batch, diffreply, data);
    free(diffreply01);

    //subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_082_variableWhen_016_2");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns variableWhen -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}

