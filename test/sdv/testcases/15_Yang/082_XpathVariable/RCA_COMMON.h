
/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#ifndef RCA_COMMON_H
#define RCA_COMMON_H
extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/epoll.h>
#include <assert.h>
#include <errno.h>
#include <stdint.h>
#include <typeinfo>
#include <limits.h>
#include <list>
#include <atomic>
#include <vector>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

#define TIMEZONEMAXCMD 512
GmcConnT *g_conn_sync = NULL, *g_conn_async = NULL;
GmcStmtT *g_stmt_sync = NULL, *g_stmt_async = NULL;
char *g_vertexschema = NULL, *g_edgeschema = NULL;
GmcNodeT *g_vertexLabelT0Node = NULL;
GmcNodeT *g_vertexLabelT1Node = NULL;
GmcNodeT *g_vertexLabelT2Node = NULL;
GmcNodeT *g_vertexLabelT3Node = NULL;
GmcNodeT *SubT1choiceCase = NULL;

GmcNodeT *g_containerT2Node = NULL;
GmcNodeT *g_choiceNode = NULL;
GmcNodeT *g_choiceCaseNode = NULL;
GmcNodeT *g_caseContainerNode1 = NULL;
GmcNodeT *g_caseContainerNode2 = NULL;
GmcNodeT *g_containerT3Node = NULL;

GmcNodeT *g_listContainerNode1 = NULL;
GmcNodeT *g_listChoiceNode = NULL;
GmcNodeT *g_listChoiceCaseNode1 = NULL;
GmcNodeT *g_listContainerNode2 = NULL;
const char *g_savepointName = "sp";
const char *g_vertexLabelT0 = "ContainerOne";
const char *g_vertexLabelT1 = "ListOne";
const char *g_vertexLabelT2 = "LeafList";
const char *g_vertexLabelT3 = "ListTwo";
const char *g_edgeLabeT0T1 = "ContainerList";
const char *g_edgeLabeT0T2 = "ContainerLeafList";
const char *g_edgeLabeT1T3 = "ListListTwo";

const char *g_containerNodeName2 = "ContainerTwo";
const char *g_choiceNodeName = "Choice";
const char *g_choiceCaseNodeName = "CaseOne";
const char *g_caseContainerNodeName1 = "CaseContainerOne";
const char *g_caseContainerNodeName2 = "CaseContainerTwo";
const char *g_containerNodeName3 = "ContainerThree";


const char *g_listContainerNodeName1 = "ListContainerOne";
const char *g_listChoiceNodeName = "Listchoice";
const char *g_listChoiceCaseNodeName = "ListchoiceCase";
const char *g_listContainerNodeName2 = "ListContainerTwo";
struct FetchRetCbParam01 {
    int step;
    GmcStmtT *stmt;
    int32_t expectStatus;                    // 预期的操作状态
    uint32_t filterMode;                    // 过滤模式，使用枚举GmcSubtreeFilterModeE设置值
    uint32_t lastExpectIdx;                 // 分批查询上次查询期望结果的最后索引
    std::vector<std::string> &expectReply;  // 过滤模式下预期返回的查询结果, 校验用的字符串
};
AsyncUserDataT userData = {0};
// 32deep
const char *g_vertexLabelroot = "root";
const char *g_labelconfig = R"(
{
    "max_record_count":10000,
    "isFastReadUncommitted":0,
    "auto_increment":1,
    "yang_model":1
})";
const char *g_keyName = "PK";
// 申请各个表用的stmt
GmcStmtT *g_stmt_sync_T0 = NULL;
GmcStmtT *g_stmt_sync_T1List = NULL;
GmcStmtT *g_stmt_sync_T2List = NULL;
GmcStmtT *g_stmt_sync_LeafList = NULL;
// 申请32depp表用的stmt
GmcStmtT *g_stmt_sync_32deep = NULL;
int GetTimeZoneType()
{
    char timecmd[TIMEZONEMAXCMD] = {0};
    // 将date 定向到 time.ini中
    (void)snprintf(timecmd, TIMEZONEMAXCMD, " date > time.ini");
    system(timecmd);

    // 返回0为CST时区，1为UTC时区
    char key1[2048] = "CST";
    char key2[2048] = "Defaul";
    char key3[2048] = "UTC";
    FILE *fp;
    char buffer[2048];
    // 从 time.ini 获取 date时区
    (void)snprintf(timecmd, TIMEZONEMAXCMD, "cat  time.ini");
    printf("%s\n", timecmd);
    fp = popen(timecmd, "r");
    (void)fgets(buffer, sizeof(buffer), fp);
    printf("%s", buffer);
    // 如果获取的字符串中 包括 CST 则时区类型为 CST
    if (strstr(buffer, key1)) {
        printf("key1 is %s  buffer  is %s \n", key1, buffer);
        (void)pclose(fp);
         fp = NULL;
        return 0;
    // 如果获取的字符串中 包括 Defaul 则时区类型为 Defaul
    } else if (strstr(buffer, key2)) {
        printf("key2 is %s  buffer  is %s \n", key2, buffer);
        (void)pclose(fp);
         fp = NULL;
        return 1;
    } else if (strstr(buffer, key3)) {
        printf("key3 is %s  buffer  is %s \n", key3, buffer);
        (void)pclose(fp);
         fp = NULL;
        return 1;
    } else {
        printf(" buffer  is %s \n", buffer);
        (void)pclose(fp);
        fp = NULL;
        return 0;
    }
}
void TestYangAllocAllstmt()
{
    int ret = GmcAllocStmt(g_conn_async, &g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_sync_T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_sync_LeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void TestYangAlloc32Deppstmt()
{
    int ret = GmcAllocStmt(g_conn_async, &g_stmt_sync_32deep);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
string testYangTreeToStr(GmcStmtT *stmt, const GmcYangTreeT *reply, bool isDiff)
{
    string res;
    if (!isDiff) {
        char *replyJson = NULL;
        EXPECT_EQ(GMERR_OK, GmcYangTreeToJson(reply, &replyJson));
        res = string(replyJson);
        return res;
    }
}
void CheckTreeReply(const GmcYangTreeT **yangTree, uint32_t count, FetchRetCbParam01 *param01, bool isDiff = false)
{
    uint32_t idx = param01->lastExpectIdx;
    ASSERT_TRUE(param01->expectReply.size() >= (idx + count));  // 断言防止越界
    for (uint32_t i = 0; i < count; i++) {
        if (yangTree[i] == NULL) {
            ASSERT_STREQ(param01->expectReply[idx + i].c_str(), "{}");
            continue;
        }
        std::string reply = testYangTreeToStr(param01->stmt, yangTree[i], isDiff);
        EXPECT_TRUE(testYangJsonIsEqual(reply.c_str(), param01->expectReply[idx + i].c_str()))
            << "replyJson:\n"
            << reply << endl;
        GmcYangFreeTree(yangTree[i]);
    }
}
void AsyncFetchRetCb01(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    FetchRetCbParam01 *param01 = reinterpret_cast<FetchRetCbParam01 *>(userData);
    ASSERT_EQ(param01->expectStatus, status) << errMsg;
    if (param01->expectStatus != GMERR_OK) {
        ASSERT_NE((errMsg != NULL), 0);
        ASSERT_NE(strcmp(errMsg, ""), 0);
        return;
    }
    bool isEnd = false;
    uint32_t count = 0;
    const GmcYangTreeT **yangTree = NULL;
    uint32_t idx = param01->lastExpectIdx;
    ASSERT_EQ(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
    ASSERT_TRUE(yangTree != NULL);
    if (param01->expectReply.size() != 0) {
        CheckTreeReply(yangTree, count, param01);
    }
    // 判断是否全部结果都已获取，全部获取则释放fetchRet，否则再发送一次查询直至全部结果都已经获取
    if (isEnd) {
        param01->step++;
        GmcYangFreeFetchRet(fetchRet);
        return;
    }
    param01->lastExpectIdx = idx + count;
    ASSERT_EQ(GMERR_OK, GmcYangSubtreeFilterExecuteAsync(param01->stmt, NULL, fetchRet, AsyncFetchRetCb01, param01));
    return;
}
void TestYangFreeAllstmt()
{
    GmcFreeStmt(g_stmt_sync_T0);
    GmcFreeStmt(g_stmt_sync_T1List);
    GmcFreeStmt(g_stmt_sync_T2List);
    GmcFreeStmt(g_stmt_sync_LeafList);
}
void TestYangFree2Deepstmt()
{
    GmcFreeStmt(g_stmt_sync_32deep);
}
void TransStart(GmcConnT *conn)
{
    int ret = 0;
    // 事务结构体
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE;  // 可重复读
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;  // 乐观事务

    // 开启乐观事务
    ret = GmcTransStartAsync(conn, &config, trans_start_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    if (userData.status != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
        AW_FUN_Log(LOG_DEBUG, "trans start error code:%d\n", userData.status);
        ret = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    memset(&userData, 0, sizeof(AsyncUserDataT));
}

void TransCommit(GmcConnT *conn, int expectStatus = GMERR_OK)
{
    int ret = 0;
    ret = GmcTransCommitAsync(conn, trans_commit_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(expectStatus, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
}
void TransRollback(GmcConnT *conn, int expectStatus = GMERR_OK)
{
    int ret = 0;
    ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    if (userData.status != expectStatus) {
        AW_MACRO_EXPECT_EQ_INT(expectStatus, userData.status);
        AW_FUN_Log(LOG_DEBUG, "trans rollback error code:%d\n", userData.status);
        ret = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    memset(&userData, 0, sizeof(AsyncUserDataT));
}

void CreateSavepoint(GmcConnT *conn, const char *savepointname, int expstatus = GMERR_OK)
{
    int ret = 0;
    ret = GmcTransCreateSavepointAsync(conn, savepointname, TransSavePointCb, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    if (userData.status != expstatus) {
        AW_MACRO_EXPECT_EQ_INT(expstatus, userData.status);
        AW_FUN_Log(LOG_DEBUG, "create savepoint error code:%d\n", userData.status);
        ret = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    memset(&userData, 0, sizeof(AsyncUserDataT));
}

void ReleaseSavepoint(GmcConnT *conn, const char *savepointname, int expstatus = GMERR_OK)
{
    int ret = 0;
    ret = GmcTransReleaseSavepointAsync(conn, savepointname, TransSavePointCb, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    if (userData.status != expstatus) {
        AW_MACRO_EXPECT_EQ_INT(expstatus, userData.status);
        AW_FUN_Log(LOG_DEBUG, "release savepoint error code:%d\n", userData.status);
        ret = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    memset(&userData, 0, sizeof(AsyncUserDataT));
}

void RollbackSavepoint(GmcConnT *conn, const char *savepointname, int expstatus = GMERR_OK)
{
    int ret = 0;
    ret = GmcTransRollBackSavepointAsync(conn, savepointname, TransSavePointCb, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    if (userData.status != expstatus) {
        AW_MACRO_EXPECT_EQ_INT(expstatus, userData.status);
        AW_FUN_Log(LOG_DEBUG, "rollback savepoint error code:%d\n", userData.status);
        ret = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    memset(&userData, 0, sizeof(AsyncUserDataT));
}

int testBatchPrepare(GmcConnT *conn, GmcBatchT **batch, GmcBatchTypeE batchType = GMC_BATCH_YANG,
    GmcYangDiffTypeE diffType = GMC_YANG_DIFF_DELAY_READ_ON)
{
    int ret = 0;
    GmcBatchOptionT batchOption;

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBatchType(&batchOption, batchType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcYangBatchOptionSetDiffType(&batchOption, diffType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchPrepare(conn, &batchOption, batch);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }

    return ret;
}

void BatchExecute(GmcBatchT *batch, uint32_t exptotalnum, uint32_t expsuccnum, int expstatus = GMERR_OK)
{
    int ret = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(expstatus, userData.status);
    AW_MACRO_EXPECT_EQ_INT(exptotalnum, userData.totalNum);
    AW_MACRO_EXPECT_EQ_INT(expsuccnum, userData.succNum);
    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));
}

int testsubtreeSetvalue(GmcNodeT * Node, GmcDataTypeE type, void *value, uint32_t size,
    const char *fieldname, GmcYangPropOpTypeE optype)
{
    int ret = 0;

    GmcPropValueT propValue;
    memcpy(propValue.propertyName, fieldname, (strlen(fieldname) + 1));
    propValue.type = type;
    propValue.value = value;
    propValue.size = size;
    ret = GmcYangSetNodeProperty(Node, &propValue, optype);
    return ret;
}
// yang set 字段
int testYangSetField(GmcNodeT *node, GmcDataTypeE type, void *value, uint32_t size,
    const char *fieldname, GmcYangPropOpTypeE optype)
{
    int ret = 0;

    GmcPropValueT propValue;
    memcpy(propValue.propertyName, fieldname, (strlen(fieldname) + 1));
    propValue.type = type;
    propValue.value = value;
    propValue.size = size;
    ret = GmcYangSetNodeProperty(node, &propValue, optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    return ret;
}

// yang set T0层f0
void testYangSetVertexProperty_F0(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE optype)
{
    int ret = 0;
    uint32_t f0Value = i;
    ret = testYangSetField(node, GMC_DATATYPE_UINT32, &f0Value, sizeof(uint32_t), "F0", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// yang set T0层一般字段 all type
void testYangSetVertexProperty(GmcNodeT *node, int32_t f1, bool f2, double f3, bool f4, float f5,
    GmcYangPropOpTypeE optype)
{
    int ret = 0;

    ret = testYangSetField(node, GMC_DATATYPE_INT32, &f1, sizeof(int32_t), "F1", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(node, GMC_DATATYPE_BOOL, &f2, sizeof(f2), "F2", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(node, GMC_DATATYPE_DOUBLE, &f3, sizeof(f3), "F3", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(node, GMC_DATATYPE_BOOL, &f4, sizeof(f4), "F4", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(node, GMC_DATATYPE_FLOAT, &f5, sizeof(f5), "F5", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char f6value[8] = "string";
    ret = testYangSetField(node, GMC_DATATYPE_STRING, &f6value, (strlen(f6value)), "F6", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char f7 = 'A';
    ret = testYangSetField(node, GMC_DATATYPE_CHAR, &f7, sizeof(char), "F7", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void testYangSetNodeProperty(GmcNodeT *node, int32_t f1, bool f2, double f3, bool f4, float f5,
    GmcYangPropOpTypeE optype)
{
    int ret = 0;
    ret = testYangSetField(node, GMC_DATATYPE_INT32, &f1, sizeof(int32_t), "F1", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(node, GMC_DATATYPE_BOOL, &f2, sizeof(f2), "F2", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(node, GMC_DATATYPE_DOUBLE, &f3, sizeof(f3), "F3", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(node, GMC_DATATYPE_BOOL, &f4, sizeof(f4), "F4", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(node, GMC_DATATYPE_FLOAT, &f5, sizeof(f5), "F5", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char f6value[8] = "string";
    ret = testYangSetField(node, GMC_DATATYPE_STRING, &f6value, (strlen(f6value)), "F6", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// yang set list类型 主键
void testYangSetNodeProperty_PK(GmcNodeT *node, uint32_t f0, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    uint32_t pkValue = f0;
    ret = testYangSetField(node, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "F0", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void testSetKeyNameAndValue(GmcStmtT *stmt, uint32_t keyvalue, uint32_t numPID = 0, bool isList = false)
{
    int ret;

    // 设置KeyValue，PID为上层节点的ID，自增从1开始，PID=0，代表是根节点
    // yang模型现在修改为只能ID或PID为主键，只有list可以有PID+其他属性为主键
    if (numPID != 0) {
        // 只有list的主键允许设置为PID+属性
        if (isList) {
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &keyvalue, sizeof(uint32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    ret = GmcSetIndexKeyName(stmt, g_keyName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

int testTransStartAsync(GmcConnT *conn, GmcTxConfigT Config)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransStartAsync(conn, &Config, trans_start_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        return ret;
    }
}

int testTransCommitAsync(GmcConnT *conn)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        return ret;
    }
}

string GetOpTypeString(GmcDiffOpTypeE op)
{
    switch (op) {
        case GMC_DIFF_OP_CREATE:
            return "create";
        case GMC_DIFF_OP_REMOVE:
            return "remove";
        case GMC_DIFF_OP_UPDATE:
            return "update";
        default:
            return "invalid";
    }
}

// 比较s1 s2两个字符串，如果字符串相同，返回空串，不同返回从不同位置开始的子串
string StrCmp(string &s1, string &s2)
{
    int i = 0;
    for (; s1[i] != 0 && s2[i] != 0; i++) {
        if (s1[i] != s2[i]) {
            return s1.substr(i);
        }
    }

    if (s1[i] == 0 && s2[i] == 0) {
        return string("");
    } else if (s2[i] == 0) {
        return s1.substr(i);
    } else {
        return s2.substr(i);
    }
}
string GetValueString(GmcYangNodeValueT *value)
{
    switch (value->type) {
        case GMC_DATATYPE_STRING:
            return string("") + (const char *)value->value;
        case GMC_DATATYPE_CHAR:
        case GMC_DATATYPE_UCHAR:
            return "" + to_string(*(const char *)value->value);
            break;
        case GMC_DATATYPE_INT8:
        case GMC_DATATYPE_UINT8:
            return "" + to_string(*(const uint8_t *)value->value);
        case GMC_DATATYPE_INT16:
        case GMC_DATATYPE_UINT16:
            return "" + to_string(*(const uint16_t *)value->value);
        case GMC_DATATYPE_INT32:
        case GMC_DATATYPE_UINT32:
            return "" + to_string(*(const uint32_t *)value->value);
        case GMC_DATATYPE_UINT64:
        case GMC_DATATYPE_INT64:
        case GMC_DATATYPE_TIME:
            return "" + to_string(*(const uint64_t *)value->value);
        case GMC_DATATYPE_FLOAT:
            return "" + to_string(*(const float *)value->value);
        case GMC_DATATYPE_DOUBLE:
            return "" + to_string(*(const double *)value->value);
        case GMC_DATATYPE_NULL:
            return string("NULL");
        default:
            return string("NIL:") + to_string(value->type);
    }
}
string GetVertexString(GmcStmtT *stmt, GmcYangNodeT *info, bool isNewData)
{
    GmcYangNodeValueT *propValue = NULL;
    string res = "";
    int32_t ret;
    GmcDiffOpTypeE opType;
    EXPECT_EQ(GMERR_OK, GmcYangNodeGetDiffOpType(info, &opType));
    uint32_t propNum = 0;
    if ((isNewData && opType == GMC_DIFF_OP_CREATE) || (!isNewData && opType == GMC_DIFF_OP_REMOVE) ||
        opType == GMC_DIFF_OP_UPDATE) {
        ret = GmcYangNodeGetKeyPropNum(info, &propNum);
        if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
            return "";
        }
        EXPECT_EQ(GMERR_OK, ret);
        res += "priKey(";
        for (unsigned int i = 0; i < propNum; i++) {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetKeyPropValue(info, i, &propValue));
            string propNameString = propValue->name;
            res += propNameString + ":" + GetValueString(propValue) + ",";
        }
        if (res[res.size() - 1] == ',') {
            res[res.size() - 1] = ')';
        } else {
            res += ")";
        }
    }
    bool isHasNewPrevNode = false;
    bool isHasOldPrevNode = false;
    ret = GmcYangNodeHasNewPrev(info, &isHasNewPrevNode);
    if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
        return "";
    }
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangNodeHasOldPrev(info, &isHasOldPrevNode);
    if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
        return "";
    }
    EXPECT_EQ(GMERR_OK, ret);
    if ((isNewData && isHasNewPrevNode) || (!isNewData && isHasOldPrevNode) || opType == GMC_DIFF_OP_UPDATE) {
        bool isExist = (isNewData ? isHasNewPrevNode : isHasOldPrevNode);
        if (!isExist) {
            if (res == "") {
                res = "NULL";
            }
            return res;
        }
        res += ", preKey(";
        ret = GmcYangNodeGetKeyPropNum(info, &propNum);
        if (ret != GMERR_OK) {
            if (res == "") {
                res = "NULL";
            }
            return res;
        }
        for (unsigned int i = 0; i < propNum; i++) {
            ret = isNewData ? GmcYangNodeGetNewPrevKey(info, i, &propValue)
                            : GmcYangNodeGetOldPrevKey(info, i, &propValue);
            if (ret == GMERR_INVALID_NAME) {
                continue;
            } else if (ret != GMERR_OK) {
                cout << "error:" << ret << ", " << propValue->name;
            }
            string propNameString = propValue->name;
            res += propNameString + ":" + GetValueString(propValue) + ",";
        }
        if (res[res.size() - 1] == ',') {
            res[res.size() - 1] = ')';
        } else {
            res += ")";
        }
    }
    if (res == "") {
        res = "NULL";
    }
    return res;
}
void GetYangInfoString(GmcStmtT *stmt, GmcYangNodeT *info, string parentFix, string &res)
{
    res = parentFix + ":";
    GmcDiffOpTypeE opType;
    EXPECT_EQ(GMERR_OK, GmcYangNodeGetDiffOpType(info, &opType));
    res += GetOpTypeString((GmcDiffOpTypeE)opType);

    GmcYangNodeTypeE nodeType;
    EXPECT_EQ(GMERR_OK, GmcYangNodeGetType(info, &nodeType));
    if (nodeType == GMC_YANG_FIELD) {
        GmcYangNodeValueT *newValue = NULL;
        GmcYangNodeValueT *oldValue = NULL;
        if (opType == GMC_DIFF_OP_CREATE) {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetNewValue(info, &newValue));
            res += "(" + GetValueString(newValue) + ")";
        } else if (opType == GMC_DIFF_OP_REMOVE) {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetOldValue(info, &oldValue));
            res += "(" + GetValueString(oldValue) + ")";
        } else {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetNewValue(info, &newValue));
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetOldValue(info, &oldValue));
            res += "(" + GetValueString(newValue) + "," + GetValueString(oldValue) + ")";
        }
    } else {
        if (strcmp(GetVertexString(stmt, info, true).c_str(), "") != 0) {
            res += "[(" + GetVertexString(stmt, info, true) + "),";
        }
        if (strcmp(GetVertexString(stmt, info, false).c_str(), "") != 0) {
            res += "(" + GetVertexString(stmt, info, false) + ")]";
        }
    }
}
// 深度遍历生成diff信息字符串
void DFSYangNode(GmcStmtT *stmt, GmcYangNodeT *parent, string prefix, string &resStr)
{
    GmcYangNodeT *child = NULL;
    GmcYangNodeT *prevChild = NULL;
    string res = "";
    string diffStr;
    do {
        ASSERT_EQ(GMERR_OK, GmcYangNodeGetNext(parent, prevChild, &child));
        prevChild = child;
        if (child != NULL) {
            // 打印diff信息
            const char *nodeName;
            ASSERT_EQ(GMERR_OK, GmcYangNodeGetName(child, &nodeName));
            string childName = prefix + nodeName;
            ASSERT_NO_FATAL_FAILURE(GetYangInfoString(stmt, child, childName, diffStr));
            res += diffStr + "\n";
            string childStr;
            DFSYangNode(stmt, child, nodeName + string("."), childStr);
            res += childStr;
        }
    } while (child != NULL);
    resStr += res;
}
void TestCheckYangTree(GmcStmtT *stmt, const GmcYangTreeT **yangTrees, uint32_t count, vector<string> &expectReply)
{
    ASSERT_EQ(expectReply.size(), count);
    GmcYangNodeT *rootInfo = NULL;
    for (uint32_t i = 0; i < count; i++) {
        ASSERT_EQ(GMERR_OK, GmcYangGetRootNode(yangTrees[i], &rootInfo));
        string res;
        const char *rootName;
        ASSERT_EQ(GMERR_OK, GmcYangNodeGetName(rootInfo, &rootName));
        ASSERT_NO_FATAL_FAILURE(GetYangInfoString(stmt, rootInfo, rootName + string(""), res));
        res += "\n";
        ASSERT_NO_FATAL_FAILURE(DFSYangNode(stmt, rootInfo, rootName + string("."), res));
        char fileName[128] = {0};
        int ret = sprintf_s(fileName, sizeof(fileName), "perf_%dDiffTreeInfo", count);
        if (ret == -1) {
            AW_FUN_Log(LOG_INFO, "sprintf_s error\n");
            return;
        }
        FILE *fp = fopen(fileName, "w");
        if (fp == NULL) {
            AW_FUN_Log(LOG_INFO, "fopen error\n");
            return;
        }
        ret = fputs(res.c_str(), fp);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "fputs error\n");
            return;
        }
        ret = fclose(fp);
        if (ret == -1) {
            AW_FUN_Log(LOG_INFO, "fclose error\n");
            return;
        }
        cout << "actual diff：\n" << res;
        cout << "expect：\n" << expectReply[0].c_str();
        ASSERT_STREQ(StrCmp(expectReply[i], res).c_str(), "") << i;
        ASSERT_EQ(GMERR_OK, GmcYangFreeTree(yangTrees[i]));
    }
}

// diff 回调
void FetchDiff_callback(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        uint32_t count = 0;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            const GmcYangTreeT **yangTree = NULL;
            ASSERT_EQ(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));

            ASSERT_EQ((uint32_t)(*userData1->expectDiff).size(), count);
            TestCheckYangTree(userData1->stmt, yangTree, count, *userData1->expectDiff);
            GmcYangFreeFetchRet(fetchRet);
        }
        userData1->recvNum++;
    }
}

// 获取diff
void testFetchAndDeparseDiff(
    GmcStmtT *stmt, GmcBatchT *batch, vector<string> &expectDiff, AsyncUserDataT data, int rets = GMERR_OK)
{
    data.stmt = stmt;
    data.expectDiff = &expectDiff;
    int ret = GmcYangFetchDiffExecuteAsync(stmt, NULL, FetchDiff_callback, &data);
    if (ret != GMERR_OK) {
        testGmcGetLastError(NULL);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(rets, data.status);
}

// 模型树直接预置 数据
void testYangPresetAllDate(GmcConnT *conn)
{
    int ret ;
    // 开启事务
    GmcTxConfigT TrxConfig = {0};
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 启动事务
    ret = testTransStartAsync(conn, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcNodeT *T0Node = NULL;
    GmcNodeT *T1containerNode = NULL;
    GmcNodeT *T3containerNode = NULL;
    GmcNodeT *T1SubT1choice = NULL;
    GmcNodeT *SubT1choiceCase = NULL;

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_T0, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 根节点 set 字段值
    uint32_t f0value = 1;
    int32_t f1 = 1;
    bool f2 = true;
    double f3 = 1;
    bool f4 = true;
    float f5 = 1;
    // 设置根节点属性
    testYangSetVertexProperty_F0(T0Node, f0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty(T0Node, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    // 设置child节点
    ret = GmcYangEditChildNode(T0Node, g_containerNodeName2, GMC_OPERATION_INSERT, &T1containerNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点属性
    testYangSetVertexProperty_F0(T1containerNode, f0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodeProperty(T1containerNode, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    // 设置child节点
    ret = GmcYangEditChildNode(T0Node, g_containerNodeName3, GMC_OPERATION_INSERT, &T3containerNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点属性
    testYangSetVertexProperty_F0(T3containerNode, f0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodeProperty(T3containerNode, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    // 设置child节点
    ret = GmcYangEditChildNode(T0Node, g_choiceNodeName, GMC_OPERATION_INSERT, &T1SubT1choice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T1SubT1choice, g_choiceCaseNodeName, GMC_OPERATION_INSERT, &SubT1choiceCase);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点属性
    testYangSetVertexProperty_F0(SubT1choiceCase, f0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodeProperty(SubT1choiceCase, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    for (uint32_t i = 1; i < 11; i++) {
        int32_t listf1 = i;
        bool listf2 = true;
        double listf3 = i;
        bool listf4 = true;
        float listf5 = 1;
        GmcNodeT *T1ListNode = NULL;
        GmcNodeT *T2containerNode = NULL;
        GmcNodeT *T2choiceNode = NULL;
        GmcNodeT *T2choiceCaseNode = NULL;
        GmcNodeT *T2containerNodeTwo = NULL;
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &T1ListNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetNodeProperty_PK(T1ListNode, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetNodeProperty(T1ListNode, listf1, listf2, listf3, listf4, listf5,
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        // 设置child节点
        ret = GmcYangEditChildNode(T1ListNode, g_listContainerNodeName1, GMC_OPERATION_INSERT, &T2containerNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetVertexProperty_F0(T2containerNode, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetNodeProperty(T2containerNode, listf1, listf2, listf3, listf4, listf5,
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        // 设置child节点
        ret = GmcYangEditChildNode(T1ListNode, g_listChoiceNodeName, GMC_OPERATION_INSERT, &T2choiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(T2choiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_INSERT, &T2choiceCaseNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置node节点属性
        testYangSetVertexProperty_F0(T2choiceCaseNode, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetNodeProperty(T2choiceCaseNode, listf1, listf2, listf3, listf4, listf5,
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        // 设置child节点
        ret = GmcYangEditChildNode(T1ListNode, g_listContainerNodeName2, GMC_OPERATION_INSERT, &T2containerNodeTwo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetVertexProperty_F0(T2containerNodeTwo, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetNodeProperty(T2containerNodeTwo, listf1, listf2, listf3, listf4, listf5,
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int k = 1; k < 11; k++) {
            int32_t list2f1 = k;
            bool list2f2 = true;
            double list2f3 = k;
            bool list2f4 = true;
            float list2f5 = 1;
            GmcNodeT *T2ListNode = NULL;
            // 这里需要prepar list的labelname
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_sync_T2List, &T2ListNode);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            testYangSetNodeProperty_PK(T2ListNode, k, GMC_YANG_PROPERTY_OPERATION_CREATE);
            testYangSetNodeProperty(T2ListNode, list2f1, list2f2, list2f3, list2f4, list2f5,
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    // 这里需要prepar list的labelname
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_LeafList, g_vertexLabelT2, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_LeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_LeafList, &g_vertexLabelT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetNodeProperty_PK(g_vertexLabelT2Node, 1, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_LeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    printf("data.succNum is %d \n", data.succNum);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 32deep 预置数据
void testYang32deepPresetAllDate(GmcConnT *conn)
{
    int ret ;
    // 创建乐观事务
    TransStart(conn);
    GmcBatchT *batch = NULL;
    // 设置批处理
    ret = testBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *T0Node = NULL;
    GmcNodeT *T1Node = NULL;
    GmcNodeT *T2Node = NULL;
    GmcNodeT *T3Node = NULL;
    GmcNodeT *T4Node = NULL;
    GmcNodeT *T5Node = NULL;
    GmcNodeT *T6Node = NULL;
    GmcNodeT *T7Node = NULL;
    GmcNodeT *T8Node = NULL;
    GmcNodeT *T9Node = NULL;
    GmcNodeT *T10Node = NULL;
    GmcNodeT *T11Node = NULL;
    GmcNodeT *T12Node = NULL;
    GmcNodeT *T13Node = NULL;
    GmcNodeT *T14Node = NULL;
    GmcNodeT *T15Node = NULL;
    GmcNodeT *T16Node = NULL;
    GmcNodeT *T17Node = NULL;
    GmcNodeT *T18Node = NULL;
    GmcNodeT *T19Node = NULL;
    GmcNodeT *T20Node = NULL;
    GmcNodeT *T21Node = NULL;
    GmcNodeT *T22Node = NULL;
    GmcNodeT *T23Node = NULL;
    GmcNodeT *T24Node = NULL;
    GmcNodeT *T25Node = NULL;
    GmcNodeT *T26Node = NULL;
    GmcNodeT *T27Node = NULL;
    GmcNodeT *T28Node = NULL;
    GmcNodeT *T29Node = NULL;
    GmcNodeT *T30Node = NULL;
    GmcNodeT *T31Node = NULL;
    GmcNodeT *listNode = NULL;
    const char *vertexLabelT0 = "root";
    const char *vertexLabelT1 = "T1";
    const char *vertexLabelT2 = "T2";
    const char *vertexLabelT3 = "T3";
    const char *vertexLabelT4 = "T4";
    const char *vertexLabelT5 = "T5";
    const char *vertexLabelT6 = "T6";
    const char *vertexLabelT7 = "T7";
    const char *vertexLabelT8 = "T8";
    const char *vertexLabelT9 = "T9";
    const char *vertexLabelT10 = "T10";
    const char *vertexLabelT11 = "T11";
    const char *vertexLabelT12 = "T12";
    const char *vertexLabelT13 = "T13";
    const char *vertexLabelT14 = "T14";
    const char *vertexLabelT15 = "T15";
    const char *vertexLabelT16 = "T16";
    const char *vertexLabelT17 = "T17";
    const char *vertexLabelT18 = "T18";
    const char *vertexLabelT19 = "T19";
    const char *vertexLabelT20 = "T20";
    const char *vertexLabelT21 = "T21";
    const char *vertexLabelT22 = "T22";
    const char *vertexLabelT23 = "T23";
    const char *vertexLabelT24 = "T24";
    const char *vertexLabelT25 = "T25";
    const char *vertexLabelT26 = "T26";
    const char *vertexLabelT27 = "T27";
    const char *vertexLabelT28 = "T28";
    const char *vertexLabelT29 = "T29";
    const char *vertexLabelT30 = "T30";
    const char *vertexLabelT31 = "T31";
    const char *vertexLabellist = "List32deep";

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 1;
    testYangSetVertexProperty_F0(T0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 对 container NP 子节点做replace操作
    ret = GmcYangEditChildNode(T0Node, vertexLabelT1, GMC_OPERATION_INSERT, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T1Node, vertexLabelT2, GMC_OPERATION_INSERT, &T2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T2Node, vertexLabelT3, GMC_OPERATION_INSERT, &T3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T3Node, vertexLabelT4, GMC_OPERATION_INSERT, &T4Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T4Node, vertexLabelT5, GMC_OPERATION_INSERT, &T5Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T5Node, vertexLabelT6, GMC_OPERATION_INSERT, &T6Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T6Node, vertexLabelT7, GMC_OPERATION_INSERT, &T7Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T7Node, vertexLabelT8, GMC_OPERATION_INSERT, &T8Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T8Node, vertexLabelT9, GMC_OPERATION_INSERT, &T9Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T9Node, vertexLabelT10, GMC_OPERATION_INSERT, &T10Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T10Node, vertexLabelT11, GMC_OPERATION_INSERT, &T11Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T11Node, vertexLabelT12, GMC_OPERATION_INSERT, &T12Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T12Node, vertexLabelT13, GMC_OPERATION_INSERT, &T13Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T13Node, vertexLabelT14, GMC_OPERATION_INSERT, &T14Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T14Node, vertexLabelT15, GMC_OPERATION_INSERT, &T15Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T15Node, vertexLabelT16, GMC_OPERATION_INSERT, &T16Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T16Node, vertexLabelT17, GMC_OPERATION_INSERT, &T17Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T17Node, vertexLabelT18, GMC_OPERATION_INSERT, &T18Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T18Node, vertexLabelT19, GMC_OPERATION_INSERT, &T19Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T19Node, vertexLabelT20, GMC_OPERATION_INSERT, &T20Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T20Node, vertexLabelT21, GMC_OPERATION_INSERT, &T21Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T21Node, vertexLabelT22, GMC_OPERATION_INSERT, &T22Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T22Node, vertexLabelT23, GMC_OPERATION_INSERT, &T23Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T23Node, vertexLabelT24, GMC_OPERATION_INSERT, &T24Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T24Node, vertexLabelT25, GMC_OPERATION_INSERT, &T25Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T25Node, vertexLabelT26, GMC_OPERATION_INSERT, &T26Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T26Node, vertexLabelT27, GMC_OPERATION_INSERT, &T27Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T27Node, vertexLabelT28, GMC_OPERATION_INSERT, &T28Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T28Node, vertexLabelT29, GMC_OPERATION_INSERT, &T29Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T29Node, vertexLabelT30, GMC_OPERATION_INSERT, &T30Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T30Node, vertexLabelT31, GMC_OPERATION_INSERT, &T31Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // // 对子节点字段 做replace操作
    fieldValue = 1;
    testYangSetVertexProperty_F0(T30Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 对子节点字段 做merge操作
    int32_t f1fieldValue = 1;
    bool f4 = true;
    double f3 = 1;
    float f5 = 1;
    GmcPropValueT propValue;
    ret = testYangSetField(T31Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(T31Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memcpy(propValue.propertyName, "F3", (strlen("F3") + 1));
    propValue.type = GMC_DATATYPE_DOUBLE;
    propValue.value = &f3;
    propValue.size = sizeof(double);
    ret = GmcYangSetNodeProperty(T31Node, &propValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(T31Node, GMC_DATATYPE_FLOAT, &f5, sizeof(float), "F5", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 这里需要prepar list的labelname
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, vertexLabellist, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T1List, &listNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    testYangSetVertexProperty_F0(listNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = testYangSetField(listNode, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(listNode, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memcpy(propValue.propertyName, "F3", (strlen("F3") + 1));
    propValue.type = GMC_DATATYPE_DOUBLE;
    propValue.value = &f3;
    propValue.size = sizeof(double);
    ret = GmcYangSetNodeProperty(listNode, &propValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(listNode, GMC_DATATYPE_FLOAT, &f5, sizeof(float), "F5", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 提交批处理
    BatchExecute(batch, 2, 2);

    // 提交事务
    TransCommit(conn);
}
void testYang32deepCasePresetAllDate(GmcConnT *conn)
{
    int ret ;
    // 创建乐观事务
    TransStart(conn);
    GmcBatchT *batch = NULL;
    // 设置批处理
    ret = testBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *T0Node = NULL;
    GmcNodeT *T1Node = NULL;
    GmcNodeT *T2Node = NULL;
    GmcNodeT *T3Node = NULL;
    GmcNodeT *T4Node = NULL;
    GmcNodeT *T5Node = NULL;
    GmcNodeT *T6Node = NULL;
    GmcNodeT *T7Node = NULL;
    GmcNodeT *T8Node = NULL;
    GmcNodeT *T9Node = NULL;
    GmcNodeT *T10Node = NULL;
    GmcNodeT *T11Node = NULL;
    GmcNodeT *T12Node = NULL;
    GmcNodeT *T13Node = NULL;
    GmcNodeT *T14Node = NULL;
    GmcNodeT *T15Node = NULL;
    GmcNodeT *T16Node = NULL;
    GmcNodeT *T17Node = NULL;
    GmcNodeT *T18Node = NULL;
    GmcNodeT *T19Node = NULL;
    GmcNodeT *T20Node = NULL;
    GmcNodeT *T21Node = NULL;
    GmcNodeT *T22Node = NULL;
    GmcNodeT *T23Node = NULL;
    GmcNodeT *T24Node = NULL;
    GmcNodeT *T25Node = NULL;
    GmcNodeT *T26Node = NULL;
    GmcNodeT *T27Node = NULL;
    GmcNodeT *T28Node = NULL;
    GmcNodeT *T29Node = NULL;
    GmcNodeT *T30Node = NULL;
    GmcNodeT *T31Node = NULL;
    const char *vertexLabelT0 = "root";
    const char *vertexLabelT1 = "T1";
    const char *vertexLabelT2 = "T2";
    const char *vertexLabelT3 = "T3";
    const char *vertexLabelT4 = "T4";
    const char *vertexLabelT5 = "T5";
    const char *vertexLabelT6 = "T6";
    const char *vertexLabelT7 = "T7";
    const char *vertexLabelT8 = "T8";
    const char *vertexLabelT9 = "T9";
    const char *vertexLabelT10 = "T10";
    const char *vertexLabelT11 = "T11";
    const char *vertexLabelT12 = "T12";
    const char *vertexLabelT13 = "T13";
    const char *vertexLabelT14 = "T14";
    const char *vertexLabelT15 = "T15";
    const char *vertexLabelT16 = "T16";
    const char *vertexLabelT17 = "T17";
    const char *vertexLabelT18 = "T18";
    const char *vertexLabelT19 = "T19";
    const char *vertexLabelT20 = "T20";
    const char *vertexLabelT21 = "T21";
    const char *vertexLabelT22 = "T22";
    const char *vertexLabelT23 = "T23";
    const char *vertexLabelT24 = "T24";
    const char *vertexLabelT25 = "T25";
    const char *vertexLabelT26 = "T26";
    const char *vertexLabelT27 = "T27";
    const char *vertexLabelT28 = "T28";
    const char *vertexLabelT29 = "T29";
    const char *vertexLabelT30 = "T30";
    const char *vertexLabelT31 = "T31";

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 1;
    testYangSetVertexProperty_F0(T0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 对 container NP 子节点做replace操作
    ret = GmcYangEditChildNode(T0Node, vertexLabelT1, GMC_OPERATION_INSERT, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T1Node, vertexLabelT2, GMC_OPERATION_INSERT, &T2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T2Node, vertexLabelT3, GMC_OPERATION_INSERT, &T3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T3Node, vertexLabelT4, GMC_OPERATION_INSERT, &T4Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T4Node, vertexLabelT5, GMC_OPERATION_INSERT, &T5Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T5Node, vertexLabelT6, GMC_OPERATION_INSERT, &T6Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T6Node, vertexLabelT7, GMC_OPERATION_INSERT, &T7Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T7Node, vertexLabelT8, GMC_OPERATION_INSERT, &T8Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T8Node, vertexLabelT9, GMC_OPERATION_INSERT, &T9Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T9Node, vertexLabelT10, GMC_OPERATION_INSERT, &T10Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T10Node, vertexLabelT11, GMC_OPERATION_INSERT, &T11Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T11Node, vertexLabelT12, GMC_OPERATION_INSERT, &T12Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T12Node, vertexLabelT13, GMC_OPERATION_INSERT, &T13Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T13Node, vertexLabelT14, GMC_OPERATION_INSERT, &T14Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T14Node, vertexLabelT15, GMC_OPERATION_INSERT, &T15Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T15Node, vertexLabelT16, GMC_OPERATION_INSERT, &T16Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T16Node, vertexLabelT17, GMC_OPERATION_INSERT, &T17Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T17Node, vertexLabelT18, GMC_OPERATION_INSERT, &T18Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T18Node, vertexLabelT19, GMC_OPERATION_INSERT, &T19Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T19Node, vertexLabelT20, GMC_OPERATION_INSERT, &T20Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T20Node, vertexLabelT21, GMC_OPERATION_INSERT, &T21Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T21Node, vertexLabelT22, GMC_OPERATION_INSERT, &T22Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T22Node, vertexLabelT23, GMC_OPERATION_INSERT, &T23Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T23Node, vertexLabelT24, GMC_OPERATION_INSERT, &T24Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T24Node, vertexLabelT25, GMC_OPERATION_INSERT, &T25Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T25Node, vertexLabelT26, GMC_OPERATION_INSERT, &T26Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T26Node, vertexLabelT27, GMC_OPERATION_INSERT, &T27Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T27Node, vertexLabelT28, GMC_OPERATION_INSERT, &T28Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T28Node, vertexLabelT29, GMC_OPERATION_INSERT, &T29Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T29Node, vertexLabelT30, GMC_OPERATION_INSERT, &T30Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T30Node, vertexLabelT31, GMC_OPERATION_INSERT, &T31Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 create
    fieldValue = 1;
    testYangSetVertexProperty_F0(T31Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t f1fieldValue = 1;
    bool f4 = true;
    double f3 = 1;
    float f5 = 1;
    GmcPropValueT propValue;
    ret = testYangSetField(T31Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(T31Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memcpy(propValue.propertyName, "F3", (strlen("F3") + 1));
    propValue.type = GMC_DATATYPE_DOUBLE;
    propValue.value = &f3;
    propValue.size = sizeof(double);
    ret = GmcYangSetNodeProperty(T31Node, &propValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(T31Node, GMC_DATATYPE_FLOAT, &f5, sizeof(float), "F5", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 提交批处理
    BatchExecute(batch, 1, 1);

    // 提交事务
    TransCommit(conn);
}
void testYang32deepLeaflistAllDate(GmcConnT *conn)
{
    int ret ;
    // 创建乐观事务
    TransStart(conn);
    GmcBatchT *batch = NULL;
    // 设置批处理
    ret = testBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *T0Node = NULL;
    GmcNodeT *T1Node = NULL;
    GmcNodeT *T2Node = NULL;
    GmcNodeT *T3Node = NULL;
    GmcNodeT *T4Node = NULL;
    GmcNodeT *T5Node = NULL;
    GmcNodeT *T6Node = NULL;
    GmcNodeT *T7Node = NULL;
    GmcNodeT *T8Node = NULL;
    GmcNodeT *T9Node = NULL;
    GmcNodeT *T10Node = NULL;
    GmcNodeT *T11Node = NULL;
    GmcNodeT *T12Node = NULL;
    GmcNodeT *T13Node = NULL;
    GmcNodeT *T14Node = NULL;
    GmcNodeT *T15Node = NULL;
    GmcNodeT *T16Node = NULL;
    GmcNodeT *T17Node = NULL;
    GmcNodeT *T18Node = NULL;
    GmcNodeT *T19Node = NULL;
    GmcNodeT *T20Node = NULL;
    GmcNodeT *T21Node = NULL;
    GmcNodeT *T22Node = NULL;
    GmcNodeT *T23Node = NULL;
    GmcNodeT *T24Node = NULL;
    GmcNodeT *T25Node = NULL;
    GmcNodeT *T26Node = NULL;
    GmcNodeT *T27Node = NULL;
    GmcNodeT *T28Node = NULL;
    GmcNodeT *T29Node = NULL;
    GmcNodeT *T30Node = NULL;
    GmcNodeT *T31Node = NULL;
    GmcNodeT *listNode = NULL;
    const char *vertexLabelT0 = "root";
    const char *vertexLabelT1 = "T1";
    const char *vertexLabelT2 = "T2";
    const char *vertexLabelT3 = "T3";
    const char *vertexLabelT4 = "T4";
    const char *vertexLabelT5 = "T5";
    const char *vertexLabelT6 = "T6";
    const char *vertexLabelT7 = "T7";
    const char *vertexLabelT8 = "T8";
    const char *vertexLabelT9 = "T9";
    const char *vertexLabelT10 = "T10";
    const char *vertexLabelT11 = "T11";
    const char *vertexLabelT12 = "T12";
    const char *vertexLabelT13 = "T13";
    const char *vertexLabelT14 = "T14";
    const char *vertexLabelT15 = "T15";
    const char *vertexLabelT16 = "T16";
    const char *vertexLabelT17 = "T17";
    const char *vertexLabelT18 = "T18";
    const char *vertexLabelT19 = "T19";
    const char *vertexLabelT20 = "T20";
    const char *vertexLabelT21 = "T21";
    const char *vertexLabelT22 = "T22";
    const char *vertexLabelT23 = "T23";
    const char *vertexLabelT24 = "T24";
    const char *vertexLabelT25 = "T25";
    const char *vertexLabelT26 = "T26";
    const char *vertexLabelT27 = "T27";
    const char *vertexLabelT28 = "T28";
    const char *vertexLabelT29 = "T29";
    const char *vertexLabelT30 = "T30";
    const char *vertexLabelT31 = "T31";
    const char *vertexLabellist = "leafList";

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 1;
    testYangSetVertexProperty_F0(T0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 对 container NP 子节点做replace操作
    ret = GmcYangEditChildNode(T0Node, vertexLabelT1, GMC_OPERATION_INSERT, &T1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T1Node, vertexLabelT2, GMC_OPERATION_INSERT, &T2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T2Node, vertexLabelT3, GMC_OPERATION_INSERT, &T3Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T3Node, vertexLabelT4, GMC_OPERATION_INSERT, &T4Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T4Node, vertexLabelT5, GMC_OPERATION_INSERT, &T5Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T5Node, vertexLabelT6, GMC_OPERATION_INSERT, &T6Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T6Node, vertexLabelT7, GMC_OPERATION_INSERT, &T7Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T7Node, vertexLabelT8, GMC_OPERATION_INSERT, &T8Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T8Node, vertexLabelT9, GMC_OPERATION_INSERT, &T9Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T9Node, vertexLabelT10, GMC_OPERATION_INSERT, &T10Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T10Node, vertexLabelT11, GMC_OPERATION_INSERT, &T11Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T11Node, vertexLabelT12, GMC_OPERATION_INSERT, &T12Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T12Node, vertexLabelT13, GMC_OPERATION_INSERT, &T13Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T13Node, vertexLabelT14, GMC_OPERATION_INSERT, &T14Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T14Node, vertexLabelT15, GMC_OPERATION_INSERT, &T15Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T15Node, vertexLabelT16, GMC_OPERATION_INSERT, &T16Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T16Node, vertexLabelT17, GMC_OPERATION_INSERT, &T17Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T17Node, vertexLabelT18, GMC_OPERATION_INSERT, &T18Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T18Node, vertexLabelT19, GMC_OPERATION_INSERT, &T19Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T19Node, vertexLabelT20, GMC_OPERATION_INSERT, &T20Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T20Node, vertexLabelT21, GMC_OPERATION_INSERT, &T21Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T21Node, vertexLabelT22, GMC_OPERATION_INSERT, &T22Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T22Node, vertexLabelT23, GMC_OPERATION_INSERT, &T23Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T23Node, vertexLabelT24, GMC_OPERATION_INSERT, &T24Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T24Node, vertexLabelT25, GMC_OPERATION_INSERT, &T25Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T25Node, vertexLabelT26, GMC_OPERATION_INSERT, &T26Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T26Node, vertexLabelT27, GMC_OPERATION_INSERT, &T27Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T27Node, vertexLabelT28, GMC_OPERATION_INSERT, &T28Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T28Node, vertexLabelT29, GMC_OPERATION_INSERT, &T29Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T29Node, vertexLabelT30, GMC_OPERATION_INSERT, &T30Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T30Node, vertexLabelT31, GMC_OPERATION_INSERT, &T31Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // // 对子节点字段 做replace操作
    fieldValue = 1;
    testYangSetVertexProperty_F0(T30Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 对子节点字段 做merge操作
    int32_t f1fieldValue = 1;
    bool f4 = true;
    double f3 = 1;
    float f5 = 1;
    GmcPropValueT propValue;
    ret = testYangSetField(T31Node, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(T31Node, GMC_DATATYPE_BOOL, &f4, sizeof(bool), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memcpy(propValue.propertyName, "F3", (strlen("F3") + 1));
    propValue.type = GMC_DATATYPE_DOUBLE;
    propValue.value = &f3;
    propValue.size = sizeof(double);
    ret = GmcYangSetNodeProperty(T31Node, &propValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(T31Node, GMC_DATATYPE_FLOAT, &f5, sizeof(float), "F5", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 这里需要prepar list的labelname
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, vertexLabellist, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_T1List, &listNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    testYangSetVertexProperty_F0(listNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 提交批处理
    BatchExecute(batch, 2, 2);

    // 提交事务
    TransCommit(conn);
}
#endif
