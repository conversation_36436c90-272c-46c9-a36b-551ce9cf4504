/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#include "RCA_COMMON.h"
#include "../../../reliability/try.h"
#include "aliasTool.h"
#include "yangSubTool.h"

class yangSubExpFunc : public testing::Test {
public:
    SnUserDataT *newSubData;
    SnUserDataT *oldSubData;
    virtual void SetUp();
    virtual void TearDown();

    SnUserDataT *user_data;
    SnUserDataT *user_data2;
    SnUserDataT *user_data3;
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
    void clearSubUserData();
};

void yangSubExpFunc::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("mkdir -p /data/gmdb/; rm -rf /data/gmdb/*");
#ifndef FEATURE_CLT_SERVER_SAME_PROCESS
    system("sh $TEST_HOME/tools/start.sh -f");
#endif
    int ret = testEnvInit();
    ASSERT_EQ(GMERR_OK, ret);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void yangSubExpFunc::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

void yangSubExpFunc::SetUp()
{
    int ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    user_data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data, 0, sizeof(SnUserDataT));

    user_data->subscriptionName = (char *)malloc(sizeof(char) * 128);
    memset(user_data->subscriptionName, 0, sizeof(char) * 128);

    user_data->connectionName = (char *)malloc(sizeof(char) * 128);
    memset(user_data->connectionName, 0, sizeof(char) * 128);

    user_data2 = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data2, 0, sizeof(SnUserDataT));

    user_data2->subscriptionName = (char *)malloc(sizeof(char) * 128);
    memset(user_data2->subscriptionName, 0, sizeof(char) * 128);

    user_data2->connectionName = (char *)malloc(sizeof(char) * 128);
    memset(user_data2->connectionName, 0, sizeof(char) * 128);

    user_data3 = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data3, 0, sizeof(SnUserDataT));

    user_data3->subscriptionName = (char *)malloc(sizeof(char) * 128);
    memset(user_data3->subscriptionName, 0, sizeof(char) * 128);

    user_data3->connectionName = (char *)malloc(sizeof(char) * 128);
    memset(user_data3->connectionName, 0, sizeof(char) * 128);

    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    const char *namespace1 = "yangSubExpFunc";
    const char *namespaceUserName = "abc";

    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观
    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // alloc all stmt
    TestYangAllocAllstmt();

    // 创建同步连接
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(g_stmtSync, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建订阅连接
    int chanRingLen = 256;
    const char *newSubConnName = "yangSubConn";
    ret = testSubConnect(&g_connSub, &g_stmtSub, 1, g_epoll_reg_info, newSubConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(user_data->connectionName, 128, "%s", newSubConnName);
    (void)snprintf(user_data2->connectionName, 128, "%s", newSubConnName);
    (void)snprintf(user_data3->connectionName, 128, "%s", newSubConnName);

    // 创建同步连接
    ret = testGmcConnect(&g_connSync2, &g_stmtSync2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
}

void yangSubExpFunc::clearSubUserData()
{
    user_data->diffNum = 0;
    user_data->isFetchNull = false;
    user_data2->diffNum = 0;
    user_data2->isFetchNull = false;
    user_data2->isFetchExpNull = false;
    user_data3->diffNum = 0;
    user_data3->isFetchNull = false;
    user_data3->isFetchExpNull = false;
}

void yangSubExpFunc::TearDown()
{
    const char *namespace1 = "yangSubExpFunc";
    TryDropNameSpace(g_stmt_async, namespace1);

    // 释放all stmt
    TestYangFreeAllstmt();

    int ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubDisConnect(g_connSub, g_stmtSub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    if (user_data->subscriptionName) {
        free(user_data->subscriptionName);
        user_data->subscriptionName = NULL;
    }
    if (user_data->connectionName) {
        free(user_data->connectionName);
        user_data->connectionName = NULL;
    }
    if (user_data) {
        free(user_data);
        user_data = NULL;
    }

    if (user_data2->subscriptionName) {
        free(user_data2->subscriptionName);
        user_data2->subscriptionName = NULL;
    }
    if (user_data2->connectionName) {
        free(user_data2->connectionName);
        user_data2->connectionName = NULL;
    }
    if (user_data2) {
        free(user_data2);
        user_data2 = NULL;
    }

    if (user_data3->subscriptionName) {
        free(user_data3->subscriptionName);
        user_data3->subscriptionName = NULL;
    }
    if (user_data3->connectionName) {
        free(user_data3->connectionName);
        user_data3->connectionName = NULL;
    }
    if (user_data3) {
        free(user_data3);
        user_data3 = NULL;
    }
}


void TestCheckValidateModelAsync(GmcStmtT *stmt)
{
    // 模型校验
    YangValidateUserDataT checkData = {0};
    int ret = GmcYangValidateModelAsync(stmt, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(true, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(0, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));
}

// *typedef void (*GmcYangValidateDoneT)(void *userData, GmcValidateResT validateRes, int32_t status, const char *errMsg);*/
void AsyncValidateLeafRefCb(void *userData, GmcValidateResT validateRes, int32_t status, const char *errMsg)
{
    if (userData) {
        YangValidateUserDataT *uData = (YangValidateUserDataT *)userData;
        uData->status = status;
        if ((status != GMERR_OK) && (errMsg != NULL)) {
            printf("YangValidate errMsg: %s\n", errMsg);
        }
        uData->validateRes = validateRes.validateRes;
        uData->failCount = validateRes.failCount;

        printf(">>> validateRes: %d\n", validateRes.validateRes);
        printf(">>> failCount: %u\n", validateRes.failCount);

        if (uData->isValidErrorPathInfo) {
            GmcErrorPathInfoT msg;
            ASSERT_EQ(GMERR_OK, GmcYangGetErrorPathInfo(&msg));

            // 结果检查
            printf("--- errcode: %d\n", msg.errorCode);
            printf("--- errorClauseIndex: %u\n", msg.errorClauseIndex);
            printf("--- errorMsg: %s\n", msg.errorMsg);
            printf("--- errorPath: %s\n", msg.errorPath);
            EXPECT_EQ(uData->expectedErrCode, msg.errorCode);
            EXPECT_EQ(uData->expectedErrClauseIndex, msg.errorClauseIndex);
            EXPECT_STREQ(uData->expectedErrMsg, msg.errorMsg);
            EXPECT_STREQ(uData->expectedErrPath, msg.errorPath);
            ASSERT_NO_FATAL_FAILURE(GmcYangFreeErrorPathInfo());
        }

        uData->recvNum++;
    }
}


/*****************************************************************************
 * Description  : 001.Yang表订阅，订阅xpath路径包含NP节点，NP节点包含默认值字段和不带默认值字段，编辑NP节点父节点，不编辑NP节点，订阅类型分别设置report_all、explicit、同时设置
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubExpFunc, Yang_112_yangSubExpFunc_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "events":[
            {"type": "diff"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson, user_data, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *newSubJson2 = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "events":[
            {"type": "diff_explicit"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson2, user_data2, "yangSub2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *newSubJson3 = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "events":[
            {"type": "diff"},
            {"type": "diff_explicit"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson3, user_data3, "yangSub3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    char strEmpty[1] = "";
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_EMPTY, &strEmpty, 0, "F1",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F2",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff001_subAll.json", NULL, user_data);
    SetDiffFileExp("diffFunc/diff001_subExp.json", NULL, user_data2);

    SetDiffFile2("diffFunc/diff001_subAll.json", NULL, user_data3);
    SetDiffFileExp2("diffFunc/diff001_subExp.json", NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();
 
    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, "yangSub2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, "yangSub3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ClearDiffFile();
    ClearDiffFileExp();
    ClearDiffFile2();
    ClearDiffFileExp2();
    system("gmsysview subtree -ns yangSubExpFunc -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 002.Yang表订阅，订阅xpath路径包含NP节点，NP节点包含默认值字段和不带默认值字段，编辑NP节点不编辑字段，订阅类型分别设置report_all、explicit、同时设置
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubExpFunc, Yang_112_yangSubExpFunc_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "events":[
            {"type": "diff"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson, user_data, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *newSubJson2 = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "events":[
            {"type": "diff_explicit"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson2, user_data2, "yangSub2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *newSubJson3 = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "events":[
            {"type": "diff"},
            {"type": "diff_explicit"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson3, user_data3, "yangSub3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    char strEmpty[1] = "";
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_EMPTY, &strEmpty, 0, "F1",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F2",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container NP 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff001_subAll.json", NULL, user_data);
    SetDiffFileExp("diffFunc/diff001_subExp.json", NULL, user_data2);

    SetDiffFile2("diffFunc/diff001_subAll.json", NULL, user_data3);
    SetDiffFileExp2("diffFunc/diff001_subExp.json", NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();
 
    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, "yangSub2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, "yangSub3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ClearDiffFile();
    ClearDiffFileExp();
    ClearDiffFile2();
    ClearDiffFileExp2();
    system("gmsysview subtree -ns yangSubExpFunc -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 003.Yang表订阅，订阅xpath路径包含NP节点，NP节点包含默认值字段和不带默认值字段，NP节点编辑字段为默认值，订阅类型分别设置report_all、explicit、同时设置
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubExpFunc, Yang_112_yangSubExpFunc_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "events":[
            {"type": "diff"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson, user_data, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *newSubJson2 = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "events":[
            {"type": "diff_explicit"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson2, user_data2, "yangSub2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *newSubJson3 = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "events":[
            {"type": "diff"},
            {"type": "diff_explicit"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson3, user_data3, "yangSub3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    char strEmpty[1] = "";
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_EMPTY, &strEmpty, 0, "F1",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F2",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container NP 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    fieldValue = 123;
    testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F0");
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_EMPTY, &strEmpty, 0, "F1",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool boolValue = true;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_BOOL, &boolValue, sizeof(bool), "F4",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 888;
    testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F8");
    char valueStr[10] = "default11";
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_STRING, &valueStr, (strlen(valueStr)), "F11",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff003_subAll.json", NULL, user_data);
    SetDiffFileExp("diffFunc/diff003_subExp.json", NULL, user_data2);

    SetDiffFile2("diffFunc/diff003_subAll.json", NULL, user_data3);
    SetDiffFileExp2("diffFunc/diff003_subExp.json", NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();
 
    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, "yangSub2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, "yangSub3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ClearDiffFile();
    ClearDiffFileExp();
    ClearDiffFile2();
    ClearDiffFileExp2();
    system("gmsysview subtree -ns yangSubExpFunc -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 004.Yang表订阅，订阅xpath路径包含NP节点，NP节点包含默认值字段和不带默认值字段，NP节点编辑字段值不等于默认值，订阅类型分别设置report_all、explicit、同时设置
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubExpFunc, Yang_112_yangSubExpFunc_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "events":[
            {"type": "diff"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson, user_data, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *newSubJson2 = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "events":[
            {"type": "diff_explicit"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson2, user_data2, "yangSub2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *newSubJson3 = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "events":[
            {"type": "diff"},
            {"type": "diff_explicit"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson3, user_data3, "yangSub3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    char strEmpty[1] = "";
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_EMPTY, &strEmpty, 0, "F1",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F2",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container NP 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    fieldValue = 123;
    testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F0");
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_EMPTY, &strEmpty, 0, "F1",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool boolValue = false;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_BOOL, &boolValue, sizeof(bool), "F4",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 234;
    testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F8");
    char valueStr[10] = "str123";
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_STRING, &valueStr, (strlen(valueStr)), "F11",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff004_subAll.json", NULL, user_data);
    SetDiffFileExp("diffFunc/diff004_subExp.json", NULL, user_data2);

    SetDiffFile2("diffFunc/diff004_subAll.json", NULL, user_data3);
    SetDiffFileExp2("diffFunc/diff004_subExp.json", NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();
 
    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, "yangSub2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, "yangSub3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ClearDiffFile();
    ClearDiffFileExp();
    ClearDiffFile2();
    ClearDiffFileExp2();
    system("gmsysview subtree -ns yangSubExpFunc -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 005.Yang表订阅，订阅xpath路径包含NP节点，NP节点包含默认值字段和不带默认值字段，预置NP节点编辑字段值等于默认值，订阅类型分别设置report_all、explicit、同时设置
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubExpFunc, Yang_112_yangSubExpFunc_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "events":[
            {"type": "diff"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson, user_data, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *newSubJson2 = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "events":[
            {"type": "diff_explicit"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson2, user_data2, "yangSub2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *newSubJson3 = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "events":[
            {"type": "diff"},
            {"type": "diff_explicit"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson3, user_data3, "yangSub3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    char strEmpty[1] = "";
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_EMPTY, &strEmpty, 0, "F1",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F2",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container NP 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    fieldValue = 123;
    testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F0");
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_EMPTY, &strEmpty, 0, "F1",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool boolValue = false;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_BOOL, &boolValue, sizeof(bool), "F4",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 888;
    testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F8");
    char valueStr[10] = "default11";
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_STRING, &valueStr, (strlen(valueStr)), "F11",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff005_subAll.json", NULL, user_data);
    SetDiffFileExp("diffFunc/diff005_subExp.json", NULL, user_data2);

    SetDiffFile2("diffFunc/diff005_subAll.json", NULL, user_data3);
    SetDiffFileExp2("diffFunc/diff005_subExp.json", NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();
 

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 2");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container NP 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_NONE, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    fieldValue = 123;
    testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F0");
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_EMPTY, &strEmpty, 0, "F1",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    boolValue = true;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_BOOL, &boolValue, sizeof(bool), "F4",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 888;
    testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F8");
    char valueStr2[10] = "default11";
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_STRING, &valueStr2, (strlen(valueStr2)), "F11",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff005_subAll2.json", NULL, user_data);
    SetDiffFileExp("diffFunc/diff005_subExp2.json", NULL, user_data2);

    SetDiffFile2("diffFunc/diff005_subAll2.json", NULL, user_data3);
    SetDiffFileExp2("diffFunc/diff005_subExp2.json", NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();
 
    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 3");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container NP 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_NONE, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    fieldValue = 234;
    testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F0");
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_EMPTY, &strEmpty, 0, "F1",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    boolValue = false;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_BOOL, &boolValue, sizeof(bool), "F4",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 456;
    testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F8");
    char valueStr3[10] = "str456";
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_STRING, &valueStr3, (strlen(valueStr3)), "F11",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff005_subAll3.json", NULL, user_data);
    SetDiffFileExp("diffFunc/diff005_subExp3.json", NULL, user_data2);

    SetDiffFile2("diffFunc/diff005_subAll3.json", NULL, user_data3);
    SetDiffFileExp2("diffFunc/diff005_subExp3.json", NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();
 
    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, "yangSub2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, "yangSub3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ClearDiffFile();
    ClearDiffFileExp();
    ClearDiffFile2();
    ClearDiffFileExp2();
    system("gmsysview subtree -ns yangSubExpFunc -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 006.Yang表订阅，订阅xpath路径包含NP节点，NP节点包含默认值字段和不带默认值字段，预置NP节点编辑字段值不等于默认值，订阅类型分别设置report_all、explicit、同时设置
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubExpFunc, Yang_112_yangSubExpFunc_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "events":[
            {"type": "diff"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson, user_data, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *newSubJson2 = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "events":[
            {"type": "diff_explicit"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson2, user_data2, "yangSub2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *newSubJson3 = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "events":[
            {"type": "diff"},
            {"type": "diff_explicit"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson3, user_data3, "yangSub3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    char strEmpty[1] = "";
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_EMPTY, &strEmpty, 0, "F1",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F2",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container NP 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    fieldValue = 123;
    testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F0");
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_EMPTY, &strEmpty, 0, "F1",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool boolValue = false;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_BOOL, &boolValue, sizeof(bool), "F4",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 234;
    testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F8");
    char valueStr[10] = "str234";
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_STRING, &valueStr, (strlen(valueStr)), "F11",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff006_subAll.json", NULL, user_data);
    SetDiffFileExp("diffFunc/diff006_subExp.json", NULL, user_data2);

    SetDiffFile2("diffFunc/diff006_subAll.json", NULL, user_data3);
    SetDiffFileExp2("diffFunc/diff006_subExp.json", NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 2");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container NP 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_NONE, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    fieldValue = 123;
    testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F0");
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_EMPTY, &strEmpty, 0, "F1",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    boolValue = true;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_BOOL, &boolValue, sizeof(bool), "F4",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 888;
    testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F8");
    char valueStr2[10] = "default11";
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_STRING, &valueStr2, (strlen(valueStr2)), "F11",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff006_subAll2.json", NULL, user_data);
    SetDiffFileExp("diffFunc/diff006_subExp2.json", NULL, user_data2);

    SetDiffFile2("diffFunc/diff006_subAll2.json", NULL, user_data3);
    SetDiffFileExp2("diffFunc/diff006_subExp2.json", NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();
 
    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 3");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container NP 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_NONE, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    fieldValue = 234;
    testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F0");
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_EMPTY, &strEmpty, 0, "F1",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    boolValue = false;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_BOOL, &boolValue, sizeof(bool), "F4",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 456;
    testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F8");
    char valueStr3[10] = "str456";
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_STRING, &valueStr3, (strlen(valueStr3)), "F11",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff006_subAll3.json", NULL, user_data);
    SetDiffFileExp("diffFunc/diff006_subExp3.json", NULL, user_data2);

    SetDiffFile2("diffFunc/diff006_subAll3.json", NULL, user_data3);
    SetDiffFileExp2("diffFunc/diff006_subExp3.json", NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();
 
    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, "yangSub2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, "yangSub3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ClearDiffFile();
    ClearDiffFileExp();
    ClearDiffFile2();
    ClearDiffFileExp2();
    system("gmsysview subtree -ns yangSubExpFunc -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}



/*****************************************************************************
 * Description  : 007.Yang表订阅，订阅xpath路径包含P节点，P节点包含默认值字段和不带默认值字段，不编辑P点数据，订阅类型分别设置report_all、explicit、同时设置
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubExpFunc, Yang_112_yangSubExpFunc_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel2.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "events":[
            {"type": "diff"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson, user_data, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *newSubJson2 = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "events":[
            {"type": "diff_explicit"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson2, user_data2, "yangSub2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *newSubJson3 = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "events":[
            {"type": "diff"},
            {"type": "diff_explicit"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson3, user_data3, "yangSub3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 1");
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    char strEmpty[1] = "";
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_EMPTY, &strEmpty, 0, "F1",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F2",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff007_subAll.json", NULL, user_data);
    SetDiffFileExp("diffFunc/diff007_subExp.json", NULL, user_data2);

    SetDiffFile2("diffFunc/diff007_subAll.json", NULL, user_data3);
    SetDiffFileExp2("diffFunc/diff007_subExp.json", NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 2");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container NP 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffFunc/diff007_2.json", NULL, user_data);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffReply, data);
    ReleaseDiffFile();

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff007_subAll2.json", NULL, user_data);
    SetDiffFileExp("diffFunc/diff007_subExp2.json", NULL, user_data2);

    SetDiffFile2("diffFunc/diff007_subAll2.json", NULL, user_data3);
    SetDiffFileExp2("diffFunc/diff007_subExp2.json", NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();
 
    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, "yangSub2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, "yangSub3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ClearDiffFile();
    ClearDiffFileExp();
    ClearDiffFile2();
    ClearDiffFileExp2();
    system("gmsysview subtree -ns yangSubExpFunc -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 008.Yang表订阅，订阅xpath路径包含P节点，P节点包含默认值字段和不带默认值字段，编辑P点数据，订阅类型分别设置report_all、explicit、同时设置
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubExpFunc, Yang_112_yangSubExpFunc_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel2.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "ContainerTwo",
                "states": [{"type": "create"},{"type": "update"},{"type": "remove"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson, user_data, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *newSubJson2 = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "ContainerTwo",
                "states": [{"type": "create"},{"type": "update"},{"type": "remove"}]
            }
        ],
        "events":[
            {"type": "diff_explicit"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson2, user_data2, "yangSub2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *newSubJson3 = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "ContainerTwo",
                "states": [{"type": "create"},{"type": "update"},{"type": "remove"}]
            }
        ],
        "events":[
            {"type": "diff"},
            {"type": "diff_explicit"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson3, user_data3, "yangSub3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    char strEmpty[1] = "";
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_EMPTY, &strEmpty, 0, "F1",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F2",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container NP 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    fieldValue = 123;
    testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F0");
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_EMPTY, &strEmpty, 0, "F1",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool boolValue = false;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_BOOL, &boolValue, sizeof(bool), "F4",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 888;
    testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F8");
    char valueStr[10] = "default11";
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_STRING, &valueStr, (strlen(valueStr)), "F11",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff008_subAll.json", NULL, user_data);
    SetDiffFileExp("diffFunc/diff008_subExp.json", NULL, user_data2);

    SetDiffFile2("diffFunc/diff008_subAll.json", NULL, user_data3);
    SetDiffFileExp2("diffFunc/diff008_subExp.json", NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 2");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 container NP 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_NONE, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    boolValue = true;
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_BOOL, &boolValue, sizeof(bool), "F4",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 123;
    testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F8");
    char valueStr2[10] = "str234";
    ret = testYangSetField(g_containerT2Node, GMC_DATATYPE_STRING, &valueStr2, (strlen(valueStr2)), "F11",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff008_subAll2.json", NULL, user_data);
    SetDiffFileExp("diffFunc/diff008_subExp2.json", NULL, user_data2);

    SetDiffFile2("diffFunc/diff008_subAll2.json", NULL, user_data3);
    SetDiffFileExp2("diffFunc/diff008_subExp2.json", NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();
 
    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, "yangSub2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, "yangSub3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ClearDiffFile();
    ClearDiffFileExp();
    ClearDiffFile2();
    ClearDiffFileExp2();
    system("gmsysview subtree -ns yangSubExpFunc -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 009.Yang表订阅，订阅xpath路径包含默认choice-case，默认case包含默认值字段和不带默认值字段，不编辑case，订阅类型分别设置report_all、explicit、同时设置
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubExpFunc, Yang_112_yangSubExpFunc_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel2.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "Choice",
                "states": [{"type": "create"},{"type": "update"},{"type": "remove"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson, user_data, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *newSubJson2 = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "Choice",
                "states": [{"type": "create"},{"type": "update"},{"type": "remove"}]
            }
        ],
        "events":[
            {"type": "diff_explicit"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson2, user_data2, "yangSub2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *newSubJson3 = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "Choice",
                "states": [{"type": "create"},{"type": "update"},{"type": "remove"}]
            }
        ],
        "events":[
            {"type": "diff"},
            {"type": "diff_explicit"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson3, user_data3, "yangSub3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    char strEmpty[1] = "";
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_EMPTY, &strEmpty, 0, "F1",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F2",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff009_subAll.json", NULL, user_data);
    SetDiffFileExp(NULL, NULL, user_data2);

    SetDiffFile2("diffFunc/diff009_subAll.json", NULL, user_data3);
    SetDiffFileExp2(NULL, NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 2");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile(NULL, NULL, user_data);
    SetDiffFileExp(NULL, NULL, user_data2);

    SetDiffFile2(NULL, NULL, user_data3);
    SetDiffFileExp2(NULL, NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 3");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile(NULL, NULL, user_data);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffReply, data);
    ReleaseDiffFile();

    // 设置订阅校验信息
    SetDiffFile(NULL, NULL, user_data);
    SetDiffFileExp(NULL, NULL, user_data2);

    SetDiffFile2(NULL, NULL, user_data3);
    SetDiffFileExp2(NULL, NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 4");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, "CaseTwo", GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_choiceCaseNode, GMC_DATATYPE_EMPTY, &strEmpty, 0, "F1",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff009_subAll4.json", NULL, user_data);
    SetDiffFileExp("diffFunc/diff009_subExp4.json", NULL, user_data2);

    SetDiffFile2("diffFunc/diff009_subAll4.json", NULL, user_data3);
    SetDiffFileExp2("diffFunc/diff009_subExp4.json", NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();
 
    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, "yangSub2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, "yangSub3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ClearDiffFile();
    ClearDiffFileExp();
    ClearDiffFile2();
    ClearDiffFileExp2();
    system("gmsysview subtree -ns yangSubExpFunc -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 010.Yang表订阅，订阅xpath路径包含默认choice-case，默认case包含默认值字段和不带默认值字段，编辑默认case，订阅类型分别设置report_all、explicit、同时设置
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubExpFunc, Yang_112_yangSubExpFunc_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel2.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "Choice",
                "states": [{"type": "create"},{"type": "update"},{"type": "remove"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson, user_data, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *newSubJson2 = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "Choice",
                "states": [{"type": "create"},{"type": "update"},{"type": "remove"}]
            }
        ],
        "events":[
            {"type": "diff_explicit"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson2, user_data2, "yangSub2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *newSubJson3 = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "Choice",
                "states": [{"type": "create"},{"type": "update"},{"type": "remove"}]
            }
        ],
        "events":[
            {"type": "diff"},
            {"type": "diff_explicit"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson3, user_data3, "yangSub3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    char strEmpty[1] = "";
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_EMPTY, &strEmpty, 0, "F1",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F2",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    fieldValue = 123;
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F0");
    ret = testYangSetField(g_choiceCaseNode, GMC_DATATYPE_EMPTY, &strEmpty, 0, "F1",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool boolValue = false;
    ret = testYangSetField(g_choiceCaseNode, GMC_DATATYPE_BOOL, &boolValue, sizeof(bool), "F4",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 888;
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F8");
    char valueStr[10] = "default11";
    ret = testYangSetField(g_choiceCaseNode, GMC_DATATYPE_STRING, &valueStr, (strlen(valueStr)), "F11",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff010_subAll.json", NULL, user_data);
    SetDiffFileExp("diffFunc/diff010_subExp.json", NULL, user_data2);

    SetDiffFile2("diffFunc/diff010_subAll.json", NULL, user_data3);
    SetDiffFileExp2("diffFunc/diff010_subExp.json", NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 2");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    fieldValue = 123;
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F0");
    boolValue = true;
    ret = testYangSetField(g_choiceCaseNode, GMC_DATATYPE_BOOL, &boolValue, sizeof(bool), "F4",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 567;
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F8");
    char valueStr2[10] = "str567";
    ret = testYangSetField(g_choiceCaseNode, GMC_DATATYPE_STRING, &valueStr2, (strlen(valueStr2)), "F11",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff010_subAll2.json", NULL, user_data);
    SetDiffFileExp("diffFunc/diff010_subExp2.json", NULL, user_data2);

    SetDiffFile2("diffFunc/diff010_subAll2.json", NULL, user_data3);
    SetDiffFileExp2("diffFunc/diff010_subExp2.json", NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 3");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    fieldValue = 567;
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F0");
    boolValue = false;
    ret = testYangSetField(g_choiceCaseNode, GMC_DATATYPE_BOOL, &boolValue, sizeof(bool), "F4",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 888;
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F8");
    char valueStr3[10] = "default11";
    ret = testYangSetField(g_choiceCaseNode, GMC_DATATYPE_STRING, &valueStr3, (strlen(valueStr3)), "F11",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff010_subAll3.json", NULL, user_data);
    SetDiffFileExp("diffFunc/diff010_subExp3.json", NULL, user_data2);

    SetDiffFile2("diffFunc/diff010_subAll3.json", NULL, user_data3);
    SetDiffFileExp2("diffFunc/diff010_subExp3.json", NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 4");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, "CaseTwo", GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_choiceCaseNode, GMC_DATATYPE_EMPTY, &strEmpty, 0, "F1",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff010_subAll4.json", NULL, user_data);
    SetDiffFileExp("diffFunc/diff010_subExp4.json", NULL, user_data2);

    SetDiffFile2("diffFunc/diff010_subAll4.json", NULL, user_data3);
    SetDiffFileExp2("diffFunc/diff010_subExp4.json", NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();
 
    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, "yangSub2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, "yangSub3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ClearDiffFile();
    ClearDiffFileExp();
    ClearDiffFile2();
    ClearDiffFileExp2();
    system("gmsysview subtree -ns yangSubExpFunc -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 011.Yang表订阅，订阅xpath路径包含默认choice-case，默认case包含默认值字段和不带默认值字段，编辑非默认case，订阅类型分别设置report_all、explicit、同时设置
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubExpFunc, Yang_112_yangSubExpFunc_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel2.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "Choice",
                "states": [{"type": "create"},{"type": "update"},{"type": "remove"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson, user_data, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *newSubJson2 = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "Choice",
                "states": [{"type": "create"},{"type": "update"},{"type": "remove"}]
            }
        ],
        "events":[
            {"type": "diff_explicit"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson2, user_data2, "yangSub2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *newSubJson3 = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "Choice",
                "states": [{"type": "create"},{"type": "update"},{"type": "remove"}]
            }
        ],
        "events":[
            {"type": "diff"},
            {"type": "diff_explicit"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson3, user_data3, "yangSub3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    char strEmpty[1] = "";
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_EMPTY, &strEmpty, 0, "F1",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F2",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, "CaseTwo", GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    fieldValue = 123;
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F0");
    ret = testYangSetField(g_choiceCaseNode, GMC_DATATYPE_EMPTY, &strEmpty, 0, "F1",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool boolValue = false;
    ret = testYangSetField(g_choiceCaseNode, GMC_DATATYPE_BOOL, &boolValue, sizeof(bool), "F4",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 888;
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F8");
    char valueStr[10] = "default11";
    ret = testYangSetField(g_choiceCaseNode, GMC_DATATYPE_STRING, &valueStr, (strlen(valueStr)), "F11",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff011_subAll.json", NULL, user_data);
    SetDiffFileExp("diffFunc/diff011_subExp.json", NULL, user_data2);

    SetDiffFile2("diffFunc/diff011_subAll.json", NULL, user_data3);
    SetDiffFileExp2("diffFunc/diff011_subExp.json", NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 2");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, "CaseTwo", GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    fieldValue = 123;
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F0");
    boolValue = true;
    ret = testYangSetField(g_choiceCaseNode, GMC_DATATYPE_BOOL, &boolValue, sizeof(bool), "F4",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 567;
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F8");
    char valueStr2[10] = "str567";
    ret = testYangSetField(g_choiceCaseNode, GMC_DATATYPE_STRING, &valueStr2, (strlen(valueStr2)), "F11",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff011_subAll2.json", NULL, user_data);
    SetDiffFileExp("diffFunc/diff011_subExp2.json", NULL, user_data2);

    SetDiffFile2("diffFunc/diff011_subAll2.json", NULL, user_data3);
    SetDiffFileExp2("diffFunc/diff011_subExp2.json", NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 3");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, "CaseTwo", GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    fieldValue = 567;
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F0");
    boolValue = false;
    ret = testYangSetField(g_choiceCaseNode, GMC_DATATYPE_BOOL, &boolValue, sizeof(bool), "F4",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 888;
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F8");
    char valueStr3[10] = "default11";
    ret = testYangSetField(g_choiceCaseNode, GMC_DATATYPE_STRING, &valueStr3, (strlen(valueStr3)), "F11",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff011_subAll3.json", NULL, user_data);
    SetDiffFileExp("diffFunc/diff011_subExp3.json", NULL, user_data2);

    SetDiffFile2("diffFunc/diff011_subAll3.json", NULL, user_data3);
    SetDiffFileExp2("diffFunc/diff011_subExp3.json", NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 4");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, "CaseOne", GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_choiceCaseNode, GMC_DATATYPE_EMPTY, &strEmpty, 0, "F1",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff011_subAll4.json", NULL, user_data);
    SetDiffFileExp("diffFunc/diff011_subExp4.json", NULL, user_data2);

    SetDiffFile2("diffFunc/diff011_subAll4.json", NULL, user_data3);
    SetDiffFileExp2("diffFunc/diff011_subExp4.json", NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();
 
    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, "yangSub2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, "yangSub3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ClearDiffFile();
    ClearDiffFileExp();
    ClearDiffFile2();
    ClearDiffFileExp2();
    system("gmsysview subtree -ns yangSubExpFunc -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 012.Yang表订阅，订阅xpath路径包含leaf-list，leaf-list含默认值字段，编辑record为默认值，订阅类型分别设置report_all、explicit、同时设置
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubExpFunc, Yang_112_yangSubExpFunc_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel3.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "LeafList",
                "states": [{"type": "create"},{"type": "update"},{"type": "remove"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson, user_data, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *newSubJson2 = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "LeafList",
                "states": [{"type": "create"},{"type": "update"},{"type": "remove"}]
            }
        ],
        "events":[
            {"type": "diff_explicit"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson2, user_data2, "yangSub2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *newSubJson3 = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "LeafList",
                "states": [{"type": "create"},{"type": "update"},{"type": "remove"}]
            }
        ],
        "events":[
            {"type": "diff"},
            {"type": "diff_explicit"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson3, user_data3, "yangSub3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    char strEmpty[1] = "";
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_EMPTY, &strEmpty, 0, "F1",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F2",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 102; i++) {
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_LeafList, g_vertexLabelT2, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_LeafList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_LeafList, &g_vertexLabelT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F0");
        ret = GmcBatchAddDML(batch, g_stmt_sync_LeafList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff012_subAll.json", NULL, user_data);
    SetDiffFileExp("diffFunc/diff012_subExp.json", NULL, user_data2);

    SetDiffFile2("diffFunc/diff012_subAll.json", NULL, user_data3);
    SetDiffFileExp2("diffFunc/diff012_subExp.json", NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 2");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 这里需要prepar list的labelname
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_LeafList, g_vertexLabelT2, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_LeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_LeafList, &g_vertexLabelT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    fieldValue = 101;
    GmcYangListLocatorT listProp;
    GmcPropValueT refKey;
    InitRefKeys(&refKey, 1, &fieldValue);
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_BEFORE, &refKey);
    ret = GmcYangSetListLocator(g_stmt_sync_LeafList, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 对子节点字段 做replace操作
    fieldValue = 105;
    testYangSetVertexProperty_Fx(g_vertexLabelT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F0");
    ret = GmcBatchAddDML(batch, g_stmt_sync_LeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);
 
    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff012_subAll2.json", NULL, user_data);
    SetDiffFileExp("diffFunc/diff012_subExp2.json", NULL, user_data2);

    SetDiffFile2("diffFunc/diff012_subAll2.json", NULL, user_data3);
    SetDiffFileExp2("diffFunc/diff012_subExp2.json", NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();

    system("gmsysview subtree -ns yangSubExpFunc -rn ContainerOne -defaultMode REPORT_ALL");

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 3");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff012_subAll3.json", NULL, user_data);
    SetDiffFileExp("diffFunc/diff012_subExp3.json", NULL, user_data2);

    SetDiffFile2("diffFunc/diff012_subAll3.json", NULL, user_data3);
    SetDiffFileExp2("diffFunc/diff012_subExp3.json", NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();
 
    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, "yangSub2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, "yangSub3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ClearDiffFile();
    ClearDiffFileExp();
    ClearDiffFile2();
    ClearDiffFileExp2();
    system("gmsysview subtree -ns yangSubExpFunc -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 013.Yang表订阅，订阅xpath路径包含leaf-list，leaf-list含默认值字段，编辑record为非默认值，订阅类型分别设置report_all、explicit、同时设置
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubExpFunc, Yang_112_yangSubExpFunc_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel3.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "LeafList",
                "states": [{"type": "create"},{"type": "update"},{"type": "remove"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson, user_data, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *newSubJson2 = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "LeafList",
                "states": [{"type": "create"},{"type": "update"},{"type": "remove"}]
            }
        ],
        "events":[
            {"type": "diff_explicit"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson2, user_data2, "yangSub2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *newSubJson3 = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "LeafList",
                "states": [{"type": "create"},{"type": "update"},{"type": "remove"}]
            }
        ],
        "events":[
            {"type": "diff"},
            {"type": "diff_explicit"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson3, user_data3, "yangSub3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    char strEmpty[1] = "";
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_EMPTY, &strEmpty, 0, "F1",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F2",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 200; i < 202; i++) {
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_LeafList, g_vertexLabelT2, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_LeafList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_LeafList, &g_vertexLabelT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F0");
        ret = GmcBatchAddDML(batch, g_stmt_sync_LeafList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff013_subAll.json", NULL, user_data);
    SetDiffFileExp("diffFunc/diff013_subExp.json", NULL, user_data2);

    SetDiffFile2("diffFunc/diff013_subAll.json", NULL, user_data3);
    SetDiffFileExp2("diffFunc/diff013_subExp.json", NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 2");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 这里需要prepar list的labelname
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_LeafList, g_vertexLabelT2, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_LeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_LeafList, &g_vertexLabelT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置插入位置
    fieldValue = 102;
    GmcYangListLocatorT listProp;
    GmcPropValueT refKey;
    InitRefKeys(&refKey, 1, &fieldValue);
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_BEFORE, &refKey);
    ret = GmcYangSetListLocator(g_stmt_sync_LeafList, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    // 对子节点字段 做replace操作
    fieldValue = 202;
    testYangSetVertexProperty_Fx(g_vertexLabelT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F0");
    ret = GmcBatchAddDML(batch, g_stmt_sync_LeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);
 
    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff013_subAll2.json", NULL, user_data);
    SetDiffFileExp("diffFunc/diff013_subExp2.json", NULL, user_data2);

    SetDiffFile2("diffFunc/diff013_subAll2.json", NULL, user_data3);
    SetDiffFileExp2("diffFunc/diff013_subExp2.json", NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();

    system("gmsysview subtree -ns yangSubExpFunc -rn ContainerOne -defaultMode REPORT_ALL");

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 3");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff013_subAll3.json", NULL, user_data);
    SetDiffFileExp("diffFunc/diff013_subExp3.json", NULL, user_data2);

    SetDiffFile2("diffFunc/diff013_subAll3.json", NULL, user_data3);
    SetDiffFileExp2("diffFunc/diff013_subExp3.json", NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();
 
    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, "yangSub2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, "yangSub3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ClearDiffFile();
    ClearDiffFileExp();
    ClearDiffFile2();
    ClearDiffFileExp2();
    system("gmsysview subtree -ns yangSubExpFunc -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 014.Yang表订阅，订阅xpath路径包含list，list含默认值字段，插入数据，订阅类型分别设置report_all、explicit、同时设置
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubExpFunc, Yang_112_yangSubExpFunc_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel3.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "ListOne",
                "states": [{"type": "create"},{"type": "update"},{"type": "remove"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson, user_data, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *newSubJson2 = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "ListOne",
                "states": [{"type": "create"},{"type": "update"},{"type": "remove"}]
            }
        ],
        "events":[
            {"type": "diff_explicit"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson2, user_data2, "yangSub2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *newSubJson3 = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "ListOne",
                "states": [{"type": "create"},{"type": "update"},{"type": "remove"}]
            }
        ],
        "events":[
            {"type": "diff"},
            {"type": "diff_explicit"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson3, user_data3, "yangSub3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    char strEmpty[1] = "";
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_EMPTY, &strEmpty, 0, "F1",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F2",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 102; i++) {
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_EMPTY, &strEmpty, 0, "F1",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F2",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff014_subAll.json", NULL, user_data);
    SetDiffFileExp("diffFunc/diff014_subExp.json", NULL, user_data2);

    SetDiffFile2("diffFunc/diff014_subAll.json", NULL, user_data3);
    SetDiffFileExp2("diffFunc/diff014_subExp.json", NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 2");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 102; i < 104; i++) {
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_EMPTY, &strEmpty, 0, "F1",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F2",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 456;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F3");

        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);
 
    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff014_subAll2.json", NULL, user_data);
    SetDiffFileExp("diffFunc/diff014_subExp2.json", NULL, user_data2);

    SetDiffFile2("diffFunc/diff014_subAll2.json", NULL, user_data3);
    SetDiffFileExp2("diffFunc/diff014_subExp2.json", NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();

    system("gmsysview subtree -ns yangSubExpFunc -rn ContainerOne -defaultMode REPORT_ALL");

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 3");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 104; i < 105; i++) {
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_EMPTY, &strEmpty, 0, "F1",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F2",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 800;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F3");
        char valueStr[8] = "str456";
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &valueStr, (strlen(valueStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffFunc/diff014_3.json", NULL, user_data);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffReply, data);
    ReleaseDiffFile();

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff014_subAll3.json", NULL, user_data);
    SetDiffFileExp("diffFunc/diff014_subExp3.json", NULL, user_data2);

    SetDiffFile2("diffFunc/diff014_subAll3.json", NULL, user_data3);
    SetDiffFileExp2("diffFunc/diff014_subExp3.json", NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 4");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff014_subAll4.json", NULL, user_data);
    SetDiffFileExp("diffFunc/diff014_subExp4.json", NULL, user_data2);

    SetDiffFile2("diffFunc/diff014_subAll4.json", NULL, user_data3);
    SetDiffFileExp2("diffFunc/diff014_subExp4.json", NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, "yangSub2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, "yangSub3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ClearDiffFile();
    ClearDiffFileExp();
    ClearDiffFile2();
    ClearDiffFileExp2();
    system("gmsysview subtree -ns yangSubExpFunc -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 015.Yang表订阅，订阅xpath路径包含list，list含默认值字段，预置数据不编辑默认值字段，更新数据，订阅类型分别设置report_all、explicit、同时设置
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubExpFunc, Yang_112_yangSubExpFunc_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel3.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "ListOne",
                "states": [{"type": "create"},{"type": "update"},{"type": "remove"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson, user_data, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *newSubJson2 = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "ListOne",
                "states": [{"type": "create"},{"type": "update"},{"type": "remove"}]
            }
        ],
        "events":[
            {"type": "diff_explicit"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson2, user_data2, "yangSub2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *newSubJson3 = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "ListOne",
                "states": [{"type": "create"},{"type": "update"},{"type": "remove"}]
            }
        ],
        "events":[
            {"type": "diff"},
            {"type": "diff_explicit"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson3, user_data3, "yangSub3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    char strEmpty[1] = "";
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_EMPTY, &strEmpty, 0, "F1",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F2",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 102; i++) {
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_EMPTY, &strEmpty, 0, "F1",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F2",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff015_subAll.json", NULL, user_data);
    SetDiffFileExp("diffFunc/diff014_subExp.json", NULL, user_data2);

    SetDiffFile2("diffFunc/diff015_subAll.json", NULL, user_data3);
    SetDiffFileExp2("diffFunc/diff015_subExp.json", NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 2");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 102; i++) {
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_EMPTY, &strEmpty, 0, "F1",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F2",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 456;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F3");
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F4");

        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);
 
    // 设置订阅校验信息
    SetDiffFile(NULL, NULL, user_data);
    SetDiffFileExp("diffFunc/diff015_subExp2.json", NULL, user_data2);

    SetDiffFile2(NULL, NULL, user_data3);
    SetDiffFileExp2("diffFunc/diff015_subExp2.json", NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();

    system("gmsysview subtree -ns yangSubExpFunc -rn ContainerOne -defaultMode REPORT_ALL");

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 3");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 102; i++) {
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_EMPTY, &strEmpty, 0, "F1",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F2",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 800;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F3");
        fieldValue = 900;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F4");
        char valueStr[8] = "str456";
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &valueStr, (strlen(valueStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffFunc/diff015_3.json", NULL, user_data);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffReply, data);
    ReleaseDiffFile();

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff015_subAll3.json", NULL, user_data);
    SetDiffFileExp("diffFunc/diff015_subExp3.json", NULL, user_data2);

    SetDiffFile2("diffFunc/diff015_subAll3.json", NULL, user_data3);
    SetDiffFileExp2("diffFunc/diff015_subExp3.json", NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 4");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff015_subAll4.json", NULL, user_data);
    SetDiffFileExp("diffFunc/diff015_subExp4.json", NULL, user_data2);

    SetDiffFile2("diffFunc/diff015_subAll4.json", NULL, user_data3);
    SetDiffFileExp2("diffFunc/diff015_subExp4.json", NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, "yangSub2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, "yangSub3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ClearDiffFile();
    ClearDiffFileExp();
    ClearDiffFile2();
    ClearDiffFileExp2();
    system("gmsysview subtree -ns yangSubExpFunc -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 016.Yang表订阅，订阅xpath路径包含list，list含默认值字段，预置数据编辑默认值字段为默认值，更新数据，订阅类型分别设置report_all、explicit、同时设置
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubExpFunc, Yang_112_yangSubExpFunc_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel3.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "ListOne",
                "states": [{"type": "create"},{"type": "update"},{"type": "remove"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson, user_data, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *newSubJson2 = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "ListOne",
                "states": [{"type": "create"},{"type": "update"},{"type": "remove"}]
            }
        ],
        "events":[
            {"type": "diff_explicit"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson2, user_data2, "yangSub2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *newSubJson3 = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "ListOne",
                "states": [{"type": "create"},{"type": "update"},{"type": "remove"}]
            }
        ],
        "events":[
            {"type": "diff"},
            {"type": "diff_explicit"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson3, user_data3, "yangSub3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    char strEmpty[1] = "";
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_EMPTY, &strEmpty, 0, "F1",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F2",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 102; i++) {
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_EMPTY, &strEmpty, 0, "F1",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F2",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 456;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F3");
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F4");

        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff016_subAll.json", NULL, user_data);
    SetDiffFileExp("diffFunc/diff016_subExp.json", NULL, user_data2);

    SetDiffFile2("diffFunc/diff016_subAll.json", NULL, user_data3);
    SetDiffFileExp2("diffFunc/diff016_subExp.json", NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 2");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 102; i++) {
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_EMPTY, &strEmpty, 0, "F1",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F2",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 456;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F3");
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F4");

        char valueStr[8] = "str456";
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &valueStr, (strlen(valueStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);
 
    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff016_subAll2.json", NULL, user_data);
    SetDiffFileExp("diffFunc/diff016_subExp2.json", NULL, user_data2);

    SetDiffFile2("diffFunc/diff016_subAll2.json", NULL, user_data3);
    SetDiffFileExp2("diffFunc/diff016_subExp2.json", NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 3");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 102; i++) {
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_EMPTY, &strEmpty, 0, "F1",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F2",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 800;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F3");
        fieldValue = 900;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F4");
        char valueStr[8] = "str456";
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &valueStr, (strlen(valueStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffFunc/diff016_3.json", NULL, user_data);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffReply, data);
    ReleaseDiffFile();

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff016_subAll3.json", NULL, user_data);
    SetDiffFileExp("diffFunc/diff016_subExp3.json", NULL, user_data2);

    SetDiffFile2("diffFunc/diff016_subAll3.json", NULL, user_data3);
    SetDiffFileExp2("diffFunc/diff016_subExp3.json", NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 4");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff016_subAll4.json", NULL, user_data);
    SetDiffFileExp("diffFunc/diff016_subExp4.json", NULL, user_data2);

    SetDiffFile2("diffFunc/diff016_subAll4.json", NULL, user_data3);
    SetDiffFileExp2("diffFunc/diff016_subExp4.json", NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, "yangSub2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, "yangSub3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ClearDiffFile();
    ClearDiffFileExp();
    ClearDiffFile2();
    ClearDiffFileExp2();
    system("gmsysview subtree -ns yangSubExpFunc -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 017.Yang表订阅，订阅xpath路径包含list，list含默认值字段，预置数据编辑默认值字段为其他值，更新数据，订阅类型分别设置report_all、explicit、同时设置
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubExpFunc, Yang_112_yangSubExpFunc_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel3.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "ListOne",
                "states": [{"type": "create"},{"type": "update"},{"type": "remove"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson, user_data, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *newSubJson2 = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "ListOne",
                "states": [{"type": "create"},{"type": "update"},{"type": "remove"}]
            }
        ],
        "events":[
            {"type": "diff_explicit"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson2, user_data2, "yangSub2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *newSubJson3 = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "ListOne",
                "states": [{"type": "create"},{"type": "update"},{"type": "remove"}]
            }
        ],
        "events":[
            {"type": "diff"},
            {"type": "diff_explicit"}
        ]
    })";
    ret = TestYangSub(g_stmtSync, g_connSub, newSubJson3, user_data3, "yangSub3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    char strEmpty[1] = "";
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_EMPTY, &strEmpty, 0, "F1",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F2",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 102; i++) {
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_EMPTY, &strEmpty, 0, "F1",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F2",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 800;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F3");
        fieldValue = 900;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F4");

        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff017_subAll.json", NULL, user_data);
    SetDiffFileExp("diffFunc/diff017_subExp.json", NULL, user_data2);

    SetDiffFile2("diffFunc/diff017_subAll.json", NULL, user_data3);
    SetDiffFileExp2("diffFunc/diff017_subExp.json", NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 2");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 102; i++) {
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_EMPTY, &strEmpty, 0, "F1",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F2",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 456;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F3");
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F4");

        char valueStr[8] = "str123";
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &valueStr, (strlen(valueStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);
 
    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff017_subAll2.json", NULL, user_data);
    SetDiffFileExp("diffFunc/diff017_subExp2.json", NULL, user_data2);

    SetDiffFile2("diffFunc/diff017_subAll2.json", NULL, user_data3);
    SetDiffFileExp2("diffFunc/diff017_subExp2.json", NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 3");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 102; i++) {
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 对子节点字段 做replace操作
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_EMPTY, &strEmpty, 0, "F1",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F2",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 800;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F3");
        fieldValue = 900;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F4");
        char valueStr[8] = "str456";
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &valueStr, (strlen(valueStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffFunc/diff017_3.json", NULL, user_data);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffReply, data);
    ReleaseDiffFile();

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff017_subAll3.json", NULL, user_data);
    SetDiffFileExp("diffFunc/diff017_subExp3.json", NULL, user_data2);

    SetDiffFile2("diffFunc/diff017_subAll3.json", NULL, user_data3);
    SetDiffFileExp2("diffFunc/diff017_subExp3.json", NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 4");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff017_subAll4.json", NULL, user_data);
    SetDiffFileExp("diffFunc/diff017_subExp4.json", NULL, user_data2);

    SetDiffFile2("diffFunc/diff017_subAll4.json", NULL, user_data3);
    SetDiffFileExp2("diffFunc/diff017_subExp4.json", NULL, user_data3);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFileExp();

    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ret = testWaitSnRecv(user_data3, GMC_SUB_EVENT_DIFF_EXPLICIT, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile2();
    ReleaseDiffFileExp2();

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, "yangSub2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmtSync, "yangSub3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ClearDiffFile();
    ClearDiffFileExp();
    ClearDiffFile2();
    ClearDiffFileExp2();
    system("gmsysview subtree -ns yangSubExpFunc -rn ContainerOne -defaultMode REPORT_ALL");

    AW_FUN_Log(LOG_STEP, "END");
}

