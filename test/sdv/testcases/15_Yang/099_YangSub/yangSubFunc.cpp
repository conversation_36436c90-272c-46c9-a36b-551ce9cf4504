/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#include "RCA_COMMON.h"
#include "../../../reliability/try.h"
#include "aliasTool.h"
#include "yangSubTool.h"

class yangSubFunc : public testing::Test {
public:
    SnUserDataT *newSubData;
    SnUserDataT *oldSubData;
    virtual void SetUp();
    virtual void TearDown();

    SnUserDataT *user_data;
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
    void clearSubUserData();
};

void yangSubFunc::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("mkdir -p /data/gmdb/; rm -rf /data/gmdb/*");
#ifndef FEATURE_CLT_SERVER_SAME_PROCESS
    system("sh $TEST_HOME/tools/start.sh -f");
#endif
    int ret = testEnvInit();
    ASSERT_EQ(GMERR_OK, ret);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void yangSubFunc::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

void yangSubFunc::SetUp()
{
    int ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    user_data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data, 0, sizeof(SnUserDataT));

    user_data->subscriptionName = (char *)malloc(sizeof(char) * 128);
    memset(user_data->subscriptionName, 0, sizeof(char) * 128);

    user_data->connectionName = (char *)malloc(sizeof(char) * 128);
    memset(user_data->connectionName, 0, sizeof(char) * 128);

    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    const char *namespace1 = "yangSubFunc";
    const char *namespaceUserName = "abc";

    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观
    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // alloc all stmt
    TestYangAllocAllstmt();

    // 创建同步连接
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(g_stmtSync, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建订阅连接
    int chanRingLen = 256;
    const char *newSubConnName = "yangSubConn";
    ret = testSubConnect(&g_connSub, &g_stmtSub, 1, g_epoll_reg_info, newSubConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(user_data->connectionName, 128, "%s", newSubConnName);

    // 创建同步连接
    ret = testGmcConnect(&g_connSync2, &g_stmtSync2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
}

void yangSubFunc::clearSubUserData()
{
    user_data->diffNum = 0;
    user_data->isFetchNull = false;
}

void yangSubFunc::TearDown()
{
    const char *namespace1 = "yangSubFunc";
    TryDropNameSpace(g_stmt_async, namespace1);

    // 释放all stmt
    TestYangFreeAllstmt();

    int ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubDisConnect(g_connSub, g_stmtSub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    if (user_data->subscriptionName) {
        free(user_data->subscriptionName);
        user_data->subscriptionName = NULL;
    }
    if (user_data->connectionName) {
        free(user_data->connectionName);
        user_data->connectionName = NULL;
    }
    if (user_data) {
        free(user_data);
        user_data = NULL;
    }
}


void TestCheckValidateModelAsync(GmcStmtT *stmt)
{
    // 模型校验
    YangValidateUserDataT checkData = {0};
    int ret = GmcYangValidateModelAsync(stmt, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(true, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(0, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));
}

// *typedef void (*GmcYangValidateDoneT)(void *userData, GmcValidateResT validateRes, int32_t status, const char *errMsg);*/
void AsyncValidateLeafRefCb(void *userData, GmcValidateResT validateRes, int32_t status, const char *errMsg)
{
    if (userData) {
        YangValidateUserDataT *uData = (YangValidateUserDataT *)userData;
        uData->status = status;
        if ((status != GMERR_OK) && (errMsg != NULL)) {
            printf("YangValidate errMsg: %s\n", errMsg);
        }
        uData->validateRes = validateRes.validateRes;
        uData->failCount = validateRes.failCount;

        printf(">>> validateRes: %d\n", validateRes.validateRes);
        printf(">>> failCount: %u\n", validateRes.failCount);

        if (uData->isValidErrorPathInfo) {
            GmcErrorPathInfoT msg;
            ASSERT_EQ(GMERR_OK, GmcYangGetErrorPathInfo(&msg));

            // 结果检查
            printf("--- errcode: %d\n", msg.errorCode);
            printf("--- errorClauseIndex: %u\n", msg.errorClauseIndex);
            printf("--- errorMsg: %s\n", msg.errorMsg);
            printf("--- errorPath: %s\n", msg.errorPath);
            EXPECT_EQ(uData->expectedErrCode, msg.errorCode);
            EXPECT_EQ(uData->expectedErrClauseIndex, msg.errorClauseIndex);
            EXPECT_STREQ(uData->expectedErrMsg, msg.errorMsg);
            EXPECT_STREQ(uData->expectedErrPath, msg.errorPath);
            ASSERT_NO_FATAL_FAILURE(GmcYangFreeErrorPathInfo());
        }

        uData->recvNum++;
    }
}


/*****************************************************************************
 * Description  : 001.Yang表订阅, 订阅xpath路径为root, states为create
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubFunc, Yang_099_yangSubFunc_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "states": [{"type": "create"}],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    int retSub = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, retSub);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffFunc/diff001.json", NULL, user_data);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffReply, data);
    ReleaseDiffFile();

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff001.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 2");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息，为空 
    SetDiffFile(NULL, NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 3.");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 获取diff
    memset(&data, 0, sizeof(data));
    SetDiffFile("diffFunc/diff001_3.json", NULL, user_data);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffReply, data);
    ReleaseDiffFile();

    // 设置订阅校验信息，筛选为空
    SetDiffFile(NULL, NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ReleaseDiffFile();
    ClearDiffFile();
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 002.Yang表订阅, 订阅xpath路径为root, states为update
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubFunc, Yang_099_yangSubFunc_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "states": [{"type": "update"}],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    int retSub = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, retSub);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffFunc/diff001.json", NULL, user_data);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffReply, data);
    ReleaseDiffFile();

    // 设置订阅校验信息
    SetDiffFile(NULL, NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 2");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 获取diff
    memset(&data, 0, sizeof(data));
    SetDiffFile("diffFunc/diff001_2.json", NULL, user_data);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffReply, data);
    ReleaseDiffFile();

    // 设置订阅校验信息，为空 
    SetDiffFile("diffFunc/diff001_2.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 3.");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息，筛选为空
    SetDiffFile("diffFunc/diff001_3.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ReleaseDiffFile();
    ClearDiffFile();
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 003.Yang表订阅, 订阅xpath路径为root, states为remove
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubFunc, Yang_099_yangSubFunc_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "states": [{"type": "remove"}],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    int retSub = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, retSub);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffFunc/diff001.json", NULL, user_data);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffReply, data);
    ReleaseDiffFile();

    // 设置订阅校验信息
    SetDiffFile(NULL, NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 2");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 获取diff
    memset(&data, 0, sizeof(data));
    SetDiffFile("diffFunc/diff001_2.json", NULL, user_data);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffReply, data);
    ReleaseDiffFile();

    // 设置订阅校验信息
    SetDiffFile(NULL, NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 3.");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile(NULL, NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ReleaseDiffFile();
    ClearDiffFile();
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 004.Yang表订阅, 订阅xpath路径为root, states为remove和update
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubFunc, Yang_099_yangSubFunc_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "states": [{"type": "remove"}, {"type": "update"}],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    int retSub = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, retSub);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffFunc/diff001.json", NULL, user_data);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffReply, data);
    ReleaseDiffFile();

    // 设置订阅校验信息
    SetDiffFile(NULL, NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 2");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 获取diff
    memset(&data, 0, sizeof(data));
    SetDiffFile("diffFunc/diff001_2.json", NULL, user_data);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffReply, data);
    ReleaseDiffFile();

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff001_2.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 3.");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff001_3.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ReleaseDiffFile();
    ClearDiffFile();
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 005.Yang表订阅, 订阅xpath路径为root, states为all
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubFunc, Yang_099_yangSubFunc_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "states": [{"type": "all"}],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    int retSub = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, retSub);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff001.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 2");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff001_2.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 3.");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff001_3.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ReleaseDiffFile();
    ClearDiffFile();
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 006.Yang表订阅, 订阅xpath路径为root, states不设置
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubFunc, Yang_099_yangSubFunc_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    int retSub = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, retSub);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff001.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 2");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff001_2.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 3.");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff001_3.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ReleaseDiffFile();
    ClearDiffFile();
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 007.Yang表订阅, 订阅xpath路径为root/F1，states为create和update
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubFunc, Yang_099_yangSubFunc_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "F1",
                "states": [{"type": "create"},{"type": "update"}]
            }

        ],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    int retSub = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, retSub);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffFunc/diff007.json", NULL, user_data);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffReply, data);
    ReleaseDiffFile();

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff007_sub.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 2");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff007_sub2.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 3.");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息，筛选为空
    ClearDiffFile();
    SetDiffFile(NULL, NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    if (!retSub) {
        ret = GmcUnSubscribe(g_stmtSync, "yangSub");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ReleaseDiffFile();
    ClearDiffFile();
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 008.Yang表订阅, 订阅xpath路径为root/F1，states为remove
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubFunc, Yang_099_yangSubFunc_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "F1",
                "states": [{"type": "remove"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    int retSub = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, retSub);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息，筛选为空
    SetDiffFile(NULL, NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 2");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息，筛选为空
    SetDiffFile(NULL, NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 3.");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff008_sub.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    if (!retSub) {
        ret = GmcUnSubscribe(g_stmtSync, "yangSub");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ReleaseDiffFile();
    ClearDiffFile();
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 009.Yang表订阅, 订阅xpath路径为root/[F1=100]，states为all
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubFunc, Yang_099_yangSubFunc_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "[F1=101]",
                "states": [{"type": "all"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    int retSub = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, retSub);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息，筛选为空
    SetDiffFile(NULL, NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 2");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息，筛选为空
    SetDiffFile("diffFunc/diff009_sub.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 3.");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff009_sub2.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    if (!retSub) {
        ret = GmcUnSubscribe(g_stmtSync, "yangSub");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ReleaseDiffFile();
    ClearDiffFile();
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 010.Yang表订阅, 订阅xpath路径为root/F1[.=101]，states为all
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubFunc, Yang_099_yangSubFunc_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "F1[.=101]",
                "states": [{"type": "all"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    int retSub = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, retSub);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息，筛选为空
    SetDiffFile(NULL, NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 2");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffFunc/diff010.json", NULL, user_data);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffReply, data);
    ReleaseDiffFile();

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff010_sub.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 3.");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff010_sub2.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ReleaseDiffFile();
    ClearDiffFile();
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 011.Yang表订阅, 订阅xpath路径为root/contain1，states为create和update和remove
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubFunc, Yang_099_yangSubFunc_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "ContainerTwo",
                "states": [{"type": "create"},{"type": "update"},{"type": "remove"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    int retSub = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, retSub);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 container NP 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffFunc/diff011.json", NULL, user_data);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffReply, data);
    ReleaseDiffFile();

    // 设置订阅校验信息，筛选为空
    SetDiffFile("diffFunc/diff011_sub.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);


    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 2");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 container NP 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 获取diff
    memset(&data, 0, sizeof(data));
    SetDiffFile("diffFunc/diff011_2.json", NULL, user_data);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffReply, data);
    ReleaseDiffFile();

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff011_sub2.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 3.");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff011_sub3.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ReleaseDiffFile();
    ClearDiffFile();
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 012.Yang表订阅, 订阅xpath路径为root/contain1，states为create和remove
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubFunc, Yang_099_yangSubFunc_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "ContainerTwo",
                "states": [{"type": "create"},{"type": "remove"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    int retSub = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, retSub);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 container NP 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffFunc/diff011.json", NULL, user_data);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffReply, data);
    ReleaseDiffFile();

    // 设置订阅校验信息，筛选为空
    SetDiffFile("diffFunc/diff011_sub.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);


    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 2");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 container NP 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 获取diff
    memset(&data, 0, sizeof(data));
    SetDiffFile("diffFunc/diff011_2.json", NULL, user_data);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffReply, data);
    ReleaseDiffFile();

    // 设置订阅校验信息
    SetDiffFile(NULL, NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 3.");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff011_sub3.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ReleaseDiffFile();
    ClearDiffFile();
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 013.Yang表订阅, 订阅xpath路径为root/contain/F1，states为create和all
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubFunc, Yang_099_yangSubFunc_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "ContainerTwo/F0",
                "states": [{"type": "create"},{"type": "all"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    int retSub = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, retSub);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 container NP 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffFunc/diff013.json", NULL, user_data);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffReply, data);
    ReleaseDiffFile();

    // 设置订阅校验信息，筛选为空
    SetDiffFile(NULL, NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 2");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 container NP 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff013_sub2.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 3.");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    SetDiffFile("diffFunc/diff013_3.json", NULL, user_data);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffReply, data);
    ReleaseDiffFile();

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff013_sub3.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ReleaseDiffFile();
    ClearDiffFile();
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 014.Yang表订阅, 订阅xpath路径为root/contain/F1, states为update和remove
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubFunc, Yang_099_yangSubFunc_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "ContainerTwo/F0",
                "states": [{"type": "update"},{"type": "remove"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    int retSub = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, retSub);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 container NP 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F0");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffFunc/diff014.json", NULL, user_data);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffReply, data);
    ReleaseDiffFile();

    // 设置订阅校验信息，筛选为空
    SetDiffFile(NULL, NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 2");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 container NP 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    fieldValue = 101;
    testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F0");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff014_sub2.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 3.");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff014_sub3.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ReleaseDiffFile();
    ClearDiffFile();
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 015.Yang表订阅, 订阅xpath路径为root/contain[F1=101]，states为不设置
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubFunc, Yang_099_yangSubFunc_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "ContainerTwo[F0=101]"
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    int retSub = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, retSub);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 container NP 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    fieldValue = 101;
    testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F0");
    testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffFunc/diff015.json", NULL, user_data);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffReply, data);
    ReleaseDiffFile();

    // 设置订阅校验信息，筛选为空
    SetDiffFile("diffFunc/diff015_sub.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 2");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 container NP 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F0");
    testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff015_sub2.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 3.");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile(NULL, NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ReleaseDiffFile();
    ClearDiffFile();
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 016.Yang表订阅, 订阅xpath路径为root/contain/F1[.=101]，states为设置为create和remove
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubFunc, Yang_099_yangSubFunc_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "ContainerTwo/F0[.=101]",
                "states": [{"type": "create"},{"type": "remove"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    int retSub = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, retSub);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 container NP 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F0");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffFunc/diff016.json", NULL, user_data);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffReply, data);
    ReleaseDiffFile();

    // 设置订阅校验信息，筛选为空
    SetDiffFile(NULL, NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 2");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 container NP 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    fieldValue = 101;
    testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F0");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile(NULL, NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 3.");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff016_sub3.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ReleaseDiffFile();
    ClearDiffFile();
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 017.Yang表订阅, 订阅xpath路径为root/contain/F1[.=101]，states为设置为create和remove
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubFunc, Yang_099_yangSubFunc_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件  此时根节点的states不生效
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "states": [{"type": "create"}],
        "subs_path":[
            {
                "xpath": "ContainerTwo",
                "states": [{"type": "create"}]
            },
            {
                "xpath": "ContainerTwo/F1",
                "states": [{"type": "create"}, {"type": "update"}]
            },
            {
                "xpath": "ContainerTwo/F0[.=100]",
                "states": [{"type": "create"}, {"type": "remove"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    int retSub = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, retSub);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 container NP 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    fieldValue = 101;
    testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F0");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffFunc/diff017.json", NULL, user_data);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffReply, data);
    ReleaseDiffFile();

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff017_sub1.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 2");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 container NP 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F0");
    testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 获取diff
    memset(&data, 0, sizeof(data));
    SetDiffFile("diffFunc/diff017_2.json", NULL, user_data);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffReply, data);
    ReleaseDiffFile();

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff017_sub2.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 3.");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff017_sub3.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ReleaseDiffFile();
    ClearDiffFile();
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 018.Yang表订阅, 订阅多条xpath路径, 包括root/  states为create 和root/contain/ states为update 和root/contain/F1 states为update和ContainerTwo/F0[.=101] states为create remove
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubFunc, Yang_099_yangSubFunc_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": ".",
                "states": [{"type": "create"}]
            },
            {
                "xpath": "ContainerTwo",
                "states": [{"type": "create"}]
            },
            {
                "xpath": "ContainerTwo/F1",
                "states": [{"type": "create"}, {"type": "update"}]
            },
            {
                "xpath": "ContainerTwo/F0[.=100]",
                "states": [{"type": "create"}, {"type": "remove"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    int retSub = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, retSub);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 container NP 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    fieldValue = 101;
    testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F0");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffFunc/diff018.json", NULL, user_data);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffReply, data);
    ReleaseDiffFile();

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff018.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 2");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 container NP 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    fieldValue = 100;
    testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F0");
    testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 获取diff
    memset(&data, 0, sizeof(data));
    SetDiffFile("diffFunc/diff018_2.json", NULL, user_data);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffReply, data);
    ReleaseDiffFile();

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff018_sub2.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 3.");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff018_sub3.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, "yangSub");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ReleaseDiffFile();
    ClearDiffFile();
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 019.Yang表订阅, 订阅xpath路径为choice，states为create和update
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubFunc, Yang_099_yangSubFunc_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "Choice",
                "states": [{"type": "create"},{"type": "update"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    int retSub = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, retSub);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffFunc/diff019.json", NULL, user_data);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffReply, data);
    ReleaseDiffFile();

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff019_sub.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 2");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, "CaseTwo", GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff019_sub2.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 3.");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息，筛选为空
    SetDiffFile(NULL, NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    if (!retSub) {
        ret = GmcUnSubscribe(g_stmtSync, "yangSub");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ReleaseDiffFile();
    ClearDiffFile();
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 020.Yang表订阅, 订阅xpath路径为choice，states为remove
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubFunc, Yang_099_yangSubFunc_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "Choice",
                "states": [{"type": "remove"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    int retSub = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, retSub);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile(NULL, NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 2");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, "CaseTwo", GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile(NULL, NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 3.");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff020_sub.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    if (!retSub) {
        ret = GmcUnSubscribe(g_stmtSync, "yangSub");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ReleaseDiffFile();
    ClearDiffFile();
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 021.Yang表订阅, 订阅xpath路径为choice/case1，states为create
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubFunc, Yang_099_yangSubFunc_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "Choice/CaseTwo",
                "states": [{"type": "create"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    int retSub = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, retSub);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffFunc/diff021.json", NULL, user_data);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffReply, data);
    ReleaseDiffFile();

    // 设置订阅校验信息
    SetDiffFile(NULL, NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 2");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, "CaseTwo", GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff021_sub.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 3.");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息，筛选为空
    SetDiffFile(NULL, NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    if (!retSub) {
        ret = GmcUnSubscribe(g_stmtSync, "yangSub");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ReleaseDiffFile();
    ClearDiffFile();
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 022.Yang表订阅, 订阅xpath路径为choice/case1，states为update
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubFunc, Yang_099_yangSubFunc_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "Choice/CaseOne",
                "states": [{"type": "update"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    int retSub = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, retSub);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffFunc/diff021.json", NULL, user_data);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffReply, data);
    ReleaseDiffFile();

    // 设置订阅校验信息
    SetDiffFile(NULL, NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 2");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, "CaseOne", GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff022_sub.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 2");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, "CaseTwo", GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile(NULL, NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 4.\n");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息，筛选为空
    SetDiffFile(NULL, NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    if (!retSub) {
        ret = GmcUnSubscribe(g_stmtSync, "yangSub");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ReleaseDiffFile();
    ClearDiffFile();
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 023.Yang表订阅, 订阅xpath路径为choice/case1，states为remove
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubFunc, Yang_099_yangSubFunc_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "Choice/CaseOne",
                "states": [{"type": "remove"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    int retSub = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, retSub);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffFunc/diff021.json", NULL, user_data);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffReply, data);
    ReleaseDiffFile();

    // 设置订阅校验信息
    SetDiffFile(NULL, NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 2");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, "CaseOne", GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile(NULL, NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 3.");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息，筛选为空
    SetDiffFile("diffFunc/diff023_sub.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    if (!retSub) {
        ret = GmcUnSubscribe(g_stmtSync, "yangSub");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ReleaseDiffFile();
    ClearDiffFile();
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 024.Yang表订阅, 订阅xpath路径为choice/case1，states为all
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubFunc, Yang_099_yangSubFunc_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "Choice/CaseOne",
                "states": [{"type": "all"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    int retSub = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, retSub);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffFunc/diff021.json", NULL, user_data);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffReply, data);
    ReleaseDiffFile();

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff024_sub.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 2");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, "CaseOne", GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff024_sub2.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 3.");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, "CaseTwo", GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff024_sub3.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 4.\n");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息，筛选为空
    SetDiffFile(NULL, NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    if (!retSub) {
        ret = GmcUnSubscribe(g_stmtSync, "yangSub");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ReleaseDiffFile();
    ClearDiffFile();
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 025.Yang表订阅, 订阅xpath路径为choice/case1/F1，states为update和remove
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubFunc, Yang_099_yangSubFunc_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "Choice/CaseOne/F1",
                "states": [{"type": "update"},{"type": "remove"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    int retSub = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, retSub);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffFunc/diff025.json", NULL, user_data);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffReply, data);
    ReleaseDiffFile();

    // 设置订阅校验信息
    SetDiffFile(NULL, NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 2");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, "CaseOne", GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff025_sub.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 3.");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, "CaseOne", GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff025_sub2.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 4.\n");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息，筛选为空
    SetDiffFile(NULL, NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    if (!retSub) {
        ret = GmcUnSubscribe(g_stmtSync, "yangSub");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ReleaseDiffFile();
    ClearDiffFile();
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 026.Yang表订阅, 订阅xpath路径为choice/case1/F1，states为create和update
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubFunc, Yang_099_yangSubFunc_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "Choice/CaseOne/F1",
                "states": [{"type": "create"},{"type": "update"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    int retSub = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, retSub);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffFunc/diff026.json", NULL, user_data);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffReply, data);
    ReleaseDiffFile();

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff026_sub.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 2");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, "CaseOne", GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff026_sub2.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 3.");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, "CaseOne", GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile(NULL, NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 4.\n");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息，筛选为空
    SetDiffFile(NULL, NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    if (!retSub) {
        ret = GmcUnSubscribe(g_stmtSync, "yangSub");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ReleaseDiffFile();
    ClearDiffFile();
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 027.Yang表订阅, 订阅xpath路径为choice/case1[F1=101]，states为create和update和remove
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubFunc, Yang_099_yangSubFunc_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "Choice/CaseOne[F1=101]",
                "states": [{"type": "create"},{"type": "update"},{"type": "remove"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    int retSub = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, retSub);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffFunc/diff027.json", NULL, user_data);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffReply, data);
    ReleaseDiffFile();

    // 设置订阅校验信息 
    SetDiffFile(NULL, NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 2");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, "CaseOne", GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff027_sub.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 3.");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, "CaseOne", GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff027_sub2.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 4.\n");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息，筛选为空
    SetDiffFile(NULL, NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    if (!retSub) {
        ret = GmcUnSubscribe(g_stmtSync, "yangSub");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ReleaseDiffFile();
    ClearDiffFile();
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 028.Yang表订阅, 订阅xpath路径为choice/case1/F1[.=100]，states为all
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubFunc, Yang_099_yangSubFunc_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "Choice/CaseOne/F1[.=101]"
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    int retSub = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, retSub);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffFunc/diff028.json", NULL, user_data);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffReply, data);
    ReleaseDiffFile();

    // 设置订阅校验信息 
    SetDiffFile("diffFunc/diff028_sub.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 2");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, "CaseOne", GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff028_sub2.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 3.");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, "CaseOne", GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff028_sub3.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 4.\n");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息，筛选为空
    SetDiffFile("diffFunc/diff028_sub4.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    if (!retSub) {
        ret = GmcUnSubscribe(g_stmtSync, "yangSub");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ReleaseDiffFile();
    ClearDiffFile();
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 029.Yang表订阅, 订阅多条xpath路径为choice/，states为create；choice/case1 states为update；choice/case1/F1 states为remove
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubFunc, Yang_099_yangSubFunc_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "Choice",
                "states": [{"type": "create"}]
            },
            {
                "xpath": "Choice/CaseOne",
                "states": [{"type": "remove"}]
            },
            {
                "xpath": "Choice/CaseOne/F0",
                "states": [{"type": "update"}]
            },
            {
                "xpath": "Choice/CaseOne/F1[.=101]",
                "states": [{"type": "create"},{"type": "update"},{"type": "remove"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    int retSub = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, retSub);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffFunc/diff029.json", NULL, user_data);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffReply, data);
    ReleaseDiffFile();

    // 设置订阅校验信息 
    SetDiffFile("diffFunc/diff029_sub.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 2");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, "CaseOne", GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff029_sub2.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 3.");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, "CaseOne", GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff029_sub3.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 4.\n");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息，筛选为空
    SetDiffFile("diffFunc/diff029_sub4.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    if (!retSub) {
        ret = GmcUnSubscribe(g_stmtSync, "yangSub");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ReleaseDiffFile();
    ClearDiffFile();
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 029.Yang表订阅, 订阅多条xpath路径为choice/，states为create；choice/case1 states为update；choice/case1/F1 states为remove
 * Input        : None
 * Output       : None
 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(yangSubFunc, Yang_099_yangSubFunc_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    ret = CreateVertexAndEdge("schemaFunc/SubTreeVertexLabel.gmjson", "schemaFunc/SubTreeEdgelLabel.gmjson");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // yang 订阅文件
    const char *newSubJson = R"({
        "label_name":"ContainerOne",
        "comment":"yang sub scription",
        "subs_path":[
            {
                "xpath": "Choice",
                "states": [{"type": "remove"}]
            },
            {
                "xpath": "Choice/CaseOne",
                "states": [{"type": "create"}]
            },
            {
                "xpath": "Choice/CaseOne/F0",
                "states": [{"type": "update"}]
            },
            {
                "xpath": "Choice/CaseOne/F1[.=101]",
                "states": [{"type": "remove"}]
            }
        ],
        "events":[
            {"type": "diff"}
        ]
    })";

    GmcSubConfigT newSubInfo;
    newSubInfo.subsName = "yangSub";
    newSubInfo.configJson = newSubJson;
    int retSub = GmcSubscribe(g_stmtSync, &newSubInfo, g_connSub, YangSnCallBack, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, retSub);
    (void)snprintf(user_data->subscriptionName, 128, "%s", newSubInfo.subsName);

    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, g_choiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 获取diff
    AsyncUserDataT data = {0};
    SetDiffFile("diffFunc/diff030.json", NULL, user_data);
    testFetchAndDeparseDiff(g_stmt_async, batch, g_diffReply, data);
    ReleaseDiffFile();

    // 设置订阅校验信息 
    SetDiffFile("diffFunc/diff030_sub.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 2");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, "CaseOne", GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F2");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff030_sub2.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 3.");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 对 choice case 子节点做replace操作
    ret = GmcYangEditChildNode(g_vertexLabelT0Node, g_choiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_choiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_choiceNode, "CaseOne", GMC_OPERATION_REPLACE_GRAPH, &g_choiceCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对子节点字段 做replace操作
    testYangSetVertexProperty_F0(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_choiceCaseNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息
    SetDiffFile("diffFunc/diff030_sub3.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    // 创建乐观事务
    AW_FUN_Log(LOG_INFO, ">> trans 4.\n");
    clearSubUserData();
    TransStart(g_conn_async);
    nDmlCnt = 0;

    // 设置批处理,开启diff
    batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, "ContainerOne", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 101;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // 设置订阅校验信息，筛选为空
    SetDiffFile("diffFunc/diff030_sub4.json", NULL, user_data);

    // 提交事务
    TransCommit(g_conn_async);

    // 等待订阅消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DIFF, 1);
    ASSERT_EQ(0, ret);
    ReleaseDiffFile();

    if (!retSub) {
        ret = GmcUnSubscribe(g_stmtSync, "yangSub");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ReleaseDiffFile();
    ClearDiffFile();
    AW_FUN_Log(LOG_STEP, "END");
}
